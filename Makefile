GOHOSTOS:=$(shell go env GOHOSTOS)
GOPATH:=$(shell go env GOPATH)
VERSION=$(shell git describe --tags --always)

ifeq ($(GOHOSTOS), windows)
	#the `find.exe` is different from `find` in bash/shell.
	#to see https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/find.
	#changed to use git-bash.exe to run find cli or other cli friendly, caused of every developer has a Git.
	#Git_Bash= $(subst cmd\,bin\bash.exe,$(dir $(shell where git)))
	Git_Bash=$(subst \,/,$(subst cmd\,bin\bash.exe,$(dir $(shell where git))))
	INTERNAL_PROTO_FILES=$(shell $(Git_Bash) -c "find internal -name *.proto")
	API_PROTO_FILES=$(shell $(Git_Bash) -c "find api -name *.proto")
else
	INTERNAL_PROTO_FILES=$(shell find internal -name *.proto)
	API_PROTO_FILES=$(shell find api -name *.proto)
endif

.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.35.2
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install gitlab.minum.cloud/BackendTeam/kratos/cmd/kratos/v2@main
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-errors/v2@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/gnostic/cmd/protoc-gen-openapi@v0.6.9
	go install github.com/envoyproxy/protoc-gen-validate@latest
	go install github.com/google/wire/cmd/wire@latest
	go install entgo.io/ent/cmd/ent@v0.13.0
	go install github.com/bufbuild/buf/cmd/buf@latest
	go install mvdan.cc/gofumpt@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest

.PHONY: config
# generate internal proto
config:
	@cd internal/conf && buf generate

.PHONY: api
# generate api proto
api:
	@cd api && buf format -w && buf generate

.PHONY: doc
doc:
	@ for item in $(shell ls -d api/*/ | sed 's|^api/||'); do \
		cd api; \
		buf generate --path $$item --template ./buf.openapi.gen.yaml; \
		cd ..; \
		rm -rf doc/$$item && mkdir -p doc/$$item; \
		mv  doc/openapi.yaml doc/$$item/openapi.yaml; \
	done

.PHONY: mac-doc
mac-doc:
	@ for item in $(shell ls -d api/*/ | grep -Eo '^api/\K.*'); do \
		cd api; \
		buf generate --path $$item --template ./buf.openapi.gen.yaml; \
		cd ..; \
		rm -rf doc/$$item && mkdir -p doc/$$item; \
		mv  doc/openapi.yaml doc/$$item/openapi.yaml; \
	done

.PHONY: build
# build
build:
	curl https://file.minum.cloud/skywalking-agent/skywalking-go-agent -o /tmp/agent
	chmod +x /tmp/agent
	mkdir -p bin/ && go build -a -ldflags "-X main.Version=$(VERSION)" -o ./bin/ ./...
	#mkdir -p bin/ && go build -toolexec="/tmp/agent" -a -ldflags "-X main.Version=$(VERSION)" -o ./bin/ ./...

.PHONY: generate
# generate
generate:
	go generate ./...
	go mod tidy

.PHONY: all
# generate all
all:
	make api;
	make doc;
	make config;
	make generate;

.PHONY: check
# format and lint
check:
	gofumpt -l -w .;
	golangci-lint run;

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help

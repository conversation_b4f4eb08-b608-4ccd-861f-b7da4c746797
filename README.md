# 后端项目模板

1. 初始化工具链 `make init`
2. 创建新服务

    + ```shell
      kratos proto add api/xxx/xxx.proto
      kratos proto server api/xxx/xxx.proto -t internal/service
      make api
      make generate
      ```

3. 新建 schema

    + ```shell
      cd internal/data
      ent new User
      cd ../..
      make all
      ```

## Docker

```bash
# build
docker build -t <your-docker-image-name> .

# run
docker run --rm -p 8000:8000 -p 9000:9000 -v </path/to/your/configs>:/data/conf <your-docker-image-name>
```

syntax = "proto3";

package api.webAgent;

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/webAgent;webAgent";
option java_multiple_files = true;
option java_package = "api.webAgent";

enum VisibilityType {
  VisibilityTypeAll = 0; // 所有人
  VisibilityTypeUser = 1; // 指定用户
}

enum ChatType {
  ChatTypeUnknown = 0;
  ChatTypeRag = 1;
  ChatTypeAgent = 2;
}

enum ChatObjectType {
  ChatObjectTypeUnknown = 0;
  ChatObjectTypeUser = 1;
  ChatObjectTypeSystem = 2;
}

enum ModelType {
  ModelTypeUnknown = 0;
  ModelTypeInternal = 1;
  ModelTypeGateway = 2;
}

enum SearchTopType {
  SearchTopTypeUnknown = 0;
  SearchTopTypeUser = 1; // 用户
  SearchTopTypeDept = 2; // 部门
}

enum AgentCategory {
  AgentCategoryUnknown = 0;
  AgentCategoryAiTools = 1; // AI工具
  AgentCategoryRag = 2; // 问答智能体
  AgentCategoryDeepRag = 3; // 深度检索问答智能体
  AgentCategoryBaseAndDeepSearchRag = 4; // 基础和深度搜索问答
  AgentCategoryCommonlyUsed = 5; // 常用智能体
}

enum AgentType {
  AgentTypeUnknown = 0;
  AgentTypeBaseRag = 1; // 基础问答
  AgentTypeDeepSearchRag = 2; // 深度搜索问答
  AgentTypeContractReview = 3; //合同审查
}

enum KnowledgeBaseType {
  KnowledgeBaseTypeDirect = 0; // 指定知识库
  KnowledgeBaseTypeMine = 1; // 我有权限的所有文件
}

enum CallAgentStatus {
  CallAgentStatusUnknown = 0;
  CallAgentStatusSuccess = 1; // 成功
  CallAgentStatusFail = 2; // 失败
}

enum PolicyCategory {
  PolicyCategoryUnknown = 0; // 未知
  PolicyCategorySensitiveInformationMatching = 1; // 敏感信息匹配
  PolicyCategoryCoreDataIdentification = 2; // 核心数据识别
  PolicyCategoryIntentionIdentification = 3; // 意图识别
}

enum RiskLevel {
  RiskLevelUnknown = 0; // 未知
  RiskLevelLow = 1; // 低危
  RiskLevelMedium = 2; // 中危
  RiskLevelHigh = 3; // 高危
}

enum HitAction {
  HitActionUnknown = 0; // 未知
  HitActionBlock = 1; // 阻断
  HitActionAlert = 2; // 告警
}

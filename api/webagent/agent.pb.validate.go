// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: webagent/agent.proto

package webAgent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on InternalModelChatPageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InternalModelChatPageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InternalModelChatPageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InternalModelChatPageRequestMultiError, or nil if none found.
func (m *InternalModelChatPageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InternalModelChatPageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageNum() <= 0 {
		err := InternalModelChatPageRequestValidationError{
			field:  "PageNum",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val <= 0 || val > 100 {
		err := InternalModelChatPageRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range (0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for UserName

	// no validation rules for DeptName

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InternalModelChatPageRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InternalModelChatPageRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InternalModelChatPageRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InternalModelChatPageRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InternalModelChatPageRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InternalModelChatPageRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ChatItemID

	// no validation rules for AgentID

	// no validation rules for ModelID

	// no validation rules for NoRefFiles

	// no validation rules for SearchModel

	// no validation rules for Class

	if len(errors) > 0 {
		return InternalModelChatPageRequestMultiError(errors)
	}

	return nil
}

// InternalModelChatPageRequestMultiError is an error wrapping multiple
// validation errors returned by InternalModelChatPageRequest.ValidateAll() if
// the designated constraints aren't met.
type InternalModelChatPageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InternalModelChatPageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InternalModelChatPageRequestMultiError) AllErrors() []error { return m }

// InternalModelChatPageRequestValidationError is the validation error returned
// by InternalModelChatPageRequest.Validate if the designated constraints
// aren't met.
type InternalModelChatPageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InternalModelChatPageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InternalModelChatPageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InternalModelChatPageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InternalModelChatPageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InternalModelChatPageRequestValidationError) ErrorName() string {
	return "InternalModelChatPageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InternalModelChatPageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInternalModelChatPageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InternalModelChatPageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InternalModelChatPageRequestValidationError{}

// Validate checks the field values on InternalModelChatPageReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InternalModelChatPageReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InternalModelChatPageReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InternalModelChatPageReplyMultiError, or nil if none found.
func (m *InternalModelChatPageReply) ValidateAll() error {
	return m.validate(true)
}

func (m *InternalModelChatPageReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InternalModelChatPageReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InternalModelChatPageReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InternalModelChatPageReplyValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InternalModelChatPageReplyMultiError(errors)
	}

	return nil
}

// InternalModelChatPageReplyMultiError is an error wrapping multiple
// validation errors returned by InternalModelChatPageReply.ValidateAll() if
// the designated constraints aren't met.
type InternalModelChatPageReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InternalModelChatPageReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InternalModelChatPageReplyMultiError) AllErrors() []error { return m }

// InternalModelChatPageReplyValidationError is the validation error returned
// by InternalModelChatPageReply.Validate if the designated constraints aren't met.
type InternalModelChatPageReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InternalModelChatPageReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InternalModelChatPageReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InternalModelChatPageReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InternalModelChatPageReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InternalModelChatPageReplyValidationError) ErrorName() string {
	return "InternalModelChatPageReplyValidationError"
}

// Error satisfies the builtin error interface
func (e InternalModelChatPageReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInternalModelChatPageReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InternalModelChatPageReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InternalModelChatPageReplyValidationError{}

// Validate checks the field values on InternalModelChatPageReplyItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InternalModelChatPageReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InternalModelChatPageReplyItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// InternalModelChatPageReplyItemMultiError, or nil if none found.
func (m *InternalModelChatPageReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *InternalModelChatPageReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	// no validation rules for Username

	// no validation rules for UserID

	// no validation rules for UserAvatar

	// no validation rules for DeptName

	// no validation rules for DeptID

	// no validation rules for AgentName

	// no validation rules for AgentAvatar

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InternalModelChatPageReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InternalModelChatPageReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InternalModelChatPageReplyItemValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PcName

	// no validation rules for Id

	// no validation rules for Class

	// no validation rules for RefFiesText

	for idx, item := range m.GetRefFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InternalModelChatPageReplyItemValidationError{
						field:  fmt.Sprintf("RefFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InternalModelChatPageReplyItemValidationError{
						field:  fmt.Sprintf("RefFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InternalModelChatPageReplyItemValidationError{
					field:  fmt.Sprintf("RefFiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return InternalModelChatPageReplyItemMultiError(errors)
	}

	return nil
}

// InternalModelChatPageReplyItemMultiError is an error wrapping multiple
// validation errors returned by InternalModelChatPageReplyItem.ValidateAll()
// if the designated constraints aren't met.
type InternalModelChatPageReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InternalModelChatPageReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InternalModelChatPageReplyItemMultiError) AllErrors() []error { return m }

// InternalModelChatPageReplyItemValidationError is the validation error
// returned by InternalModelChatPageReplyItem.Validate if the designated
// constraints aren't met.
type InternalModelChatPageReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InternalModelChatPageReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InternalModelChatPageReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InternalModelChatPageReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InternalModelChatPageReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InternalModelChatPageReplyItemValidationError) ErrorName() string {
	return "InternalModelChatPageReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e InternalModelChatPageReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInternalModelChatPageReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InternalModelChatPageReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InternalModelChatPageReplyItemValidationError{}

// Validate checks the field values on AgentInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AgentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AgentInfoMultiError, or nil
// if none found.
func (m *AgentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AgentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for WelcomeMsg

	// no validation rules for FallbackMsg

	// no validation rules for OwnerId

	// no validation rules for VisibilityType

	// no validation rules for Schema

	// no validation rules for IsPublic

	// no validation rules for IsEnabled

	// no validation rules for Description

	// no validation rules for ModelType

	if len(errors) > 0 {
		return AgentInfoMultiError(errors)
	}

	return nil
}

// AgentInfoMultiError is an error wrapping multiple validation errors returned
// by AgentInfo.ValidateAll() if the designated constraints aren't met.
type AgentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgentInfoMultiError) AllErrors() []error { return m }

// AgentInfoValidationError is the validation error returned by
// AgentInfo.Validate if the designated constraints aren't met.
type AgentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgentInfoValidationError) ErrorName() string { return "AgentInfoValidationError" }

// Error satisfies the builtin error interface
func (e AgentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgentInfoValidationError{}

// Validate checks the field values on UserInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UserInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UserInfoMultiError, or nil
// if none found.
func (m *UserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *UserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Avatar

	if len(errors) > 0 {
		return UserInfoMultiError(errors)
	}

	return nil
}

// UserInfoMultiError is an error wrapping multiple validation errors returned
// by UserInfo.ValidateAll() if the designated constraints aren't met.
type UserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UserInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UserInfoMultiError) AllErrors() []error { return m }

// UserInfoValidationError is the validation error returned by
// UserInfo.Validate if the designated constraints aren't met.
type UserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UserInfoValidationError) ErrorName() string { return "UserInfoValidationError" }

// Error satisfies the builtin error interface
func (e UserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UserInfoValidationError{}

// Validate checks the field values on DeptInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DeptInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeptInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DeptInfoMultiError, or nil
// if none found.
func (m *DeptInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DeptInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if len(errors) > 0 {
		return DeptInfoMultiError(errors)
	}

	return nil
}

// DeptInfoMultiError is an error wrapping multiple validation errors returned
// by DeptInfo.ValidateAll() if the designated constraints aren't met.
type DeptInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeptInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeptInfoMultiError) AllErrors() []error { return m }

// DeptInfoValidationError is the validation error returned by
// DeptInfo.Validate if the designated constraints aren't met.
type DeptInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeptInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeptInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeptInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeptInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeptInfoValidationError) ErrorName() string { return "DeptInfoValidationError" }

// Error satisfies the builtin error interface
func (e DeptInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeptInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeptInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeptInfoValidationError{}

// Validate checks the field values on SecurityPolicy with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SecurityPolicy) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecurityPolicy with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SecurityPolicyMultiError,
// or nil if none found.
func (m *SecurityPolicy) ValidateAll() error {
	return m.validate(true)
}

func (m *SecurityPolicy) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for PolicyCategory

	// no validation rules for RiskLevel

	// no validation rules for Enabled

	// no validation rules for HitAction

	// no validation rules for HitResponse

	// no validation rules for Id

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityPolicyValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityPolicyValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityPolicyValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityPolicyValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityPolicyValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityPolicyValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UpdatedBy

	// no validation rules for UpdatedByName

	if len(errors) > 0 {
		return SecurityPolicyMultiError(errors)
	}

	return nil
}

// SecurityPolicyMultiError is an error wrapping multiple validation errors
// returned by SecurityPolicy.ValidateAll() if the designated constraints
// aren't met.
type SecurityPolicyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecurityPolicyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecurityPolicyMultiError) AllErrors() []error { return m }

// SecurityPolicyValidationError is the validation error returned by
// SecurityPolicy.Validate if the designated constraints aren't met.
type SecurityPolicyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecurityPolicyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecurityPolicyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecurityPolicyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecurityPolicyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecurityPolicyValidationError) ErrorName() string { return "SecurityPolicyValidationError" }

// Error satisfies the builtin error interface
func (e SecurityPolicyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecurityPolicy.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecurityPolicyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecurityPolicyValidationError{}

// Validate checks the field values on CreateAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAgentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAgentRequestMultiError, or nil if none found.
func (m *CreateAgentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAgentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for WelcomeMsg

	// no validation rules for FallbackMsg

	// no validation rules for OwnerId

	// no validation rules for VisibilityType

	// no validation rules for Schema

	// no validation rules for IsPublic

	// no validation rules for IsEnabled

	// no validation rules for Description

	// no validation rules for ShowReferenceFile

	// no validation rules for ModelType

	// no validation rules for ModelID

	// no validation rules for InternetSearch

	// no validation rules for AgentType

	// no validation rules for Thinking

	// no validation rules for ThinkingModelID

	// no validation rules for RoleSetting

	for idx, item := range m.GetSecurityPolicies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateAgentRequestValidationError{
						field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateAgentRequestValidationError{
						field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateAgentRequestValidationError{
					field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UploadFile

	// no validation rules for SemanticCache

	// no validation rules for ClickedAvatar

	if len(errors) > 0 {
		return CreateAgentRequestMultiError(errors)
	}

	return nil
}

// CreateAgentRequestMultiError is an error wrapping multiple validation errors
// returned by CreateAgentRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateAgentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAgentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAgentRequestMultiError) AllErrors() []error { return m }

// CreateAgentRequestValidationError is the validation error returned by
// CreateAgentRequest.Validate if the designated constraints aren't met.
type CreateAgentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAgentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAgentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAgentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAgentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAgentRequestValidationError) ErrorName() string {
	return "CreateAgentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAgentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAgentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAgentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAgentRequestValidationError{}

// Validate checks the field values on CreateAgentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateAgentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAgentReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAgentReplyMultiError, or nil if none found.
func (m *CreateAgentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAgentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for WelcomeMsg

	// no validation rules for FallbackMsg

	// no validation rules for OwnerId

	// no validation rules for VisibilityType

	// no validation rules for Schema

	// no validation rules for IsPublic

	// no validation rules for IsEnabled

	// no validation rules for Description

	// no validation rules for ShowReferenceFile

	if len(errors) > 0 {
		return CreateAgentReplyMultiError(errors)
	}

	return nil
}

// CreateAgentReplyMultiError is an error wrapping multiple validation errors
// returned by CreateAgentReply.ValidateAll() if the designated constraints
// aren't met.
type CreateAgentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAgentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAgentReplyMultiError) AllErrors() []error { return m }

// CreateAgentReplyValidationError is the validation error returned by
// CreateAgentReply.Validate if the designated constraints aren't met.
type CreateAgentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAgentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAgentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAgentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAgentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAgentReplyValidationError) ErrorName() string { return "CreateAgentReplyValidationError" }

// Error satisfies the builtin error interface
func (e CreateAgentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAgentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAgentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAgentReplyValidationError{}

// Validate checks the field values on UpdateAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAgentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAgentRequestMultiError, or nil if none found.
func (m *UpdateAgentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAgentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for WelcomeMsg

	// no validation rules for FallbackMsg

	// no validation rules for OwnerId

	// no validation rules for VisibilityType

	// no validation rules for Schema

	// no validation rules for IsPublic

	// no validation rules for IsEnabled

	// no validation rules for Description

	// no validation rules for ShowReferenceFile

	// no validation rules for InternetSearch

	// no validation rules for RoleSetting

	// no validation rules for Thinking

	// no validation rules for ThinkingModelID

	for idx, item := range m.GetSecurityPolicies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateAgentRequestValidationError{
						field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateAgentRequestValidationError{
						field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateAgentRequestValidationError{
					field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UploadFile

	// no validation rules for SemanticCache

	// no validation rules for ModelID

	// no validation rules for ClickedAvatar

	if len(errors) > 0 {
		return UpdateAgentRequestMultiError(errors)
	}

	return nil
}

// UpdateAgentRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateAgentRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateAgentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAgentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAgentRequestMultiError) AllErrors() []error { return m }

// UpdateAgentRequestValidationError is the validation error returned by
// UpdateAgentRequest.Validate if the designated constraints aren't met.
type UpdateAgentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAgentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAgentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAgentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAgentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAgentRequestValidationError) ErrorName() string {
	return "UpdateAgentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAgentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAgentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAgentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAgentRequestValidationError{}

// Validate checks the field values on UpdateAgentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateAgentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAgentReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAgentReplyMultiError, or nil if none found.
func (m *UpdateAgentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAgentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for WelcomeMsg

	// no validation rules for FallbackMsg

	// no validation rules for OwnerId

	// no validation rules for VisibilityType

	// no validation rules for Schema

	// no validation rules for IsPublic

	// no validation rules for IsEnabled

	// no validation rules for Description

	// no validation rules for ShowReferenceFile

	if len(errors) > 0 {
		return UpdateAgentReplyMultiError(errors)
	}

	return nil
}

// UpdateAgentReplyMultiError is an error wrapping multiple validation errors
// returned by UpdateAgentReply.ValidateAll() if the designated constraints
// aren't met.
type UpdateAgentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAgentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAgentReplyMultiError) AllErrors() []error { return m }

// UpdateAgentReplyValidationError is the validation error returned by
// UpdateAgentReply.Validate if the designated constraints aren't met.
type UpdateAgentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAgentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAgentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAgentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAgentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAgentReplyValidationError) ErrorName() string { return "UpdateAgentReplyValidationError" }

// Error satisfies the builtin error interface
func (e UpdateAgentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAgentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAgentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAgentReplyValidationError{}

// Validate checks the field values on DeleteAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAgentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAgentRequestMultiError, or nil if none found.
func (m *DeleteAgentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAgentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteAgentRequestMultiError(errors)
	}

	return nil
}

// DeleteAgentRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteAgentRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteAgentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAgentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAgentRequestMultiError) AllErrors() []error { return m }

// DeleteAgentRequestValidationError is the validation error returned by
// DeleteAgentRequest.Validate if the designated constraints aren't met.
type DeleteAgentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAgentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAgentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAgentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAgentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAgentRequestValidationError) ErrorName() string {
	return "DeleteAgentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAgentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAgentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAgentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAgentRequestValidationError{}

// Validate checks the field values on DeleteAgentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteAgentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAgentReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAgentReplyMultiError, or nil if none found.
func (m *DeleteAgentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAgentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteAgentReplyMultiError(errors)
	}

	return nil
}

// DeleteAgentReplyMultiError is an error wrapping multiple validation errors
// returned by DeleteAgentReply.ValidateAll() if the designated constraints
// aren't met.
type DeleteAgentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAgentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAgentReplyMultiError) AllErrors() []error { return m }

// DeleteAgentReplyValidationError is the validation error returned by
// DeleteAgentReply.Validate if the designated constraints aren't met.
type DeleteAgentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAgentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAgentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAgentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAgentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAgentReplyValidationError) ErrorName() string { return "DeleteAgentReplyValidationError" }

// Error satisfies the builtin error interface
func (e DeleteAgentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAgentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAgentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAgentReplyValidationError{}

// Validate checks the field values on GetAgentRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAgentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAgentRequestMultiError, or nil if none found.
func (m *GetAgentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := len(m.GetIds()); l < 1 || l > 100 {
		err := GetAgentRequestValidationError{
			field:  "Ids",
			reason: "value must contain between 1 and 100 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAgentRequestMultiError(errors)
	}

	return nil
}

// GetAgentRequestMultiError is an error wrapping multiple validation errors
// returned by GetAgentRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAgentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentRequestMultiError) AllErrors() []error { return m }

// GetAgentRequestValidationError is the validation error returned by
// GetAgentRequest.Validate if the designated constraints aren't met.
type GetAgentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentRequestValidationError) ErrorName() string { return "GetAgentRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetAgentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentRequestValidationError{}

// Validate checks the field values on GetAgentReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetAgentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetAgentReplyMultiError, or
// nil if none found.
func (m *GetAgentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAgents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentReplyValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentReplyValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentReplyValidationError{
					field:  fmt.Sprintf("Agents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAgentReplyMultiError(errors)
	}

	return nil
}

// GetAgentReplyMultiError is an error wrapping multiple validation errors
// returned by GetAgentReply.ValidateAll() if the designated constraints
// aren't met.
type GetAgentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentReplyMultiError) AllErrors() []error { return m }

// GetAgentReplyValidationError is the validation error returned by
// GetAgentReply.Validate if the designated constraints aren't met.
type GetAgentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentReplyValidationError) ErrorName() string { return "GetAgentReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetAgentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentReplyValidationError{}

// Validate checks the field values on GetAgentReplyItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAgentReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentReplyItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAgentReplyItemMultiError, or nil if none found.
func (m *GetAgentReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for AvatarUrl

	// no validation rules for Avatar

	// no validation rules for WelcomeMsg

	// no validation rules for FallbackMsg

	// no validation rules for OwnerId

	// no validation rules for OwnerName

	// no validation rules for OwnerAvatar

	// no validation rules for VisibilityType

	// no validation rules for Schema

	// no validation rules for IsPublic

	// no validation rules for IsEnabled

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAgentReplyItemValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAgentReplyItemValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAgentReplyItemValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAgentReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAgentReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAgentReplyItemValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVisibleUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentReplyItemValidationError{
						field:  fmt.Sprintf("VisibleUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentReplyItemValidationError{
						field:  fmt.Sprintf("VisibleUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentReplyItemValidationError{
					field:  fmt.Sprintf("VisibleUsers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetVisibleDepts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentReplyItemValidationError{
						field:  fmt.Sprintf("VisibleDepts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentReplyItemValidationError{
						field:  fmt.Sprintf("VisibleDepts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentReplyItemValidationError{
					field:  fmt.Sprintf("VisibleDepts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ShowReferenceFile

	// no validation rules for ModelName

	// no validation rules for ModelType

	// no validation rules for ModelID

	// no validation rules for KnowledgeBaseType

	// no validation rules for InternetSearch

	// no validation rules for EnableInternetSearchSwitch

	// no validation rules for AgentType

	// no validation rules for RoleSetting

	// no validation rules for Thinking

	// no validation rules for ThinkingModelID

	// no validation rules for ThinkingModelName

	// no validation rules for ThinkingModelAvatar

	// no validation rules for ModelAvatar

	// no validation rules for ModelDetailID

	// no validation rules for ThinkingEnableStatus

	for idx, item := range m.GetSecurityPolicies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentReplyItemValidationError{
						field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentReplyItemValidationError{
						field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentReplyItemValidationError{
					field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UploadFile

	// no validation rules for SemanticCache

	for idx, item := range m.GetManageableUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentReplyItemValidationError{
						field:  fmt.Sprintf("ManageableUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentReplyItemValidationError{
						field:  fmt.Sprintf("ManageableUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentReplyItemValidationError{
					field:  fmt.Sprintf("ManageableUsers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAgentReplyItemMultiError(errors)
	}

	return nil
}

// GetAgentReplyItemMultiError is an error wrapping multiple validation errors
// returned by GetAgentReplyItem.ValidateAll() if the designated constraints
// aren't met.
type GetAgentReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentReplyItemMultiError) AllErrors() []error { return m }

// GetAgentReplyItemValidationError is the validation error returned by
// GetAgentReplyItem.Validate if the designated constraints aren't met.
type GetAgentReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentReplyItemValidationError) ErrorName() string {
	return "GetAgentReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentReplyItemValidationError{}

// Validate checks the field values on PageAgentRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PageAgentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageAgentRequestMultiError, or nil if none found.
func (m *PageAgentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageAgentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageNum() <= 0 {
		err := PageAgentRequestValidationError{
			field:  "PageNum",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageSize

	// no validation rules for ShowOnClient

	// no validation rules for ModelType

	// no validation rules for IsMine

	// no validation rules for AgentCategoryType

	// no validation rules for AgentName

	if len(errors) > 0 {
		return PageAgentRequestMultiError(errors)
	}

	return nil
}

// PageAgentRequestMultiError is an error wrapping multiple validation errors
// returned by PageAgentRequest.ValidateAll() if the designated constraints
// aren't met.
type PageAgentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageAgentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageAgentRequestMultiError) AllErrors() []error { return m }

// PageAgentRequestValidationError is the validation error returned by
// PageAgentRequest.Validate if the designated constraints aren't met.
type PageAgentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageAgentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageAgentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageAgentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageAgentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageAgentRequestValidationError) ErrorName() string { return "PageAgentRequestValidationError" }

// Error satisfies the builtin error interface
func (e PageAgentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageAgentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageAgentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageAgentRequestValidationError{}

// Validate checks the field values on PageAgentReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PageAgentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageAgentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PageAgentReplyMultiError,
// or nil if none found.
func (m *PageAgentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PageAgentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageAgentReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageAgentReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageAgentReplyValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PageAgentReplyMultiError(errors)
	}

	return nil
}

// PageAgentReplyMultiError is an error wrapping multiple validation errors
// returned by PageAgentReply.ValidateAll() if the designated constraints
// aren't met.
type PageAgentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageAgentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageAgentReplyMultiError) AllErrors() []error { return m }

// PageAgentReplyValidationError is the validation error returned by
// PageAgentReply.Validate if the designated constraints aren't met.
type PageAgentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageAgentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageAgentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageAgentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageAgentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageAgentReplyValidationError) ErrorName() string { return "PageAgentReplyValidationError" }

// Error satisfies the builtin error interface
func (e PageAgentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageAgentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageAgentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageAgentReplyValidationError{}

// Validate checks the field values on PageAgentReplyItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageAgentReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageAgentReplyItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageAgentReplyItemMultiError, or nil if none found.
func (m *PageAgentReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *PageAgentReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for AvatarUrl

	// no validation rules for WelcomeMsg

	// no validation rules for FallbackMsg

	// no validation rules for OwnerId

	// no validation rules for OwnerName

	// no validation rules for OwnerAvatar

	// no validation rules for VisibilityType

	// no validation rules for Schema

	// no validation rules for IsPublic

	// no validation rules for IsEnabled

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageAgentReplyItemValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageAgentReplyItemValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageAgentReplyItemValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageAgentReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageAgentReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageAgentReplyItemValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetVisibleUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageAgentReplyItemValidationError{
						field:  fmt.Sprintf("VisibleUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageAgentReplyItemValidationError{
						field:  fmt.Sprintf("VisibleUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageAgentReplyItemValidationError{
					field:  fmt.Sprintf("VisibleUsers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetVisibleDepts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageAgentReplyItemValidationError{
						field:  fmt.Sprintf("VisibleDepts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageAgentReplyItemValidationError{
						field:  fmt.Sprintf("VisibleDepts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageAgentReplyItemValidationError{
					field:  fmt.Sprintf("VisibleDepts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ShowReferenceFile

	// no validation rules for ModelName

	// no validation rules for ModelType

	// no validation rules for Model

	// no validation rules for KnowledgeBaseType

	// no validation rules for CanInternetSearch

	// no validation rules for AgentType

	// no validation rules for ThinkingEnableStatus

	// no validation rules for Thinking

	// no validation rules for UploadFile

	// no validation rules for SemanticCache

	// no validation rules for ModelDetailName

	for idx, item := range m.GetManageableUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageAgentReplyItemValidationError{
						field:  fmt.Sprintf("ManageableUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageAgentReplyItemValidationError{
						field:  fmt.Sprintf("ManageableUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageAgentReplyItemValidationError{
					field:  fmt.Sprintf("ManageableUsers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ClickedAvatarUrl

	if len(errors) > 0 {
		return PageAgentReplyItemMultiError(errors)
	}

	return nil
}

// PageAgentReplyItemMultiError is an error wrapping multiple validation errors
// returned by PageAgentReplyItem.ValidateAll() if the designated constraints
// aren't met.
type PageAgentReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageAgentReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageAgentReplyItemMultiError) AllErrors() []error { return m }

// PageAgentReplyItemValidationError is the validation error returned by
// PageAgentReplyItem.Validate if the designated constraints aren't met.
type PageAgentReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageAgentReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageAgentReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageAgentReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageAgentReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageAgentReplyItemValidationError) ErrorName() string {
	return "PageAgentReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e PageAgentReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageAgentReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageAgentReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageAgentReplyItemValidationError{}

// Validate checks the field values on AskAgentRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AskAgentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AskAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AskAgentRequestMultiError, or nil if none found.
func (m *AskAgentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AskAgentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := AskAgentRequestValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ChatID

	// no validation rules for RoundID

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AskAgentRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AskAgentRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AskAgentRequestValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AgentID

	// no validation rules for IsMultiRound

	// no validation rules for PcName

	// no validation rules for InternetSearch

	// no validation rules for Thinking

	// no validation rules for ChatItemID

	// no validation rules for DesignateSheetKnowledgeBaseID

	// no validation rules for ExistingTableData

	if len(errors) > 0 {
		return AskAgentRequestMultiError(errors)
	}

	return nil
}

// AskAgentRequestMultiError is an error wrapping multiple validation errors
// returned by AskAgentRequest.ValidateAll() if the designated constraints
// aren't met.
type AskAgentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AskAgentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AskAgentRequestMultiError) AllErrors() []error { return m }

// AskAgentRequestValidationError is the validation error returned by
// AskAgentRequest.Validate if the designated constraints aren't met.
type AskAgentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AskAgentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AskAgentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AskAgentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AskAgentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AskAgentRequestValidationError) ErrorName() string { return "AskAgentRequestValidationError" }

// Error satisfies the builtin error interface
func (e AskAgentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAskAgentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AskAgentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AskAgentRequestValidationError{}

// Validate checks the field values on AskAgentReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AskAgentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AskAgentReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AskAgentReplyMultiError, or
// nil if none found.
func (m *AskAgentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *AskAgentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Answer

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AskAgentReplyValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AskAgentReplyValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AskAgentReplyValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Status

	// no validation rules for RoundID

	// no validation rules for Type

	// no validation rules for Reason

	// no validation rules for ChatItemID

	// no validation rules for DebugContent

	if len(errors) > 0 {
		return AskAgentReplyMultiError(errors)
	}

	return nil
}

// AskAgentReplyMultiError is an error wrapping multiple validation errors
// returned by AskAgentReply.ValidateAll() if the designated constraints
// aren't met.
type AskAgentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AskAgentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AskAgentReplyMultiError) AllErrors() []error { return m }

// AskAgentReplyValidationError is the validation error returned by
// AskAgentReply.Validate if the designated constraints aren't met.
type AskAgentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AskAgentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AskAgentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AskAgentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AskAgentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AskAgentReplyValidationError) ErrorName() string { return "AskAgentReplyValidationError" }

// Error satisfies the builtin error interface
func (e AskAgentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAskAgentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AskAgentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AskAgentReplyValidationError{}

// Validate checks the field values on GetDefaultAvatarsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDefaultAvatarsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDefaultAvatarsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDefaultAvatarsRequestMultiError, or nil if none found.
func (m *GetDefaultAvatarsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDefaultAvatarsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AvatarType

	if len(errors) > 0 {
		return GetDefaultAvatarsRequestMultiError(errors)
	}

	return nil
}

// GetDefaultAvatarsRequestMultiError is an error wrapping multiple validation
// errors returned by GetDefaultAvatarsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDefaultAvatarsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDefaultAvatarsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDefaultAvatarsRequestMultiError) AllErrors() []error { return m }

// GetDefaultAvatarsRequestValidationError is the validation error returned by
// GetDefaultAvatarsRequest.Validate if the designated constraints aren't met.
type GetDefaultAvatarsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDefaultAvatarsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDefaultAvatarsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDefaultAvatarsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDefaultAvatarsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDefaultAvatarsRequestValidationError) ErrorName() string {
	return "GetDefaultAvatarsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDefaultAvatarsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDefaultAvatarsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDefaultAvatarsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDefaultAvatarsRequestValidationError{}

// Validate checks the field values on GetDefaultAvatarsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDefaultAvatarsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDefaultAvatarsReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDefaultAvatarsReplyMultiError, or nil if none found.
func (m *GetDefaultAvatarsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDefaultAvatarsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAvatars() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetDefaultAvatarsReplyValidationError{
						field:  fmt.Sprintf("Avatars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetDefaultAvatarsReplyValidationError{
						field:  fmt.Sprintf("Avatars[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetDefaultAvatarsReplyValidationError{
					field:  fmt.Sprintf("Avatars[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetDefaultAvatarsReplyMultiError(errors)
	}

	return nil
}

// GetDefaultAvatarsReplyMultiError is an error wrapping multiple validation
// errors returned by GetDefaultAvatarsReply.ValidateAll() if the designated
// constraints aren't met.
type GetDefaultAvatarsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDefaultAvatarsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDefaultAvatarsReplyMultiError) AllErrors() []error { return m }

// GetDefaultAvatarsReplyValidationError is the validation error returned by
// GetDefaultAvatarsReply.Validate if the designated constraints aren't met.
type GetDefaultAvatarsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDefaultAvatarsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDefaultAvatarsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDefaultAvatarsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDefaultAvatarsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDefaultAvatarsReplyValidationError) ErrorName() string {
	return "GetDefaultAvatarsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetDefaultAvatarsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDefaultAvatarsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDefaultAvatarsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDefaultAvatarsReplyValidationError{}

// Validate checks the field values on GetDefaultAvatarItems with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDefaultAvatarItems) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDefaultAvatarItems with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDefaultAvatarItemsMultiError, or nil if none found.
func (m *GetDefaultAvatarItems) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDefaultAvatarItems) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Avatar

	// no validation rules for Url

	// no validation rules for ClickedAvatar

	// no validation rules for ClickedAvatarUrl

	if len(errors) > 0 {
		return GetDefaultAvatarItemsMultiError(errors)
	}

	return nil
}

// GetDefaultAvatarItemsMultiError is an error wrapping multiple validation
// errors returned by GetDefaultAvatarItems.ValidateAll() if the designated
// constraints aren't met.
type GetDefaultAvatarItemsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDefaultAvatarItemsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDefaultAvatarItemsMultiError) AllErrors() []error { return m }

// GetDefaultAvatarItemsValidationError is the validation error returned by
// GetDefaultAvatarItems.Validate if the designated constraints aren't met.
type GetDefaultAvatarItemsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDefaultAvatarItemsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDefaultAvatarItemsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDefaultAvatarItemsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDefaultAvatarItemsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDefaultAvatarItemsValidationError) ErrorName() string {
	return "GetDefaultAvatarItemsValidationError"
}

// Error satisfies the builtin error interface
func (e GetDefaultAvatarItemsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDefaultAvatarItems.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDefaultAvatarItemsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDefaultAvatarItemsValidationError{}

// Validate checks the field values on GetFilePermissionByAgentIDRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFilePermissionByAgentIDRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFilePermissionByAgentIDRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFilePermissionByAgentIDRequestMultiError, or nil if none found.
func (m *GetFilePermissionByAgentIDRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFilePermissionByAgentIDRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentID

	// no validation rules for FileRelationID

	if len(errors) > 0 {
		return GetFilePermissionByAgentIDRequestMultiError(errors)
	}

	return nil
}

// GetFilePermissionByAgentIDRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFilePermissionByAgentIDRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFilePermissionByAgentIDRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFilePermissionByAgentIDRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFilePermissionByAgentIDRequestMultiError) AllErrors() []error { return m }

// GetFilePermissionByAgentIDRequestValidationError is the validation error
// returned by GetFilePermissionByAgentIDRequest.Validate if the designated
// constraints aren't met.
type GetFilePermissionByAgentIDRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFilePermissionByAgentIDRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFilePermissionByAgentIDRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFilePermissionByAgentIDRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFilePermissionByAgentIDRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFilePermissionByAgentIDRequestValidationError) ErrorName() string {
	return "GetFilePermissionByAgentIDRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFilePermissionByAgentIDRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFilePermissionByAgentIDRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFilePermissionByAgentIDRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFilePermissionByAgentIDRequestValidationError{}

// Validate checks the field values on GetFilePermissionByAgentIDReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFilePermissionByAgentIDReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFilePermissionByAgentIDReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFilePermissionByAgentIDReplyMultiError, or nil if none found.
func (m *GetFilePermissionByAgentIDReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFilePermissionByAgentIDReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HasPermission

	if len(errors) > 0 {
		return GetFilePermissionByAgentIDReplyMultiError(errors)
	}

	return nil
}

// GetFilePermissionByAgentIDReplyMultiError is an error wrapping multiple
// validation errors returned by GetFilePermissionByAgentIDReply.ValidateAll()
// if the designated constraints aren't met.
type GetFilePermissionByAgentIDReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFilePermissionByAgentIDReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFilePermissionByAgentIDReplyMultiError) AllErrors() []error { return m }

// GetFilePermissionByAgentIDReplyValidationError is the validation error
// returned by GetFilePermissionByAgentIDReply.Validate if the designated
// constraints aren't met.
type GetFilePermissionByAgentIDReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFilePermissionByAgentIDReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFilePermissionByAgentIDReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFilePermissionByAgentIDReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFilePermissionByAgentIDReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFilePermissionByAgentIDReplyValidationError) ErrorName() string {
	return "GetFilePermissionByAgentIDReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetFilePermissionByAgentIDReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFilePermissionByAgentIDReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFilePermissionByAgentIDReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFilePermissionByAgentIDReplyValidationError{}

// Validate checks the field values on
// QueryModelAskClassificationDistributionRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QueryModelAskClassificationDistributionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryModelAskClassificationDistributionRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// QueryModelAskClassificationDistributionRequestMultiError, or nil if none found.
func (m *QueryModelAskClassificationDistributionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryModelAskClassificationDistributionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModelType

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryModelAskClassificationDistributionRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryModelAskClassificationDistributionRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryModelAskClassificationDistributionRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryModelAskClassificationDistributionRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryModelAskClassificationDistributionRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryModelAskClassificationDistributionRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QueryModelAskClassificationDistributionRequestMultiError(errors)
	}

	return nil
}

// QueryModelAskClassificationDistributionRequestMultiError is an error
// wrapping multiple validation errors returned by
// QueryModelAskClassificationDistributionRequest.ValidateAll() if the
// designated constraints aren't met.
type QueryModelAskClassificationDistributionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryModelAskClassificationDistributionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryModelAskClassificationDistributionRequestMultiError) AllErrors() []error { return m }

// QueryModelAskClassificationDistributionRequestValidationError is the
// validation error returned by
// QueryModelAskClassificationDistributionRequest.Validate if the designated
// constraints aren't met.
type QueryModelAskClassificationDistributionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryModelAskClassificationDistributionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryModelAskClassificationDistributionRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e QueryModelAskClassificationDistributionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryModelAskClassificationDistributionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryModelAskClassificationDistributionRequestValidationError) ErrorName() string {
	return "QueryModelAskClassificationDistributionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryModelAskClassificationDistributionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryModelAskClassificationDistributionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryModelAskClassificationDistributionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryModelAskClassificationDistributionRequestValidationError{}

// Validate checks the field values on
// QueryModelAskClassificationDistributionReply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QueryModelAskClassificationDistributionReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryModelAskClassificationDistributionReply with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// QueryModelAskClassificationDistributionReplyMultiError, or nil if none found.
func (m *QueryModelAskClassificationDistributionReply) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryModelAskClassificationDistributionReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClassificationDistributions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryModelAskClassificationDistributionReplyValidationError{
						field:  fmt.Sprintf("ClassificationDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryModelAskClassificationDistributionReplyValidationError{
						field:  fmt.Sprintf("ClassificationDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryModelAskClassificationDistributionReplyValidationError{
					field:  fmt.Sprintf("ClassificationDistributions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QueryModelAskClassificationDistributionReplyMultiError(errors)
	}

	return nil
}

// QueryModelAskClassificationDistributionReplyMultiError is an error wrapping
// multiple validation errors returned by
// QueryModelAskClassificationDistributionReply.ValidateAll() if the
// designated constraints aren't met.
type QueryModelAskClassificationDistributionReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryModelAskClassificationDistributionReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryModelAskClassificationDistributionReplyMultiError) AllErrors() []error { return m }

// QueryModelAskClassificationDistributionReplyValidationError is the
// validation error returned by
// QueryModelAskClassificationDistributionReply.Validate if the designated
// constraints aren't met.
type QueryModelAskClassificationDistributionReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryModelAskClassificationDistributionReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryModelAskClassificationDistributionReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryModelAskClassificationDistributionReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryModelAskClassificationDistributionReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryModelAskClassificationDistributionReplyValidationError) ErrorName() string {
	return "QueryModelAskClassificationDistributionReplyValidationError"
}

// Error satisfies the builtin error interface
func (e QueryModelAskClassificationDistributionReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryModelAskClassificationDistributionReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryModelAskClassificationDistributionReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryModelAskClassificationDistributionReplyValidationError{}

// Validate checks the field values on QueryUploadFileTypeDistributionRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *QueryUploadFileTypeDistributionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryUploadFileTypeDistributionRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// QueryUploadFileTypeDistributionRequestMultiError, or nil if none found.
func (m *QueryUploadFileTypeDistributionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryUploadFileTypeDistributionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModelType

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryUploadFileTypeDistributionRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryUploadFileTypeDistributionRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryUploadFileTypeDistributionRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryUploadFileTypeDistributionRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryUploadFileTypeDistributionRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryUploadFileTypeDistributionRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return QueryUploadFileTypeDistributionRequestMultiError(errors)
	}

	return nil
}

// QueryUploadFileTypeDistributionRequestMultiError is an error wrapping
// multiple validation errors returned by
// QueryUploadFileTypeDistributionRequest.ValidateAll() if the designated
// constraints aren't met.
type QueryUploadFileTypeDistributionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryUploadFileTypeDistributionRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryUploadFileTypeDistributionRequestMultiError) AllErrors() []error { return m }

// QueryUploadFileTypeDistributionRequestValidationError is the validation
// error returned by QueryUploadFileTypeDistributionRequest.Validate if the
// designated constraints aren't met.
type QueryUploadFileTypeDistributionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryUploadFileTypeDistributionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryUploadFileTypeDistributionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryUploadFileTypeDistributionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryUploadFileTypeDistributionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryUploadFileTypeDistributionRequestValidationError) ErrorName() string {
	return "QueryUploadFileTypeDistributionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryUploadFileTypeDistributionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryUploadFileTypeDistributionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryUploadFileTypeDistributionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryUploadFileTypeDistributionRequestValidationError{}

// Validate checks the field values on QueryUploadFileTypeDistributionReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *QueryUploadFileTypeDistributionReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryUploadFileTypeDistributionReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// QueryUploadFileTypeDistributionReplyMultiError, or nil if none found.
func (m *QueryUploadFileTypeDistributionReply) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryUploadFileTypeDistributionReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetFileTypeDistributions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryUploadFileTypeDistributionReplyValidationError{
						field:  fmt.Sprintf("FileTypeDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryUploadFileTypeDistributionReplyValidationError{
						field:  fmt.Sprintf("FileTypeDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryUploadFileTypeDistributionReplyValidationError{
					field:  fmt.Sprintf("FileTypeDistributions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QueryUploadFileTypeDistributionReplyMultiError(errors)
	}

	return nil
}

// QueryUploadFileTypeDistributionReplyMultiError is an error wrapping multiple
// validation errors returned by
// QueryUploadFileTypeDistributionReply.ValidateAll() if the designated
// constraints aren't met.
type QueryUploadFileTypeDistributionReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryUploadFileTypeDistributionReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryUploadFileTypeDistributionReplyMultiError) AllErrors() []error { return m }

// QueryUploadFileTypeDistributionReplyValidationError is the validation error
// returned by QueryUploadFileTypeDistributionReply.Validate if the designated
// constraints aren't met.
type QueryUploadFileTypeDistributionReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryUploadFileTypeDistributionReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryUploadFileTypeDistributionReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryUploadFileTypeDistributionReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryUploadFileTypeDistributionReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryUploadFileTypeDistributionReplyValidationError) ErrorName() string {
	return "QueryUploadFileTypeDistributionReplyValidationError"
}

// Error satisfies the builtin error interface
func (e QueryUploadFileTypeDistributionReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryUploadFileTypeDistributionReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryUploadFileTypeDistributionReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryUploadFileTypeDistributionReplyValidationError{}

// Validate checks the field values on QueryModelAskClassificationTop10Request
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *QueryModelAskClassificationTop10Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryModelAskClassificationTop10Request with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// QueryModelAskClassificationTop10RequestMultiError, or nil if none found.
func (m *QueryModelAskClassificationTop10Request) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryModelAskClassificationTop10Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TopType

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryModelAskClassificationTop10RequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryModelAskClassificationTop10RequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryModelAskClassificationTop10RequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryModelAskClassificationTop10RequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryModelAskClassificationTop10RequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryModelAskClassificationTop10RequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ModelType

	if len(errors) > 0 {
		return QueryModelAskClassificationTop10RequestMultiError(errors)
	}

	return nil
}

// QueryModelAskClassificationTop10RequestMultiError is an error wrapping
// multiple validation errors returned by
// QueryModelAskClassificationTop10Request.ValidateAll() if the designated
// constraints aren't met.
type QueryModelAskClassificationTop10RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryModelAskClassificationTop10RequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryModelAskClassificationTop10RequestMultiError) AllErrors() []error { return m }

// QueryModelAskClassificationTop10RequestValidationError is the validation
// error returned by QueryModelAskClassificationTop10Request.Validate if the
// designated constraints aren't met.
type QueryModelAskClassificationTop10RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryModelAskClassificationTop10RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryModelAskClassificationTop10RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryModelAskClassificationTop10RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryModelAskClassificationTop10RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryModelAskClassificationTop10RequestValidationError) ErrorName() string {
	return "QueryModelAskClassificationTop10RequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryModelAskClassificationTop10RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryModelAskClassificationTop10Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryModelAskClassificationTop10RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryModelAskClassificationTop10RequestValidationError{}

// Validate checks the field values on QueryModelAskClassificationTop10Reply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *QueryModelAskClassificationTop10Reply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryModelAskClassificationTop10Reply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// QueryModelAskClassificationTop10ReplyMultiError, or nil if none found.
func (m *QueryModelAskClassificationTop10Reply) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryModelAskClassificationTop10Reply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryModelAskClassificationTop10ReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryModelAskClassificationTop10ReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryModelAskClassificationTop10ReplyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QueryModelAskClassificationTop10ReplyMultiError(errors)
	}

	return nil
}

// QueryModelAskClassificationTop10ReplyMultiError is an error wrapping
// multiple validation errors returned by
// QueryModelAskClassificationTop10Reply.ValidateAll() if the designated
// constraints aren't met.
type QueryModelAskClassificationTop10ReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryModelAskClassificationTop10ReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryModelAskClassificationTop10ReplyMultiError) AllErrors() []error { return m }

// QueryModelAskClassificationTop10ReplyValidationError is the validation error
// returned by QueryModelAskClassificationTop10Reply.Validate if the
// designated constraints aren't met.
type QueryModelAskClassificationTop10ReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryModelAskClassificationTop10ReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryModelAskClassificationTop10ReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryModelAskClassificationTop10ReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryModelAskClassificationTop10ReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryModelAskClassificationTop10ReplyValidationError) ErrorName() string {
	return "QueryModelAskClassificationTop10ReplyValidationError"
}

// Error satisfies the builtin error interface
func (e QueryModelAskClassificationTop10ReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryModelAskClassificationTop10Reply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryModelAskClassificationTop10ReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryModelAskClassificationTop10ReplyValidationError{}

// Validate checks the field values on QueryUploadFileTypeTop10Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryUploadFileTypeTop10Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryUploadFileTypeTop10Request with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// QueryUploadFileTypeTop10RequestMultiError, or nil if none found.
func (m *QueryUploadFileTypeTop10Request) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryUploadFileTypeTop10Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TopType

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryUploadFileTypeTop10RequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryUploadFileTypeTop10RequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryUploadFileTypeTop10RequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, QueryUploadFileTypeTop10RequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, QueryUploadFileTypeTop10RequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return QueryUploadFileTypeTop10RequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ModelType

	if len(errors) > 0 {
		return QueryUploadFileTypeTop10RequestMultiError(errors)
	}

	return nil
}

// QueryUploadFileTypeTop10RequestMultiError is an error wrapping multiple
// validation errors returned by QueryUploadFileTypeTop10Request.ValidateAll()
// if the designated constraints aren't met.
type QueryUploadFileTypeTop10RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryUploadFileTypeTop10RequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryUploadFileTypeTop10RequestMultiError) AllErrors() []error { return m }

// QueryUploadFileTypeTop10RequestValidationError is the validation error
// returned by QueryUploadFileTypeTop10Request.Validate if the designated
// constraints aren't met.
type QueryUploadFileTypeTop10RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryUploadFileTypeTop10RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryUploadFileTypeTop10RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryUploadFileTypeTop10RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryUploadFileTypeTop10RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryUploadFileTypeTop10RequestValidationError) ErrorName() string {
	return "QueryUploadFileTypeTop10RequestValidationError"
}

// Error satisfies the builtin error interface
func (e QueryUploadFileTypeTop10RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryUploadFileTypeTop10Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryUploadFileTypeTop10RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryUploadFileTypeTop10RequestValidationError{}

// Validate checks the field values on QueryUploadFileTypeTop10Reply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryUploadFileTypeTop10Reply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QueryUploadFileTypeTop10Reply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// QueryUploadFileTypeTop10ReplyMultiError, or nil if none found.
func (m *QueryUploadFileTypeTop10Reply) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryUploadFileTypeTop10Reply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryUploadFileTypeTop10ReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryUploadFileTypeTop10ReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryUploadFileTypeTop10ReplyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return QueryUploadFileTypeTop10ReplyMultiError(errors)
	}

	return nil
}

// QueryUploadFileTypeTop10ReplyMultiError is an error wrapping multiple
// validation errors returned by QueryUploadFileTypeTop10Reply.ValidateAll()
// if the designated constraints aren't met.
type QueryUploadFileTypeTop10ReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryUploadFileTypeTop10ReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryUploadFileTypeTop10ReplyMultiError) AllErrors() []error { return m }

// QueryUploadFileTypeTop10ReplyValidationError is the validation error
// returned by QueryUploadFileTypeTop10Reply.Validate if the designated
// constraints aren't met.
type QueryUploadFileTypeTop10ReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryUploadFileTypeTop10ReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QueryUploadFileTypeTop10ReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QueryUploadFileTypeTop10ReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QueryUploadFileTypeTop10ReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryUploadFileTypeTop10ReplyValidationError) ErrorName() string {
	return "QueryUploadFileTypeTop10ReplyValidationError"
}

// Error satisfies the builtin error interface
func (e QueryUploadFileTypeTop10ReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryUploadFileTypeTop10Reply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryUploadFileTypeTop10ReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryUploadFileTypeTop10ReplyValidationError{}

// Validate checks the field values on GetAllAgentInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllAgentInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllAgentInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllAgentInfoRequestMultiError, or nil if none found.
func (m *GetAllAgentInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllAgentInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModelType

	if len(errors) > 0 {
		return GetAllAgentInfoRequestMultiError(errors)
	}

	return nil
}

// GetAllAgentInfoRequestMultiError is an error wrapping multiple validation
// errors returned by GetAllAgentInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAllAgentInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllAgentInfoRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllAgentInfoRequestMultiError) AllErrors() []error { return m }

// GetAllAgentInfoRequestValidationError is the validation error returned by
// GetAllAgentInfoRequest.Validate if the designated constraints aren't met.
type GetAllAgentInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllAgentInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllAgentInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllAgentInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllAgentInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllAgentInfoRequestValidationError) ErrorName() string {
	return "GetAllAgentInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllAgentInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllAgentInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllAgentInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllAgentInfoRequestValidationError{}

// Validate checks the field values on GetAllAgentInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllAgentInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllAgentInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllAgentInfoReplyMultiError, or nil if none found.
func (m *GetAllAgentInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllAgentInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAgents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllAgentInfoReplyValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllAgentInfoReplyValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllAgentInfoReplyValidationError{
					field:  fmt.Sprintf("Agents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllAgentInfoReplyMultiError(errors)
	}

	return nil
}

// GetAllAgentInfoReplyMultiError is an error wrapping multiple validation
// errors returned by GetAllAgentInfoReply.ValidateAll() if the designated
// constraints aren't met.
type GetAllAgentInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllAgentInfoReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllAgentInfoReplyMultiError) AllErrors() []error { return m }

// GetAllAgentInfoReplyValidationError is the validation error returned by
// GetAllAgentInfoReply.Validate if the designated constraints aren't met.
type GetAllAgentInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllAgentInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllAgentInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllAgentInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllAgentInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllAgentInfoReplyValidationError) ErrorName() string {
	return "GetAllAgentInfoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllAgentInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllAgentInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllAgentInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllAgentInfoReplyValidationError{}

// Validate checks the field values on SaveAgentQueueWhiteListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveAgentQueueWhiteListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveAgentQueueWhiteListRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SaveAgentQueueWhiteListRequestMultiError, or nil if none found.
func (m *SaveAgentQueueWhiteListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveAgentQueueWhiteListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SaveAgentQueueWhiteListRequestMultiError(errors)
	}

	return nil
}

// SaveAgentQueueWhiteListRequestMultiError is an error wrapping multiple
// validation errors returned by SaveAgentQueueWhiteListRequest.ValidateAll()
// if the designated constraints aren't met.
type SaveAgentQueueWhiteListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveAgentQueueWhiteListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveAgentQueueWhiteListRequestMultiError) AllErrors() []error { return m }

// SaveAgentQueueWhiteListRequestValidationError is the validation error
// returned by SaveAgentQueueWhiteListRequest.Validate if the designated
// constraints aren't met.
type SaveAgentQueueWhiteListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveAgentQueueWhiteListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveAgentQueueWhiteListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveAgentQueueWhiteListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveAgentQueueWhiteListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveAgentQueueWhiteListRequestValidationError) ErrorName() string {
	return "SaveAgentQueueWhiteListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SaveAgentQueueWhiteListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveAgentQueueWhiteListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveAgentQueueWhiteListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveAgentQueueWhiteListRequestValidationError{}

// Validate checks the field values on SaveAgentQueueWhiteListReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaveAgentQueueWhiteListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaveAgentQueueWhiteListReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaveAgentQueueWhiteListReplyMultiError, or nil if none found.
func (m *SaveAgentQueueWhiteListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SaveAgentQueueWhiteListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SaveAgentQueueWhiteListReplyMultiError(errors)
	}

	return nil
}

// SaveAgentQueueWhiteListReplyMultiError is an error wrapping multiple
// validation errors returned by SaveAgentQueueWhiteListReply.ValidateAll() if
// the designated constraints aren't met.
type SaveAgentQueueWhiteListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaveAgentQueueWhiteListReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaveAgentQueueWhiteListReplyMultiError) AllErrors() []error { return m }

// SaveAgentQueueWhiteListReplyValidationError is the validation error returned
// by SaveAgentQueueWhiteListReply.Validate if the designated constraints
// aren't met.
type SaveAgentQueueWhiteListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaveAgentQueueWhiteListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaveAgentQueueWhiteListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaveAgentQueueWhiteListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaveAgentQueueWhiteListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaveAgentQueueWhiteListReplyValidationError) ErrorName() string {
	return "SaveAgentQueueWhiteListReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SaveAgentQueueWhiteListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaveAgentQueueWhiteListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaveAgentQueueWhiteListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaveAgentQueueWhiteListReplyValidationError{}

// Validate checks the field values on GetAgentQueueWhiteListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAgentQueueWhiteListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentQueueWhiteListRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAgentQueueWhiteListRequestMultiError, or nil if none found.
func (m *GetAgentQueueWhiteListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentQueueWhiteListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAgentQueueWhiteListRequestMultiError(errors)
	}

	return nil
}

// GetAgentQueueWhiteListRequestMultiError is an error wrapping multiple
// validation errors returned by GetAgentQueueWhiteListRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAgentQueueWhiteListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentQueueWhiteListRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentQueueWhiteListRequestMultiError) AllErrors() []error { return m }

// GetAgentQueueWhiteListRequestValidationError is the validation error
// returned by GetAgentQueueWhiteListRequest.Validate if the designated
// constraints aren't met.
type GetAgentQueueWhiteListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentQueueWhiteListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentQueueWhiteListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentQueueWhiteListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentQueueWhiteListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentQueueWhiteListRequestValidationError) ErrorName() string {
	return "GetAgentQueueWhiteListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentQueueWhiteListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentQueueWhiteListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentQueueWhiteListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentQueueWhiteListRequestValidationError{}

// Validate checks the field values on GetAgentQueueWhiteListReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAgentQueueWhiteListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentQueueWhiteListReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAgentQueueWhiteListReplyMultiError, or nil if none found.
func (m *GetAgentQueueWhiteListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentQueueWhiteListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentQueueWhiteListReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentQueueWhiteListReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentQueueWhiteListReplyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAgentQueueWhiteListReplyMultiError(errors)
	}

	return nil
}

// GetAgentQueueWhiteListReplyMultiError is an error wrapping multiple
// validation errors returned by GetAgentQueueWhiteListReply.ValidateAll() if
// the designated constraints aren't met.
type GetAgentQueueWhiteListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentQueueWhiteListReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentQueueWhiteListReplyMultiError) AllErrors() []error { return m }

// GetAgentQueueWhiteListReplyValidationError is the validation error returned
// by GetAgentQueueWhiteListReply.Validate if the designated constraints
// aren't met.
type GetAgentQueueWhiteListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentQueueWhiteListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentQueueWhiteListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentQueueWhiteListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentQueueWhiteListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentQueueWhiteListReplyValidationError) ErrorName() string {
	return "GetAgentQueueWhiteListReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentQueueWhiteListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentQueueWhiteListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentQueueWhiteListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentQueueWhiteListReplyValidationError{}

// Validate checks the field values on GetAgentQueueWhiteListItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAgentQueueWhiteListItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentQueueWhiteListItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAgentQueueWhiteListItemMultiError, or nil if none found.
func (m *GetAgentQueueWhiteListItem) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentQueueWhiteListItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserID

	// no validation rules for UserName

	// no validation rules for UserAvatar

	if len(errors) > 0 {
		return GetAgentQueueWhiteListItemMultiError(errors)
	}

	return nil
}

// GetAgentQueueWhiteListItemMultiError is an error wrapping multiple
// validation errors returned by GetAgentQueueWhiteListItem.ValidateAll() if
// the designated constraints aren't met.
type GetAgentQueueWhiteListItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentQueueWhiteListItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentQueueWhiteListItemMultiError) AllErrors() []error { return m }

// GetAgentQueueWhiteListItemValidationError is the validation error returned
// by GetAgentQueueWhiteListItem.Validate if the designated constraints aren't met.
type GetAgentQueueWhiteListItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentQueueWhiteListItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentQueueWhiteListItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentQueueWhiteListItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentQueueWhiteListItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentQueueWhiteListItemValidationError) ErrorName() string {
	return "GetAgentQueueWhiteListItemValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentQueueWhiteListItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentQueueWhiteListItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentQueueWhiteListItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentQueueWhiteListItemValidationError{}

// Validate checks the field values on UpdateAgentSortRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAgentSortRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAgentSortRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAgentSortRequestMultiError, or nil if none found.
func (m *UpdateAgentSortRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAgentSortRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateAgentSortRequestValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateAgentSortRequestValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateAgentSortRequestValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateAgentSortRequestMultiError(errors)
	}

	return nil
}

// UpdateAgentSortRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAgentSortRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAgentSortRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAgentSortRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAgentSortRequestMultiError) AllErrors() []error { return m }

// UpdateAgentSortRequestValidationError is the validation error returned by
// UpdateAgentSortRequest.Validate if the designated constraints aren't met.
type UpdateAgentSortRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAgentSortRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAgentSortRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAgentSortRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAgentSortRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAgentSortRequestValidationError) ErrorName() string {
	return "UpdateAgentSortRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAgentSortRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAgentSortRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAgentSortRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAgentSortRequestValidationError{}

// Validate checks the field values on UpdateAgentSortRequestItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAgentSortRequestItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAgentSortRequestItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAgentSortRequestItemMultiError, or nil if none found.
func (m *UpdateAgentSortRequestItem) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAgentSortRequestItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentID

	// no validation rules for Index

	if len(errors) > 0 {
		return UpdateAgentSortRequestItemMultiError(errors)
	}

	return nil
}

// UpdateAgentSortRequestItemMultiError is an error wrapping multiple
// validation errors returned by UpdateAgentSortRequestItem.ValidateAll() if
// the designated constraints aren't met.
type UpdateAgentSortRequestItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAgentSortRequestItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAgentSortRequestItemMultiError) AllErrors() []error { return m }

// UpdateAgentSortRequestItemValidationError is the validation error returned
// by UpdateAgentSortRequestItem.Validate if the designated constraints aren't met.
type UpdateAgentSortRequestItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAgentSortRequestItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAgentSortRequestItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAgentSortRequestItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAgentSortRequestItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAgentSortRequestItemValidationError) ErrorName() string {
	return "UpdateAgentSortRequestItemValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAgentSortRequestItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAgentSortRequestItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAgentSortRequestItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAgentSortRequestItemValidationError{}

// Validate checks the field values on UpdateAgentSortReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAgentSortReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAgentSortReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAgentSortReplyMultiError, or nil if none found.
func (m *UpdateAgentSortReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAgentSortReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateAgentSortReplyMultiError(errors)
	}

	return nil
}

// UpdateAgentSortReplyMultiError is an error wrapping multiple validation
// errors returned by UpdateAgentSortReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateAgentSortReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAgentSortReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAgentSortReplyMultiError) AllErrors() []error { return m }

// UpdateAgentSortReplyValidationError is the validation error returned by
// UpdateAgentSortReply.Validate if the designated constraints aren't met.
type UpdateAgentSortReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAgentSortReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAgentSortReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAgentSortReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAgentSortReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAgentSortReplyValidationError) ErrorName() string {
	return "UpdateAgentSortReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAgentSortReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAgentSortReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAgentSortReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAgentSortReplyValidationError{}

// Validate checks the field values on TransferAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransferAgentRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransferAgentRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransferAgentRequestMultiError, or nil if none found.
func (m *TransferAgentRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TransferAgentRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NewOwnerID

	if len(errors) > 0 {
		return TransferAgentRequestMultiError(errors)
	}

	return nil
}

// TransferAgentRequestMultiError is an error wrapping multiple validation
// errors returned by TransferAgentRequest.ValidateAll() if the designated
// constraints aren't met.
type TransferAgentRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransferAgentRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransferAgentRequestMultiError) AllErrors() []error { return m }

// TransferAgentRequestValidationError is the validation error returned by
// TransferAgentRequest.Validate if the designated constraints aren't met.
type TransferAgentRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransferAgentRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransferAgentRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransferAgentRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransferAgentRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransferAgentRequestValidationError) ErrorName() string {
	return "TransferAgentRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TransferAgentRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransferAgentRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransferAgentRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransferAgentRequestValidationError{}

// Validate checks the field values on TransferAgentReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransferAgentReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransferAgentReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransferAgentReplyMultiError, or nil if none found.
func (m *TransferAgentReply) ValidateAll() error {
	return m.validate(true)
}

func (m *TransferAgentReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return TransferAgentReplyMultiError(errors)
	}

	return nil
}

// TransferAgentReplyMultiError is an error wrapping multiple validation errors
// returned by TransferAgentReply.ValidateAll() if the designated constraints
// aren't met.
type TransferAgentReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransferAgentReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransferAgentReplyMultiError) AllErrors() []error { return m }

// TransferAgentReplyValidationError is the validation error returned by
// TransferAgentReply.Validate if the designated constraints aren't met.
type TransferAgentReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransferAgentReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransferAgentReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransferAgentReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransferAgentReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransferAgentReplyValidationError) ErrorName() string {
	return "TransferAgentReplyValidationError"
}

// Error satisfies the builtin error interface
func (e TransferAgentReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransferAgentReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransferAgentReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransferAgentReplyValidationError{}

// Validate checks the field values on CheckQuestionSecurityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckQuestionSecurityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckQuestionSecurityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckQuestionSecurityRequestMultiError, or nil if none found.
func (m *CheckQuestionSecurityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckQuestionSecurityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := CheckQuestionSecurityRequestValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AgentID

	for idx, item := range m.GetUploadedFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CheckQuestionSecurityRequestValidationError{
						field:  fmt.Sprintf("UploadedFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CheckQuestionSecurityRequestValidationError{
						field:  fmt.Sprintf("UploadedFiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CheckQuestionSecurityRequestValidationError{
					field:  fmt.Sprintf("UploadedFiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ChatID

	// no validation rules for PcName

	if len(errors) > 0 {
		return CheckQuestionSecurityRequestMultiError(errors)
	}

	return nil
}

// CheckQuestionSecurityRequestMultiError is an error wrapping multiple
// validation errors returned by CheckQuestionSecurityRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckQuestionSecurityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckQuestionSecurityRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckQuestionSecurityRequestMultiError) AllErrors() []error { return m }

// CheckQuestionSecurityRequestValidationError is the validation error returned
// by CheckQuestionSecurityRequest.Validate if the designated constraints
// aren't met.
type CheckQuestionSecurityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckQuestionSecurityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckQuestionSecurityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckQuestionSecurityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckQuestionSecurityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckQuestionSecurityRequestValidationError) ErrorName() string {
	return "CheckQuestionSecurityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckQuestionSecurityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckQuestionSecurityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckQuestionSecurityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckQuestionSecurityRequestValidationError{}

// Validate checks the field values on CheckQuestionSecurityReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckQuestionSecurityReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckQuestionSecurityReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckQuestionSecurityReplyMultiError, or nil if none found.
func (m *CheckQuestionSecurityReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckQuestionSecurityReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RiskLevel

	// no validation rules for HitAction

	// no validation rules for HitResponse

	// no validation rules for ChatItemID

	if len(errors) > 0 {
		return CheckQuestionSecurityReplyMultiError(errors)
	}

	return nil
}

// CheckQuestionSecurityReplyMultiError is an error wrapping multiple
// validation errors returned by CheckQuestionSecurityReply.ValidateAll() if
// the designated constraints aren't met.
type CheckQuestionSecurityReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckQuestionSecurityReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckQuestionSecurityReplyMultiError) AllErrors() []error { return m }

// CheckQuestionSecurityReplyValidationError is the validation error returned
// by CheckQuestionSecurityReply.Validate if the designated constraints aren't met.
type CheckQuestionSecurityReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckQuestionSecurityReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckQuestionSecurityReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckQuestionSecurityReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckQuestionSecurityReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckQuestionSecurityReplyValidationError) ErrorName() string {
	return "CheckQuestionSecurityReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CheckQuestionSecurityReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckQuestionSecurityReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckQuestionSecurityReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckQuestionSecurityReplyValidationError{}

// Validate checks the field values on PageAgentSecurityLogsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageAgentSecurityLogsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageAgentSecurityLogsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageAgentSecurityLogsRequestMultiError, or nil if none found.
func (m *PageAgentSecurityLogsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageAgentSecurityLogsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageNum() <= 0 {
		err := PageAgentSecurityLogsRequestValidationError{
			field:  "PageNum",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for PageSize

	// no validation rules for UserName

	// no validation rules for DeptName

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageAgentSecurityLogsRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageAgentSecurityLogsRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageAgentSecurityLogsRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageAgentSecurityLogsRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageAgentSecurityLogsRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageAgentSecurityLogsRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PageAgentSecurityLogsRequestMultiError(errors)
	}

	return nil
}

// PageAgentSecurityLogsRequestMultiError is an error wrapping multiple
// validation errors returned by PageAgentSecurityLogsRequest.ValidateAll() if
// the designated constraints aren't met.
type PageAgentSecurityLogsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageAgentSecurityLogsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageAgentSecurityLogsRequestMultiError) AllErrors() []error { return m }

// PageAgentSecurityLogsRequestValidationError is the validation error returned
// by PageAgentSecurityLogsRequest.Validate if the designated constraints
// aren't met.
type PageAgentSecurityLogsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageAgentSecurityLogsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageAgentSecurityLogsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageAgentSecurityLogsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageAgentSecurityLogsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageAgentSecurityLogsRequestValidationError) ErrorName() string {
	return "PageAgentSecurityLogsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PageAgentSecurityLogsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageAgentSecurityLogsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageAgentSecurityLogsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageAgentSecurityLogsRequestValidationError{}

// Validate checks the field values on PageAgentSecurityLogsReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageAgentSecurityLogsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageAgentSecurityLogsReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageAgentSecurityLogsReplyMultiError, or nil if none found.
func (m *PageAgentSecurityLogsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PageAgentSecurityLogsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageAgentSecurityLogsReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageAgentSecurityLogsReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageAgentSecurityLogsReplyValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PageAgentSecurityLogsReplyMultiError(errors)
	}

	return nil
}

// PageAgentSecurityLogsReplyMultiError is an error wrapping multiple
// validation errors returned by PageAgentSecurityLogsReply.ValidateAll() if
// the designated constraints aren't met.
type PageAgentSecurityLogsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageAgentSecurityLogsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageAgentSecurityLogsReplyMultiError) AllErrors() []error { return m }

// PageAgentSecurityLogsReplyValidationError is the validation error returned
// by PageAgentSecurityLogsReply.Validate if the designated constraints aren't met.
type PageAgentSecurityLogsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageAgentSecurityLogsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageAgentSecurityLogsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageAgentSecurityLogsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageAgentSecurityLogsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageAgentSecurityLogsReplyValidationError) ErrorName() string {
	return "PageAgentSecurityLogsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e PageAgentSecurityLogsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageAgentSecurityLogsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageAgentSecurityLogsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageAgentSecurityLogsReplyValidationError{}

// Validate checks the field values on PageAgentSecurityLogsReplyItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageAgentSecurityLogsReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageAgentSecurityLogsReplyItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PageAgentSecurityLogsReplyItemMultiError, or nil if none found.
func (m *PageAgentSecurityLogsReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *PageAgentSecurityLogsReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for RiskLevel

	// no validation rules for HitAction

	// no validation rules for UserID

	// no validation rules for UserName

	// no validation rules for DeptName

	// no validation rules for UserAvatar

	// no validation rules for AgentID

	// no validation rules for AgentName

	// no validation rules for ActionCategory

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageAgentSecurityLogsReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageAgentSecurityLogsReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageAgentSecurityLogsReplyItemValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PcName

	// no validation rules for AgentAvatar

	if len(errors) > 0 {
		return PageAgentSecurityLogsReplyItemMultiError(errors)
	}

	return nil
}

// PageAgentSecurityLogsReplyItemMultiError is an error wrapping multiple
// validation errors returned by PageAgentSecurityLogsReplyItem.ValidateAll()
// if the designated constraints aren't met.
type PageAgentSecurityLogsReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageAgentSecurityLogsReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageAgentSecurityLogsReplyItemMultiError) AllErrors() []error { return m }

// PageAgentSecurityLogsReplyItemValidationError is the validation error
// returned by PageAgentSecurityLogsReplyItem.Validate if the designated
// constraints aren't met.
type PageAgentSecurityLogsReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageAgentSecurityLogsReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageAgentSecurityLogsReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageAgentSecurityLogsReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageAgentSecurityLogsReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageAgentSecurityLogsReplyItemValidationError) ErrorName() string {
	return "PageAgentSecurityLogsReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e PageAgentSecurityLogsReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageAgentSecurityLogsReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageAgentSecurityLogsReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageAgentSecurityLogsReplyItemValidationError{}

// Validate checks the field values on GetAgentSecurityLogsCountRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAgentSecurityLogsCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentSecurityLogsCountRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAgentSecurityLogsCountRequestMultiError, or nil if none found.
func (m *GetAgentSecurityLogsCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentSecurityLogsCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAgentSecurityLogsCountRequestMultiError(errors)
	}

	return nil
}

// GetAgentSecurityLogsCountRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAgentSecurityLogsCountRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAgentSecurityLogsCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentSecurityLogsCountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentSecurityLogsCountRequestMultiError) AllErrors() []error { return m }

// GetAgentSecurityLogsCountRequestValidationError is the validation error
// returned by GetAgentSecurityLogsCountRequest.Validate if the designated
// constraints aren't met.
type GetAgentSecurityLogsCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentSecurityLogsCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentSecurityLogsCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentSecurityLogsCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentSecurityLogsCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentSecurityLogsCountRequestValidationError) ErrorName() string {
	return "GetAgentSecurityLogsCountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentSecurityLogsCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentSecurityLogsCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentSecurityLogsCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentSecurityLogsCountRequestValidationError{}

// Validate checks the field values on GetAgentSecurityLogsCountReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAgentSecurityLogsCountReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentSecurityLogsCountReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAgentSecurityLogsCountReplyMultiError, or nil if none found.
func (m *GetAgentSecurityLogsCountReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentSecurityLogsCountReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HighRiskCount

	// no validation rules for MediumRiskCount

	// no validation rules for LowRiskCount

	// no validation rules for BlockedCount

	// no validation rules for WarningCount

	if len(errors) > 0 {
		return GetAgentSecurityLogsCountReplyMultiError(errors)
	}

	return nil
}

// GetAgentSecurityLogsCountReplyMultiError is an error wrapping multiple
// validation errors returned by GetAgentSecurityLogsCountReply.ValidateAll()
// if the designated constraints aren't met.
type GetAgentSecurityLogsCountReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentSecurityLogsCountReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentSecurityLogsCountReplyMultiError) AllErrors() []error { return m }

// GetAgentSecurityLogsCountReplyValidationError is the validation error
// returned by GetAgentSecurityLogsCountReply.Validate if the designated
// constraints aren't met.
type GetAgentSecurityLogsCountReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentSecurityLogsCountReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentSecurityLogsCountReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentSecurityLogsCountReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentSecurityLogsCountReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentSecurityLogsCountReplyValidationError) ErrorName() string {
	return "GetAgentSecurityLogsCountReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentSecurityLogsCountReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentSecurityLogsCountReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentSecurityLogsCountReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentSecurityLogsCountReplyValidationError{}

// Validate checks the field values on GetAgentSecurityLogDetailRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAgentSecurityLogDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentSecurityLogDetailRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAgentSecurityLogDetailRequestMultiError, or nil if none found.
func (m *GetAgentSecurityLogDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentSecurityLogDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetAgentSecurityLogDetailRequestMultiError(errors)
	}

	return nil
}

// GetAgentSecurityLogDetailRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAgentSecurityLogDetailRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAgentSecurityLogDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentSecurityLogDetailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentSecurityLogDetailRequestMultiError) AllErrors() []error { return m }

// GetAgentSecurityLogDetailRequestValidationError is the validation error
// returned by GetAgentSecurityLogDetailRequest.Validate if the designated
// constraints aren't met.
type GetAgentSecurityLogDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentSecurityLogDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentSecurityLogDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentSecurityLogDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentSecurityLogDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentSecurityLogDetailRequestValidationError) ErrorName() string {
	return "GetAgentSecurityLogDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentSecurityLogDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentSecurityLogDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentSecurityLogDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentSecurityLogDetailRequestValidationError{}

// Validate checks the field values on GetAgentSecurityLogDetailReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAgentSecurityLogDetailReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAgentSecurityLogDetailReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAgentSecurityLogDetailReplyMultiError, or nil if none found.
func (m *GetAgentSecurityLogDetailReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAgentSecurityLogDetailReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for RiskLevel

	// no validation rules for HitAction

	// no validation rules for UserID

	// no validation rules for UserName

	// no validation rules for DeptName

	// no validation rules for UserAvatar

	// no validation rules for AgentID

	// no validation rules for AgentName

	// no validation rules for AgentAvatar

	// no validation rules for ActionCategory

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAgentSecurityLogDetailReplyValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAgentSecurityLogDetailReplyValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAgentSecurityLogDetailReplyValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PcName

	// no validation rules for Question

	for idx, item := range m.GetSecurityPolicies() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAgentSecurityLogDetailReplyValidationError{
						field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAgentSecurityLogDetailReplyValidationError{
						field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAgentSecurityLogDetailReplyValidationError{
					field:  fmt.Sprintf("SecurityPolicies[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for AgentDescription

	if len(errors) > 0 {
		return GetAgentSecurityLogDetailReplyMultiError(errors)
	}

	return nil
}

// GetAgentSecurityLogDetailReplyMultiError is an error wrapping multiple
// validation errors returned by GetAgentSecurityLogDetailReply.ValidateAll()
// if the designated constraints aren't met.
type GetAgentSecurityLogDetailReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAgentSecurityLogDetailReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAgentSecurityLogDetailReplyMultiError) AllErrors() []error { return m }

// GetAgentSecurityLogDetailReplyValidationError is the validation error
// returned by GetAgentSecurityLogDetailReply.Validate if the designated
// constraints aren't met.
type GetAgentSecurityLogDetailReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAgentSecurityLogDetailReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAgentSecurityLogDetailReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAgentSecurityLogDetailReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAgentSecurityLogDetailReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAgentSecurityLogDetailReplyValidationError) ErrorName() string {
	return "GetAgentSecurityLogDetailReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAgentSecurityLogDetailReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAgentSecurityLogDetailReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAgentSecurityLogDetailReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAgentSecurityLogDetailReplyValidationError{}

// Validate checks the field values on GetUserAgentsAndKnowledgeBasesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetUserAgentsAndKnowledgeBasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserAgentsAndKnowledgeBasesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetUserAgentsAndKnowledgeBasesRequestMultiError, or nil if none found.
func (m *GetUserAgentsAndKnowledgeBasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserAgentsAndKnowledgeBasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserID

	if len(errors) > 0 {
		return GetUserAgentsAndKnowledgeBasesRequestMultiError(errors)
	}

	return nil
}

// GetUserAgentsAndKnowledgeBasesRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetUserAgentsAndKnowledgeBasesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetUserAgentsAndKnowledgeBasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserAgentsAndKnowledgeBasesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserAgentsAndKnowledgeBasesRequestMultiError) AllErrors() []error { return m }

// GetUserAgentsAndKnowledgeBasesRequestValidationError is the validation error
// returned by GetUserAgentsAndKnowledgeBasesRequest.Validate if the
// designated constraints aren't met.
type GetUserAgentsAndKnowledgeBasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserAgentsAndKnowledgeBasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserAgentsAndKnowledgeBasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserAgentsAndKnowledgeBasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserAgentsAndKnowledgeBasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserAgentsAndKnowledgeBasesRequestValidationError) ErrorName() string {
	return "GetUserAgentsAndKnowledgeBasesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserAgentsAndKnowledgeBasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserAgentsAndKnowledgeBasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserAgentsAndKnowledgeBasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserAgentsAndKnowledgeBasesRequestValidationError{}

// Validate checks the field values on GetUserAgentsAndKnowledgeBasesReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetUserAgentsAndKnowledgeBasesReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserAgentsAndKnowledgeBasesReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetUserAgentsAndKnowledgeBasesReplyMultiError, or nil if none found.
func (m *GetUserAgentsAndKnowledgeBasesReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserAgentsAndKnowledgeBasesReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAgents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserAgentsAndKnowledgeBasesReplyValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserAgentsAndKnowledgeBasesReplyValidationError{
						field:  fmt.Sprintf("Agents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserAgentsAndKnowledgeBasesReplyValidationError{
					field:  fmt.Sprintf("Agents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetKnowledgeBases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUserAgentsAndKnowledgeBasesReplyValidationError{
						field:  fmt.Sprintf("KnowledgeBases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUserAgentsAndKnowledgeBasesReplyValidationError{
						field:  fmt.Sprintf("KnowledgeBases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUserAgentsAndKnowledgeBasesReplyValidationError{
					field:  fmt.Sprintf("KnowledgeBases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUserAgentsAndKnowledgeBasesReplyMultiError(errors)
	}

	return nil
}

// GetUserAgentsAndKnowledgeBasesReplyMultiError is an error wrapping multiple
// validation errors returned by
// GetUserAgentsAndKnowledgeBasesReply.ValidateAll() if the designated
// constraints aren't met.
type GetUserAgentsAndKnowledgeBasesReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserAgentsAndKnowledgeBasesReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserAgentsAndKnowledgeBasesReplyMultiError) AllErrors() []error { return m }

// GetUserAgentsAndKnowledgeBasesReplyValidationError is the validation error
// returned by GetUserAgentsAndKnowledgeBasesReply.Validate if the designated
// constraints aren't met.
type GetUserAgentsAndKnowledgeBasesReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserAgentsAndKnowledgeBasesReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserAgentsAndKnowledgeBasesReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserAgentsAndKnowledgeBasesReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserAgentsAndKnowledgeBasesReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserAgentsAndKnowledgeBasesReplyValidationError) ErrorName() string {
	return "GetUserAgentsAndKnowledgeBasesReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserAgentsAndKnowledgeBasesReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserAgentsAndKnowledgeBasesReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserAgentsAndKnowledgeBasesReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserAgentsAndKnowledgeBasesReplyValidationError{}

// Validate checks the field values on GenerateQuestionOptimizationRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GenerateQuestionOptimizationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateQuestionOptimizationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GenerateQuestionOptimizationRequestMultiError, or nil if none found.
func (m *GenerateQuestionOptimizationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateQuestionOptimizationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Question

	if len(errors) > 0 {
		return GenerateQuestionOptimizationRequestMultiError(errors)
	}

	return nil
}

// GenerateQuestionOptimizationRequestMultiError is an error wrapping multiple
// validation errors returned by
// GenerateQuestionOptimizationRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateQuestionOptimizationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateQuestionOptimizationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateQuestionOptimizationRequestMultiError) AllErrors() []error { return m }

// GenerateQuestionOptimizationRequestValidationError is the validation error
// returned by GenerateQuestionOptimizationRequest.Validate if the designated
// constraints aren't met.
type GenerateQuestionOptimizationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateQuestionOptimizationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateQuestionOptimizationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateQuestionOptimizationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateQuestionOptimizationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateQuestionOptimizationRequestValidationError) ErrorName() string {
	return "GenerateQuestionOptimizationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateQuestionOptimizationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateQuestionOptimizationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateQuestionOptimizationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateQuestionOptimizationRequestValidationError{}

// Validate checks the field values on GenerateQuestionOptimizationReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GenerateQuestionOptimizationReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateQuestionOptimizationReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GenerateQuestionOptimizationReplyMultiError, or nil if none found.
func (m *GenerateQuestionOptimizationReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateQuestionOptimizationReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GenerateQuestionOptimizationReplyMultiError(errors)
	}

	return nil
}

// GenerateQuestionOptimizationReplyMultiError is an error wrapping multiple
// validation errors returned by
// GenerateQuestionOptimizationReply.ValidateAll() if the designated
// constraints aren't met.
type GenerateQuestionOptimizationReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateQuestionOptimizationReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateQuestionOptimizationReplyMultiError) AllErrors() []error { return m }

// GenerateQuestionOptimizationReplyValidationError is the validation error
// returned by GenerateQuestionOptimizationReply.Validate if the designated
// constraints aren't met.
type GenerateQuestionOptimizationReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateQuestionOptimizationReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateQuestionOptimizationReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateQuestionOptimizationReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateQuestionOptimizationReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateQuestionOptimizationReplyValidationError) ErrorName() string {
	return "GenerateQuestionOptimizationReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateQuestionOptimizationReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateQuestionOptimizationReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateQuestionOptimizationReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateQuestionOptimizationReplyValidationError{}

// Validate checks the field values on AskAgentRequest_FileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AskAgentRequest_FileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AskAgentRequest_FileInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AskAgentRequest_FileInfoMultiError, or nil if none found.
func (m *AskAgentRequest_FileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AskAgentRequest_FileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for Title

	// no validation rules for Size

	// no validation rules for MimeType

	// no validation rules for UserID

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for Index

	// no validation rules for ChunkIndex

	// no validation rules for FullPath

	// no validation rules for FromChatItemID

	// no validation rules for FromFileRelationID

	if len(errors) > 0 {
		return AskAgentRequest_FileInfoMultiError(errors)
	}

	return nil
}

// AskAgentRequest_FileInfoMultiError is an error wrapping multiple validation
// errors returned by AskAgentRequest_FileInfo.ValidateAll() if the designated
// constraints aren't met.
type AskAgentRequest_FileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AskAgentRequest_FileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AskAgentRequest_FileInfoMultiError) AllErrors() []error { return m }

// AskAgentRequest_FileInfoValidationError is the validation error returned by
// AskAgentRequest_FileInfo.Validate if the designated constraints aren't met.
type AskAgentRequest_FileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AskAgentRequest_FileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AskAgentRequest_FileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AskAgentRequest_FileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AskAgentRequest_FileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AskAgentRequest_FileInfoValidationError) ErrorName() string {
	return "AskAgentRequest_FileInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AskAgentRequest_FileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAskAgentRequest_FileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AskAgentRequest_FileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AskAgentRequest_FileInfoValidationError{}

// Validate checks the field values on AskAgentReply_FileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AskAgentReply_FileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AskAgentReply_FileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AskAgentReply_FileInfoMultiError, or nil if none found.
func (m *AskAgentReply_FileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AskAgentReply_FileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for Title

	// no validation rules for Size

	// no validation rules for MimeType

	// no validation rules for UserID

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for Index

	// no validation rules for ChunkIndex

	// no validation rules for ChunkSize

	// no validation rules for FullPath

	// no validation rules for KnowledgeBaseID

	// no validation rules for KnowledgeBaseName

	// no validation rules for KnowledgeBaseDataType

	// no validation rules for TableData

	// no validation rules for ChartSchema

	// no validation rules for ClassPath

	// no validation rules for Level

	if len(errors) > 0 {
		return AskAgentReply_FileInfoMultiError(errors)
	}

	return nil
}

// AskAgentReply_FileInfoMultiError is an error wrapping multiple validation
// errors returned by AskAgentReply_FileInfo.ValidateAll() if the designated
// constraints aren't met.
type AskAgentReply_FileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AskAgentReply_FileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AskAgentReply_FileInfoMultiError) AllErrors() []error { return m }

// AskAgentReply_FileInfoValidationError is the validation error returned by
// AskAgentReply_FileInfo.Validate if the designated constraints aren't met.
type AskAgentReply_FileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AskAgentReply_FileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AskAgentReply_FileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AskAgentReply_FileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AskAgentReply_FileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AskAgentReply_FileInfoValidationError) ErrorName() string {
	return "AskAgentReply_FileInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AskAgentReply_FileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAskAgentReply_FileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AskAgentReply_FileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AskAgentReply_FileInfoValidationError{}

// Validate checks the field values on
// QueryModelAskClassificationDistributionReply_ClassificationDistribution
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *QueryModelAskClassificationDistributionReply_ClassificationDistribution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryModelAskClassificationDistributionReply_ClassificationDistribution
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// QueryModelAskClassificationDistributionReply_ClassificationDistributionMultiError,
// or nil if none found.
func (m *QueryModelAskClassificationDistributionReply_ClassificationDistribution) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryModelAskClassificationDistributionReply_ClassificationDistribution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClassificationName

	// no validation rules for Count

	if len(errors) > 0 {
		return QueryModelAskClassificationDistributionReply_ClassificationDistributionMultiError(errors)
	}

	return nil
}

// QueryModelAskClassificationDistributionReply_ClassificationDistributionMultiError
// is an error wrapping multiple validation errors returned by
// QueryModelAskClassificationDistributionReply_ClassificationDistribution.ValidateAll()
// if the designated constraints aren't met.
type QueryModelAskClassificationDistributionReply_ClassificationDistributionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryModelAskClassificationDistributionReply_ClassificationDistributionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryModelAskClassificationDistributionReply_ClassificationDistributionMultiError) AllErrors() []error {
	return m
}

// QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError
// is the validation error returned by
// QueryModelAskClassificationDistributionReply_ClassificationDistribution.Validate
// if the designated constraints aren't met.
type QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError) ErrorName() string {
	return "QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError"
}

// Error satisfies the builtin error interface
func (e QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryModelAskClassificationDistributionReply_ClassificationDistribution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryModelAskClassificationDistributionReply_ClassificationDistributionValidationError{}

// Validate checks the field values on
// QueryUploadFileTypeDistributionReply_FileTypeDistribution with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryUploadFileTypeDistributionReply_FileTypeDistribution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryUploadFileTypeDistributionReply_FileTypeDistribution with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryUploadFileTypeDistributionReply_FileTypeDistributionMultiError, or nil
// if none found.
func (m *QueryUploadFileTypeDistributionReply_FileTypeDistribution) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryUploadFileTypeDistributionReply_FileTypeDistribution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileType

	// no validation rules for Count

	if len(errors) > 0 {
		return QueryUploadFileTypeDistributionReply_FileTypeDistributionMultiError(errors)
	}

	return nil
}

// QueryUploadFileTypeDistributionReply_FileTypeDistributionMultiError is an
// error wrapping multiple validation errors returned by
// QueryUploadFileTypeDistributionReply_FileTypeDistribution.ValidateAll() if
// the designated constraints aren't met.
type QueryUploadFileTypeDistributionReply_FileTypeDistributionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryUploadFileTypeDistributionReply_FileTypeDistributionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryUploadFileTypeDistributionReply_FileTypeDistributionMultiError) AllErrors() []error {
	return m
}

// QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError is
// the validation error returned by
// QueryUploadFileTypeDistributionReply_FileTypeDistribution.Validate if the
// designated constraints aren't met.
type QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError) ErrorName() string {
	return "QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError"
}

// Error satisfies the builtin error interface
func (e QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryUploadFileTypeDistributionReply_FileTypeDistribution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryUploadFileTypeDistributionReply_FileTypeDistributionValidationError{}

// Validate checks the field values on
// QueryModelAskClassificationTop10Reply_ClassificationDistribution with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryModelAskClassificationTop10Reply_ClassificationDistribution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryModelAskClassificationTop10Reply_ClassificationDistribution with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryModelAskClassificationTop10Reply_ClassificationDistributionMultiError,
// or nil if none found.
func (m *QueryModelAskClassificationTop10Reply_ClassificationDistribution) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryModelAskClassificationTop10Reply_ClassificationDistribution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClassificationName

	// no validation rules for Count

	if len(errors) > 0 {
		return QueryModelAskClassificationTop10Reply_ClassificationDistributionMultiError(errors)
	}

	return nil
}

// QueryModelAskClassificationTop10Reply_ClassificationDistributionMultiError
// is an error wrapping multiple validation errors returned by
// QueryModelAskClassificationTop10Reply_ClassificationDistribution.ValidateAll()
// if the designated constraints aren't met.
type QueryModelAskClassificationTop10Reply_ClassificationDistributionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryModelAskClassificationTop10Reply_ClassificationDistributionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryModelAskClassificationTop10Reply_ClassificationDistributionMultiError) AllErrors() []error {
	return m
}

// QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError
// is the validation error returned by
// QueryModelAskClassificationTop10Reply_ClassificationDistribution.Validate
// if the designated constraints aren't met.
type QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError) ErrorName() string {
	return "QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError"
}

// Error satisfies the builtin error interface
func (e QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryModelAskClassificationTop10Reply_ClassificationDistribution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryModelAskClassificationTop10Reply_ClassificationDistributionValidationError{}

// Validate checks the field values on
// QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemMultiError,
// or nil if none found.
func (m *QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	for idx, item := range m.GetClassificationDistributions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError{
						field:  fmt.Sprintf("ClassificationDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError{
						field:  fmt.Sprintf("ClassificationDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError{
					field:  fmt.Sprintf("ClassificationDistributions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemMultiError(errors)
	}

	return nil
}

// QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemMultiError
// is an error wrapping multiple validation errors returned by
// QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem.ValidateAll()
// if the designated constraints aren't met.
type QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemMultiError) AllErrors() []error {
	return m
}

// QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError
// is the validation error returned by
// QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem.Validate
// if the designated constraints aren't met.
type QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError) ErrorName() string {
	return "QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItemValidationError{}

// Validate checks the field values on
// QueryUploadFileTypeTop10Reply_FileTypeDistribution with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *QueryUploadFileTypeTop10Reply_FileTypeDistribution) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryUploadFileTypeTop10Reply_FileTypeDistribution with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// QueryUploadFileTypeTop10Reply_FileTypeDistributionMultiError, or nil if
// none found.
func (m *QueryUploadFileTypeTop10Reply_FileTypeDistribution) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryUploadFileTypeTop10Reply_FileTypeDistribution) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileType

	// no validation rules for Count

	if len(errors) > 0 {
		return QueryUploadFileTypeTop10Reply_FileTypeDistributionMultiError(errors)
	}

	return nil
}

// QueryUploadFileTypeTop10Reply_FileTypeDistributionMultiError is an error
// wrapping multiple validation errors returned by
// QueryUploadFileTypeTop10Reply_FileTypeDistribution.ValidateAll() if the
// designated constraints aren't met.
type QueryUploadFileTypeTop10Reply_FileTypeDistributionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryUploadFileTypeTop10Reply_FileTypeDistributionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryUploadFileTypeTop10Reply_FileTypeDistributionMultiError) AllErrors() []error { return m }

// QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError is the
// validation error returned by
// QueryUploadFileTypeTop10Reply_FileTypeDistribution.Validate if the
// designated constraints aren't met.
type QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError) ErrorName() string {
	return "QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError"
}

// Error satisfies the builtin error interface
func (e QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryUploadFileTypeTop10Reply_FileTypeDistribution.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryUploadFileTypeTop10Reply_FileTypeDistributionValidationError{}

// Validate checks the field values on
// QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemMultiError,
// or nil if none found.
func (m *QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	for idx, item := range m.GetFileTypeDistributions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError{
						field:  fmt.Sprintf("FileTypeDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError{
						field:  fmt.Sprintf("FileTypeDistributions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError{
					field:  fmt.Sprintf("FileTypeDistributions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemMultiError(errors)
	}

	return nil
}

// QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemMultiError is
// an error wrapping multiple validation errors returned by
// QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem.ValidateAll()
// if the designated constraints aren't met.
type QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemMultiError) AllErrors() []error {
	return m
}

// QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError
// is the validation error returned by
// QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem.Validate if
// the designated constraints aren't met.
type QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError) ErrorName() string {
	return "QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItemValidationError{}

// Validate checks the field values on GetAllAgentInfoReply_AgentInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllAgentInfoReply_AgentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllAgentInfoReply_AgentInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAllAgentInfoReply_AgentInfoMultiError, or nil if none found.
func (m *GetAllAgentInfoReply_AgentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllAgentInfoReply_AgentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if len(errors) > 0 {
		return GetAllAgentInfoReply_AgentInfoMultiError(errors)
	}

	return nil
}

// GetAllAgentInfoReply_AgentInfoMultiError is an error wrapping multiple
// validation errors returned by GetAllAgentInfoReply_AgentInfo.ValidateAll()
// if the designated constraints aren't met.
type GetAllAgentInfoReply_AgentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllAgentInfoReply_AgentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllAgentInfoReply_AgentInfoMultiError) AllErrors() []error { return m }

// GetAllAgentInfoReply_AgentInfoValidationError is the validation error
// returned by GetAllAgentInfoReply_AgentInfo.Validate if the designated
// constraints aren't met.
type GetAllAgentInfoReply_AgentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllAgentInfoReply_AgentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllAgentInfoReply_AgentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllAgentInfoReply_AgentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllAgentInfoReply_AgentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllAgentInfoReply_AgentInfoValidationError) ErrorName() string {
	return "GetAllAgentInfoReply_AgentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllAgentInfoReply_AgentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllAgentInfoReply_AgentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllAgentInfoReply_AgentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllAgentInfoReply_AgentInfoValidationError{}

// Validate checks the field values on CheckQuestionSecurityRequest_FileInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CheckQuestionSecurityRequest_FileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckQuestionSecurityRequest_FileInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckQuestionSecurityRequest_FileInfoMultiError, or nil if none found.
func (m *CheckQuestionSecurityRequest_FileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckQuestionSecurityRequest_FileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for Title

	// no validation rules for Size

	// no validation rules for MimeType

	// no validation rules for UserID

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for Index

	// no validation rules for ChunkIndex

	// no validation rules for FullPath

	if len(errors) > 0 {
		return CheckQuestionSecurityRequest_FileInfoMultiError(errors)
	}

	return nil
}

// CheckQuestionSecurityRequest_FileInfoMultiError is an error wrapping
// multiple validation errors returned by
// CheckQuestionSecurityRequest_FileInfo.ValidateAll() if the designated
// constraints aren't met.
type CheckQuestionSecurityRequest_FileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckQuestionSecurityRequest_FileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckQuestionSecurityRequest_FileInfoMultiError) AllErrors() []error { return m }

// CheckQuestionSecurityRequest_FileInfoValidationError is the validation error
// returned by CheckQuestionSecurityRequest_FileInfo.Validate if the
// designated constraints aren't met.
type CheckQuestionSecurityRequest_FileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckQuestionSecurityRequest_FileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckQuestionSecurityRequest_FileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckQuestionSecurityRequest_FileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckQuestionSecurityRequest_FileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckQuestionSecurityRequest_FileInfoValidationError) ErrorName() string {
	return "CheckQuestionSecurityRequest_FileInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CheckQuestionSecurityRequest_FileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckQuestionSecurityRequest_FileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckQuestionSecurityRequest_FileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckQuestionSecurityRequest_FileInfoValidationError{}

// Validate checks the field values on
// GetUserAgentsAndKnowledgeBasesReply_AgentInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUserAgentsAndKnowledgeBasesReply_AgentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUserAgentsAndKnowledgeBasesReply_AgentInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetUserAgentsAndKnowledgeBasesReply_AgentInfoMultiError, or nil if none found.
func (m *GetUserAgentsAndKnowledgeBasesReply_AgentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserAgentsAndKnowledgeBasesReply_AgentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentID

	// no validation rules for AgentName

	// no validation rules for AgentAvatar

	if len(errors) > 0 {
		return GetUserAgentsAndKnowledgeBasesReply_AgentInfoMultiError(errors)
	}

	return nil
}

// GetUserAgentsAndKnowledgeBasesReply_AgentInfoMultiError is an error wrapping
// multiple validation errors returned by
// GetUserAgentsAndKnowledgeBasesReply_AgentInfo.ValidateAll() if the
// designated constraints aren't met.
type GetUserAgentsAndKnowledgeBasesReply_AgentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserAgentsAndKnowledgeBasesReply_AgentInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserAgentsAndKnowledgeBasesReply_AgentInfoMultiError) AllErrors() []error { return m }

// GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError is the
// validation error returned by
// GetUserAgentsAndKnowledgeBasesReply_AgentInfo.Validate if the designated
// constraints aren't met.
type GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError) ErrorName() string {
	return "GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserAgentsAndKnowledgeBasesReply_AgentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserAgentsAndKnowledgeBasesReply_AgentInfoValidationError{}

// Validate checks the field values on
// GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoMultiError, or nil if
// none found.
func (m *GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for KnowledgeBaseID

	// no validation rules for KnowledgeBaseName

	// no validation rules for DataType

	if len(errors) > 0 {
		return GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoMultiError(errors)
	}

	return nil
}

// GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoMultiError is an error
// wrapping multiple validation errors returned by
// GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo.ValidateAll() if the
// designated constraints aren't met.
type GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoMultiError) AllErrors() []error {
	return m
}

// GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError is the
// validation error returned by
// GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo.Validate if the
// designated constraints aren't met.
type GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError) ErrorName() string {
	return "GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfoValidationError{}

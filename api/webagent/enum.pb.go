// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: webagent/enum.proto

package webAgent

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VisibilityType int32

const (
	VisibilityType_VisibilityTypeAll  VisibilityType = 0 // 所有人
	VisibilityType_VisibilityTypeUser VisibilityType = 1 // 指定用户
)

// Enum value maps for VisibilityType.
var (
	VisibilityType_name = map[int32]string{
		0: "VisibilityTypeAll",
		1: "VisibilityTypeUser",
	}
	VisibilityType_value = map[string]int32{
		"VisibilityTypeAll":  0,
		"VisibilityTypeUser": 1,
	}
)

func (x VisibilityType) Enum() *VisibilityType {
	p := new(VisibilityType)
	*p = x
	return p
}

func (x VisibilityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VisibilityType) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[0].Descriptor()
}

func (VisibilityType) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[0]
}

func (x VisibilityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VisibilityType.Descriptor instead.
func (VisibilityType) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{0}
}

type ChatType int32

const (
	ChatType_ChatTypeUnknown ChatType = 0
	ChatType_ChatTypeRag     ChatType = 1
	ChatType_ChatTypeAgent   ChatType = 2
)

// Enum value maps for ChatType.
var (
	ChatType_name = map[int32]string{
		0: "ChatTypeUnknown",
		1: "ChatTypeRag",
		2: "ChatTypeAgent",
	}
	ChatType_value = map[string]int32{
		"ChatTypeUnknown": 0,
		"ChatTypeRag":     1,
		"ChatTypeAgent":   2,
	}
)

func (x ChatType) Enum() *ChatType {
	p := new(ChatType)
	*p = x
	return p
}

func (x ChatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatType) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[1].Descriptor()
}

func (ChatType) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[1]
}

func (x ChatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatType.Descriptor instead.
func (ChatType) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{1}
}

type ChatObjectType int32

const (
	ChatObjectType_ChatObjectTypeUnknown ChatObjectType = 0
	ChatObjectType_ChatObjectTypeUser    ChatObjectType = 1
	ChatObjectType_ChatObjectTypeSystem  ChatObjectType = 2
)

// Enum value maps for ChatObjectType.
var (
	ChatObjectType_name = map[int32]string{
		0: "ChatObjectTypeUnknown",
		1: "ChatObjectTypeUser",
		2: "ChatObjectTypeSystem",
	}
	ChatObjectType_value = map[string]int32{
		"ChatObjectTypeUnknown": 0,
		"ChatObjectTypeUser":    1,
		"ChatObjectTypeSystem":  2,
	}
)

func (x ChatObjectType) Enum() *ChatObjectType {
	p := new(ChatObjectType)
	*p = x
	return p
}

func (x ChatObjectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatObjectType) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[2].Descriptor()
}

func (ChatObjectType) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[2]
}

func (x ChatObjectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatObjectType.Descriptor instead.
func (ChatObjectType) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{2}
}

type ModelType int32

const (
	ModelType_ModelTypeUnknown  ModelType = 0
	ModelType_ModelTypeInternal ModelType = 1
	ModelType_ModelTypeGateway  ModelType = 2
)

// Enum value maps for ModelType.
var (
	ModelType_name = map[int32]string{
		0: "ModelTypeUnknown",
		1: "ModelTypeInternal",
		2: "ModelTypeGateway",
	}
	ModelType_value = map[string]int32{
		"ModelTypeUnknown":  0,
		"ModelTypeInternal": 1,
		"ModelTypeGateway":  2,
	}
)

func (x ModelType) Enum() *ModelType {
	p := new(ModelType)
	*p = x
	return p
}

func (x ModelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelType) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[3].Descriptor()
}

func (ModelType) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[3]
}

func (x ModelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelType.Descriptor instead.
func (ModelType) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{3}
}

type SearchTopType int32

const (
	SearchTopType_SearchTopTypeUnknown SearchTopType = 0
	SearchTopType_SearchTopTypeUser    SearchTopType = 1 // 用户
	SearchTopType_SearchTopTypeDept    SearchTopType = 2 // 部门
)

// Enum value maps for SearchTopType.
var (
	SearchTopType_name = map[int32]string{
		0: "SearchTopTypeUnknown",
		1: "SearchTopTypeUser",
		2: "SearchTopTypeDept",
	}
	SearchTopType_value = map[string]int32{
		"SearchTopTypeUnknown": 0,
		"SearchTopTypeUser":    1,
		"SearchTopTypeDept":    2,
	}
)

func (x SearchTopType) Enum() *SearchTopType {
	p := new(SearchTopType)
	*p = x
	return p
}

func (x SearchTopType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchTopType) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[4].Descriptor()
}

func (SearchTopType) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[4]
}

func (x SearchTopType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchTopType.Descriptor instead.
func (SearchTopType) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{4}
}

type AgentCategory int32

const (
	AgentCategory_AgentCategoryUnknown              AgentCategory = 0
	AgentCategory_AgentCategoryAiTools              AgentCategory = 1 // AI工具
	AgentCategory_AgentCategoryRag                  AgentCategory = 2 // 问答智能体
	AgentCategory_AgentCategoryDeepRag              AgentCategory = 3 // 深度检索问答智能体
	AgentCategory_AgentCategoryBaseAndDeepSearchRag AgentCategory = 4 // 基础和深度搜索问答
	AgentCategory_AgentCategoryCommonlyUsed         AgentCategory = 5 // 常用智能体
)

// Enum value maps for AgentCategory.
var (
	AgentCategory_name = map[int32]string{
		0: "AgentCategoryUnknown",
		1: "AgentCategoryAiTools",
		2: "AgentCategoryRag",
		3: "AgentCategoryDeepRag",
		4: "AgentCategoryBaseAndDeepSearchRag",
		5: "AgentCategoryCommonlyUsed",
	}
	AgentCategory_value = map[string]int32{
		"AgentCategoryUnknown":              0,
		"AgentCategoryAiTools":              1,
		"AgentCategoryRag":                  2,
		"AgentCategoryDeepRag":              3,
		"AgentCategoryBaseAndDeepSearchRag": 4,
		"AgentCategoryCommonlyUsed":         5,
	}
)

func (x AgentCategory) Enum() *AgentCategory {
	p := new(AgentCategory)
	*p = x
	return p
}

func (x AgentCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[5].Descriptor()
}

func (AgentCategory) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[5]
}

func (x AgentCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentCategory.Descriptor instead.
func (AgentCategory) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{5}
}

type AgentType int32

const (
	AgentType_AgentTypeUnknown        AgentType = 0
	AgentType_AgentTypeBaseRag        AgentType = 1 // 基础问答
	AgentType_AgentTypeDeepSearchRag  AgentType = 2 // 深度搜索问答
	AgentType_AgentTypeContractReview AgentType = 3 //合同审查
)

// Enum value maps for AgentType.
var (
	AgentType_name = map[int32]string{
		0: "AgentTypeUnknown",
		1: "AgentTypeBaseRag",
		2: "AgentTypeDeepSearchRag",
		3: "AgentTypeContractReview",
	}
	AgentType_value = map[string]int32{
		"AgentTypeUnknown":        0,
		"AgentTypeBaseRag":        1,
		"AgentTypeDeepSearchRag":  2,
		"AgentTypeContractReview": 3,
	}
)

func (x AgentType) Enum() *AgentType {
	p := new(AgentType)
	*p = x
	return p
}

func (x AgentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AgentType) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[6].Descriptor()
}

func (AgentType) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[6]
}

func (x AgentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AgentType.Descriptor instead.
func (AgentType) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{6}
}

type KnowledgeBaseType int32

const (
	KnowledgeBaseType_KnowledgeBaseTypeDirect KnowledgeBaseType = 0 // 指定知识库
	KnowledgeBaseType_KnowledgeBaseTypeMine   KnowledgeBaseType = 1 // 我有权限的所有文件
)

// Enum value maps for KnowledgeBaseType.
var (
	KnowledgeBaseType_name = map[int32]string{
		0: "KnowledgeBaseTypeDirect",
		1: "KnowledgeBaseTypeMine",
	}
	KnowledgeBaseType_value = map[string]int32{
		"KnowledgeBaseTypeDirect": 0,
		"KnowledgeBaseTypeMine":   1,
	}
)

func (x KnowledgeBaseType) Enum() *KnowledgeBaseType {
	p := new(KnowledgeBaseType)
	*p = x
	return p
}

func (x KnowledgeBaseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeBaseType) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[7].Descriptor()
}

func (KnowledgeBaseType) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[7]
}

func (x KnowledgeBaseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeBaseType.Descriptor instead.
func (KnowledgeBaseType) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{7}
}

type CallAgentStatus int32

const (
	CallAgentStatus_CallAgentStatusUnknown CallAgentStatus = 0
	CallAgentStatus_CallAgentStatusSuccess CallAgentStatus = 1 // 成功
	CallAgentStatus_CallAgentStatusFail    CallAgentStatus = 2 // 失败
)

// Enum value maps for CallAgentStatus.
var (
	CallAgentStatus_name = map[int32]string{
		0: "CallAgentStatusUnknown",
		1: "CallAgentStatusSuccess",
		2: "CallAgentStatusFail",
	}
	CallAgentStatus_value = map[string]int32{
		"CallAgentStatusUnknown": 0,
		"CallAgentStatusSuccess": 1,
		"CallAgentStatusFail":    2,
	}
)

func (x CallAgentStatus) Enum() *CallAgentStatus {
	p := new(CallAgentStatus)
	*p = x
	return p
}

func (x CallAgentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CallAgentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[8].Descriptor()
}

func (CallAgentStatus) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[8]
}

func (x CallAgentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CallAgentStatus.Descriptor instead.
func (CallAgentStatus) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{8}
}

type PolicyCategory int32

const (
	PolicyCategory_PolicyCategoryUnknown                      PolicyCategory = 0 // 未知
	PolicyCategory_PolicyCategorySensitiveInformationMatching PolicyCategory = 1 // 敏感信息匹配
	PolicyCategory_PolicyCategoryCoreDataIdentification       PolicyCategory = 2 // 核心数据识别
	PolicyCategory_PolicyCategoryIntentionIdentification      PolicyCategory = 3 // 意图识别
)

// Enum value maps for PolicyCategory.
var (
	PolicyCategory_name = map[int32]string{
		0: "PolicyCategoryUnknown",
		1: "PolicyCategorySensitiveInformationMatching",
		2: "PolicyCategoryCoreDataIdentification",
		3: "PolicyCategoryIntentionIdentification",
	}
	PolicyCategory_value = map[string]int32{
		"PolicyCategoryUnknown":                      0,
		"PolicyCategorySensitiveInformationMatching": 1,
		"PolicyCategoryCoreDataIdentification":       2,
		"PolicyCategoryIntentionIdentification":      3,
	}
)

func (x PolicyCategory) Enum() *PolicyCategory {
	p := new(PolicyCategory)
	*p = x
	return p
}

func (x PolicyCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PolicyCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[9].Descriptor()
}

func (PolicyCategory) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[9]
}

func (x PolicyCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PolicyCategory.Descriptor instead.
func (PolicyCategory) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{9}
}

type RiskLevel int32

const (
	RiskLevel_RiskLevelUnknown RiskLevel = 0 // 未知
	RiskLevel_RiskLevelLow     RiskLevel = 1 // 低危
	RiskLevel_RiskLevelMedium  RiskLevel = 2 // 中危
	RiskLevel_RiskLevelHigh    RiskLevel = 3 // 高危
)

// Enum value maps for RiskLevel.
var (
	RiskLevel_name = map[int32]string{
		0: "RiskLevelUnknown",
		1: "RiskLevelLow",
		2: "RiskLevelMedium",
		3: "RiskLevelHigh",
	}
	RiskLevel_value = map[string]int32{
		"RiskLevelUnknown": 0,
		"RiskLevelLow":     1,
		"RiskLevelMedium":  2,
		"RiskLevelHigh":    3,
	}
)

func (x RiskLevel) Enum() *RiskLevel {
	p := new(RiskLevel)
	*p = x
	return p
}

func (x RiskLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[10].Descriptor()
}

func (RiskLevel) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[10]
}

func (x RiskLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskLevel.Descriptor instead.
func (RiskLevel) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{10}
}

type HitAction int32

const (
	HitAction_HitActionUnknown HitAction = 0 // 未知
	HitAction_HitActionBlock   HitAction = 1 // 阻断
	HitAction_HitActionAlert   HitAction = 2 // 告警
)

// Enum value maps for HitAction.
var (
	HitAction_name = map[int32]string{
		0: "HitActionUnknown",
		1: "HitActionBlock",
		2: "HitActionAlert",
	}
	HitAction_value = map[string]int32{
		"HitActionUnknown": 0,
		"HitActionBlock":   1,
		"HitActionAlert":   2,
	}
)

func (x HitAction) Enum() *HitAction {
	p := new(HitAction)
	*p = x
	return p
}

func (x HitAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HitAction) Descriptor() protoreflect.EnumDescriptor {
	return file_webagent_enum_proto_enumTypes[11].Descriptor()
}

func (HitAction) Type() protoreflect.EnumType {
	return &file_webagent_enum_proto_enumTypes[11]
}

func (x HitAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HitAction.Descriptor instead.
func (HitAction) EnumDescriptor() ([]byte, []int) {
	return file_webagent_enum_proto_rawDescGZIP(), []int{11}
}

var File_webagent_enum_proto protoreflect.FileDescriptor

var file_webagent_enum_proto_rawDesc = []byte{
	0x0a, 0x13, 0x77, 0x65, 0x62, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x2a, 0x3f, 0x0a, 0x0e, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x41, 0x6c, 0x6c, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12,
	0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x10, 0x01, 0x2a, 0x43, 0x0a, 0x08, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x13, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x61, 0x67, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x10, 0x02, 0x2a, 0x5d, 0x0a, 0x0e, 0x43, 0x68, 0x61,
	0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x43,
	0x68, 0x61, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x74, 0x4f, 0x62,
	0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x73, 0x65, 0x72, 0x10, 0x01, 0x12, 0x18,
	0x0a, 0x14, 0x43, 0x68, 0x61, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x02, 0x2a, 0x4e, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x10, 0x02, 0x2a, 0x57, 0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x54, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x6f, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x55, 0x73, 0x65, 0x72, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x54, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x44, 0x65, 0x70, 0x74, 0x10,
	0x02, 0x2a, 0xb9, 0x01, 0x0a, 0x0d, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x14, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x18, 0x0a,
	0x14, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x41, 0x69,
	0x54, 0x6f, 0x6f, 0x6c, 0x73, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x61, 0x67, 0x10, 0x02, 0x12, 0x18, 0x0a,
	0x14, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x44, 0x65,
	0x65, 0x70, 0x52, 0x61, 0x67, 0x10, 0x03, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x61, 0x73, 0x65, 0x41, 0x6e, 0x64, 0x44,
	0x65, 0x65, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x61, 0x67, 0x10, 0x04, 0x12, 0x1d,
	0x0a, 0x19, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x6c, 0x79, 0x55, 0x73, 0x65, 0x64, 0x10, 0x05, 0x2a, 0x70, 0x0a,
	0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x52, 0x61, 0x67, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x44, 0x65, 0x65, 0x70, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x61, 0x67,
	0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x61, 0x63, 0x74, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x10, 0x03, 0x2a,
	0x4b, 0x0a, 0x11, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x10,
	0x00, 0x12, 0x19, 0x0a, 0x15, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x69, 0x6e, 0x65, 0x10, 0x01, 0x2a, 0x62, 0x0a, 0x0f,
	0x43, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1a, 0x0a, 0x16, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x43,
	0x61, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x61, 0x6c, 0x6c, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x61, 0x69, 0x6c, 0x10, 0x02,
	0x2a, 0xb0, 0x01, 0x0a, 0x0e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x2e,
	0x0a, 0x2a, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x53, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x28,
	0x0a, 0x24, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x43, 0x6f, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x12, 0x29, 0x0a, 0x25, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x10, 0x03, 0x2a, 0x5b, 0x0a, 0x09, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x4c, 0x6f, 0x77, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x69, 0x73, 0x6b,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x4d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x10, 0x02, 0x12, 0x11, 0x0a,
	0x0d, 0x52, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x48, 0x69, 0x67, 0x68, 0x10, 0x03,
	0x2a, 0x49, 0x0a, 0x09, 0x48, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x14, 0x0a,
	0x10, 0x48, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x48, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x48, 0x69, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x10, 0x02, 0x42, 0x50, 0x0a, 0x0c, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x01, 0x5a, 0x3e, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x2f, 0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65, 0x61, 0x6d,
	0x2f, 0x61, 0x69, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x3b, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_webagent_enum_proto_rawDescOnce sync.Once
	file_webagent_enum_proto_rawDescData = file_webagent_enum_proto_rawDesc
)

func file_webagent_enum_proto_rawDescGZIP() []byte {
	file_webagent_enum_proto_rawDescOnce.Do(func() {
		file_webagent_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_webagent_enum_proto_rawDescData)
	})
	return file_webagent_enum_proto_rawDescData
}

var file_webagent_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 12)
var file_webagent_enum_proto_goTypes = []any{
	(VisibilityType)(0),    // 0: api.webAgent.VisibilityType
	(ChatType)(0),          // 1: api.webAgent.ChatType
	(ChatObjectType)(0),    // 2: api.webAgent.ChatObjectType
	(ModelType)(0),         // 3: api.webAgent.ModelType
	(SearchTopType)(0),     // 4: api.webAgent.SearchTopType
	(AgentCategory)(0),     // 5: api.webAgent.AgentCategory
	(AgentType)(0),         // 6: api.webAgent.AgentType
	(KnowledgeBaseType)(0), // 7: api.webAgent.KnowledgeBaseType
	(CallAgentStatus)(0),   // 8: api.webAgent.CallAgentStatus
	(PolicyCategory)(0),    // 9: api.webAgent.PolicyCategory
	(RiskLevel)(0),         // 10: api.webAgent.RiskLevel
	(HitAction)(0),         // 11: api.webAgent.HitAction
}
var file_webagent_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_webagent_enum_proto_init() }
func file_webagent_enum_proto_init() {
	if File_webagent_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_webagent_enum_proto_rawDesc,
			NumEnums:      12,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_webagent_enum_proto_goTypes,
		DependencyIndexes: file_webagent_enum_proto_depIdxs,
		EnumInfos:         file_webagent_enum_proto_enumTypes,
	}.Build()
	File_webagent_enum_proto = out.File
	file_webagent_enum_proto_rawDesc = nil
	file_webagent_enum_proto_goTypes = nil
	file_webagent_enum_proto_depIdxs = nil
}

// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: webagent/agent.proto

package webAgent

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAgentCheckQuestionSecurity = "/api.webAgent.Agent/CheckQuestionSecurity"
const OperationAgentCreateAgent = "/api.webAgent.Agent/CreateAgent"
const OperationAgentDeleteAgent = "/api.webAgent.Agent/DeleteAgent"
const OperationAgentGenerateQuestionOptimization = "/api.webAgent.Agent/GenerateQuestionOptimization"
const OperationAgentGetAgent = "/api.webAgent.Agent/GetAgent"
const OperationAgentGetAgentQueueWhiteList = "/api.webAgent.Agent/GetAgentQueueWhiteList"
const OperationAgentGetAgentSecurityLogDetail = "/api.webAgent.Agent/GetAgentSecurityLogDetail"
const OperationAgentGetAgentSecurityLogsCount = "/api.webAgent.Agent/GetAgentSecurityLogsCount"
const OperationAgentGetAllAgentInfo = "/api.webAgent.Agent/GetAllAgentInfo"
const OperationAgentGetDefaultAvatars = "/api.webAgent.Agent/GetDefaultAvatars"
const OperationAgentGetFilePermissionByAgentID = "/api.webAgent.Agent/GetFilePermissionByAgentID"
const OperationAgentGetUserAgentsAndKnowledgeBases = "/api.webAgent.Agent/GetUserAgentsAndKnowledgeBases"
const OperationAgentInternalModelChatPage = "/api.webAgent.Agent/InternalModelChatPage"
const OperationAgentPageAgent = "/api.webAgent.Agent/PageAgent"
const OperationAgentPageAgentSecurityLogs = "/api.webAgent.Agent/PageAgentSecurityLogs"
const OperationAgentQueryModelAskClassificationDistribution = "/api.webAgent.Agent/QueryModelAskClassificationDistribution"
const OperationAgentQueryModelAskClassificationTop10 = "/api.webAgent.Agent/QueryModelAskClassificationTop10"
const OperationAgentQueryUploadFileTypeDistribution = "/api.webAgent.Agent/QueryUploadFileTypeDistribution"
const OperationAgentQueryUploadFileTypeTop10 = "/api.webAgent.Agent/QueryUploadFileTypeTop10"
const OperationAgentSaveAgentQueueWhiteList = "/api.webAgent.Agent/SaveAgentQueueWhiteList"
const OperationAgentTransferAgent = "/api.webAgent.Agent/TransferAgent"
const OperationAgentUpdateAgent = "/api.webAgent.Agent/UpdateAgent"
const OperationAgentUpdateAgentSort = "/api.webAgent.Agent/UpdateAgentSort"

type AgentHTTPServer interface {
	// CheckQuestionSecurity 校验问题安全
	CheckQuestionSecurity(context.Context, *CheckQuestionSecurityRequest) (*CheckQuestionSecurityReply, error)
	// CreateAgent 创建 Agent
	CreateAgent(context.Context, *CreateAgentRequest) (*CreateAgentReply, error)
	// DeleteAgent 删除 Agent
	DeleteAgent(context.Context, *DeleteAgentRequest) (*DeleteAgentReply, error)
	// GenerateQuestionOptimization 根据问题推荐问题
	GenerateQuestionOptimization(context.Context, *GenerateQuestionOptimizationRequest) (*GenerateQuestionOptimizationReply, error)
	// GetAgent 查询 Agent
	GetAgent(context.Context, *GetAgentRequest) (*GetAgentReply, error)
	// GetAgentQueueWhiteList 获取智能体排队白名单人员id列表
	GetAgentQueueWhiteList(context.Context, *GetAgentQueueWhiteListRequest) (*GetAgentQueueWhiteListReply, error)
	// GetAgentSecurityLogDetail 获取智能体安全策略数据管控详情
	GetAgentSecurityLogDetail(context.Context, *GetAgentSecurityLogDetailRequest) (*GetAgentSecurityLogDetailReply, error)
	// GetAgentSecurityLogsCount 获取智能体安全策略数据管控数量
	GetAgentSecurityLogsCount(context.Context, *GetAgentSecurityLogsCountRequest) (*GetAgentSecurityLogsCountReply, error)
	// GetAllAgentInfo 查询所有Agent id/anme
	GetAllAgentInfo(context.Context, *GetAllAgentInfoRequest) (*GetAllAgentInfoReply, error)
	// GetDefaultAvatars 获取智能体默认头像
	GetDefaultAvatars(context.Context, *GetDefaultAvatarsRequest) (*GetDefaultAvatarsReply, error)
	// GetFilePermissionByAgentID 判断用户是否有文件权限（关联智能体、知识库）
	GetFilePermissionByAgentID(context.Context, *GetFilePermissionByAgentIDRequest) (*GetFilePermissionByAgentIDReply, error)
	// GetUserAgentsAndKnowledgeBases 获取用户智能体列表、知识库列表
	GetUserAgentsAndKnowledgeBases(context.Context, *GetUserAgentsAndKnowledgeBasesRequest) (*GetUserAgentsAndKnowledgeBasesReply, error)
	// InternalModelChatPage 内部模型聊天记录分页
	InternalModelChatPage(context.Context, *InternalModelChatPageRequest) (*InternalModelChatPageReply, error)
	// PageAgent 分页 Agent
	PageAgent(context.Context, *PageAgentRequest) (*PageAgentReply, error)
	// PageAgentSecurityLogs 分页智能体安全策略数据管控
	PageAgentSecurityLogs(context.Context, *PageAgentSecurityLogsRequest) (*PageAgentSecurityLogsReply, error)
	// QueryModelAskClassificationDistribution 查询模型提问词分类分布
	QueryModelAskClassificationDistribution(context.Context, *QueryModelAskClassificationDistributionRequest) (*QueryModelAskClassificationDistributionReply, error)
	// QueryModelAskClassificationTop10 查询模型提问词分类top10
	QueryModelAskClassificationTop10(context.Context, *QueryModelAskClassificationTop10Request) (*QueryModelAskClassificationTop10Reply, error)
	// QueryUploadFileTypeDistribution 查询上传附件文件类型分布
	QueryUploadFileTypeDistribution(context.Context, *QueryUploadFileTypeDistributionRequest) (*QueryUploadFileTypeDistributionReply, error)
	// QueryUploadFileTypeTop10 查询上传附件文件类型top10
	QueryUploadFileTypeTop10(context.Context, *QueryUploadFileTypeTop10Request) (*QueryUploadFileTypeTop10Reply, error)
	// SaveAgentQueueWhiteList 保存智能体排队白名单人员id列表
	SaveAgentQueueWhiteList(context.Context, *SaveAgentQueueWhiteListRequest) (*SaveAgentQueueWhiteListReply, error)
	// TransferAgent 转让智能体
	TransferAgent(context.Context, *TransferAgentRequest) (*TransferAgentReply, error)
	// UpdateAgent 更新 Agent
	UpdateAgent(context.Context, *UpdateAgentRequest) (*UpdateAgentReply, error)
	// UpdateAgentSort 更新智能体排序
	UpdateAgentSort(context.Context, *UpdateAgentSortRequest) (*UpdateAgentSortReply, error)
}

func RegisterAgentHTTPServer(s *http.Server, srv AgentHTTPServer) {
	r := s.Route("/")
	r.POST("/agent/create", _Agent_CreateAgent0_HTTP_Handler(srv))
	r.GET("/agent/all", _Agent_GetAllAgentInfo0_HTTP_Handler(srv))
	r.POST("/agent/update", _Agent_UpdateAgent0_HTTP_Handler(srv))
	r.POST("/agent/delete", _Agent_DeleteAgent0_HTTP_Handler(srv))
	r.GET("/agent/get", _Agent_GetAgent0_HTTP_Handler(srv))
	r.GET("/agent/page", _Agent_PageAgent0_HTTP_Handler(srv))
	r.GET("/agent/defaultAvatars", _Agent_GetDefaultAvatars0_HTTP_Handler(srv))
	r.GET("/agent/filePermission", _Agent_GetFilePermissionByAgentID0_HTTP_Handler(srv))
	r.POST("/agent/internalModelChatPage", _Agent_InternalModelChatPage0_HTTP_Handler(srv))
	r.GET("/agent/modelAskClassificationDistribution", _Agent_QueryModelAskClassificationDistribution0_HTTP_Handler(srv))
	r.GET("/agent/uploadFileTypeDistribution", _Agent_QueryUploadFileTypeDistribution0_HTTP_Handler(srv))
	r.GET("/agent/modelAskClassificationTop10", _Agent_QueryModelAskClassificationTop100_HTTP_Handler(srv))
	r.GET("/agent/uploadFileTypeTop10", _Agent_QueryUploadFileTypeTop100_HTTP_Handler(srv))
	r.POST("/agent/queue/whiteList", _Agent_SaveAgentQueueWhiteList0_HTTP_Handler(srv))
	r.GET("/agent/queue/whiteList", _Agent_GetAgentQueueWhiteList0_HTTP_Handler(srv))
	r.POST("/agent/update/sort", _Agent_UpdateAgentSort0_HTTP_Handler(srv))
	r.POST("/agent/transfer", _Agent_TransferAgent0_HTTP_Handler(srv))
	r.POST("/agent/checkQuestionSecurity", _Agent_CheckQuestionSecurity0_HTTP_Handler(srv))
	r.GET("/agent/security/logs", _Agent_PageAgentSecurityLogs0_HTTP_Handler(srv))
	r.GET("/agent/security/logs/detail", _Agent_GetAgentSecurityLogDetail0_HTTP_Handler(srv))
	r.GET("/agent/security/logs/count", _Agent_GetAgentSecurityLogsCount0_HTTP_Handler(srv))
	r.GET("/agent/user/agentsAndKnowledgeBases", _Agent_GetUserAgentsAndKnowledgeBases0_HTTP_Handler(srv))
	r.POST("/agent/generateQuestionOptimization", _Agent_GenerateQuestionOptimization0_HTTP_Handler(srv))
}

func _Agent_CreateAgent0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAgentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentCreateAgent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAgent(ctx, req.(*CreateAgentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateAgentReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_GetAllAgentInfo0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAllAgentInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentGetAllAgentInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAllAgentInfo(ctx, req.(*GetAllAgentInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAllAgentInfoReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_UpdateAgent0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAgentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentUpdateAgent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAgent(ctx, req.(*UpdateAgentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateAgentReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_DeleteAgent0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAgentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentDeleteAgent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAgent(ctx, req.(*DeleteAgentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteAgentReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_GetAgent0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAgentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentGetAgent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAgent(ctx, req.(*GetAgentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAgentReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_PageAgent0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PageAgentRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentPageAgent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PageAgent(ctx, req.(*PageAgentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PageAgentReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_GetDefaultAvatars0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDefaultAvatarsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentGetDefaultAvatars)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDefaultAvatars(ctx, req.(*GetDefaultAvatarsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDefaultAvatarsReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_GetFilePermissionByAgentID0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetFilePermissionByAgentIDRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentGetFilePermissionByAgentID)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFilePermissionByAgentID(ctx, req.(*GetFilePermissionByAgentIDRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetFilePermissionByAgentIDReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_InternalModelChatPage0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in InternalModelChatPageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentInternalModelChatPage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.InternalModelChatPage(ctx, req.(*InternalModelChatPageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*InternalModelChatPageReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_QueryModelAskClassificationDistribution0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryModelAskClassificationDistributionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentQueryModelAskClassificationDistribution)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryModelAskClassificationDistribution(ctx, req.(*QueryModelAskClassificationDistributionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryModelAskClassificationDistributionReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_QueryUploadFileTypeDistribution0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryUploadFileTypeDistributionRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentQueryUploadFileTypeDistribution)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryUploadFileTypeDistribution(ctx, req.(*QueryUploadFileTypeDistributionRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryUploadFileTypeDistributionReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_QueryModelAskClassificationTop100_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryModelAskClassificationTop10Request
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentQueryModelAskClassificationTop10)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryModelAskClassificationTop10(ctx, req.(*QueryModelAskClassificationTop10Request))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryModelAskClassificationTop10Reply)
		return ctx.Result(200, reply)
	}
}

func _Agent_QueryUploadFileTypeTop100_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in QueryUploadFileTypeTop10Request
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentQueryUploadFileTypeTop10)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.QueryUploadFileTypeTop10(ctx, req.(*QueryUploadFileTypeTop10Request))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*QueryUploadFileTypeTop10Reply)
		return ctx.Result(200, reply)
	}
}

func _Agent_SaveAgentQueueWhiteList0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SaveAgentQueueWhiteListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentSaveAgentQueueWhiteList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SaveAgentQueueWhiteList(ctx, req.(*SaveAgentQueueWhiteListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SaveAgentQueueWhiteListReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_GetAgentQueueWhiteList0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAgentQueueWhiteListRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentGetAgentQueueWhiteList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAgentQueueWhiteList(ctx, req.(*GetAgentQueueWhiteListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAgentQueueWhiteListReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_UpdateAgentSort0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAgentSortRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentUpdateAgentSort)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAgentSort(ctx, req.(*UpdateAgentSortRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateAgentSortReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_TransferAgent0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TransferAgentRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentTransferAgent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TransferAgent(ctx, req.(*TransferAgentRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TransferAgentReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_CheckQuestionSecurity0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CheckQuestionSecurityRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentCheckQuestionSecurity)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CheckQuestionSecurity(ctx, req.(*CheckQuestionSecurityRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CheckQuestionSecurityReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_PageAgentSecurityLogs0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PageAgentSecurityLogsRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentPageAgentSecurityLogs)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PageAgentSecurityLogs(ctx, req.(*PageAgentSecurityLogsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PageAgentSecurityLogsReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_GetAgentSecurityLogDetail0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAgentSecurityLogDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentGetAgentSecurityLogDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAgentSecurityLogDetail(ctx, req.(*GetAgentSecurityLogDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAgentSecurityLogDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_GetAgentSecurityLogsCount0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAgentSecurityLogsCountRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentGetAgentSecurityLogsCount)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAgentSecurityLogsCount(ctx, req.(*GetAgentSecurityLogsCountRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAgentSecurityLogsCountReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_GetUserAgentsAndKnowledgeBases0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetUserAgentsAndKnowledgeBasesRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentGetUserAgentsAndKnowledgeBases)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetUserAgentsAndKnowledgeBases(ctx, req.(*GetUserAgentsAndKnowledgeBasesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetUserAgentsAndKnowledgeBasesReply)
		return ctx.Result(200, reply)
	}
}

func _Agent_GenerateQuestionOptimization0_HTTP_Handler(srv AgentHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GenerateQuestionOptimizationRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAgentGenerateQuestionOptimization)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GenerateQuestionOptimization(ctx, req.(*GenerateQuestionOptimizationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GenerateQuestionOptimizationReply)
		return ctx.Result(200, reply)
	}
}

type AgentHTTPClient interface {
	CheckQuestionSecurity(ctx context.Context, req *CheckQuestionSecurityRequest, opts ...http.CallOption) (rsp *CheckQuestionSecurityReply, err error)
	CreateAgent(ctx context.Context, req *CreateAgentRequest, opts ...http.CallOption) (rsp *CreateAgentReply, err error)
	DeleteAgent(ctx context.Context, req *DeleteAgentRequest, opts ...http.CallOption) (rsp *DeleteAgentReply, err error)
	GenerateQuestionOptimization(ctx context.Context, req *GenerateQuestionOptimizationRequest, opts ...http.CallOption) (rsp *GenerateQuestionOptimizationReply, err error)
	GetAgent(ctx context.Context, req *GetAgentRequest, opts ...http.CallOption) (rsp *GetAgentReply, err error)
	GetAgentQueueWhiteList(ctx context.Context, req *GetAgentQueueWhiteListRequest, opts ...http.CallOption) (rsp *GetAgentQueueWhiteListReply, err error)
	GetAgentSecurityLogDetail(ctx context.Context, req *GetAgentSecurityLogDetailRequest, opts ...http.CallOption) (rsp *GetAgentSecurityLogDetailReply, err error)
	GetAgentSecurityLogsCount(ctx context.Context, req *GetAgentSecurityLogsCountRequest, opts ...http.CallOption) (rsp *GetAgentSecurityLogsCountReply, err error)
	GetAllAgentInfo(ctx context.Context, req *GetAllAgentInfoRequest, opts ...http.CallOption) (rsp *GetAllAgentInfoReply, err error)
	GetDefaultAvatars(ctx context.Context, req *GetDefaultAvatarsRequest, opts ...http.CallOption) (rsp *GetDefaultAvatarsReply, err error)
	GetFilePermissionByAgentID(ctx context.Context, req *GetFilePermissionByAgentIDRequest, opts ...http.CallOption) (rsp *GetFilePermissionByAgentIDReply, err error)
	GetUserAgentsAndKnowledgeBases(ctx context.Context, req *GetUserAgentsAndKnowledgeBasesRequest, opts ...http.CallOption) (rsp *GetUserAgentsAndKnowledgeBasesReply, err error)
	InternalModelChatPage(ctx context.Context, req *InternalModelChatPageRequest, opts ...http.CallOption) (rsp *InternalModelChatPageReply, err error)
	PageAgent(ctx context.Context, req *PageAgentRequest, opts ...http.CallOption) (rsp *PageAgentReply, err error)
	PageAgentSecurityLogs(ctx context.Context, req *PageAgentSecurityLogsRequest, opts ...http.CallOption) (rsp *PageAgentSecurityLogsReply, err error)
	QueryModelAskClassificationDistribution(ctx context.Context, req *QueryModelAskClassificationDistributionRequest, opts ...http.CallOption) (rsp *QueryModelAskClassificationDistributionReply, err error)
	QueryModelAskClassificationTop10(ctx context.Context, req *QueryModelAskClassificationTop10Request, opts ...http.CallOption) (rsp *QueryModelAskClassificationTop10Reply, err error)
	QueryUploadFileTypeDistribution(ctx context.Context, req *QueryUploadFileTypeDistributionRequest, opts ...http.CallOption) (rsp *QueryUploadFileTypeDistributionReply, err error)
	QueryUploadFileTypeTop10(ctx context.Context, req *QueryUploadFileTypeTop10Request, opts ...http.CallOption) (rsp *QueryUploadFileTypeTop10Reply, err error)
	SaveAgentQueueWhiteList(ctx context.Context, req *SaveAgentQueueWhiteListRequest, opts ...http.CallOption) (rsp *SaveAgentQueueWhiteListReply, err error)
	TransferAgent(ctx context.Context, req *TransferAgentRequest, opts ...http.CallOption) (rsp *TransferAgentReply, err error)
	UpdateAgent(ctx context.Context, req *UpdateAgentRequest, opts ...http.CallOption) (rsp *UpdateAgentReply, err error)
	UpdateAgentSort(ctx context.Context, req *UpdateAgentSortRequest, opts ...http.CallOption) (rsp *UpdateAgentSortReply, err error)
}

type AgentHTTPClientImpl struct {
	cc *http.Client
}

func NewAgentHTTPClient(client *http.Client) AgentHTTPClient {
	return &AgentHTTPClientImpl{client}
}

func (c *AgentHTTPClientImpl) CheckQuestionSecurity(ctx context.Context, in *CheckQuestionSecurityRequest, opts ...http.CallOption) (*CheckQuestionSecurityReply, error) {
	var out CheckQuestionSecurityReply
	pattern := "/agent/checkQuestionSecurity"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentCheckQuestionSecurity))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) CreateAgent(ctx context.Context, in *CreateAgentRequest, opts ...http.CallOption) (*CreateAgentReply, error) {
	var out CreateAgentReply
	pattern := "/agent/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentCreateAgent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) DeleteAgent(ctx context.Context, in *DeleteAgentRequest, opts ...http.CallOption) (*DeleteAgentReply, error) {
	var out DeleteAgentReply
	pattern := "/agent/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentDeleteAgent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) GenerateQuestionOptimization(ctx context.Context, in *GenerateQuestionOptimizationRequest, opts ...http.CallOption) (*GenerateQuestionOptimizationReply, error) {
	var out GenerateQuestionOptimizationReply
	pattern := "/agent/generateQuestionOptimization"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentGenerateQuestionOptimization))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) GetAgent(ctx context.Context, in *GetAgentRequest, opts ...http.CallOption) (*GetAgentReply, error) {
	var out GetAgentReply
	pattern := "/agent/get"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentGetAgent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) GetAgentQueueWhiteList(ctx context.Context, in *GetAgentQueueWhiteListRequest, opts ...http.CallOption) (*GetAgentQueueWhiteListReply, error) {
	var out GetAgentQueueWhiteListReply
	pattern := "/agent/queue/whiteList"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentGetAgentQueueWhiteList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) GetAgentSecurityLogDetail(ctx context.Context, in *GetAgentSecurityLogDetailRequest, opts ...http.CallOption) (*GetAgentSecurityLogDetailReply, error) {
	var out GetAgentSecurityLogDetailReply
	pattern := "/agent/security/logs/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentGetAgentSecurityLogDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) GetAgentSecurityLogsCount(ctx context.Context, in *GetAgentSecurityLogsCountRequest, opts ...http.CallOption) (*GetAgentSecurityLogsCountReply, error) {
	var out GetAgentSecurityLogsCountReply
	pattern := "/agent/security/logs/count"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentGetAgentSecurityLogsCount))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) GetAllAgentInfo(ctx context.Context, in *GetAllAgentInfoRequest, opts ...http.CallOption) (*GetAllAgentInfoReply, error) {
	var out GetAllAgentInfoReply
	pattern := "/agent/all"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentGetAllAgentInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) GetDefaultAvatars(ctx context.Context, in *GetDefaultAvatarsRequest, opts ...http.CallOption) (*GetDefaultAvatarsReply, error) {
	var out GetDefaultAvatarsReply
	pattern := "/agent/defaultAvatars"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentGetDefaultAvatars))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) GetFilePermissionByAgentID(ctx context.Context, in *GetFilePermissionByAgentIDRequest, opts ...http.CallOption) (*GetFilePermissionByAgentIDReply, error) {
	var out GetFilePermissionByAgentIDReply
	pattern := "/agent/filePermission"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentGetFilePermissionByAgentID))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) GetUserAgentsAndKnowledgeBases(ctx context.Context, in *GetUserAgentsAndKnowledgeBasesRequest, opts ...http.CallOption) (*GetUserAgentsAndKnowledgeBasesReply, error) {
	var out GetUserAgentsAndKnowledgeBasesReply
	pattern := "/agent/user/agentsAndKnowledgeBases"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentGetUserAgentsAndKnowledgeBases))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) InternalModelChatPage(ctx context.Context, in *InternalModelChatPageRequest, opts ...http.CallOption) (*InternalModelChatPageReply, error) {
	var out InternalModelChatPageReply
	pattern := "/agent/internalModelChatPage"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentInternalModelChatPage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) PageAgent(ctx context.Context, in *PageAgentRequest, opts ...http.CallOption) (*PageAgentReply, error) {
	var out PageAgentReply
	pattern := "/agent/page"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentPageAgent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) PageAgentSecurityLogs(ctx context.Context, in *PageAgentSecurityLogsRequest, opts ...http.CallOption) (*PageAgentSecurityLogsReply, error) {
	var out PageAgentSecurityLogsReply
	pattern := "/agent/security/logs"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentPageAgentSecurityLogs))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) QueryModelAskClassificationDistribution(ctx context.Context, in *QueryModelAskClassificationDistributionRequest, opts ...http.CallOption) (*QueryModelAskClassificationDistributionReply, error) {
	var out QueryModelAskClassificationDistributionReply
	pattern := "/agent/modelAskClassificationDistribution"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentQueryModelAskClassificationDistribution))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) QueryModelAskClassificationTop10(ctx context.Context, in *QueryModelAskClassificationTop10Request, opts ...http.CallOption) (*QueryModelAskClassificationTop10Reply, error) {
	var out QueryModelAskClassificationTop10Reply
	pattern := "/agent/modelAskClassificationTop10"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentQueryModelAskClassificationTop10))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) QueryUploadFileTypeDistribution(ctx context.Context, in *QueryUploadFileTypeDistributionRequest, opts ...http.CallOption) (*QueryUploadFileTypeDistributionReply, error) {
	var out QueryUploadFileTypeDistributionReply
	pattern := "/agent/uploadFileTypeDistribution"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentQueryUploadFileTypeDistribution))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) QueryUploadFileTypeTop10(ctx context.Context, in *QueryUploadFileTypeTop10Request, opts ...http.CallOption) (*QueryUploadFileTypeTop10Reply, error) {
	var out QueryUploadFileTypeTop10Reply
	pattern := "/agent/uploadFileTypeTop10"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAgentQueryUploadFileTypeTop10))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) SaveAgentQueueWhiteList(ctx context.Context, in *SaveAgentQueueWhiteListRequest, opts ...http.CallOption) (*SaveAgentQueueWhiteListReply, error) {
	var out SaveAgentQueueWhiteListReply
	pattern := "/agent/queue/whiteList"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentSaveAgentQueueWhiteList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) TransferAgent(ctx context.Context, in *TransferAgentRequest, opts ...http.CallOption) (*TransferAgentReply, error) {
	var out TransferAgentReply
	pattern := "/agent/transfer"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentTransferAgent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) UpdateAgent(ctx context.Context, in *UpdateAgentRequest, opts ...http.CallOption) (*UpdateAgentReply, error) {
	var out UpdateAgentReply
	pattern := "/agent/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentUpdateAgent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AgentHTTPClientImpl) UpdateAgentSort(ctx context.Context, in *UpdateAgentSortRequest, opts ...http.CallOption) (*UpdateAgentSortReply, error) {
	var out UpdateAgentSortReply
	pattern := "/agent/update/sort"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAgentUpdateAgentSort))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

syntax = "proto3";

package api.webAgent;

import "errors/errors.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/webAgent;webAgent";

enum ErrorReason {
  option (errors.default_code) = 500;
  ERROR_CALL_AI_API = 0 [(errors.code) = 500];
  ERROR_CALL_AI_API_RECEIVE = 1 [(errors.code) = 500];
  ERROR_AGENT_NOT_FOUND = 2 [(errors.code) = 500];
  ERROR_MODEL_NOT_FOUND = 3 [(errors.code) = 500];
}

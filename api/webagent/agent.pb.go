// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: webagent/agent.proto

package webAgent

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InternalModelChatPageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum    int64                  `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize   int64                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	UserName   string                 `protobuf:"bytes,3,opt,name=userName,proto3" json:"userName,omitempty"`
	DeptName   string                 `protobuf:"bytes,4,opt,name=deptName,proto3" json:"deptName,omitempty"`
	StartTime  *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime    *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=endTime,proto3" json:"endTime,omitempty"`
	ChatItemID int64                  `protobuf:"varint,7,opt,name=chatItemID,proto3" json:"chatItemID,omitempty"`
	// 私有模型id
	AgentID int64 `protobuf:"varint,8,opt,name=agentID,proto3" json:"agentID,omitempty"`
	// 网关模型id
	ModelID int64 `protobuf:"varint,9,opt,name=modelID,proto3" json:"modelID,omitempty"`
	// 搜索文件类型
	Searchfile []string `protobuf:"bytes,10,rep,name=searchfile,proto3" json:"searchfile,omitempty"`
	// 搜索无附件
	NoRefFiles bool `protobuf:"varint,12,opt,name=noRefFiles,proto3" json:"noRefFiles,omitempty"`
	// 是否是网关模型
	SearchModel bool `protobuf:"varint,11,opt,name=searchModel,proto3" json:"searchModel,omitempty"`
	// 文件分类
	Class string `protobuf:"bytes,13,opt,name=class,proto3" json:"class,omitempty"`
}

func (x *InternalModelChatPageRequest) Reset() {
	*x = InternalModelChatPageRequest{}
	mi := &file_webagent_agent_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalModelChatPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalModelChatPageRequest) ProtoMessage() {}

func (x *InternalModelChatPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalModelChatPageRequest.ProtoReflect.Descriptor instead.
func (*InternalModelChatPageRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{0}
}

func (x *InternalModelChatPageRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *InternalModelChatPageRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *InternalModelChatPageRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *InternalModelChatPageRequest) GetDeptName() string {
	if x != nil {
		return x.DeptName
	}
	return ""
}

func (x *InternalModelChatPageRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *InternalModelChatPageRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *InternalModelChatPageRequest) GetChatItemID() int64 {
	if x != nil {
		return x.ChatItemID
	}
	return 0
}

func (x *InternalModelChatPageRequest) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *InternalModelChatPageRequest) GetModelID() int64 {
	if x != nil {
		return x.ModelID
	}
	return 0
}

func (x *InternalModelChatPageRequest) GetSearchfile() []string {
	if x != nil {
		return x.Searchfile
	}
	return nil
}

func (x *InternalModelChatPageRequest) GetNoRefFiles() bool {
	if x != nil {
		return x.NoRefFiles
	}
	return false
}

func (x *InternalModelChatPageRequest) GetSearchModel() bool {
	if x != nil {
		return x.SearchModel
	}
	return false
}

func (x *InternalModelChatPageRequest) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

type InternalModelChatPageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total   int64                             `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Records []*InternalModelChatPageReplyItem `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *InternalModelChatPageReply) Reset() {
	*x = InternalModelChatPageReply{}
	mi := &file_webagent_agent_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalModelChatPageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalModelChatPageReply) ProtoMessage() {}

func (x *InternalModelChatPageReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalModelChatPageReply.ProtoReflect.Descriptor instead.
func (*InternalModelChatPageReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{1}
}

func (x *InternalModelChatPageReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *InternalModelChatPageReply) GetRecords() []*InternalModelChatPageReplyItem {
	if x != nil {
		return x.Records
	}
	return nil
}

type InternalModelChatPageReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message     string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	Username    string                 `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	UserID      int64                  `protobuf:"varint,3,opt,name=userID,proto3" json:"userID,omitempty"`
	UserAvatar  string                 `protobuf:"bytes,9,opt,name=userAvatar,proto3" json:"userAvatar,omitempty"`
	DeptName    string                 `protobuf:"bytes,4,opt,name=deptName,proto3" json:"deptName,omitempty"`
	DeptID      int64                  `protobuf:"varint,5,opt,name=deptID,proto3" json:"deptID,omitempty"`
	AgentName   string                 `protobuf:"bytes,6,opt,name=agentName,proto3" json:"agentName,omitempty"`
	AgentAvatar string                 `protobuf:"bytes,7,opt,name=agentAvatar,proto3" json:"agentAvatar,omitempty"`
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	PcName      string                 `protobuf:"bytes,10,opt,name=pcName,proto3" json:"pcName,omitempty"`
	Id          int64                  `protobuf:"varint,11,opt,name=id,proto3" json:"id,omitempty"`
	// 提示词分类
	Class string `protobuf:"bytes,12,opt,name=class,proto3" json:"class,omitempty"`
	// 引用文件
	RefFiesText string `protobuf:"bytes,13,opt,name=refFiesText,proto3" json:"refFiesText,omitempty"`
	// 引用文件
	RefFiles []*AskAgentReply_FileInfo `protobuf:"bytes,14,rep,name=refFiles,proto3" json:"refFiles,omitempty"`
}

func (x *InternalModelChatPageReplyItem) Reset() {
	*x = InternalModelChatPageReplyItem{}
	mi := &file_webagent_agent_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InternalModelChatPageReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InternalModelChatPageReplyItem) ProtoMessage() {}

func (x *InternalModelChatPageReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InternalModelChatPageReplyItem.ProtoReflect.Descriptor instead.
func (*InternalModelChatPageReplyItem) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{2}
}

func (x *InternalModelChatPageReplyItem) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *InternalModelChatPageReplyItem) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *InternalModelChatPageReplyItem) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *InternalModelChatPageReplyItem) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *InternalModelChatPageReplyItem) GetDeptName() string {
	if x != nil {
		return x.DeptName
	}
	return ""
}

func (x *InternalModelChatPageReplyItem) GetDeptID() int64 {
	if x != nil {
		return x.DeptID
	}
	return 0
}

func (x *InternalModelChatPageReplyItem) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *InternalModelChatPageReplyItem) GetAgentAvatar() string {
	if x != nil {
		return x.AgentAvatar
	}
	return ""
}

func (x *InternalModelChatPageReplyItem) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *InternalModelChatPageReplyItem) GetPcName() string {
	if x != nil {
		return x.PcName
	}
	return ""
}

func (x *InternalModelChatPageReplyItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InternalModelChatPageReplyItem) GetClass() string {
	if x != nil {
		return x.Class
	}
	return ""
}

func (x *InternalModelChatPageReplyItem) GetRefFiesText() string {
	if x != nil {
		return x.RefFiesText
	}
	return ""
}

func (x *InternalModelChatPageReplyItem) GetRefFiles() []*AskAgentReply_FileInfo {
	if x != nil {
		return x.RefFiles
	}
	return nil
}

type AgentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 智能体id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 智能体名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 智能体头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 欢迎语
	WelcomeMsg string `protobuf:"bytes,4,opt,name=welcome_msg,json=welcomeMsg,proto3" json:"welcome_msg,omitempty"`
	// 兜底回复
	FallbackMsg string `protobuf:"bytes,5,opt,name=fallback_msg,json=fallbackMsg,proto3" json:"fallback_msg,omitempty"`
	// 创建者id
	OwnerId int64 `protobuf:"varint,6,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// 可见性对象类型
	VisibilityType int64 `protobuf:"varint,7,opt,name=visibility_type,json=visibilityType,proto3" json:"visibility_type,omitempty"`
	// 可见用户id列表
	VisibleToUser []int64 `protobuf:"varint,8,rep,packed,name=visible_to_user,json=visibleToUser,proto3" json:"visible_to_user,omitempty"`
	// 可见部门id列表
	VisibleToDept []int64 `protobuf:"varint,14,rep,packed,name=visible_to_dept,json=visibleToDept,proto3" json:"visible_to_dept,omitempty"`
	// 知识库id列表
	KnowledgeBaseIds []int64 `protobuf:"varint,9,rep,packed,name=knowledge_base_ids,json=knowledgeBaseIds,proto3" json:"knowledge_base_ids,omitempty"`
	// 智能体编排schema
	Schema string `protobuf:"bytes,10,opt,name=schema,proto3" json:"schema,omitempty"`
	// 是否公开
	IsPublic bool `protobuf:"varint,11,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	// 是否启用
	IsEnabled bool `protobuf:"varint,12,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	// 描述
	Description string `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`
	// 模型类型
	ModelType int64 `protobuf:"varint,15,opt,name=modelType,proto3" json:"modelType,omitempty"`
}

func (x *AgentInfo) Reset() {
	*x = AgentInfo{}
	mi := &file_webagent_agent_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgentInfo) ProtoMessage() {}

func (x *AgentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgentInfo.ProtoReflect.Descriptor instead.
func (*AgentInfo) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{3}
}

func (x *AgentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AgentInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AgentInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *AgentInfo) GetWelcomeMsg() string {
	if x != nil {
		return x.WelcomeMsg
	}
	return ""
}

func (x *AgentInfo) GetFallbackMsg() string {
	if x != nil {
		return x.FallbackMsg
	}
	return ""
}

func (x *AgentInfo) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *AgentInfo) GetVisibilityType() int64 {
	if x != nil {
		return x.VisibilityType
	}
	return 0
}

func (x *AgentInfo) GetVisibleToUser() []int64 {
	if x != nil {
		return x.VisibleToUser
	}
	return nil
}

func (x *AgentInfo) GetVisibleToDept() []int64 {
	if x != nil {
		return x.VisibleToDept
	}
	return nil
}

func (x *AgentInfo) GetKnowledgeBaseIds() []int64 {
	if x != nil {
		return x.KnowledgeBaseIds
	}
	return nil
}

func (x *AgentInfo) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *AgentInfo) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *AgentInfo) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *AgentInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AgentInfo) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 用户名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 用户头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_webagent_agent_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{4}
}

func (x *UserInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type DeptInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 部门id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 部门名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeptInfo) Reset() {
	*x = DeptInfo{}
	mi := &file_webagent_agent_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeptInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeptInfo) ProtoMessage() {}

func (x *DeptInfo) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeptInfo.ProtoReflect.Descriptor instead.
func (*DeptInfo) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{5}
}

func (x *DeptInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeptInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SecurityPolicy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 策略分类 1.敏感信息匹配
	PolicyCategory int64 `protobuf:"varint,2,opt,name=policyCategory,proto3" json:"policyCategory,omitempty"`
	// 风险级别 1.低危 2.中危 3.高危
	RiskLevel int64 `protobuf:"varint,3,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	// 是否启用
	Enabled bool `protobuf:"varint,4,opt,name=enabled,proto3" json:"enabled,omitempty"`
	// 策略内容
	Policies []string `protobuf:"bytes,5,rep,name=policies,proto3" json:"policies,omitempty"`
	// 命中策略后的操作 1.阻断 2.警告
	HitAction int64 `protobuf:"varint,6,opt,name=hitAction,proto3" json:"hitAction,omitempty"`
	// 命中策略后的回复
	HitResponse string `protobuf:"bytes,7,opt,name=hitResponse,proto3" json:"hitResponse,omitempty"`
	Id          int64  `protobuf:"varint,8,opt,name=id,proto3" json:"id,omitempty"`
	// 创建时间
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	// 更新时间
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	// 修改人
	UpdatedBy int64 `protobuf:"varint,11,opt,name=updatedBy,proto3" json:"updatedBy,omitempty"`
	// 修改人姓名
	UpdatedByName string `protobuf:"bytes,12,opt,name=updatedByName,proto3" json:"updatedByName,omitempty"`
}

func (x *SecurityPolicy) Reset() {
	*x = SecurityPolicy{}
	mi := &file_webagent_agent_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecurityPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityPolicy) ProtoMessage() {}

func (x *SecurityPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityPolicy.ProtoReflect.Descriptor instead.
func (*SecurityPolicy) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{6}
}

func (x *SecurityPolicy) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SecurityPolicy) GetPolicyCategory() int64 {
	if x != nil {
		return x.PolicyCategory
	}
	return 0
}

func (x *SecurityPolicy) GetRiskLevel() int64 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *SecurityPolicy) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *SecurityPolicy) GetPolicies() []string {
	if x != nil {
		return x.Policies
	}
	return nil
}

func (x *SecurityPolicy) GetHitAction() int64 {
	if x != nil {
		return x.HitAction
	}
	return 0
}

func (x *SecurityPolicy) GetHitResponse() string {
	if x != nil {
		return x.HitResponse
	}
	return ""
}

func (x *SecurityPolicy) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SecurityPolicy) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SecurityPolicy) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SecurityPolicy) GetUpdatedBy() int64 {
	if x != nil {
		return x.UpdatedBy
	}
	return 0
}

func (x *SecurityPolicy) GetUpdatedByName() string {
	if x != nil {
		return x.UpdatedByName
	}
	return ""
}

type CreateAgentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 智能体名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 智能体头像
	Avatar string `protobuf:"bytes,2,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 欢迎语
	WelcomeMsg string `protobuf:"bytes,3,opt,name=welcome_msg,json=welcomeMsg,proto3" json:"welcome_msg,omitempty"`
	// 兜底回复
	FallbackMsg string `protobuf:"bytes,4,opt,name=fallback_msg,json=fallbackMsg,proto3" json:"fallback_msg,omitempty"`
	// 创建者id
	OwnerId int64 `protobuf:"varint,5,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// 可见性对象类型
	VisibilityType int64 `protobuf:"varint,6,opt,name=visibility_type,json=visibilityType,proto3" json:"visibility_type,omitempty"`
	// 可见对象id列表
	VisibleToUser []int64 `protobuf:"varint,7,rep,packed,name=visible_to_user,json=visibleToUser,proto3" json:"visible_to_user,omitempty"`
	// 可见对象id列表
	VisibleToDept []int64 `protobuf:"varint,13,rep,packed,name=visible_to_dept,json=visibleToDept,proto3" json:"visible_to_dept,omitempty"`
	// 知识库id列表
	KnowledgeBaseIds []int64 `protobuf:"varint,8,rep,packed,name=knowledge_base_ids,json=knowledgeBaseIds,proto3" json:"knowledge_base_ids,omitempty"`
	// 智能体编排schema
	Schema string `protobuf:"bytes,9,opt,name=schema,proto3" json:"schema,omitempty"`
	// 是否公开
	IsPublic bool `protobuf:"varint,10,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	// 是否启用
	IsEnabled bool `protobuf:"varint,11,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	// 描述
	Description string `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`
	// 是否显示引用文件
	ShowReferenceFile bool `protobuf:"varint,14,opt,name=showReferenceFile,proto3" json:"showReferenceFile,omitempty"`
	// 1. 内部模型 2. 外部模型
	ModelType int64 `protobuf:"varint,15,opt,name=modelType,proto3" json:"modelType,omitempty"`
	// 模型id
	ModelID int64 `protobuf:"varint,16,opt,name=modelID,proto3" json:"modelID,omitempty"`
	// 联网搜索
	InternetSearch bool `protobuf:"varint,17,opt,name=internetSearch,proto3" json:"internetSearch,omitempty"`
	// 智能体类型 1：基础问答 2：检索深度问答 3：合同审核
	AgentType int64 `protobuf:"varint,18,opt,name=agentType,proto3" json:"agentType,omitempty"`
	// 深度思考
	Thinking bool `protobuf:"varint,19,opt,name=thinking,proto3" json:"thinking,omitempty"`
	// 深度思考模型id
	ThinkingModelID int64 `protobuf:"varint,20,opt,name=thinkingModelID,proto3" json:"thinkingModelID,omitempty"`
	// 角色设定
	RoleSetting string `protobuf:"bytes,21,opt,name=role_setting,json=roleSetting,proto3" json:"role_setting,omitempty"`
	// 安全策略
	SecurityPolicies []*SecurityPolicy `protobuf:"bytes,22,rep,name=securityPolicies,proto3" json:"securityPolicies,omitempty"`
	// 上传附件
	UploadFile bool `protobuf:"varint,23,opt,name=uploadFile,proto3" json:"uploadFile,omitempty"`
	// 语义缓存
	SemanticCache bool `protobuf:"varint,24,opt,name=semanticCache,proto3" json:"semanticCache,omitempty"`
	// 可见对象id列表
	ManageableToUser []int64 `protobuf:"varint,25,rep,packed,name=manageable_to_user,json=manageableToUser,proto3" json:"manageable_to_user,omitempty"`
	// 点击头像
	ClickedAvatar string `protobuf:"bytes,26,opt,name=clicked_avatar,json=clickedAvatar,proto3" json:"clicked_avatar,omitempty"`
}

func (x *CreateAgentRequest) Reset() {
	*x = CreateAgentRequest{}
	mi := &file_webagent_agent_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAgentRequest) ProtoMessage() {}

func (x *CreateAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAgentRequest.ProtoReflect.Descriptor instead.
func (*CreateAgentRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{7}
}

func (x *CreateAgentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateAgentRequest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *CreateAgentRequest) GetWelcomeMsg() string {
	if x != nil {
		return x.WelcomeMsg
	}
	return ""
}

func (x *CreateAgentRequest) GetFallbackMsg() string {
	if x != nil {
		return x.FallbackMsg
	}
	return ""
}

func (x *CreateAgentRequest) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *CreateAgentRequest) GetVisibilityType() int64 {
	if x != nil {
		return x.VisibilityType
	}
	return 0
}

func (x *CreateAgentRequest) GetVisibleToUser() []int64 {
	if x != nil {
		return x.VisibleToUser
	}
	return nil
}

func (x *CreateAgentRequest) GetVisibleToDept() []int64 {
	if x != nil {
		return x.VisibleToDept
	}
	return nil
}

func (x *CreateAgentRequest) GetKnowledgeBaseIds() []int64 {
	if x != nil {
		return x.KnowledgeBaseIds
	}
	return nil
}

func (x *CreateAgentRequest) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *CreateAgentRequest) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *CreateAgentRequest) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *CreateAgentRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateAgentRequest) GetShowReferenceFile() bool {
	if x != nil {
		return x.ShowReferenceFile
	}
	return false
}

func (x *CreateAgentRequest) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

func (x *CreateAgentRequest) GetModelID() int64 {
	if x != nil {
		return x.ModelID
	}
	return 0
}

func (x *CreateAgentRequest) GetInternetSearch() bool {
	if x != nil {
		return x.InternetSearch
	}
	return false
}

func (x *CreateAgentRequest) GetAgentType() int64 {
	if x != nil {
		return x.AgentType
	}
	return 0
}

func (x *CreateAgentRequest) GetThinking() bool {
	if x != nil {
		return x.Thinking
	}
	return false
}

func (x *CreateAgentRequest) GetThinkingModelID() int64 {
	if x != nil {
		return x.ThinkingModelID
	}
	return 0
}

func (x *CreateAgentRequest) GetRoleSetting() string {
	if x != nil {
		return x.RoleSetting
	}
	return ""
}

func (x *CreateAgentRequest) GetSecurityPolicies() []*SecurityPolicy {
	if x != nil {
		return x.SecurityPolicies
	}
	return nil
}

func (x *CreateAgentRequest) GetUploadFile() bool {
	if x != nil {
		return x.UploadFile
	}
	return false
}

func (x *CreateAgentRequest) GetSemanticCache() bool {
	if x != nil {
		return x.SemanticCache
	}
	return false
}

func (x *CreateAgentRequest) GetManageableToUser() []int64 {
	if x != nil {
		return x.ManageableToUser
	}
	return nil
}

func (x *CreateAgentRequest) GetClickedAvatar() string {
	if x != nil {
		return x.ClickedAvatar
	}
	return ""
}

type CreateAgentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 智能体id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 智能体名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 智能体头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 欢迎语
	WelcomeMsg string `protobuf:"bytes,4,opt,name=welcome_msg,json=welcomeMsg,proto3" json:"welcome_msg,omitempty"`
	// 兜底回复
	FallbackMsg string `protobuf:"bytes,5,opt,name=fallback_msg,json=fallbackMsg,proto3" json:"fallback_msg,omitempty"`
	// 创建者id
	OwnerId int64 `protobuf:"varint,6,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// 可见性对象类型
	VisibilityType int64 `protobuf:"varint,7,opt,name=visibility_type,json=visibilityType,proto3" json:"visibility_type,omitempty"`
	// 可见对象id列表
	VisibleToUser []int64 `protobuf:"varint,8,rep,packed,name=visible_to_user,json=visibleToUser,proto3" json:"visible_to_user,omitempty"`
	// 可见对象id列表
	VisibleToDept []int64 `protobuf:"varint,13,rep,packed,name=visible_to_dept,json=visibleToDept,proto3" json:"visible_to_dept,omitempty"`
	// 知识库id列表
	KnowledgeBaseIds []int64 `protobuf:"varint,9,rep,packed,name=knowledge_base_ids,json=knowledgeBaseIds,proto3" json:"knowledge_base_ids,omitempty"`
	// 智能体编排schema
	Schema string `protobuf:"bytes,10,opt,name=schema,proto3" json:"schema,omitempty"`
	// 是否公开
	IsPublic bool `protobuf:"varint,11,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	// 是否启用
	IsEnabled bool `protobuf:"varint,12,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	// 描述
	Description string `protobuf:"bytes,14,opt,name=description,proto3" json:"description,omitempty"`
	// 是否显示引用文件
	ShowReferenceFile bool `protobuf:"varint,15,opt,name=showReferenceFile,proto3" json:"showReferenceFile,omitempty"`
}

func (x *CreateAgentReply) Reset() {
	*x = CreateAgentReply{}
	mi := &file_webagent_agent_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAgentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAgentReply) ProtoMessage() {}

func (x *CreateAgentReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAgentReply.ProtoReflect.Descriptor instead.
func (*CreateAgentReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{8}
}

func (x *CreateAgentReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateAgentReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateAgentReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *CreateAgentReply) GetWelcomeMsg() string {
	if x != nil {
		return x.WelcomeMsg
	}
	return ""
}

func (x *CreateAgentReply) GetFallbackMsg() string {
	if x != nil {
		return x.FallbackMsg
	}
	return ""
}

func (x *CreateAgentReply) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *CreateAgentReply) GetVisibilityType() int64 {
	if x != nil {
		return x.VisibilityType
	}
	return 0
}

func (x *CreateAgentReply) GetVisibleToUser() []int64 {
	if x != nil {
		return x.VisibleToUser
	}
	return nil
}

func (x *CreateAgentReply) GetVisibleToDept() []int64 {
	if x != nil {
		return x.VisibleToDept
	}
	return nil
}

func (x *CreateAgentReply) GetKnowledgeBaseIds() []int64 {
	if x != nil {
		return x.KnowledgeBaseIds
	}
	return nil
}

func (x *CreateAgentReply) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *CreateAgentReply) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *CreateAgentReply) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *CreateAgentReply) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateAgentReply) GetShowReferenceFile() bool {
	if x != nil {
		return x.ShowReferenceFile
	}
	return false
}

// =========================== update
type UpdateAgentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 智能体id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 智能体名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 智能体头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 欢迎语
	WelcomeMsg string `protobuf:"bytes,4,opt,name=welcome_msg,json=welcomeMsg,proto3" json:"welcome_msg,omitempty"`
	// 兜底回复
	FallbackMsg string `protobuf:"bytes,5,opt,name=fallback_msg,json=fallbackMsg,proto3" json:"fallback_msg,omitempty"`
	// 创建者id
	OwnerId int64 `protobuf:"varint,6,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// 可见性对象类型
	VisibilityType int64 `protobuf:"varint,7,opt,name=visibility_type,json=visibilityType,proto3" json:"visibility_type,omitempty"`
	// 可见对象id列表
	VisibleToUser []int64 `protobuf:"varint,8,rep,packed,name=visible_to_user,json=visibleToUser,proto3" json:"visible_to_user,omitempty"`
	// 可见对象id列表
	VisibleToDept []int64 `protobuf:"varint,13,rep,packed,name=visible_to_dept,json=visibleToDept,proto3" json:"visible_to_dept,omitempty"`
	// 知识库id列表
	KnowledgeBaseIds []int64 `protobuf:"varint,9,rep,packed,name=knowledge_base_ids,json=knowledgeBaseIds,proto3" json:"knowledge_base_ids,omitempty"`
	// 智能体编排schema
	Schema string `protobuf:"bytes,10,opt,name=schema,proto3" json:"schema,omitempty"`
	// 是否公开
	IsPublic bool `protobuf:"varint,11,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	// 是否启用
	IsEnabled bool `protobuf:"varint,12,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	// 描述
	Description string `protobuf:"bytes,14,opt,name=description,proto3" json:"description,omitempty"`
	// 是否显示引用文件
	ShowReferenceFile bool `protobuf:"varint,15,opt,name=showReferenceFile,proto3" json:"showReferenceFile,omitempty"`
	// 联网搜索
	InternetSearch bool `protobuf:"varint,16,opt,name=internetSearch,proto3" json:"internetSearch,omitempty"`
	// 角色设定
	RoleSetting string `protobuf:"bytes,17,opt,name=role_setting,json=roleSetting,proto3" json:"role_setting,omitempty"`
	// 深度思考
	Thinking bool `protobuf:"varint,18,opt,name=thinking,proto3" json:"thinking,omitempty"`
	// 深度思考模型id
	ThinkingModelID int64 `protobuf:"varint,19,opt,name=thinkingModelID,proto3" json:"thinkingModelID,omitempty"`
	// 策略，修改策略必须传id，创建策略id为0
	SecurityPolicies []*SecurityPolicy `protobuf:"bytes,20,rep,name=securityPolicies,proto3" json:"securityPolicies,omitempty"`
	// 删除的安全策略id列表
	DeletedSecurityPolicyIDs []int64 `protobuf:"varint,21,rep,packed,name=deletedSecurityPolicyIDs,proto3" json:"deletedSecurityPolicyIDs,omitempty"`
	// 上传附件
	UploadFile bool `protobuf:"varint,22,opt,name=uploadFile,proto3" json:"uploadFile,omitempty"`
	// 语义缓存
	SemanticCache bool `protobuf:"varint,23,opt,name=semanticCache,proto3" json:"semanticCache,omitempty"`
	// 模型id
	ModelID int64 `protobuf:"varint,24,opt,name=modelID,proto3" json:"modelID,omitempty"`
	// 可见对象id列表
	ManageableToUser []int64 `protobuf:"varint,25,rep,packed,name=manageable_to_user,json=manageableToUser,proto3" json:"manageable_to_user,omitempty"`
	// 点击头像地址
	ClickedAvatar string `protobuf:"bytes,26,opt,name=clicked_avatar,json=clickedAvatar,proto3" json:"clicked_avatar,omitempty"`
}

func (x *UpdateAgentRequest) Reset() {
	*x = UpdateAgentRequest{}
	mi := &file_webagent_agent_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentRequest) ProtoMessage() {}

func (x *UpdateAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentRequest.ProtoReflect.Descriptor instead.
func (*UpdateAgentRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateAgentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAgentRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateAgentRequest) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateAgentRequest) GetWelcomeMsg() string {
	if x != nil {
		return x.WelcomeMsg
	}
	return ""
}

func (x *UpdateAgentRequest) GetFallbackMsg() string {
	if x != nil {
		return x.FallbackMsg
	}
	return ""
}

func (x *UpdateAgentRequest) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *UpdateAgentRequest) GetVisibilityType() int64 {
	if x != nil {
		return x.VisibilityType
	}
	return 0
}

func (x *UpdateAgentRequest) GetVisibleToUser() []int64 {
	if x != nil {
		return x.VisibleToUser
	}
	return nil
}

func (x *UpdateAgentRequest) GetVisibleToDept() []int64 {
	if x != nil {
		return x.VisibleToDept
	}
	return nil
}

func (x *UpdateAgentRequest) GetKnowledgeBaseIds() []int64 {
	if x != nil {
		return x.KnowledgeBaseIds
	}
	return nil
}

func (x *UpdateAgentRequest) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *UpdateAgentRequest) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *UpdateAgentRequest) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *UpdateAgentRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateAgentRequest) GetShowReferenceFile() bool {
	if x != nil {
		return x.ShowReferenceFile
	}
	return false
}

func (x *UpdateAgentRequest) GetInternetSearch() bool {
	if x != nil {
		return x.InternetSearch
	}
	return false
}

func (x *UpdateAgentRequest) GetRoleSetting() string {
	if x != nil {
		return x.RoleSetting
	}
	return ""
}

func (x *UpdateAgentRequest) GetThinking() bool {
	if x != nil {
		return x.Thinking
	}
	return false
}

func (x *UpdateAgentRequest) GetThinkingModelID() int64 {
	if x != nil {
		return x.ThinkingModelID
	}
	return 0
}

func (x *UpdateAgentRequest) GetSecurityPolicies() []*SecurityPolicy {
	if x != nil {
		return x.SecurityPolicies
	}
	return nil
}

func (x *UpdateAgentRequest) GetDeletedSecurityPolicyIDs() []int64 {
	if x != nil {
		return x.DeletedSecurityPolicyIDs
	}
	return nil
}

func (x *UpdateAgentRequest) GetUploadFile() bool {
	if x != nil {
		return x.UploadFile
	}
	return false
}

func (x *UpdateAgentRequest) GetSemanticCache() bool {
	if x != nil {
		return x.SemanticCache
	}
	return false
}

func (x *UpdateAgentRequest) GetModelID() int64 {
	if x != nil {
		return x.ModelID
	}
	return 0
}

func (x *UpdateAgentRequest) GetManageableToUser() []int64 {
	if x != nil {
		return x.ManageableToUser
	}
	return nil
}

func (x *UpdateAgentRequest) GetClickedAvatar() string {
	if x != nil {
		return x.ClickedAvatar
	}
	return ""
}

type UpdateAgentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 智能体id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 智能体名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 智能体头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 欢迎语
	WelcomeMsg string `protobuf:"bytes,4,opt,name=welcome_msg,json=welcomeMsg,proto3" json:"welcome_msg,omitempty"`
	// 兜底回复
	FallbackMsg string `protobuf:"bytes,5,opt,name=fallback_msg,json=fallbackMsg,proto3" json:"fallback_msg,omitempty"`
	// 创建者id
	OwnerId int64 `protobuf:"varint,6,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// 可见性对象类型
	VisibilityType int64 `protobuf:"varint,7,opt,name=visibility_type,json=visibilityType,proto3" json:"visibility_type,omitempty"`
	// 可见对象id列表
	VisibleToUser []int64 `protobuf:"varint,8,rep,packed,name=visible_to_user,json=visibleToUser,proto3" json:"visible_to_user,omitempty"`
	// 可见对象id列表
	VisibleToDept []int64 `protobuf:"varint,13,rep,packed,name=visible_to_dept,json=visibleToDept,proto3" json:"visible_to_dept,omitempty"`
	// 知识库id列表
	KnowledgeBaseIds []int64 `protobuf:"varint,9,rep,packed,name=knowledge_base_ids,json=knowledgeBaseIds,proto3" json:"knowledge_base_ids,omitempty"`
	// 智能体编排schema
	Schema string `protobuf:"bytes,10,opt,name=schema,proto3" json:"schema,omitempty"`
	// 是否公开
	IsPublic bool `protobuf:"varint,11,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	// 是否启用
	IsEnabled bool `protobuf:"varint,12,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	// 描述
	Description string `protobuf:"bytes,14,opt,name=description,proto3" json:"description,omitempty"`
	// 是否显示引用文件
	ShowReferenceFile bool `protobuf:"varint,15,opt,name=showReferenceFile,proto3" json:"showReferenceFile,omitempty"`
}

func (x *UpdateAgentReply) Reset() {
	*x = UpdateAgentReply{}
	mi := &file_webagent_agent_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAgentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentReply) ProtoMessage() {}

func (x *UpdateAgentReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentReply.ProtoReflect.Descriptor instead.
func (*UpdateAgentReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateAgentReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAgentReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateAgentReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateAgentReply) GetWelcomeMsg() string {
	if x != nil {
		return x.WelcomeMsg
	}
	return ""
}

func (x *UpdateAgentReply) GetFallbackMsg() string {
	if x != nil {
		return x.FallbackMsg
	}
	return ""
}

func (x *UpdateAgentReply) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *UpdateAgentReply) GetVisibilityType() int64 {
	if x != nil {
		return x.VisibilityType
	}
	return 0
}

func (x *UpdateAgentReply) GetVisibleToUser() []int64 {
	if x != nil {
		return x.VisibleToUser
	}
	return nil
}

func (x *UpdateAgentReply) GetVisibleToDept() []int64 {
	if x != nil {
		return x.VisibleToDept
	}
	return nil
}

func (x *UpdateAgentReply) GetKnowledgeBaseIds() []int64 {
	if x != nil {
		return x.KnowledgeBaseIds
	}
	return nil
}

func (x *UpdateAgentReply) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *UpdateAgentReply) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *UpdateAgentReply) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *UpdateAgentReply) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateAgentReply) GetShowReferenceFile() bool {
	if x != nil {
		return x.ShowReferenceFile
	}
	return false
}

// =========================== delete
type DeleteAgentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 智能体id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteAgentRequest) Reset() {
	*x = DeleteAgentRequest{}
	mi := &file_webagent_agent_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgentRequest) ProtoMessage() {}

func (x *DeleteAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgentRequest.ProtoReflect.Descriptor instead.
func (*DeleteAgentRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteAgentRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteAgentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteAgentReply) Reset() {
	*x = DeleteAgentReply{}
	mi := &file_webagent_agent_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAgentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAgentReply) ProtoMessage() {}

func (x *DeleteAgentReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAgentReply.ProtoReflect.Descriptor instead.
func (*DeleteAgentReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{12}
}

// =========================== get
type GetAgentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *GetAgentRequest) Reset() {
	*x = GetAgentRequest{}
	mi := &file_webagent_agent_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentRequest) ProtoMessage() {}

func (x *GetAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentRequest.ProtoReflect.Descriptor instead.
func (*GetAgentRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{13}
}

func (x *GetAgentRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type GetAgentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agents []*GetAgentReplyItem `protobuf:"bytes,1,rep,name=agents,proto3" json:"agents,omitempty"`
}

func (x *GetAgentReply) Reset() {
	*x = GetAgentReply{}
	mi := &file_webagent_agent_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentReply) ProtoMessage() {}

func (x *GetAgentReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentReply.ProtoReflect.Descriptor instead.
func (*GetAgentReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{14}
}

func (x *GetAgentReply) GetAgents() []*GetAgentReplyItem {
	if x != nil {
		return x.Agents
	}
	return nil
}

type GetAgentReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 智能体id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 智能体名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 头像地址
	AvatarUrl string `protobuf:"bytes,17,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	// 智能体头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 欢迎语
	WelcomeMsg string `protobuf:"bytes,4,opt,name=welcome_msg,json=welcomeMsg,proto3" json:"welcome_msg,omitempty"`
	// 兜底回复
	FallbackMsg string `protobuf:"bytes,5,opt,name=fallback_msg,json=fallbackMsg,proto3" json:"fallback_msg,omitempty"`
	// 创建者id
	OwnerId int64 `protobuf:"varint,6,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// 创建者姓名
	OwnerName string `protobuf:"bytes,16,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`
	// 创建者头像
	OwnerAvatar string `protobuf:"bytes,21,opt,name=owner_avatar,json=ownerAvatar,proto3" json:"owner_avatar,omitempty"`
	// 可见性对象类型
	VisibilityType int64 `protobuf:"varint,7,opt,name=visibility_type,json=visibilityType,proto3" json:"visibility_type,omitempty"`
	// 可见对象id列表
	VisibleToUser []int64 `protobuf:"varint,8,rep,packed,name=visible_to_user,json=visibleToUser,proto3" json:"visible_to_user,omitempty"`
	// 可见对象id列表
	VisibleToDept []int64 `protobuf:"varint,13,rep,packed,name=visible_to_dept,json=visibleToDept,proto3" json:"visible_to_dept,omitempty"`
	// 知识库id列表
	KnowledgeBaseIds []int64 `protobuf:"varint,9,rep,packed,name=knowledge_base_ids,json=knowledgeBaseIds,proto3" json:"knowledge_base_ids,omitempty"`
	// 智能体编排schema
	Schema string `protobuf:"bytes,10,opt,name=schema,proto3" json:"schema,omitempty"`
	// 是否公开
	IsPublic bool `protobuf:"varint,11,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	// 是否启用
	IsEnabled bool `protobuf:"varint,12,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	// 描述
	Description string `protobuf:"bytes,14,opt,name=description,proto3" json:"description,omitempty"`
	// 更新时间
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	// 创建时间
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	// 可见用户
	VisibleUsers []*UserInfo `protobuf:"bytes,19,rep,name=visible_users,json=visibleUsers,proto3" json:"visible_users,omitempty"`
	// 可见部门
	VisibleDepts []*DeptInfo `protobuf:"bytes,20,rep,name=visible_depts,json=visibleDepts,proto3" json:"visible_depts,omitempty"`
	// 是否显示引用文件
	ShowReferenceFile bool `protobuf:"varint,22,opt,name=showReferenceFile,proto3" json:"showReferenceFile,omitempty"`
	// 模型名称
	ModelName string `protobuf:"bytes,23,opt,name=modelName,proto3" json:"modelName,omitempty"`
	// 模型类型
	ModelType int64 `protobuf:"varint,24,opt,name=modelType,proto3" json:"modelType,omitempty"`
	// 模型
	ModelID int64 `protobuf:"varint,25,opt,name=modelID,proto3" json:"modelID,omitempty"`
	// 知识库类型
	KnowledgeBaseType int64 `protobuf:"varint,26,opt,name=knowledge_base_type,json=knowledgeBaseType,proto3" json:"knowledge_base_type,omitempty"`
	// 联网搜索
	InternetSearch bool `protobuf:"varint,27,opt,name=internetSearch,proto3" json:"internetSearch,omitempty"`
	// 是否启用联网搜索开关
	EnableInternetSearchSwitch bool `protobuf:"varint,28,opt,name=enableInternetSearchSwitch,proto3" json:"enableInternetSearchSwitch,omitempty"`
	// 智能体类型 1：基础问答 2：检索深度问答 3：合同审核
	AgentType int64 `protobuf:"varint,29,opt,name=agentType,proto3" json:"agentType,omitempty"`
	// 角色设定
	RoleSetting string `protobuf:"bytes,30,opt,name=roleSetting,proto3" json:"roleSetting,omitempty"`
	// 深度思考
	Thinking bool `protobuf:"varint,31,opt,name=thinking,proto3" json:"thinking,omitempty"`
	// 深度思考模型id
	ThinkingModelID int64 `protobuf:"varint,32,opt,name=thinkingModelID,proto3" json:"thinkingModelID,omitempty"`
	// 深度思考模型名称
	ThinkingModelName string `protobuf:"bytes,33,opt,name=thinkingModelName,proto3" json:"thinkingModelName,omitempty"`
	// 深度思考模型头像
	ThinkingModelAvatar string `protobuf:"bytes,34,opt,name=thinkingModelAvatar,proto3" json:"thinkingModelAvatar,omitempty"`
	// 模型头像
	ModelAvatar   string `protobuf:"bytes,35,opt,name=modelAvatar,proto3" json:"modelAvatar,omitempty"`
	ModelDetailID int64  `protobuf:"varint,36,opt,name=modelDetailID,proto3" json:"modelDetailID,omitempty"`
	// 深度思考启用状态 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
	ThinkingEnableStatus int64 `protobuf:"varint,37,opt,name=thinkingEnableStatus,proto3" json:"thinkingEnableStatus,omitempty"`
	// 安全策略
	SecurityPolicies []*SecurityPolicy `protobuf:"bytes,38,rep,name=securityPolicies,proto3" json:"securityPolicies,omitempty"`
	// 上传附件
	UploadFile bool `protobuf:"varint,39,opt,name=uploadFile,proto3" json:"uploadFile,omitempty"`
	// 语义缓存
	SemanticCache bool `protobuf:"varint,40,opt,name=semanticCache,proto3" json:"semanticCache,omitempty"`
	// 可管理对象id列表
	ManageableToUser []int64 `protobuf:"varint,41,rep,packed,name=manageable_to_user,json=manageableToUser,proto3" json:"manageable_to_user,omitempty"`
	// 可管理用户
	ManageableUsers []*UserInfo `protobuf:"bytes,42,rep,name=manageable_users,json=manageableUsers,proto3" json:"manageable_users,omitempty"`
}

func (x *GetAgentReplyItem) Reset() {
	*x = GetAgentReplyItem{}
	mi := &file_webagent_agent_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentReplyItem) ProtoMessage() {}

func (x *GetAgentReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentReplyItem.ProtoReflect.Descriptor instead.
func (*GetAgentReplyItem) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{15}
}

func (x *GetAgentReplyItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAgentReplyItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetAgentReplyItem) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *GetAgentReplyItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetAgentReplyItem) GetWelcomeMsg() string {
	if x != nil {
		return x.WelcomeMsg
	}
	return ""
}

func (x *GetAgentReplyItem) GetFallbackMsg() string {
	if x != nil {
		return x.FallbackMsg
	}
	return ""
}

func (x *GetAgentReplyItem) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *GetAgentReplyItem) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *GetAgentReplyItem) GetOwnerAvatar() string {
	if x != nil {
		return x.OwnerAvatar
	}
	return ""
}

func (x *GetAgentReplyItem) GetVisibilityType() int64 {
	if x != nil {
		return x.VisibilityType
	}
	return 0
}

func (x *GetAgentReplyItem) GetVisibleToUser() []int64 {
	if x != nil {
		return x.VisibleToUser
	}
	return nil
}

func (x *GetAgentReplyItem) GetVisibleToDept() []int64 {
	if x != nil {
		return x.VisibleToDept
	}
	return nil
}

func (x *GetAgentReplyItem) GetKnowledgeBaseIds() []int64 {
	if x != nil {
		return x.KnowledgeBaseIds
	}
	return nil
}

func (x *GetAgentReplyItem) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *GetAgentReplyItem) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *GetAgentReplyItem) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *GetAgentReplyItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *GetAgentReplyItem) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *GetAgentReplyItem) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *GetAgentReplyItem) GetVisibleUsers() []*UserInfo {
	if x != nil {
		return x.VisibleUsers
	}
	return nil
}

func (x *GetAgentReplyItem) GetVisibleDepts() []*DeptInfo {
	if x != nil {
		return x.VisibleDepts
	}
	return nil
}

func (x *GetAgentReplyItem) GetShowReferenceFile() bool {
	if x != nil {
		return x.ShowReferenceFile
	}
	return false
}

func (x *GetAgentReplyItem) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *GetAgentReplyItem) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

func (x *GetAgentReplyItem) GetModelID() int64 {
	if x != nil {
		return x.ModelID
	}
	return 0
}

func (x *GetAgentReplyItem) GetKnowledgeBaseType() int64 {
	if x != nil {
		return x.KnowledgeBaseType
	}
	return 0
}

func (x *GetAgentReplyItem) GetInternetSearch() bool {
	if x != nil {
		return x.InternetSearch
	}
	return false
}

func (x *GetAgentReplyItem) GetEnableInternetSearchSwitch() bool {
	if x != nil {
		return x.EnableInternetSearchSwitch
	}
	return false
}

func (x *GetAgentReplyItem) GetAgentType() int64 {
	if x != nil {
		return x.AgentType
	}
	return 0
}

func (x *GetAgentReplyItem) GetRoleSetting() string {
	if x != nil {
		return x.RoleSetting
	}
	return ""
}

func (x *GetAgentReplyItem) GetThinking() bool {
	if x != nil {
		return x.Thinking
	}
	return false
}

func (x *GetAgentReplyItem) GetThinkingModelID() int64 {
	if x != nil {
		return x.ThinkingModelID
	}
	return 0
}

func (x *GetAgentReplyItem) GetThinkingModelName() string {
	if x != nil {
		return x.ThinkingModelName
	}
	return ""
}

func (x *GetAgentReplyItem) GetThinkingModelAvatar() string {
	if x != nil {
		return x.ThinkingModelAvatar
	}
	return ""
}

func (x *GetAgentReplyItem) GetModelAvatar() string {
	if x != nil {
		return x.ModelAvatar
	}
	return ""
}

func (x *GetAgentReplyItem) GetModelDetailID() int64 {
	if x != nil {
		return x.ModelDetailID
	}
	return 0
}

func (x *GetAgentReplyItem) GetThinkingEnableStatus() int64 {
	if x != nil {
		return x.ThinkingEnableStatus
	}
	return 0
}

func (x *GetAgentReplyItem) GetSecurityPolicies() []*SecurityPolicy {
	if x != nil {
		return x.SecurityPolicies
	}
	return nil
}

func (x *GetAgentReplyItem) GetUploadFile() bool {
	if x != nil {
		return x.UploadFile
	}
	return false
}

func (x *GetAgentReplyItem) GetSemanticCache() bool {
	if x != nil {
		return x.SemanticCache
	}
	return false
}

func (x *GetAgentReplyItem) GetManageableToUser() []int64 {
	if x != nil {
		return x.ManageableToUser
	}
	return nil
}

func (x *GetAgentReplyItem) GetManageableUsers() []*UserInfo {
	if x != nil {
		return x.ManageableUsers
	}
	return nil
}

// =========================== page
type PageAgentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int64 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	// 是否展示在客户端
	ShowOnClient bool `protobuf:"varint,3,opt,name=showOnClient,proto3" json:"showOnClient,omitempty"`
	// 模型类型 0.所有 1.私有模型 2.网关模型
	ModelType int64 `protobuf:"varint,4,opt,name=modelType,proto3" json:"modelType,omitempty"`
	// 是我创建的
	IsMine bool `protobuf:"varint,5,opt,name=isMine,proto3" json:"isMine,omitempty"`
	// 智能体类别 1.AI工具 2.问答智能体 3.深度检索问答智能体 4.问答+深度检索智能体 5.常用智能体
	AgentCategoryType int64 `protobuf:"varint,6,opt,name=agentCategoryType,proto3" json:"agentCategoryType,omitempty"`
	// 智能体名称
	AgentName string `protobuf:"bytes,7,opt,name=agentName,proto3" json:"agentName,omitempty"`
}

func (x *PageAgentRequest) Reset() {
	*x = PageAgentRequest{}
	mi := &file_webagent_agent_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageAgentRequest) ProtoMessage() {}

func (x *PageAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageAgentRequest.ProtoReflect.Descriptor instead.
func (*PageAgentRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{16}
}

func (x *PageAgentRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PageAgentRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageAgentRequest) GetShowOnClient() bool {
	if x != nil {
		return x.ShowOnClient
	}
	return false
}

func (x *PageAgentRequest) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

func (x *PageAgentRequest) GetIsMine() bool {
	if x != nil {
		return x.IsMine
	}
	return false
}

func (x *PageAgentRequest) GetAgentCategoryType() int64 {
	if x != nil {
		return x.AgentCategoryType
	}
	return 0
}

func (x *PageAgentRequest) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

type PageAgentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total   int64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Records []*PageAgentReplyItem `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *PageAgentReply) Reset() {
	*x = PageAgentReply{}
	mi := &file_webagent_agent_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageAgentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageAgentReply) ProtoMessage() {}

func (x *PageAgentReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageAgentReply.ProtoReflect.Descriptor instead.
func (*PageAgentReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{17}
}

func (x *PageAgentReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PageAgentReply) GetRecords() []*PageAgentReplyItem {
	if x != nil {
		return x.Records
	}
	return nil
}

type PageAgentReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 智能体id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 智能体名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 智能体头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	// 头像地址
	AvatarUrl string `protobuf:"bytes,17,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	// 欢迎语
	WelcomeMsg string `protobuf:"bytes,4,opt,name=welcome_msg,json=welcomeMsg,proto3" json:"welcome_msg,omitempty"`
	// 兜底回复
	FallbackMsg string `protobuf:"bytes,5,opt,name=fallback_msg,json=fallbackMsg,proto3" json:"fallback_msg,omitempty"`
	// 创建者id
	OwnerId int64 `protobuf:"varint,6,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// 创建者姓名
	OwnerName string `protobuf:"bytes,16,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`
	// 创建者头像
	OwnerAvatar string `protobuf:"bytes,21,opt,name=owner_avatar,json=ownerAvatar,proto3" json:"owner_avatar,omitempty"`
	// 可见性对象类型
	VisibilityType int64 `protobuf:"varint,7,opt,name=visibility_type,json=visibilityType,proto3" json:"visibility_type,omitempty"`
	// 可见对象id列表
	VisibleToUser []int64 `protobuf:"varint,8,rep,packed,name=visible_to_user,json=visibleToUser,proto3" json:"visible_to_user,omitempty"`
	// 可见对象id列表
	VisibleToDept []int64 `protobuf:"varint,18,rep,packed,name=visible_to_dept,json=visibleToDept,proto3" json:"visible_to_dept,omitempty"`
	// 知识库id列表
	KnowledgeBaseIds []int64 `protobuf:"varint,9,rep,packed,name=knowledge_base_ids,json=knowledgeBaseIds,proto3" json:"knowledge_base_ids,omitempty"`
	// 智能体编排schema
	Schema string `protobuf:"bytes,10,opt,name=schema,proto3" json:"schema,omitempty"`
	// 是否公开
	IsPublic bool `protobuf:"varint,11,opt,name=is_public,json=isPublic,proto3" json:"is_public,omitempty"`
	// 是否启用
	IsEnabled bool `protobuf:"varint,12,opt,name=is_enabled,json=isEnabled,proto3" json:"is_enabled,omitempty"`
	// 描述
	Description string `protobuf:"bytes,13,opt,name=description,proto3" json:"description,omitempty"`
	// 更新时间
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	// 创建时间
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	// 可见用户
	VisibleUsers []*UserInfo `protobuf:"bytes,19,rep,name=visible_users,json=visibleUsers,proto3" json:"visible_users,omitempty"`
	// 可见部门
	VisibleDepts []*DeptInfo `protobuf:"bytes,20,rep,name=visible_depts,json=visibleDepts,proto3" json:"visible_depts,omitempty"`
	// 是否显示引用文件
	ShowReferenceFile bool `protobuf:"varint,22,opt,name=showReferenceFile,proto3" json:"showReferenceFile,omitempty"`
	// 模型名称
	ModelName string `protobuf:"bytes,23,opt,name=modelName,proto3" json:"modelName,omitempty"`
	// 模型类型
	ModelType int64 `protobuf:"varint,24,opt,name=modelType,proto3" json:"modelType,omitempty"`
	// 模型
	Model int64 `protobuf:"varint,25,opt,name=model,proto3" json:"model,omitempty"`
	// 知识库类型
	KnowledgeBaseType int64 `protobuf:"varint,26,opt,name=knowledgeBaseType,proto3" json:"knowledgeBaseType,omitempty"`
	// 是否可联网搜索
	CanInternetSearch bool  `protobuf:"varint,27,opt,name=canInternetSearch,proto3" json:"canInternetSearch,omitempty"`
	AgentType         int64 `protobuf:"varint,28,opt,name=agentType,proto3" json:"agentType,omitempty"`
	// 深度思考开启状态 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
	ThinkingEnableStatus int64 `protobuf:"varint,29,opt,name=thinkingEnableStatus,proto3" json:"thinkingEnableStatus,omitempty"`
	// 是否开启深度思考
	Thinking bool `protobuf:"varint,30,opt,name=thinking,proto3" json:"thinking,omitempty"`
	// 上传文件
	UploadFile bool `protobuf:"varint,31,opt,name=uploadFile,proto3" json:"uploadFile,omitempty"`
	// 语义缓存
	SemanticCache bool `protobuf:"varint,32,opt,name=semanticCache,proto3" json:"semanticCache,omitempty"`
	// 外网模型名称
	ModelDetailName string `protobuf:"bytes,33,opt,name=modelDetailName,proto3" json:"modelDetailName,omitempty"`
	// 可管理用户
	ManageableUsers []*UserInfo `protobuf:"bytes,34,rep,name=manageable_users,json=manageableUsers,proto3" json:"manageable_users,omitempty"`
	// 可管理用户id列表
	ManageableToUser []int64 `protobuf:"varint,35,rep,packed,name=manageable_to_user,json=manageableToUser,proto3" json:"manageable_to_user,omitempty"`
	// 点击头像地址
	ClickedAvatarUrl string `protobuf:"bytes,36,opt,name=clicked_avatar_url,json=clickedAvatarUrl,proto3" json:"clicked_avatar_url,omitempty"`
}

func (x *PageAgentReplyItem) Reset() {
	*x = PageAgentReplyItem{}
	mi := &file_webagent_agent_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageAgentReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageAgentReplyItem) ProtoMessage() {}

func (x *PageAgentReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageAgentReplyItem.ProtoReflect.Descriptor instead.
func (*PageAgentReplyItem) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{18}
}

func (x *PageAgentReplyItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PageAgentReplyItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PageAgentReplyItem) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *PageAgentReplyItem) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *PageAgentReplyItem) GetWelcomeMsg() string {
	if x != nil {
		return x.WelcomeMsg
	}
	return ""
}

func (x *PageAgentReplyItem) GetFallbackMsg() string {
	if x != nil {
		return x.FallbackMsg
	}
	return ""
}

func (x *PageAgentReplyItem) GetOwnerId() int64 {
	if x != nil {
		return x.OwnerId
	}
	return 0
}

func (x *PageAgentReplyItem) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *PageAgentReplyItem) GetOwnerAvatar() string {
	if x != nil {
		return x.OwnerAvatar
	}
	return ""
}

func (x *PageAgentReplyItem) GetVisibilityType() int64 {
	if x != nil {
		return x.VisibilityType
	}
	return 0
}

func (x *PageAgentReplyItem) GetVisibleToUser() []int64 {
	if x != nil {
		return x.VisibleToUser
	}
	return nil
}

func (x *PageAgentReplyItem) GetVisibleToDept() []int64 {
	if x != nil {
		return x.VisibleToDept
	}
	return nil
}

func (x *PageAgentReplyItem) GetKnowledgeBaseIds() []int64 {
	if x != nil {
		return x.KnowledgeBaseIds
	}
	return nil
}

func (x *PageAgentReplyItem) GetSchema() string {
	if x != nil {
		return x.Schema
	}
	return ""
}

func (x *PageAgentReplyItem) GetIsPublic() bool {
	if x != nil {
		return x.IsPublic
	}
	return false
}

func (x *PageAgentReplyItem) GetIsEnabled() bool {
	if x != nil {
		return x.IsEnabled
	}
	return false
}

func (x *PageAgentReplyItem) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PageAgentReplyItem) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PageAgentReplyItem) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PageAgentReplyItem) GetVisibleUsers() []*UserInfo {
	if x != nil {
		return x.VisibleUsers
	}
	return nil
}

func (x *PageAgentReplyItem) GetVisibleDepts() []*DeptInfo {
	if x != nil {
		return x.VisibleDepts
	}
	return nil
}

func (x *PageAgentReplyItem) GetShowReferenceFile() bool {
	if x != nil {
		return x.ShowReferenceFile
	}
	return false
}

func (x *PageAgentReplyItem) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *PageAgentReplyItem) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

func (x *PageAgentReplyItem) GetModel() int64 {
	if x != nil {
		return x.Model
	}
	return 0
}

func (x *PageAgentReplyItem) GetKnowledgeBaseType() int64 {
	if x != nil {
		return x.KnowledgeBaseType
	}
	return 0
}

func (x *PageAgentReplyItem) GetCanInternetSearch() bool {
	if x != nil {
		return x.CanInternetSearch
	}
	return false
}

func (x *PageAgentReplyItem) GetAgentType() int64 {
	if x != nil {
		return x.AgentType
	}
	return 0
}

func (x *PageAgentReplyItem) GetThinkingEnableStatus() int64 {
	if x != nil {
		return x.ThinkingEnableStatus
	}
	return 0
}

func (x *PageAgentReplyItem) GetThinking() bool {
	if x != nil {
		return x.Thinking
	}
	return false
}

func (x *PageAgentReplyItem) GetUploadFile() bool {
	if x != nil {
		return x.UploadFile
	}
	return false
}

func (x *PageAgentReplyItem) GetSemanticCache() bool {
	if x != nil {
		return x.SemanticCache
	}
	return false
}

func (x *PageAgentReplyItem) GetModelDetailName() string {
	if x != nil {
		return x.ModelDetailName
	}
	return ""
}

func (x *PageAgentReplyItem) GetManageableUsers() []*UserInfo {
	if x != nil {
		return x.ManageableUsers
	}
	return nil
}

func (x *PageAgentReplyItem) GetManageableToUser() []int64 {
	if x != nil {
		return x.ManageableToUser
	}
	return nil
}

func (x *PageAgentReplyItem) GetClickedAvatarUrl() string {
	if x != nil {
		return x.ClickedAvatarUrl
	}
	return ""
}

type AskAgentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question string                      `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	ChatID   int64                       `protobuf:"varint,2,opt,name=chatID,proto3" json:"chatID,omitempty"`
	RoundID  int64                       `protobuf:"varint,3,opt,name=roundID,proto3" json:"roundID,omitempty"`
	Files    []*AskAgentRequest_FileInfo `protobuf:"bytes,4,rep,name=files,proto3" json:"files,omitempty"`
	AgentID  int64                       `protobuf:"varint,5,opt,name=agentID,proto3" json:"agentID,omitempty"`
	// 是否多轮
	IsMultiRound bool   `protobuf:"varint,6,opt,name=isMultiRound,proto3" json:"isMultiRound,omitempty"`
	PcName       string `protobuf:"bytes,7,opt,name=pcName,proto3" json:"pcName,omitempty"`
	// 是否联网搜索
	InternetSearch bool `protobuf:"varint,8,opt,name=internetSearch,proto3" json:"internetSearch,omitempty"`
	// 是否深度思考
	Thinking bool `protobuf:"varint,9,opt,name=thinking,proto3" json:"thinking,omitempty"`
	// chatItemID, 如果传值则在此chatItemID下进行处理，不会对问题创建新的chatItem
	ChatItemID int64 `protobuf:"varint,10,opt,name=chatItemID,proto3" json:"chatItemID,omitempty"`
	// 指定sheet知识库, 用于指定一个sheet文件问答，因为同一个sheet文件在每个知识库都是不一样的。
	DesignateSheetKnowledgeBaseID int64 `protobuf:"varint,11,opt,name=designateSheetKnowledgeBaseID,proto3" json:"designateSheetKnowledgeBaseID,omitempty"`
	// 已存在图表数据
	ExistingTableData string `protobuf:"bytes,12,opt,name=existingTableData,proto3" json:"existingTableData,omitempty"`
}

func (x *AskAgentRequest) Reset() {
	*x = AskAgentRequest{}
	mi := &file_webagent_agent_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskAgentRequest) ProtoMessage() {}

func (x *AskAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskAgentRequest.ProtoReflect.Descriptor instead.
func (*AskAgentRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{19}
}

func (x *AskAgentRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *AskAgentRequest) GetChatID() int64 {
	if x != nil {
		return x.ChatID
	}
	return 0
}

func (x *AskAgentRequest) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *AskAgentRequest) GetFiles() []*AskAgentRequest_FileInfo {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *AskAgentRequest) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *AskAgentRequest) GetIsMultiRound() bool {
	if x != nil {
		return x.IsMultiRound
	}
	return false
}

func (x *AskAgentRequest) GetPcName() string {
	if x != nil {
		return x.PcName
	}
	return ""
}

func (x *AskAgentRequest) GetInternetSearch() bool {
	if x != nil {
		return x.InternetSearch
	}
	return false
}

func (x *AskAgentRequest) GetThinking() bool {
	if x != nil {
		return x.Thinking
	}
	return false
}

func (x *AskAgentRequest) GetChatItemID() int64 {
	if x != nil {
		return x.ChatItemID
	}
	return 0
}

func (x *AskAgentRequest) GetDesignateSheetKnowledgeBaseID() int64 {
	if x != nil {
		return x.DesignateSheetKnowledgeBaseID
	}
	return 0
}

func (x *AskAgentRequest) GetExistingTableData() string {
	if x != nil {
		return x.ExistingTableData
	}
	return ""
}

type AskAgentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Answer string                    `protobuf:"bytes,1,opt,name=answer,proto3" json:"answer,omitempty"`
	Files  []*AskAgentReply_FileInfo `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`
	// 0:回答中,1:回答完成
	Status       int64  `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	RoundID      int64  `protobuf:"varint,4,opt,name=roundID,proto3" json:"roundID,omitempty"`
	Type         int64  `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
	Reason       string `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
	ChatItemID   int64  `protobuf:"varint,7,opt,name=chatItemID,proto3" json:"chatItemID,omitempty"`
	DebugContent string `protobuf:"bytes,8,opt,name=debugContent,proto3" json:"debugContent,omitempty"`
}

func (x *AskAgentReply) Reset() {
	*x = AskAgentReply{}
	mi := &file_webagent_agent_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskAgentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskAgentReply) ProtoMessage() {}

func (x *AskAgentReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskAgentReply.ProtoReflect.Descriptor instead.
func (*AskAgentReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{20}
}

func (x *AskAgentReply) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *AskAgentReply) GetFiles() []*AskAgentReply_FileInfo {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *AskAgentReply) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AskAgentReply) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *AskAgentReply) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *AskAgentReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *AskAgentReply) GetChatItemID() int64 {
	if x != nil {
		return x.ChatItemID
	}
	return 0
}

func (x *AskAgentReply) GetDebugContent() string {
	if x != nil {
		return x.DebugContent
	}
	return ""
}

type GetDefaultAvatarsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 1.部门应用图标  2.数字员工图标
	AvatarType int64 `protobuf:"varint,1,opt,name=avatar_type,json=avatarType,proto3" json:"avatar_type,omitempty"`
}

func (x *GetDefaultAvatarsRequest) Reset() {
	*x = GetDefaultAvatarsRequest{}
	mi := &file_webagent_agent_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDefaultAvatarsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDefaultAvatarsRequest) ProtoMessage() {}

func (x *GetDefaultAvatarsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDefaultAvatarsRequest.ProtoReflect.Descriptor instead.
func (*GetDefaultAvatarsRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{21}
}

func (x *GetDefaultAvatarsRequest) GetAvatarType() int64 {
	if x != nil {
		return x.AvatarType
	}
	return 0
}

type GetDefaultAvatarsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Avatars []*GetDefaultAvatarItems `protobuf:"bytes,1,rep,name=avatars,proto3" json:"avatars,omitempty"`
}

func (x *GetDefaultAvatarsReply) Reset() {
	*x = GetDefaultAvatarsReply{}
	mi := &file_webagent_agent_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDefaultAvatarsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDefaultAvatarsReply) ProtoMessage() {}

func (x *GetDefaultAvatarsReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDefaultAvatarsReply.ProtoReflect.Descriptor instead.
func (*GetDefaultAvatarsReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{22}
}

func (x *GetDefaultAvatarsReply) GetAvatars() []*GetDefaultAvatarItems {
	if x != nil {
		return x.Avatars
	}
	return nil
}

type GetDefaultAvatarItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Avatar           string `protobuf:"bytes,1,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Url              string `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	ClickedAvatar    string `protobuf:"bytes,3,opt,name=clicked_avatar,json=clickedAvatar,proto3" json:"clicked_avatar,omitempty"`
	ClickedAvatarUrl string `protobuf:"bytes,4,opt,name=clicked_avatar_url,json=clickedAvatarUrl,proto3" json:"clicked_avatar_url,omitempty"`
}

func (x *GetDefaultAvatarItems) Reset() {
	*x = GetDefaultAvatarItems{}
	mi := &file_webagent_agent_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDefaultAvatarItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDefaultAvatarItems) ProtoMessage() {}

func (x *GetDefaultAvatarItems) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDefaultAvatarItems.ProtoReflect.Descriptor instead.
func (*GetDefaultAvatarItems) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{23}
}

func (x *GetDefaultAvatarItems) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetDefaultAvatarItems) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GetDefaultAvatarItems) GetClickedAvatar() string {
	if x != nil {
		return x.ClickedAvatar
	}
	return ""
}

func (x *GetDefaultAvatarItems) GetClickedAvatarUrl() string {
	if x != nil {
		return x.ClickedAvatarUrl
	}
	return ""
}

type GetFilePermissionByAgentIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentID        int64 `protobuf:"varint,1,opt,name=agentID,proto3" json:"agentID,omitempty"`
	FileRelationID int64 `protobuf:"varint,2,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
}

func (x *GetFilePermissionByAgentIDRequest) Reset() {
	*x = GetFilePermissionByAgentIDRequest{}
	mi := &file_webagent_agent_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFilePermissionByAgentIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFilePermissionByAgentIDRequest) ProtoMessage() {}

func (x *GetFilePermissionByAgentIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFilePermissionByAgentIDRequest.ProtoReflect.Descriptor instead.
func (*GetFilePermissionByAgentIDRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{24}
}

func (x *GetFilePermissionByAgentIDRequest) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *GetFilePermissionByAgentIDRequest) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

type GetFilePermissionByAgentIDReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HasPermission bool `protobuf:"varint,1,opt,name=hasPermission,proto3" json:"hasPermission,omitempty"`
}

func (x *GetFilePermissionByAgentIDReply) Reset() {
	*x = GetFilePermissionByAgentIDReply{}
	mi := &file_webagent_agent_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFilePermissionByAgentIDReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFilePermissionByAgentIDReply) ProtoMessage() {}

func (x *GetFilePermissionByAgentIDReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFilePermissionByAgentIDReply.ProtoReflect.Descriptor instead.
func (*GetFilePermissionByAgentIDReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{25}
}

func (x *GetFilePermissionByAgentIDReply) GetHasPermission() bool {
	if x != nil {
		return x.HasPermission
	}
	return false
}

type QueryModelAskClassificationDistributionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 1. 私有模型 2. 网关模型 3. 外网模型
	ModelType int64                  `protobuf:"varint,1,opt,name=modelType,proto3" json:"modelType,omitempty"`
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *QueryModelAskClassificationDistributionRequest) Reset() {
	*x = QueryModelAskClassificationDistributionRequest{}
	mi := &file_webagent_agent_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryModelAskClassificationDistributionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryModelAskClassificationDistributionRequest) ProtoMessage() {}

func (x *QueryModelAskClassificationDistributionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryModelAskClassificationDistributionRequest.ProtoReflect.Descriptor instead.
func (*QueryModelAskClassificationDistributionRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{26}
}

func (x *QueryModelAskClassificationDistributionRequest) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

func (x *QueryModelAskClassificationDistributionRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *QueryModelAskClassificationDistributionRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type QueryModelAskClassificationDistributionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClassificationDistributions []*QueryModelAskClassificationDistributionReply_ClassificationDistribution `protobuf:"bytes,1,rep,name=classificationDistributions,proto3" json:"classificationDistributions,omitempty"`
}

func (x *QueryModelAskClassificationDistributionReply) Reset() {
	*x = QueryModelAskClassificationDistributionReply{}
	mi := &file_webagent_agent_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryModelAskClassificationDistributionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryModelAskClassificationDistributionReply) ProtoMessage() {}

func (x *QueryModelAskClassificationDistributionReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryModelAskClassificationDistributionReply.ProtoReflect.Descriptor instead.
func (*QueryModelAskClassificationDistributionReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{27}
}

func (x *QueryModelAskClassificationDistributionReply) GetClassificationDistributions() []*QueryModelAskClassificationDistributionReply_ClassificationDistribution {
	if x != nil {
		return x.ClassificationDistributions
	}
	return nil
}

type QueryUploadFileTypeDistributionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 1. 私有模型 2. 网关模型 3. 外网模型
	ModelType int64                  `protobuf:"varint,1,opt,name=modelType,proto3" json:"modelType,omitempty"`
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *QueryUploadFileTypeDistributionRequest) Reset() {
	*x = QueryUploadFileTypeDistributionRequest{}
	mi := &file_webagent_agent_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryUploadFileTypeDistributionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUploadFileTypeDistributionRequest) ProtoMessage() {}

func (x *QueryUploadFileTypeDistributionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUploadFileTypeDistributionRequest.ProtoReflect.Descriptor instead.
func (*QueryUploadFileTypeDistributionRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{28}
}

func (x *QueryUploadFileTypeDistributionRequest) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

func (x *QueryUploadFileTypeDistributionRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *QueryUploadFileTypeDistributionRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type QueryUploadFileTypeDistributionReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileTypeDistributions []*QueryUploadFileTypeDistributionReply_FileTypeDistribution `protobuf:"bytes,1,rep,name=fileTypeDistributions,proto3" json:"fileTypeDistributions,omitempty"`
}

func (x *QueryUploadFileTypeDistributionReply) Reset() {
	*x = QueryUploadFileTypeDistributionReply{}
	mi := &file_webagent_agent_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryUploadFileTypeDistributionReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUploadFileTypeDistributionReply) ProtoMessage() {}

func (x *QueryUploadFileTypeDistributionReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUploadFileTypeDistributionReply.ProtoReflect.Descriptor instead.
func (*QueryUploadFileTypeDistributionReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{29}
}

func (x *QueryUploadFileTypeDistributionReply) GetFileTypeDistributions() []*QueryUploadFileTypeDistributionReply_FileTypeDistribution {
	if x != nil {
		return x.FileTypeDistributions
	}
	return nil
}

type QueryModelAskClassificationTop10Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 1. 员工top 10 2. 部门top 10
	TopType   int64                  `protobuf:"varint,1,opt,name=topType,proto3" json:"topType,omitempty"`
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	// 1. 私有模型 2. 网关模型
	ModelType int64 `protobuf:"varint,4,opt,name=modelType,proto3" json:"modelType,omitempty"`
}

func (x *QueryModelAskClassificationTop10Request) Reset() {
	*x = QueryModelAskClassificationTop10Request{}
	mi := &file_webagent_agent_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryModelAskClassificationTop10Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryModelAskClassificationTop10Request) ProtoMessage() {}

func (x *QueryModelAskClassificationTop10Request) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryModelAskClassificationTop10Request.ProtoReflect.Descriptor instead.
func (*QueryModelAskClassificationTop10Request) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{30}
}

func (x *QueryModelAskClassificationTop10Request) GetTopType() int64 {
	if x != nil {
		return x.TopType
	}
	return 0
}

func (x *QueryModelAskClassificationTop10Request) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *QueryModelAskClassificationTop10Request) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *QueryModelAskClassificationTop10Request) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

type QueryModelAskClassificationTop10Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *QueryModelAskClassificationTop10Reply) Reset() {
	*x = QueryModelAskClassificationTop10Reply{}
	mi := &file_webagent_agent_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryModelAskClassificationTop10Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryModelAskClassificationTop10Reply) ProtoMessage() {}

func (x *QueryModelAskClassificationTop10Reply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryModelAskClassificationTop10Reply.ProtoReflect.Descriptor instead.
func (*QueryModelAskClassificationTop10Reply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{31}
}

func (x *QueryModelAskClassificationTop10Reply) GetItems() []*QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type QueryUploadFileTypeTop10Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 1. 员工top 10 2. 部门top 10
	TopType   int64                  `protobuf:"varint,1,opt,name=topType,proto3" json:"topType,omitempty"`
	StartTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	// 1. 私有模型 2. 网关模型
	ModelType int64 `protobuf:"varint,4,opt,name=modelType,proto3" json:"modelType,omitempty"`
}

func (x *QueryUploadFileTypeTop10Request) Reset() {
	*x = QueryUploadFileTypeTop10Request{}
	mi := &file_webagent_agent_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryUploadFileTypeTop10Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUploadFileTypeTop10Request) ProtoMessage() {}

func (x *QueryUploadFileTypeTop10Request) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUploadFileTypeTop10Request.ProtoReflect.Descriptor instead.
func (*QueryUploadFileTypeTop10Request) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{32}
}

func (x *QueryUploadFileTypeTop10Request) GetTopType() int64 {
	if x != nil {
		return x.TopType
	}
	return 0
}

func (x *QueryUploadFileTypeTop10Request) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *QueryUploadFileTypeTop10Request) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *QueryUploadFileTypeTop10Request) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

type QueryUploadFileTypeTop10Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *QueryUploadFileTypeTop10Reply) Reset() {
	*x = QueryUploadFileTypeTop10Reply{}
	mi := &file_webagent_agent_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryUploadFileTypeTop10Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUploadFileTypeTop10Reply) ProtoMessage() {}

func (x *QueryUploadFileTypeTop10Reply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUploadFileTypeTop10Reply.ProtoReflect.Descriptor instead.
func (*QueryUploadFileTypeTop10Reply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{33}
}

func (x *QueryUploadFileTypeTop10Reply) GetItems() []*QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetAllAgentInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 1. 私有模型 2. 网关模型
	ModelType int64 `protobuf:"varint,1,opt,name=model_type,json=modelType,proto3" json:"model_type,omitempty"`
}

func (x *GetAllAgentInfoRequest) Reset() {
	*x = GetAllAgentInfoRequest{}
	mi := &file_webagent_agent_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllAgentInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllAgentInfoRequest) ProtoMessage() {}

func (x *GetAllAgentInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllAgentInfoRequest.ProtoReflect.Descriptor instead.
func (*GetAllAgentInfoRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{34}
}

func (x *GetAllAgentInfoRequest) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

type GetAllAgentInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agents []*GetAllAgentInfoReply_AgentInfo `protobuf:"bytes,1,rep,name=agents,proto3" json:"agents,omitempty"`
}

func (x *GetAllAgentInfoReply) Reset() {
	*x = GetAllAgentInfoReply{}
	mi := &file_webagent_agent_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllAgentInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllAgentInfoReply) ProtoMessage() {}

func (x *GetAllAgentInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllAgentInfoReply.ProtoReflect.Descriptor instead.
func (*GetAllAgentInfoReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{35}
}

func (x *GetAllAgentInfoReply) GetAgents() []*GetAllAgentInfoReply_AgentInfo {
	if x != nil {
		return x.Agents
	}
	return nil
}

type SaveAgentQueueWhiteListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserIDs []int64 `protobuf:"varint,1,rep,packed,name=userIDs,proto3" json:"userIDs,omitempty"`
}

func (x *SaveAgentQueueWhiteListRequest) Reset() {
	*x = SaveAgentQueueWhiteListRequest{}
	mi := &file_webagent_agent_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAgentQueueWhiteListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAgentQueueWhiteListRequest) ProtoMessage() {}

func (x *SaveAgentQueueWhiteListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAgentQueueWhiteListRequest.ProtoReflect.Descriptor instead.
func (*SaveAgentQueueWhiteListRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{36}
}

func (x *SaveAgentQueueWhiteListRequest) GetUserIDs() []int64 {
	if x != nil {
		return x.UserIDs
	}
	return nil
}

type SaveAgentQueueWhiteListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SaveAgentQueueWhiteListReply) Reset() {
	*x = SaveAgentQueueWhiteListReply{}
	mi := &file_webagent_agent_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveAgentQueueWhiteListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveAgentQueueWhiteListReply) ProtoMessage() {}

func (x *SaveAgentQueueWhiteListReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveAgentQueueWhiteListReply.ProtoReflect.Descriptor instead.
func (*SaveAgentQueueWhiteListReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{37}
}

type GetAgentQueueWhiteListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetAgentQueueWhiteListRequest) Reset() {
	*x = GetAgentQueueWhiteListRequest{}
	mi := &file_webagent_agent_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentQueueWhiteListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentQueueWhiteListRequest) ProtoMessage() {}

func (x *GetAgentQueueWhiteListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentQueueWhiteListRequest.ProtoReflect.Descriptor instead.
func (*GetAgentQueueWhiteListRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{38}
}

type GetAgentQueueWhiteListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*GetAgentQueueWhiteListItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetAgentQueueWhiteListReply) Reset() {
	*x = GetAgentQueueWhiteListReply{}
	mi := &file_webagent_agent_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentQueueWhiteListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentQueueWhiteListReply) ProtoMessage() {}

func (x *GetAgentQueueWhiteListReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentQueueWhiteListReply.ProtoReflect.Descriptor instead.
func (*GetAgentQueueWhiteListReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{39}
}

func (x *GetAgentQueueWhiteListReply) GetItems() []*GetAgentQueueWhiteListItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetAgentQueueWhiteListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID     int64  `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"`
	UserName   string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName,omitempty"`
	UserAvatar string `protobuf:"bytes,3,opt,name=userAvatar,proto3" json:"userAvatar,omitempty"`
}

func (x *GetAgentQueueWhiteListItem) Reset() {
	*x = GetAgentQueueWhiteListItem{}
	mi := &file_webagent_agent_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentQueueWhiteListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentQueueWhiteListItem) ProtoMessage() {}

func (x *GetAgentQueueWhiteListItem) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentQueueWhiteListItem.ProtoReflect.Descriptor instead.
func (*GetAgentQueueWhiteListItem) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{40}
}

func (x *GetAgentQueueWhiteListItem) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *GetAgentQueueWhiteListItem) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *GetAgentQueueWhiteListItem) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

type UpdateAgentSortRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*UpdateAgentSortRequestItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *UpdateAgentSortRequest) Reset() {
	*x = UpdateAgentSortRequest{}
	mi := &file_webagent_agent_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAgentSortRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentSortRequest) ProtoMessage() {}

func (x *UpdateAgentSortRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentSortRequest.ProtoReflect.Descriptor instead.
func (*UpdateAgentSortRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{41}
}

func (x *UpdateAgentSortRequest) GetItems() []*UpdateAgentSortRequestItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type UpdateAgentSortRequestItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentID int64 `protobuf:"varint,1,opt,name=agentID,proto3" json:"agentID,omitempty"`
	Index   int64 `protobuf:"varint,2,opt,name=index,proto3" json:"index,omitempty"`
}

func (x *UpdateAgentSortRequestItem) Reset() {
	*x = UpdateAgentSortRequestItem{}
	mi := &file_webagent_agent_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAgentSortRequestItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentSortRequestItem) ProtoMessage() {}

func (x *UpdateAgentSortRequestItem) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentSortRequestItem.ProtoReflect.Descriptor instead.
func (*UpdateAgentSortRequestItem) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{42}
}

func (x *UpdateAgentSortRequestItem) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *UpdateAgentSortRequestItem) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

type UpdateAgentSortReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAgentSortReply) Reset() {
	*x = UpdateAgentSortReply{}
	mi := &file_webagent_agent_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAgentSortReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAgentSortReply) ProtoMessage() {}

func (x *UpdateAgentSortReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAgentSortReply.ProtoReflect.Descriptor instead.
func (*UpdateAgentSortReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{43}
}

type TransferAgentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentIDs   []int64 `protobuf:"varint,1,rep,packed,name=agentIDs,proto3" json:"agentIDs,omitempty"`
	NewOwnerID int64   `protobuf:"varint,2,opt,name=newOwnerID,proto3" json:"newOwnerID,omitempty"`
}

func (x *TransferAgentRequest) Reset() {
	*x = TransferAgentRequest{}
	mi := &file_webagent_agent_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransferAgentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferAgentRequest) ProtoMessage() {}

func (x *TransferAgentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferAgentRequest.ProtoReflect.Descriptor instead.
func (*TransferAgentRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{44}
}

func (x *TransferAgentRequest) GetAgentIDs() []int64 {
	if x != nil {
		return x.AgentIDs
	}
	return nil
}

func (x *TransferAgentRequest) GetNewOwnerID() int64 {
	if x != nil {
		return x.NewOwnerID
	}
	return 0
}

type TransferAgentReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TransferAgentReply) Reset() {
	*x = TransferAgentReply{}
	mi := &file_webagent_agent_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransferAgentReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferAgentReply) ProtoMessage() {}

func (x *TransferAgentReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferAgentReply.ProtoReflect.Descriptor instead.
func (*TransferAgentReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{45}
}

type CheckQuestionSecurityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question      string                                   `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	AgentID       int64                                    `protobuf:"varint,2,opt,name=agentID,proto3" json:"agentID,omitempty"`
	UploadedFiles []*CheckQuestionSecurityRequest_FileInfo `protobuf:"bytes,3,rep,name=uploadedFiles,proto3" json:"uploadedFiles,omitempty"`
	ChatID        int64                                    `protobuf:"varint,4,opt,name=chatID,proto3" json:"chatID,omitempty"`
	PcName        string                                   `protobuf:"bytes,5,opt,name=pcName,proto3" json:"pcName,omitempty"`
}

func (x *CheckQuestionSecurityRequest) Reset() {
	*x = CheckQuestionSecurityRequest{}
	mi := &file_webagent_agent_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckQuestionSecurityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckQuestionSecurityRequest) ProtoMessage() {}

func (x *CheckQuestionSecurityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckQuestionSecurityRequest.ProtoReflect.Descriptor instead.
func (*CheckQuestionSecurityRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{46}
}

func (x *CheckQuestionSecurityRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *CheckQuestionSecurityRequest) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *CheckQuestionSecurityRequest) GetUploadedFiles() []*CheckQuestionSecurityRequest_FileInfo {
	if x != nil {
		return x.UploadedFiles
	}
	return nil
}

func (x *CheckQuestionSecurityRequest) GetChatID() int64 {
	if x != nil {
		return x.ChatID
	}
	return 0
}

func (x *CheckQuestionSecurityRequest) GetPcName() string {
	if x != nil {
		return x.PcName
	}
	return ""
}

type CheckQuestionSecurityReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 风险级别 1.低危 2.中危 3.高危
	RiskLevel int64 `protobuf:"varint,1,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	// 1: 阻断 2: 警告
	HitAction int64 `protobuf:"varint,2,opt,name=hitAction,proto3" json:"hitAction,omitempty"`
	// 命中策略后的回复
	HitResponse string `protobuf:"bytes,3,opt,name=hitResponse,proto3" json:"hitResponse,omitempty"`
	// 保存的chatItemID
	ChatItemID int64 `protobuf:"varint,4,opt,name=chatItemID,proto3" json:"chatItemID,omitempty"`
}

func (x *CheckQuestionSecurityReply) Reset() {
	*x = CheckQuestionSecurityReply{}
	mi := &file_webagent_agent_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckQuestionSecurityReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckQuestionSecurityReply) ProtoMessage() {}

func (x *CheckQuestionSecurityReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckQuestionSecurityReply.ProtoReflect.Descriptor instead.
func (*CheckQuestionSecurityReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{47}
}

func (x *CheckQuestionSecurityReply) GetRiskLevel() int64 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *CheckQuestionSecurityReply) GetHitAction() int64 {
	if x != nil {
		return x.HitAction
	}
	return 0
}

func (x *CheckQuestionSecurityReply) GetHitResponse() string {
	if x != nil {
		return x.HitResponse
	}
	return ""
}

func (x *CheckQuestionSecurityReply) GetChatItemID() int64 {
	if x != nil {
		return x.ChatItemID
	}
	return 0
}

type PageAgentSecurityLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int64 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	// 1.阻断 2.警告
	HitAction []int64 `protobuf:"varint,3,rep,packed,name=hitAction,proto3" json:"hitAction,omitempty"`
	// 1. 低风险 2. 中风险 3. 高风险
	RiskLevel []int64                `protobuf:"varint,4,rep,packed,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	UserName  string                 `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName,omitempty"`
	DeptName  string                 `protobuf:"bytes,6,opt,name=deptName,proto3" json:"deptName,omitempty"`
	StartTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *PageAgentSecurityLogsRequest) Reset() {
	*x = PageAgentSecurityLogsRequest{}
	mi := &file_webagent_agent_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageAgentSecurityLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageAgentSecurityLogsRequest) ProtoMessage() {}

func (x *PageAgentSecurityLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageAgentSecurityLogsRequest.ProtoReflect.Descriptor instead.
func (*PageAgentSecurityLogsRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{48}
}

func (x *PageAgentSecurityLogsRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PageAgentSecurityLogsRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageAgentSecurityLogsRequest) GetHitAction() []int64 {
	if x != nil {
		return x.HitAction
	}
	return nil
}

func (x *PageAgentSecurityLogsRequest) GetRiskLevel() []int64 {
	if x != nil {
		return x.RiskLevel
	}
	return nil
}

func (x *PageAgentSecurityLogsRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *PageAgentSecurityLogsRequest) GetDeptName() string {
	if x != nil {
		return x.DeptName
	}
	return ""
}

func (x *PageAgentSecurityLogsRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *PageAgentSecurityLogsRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type PageAgentSecurityLogsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total   int64                             `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Records []*PageAgentSecurityLogsReplyItem `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *PageAgentSecurityLogsReply) Reset() {
	*x = PageAgentSecurityLogsReply{}
	mi := &file_webagent_agent_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageAgentSecurityLogsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageAgentSecurityLogsReply) ProtoMessage() {}

func (x *PageAgentSecurityLogsReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageAgentSecurityLogsReply.ProtoReflect.Descriptor instead.
func (*PageAgentSecurityLogsReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{49}
}

func (x *PageAgentSecurityLogsReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PageAgentSecurityLogsReply) GetRecords() []*PageAgentSecurityLogsReplyItem {
	if x != nil {
		return x.Records
	}
	return nil
}

type PageAgentSecurityLogsReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 1. 低风险 2. 中风险 3. 高风险
	RiskLevel int64 `protobuf:"varint,2,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	// 1.阻断 2.警告
	HitAction  int64  `protobuf:"varint,3,opt,name=hitAction,proto3" json:"hitAction,omitempty"`
	UserID     int64  `protobuf:"varint,4,opt,name=userID,proto3" json:"userID,omitempty"`
	UserName   string `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName,omitempty"`
	DeptName   string `protobuf:"bytes,6,opt,name=deptName,proto3" json:"deptName,omitempty"`
	UserAvatar string `protobuf:"bytes,7,opt,name=userAvatar,proto3" json:"userAvatar,omitempty"`
	AgentID    int64  `protobuf:"varint,8,opt,name=agentID,proto3" json:"agentID,omitempty"`
	AgentName  string `protobuf:"bytes,9,opt,name=agentName,proto3" json:"agentName,omitempty"`
	// 1. 智能问答
	ActionCategory int64                  `protobuf:"varint,10,opt,name=actionCategory,proto3" json:"actionCategory,omitempty"`
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	PcName         string                 `protobuf:"bytes,12,opt,name=pcName,proto3" json:"pcName,omitempty"`
	AgentAvatar    string                 `protobuf:"bytes,13,opt,name=agentAvatar,proto3" json:"agentAvatar,omitempty"`
}

func (x *PageAgentSecurityLogsReplyItem) Reset() {
	*x = PageAgentSecurityLogsReplyItem{}
	mi := &file_webagent_agent_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageAgentSecurityLogsReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageAgentSecurityLogsReplyItem) ProtoMessage() {}

func (x *PageAgentSecurityLogsReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageAgentSecurityLogsReplyItem.ProtoReflect.Descriptor instead.
func (*PageAgentSecurityLogsReplyItem) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{50}
}

func (x *PageAgentSecurityLogsReplyItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PageAgentSecurityLogsReplyItem) GetRiskLevel() int64 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *PageAgentSecurityLogsReplyItem) GetHitAction() int64 {
	if x != nil {
		return x.HitAction
	}
	return 0
}

func (x *PageAgentSecurityLogsReplyItem) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *PageAgentSecurityLogsReplyItem) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *PageAgentSecurityLogsReplyItem) GetDeptName() string {
	if x != nil {
		return x.DeptName
	}
	return ""
}

func (x *PageAgentSecurityLogsReplyItem) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *PageAgentSecurityLogsReplyItem) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *PageAgentSecurityLogsReplyItem) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *PageAgentSecurityLogsReplyItem) GetActionCategory() int64 {
	if x != nil {
		return x.ActionCategory
	}
	return 0
}

func (x *PageAgentSecurityLogsReplyItem) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PageAgentSecurityLogsReplyItem) GetPcName() string {
	if x != nil {
		return x.PcName
	}
	return ""
}

func (x *PageAgentSecurityLogsReplyItem) GetAgentAvatar() string {
	if x != nil {
		return x.AgentAvatar
	}
	return ""
}

type GetAgentSecurityLogsCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetAgentSecurityLogsCountRequest) Reset() {
	*x = GetAgentSecurityLogsCountRequest{}
	mi := &file_webagent_agent_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentSecurityLogsCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentSecurityLogsCountRequest) ProtoMessage() {}

func (x *GetAgentSecurityLogsCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentSecurityLogsCountRequest.ProtoReflect.Descriptor instead.
func (*GetAgentSecurityLogsCountRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{51}
}

type GetAgentSecurityLogsCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HighRiskCount   int64 `protobuf:"varint,1,opt,name=highRiskCount,proto3" json:"highRiskCount,omitempty"`     // 高风险数量
	MediumRiskCount int64 `protobuf:"varint,2,opt,name=mediumRiskCount,proto3" json:"mediumRiskCount,omitempty"` // 中风险数量
	LowRiskCount    int64 `protobuf:"varint,3,opt,name=lowRiskCount,proto3" json:"lowRiskCount,omitempty"`       // 低风险数量
	BlockedCount    int64 `protobuf:"varint,4,opt,name=blockedCount,proto3" json:"blockedCount,omitempty"`       // 阻断数量
	WarningCount    int64 `protobuf:"varint,5,opt,name=warningCount,proto3" json:"warningCount,omitempty"`       // 警告数量
}

func (x *GetAgentSecurityLogsCountReply) Reset() {
	*x = GetAgentSecurityLogsCountReply{}
	mi := &file_webagent_agent_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentSecurityLogsCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentSecurityLogsCountReply) ProtoMessage() {}

func (x *GetAgentSecurityLogsCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentSecurityLogsCountReply.ProtoReflect.Descriptor instead.
func (*GetAgentSecurityLogsCountReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{52}
}

func (x *GetAgentSecurityLogsCountReply) GetHighRiskCount() int64 {
	if x != nil {
		return x.HighRiskCount
	}
	return 0
}

func (x *GetAgentSecurityLogsCountReply) GetMediumRiskCount() int64 {
	if x != nil {
		return x.MediumRiskCount
	}
	return 0
}

func (x *GetAgentSecurityLogsCountReply) GetLowRiskCount() int64 {
	if x != nil {
		return x.LowRiskCount
	}
	return 0
}

func (x *GetAgentSecurityLogsCountReply) GetBlockedCount() int64 {
	if x != nil {
		return x.BlockedCount
	}
	return 0
}

func (x *GetAgentSecurityLogsCountReply) GetWarningCount() int64 {
	if x != nil {
		return x.WarningCount
	}
	return 0
}

type GetAgentSecurityLogDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *GetAgentSecurityLogDetailRequest) Reset() {
	*x = GetAgentSecurityLogDetailRequest{}
	mi := &file_webagent_agent_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentSecurityLogDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentSecurityLogDetailRequest) ProtoMessage() {}

func (x *GetAgentSecurityLogDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentSecurityLogDetailRequest.ProtoReflect.Descriptor instead.
func (*GetAgentSecurityLogDetailRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{53}
}

func (x *GetAgentSecurityLogDetailRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAgentSecurityLogDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 1. 低风险 2. 中风险 3. 高风险
	RiskLevel int64 `protobuf:"varint,2,opt,name=riskLevel,proto3" json:"riskLevel,omitempty"`
	// 1.阻断 2.警告
	HitAction   int64  `protobuf:"varint,3,opt,name=hitAction,proto3" json:"hitAction,omitempty"`
	UserID      int64  `protobuf:"varint,4,opt,name=userID,proto3" json:"userID,omitempty"`
	UserName    string `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName,omitempty"`
	DeptName    string `protobuf:"bytes,6,opt,name=deptName,proto3" json:"deptName,omitempty"`
	UserAvatar  string `protobuf:"bytes,7,opt,name=userAvatar,proto3" json:"userAvatar,omitempty"`
	AgentID     int64  `protobuf:"varint,8,opt,name=agentID,proto3" json:"agentID,omitempty"`
	AgentName   string `protobuf:"bytes,9,opt,name=agentName,proto3" json:"agentName,omitempty"`
	AgentAvatar string `protobuf:"bytes,16,opt,name=agentAvatar,proto3" json:"agentAvatar,omitempty"`
	// 1. 智能问答
	ActionCategory   int64                  `protobuf:"varint,10,opt,name=actionCategory,proto3" json:"actionCategory,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	PcName           string                 `protobuf:"bytes,12,opt,name=pcName,proto3" json:"pcName,omitempty"`
	Question         string                 `protobuf:"bytes,13,opt,name=question,proto3" json:"question,omitempty"`
	UploadedFiles    []string               `protobuf:"bytes,14,rep,name=uploadedFiles,proto3" json:"uploadedFiles,omitempty"`       // 上传的文件列表
	SecurityPolicies []*SecurityPolicy      `protobuf:"bytes,15,rep,name=securityPolicies,proto3" json:"securityPolicies,omitempty"` // 命中的安全策略
	AgentDescription string                 `protobuf:"bytes,17,opt,name=agentDescription,proto3" json:"agentDescription,omitempty"` // 智能体描述
}

func (x *GetAgentSecurityLogDetailReply) Reset() {
	*x = GetAgentSecurityLogDetailReply{}
	mi := &file_webagent_agent_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAgentSecurityLogDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAgentSecurityLogDetailReply) ProtoMessage() {}

func (x *GetAgentSecurityLogDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAgentSecurityLogDetailReply.ProtoReflect.Descriptor instead.
func (*GetAgentSecurityLogDetailReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{54}
}

func (x *GetAgentSecurityLogDetailReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAgentSecurityLogDetailReply) GetRiskLevel() int64 {
	if x != nil {
		return x.RiskLevel
	}
	return 0
}

func (x *GetAgentSecurityLogDetailReply) GetHitAction() int64 {
	if x != nil {
		return x.HitAction
	}
	return 0
}

func (x *GetAgentSecurityLogDetailReply) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *GetAgentSecurityLogDetailReply) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *GetAgentSecurityLogDetailReply) GetDeptName() string {
	if x != nil {
		return x.DeptName
	}
	return ""
}

func (x *GetAgentSecurityLogDetailReply) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *GetAgentSecurityLogDetailReply) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *GetAgentSecurityLogDetailReply) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *GetAgentSecurityLogDetailReply) GetAgentAvatar() string {
	if x != nil {
		return x.AgentAvatar
	}
	return ""
}

func (x *GetAgentSecurityLogDetailReply) GetActionCategory() int64 {
	if x != nil {
		return x.ActionCategory
	}
	return 0
}

func (x *GetAgentSecurityLogDetailReply) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *GetAgentSecurityLogDetailReply) GetPcName() string {
	if x != nil {
		return x.PcName
	}
	return ""
}

func (x *GetAgentSecurityLogDetailReply) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *GetAgentSecurityLogDetailReply) GetUploadedFiles() []string {
	if x != nil {
		return x.UploadedFiles
	}
	return nil
}

func (x *GetAgentSecurityLogDetailReply) GetSecurityPolicies() []*SecurityPolicy {
	if x != nil {
		return x.SecurityPolicies
	}
	return nil
}

func (x *GetAgentSecurityLogDetailReply) GetAgentDescription() string {
	if x != nil {
		return x.AgentDescription
	}
	return ""
}

type GetUserAgentsAndKnowledgeBasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID int64 `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"`
}

func (x *GetUserAgentsAndKnowledgeBasesRequest) Reset() {
	*x = GetUserAgentsAndKnowledgeBasesRequest{}
	mi := &file_webagent_agent_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserAgentsAndKnowledgeBasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAgentsAndKnowledgeBasesRequest) ProtoMessage() {}

func (x *GetUserAgentsAndKnowledgeBasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAgentsAndKnowledgeBasesRequest.ProtoReflect.Descriptor instead.
func (*GetUserAgentsAndKnowledgeBasesRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{55}
}

func (x *GetUserAgentsAndKnowledgeBasesRequest) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

type GetUserAgentsAndKnowledgeBasesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Agents         []*GetUserAgentsAndKnowledgeBasesReply_AgentInfo         `protobuf:"bytes,1,rep,name=agents,proto3" json:"agents,omitempty"`
	KnowledgeBases []*GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo `protobuf:"bytes,2,rep,name=knowledgeBases,proto3" json:"knowledgeBases,omitempty"`
}

func (x *GetUserAgentsAndKnowledgeBasesReply) Reset() {
	*x = GetUserAgentsAndKnowledgeBasesReply{}
	mi := &file_webagent_agent_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserAgentsAndKnowledgeBasesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAgentsAndKnowledgeBasesReply) ProtoMessage() {}

func (x *GetUserAgentsAndKnowledgeBasesReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAgentsAndKnowledgeBasesReply.ProtoReflect.Descriptor instead.
func (*GetUserAgentsAndKnowledgeBasesReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{56}
}

func (x *GetUserAgentsAndKnowledgeBasesReply) GetAgents() []*GetUserAgentsAndKnowledgeBasesReply_AgentInfo {
	if x != nil {
		return x.Agents
	}
	return nil
}

func (x *GetUserAgentsAndKnowledgeBasesReply) GetKnowledgeBases() []*GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo {
	if x != nil {
		return x.KnowledgeBases
	}
	return nil
}

type GenerateQuestionOptimizationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question string `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
}

func (x *GenerateQuestionOptimizationRequest) Reset() {
	*x = GenerateQuestionOptimizationRequest{}
	mi := &file_webagent_agent_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateQuestionOptimizationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateQuestionOptimizationRequest) ProtoMessage() {}

func (x *GenerateQuestionOptimizationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateQuestionOptimizationRequest.ProtoReflect.Descriptor instead.
func (*GenerateQuestionOptimizationRequest) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{57}
}

func (x *GenerateQuestionOptimizationRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

type GenerateQuestionOptimizationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Questions []string `protobuf:"bytes,1,rep,name=questions,proto3" json:"questions,omitempty"`
}

func (x *GenerateQuestionOptimizationReply) Reset() {
	*x = GenerateQuestionOptimizationReply{}
	mi := &file_webagent_agent_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateQuestionOptimizationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateQuestionOptimizationReply) ProtoMessage() {}

func (x *GenerateQuestionOptimizationReply) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateQuestionOptimizationReply.ProtoReflect.Descriptor instead.
func (*GenerateQuestionOptimizationReply) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{58}
}

func (x *GenerateQuestionOptimizationReply) GetQuestions() []string {
	if x != nil {
		return x.Questions
	}
	return nil
}

type AskAgentRequest_FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID     int64  `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	Title              string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Size               int64  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	MimeType           string `protobuf:"bytes,4,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	UserID             int64  `protobuf:"varint,5,opt,name=userID,proto3" json:"userID,omitempty"`
	EntityTag          string `protobuf:"bytes,6,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag       string `protobuf:"bytes,7,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	Index              int64  `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	ChunkIndex         int64  `protobuf:"varint,9,opt,name=chunkIndex,proto3" json:"chunkIndex,omitempty"`
	FullPath           string `protobuf:"bytes,10,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
	FromChatItemID     int64  `protobuf:"varint,11,opt,name=fromChatItemID,proto3" json:"fromChatItemID,omitempty"`
	FromFileRelationID int64  `protobuf:"varint,12,opt,name=fromFileRelationID,proto3" json:"fromFileRelationID,omitempty"`
}

func (x *AskAgentRequest_FileInfo) Reset() {
	*x = AskAgentRequest_FileInfo{}
	mi := &file_webagent_agent_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskAgentRequest_FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskAgentRequest_FileInfo) ProtoMessage() {}

func (x *AskAgentRequest_FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskAgentRequest_FileInfo.ProtoReflect.Descriptor instead.
func (*AskAgentRequest_FileInfo) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{19, 0}
}

func (x *AskAgentRequest_FileInfo) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *AskAgentRequest_FileInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AskAgentRequest_FileInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *AskAgentRequest_FileInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *AskAgentRequest_FileInfo) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *AskAgentRequest_FileInfo) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *AskAgentRequest_FileInfo) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *AskAgentRequest_FileInfo) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *AskAgentRequest_FileInfo) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *AskAgentRequest_FileInfo) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *AskAgentRequest_FileInfo) GetFromChatItemID() int64 {
	if x != nil {
		return x.FromChatItemID
	}
	return 0
}

func (x *AskAgentRequest_FileInfo) GetFromFileRelationID() int64 {
	if x != nil {
		return x.FromFileRelationID
	}
	return 0
}

type AskAgentReply_FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID        int64    `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	Title                 string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Size                  int64    `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	MimeType              string   `protobuf:"bytes,4,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	UserID                int64    `protobuf:"varint,5,opt,name=userID,proto3" json:"userID,omitempty"`
	EntityTag             string   `protobuf:"bytes,6,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag          string   `protobuf:"bytes,7,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	Index                 int64    `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	ChunkIndex            int64    `protobuf:"varint,9,opt,name=chunkIndex,proto3" json:"chunkIndex,omitempty"`
	ChunkSize             int64    `protobuf:"varint,10,opt,name=chunkSize,proto3" json:"chunkSize,omitempty"`
	Images                []string `protobuf:"bytes,11,rep,name=images,proto3" json:"images,omitempty"`
	Tags                  []string `protobuf:"bytes,12,rep,name=tags,proto3" json:"tags,omitempty"`
	FullPath              string   `protobuf:"bytes,13,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
	KnowledgeBaseID       int64    `protobuf:"varint,14,opt,name=knowledgeBaseID,proto3" json:"knowledgeBaseID,omitempty"`
	KnowledgeBaseName     string   `protobuf:"bytes,15,opt,name=knowledgeBaseName,proto3" json:"knowledgeBaseName,omitempty"`
	KnowledgeBaseDataType int64    `protobuf:"varint,16,opt,name=knowledgeBaseDataType,proto3" json:"knowledgeBaseDataType,omitempty"`
	TableData             string   `protobuf:"bytes,17,opt,name=tableData,proto3" json:"tableData,omitempty"`
	ChartSchema           string   `protobuf:"bytes,18,opt,name=chartSchema,proto3" json:"chartSchema,omitempty"`
	ClassPath             string   `protobuf:"bytes,19,opt,name=classPath,proto3" json:"classPath,omitempty"`
	Level                 int64    `protobuf:"varint,20,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *AskAgentReply_FileInfo) Reset() {
	*x = AskAgentReply_FileInfo{}
	mi := &file_webagent_agent_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskAgentReply_FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskAgentReply_FileInfo) ProtoMessage() {}

func (x *AskAgentReply_FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskAgentReply_FileInfo.ProtoReflect.Descriptor instead.
func (*AskAgentReply_FileInfo) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{20, 0}
}

func (x *AskAgentReply_FileInfo) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *AskAgentReply_FileInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AskAgentReply_FileInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *AskAgentReply_FileInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *AskAgentReply_FileInfo) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *AskAgentReply_FileInfo) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *AskAgentReply_FileInfo) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *AskAgentReply_FileInfo) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *AskAgentReply_FileInfo) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *AskAgentReply_FileInfo) GetChunkSize() int64 {
	if x != nil {
		return x.ChunkSize
	}
	return 0
}

func (x *AskAgentReply_FileInfo) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *AskAgentReply_FileInfo) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *AskAgentReply_FileInfo) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *AskAgentReply_FileInfo) GetKnowledgeBaseID() int64 {
	if x != nil {
		return x.KnowledgeBaseID
	}
	return 0
}

func (x *AskAgentReply_FileInfo) GetKnowledgeBaseName() string {
	if x != nil {
		return x.KnowledgeBaseName
	}
	return ""
}

func (x *AskAgentReply_FileInfo) GetKnowledgeBaseDataType() int64 {
	if x != nil {
		return x.KnowledgeBaseDataType
	}
	return 0
}

func (x *AskAgentReply_FileInfo) GetTableData() string {
	if x != nil {
		return x.TableData
	}
	return ""
}

func (x *AskAgentReply_FileInfo) GetChartSchema() string {
	if x != nil {
		return x.ChartSchema
	}
	return ""
}

func (x *AskAgentReply_FileInfo) GetClassPath() string {
	if x != nil {
		return x.ClassPath
	}
	return ""
}

func (x *AskAgentReply_FileInfo) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

type QueryModelAskClassificationDistributionReply_ClassificationDistribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分类名称
	ClassificationName string `protobuf:"bytes,1,opt,name=classificationName,proto3" json:"classificationName,omitempty"`
	// 分类数量
	Count int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *QueryModelAskClassificationDistributionReply_ClassificationDistribution) Reset() {
	*x = QueryModelAskClassificationDistributionReply_ClassificationDistribution{}
	mi := &file_webagent_agent_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryModelAskClassificationDistributionReply_ClassificationDistribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryModelAskClassificationDistributionReply_ClassificationDistribution) ProtoMessage() {}

func (x *QueryModelAskClassificationDistributionReply_ClassificationDistribution) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryModelAskClassificationDistributionReply_ClassificationDistribution.ProtoReflect.Descriptor instead.
func (*QueryModelAskClassificationDistributionReply_ClassificationDistribution) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{27, 0}
}

func (x *QueryModelAskClassificationDistributionReply_ClassificationDistribution) GetClassificationName() string {
	if x != nil {
		return x.ClassificationName
	}
	return ""
}

func (x *QueryModelAskClassificationDistributionReply_ClassificationDistribution) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type QueryUploadFileTypeDistributionReply_FileTypeDistribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文件类型
	FileType string `protobuf:"bytes,1,opt,name=fileType,proto3" json:"fileType,omitempty"`
	// 文件数量
	Count int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *QueryUploadFileTypeDistributionReply_FileTypeDistribution) Reset() {
	*x = QueryUploadFileTypeDistributionReply_FileTypeDistribution{}
	mi := &file_webagent_agent_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryUploadFileTypeDistributionReply_FileTypeDistribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUploadFileTypeDistributionReply_FileTypeDistribution) ProtoMessage() {}

func (x *QueryUploadFileTypeDistributionReply_FileTypeDistribution) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUploadFileTypeDistributionReply_FileTypeDistribution.ProtoReflect.Descriptor instead.
func (*QueryUploadFileTypeDistributionReply_FileTypeDistribution) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{29, 0}
}

func (x *QueryUploadFileTypeDistributionReply_FileTypeDistribution) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *QueryUploadFileTypeDistributionReply_FileTypeDistribution) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type QueryModelAskClassificationTop10Reply_ClassificationDistribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 分类名称
	ClassificationName string `protobuf:"bytes,1,opt,name=classificationName,proto3" json:"classificationName,omitempty"`
	// 分类数量
	Count int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *QueryModelAskClassificationTop10Reply_ClassificationDistribution) Reset() {
	*x = QueryModelAskClassificationTop10Reply_ClassificationDistribution{}
	mi := &file_webagent_agent_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryModelAskClassificationTop10Reply_ClassificationDistribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryModelAskClassificationTop10Reply_ClassificationDistribution) ProtoMessage() {}

func (x *QueryModelAskClassificationTop10Reply_ClassificationDistribution) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryModelAskClassificationTop10Reply_ClassificationDistribution.ProtoReflect.Descriptor instead.
func (*QueryModelAskClassificationTop10Reply_ClassificationDistribution) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{31, 0}
}

func (x *QueryModelAskClassificationTop10Reply_ClassificationDistribution) GetClassificationName() string {
	if x != nil {
		return x.ClassificationName
	}
	return ""
}

func (x *QueryModelAskClassificationTop10Reply_ClassificationDistribution) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 员工姓名/部门名称
	Name                        string                                                              `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ClassificationDistributions []*QueryModelAskClassificationTop10Reply_ClassificationDistribution `protobuf:"bytes,2,rep,name=classificationDistributions,proto3" json:"classificationDistributions,omitempty"`
	// 总数
	Total int64 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) Reset() {
	*x = QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem{}
	mi := &file_webagent_agent_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) ProtoMessage() {
}

func (x *QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem.ProtoReflect.Descriptor instead.
func (*QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{31, 1}
}

func (x *QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) GetClassificationDistributions() []*QueryModelAskClassificationTop10Reply_ClassificationDistribution {
	if x != nil {
		return x.ClassificationDistributions
	}
	return nil
}

func (x *QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type QueryUploadFileTypeTop10Reply_FileTypeDistribution struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文件类型
	FileType string `protobuf:"bytes,1,opt,name=fileType,proto3" json:"fileType,omitempty"`
	// 文件数量
	Count int64 `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *QueryUploadFileTypeTop10Reply_FileTypeDistribution) Reset() {
	*x = QueryUploadFileTypeTop10Reply_FileTypeDistribution{}
	mi := &file_webagent_agent_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryUploadFileTypeTop10Reply_FileTypeDistribution) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUploadFileTypeTop10Reply_FileTypeDistribution) ProtoMessage() {}

func (x *QueryUploadFileTypeTop10Reply_FileTypeDistribution) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUploadFileTypeTop10Reply_FileTypeDistribution.ProtoReflect.Descriptor instead.
func (*QueryUploadFileTypeTop10Reply_FileTypeDistribution) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{33, 0}
}

func (x *QueryUploadFileTypeTop10Reply_FileTypeDistribution) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *QueryUploadFileTypeTop10Reply_FileTypeDistribution) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 员工姓名/部门名称
	Name                  string                                                `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	FileTypeDistributions []*QueryUploadFileTypeTop10Reply_FileTypeDistribution `protobuf:"bytes,2,rep,name=fileTypeDistributions,proto3" json:"fileTypeDistributions,omitempty"`
	// 总数
	Total int64 `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) Reset() {
	*x = QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem{}
	mi := &file_webagent_agent_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) ProtoMessage() {}

func (x *QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem.ProtoReflect.Descriptor instead.
func (*QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{33, 1}
}

func (x *QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) GetFileTypeDistributions() []*QueryUploadFileTypeTop10Reply_FileTypeDistribution {
	if x != nil {
		return x.FileTypeDistributions
	}
	return nil
}

func (x *QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetAllAgentInfoReply_AgentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetAllAgentInfoReply_AgentInfo) Reset() {
	*x = GetAllAgentInfoReply_AgentInfo{}
	mi := &file_webagent_agent_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllAgentInfoReply_AgentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllAgentInfoReply_AgentInfo) ProtoMessage() {}

func (x *GetAllAgentInfoReply_AgentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllAgentInfoReply_AgentInfo.ProtoReflect.Descriptor instead.
func (*GetAllAgentInfoReply_AgentInfo) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{35, 0}
}

func (x *GetAllAgentInfoReply_AgentInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetAllAgentInfoReply_AgentInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type CheckQuestionSecurityRequest_FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID int64  `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	Title          string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Size           int64  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	MimeType       string `protobuf:"bytes,4,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	UserID         int64  `protobuf:"varint,5,opt,name=userID,proto3" json:"userID,omitempty"`
	EntityTag      string `protobuf:"bytes,6,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag   string `protobuf:"bytes,7,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	Index          int64  `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	ChunkIndex     int64  `protobuf:"varint,9,opt,name=chunkIndex,proto3" json:"chunkIndex,omitempty"`
	FullPath       string `protobuf:"bytes,10,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
}

func (x *CheckQuestionSecurityRequest_FileInfo) Reset() {
	*x = CheckQuestionSecurityRequest_FileInfo{}
	mi := &file_webagent_agent_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckQuestionSecurityRequest_FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckQuestionSecurityRequest_FileInfo) ProtoMessage() {}

func (x *CheckQuestionSecurityRequest_FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckQuestionSecurityRequest_FileInfo.ProtoReflect.Descriptor instead.
func (*CheckQuestionSecurityRequest_FileInfo) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{46, 0}
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *CheckQuestionSecurityRequest_FileInfo) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

type GetUserAgentsAndKnowledgeBasesReply_AgentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AgentID     int64  `protobuf:"varint,1,opt,name=agentID,proto3" json:"agentID,omitempty"`
	AgentName   string `protobuf:"bytes,2,opt,name=agentName,proto3" json:"agentName,omitempty"`
	AgentAvatar string `protobuf:"bytes,3,opt,name=agentAvatar,proto3" json:"agentAvatar,omitempty"`
}

func (x *GetUserAgentsAndKnowledgeBasesReply_AgentInfo) Reset() {
	*x = GetUserAgentsAndKnowledgeBasesReply_AgentInfo{}
	mi := &file_webagent_agent_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserAgentsAndKnowledgeBasesReply_AgentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAgentsAndKnowledgeBasesReply_AgentInfo) ProtoMessage() {}

func (x *GetUserAgentsAndKnowledgeBasesReply_AgentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAgentsAndKnowledgeBasesReply_AgentInfo.ProtoReflect.Descriptor instead.
func (*GetUserAgentsAndKnowledgeBasesReply_AgentInfo) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{56, 0}
}

func (x *GetUserAgentsAndKnowledgeBasesReply_AgentInfo) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *GetUserAgentsAndKnowledgeBasesReply_AgentInfo) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *GetUserAgentsAndKnowledgeBasesReply_AgentInfo) GetAgentAvatar() string {
	if x != nil {
		return x.AgentAvatar
	}
	return ""
}

type GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KnowledgeBaseID   int64  `protobuf:"varint,1,opt,name=knowledgeBaseID,proto3" json:"knowledgeBaseID,omitempty"`
	KnowledgeBaseName string `protobuf:"bytes,2,opt,name=knowledgeBaseName,proto3" json:"knowledgeBaseName,omitempty"`
	// 知识库类型 1文档 2表格
	DataType int32 `protobuf:"varint,3,opt,name=dataType,proto3" json:"dataType,omitempty"`
}

func (x *GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) Reset() {
	*x = GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo{}
	mi := &file_webagent_agent_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) ProtoMessage() {}

func (x *GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_webagent_agent_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo.ProtoReflect.Descriptor instead.
func (*GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) Descriptor() ([]byte, []int) {
	return file_webagent_agent_proto_rawDescGZIP(), []int{56, 1}
}

func (x *GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) GetKnowledgeBaseID() int64 {
	if x != nil {
		return x.KnowledgeBaseID
	}
	return 0
}

func (x *GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) GetKnowledgeBaseName() string {
	if x != nil {
		return x.KnowledgeBaseName
	}
	return ""
}

func (x *GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

var File_webagent_agent_proto protoreflect.FileDescriptor

var file_webagent_agent_proto_rawDesc = []byte{
	0x0a, 0x14, 0x77, 0x65, 0x62, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x13, 0x77, 0x65,
	0x62, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xdc, 0x03, 0x0a, 0x1c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x25, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x18, 0x64,
	0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x70, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34,
	0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x18,
	0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x6f, 0x52, 0x65,
	0x66, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x6e, 0x6f,
	0x52, 0x65, 0x66, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x22, 0x7a, 0x0a, 0x1a, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x46, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xde, 0x03, 0x0a,
	0x1e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x68,
	0x61, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1e, 0x0a,
	0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1a, 0x0a,
	0x08, 0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x70,
	0x74, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x64, 0x65, 0x70, 0x74, 0x49,
	0x44, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x65, 0x66,
	0x46, 0x69, 0x65, 0x73, 0x54, 0x65, 0x78, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x66, 0x46, 0x69, 0x65, 0x73, 0x54, 0x65, 0x78, 0x74, 0x12, 0x40, 0x0a, 0x08, 0x72,
	0x65, 0x66, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73, 0x6b,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x72, 0x65, 0x66, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xe1, 0x03,
	0x0a, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x6c, 0x63, 0x6f,
	0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65,
	0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x12,
	0x2c, 0x0a, 0x12, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x46, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0x2e, 0x0a, 0x08, 0x44, 0x65, 0x70,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa8, 0x03, 0x0a, 0x0e, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x26, 0x0a, 0x0e, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x69, 0x73,
	0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x12, 0x1c, 0x0a, 0x09,
	0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x69,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x68, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x38, 0x0a, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x12, 0x24,
	0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x42, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb6, 0x07, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x6c, 0x63, 0x6f,
	0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65,
	0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x6c, 0x6c,
	0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x07, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x12,
	0x2c, 0x0a, 0x12, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73,
	0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x73, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x28, 0x0a, 0x0f, 0x74,
	0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x18, 0x14,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x49, 0x44, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x6c,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x48, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x16, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69,
	0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x18, 0x17, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x65, 0x6d, 0x61, 0x6e,
	0x74, 0x69, 0x63, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x19,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65,
	0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65,
	0x64, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0xf8, 0x03,
	0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x12,
	0x21, 0x0a, 0x0c, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6d, 0x73, 0x67, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4d,
	0x73, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x26,
	0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x65, 0x70,
	0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65,
	0x54, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x73, 0x68,
	0x6f, 0x77, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x22, 0xc6, 0x07, 0x0a, 0x12, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x77,
	0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x12, 0x21, 0x0a, 0x0c,
	0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x69,
	0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74,
	0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x76, 0x69,
	0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x44,
	0x65, 0x70, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f,
	0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73,
	0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65,
	0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x21, 0x0a,
	0x0c, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x6f, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x12, 0x1a, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x28, 0x0a, 0x0f,
	0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x12, 0x48, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x10,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73,
	0x12, 0x3a, 0x0a, 0x18, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x44, 0x73, 0x18, 0x15, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x18, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x44, 0x73, 0x12, 0x1e, 0x0a, 0x0a,
	0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0d,
	0x73, 0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43, 0x61, 0x63, 0x68, 0x65, 0x18, 0x17, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43, 0x61, 0x63,
	0x68, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x18, 0x18, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x12,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x19, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x61, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6c,
	0x69, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x1a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x22, 0xf8, 0x03, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x6d, 0x73,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65,
	0x4d, 0x73, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f,
	0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x76, 0x69, 0x73, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69,
	0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f,
	0x5f, 0x64, 0x65, 0x70, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x76, 0x69, 0x73,
	0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x1d, 0x0a,
	0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2c,
	0x0a, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x22, 0x24, 0x0a, 0x12,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x12, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2f, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x03, 0x69, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01,
	0x10, 0x64, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22, 0x48, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x37, 0x0a, 0x06, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x06, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x73, 0x22, 0x99, 0x0d, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x6d, 0x73,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65,
	0x4d, 0x73, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f,
	0x6d, 0x73, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x4d, 0x73, 0x67, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x12, 0x27, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x76, 0x69,
	0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f,
	0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18,
	0x08, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f,
	0x74, 0x6f, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x76,
	0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x12, 0x2c, 0x0a, 0x12,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x38, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x12, 0x3b, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x70,
	0x74, 0x73, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x74, 0x73, 0x12, 0x2c,
	0x0a, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x18, 0x16, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x44, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x49, 0x44, 0x12, 0x2e, 0x0a, 0x13, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x3e, 0x0a, 0x1a, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72,
	0x63, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x72, 0x6f, 0x6c, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72,
	0x6f, 0x6c, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x68,
	0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x68,
	0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x28, 0x0a, 0x0f, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x18, 0x20, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0f, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44,
	0x12, 0x2c, 0x0a, 0x11, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x68, 0x69,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30,
	0x0a, 0x13, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x22, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x74, 0x68, 0x69,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x24, 0x0a, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x49, 0x44, 0x18, 0x24, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x49, 0x44, 0x12, 0x32, 0x0a, 0x14, 0x74, 0x68, 0x69, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x25, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x10,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73,
	0x18, 0x26, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x46, 0x69, 0x6c, 0x65, 0x18, 0x27, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x6d, 0x61, 0x6e, 0x74,
	0x69, 0x63, 0x43, 0x61, 0x63, 0x68, 0x65, 0x18, 0x28, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73,
	0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x2c, 0x0a, 0x12,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x18, 0x29, 0x20, 0x03, 0x28, 0x03, 0x52, 0x10, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x61, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12, 0x41, 0x0a, 0x10, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x2a,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22, 0xf7, 0x01,
	0x0a, 0x10, 0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x70, 0x61,
	0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x22, 0x0a, 0x0c, 0x73, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x68, 0x6f, 0x77, 0x4f, 0x6e, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x73, 0x4d, 0x69, 0x6e, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x69, 0x73, 0x4d, 0x69, 0x6e, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x74,
	0x65, 0x67, 0x6f, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x62, 0x0a, 0x0e, 0x50, 0x61, 0x67, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12,
	0x3a, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xf4, 0x0a, 0x0a, 0x12,
	0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1d,
	0x0a, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a,
	0x0b, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x4d, 0x73, 0x67, 0x12, 0x21,
	0x0a, 0x0c, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x6d, 0x73, 0x67, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4d, 0x73,
	0x67, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x27,
	0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62,
	0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x08, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x12,
	0x26, 0x0a, 0x0f, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x64, 0x65,
	0x70, 0x74, 0x18, 0x12, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c,
	0x65, 0x54, 0x6f, 0x44, 0x65, 0x70, 0x74, 0x12, 0x2c, 0x0a, 0x12, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x10, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x49, 0x64, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1b, 0x0a,
	0x09, 0x69, 0x73, 0x5f, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x69, 0x73, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73,
	0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x69, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x3b, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x18, 0x13, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c,
	0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x0a, 0x0d,
	0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x70, 0x74, 0x73, 0x18, 0x14, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c, 0x76, 0x69, 0x73,
	0x69, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x70, 0x74, 0x73, 0x12, 0x2c, 0x0a, 0x11, 0x73, 0x68, 0x6f,
	0x77, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x16,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x2c, 0x0a, 0x11, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x61, 0x6e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x14, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x45,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x14, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b,
	0x69, 0x6e, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c,
	0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46,
	0x69, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x6d, 0x61, 0x6e, 0x74, 0x69, 0x63, 0x43,
	0x61, 0x63, 0x68, 0x65, 0x18, 0x20, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x73, 0x65, 0x6d, 0x61,
	0x6e, 0x74, 0x69, 0x63, 0x43, 0x61, 0x63, 0x68, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x21, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x10, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x61, 0x62, 0x6c,
	0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x22, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x61, 0x62, 0x6c,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x74, 0x6f, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x23, 0x20, 0x03,
	0x28, 0x03, 0x52, 0x10, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x6f,
	0x55, 0x73, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55,
	0x72, 0x6c, 0x22, 0xd3, 0x06, 0x0a, 0x0f, 0x41, 0x73, 0x6b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x68, 0x61, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x68, 0x61,
	0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x3c, 0x0a,
	0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x73, 0x6b, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x4d, 0x75, 0x6c, 0x74, 0x69,
	0x52, 0x6f, 0x75, 0x6e, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x4d,
	0x75, 0x6c, 0x74, 0x69, 0x52, 0x6f, 0x75, 0x6e, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x26, 0x0a, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x68, 0x69,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x68, 0x69,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x49, 0x44, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x44, 0x12, 0x44, 0x0a, 0x1d, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x65, 0x53, 0x68, 0x65, 0x65, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x49, 0x44, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x1d, 0x64, 0x65,
	0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x65, 0x53, 0x68, 0x65, 0x65, 0x74, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x11, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x65, 0x78, 0x69, 0x73, 0x74, 0x69, 0x6e, 0x67,
	0x54, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x1a, 0xfc, 0x02, 0x0a, 0x08, 0x46, 0x69,
	0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72,
	0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68,
	0x12, 0x26, 0x0a, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x49, 0x44, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x68,
	0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x44, 0x12, 0x2e, 0x0a, 0x12, 0x66, 0x72, 0x6f, 0x6d,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x12, 0x66, 0x72, 0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0xf8, 0x06, 0x0a, 0x0d, 0x41, 0x73, 0x6b,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e,
	0x73, 0x77, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77,
	0x65, 0x72, 0x12, 0x3a, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x41, 0x73, 0x6b, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x46,
	0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a, 0x0a,
	0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c,
	0x64, 0x65, 0x62, 0x75, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x64, 0x65, 0x62, 0x75, 0x67, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74,
	0x1a, 0xf0, 0x04, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a,
	0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61,
	0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x0a, 0x09, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74,
	0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x28, 0x0a, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x49, 0x44, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x11, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x68, 0x61, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12,
	0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x12, 0x14, 0x0a,
	0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6c, 0x65,
	0x76, 0x65, 0x6c, 0x22, 0x3b, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x22, 0x57, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3d, 0x0a, 0x07, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65,
	0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x52, 0x07, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x73, 0x22, 0x96, 0x01, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x25, 0x0a,
	0x0e, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x5f,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x63, 0x6c, 0x69, 0x63, 0x6b, 0x65, 0x64, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55,
	0x72, 0x6c, 0x22, 0x65, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49,
	0x44, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0x47, 0x0a, 0x1f, 0x47, 0x65, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24, 0x0a, 0x0d,
	0x68, 0x61, 0x73, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x68, 0x61, 0x73, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x22, 0xbe, 0x01, 0x0a, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0xac, 0x02, 0x0a, 0x2c, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x97, 0x01, 0x0a, 0x1b, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x55, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x1b, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x62,
	0x0a, 0x1a, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x0a, 0x12,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0xb6, 0x01, 0x0a, 0x26, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xef, 0x01, 0x0a, 0x24,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x7d, 0x0a, 0x15, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x66, 0x69,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x1a, 0x48, 0x0a, 0x14, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44,
	0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xd1, 0x01,
	0x0a, 0x27, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x70,
	0x31, 0x30, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x6f, 0x70,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x6f, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xeb, 0x03, 0x0a, 0x25, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x73, 0x0a, 0x05, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x5d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61,
	0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x70, 0x31, 0x30,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x1a, 0x62, 0x0a, 0x1a, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e,
	0x0a, 0x12, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xe8, 0x01, 0x0a, 0x29, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x90, 0x01, 0x0a, 0x1b, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1b, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0xc9, 0x01, 0x0a, 0x1f, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46,
	0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x6f, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x96, 0x03, 0x0a, 0x1d,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x63, 0x0a,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54,
	0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70,
	0x31, 0x30, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x1a, 0x48, 0x0a, 0x14, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0xc5, 0x01, 0x0a,
	0x21, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x76, 0x0a, 0x15, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72,
	0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x22, 0x37, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8d, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x44, 0x0a, 0x06, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x2f, 0x0a, 0x09,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x3a, 0x0a,
	0x1e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x57,
	0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x18, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03,
	0x52, 0x07, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x73, 0x22, 0x1e, 0x0a, 0x1c, 0x53, 0x61, 0x76,
	0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5d, 0x0a, 0x1b, 0x47, 0x65,
	0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3e, 0x0a, 0x05, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x70, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12,
	0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75,
	0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0x58, 0x0a, 0x16, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3e, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53,
	0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x4c, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x14, 0x0a,
	0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x22, 0x16, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x52, 0x0a, 0x14, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x73, 0x12,
	0x1e, 0x0a, 0x0a, 0x6e, 0x65, 0x77, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x6e, 0x65, 0x77, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x22,
	0x14, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x8f, 0x04, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x59, 0x0a, 0x0d, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65,
	0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0d, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73,
	0x12, 0x16, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x63, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x4e, 0x61, 0x6d, 0x65,
	0x1a, 0xa4, 0x02, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a,
	0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61,
	0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x22, 0x9a, 0x01, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74,
	0x65, 0x6d, 0x49, 0x44, 0x22, 0xc1, 0x02, 0x0a, 0x1c, 0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x7a, 0x0a, 0x1a, 0x50, 0x61, 0x67, 0x65,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x46, 0x0a, 0x07,
	0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f,
	0x67, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x22, 0xb0, 0x03, 0x0a, 0x1e, 0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x70, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x1c, 0x0a,
	0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x70, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0x22, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xdc, 0x01, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x4c, 0x6f, 0x67, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x24,
	0x0a, 0x0d, 0x68, 0x69, 0x67, 0x68, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x68, 0x69, 0x67, 0x68, 0x52, 0x69, 0x73, 0x6b, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x65, 0x64, 0x69, 0x75, 0x6d, 0x52, 0x69,
	0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6d,
	0x65, 0x64, 0x69, 0x75, 0x6d, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x6c, 0x6f, 0x77, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6c, 0x6f, 0x77, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65,
	0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x61, 0x72, 0x6e, 0x69, 0x6e,
	0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x77, 0x61,
	0x72, 0x6e, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x32, 0x0a, 0x20, 0x47, 0x65,
	0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f,
	0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xe8,
	0x04, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x1c, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12,
	0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a,
	0x0d, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x0e,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x10, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x69, 0x65, 0x73, 0x12, 0x2a, 0x0a,
	0x10, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x3f, 0x0a, 0x25, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x6e, 0x64, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x22, 0xd8, 0x03, 0x0a, 0x23, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x6e, 0x64, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x53, 0x0a, 0x06, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x41,
	0x6e, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x06, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x6b, 0x0a, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x43, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x6e, 0x64, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x73, 0x1a, 0x65, 0x0a, 0x09, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x1a, 0x87, 0x01, 0x0a, 0x11,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x28, 0x0a, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x11, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x22, 0x41, 0x0a, 0x23, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x41, 0x0a, 0x21, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1c, 0x0a,
	0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x32, 0x8b, 0x1a, 0x0a, 0x05,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x69, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01,
	0x2a, 0x22, 0x0d, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x12, 0x6f, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x12, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x0c, 0x12, 0x0a, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x6c,
	0x6c, 0x12, 0x69, 0x0a, 0x0b, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x12, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x69, 0x0a, 0x0b,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x18, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x5a, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x12, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0c, 0x12, 0x0a, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f,
	0x67, 0x65, 0x74, 0x12, 0x5e, 0x0a, 0x09, 0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x13,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0d, 0x12, 0x0b, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70,
	0x61, 0x67, 0x65, 0x12, 0x80, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x73, 0x12, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12,
	0x15, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x73, 0x12, 0x9b, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x12, 0x15, 0x2f,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x96, 0x01, 0x0a, 0x15, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22,
	0x1c, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x68, 0x61, 0x74, 0x50, 0x61, 0x67, 0x65, 0x12, 0xd6, 0x01,
	0x0a, 0x27, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65,
	0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x12, 0x29, 0x2f, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69,
	0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xb6, 0x01, 0x0a, 0x1f, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69,
	0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0xba, 0x01, 0x0a, 0x20, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73,
	0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x70, 0x31, 0x30, 0x12, 0x35, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73,
	0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x12, 0x22, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x73, 0x6b, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x12, 0x9a, 0x01, 0x0a,
	0x18, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x31,
	0x30, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x51, 0x75, 0x65, 0x72, 0x79, 0x55, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x31, 0x30,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x12, 0x1a, 0x2f,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x54, 0x6f, 0x70, 0x31, 0x30, 0x12, 0x96, 0x01, 0x0a, 0x17, 0x53, 0x61,
	0x76, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75,
	0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x53, 0x61, 0x76, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x90, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x51,
	0x75, 0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x51, 0x75, 0x65, 0x75, 0x65, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x12, 0x16, 0x2f,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x77, 0x68, 0x69, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x7a, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a, 0x22, 0x12, 0x2f,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x73, 0x6f, 0x72,
	0x74, 0x12, 0x71, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x12, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62,
	0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14,
	0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x12, 0x96, 0x01, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x2a,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22,
	0x1c, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x12, 0x8b, 0x01,
	0x0a, 0x15, 0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65,
	0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1c, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x16, 0x12, 0x14, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2f, 0x6c, 0x6f, 0x67, 0x73, 0x12, 0x9e, 0x01, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x4c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x12,
	0x1b, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x2f, 0x6c, 0x6f, 0x67, 0x73, 0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x9d, 0x01, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x4c, 0x6f, 0x67, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x6f, 0x67, 0x73, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c,
	0x12, 0x1a, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x2f, 0x6c, 0x6f, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xb5, 0x01, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x6e,
	0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x12,
	0x33, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x73, 0x41, 0x6e, 0x64, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x73, 0x41, 0x6e, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12,
	0x23, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x73, 0x41, 0x6e, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x73, 0x12, 0xb2, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6d, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x28, 0x3a, 0x01, 0x2a, 0x22, 0x23, 0x2f, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x67, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6d, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x50, 0x0a, 0x0c, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x01, 0x5a, 0x3e, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f,
	0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x61,
	0x69, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65,
	0x6e, 0x74, 0x3b, 0x77, 0x65, 0x62, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_webagent_agent_proto_rawDescOnce sync.Once
	file_webagent_agent_proto_rawDescData = file_webagent_agent_proto_rawDesc
)

func file_webagent_agent_proto_rawDescGZIP() []byte {
	file_webagent_agent_proto_rawDescOnce.Do(func() {
		file_webagent_agent_proto_rawDescData = protoimpl.X.CompressGZIP(file_webagent_agent_proto_rawDescData)
	})
	return file_webagent_agent_proto_rawDescData
}

var file_webagent_agent_proto_msgTypes = make([]protoimpl.MessageInfo, 71)
var file_webagent_agent_proto_goTypes = []any{
	(*InternalModelChatPageRequest)(nil),                                            // 0: api.webAgent.InternalModelChatPageRequest
	(*InternalModelChatPageReply)(nil),                                              // 1: api.webAgent.InternalModelChatPageReply
	(*InternalModelChatPageReplyItem)(nil),                                          // 2: api.webAgent.InternalModelChatPageReplyItem
	(*AgentInfo)(nil),                                                               // 3: api.webAgent.AgentInfo
	(*UserInfo)(nil),                                                                // 4: api.webAgent.UserInfo
	(*DeptInfo)(nil),                                                                // 5: api.webAgent.DeptInfo
	(*SecurityPolicy)(nil),                                                          // 6: api.webAgent.SecurityPolicy
	(*CreateAgentRequest)(nil),                                                      // 7: api.webAgent.CreateAgentRequest
	(*CreateAgentReply)(nil),                                                        // 8: api.webAgent.CreateAgentReply
	(*UpdateAgentRequest)(nil),                                                      // 9: api.webAgent.UpdateAgentRequest
	(*UpdateAgentReply)(nil),                                                        // 10: api.webAgent.UpdateAgentReply
	(*DeleteAgentRequest)(nil),                                                      // 11: api.webAgent.DeleteAgentRequest
	(*DeleteAgentReply)(nil),                                                        // 12: api.webAgent.DeleteAgentReply
	(*GetAgentRequest)(nil),                                                         // 13: api.webAgent.GetAgentRequest
	(*GetAgentReply)(nil),                                                           // 14: api.webAgent.GetAgentReply
	(*GetAgentReplyItem)(nil),                                                       // 15: api.webAgent.GetAgentReplyItem
	(*PageAgentRequest)(nil),                                                        // 16: api.webAgent.PageAgentRequest
	(*PageAgentReply)(nil),                                                          // 17: api.webAgent.PageAgentReply
	(*PageAgentReplyItem)(nil),                                                      // 18: api.webAgent.PageAgentReplyItem
	(*AskAgentRequest)(nil),                                                         // 19: api.webAgent.AskAgentRequest
	(*AskAgentReply)(nil),                                                           // 20: api.webAgent.AskAgentReply
	(*GetDefaultAvatarsRequest)(nil),                                                // 21: api.webAgent.GetDefaultAvatarsRequest
	(*GetDefaultAvatarsReply)(nil),                                                  // 22: api.webAgent.GetDefaultAvatarsReply
	(*GetDefaultAvatarItems)(nil),                                                   // 23: api.webAgent.GetDefaultAvatarItems
	(*GetFilePermissionByAgentIDRequest)(nil),                                       // 24: api.webAgent.GetFilePermissionByAgentIDRequest
	(*GetFilePermissionByAgentIDReply)(nil),                                         // 25: api.webAgent.GetFilePermissionByAgentIDReply
	(*QueryModelAskClassificationDistributionRequest)(nil),                          // 26: api.webAgent.QueryModelAskClassificationDistributionRequest
	(*QueryModelAskClassificationDistributionReply)(nil),                            // 27: api.webAgent.QueryModelAskClassificationDistributionReply
	(*QueryUploadFileTypeDistributionRequest)(nil),                                  // 28: api.webAgent.QueryUploadFileTypeDistributionRequest
	(*QueryUploadFileTypeDistributionReply)(nil),                                    // 29: api.webAgent.QueryUploadFileTypeDistributionReply
	(*QueryModelAskClassificationTop10Request)(nil),                                 // 30: api.webAgent.QueryModelAskClassificationTop10Request
	(*QueryModelAskClassificationTop10Reply)(nil),                                   // 31: api.webAgent.QueryModelAskClassificationTop10Reply
	(*QueryUploadFileTypeTop10Request)(nil),                                         // 32: api.webAgent.QueryUploadFileTypeTop10Request
	(*QueryUploadFileTypeTop10Reply)(nil),                                           // 33: api.webAgent.QueryUploadFileTypeTop10Reply
	(*GetAllAgentInfoRequest)(nil),                                                  // 34: api.webAgent.GetAllAgentInfoRequest
	(*GetAllAgentInfoReply)(nil),                                                    // 35: api.webAgent.GetAllAgentInfoReply
	(*SaveAgentQueueWhiteListRequest)(nil),                                          // 36: api.webAgent.SaveAgentQueueWhiteListRequest
	(*SaveAgentQueueWhiteListReply)(nil),                                            // 37: api.webAgent.SaveAgentQueueWhiteListReply
	(*GetAgentQueueWhiteListRequest)(nil),                                           // 38: api.webAgent.GetAgentQueueWhiteListRequest
	(*GetAgentQueueWhiteListReply)(nil),                                             // 39: api.webAgent.GetAgentQueueWhiteListReply
	(*GetAgentQueueWhiteListItem)(nil),                                              // 40: api.webAgent.GetAgentQueueWhiteListItem
	(*UpdateAgentSortRequest)(nil),                                                  // 41: api.webAgent.UpdateAgentSortRequest
	(*UpdateAgentSortRequestItem)(nil),                                              // 42: api.webAgent.UpdateAgentSortRequestItem
	(*UpdateAgentSortReply)(nil),                                                    // 43: api.webAgent.UpdateAgentSortReply
	(*TransferAgentRequest)(nil),                                                    // 44: api.webAgent.TransferAgentRequest
	(*TransferAgentReply)(nil),                                                      // 45: api.webAgent.TransferAgentReply
	(*CheckQuestionSecurityRequest)(nil),                                            // 46: api.webAgent.CheckQuestionSecurityRequest
	(*CheckQuestionSecurityReply)(nil),                                              // 47: api.webAgent.CheckQuestionSecurityReply
	(*PageAgentSecurityLogsRequest)(nil),                                            // 48: api.webAgent.PageAgentSecurityLogsRequest
	(*PageAgentSecurityLogsReply)(nil),                                              // 49: api.webAgent.PageAgentSecurityLogsReply
	(*PageAgentSecurityLogsReplyItem)(nil),                                          // 50: api.webAgent.PageAgentSecurityLogsReplyItem
	(*GetAgentSecurityLogsCountRequest)(nil),                                        // 51: api.webAgent.GetAgentSecurityLogsCountRequest
	(*GetAgentSecurityLogsCountReply)(nil),                                          // 52: api.webAgent.GetAgentSecurityLogsCountReply
	(*GetAgentSecurityLogDetailRequest)(nil),                                        // 53: api.webAgent.GetAgentSecurityLogDetailRequest
	(*GetAgentSecurityLogDetailReply)(nil),                                          // 54: api.webAgent.GetAgentSecurityLogDetailReply
	(*GetUserAgentsAndKnowledgeBasesRequest)(nil),                                   // 55: api.webAgent.GetUserAgentsAndKnowledgeBasesRequest
	(*GetUserAgentsAndKnowledgeBasesReply)(nil),                                     // 56: api.webAgent.GetUserAgentsAndKnowledgeBasesReply
	(*GenerateQuestionOptimizationRequest)(nil),                                     // 57: api.webAgent.GenerateQuestionOptimizationRequest
	(*GenerateQuestionOptimizationReply)(nil),                                       // 58: api.webAgent.GenerateQuestionOptimizationReply
	(*AskAgentRequest_FileInfo)(nil),                                                // 59: api.webAgent.AskAgentRequest.FileInfo
	(*AskAgentReply_FileInfo)(nil),                                                  // 60: api.webAgent.AskAgentReply.FileInfo
	(*QueryModelAskClassificationDistributionReply_ClassificationDistribution)(nil), // 61: api.webAgent.QueryModelAskClassificationDistributionReply.ClassificationDistribution
	(*QueryUploadFileTypeDistributionReply_FileTypeDistribution)(nil),               // 62: api.webAgent.QueryUploadFileTypeDistributionReply.FileTypeDistribution
	(*QueryModelAskClassificationTop10Reply_ClassificationDistribution)(nil),        // 63: api.webAgent.QueryModelAskClassificationTop10Reply.ClassificationDistribution
	(*QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem)(nil), // 64: api.webAgent.QueryModelAskClassificationTop10Reply.QueryModelAskClassificationTop10ReplyItem
	(*QueryUploadFileTypeTop10Reply_FileTypeDistribution)(nil),                              // 65: api.webAgent.QueryUploadFileTypeTop10Reply.FileTypeDistribution
	(*QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem)(nil),                 // 66: api.webAgent.QueryUploadFileTypeTop10Reply.QueryUploadFileTypeTop10ReplyItem
	(*GetAllAgentInfoReply_AgentInfo)(nil),                                                  // 67: api.webAgent.GetAllAgentInfoReply.AgentInfo
	(*CheckQuestionSecurityRequest_FileInfo)(nil),                                           // 68: api.webAgent.CheckQuestionSecurityRequest.FileInfo
	(*GetUserAgentsAndKnowledgeBasesReply_AgentInfo)(nil),                                   // 69: api.webAgent.GetUserAgentsAndKnowledgeBasesReply.AgentInfo
	(*GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo)(nil),                           // 70: api.webAgent.GetUserAgentsAndKnowledgeBasesReply.KnowledgeBaseInfo
	(*timestamppb.Timestamp)(nil),                                                           // 71: google.protobuf.Timestamp
}
var file_webagent_agent_proto_depIdxs = []int32{
	71, // 0: api.webAgent.InternalModelChatPageRequest.startTime:type_name -> google.protobuf.Timestamp
	71, // 1: api.webAgent.InternalModelChatPageRequest.endTime:type_name -> google.protobuf.Timestamp
	2,  // 2: api.webAgent.InternalModelChatPageReply.records:type_name -> api.webAgent.InternalModelChatPageReplyItem
	71, // 3: api.webAgent.InternalModelChatPageReplyItem.createdAt:type_name -> google.protobuf.Timestamp
	60, // 4: api.webAgent.InternalModelChatPageReplyItem.refFiles:type_name -> api.webAgent.AskAgentReply.FileInfo
	71, // 5: api.webAgent.SecurityPolicy.createdAt:type_name -> google.protobuf.Timestamp
	71, // 6: api.webAgent.SecurityPolicy.updatedAt:type_name -> google.protobuf.Timestamp
	6,  // 7: api.webAgent.CreateAgentRequest.securityPolicies:type_name -> api.webAgent.SecurityPolicy
	6,  // 8: api.webAgent.UpdateAgentRequest.securityPolicies:type_name -> api.webAgent.SecurityPolicy
	15, // 9: api.webAgent.GetAgentReply.agents:type_name -> api.webAgent.GetAgentReplyItem
	71, // 10: api.webAgent.GetAgentReplyItem.updatedAt:type_name -> google.protobuf.Timestamp
	71, // 11: api.webAgent.GetAgentReplyItem.createdAt:type_name -> google.protobuf.Timestamp
	4,  // 12: api.webAgent.GetAgentReplyItem.visible_users:type_name -> api.webAgent.UserInfo
	5,  // 13: api.webAgent.GetAgentReplyItem.visible_depts:type_name -> api.webAgent.DeptInfo
	6,  // 14: api.webAgent.GetAgentReplyItem.securityPolicies:type_name -> api.webAgent.SecurityPolicy
	4,  // 15: api.webAgent.GetAgentReplyItem.manageable_users:type_name -> api.webAgent.UserInfo
	18, // 16: api.webAgent.PageAgentReply.records:type_name -> api.webAgent.PageAgentReplyItem
	71, // 17: api.webAgent.PageAgentReplyItem.updatedAt:type_name -> google.protobuf.Timestamp
	71, // 18: api.webAgent.PageAgentReplyItem.createdAt:type_name -> google.protobuf.Timestamp
	4,  // 19: api.webAgent.PageAgentReplyItem.visible_users:type_name -> api.webAgent.UserInfo
	5,  // 20: api.webAgent.PageAgentReplyItem.visible_depts:type_name -> api.webAgent.DeptInfo
	4,  // 21: api.webAgent.PageAgentReplyItem.manageable_users:type_name -> api.webAgent.UserInfo
	59, // 22: api.webAgent.AskAgentRequest.files:type_name -> api.webAgent.AskAgentRequest.FileInfo
	60, // 23: api.webAgent.AskAgentReply.files:type_name -> api.webAgent.AskAgentReply.FileInfo
	23, // 24: api.webAgent.GetDefaultAvatarsReply.avatars:type_name -> api.webAgent.GetDefaultAvatarItems
	71, // 25: api.webAgent.QueryModelAskClassificationDistributionRequest.startTime:type_name -> google.protobuf.Timestamp
	71, // 26: api.webAgent.QueryModelAskClassificationDistributionRequest.endTime:type_name -> google.protobuf.Timestamp
	61, // 27: api.webAgent.QueryModelAskClassificationDistributionReply.classificationDistributions:type_name -> api.webAgent.QueryModelAskClassificationDistributionReply.ClassificationDistribution
	71, // 28: api.webAgent.QueryUploadFileTypeDistributionRequest.startTime:type_name -> google.protobuf.Timestamp
	71, // 29: api.webAgent.QueryUploadFileTypeDistributionRequest.endTime:type_name -> google.protobuf.Timestamp
	62, // 30: api.webAgent.QueryUploadFileTypeDistributionReply.fileTypeDistributions:type_name -> api.webAgent.QueryUploadFileTypeDistributionReply.FileTypeDistribution
	71, // 31: api.webAgent.QueryModelAskClassificationTop10Request.startTime:type_name -> google.protobuf.Timestamp
	71, // 32: api.webAgent.QueryModelAskClassificationTop10Request.endTime:type_name -> google.protobuf.Timestamp
	64, // 33: api.webAgent.QueryModelAskClassificationTop10Reply.items:type_name -> api.webAgent.QueryModelAskClassificationTop10Reply.QueryModelAskClassificationTop10ReplyItem
	71, // 34: api.webAgent.QueryUploadFileTypeTop10Request.startTime:type_name -> google.protobuf.Timestamp
	71, // 35: api.webAgent.QueryUploadFileTypeTop10Request.endTime:type_name -> google.protobuf.Timestamp
	66, // 36: api.webAgent.QueryUploadFileTypeTop10Reply.items:type_name -> api.webAgent.QueryUploadFileTypeTop10Reply.QueryUploadFileTypeTop10ReplyItem
	67, // 37: api.webAgent.GetAllAgentInfoReply.agents:type_name -> api.webAgent.GetAllAgentInfoReply.AgentInfo
	40, // 38: api.webAgent.GetAgentQueueWhiteListReply.items:type_name -> api.webAgent.GetAgentQueueWhiteListItem
	42, // 39: api.webAgent.UpdateAgentSortRequest.items:type_name -> api.webAgent.UpdateAgentSortRequestItem
	68, // 40: api.webAgent.CheckQuestionSecurityRequest.uploadedFiles:type_name -> api.webAgent.CheckQuestionSecurityRequest.FileInfo
	71, // 41: api.webAgent.PageAgentSecurityLogsRequest.startTime:type_name -> google.protobuf.Timestamp
	71, // 42: api.webAgent.PageAgentSecurityLogsRequest.endTime:type_name -> google.protobuf.Timestamp
	50, // 43: api.webAgent.PageAgentSecurityLogsReply.records:type_name -> api.webAgent.PageAgentSecurityLogsReplyItem
	71, // 44: api.webAgent.PageAgentSecurityLogsReplyItem.createdAt:type_name -> google.protobuf.Timestamp
	71, // 45: api.webAgent.GetAgentSecurityLogDetailReply.createdAt:type_name -> google.protobuf.Timestamp
	6,  // 46: api.webAgent.GetAgentSecurityLogDetailReply.securityPolicies:type_name -> api.webAgent.SecurityPolicy
	69, // 47: api.webAgent.GetUserAgentsAndKnowledgeBasesReply.agents:type_name -> api.webAgent.GetUserAgentsAndKnowledgeBasesReply.AgentInfo
	70, // 48: api.webAgent.GetUserAgentsAndKnowledgeBasesReply.knowledgeBases:type_name -> api.webAgent.GetUserAgentsAndKnowledgeBasesReply.KnowledgeBaseInfo
	63, // 49: api.webAgent.QueryModelAskClassificationTop10Reply.QueryModelAskClassificationTop10ReplyItem.classificationDistributions:type_name -> api.webAgent.QueryModelAskClassificationTop10Reply.ClassificationDistribution
	65, // 50: api.webAgent.QueryUploadFileTypeTop10Reply.QueryUploadFileTypeTop10ReplyItem.fileTypeDistributions:type_name -> api.webAgent.QueryUploadFileTypeTop10Reply.FileTypeDistribution
	7,  // 51: api.webAgent.Agent.CreateAgent:input_type -> api.webAgent.CreateAgentRequest
	34, // 52: api.webAgent.Agent.GetAllAgentInfo:input_type -> api.webAgent.GetAllAgentInfoRequest
	9,  // 53: api.webAgent.Agent.UpdateAgent:input_type -> api.webAgent.UpdateAgentRequest
	11, // 54: api.webAgent.Agent.DeleteAgent:input_type -> api.webAgent.DeleteAgentRequest
	13, // 55: api.webAgent.Agent.GetAgent:input_type -> api.webAgent.GetAgentRequest
	16, // 56: api.webAgent.Agent.PageAgent:input_type -> api.webAgent.PageAgentRequest
	21, // 57: api.webAgent.Agent.GetDefaultAvatars:input_type -> api.webAgent.GetDefaultAvatarsRequest
	24, // 58: api.webAgent.Agent.GetFilePermissionByAgentID:input_type -> api.webAgent.GetFilePermissionByAgentIDRequest
	0,  // 59: api.webAgent.Agent.InternalModelChatPage:input_type -> api.webAgent.InternalModelChatPageRequest
	26, // 60: api.webAgent.Agent.QueryModelAskClassificationDistribution:input_type -> api.webAgent.QueryModelAskClassificationDistributionRequest
	28, // 61: api.webAgent.Agent.QueryUploadFileTypeDistribution:input_type -> api.webAgent.QueryUploadFileTypeDistributionRequest
	30, // 62: api.webAgent.Agent.QueryModelAskClassificationTop10:input_type -> api.webAgent.QueryModelAskClassificationTop10Request
	32, // 63: api.webAgent.Agent.QueryUploadFileTypeTop10:input_type -> api.webAgent.QueryUploadFileTypeTop10Request
	36, // 64: api.webAgent.Agent.SaveAgentQueueWhiteList:input_type -> api.webAgent.SaveAgentQueueWhiteListRequest
	38, // 65: api.webAgent.Agent.GetAgentQueueWhiteList:input_type -> api.webAgent.GetAgentQueueWhiteListRequest
	41, // 66: api.webAgent.Agent.UpdateAgentSort:input_type -> api.webAgent.UpdateAgentSortRequest
	44, // 67: api.webAgent.Agent.TransferAgent:input_type -> api.webAgent.TransferAgentRequest
	46, // 68: api.webAgent.Agent.CheckQuestionSecurity:input_type -> api.webAgent.CheckQuestionSecurityRequest
	48, // 69: api.webAgent.Agent.PageAgentSecurityLogs:input_type -> api.webAgent.PageAgentSecurityLogsRequest
	53, // 70: api.webAgent.Agent.GetAgentSecurityLogDetail:input_type -> api.webAgent.GetAgentSecurityLogDetailRequest
	51, // 71: api.webAgent.Agent.GetAgentSecurityLogsCount:input_type -> api.webAgent.GetAgentSecurityLogsCountRequest
	55, // 72: api.webAgent.Agent.GetUserAgentsAndKnowledgeBases:input_type -> api.webAgent.GetUserAgentsAndKnowledgeBasesRequest
	57, // 73: api.webAgent.Agent.GenerateQuestionOptimization:input_type -> api.webAgent.GenerateQuestionOptimizationRequest
	8,  // 74: api.webAgent.Agent.CreateAgent:output_type -> api.webAgent.CreateAgentReply
	35, // 75: api.webAgent.Agent.GetAllAgentInfo:output_type -> api.webAgent.GetAllAgentInfoReply
	10, // 76: api.webAgent.Agent.UpdateAgent:output_type -> api.webAgent.UpdateAgentReply
	12, // 77: api.webAgent.Agent.DeleteAgent:output_type -> api.webAgent.DeleteAgentReply
	14, // 78: api.webAgent.Agent.GetAgent:output_type -> api.webAgent.GetAgentReply
	17, // 79: api.webAgent.Agent.PageAgent:output_type -> api.webAgent.PageAgentReply
	22, // 80: api.webAgent.Agent.GetDefaultAvatars:output_type -> api.webAgent.GetDefaultAvatarsReply
	25, // 81: api.webAgent.Agent.GetFilePermissionByAgentID:output_type -> api.webAgent.GetFilePermissionByAgentIDReply
	1,  // 82: api.webAgent.Agent.InternalModelChatPage:output_type -> api.webAgent.InternalModelChatPageReply
	27, // 83: api.webAgent.Agent.QueryModelAskClassificationDistribution:output_type -> api.webAgent.QueryModelAskClassificationDistributionReply
	29, // 84: api.webAgent.Agent.QueryUploadFileTypeDistribution:output_type -> api.webAgent.QueryUploadFileTypeDistributionReply
	31, // 85: api.webAgent.Agent.QueryModelAskClassificationTop10:output_type -> api.webAgent.QueryModelAskClassificationTop10Reply
	33, // 86: api.webAgent.Agent.QueryUploadFileTypeTop10:output_type -> api.webAgent.QueryUploadFileTypeTop10Reply
	37, // 87: api.webAgent.Agent.SaveAgentQueueWhiteList:output_type -> api.webAgent.SaveAgentQueueWhiteListReply
	39, // 88: api.webAgent.Agent.GetAgentQueueWhiteList:output_type -> api.webAgent.GetAgentQueueWhiteListReply
	43, // 89: api.webAgent.Agent.UpdateAgentSort:output_type -> api.webAgent.UpdateAgentSortReply
	45, // 90: api.webAgent.Agent.TransferAgent:output_type -> api.webAgent.TransferAgentReply
	47, // 91: api.webAgent.Agent.CheckQuestionSecurity:output_type -> api.webAgent.CheckQuestionSecurityReply
	49, // 92: api.webAgent.Agent.PageAgentSecurityLogs:output_type -> api.webAgent.PageAgentSecurityLogsReply
	54, // 93: api.webAgent.Agent.GetAgentSecurityLogDetail:output_type -> api.webAgent.GetAgentSecurityLogDetailReply
	52, // 94: api.webAgent.Agent.GetAgentSecurityLogsCount:output_type -> api.webAgent.GetAgentSecurityLogsCountReply
	56, // 95: api.webAgent.Agent.GetUserAgentsAndKnowledgeBases:output_type -> api.webAgent.GetUserAgentsAndKnowledgeBasesReply
	58, // 96: api.webAgent.Agent.GenerateQuestionOptimization:output_type -> api.webAgent.GenerateQuestionOptimizationReply
	74, // [74:97] is the sub-list for method output_type
	51, // [51:74] is the sub-list for method input_type
	51, // [51:51] is the sub-list for extension type_name
	51, // [51:51] is the sub-list for extension extendee
	0,  // [0:51] is the sub-list for field type_name
}

func init() { file_webagent_agent_proto_init() }
func file_webagent_agent_proto_init() {
	if File_webagent_agent_proto != nil {
		return
	}
	file_webagent_enum_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_webagent_agent_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   71,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_webagent_agent_proto_goTypes,
		DependencyIndexes: file_webagent_agent_proto_depIdxs,
		MessageInfos:      file_webagent_agent_proto_msgTypes,
	}.Build()
	File_webagent_agent_proto = out.File
	file_webagent_agent_proto_rawDesc = nil
	file_webagent_agent_proto_goTypes = nil
	file_webagent_agent_proto_depIdxs = nil
}

syntax = "proto3";

package api.webAgent;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "webagent/enum.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/webAgent;webAgent";
option java_multiple_files = true;
option java_package = "api.webAgent";

service Agent {
  // 创建 Agent
  rpc CreateAgent(CreateAgentRequest) returns (CreateAgentReply) {
    option (google.api.http) = {
      post: "/agent/create"
      body: "*"
    };
  }

  // 查询所有Agent id/anme
  rpc GetAllAgentInfo(GetAllAgentInfoRequest) returns (GetAllAgentInfoReply) {
    option (google.api.http) = {get: "/agent/all"};
  }

  // 更新 Agent
  rpc UpdateAgent(UpdateAgentRequest) returns (UpdateAgentReply) {
    option (google.api.http) = {
      post: "/agent/update"
      body: "*"
    };
  }
  // 删除 Agent
  rpc DeleteAgent(DeleteAgentRequest) returns (DeleteAgentReply) {
    option (google.api.http) = {
      post: "/agent/delete"
      body: "*"
    };
  }
  // 查询 Agent
  rpc GetAgent(GetAgentRequest) returns (GetAgentReply) {
    option (google.api.http) = {get: "/agent/get"};
  }
  // 分页 Agent
  rpc PageAgent(PageAgentRequest) returns (PageAgentReply) {
    option (google.api.http) = {get: "/agent/page"};
  }
  // 获取智能体默认头像
  rpc GetDefaultAvatars(GetDefaultAvatarsRequest) returns (GetDefaultAvatarsReply) {
    option (google.api.http) = {get: "/agent/defaultAvatars"};
  }
  // 判断用户是否有文件权限（关联智能体、知识库）
  rpc GetFilePermissionByAgentID(GetFilePermissionByAgentIDRequest) returns (GetFilePermissionByAgentIDReply) {
    option (google.api.http) = {get: "/agent/filePermission"};
  }

  // 内部模型聊天记录分页
  rpc InternalModelChatPage(InternalModelChatPageRequest) returns (InternalModelChatPageReply) {
    option (google.api.http) = {
      post: "/agent/internalModelChatPage"
      body: "*"
    };
  }

  // 查询模型提问词分类分布
  rpc QueryModelAskClassificationDistribution(QueryModelAskClassificationDistributionRequest) returns (QueryModelAskClassificationDistributionReply) {
    option (google.api.http) = {get: "/agent/modelAskClassificationDistribution"};
  }

  // 查询上传附件文件类型分布
  rpc QueryUploadFileTypeDistribution(QueryUploadFileTypeDistributionRequest) returns (QueryUploadFileTypeDistributionReply) {
    option (google.api.http) = {get: "/agent/uploadFileTypeDistribution"};
  }

  // 查询模型提问词分类top10
  rpc QueryModelAskClassificationTop10(QueryModelAskClassificationTop10Request) returns (QueryModelAskClassificationTop10Reply) {
    option (google.api.http) = {get: "/agent/modelAskClassificationTop10"};
  }

  // 查询上传附件文件类型top10
  rpc QueryUploadFileTypeTop10(QueryUploadFileTypeTop10Request) returns (QueryUploadFileTypeTop10Reply) {
    option (google.api.http) = {get: "/agent/uploadFileTypeTop10"};
  }

  // 保存智能体排队白名单人员id列表
  rpc SaveAgentQueueWhiteList(SaveAgentQueueWhiteListRequest) returns (SaveAgentQueueWhiteListReply) {
    option (google.api.http) = {
      post: "/agent/queue/whiteList"
      body: "*"
    };
  }

  // 获取智能体排队白名单人员id列表
  rpc GetAgentQueueWhiteList(GetAgentQueueWhiteListRequest) returns (GetAgentQueueWhiteListReply) {
    option (google.api.http) = {get: "/agent/queue/whiteList"};
  }

  // 更新智能体排序
  rpc UpdateAgentSort(UpdateAgentSortRequest) returns (UpdateAgentSortReply) {
    option (google.api.http) = {
      post: "/agent/update/sort"
      body: "*"
    };
  }

  // 转让智能体
  rpc TransferAgent(TransferAgentRequest) returns (TransferAgentReply) {
    option (google.api.http) = {
      post: "/agent/transfer"
      body: "*"
    };
  }

  // 校验问题安全
  rpc CheckQuestionSecurity(CheckQuestionSecurityRequest) returns (CheckQuestionSecurityReply) {
    option (google.api.http) = {
      post: "/agent/checkQuestionSecurity"
      body: "*"
    };
  }

  // 分页智能体安全策略数据管控
  rpc PageAgentSecurityLogs(PageAgentSecurityLogsRequest) returns (PageAgentSecurityLogsReply) {
    option (google.api.http) = {get: "/agent/security/logs"};
  }

  // 获取智能体安全策略数据管控详情
  rpc GetAgentSecurityLogDetail(GetAgentSecurityLogDetailRequest) returns (GetAgentSecurityLogDetailReply) {
    option (google.api.http) = {get: "/agent/security/logs/detail"};
  }

  // 获取智能体安全策略数据管控数量
  rpc GetAgentSecurityLogsCount(GetAgentSecurityLogsCountRequest) returns (GetAgentSecurityLogsCountReply) {
    option (google.api.http) = {get: "/agent/security/logs/count"};
  }

  // 获取用户智能体列表、知识库列表
  rpc GetUserAgentsAndKnowledgeBases(GetUserAgentsAndKnowledgeBasesRequest) returns (GetUserAgentsAndKnowledgeBasesReply) {
    option (google.api.http) = {get: "/agent/user/agentsAndKnowledgeBases"};
  }

  // 根据问题推荐问题
  rpc GenerateQuestionOptimization(GenerateQuestionOptimizationRequest) returns (GenerateQuestionOptimizationReply) {
    option (google.api.http) = {
      post: "/agent/generateQuestionOptimization"
      body: "*"
    };
  }
}

message InternalModelChatPageRequest {
  int64 pageNum = 1 [(validate.rules).int64 = {gt: 0}];
  int64 pageSize = 2 [(validate.rules).int64 = {
    gt: 0
    lte: 100
  }];
  string userName = 3;
  string deptName = 4;
  google.protobuf.Timestamp startTime = 5;
  google.protobuf.Timestamp endTime = 6;
  int64 chatItemID = 7;
  // 私有模型id
  int64 agentID = 8;
  // 网关模型id
  int64 modelID = 9;
  // 搜索文件类型
  repeated string searchfile = 10;
  // 搜索无附件
  bool noRefFiles = 12;
  // 是否是网关模型
  bool searchModel = 11;
  // 文件分类
  string class = 13;
}

message InternalModelChatPageReply {
  int64 total = 1;
  repeated InternalModelChatPageReplyItem records = 2;
}

message InternalModelChatPageReplyItem {
  string message = 1;
  string username = 2;
  int64 userID = 3;
  string userAvatar = 9;
  string deptName = 4;
  int64 deptID = 5;
  string agentName = 6;
  string agentAvatar = 7;
  google.protobuf.Timestamp createdAt = 8;
  string pcName = 10;
  int64 id = 11;
  // 提示词分类
  string class = 12;
  //  引用文件
  string refFiesText = 13;
  //  引用文件
  repeated AskAgentReply.FileInfo refFiles = 14;
}

message AgentInfo {
  // 智能体id
  int64 id = 1;
  // 智能体名称
  string name = 2;
  // 智能体头像
  string avatar = 3;
  // 欢迎语
  string welcome_msg = 4;
  // 兜底回复
  string fallback_msg = 5;
  // 创建者id
  int64 owner_id = 6;
  // 可见性对象类型
  int64 visibility_type = 7;
  // 可见用户id列表
  repeated int64 visible_to_user = 8;
  // 可见部门id列表
  repeated int64 visible_to_dept = 14;
  // 知识库id列表
  repeated int64 knowledge_base_ids = 9;
  // 智能体编排schema
  string schema = 10;
  // 是否公开
  bool is_public = 11;
  // 是否启用
  bool is_enabled = 12;
  // 描述
  string description = 13;
  // 模型类型
  int64 modelType = 15;
}

message UserInfo {
  // 用户id
  int64 id = 1;
  // 用户名称
  string name = 2;
  // 用户头像
  string avatar = 3;
}

message DeptInfo {
  // 部门id
  int64 id = 1;
  // 部门名称
  string name = 2;
}

// =========================== create

message SecurityPolicy {
  string name = 1;
  // 策略分类 1.敏感信息匹配
  int64 policyCategory = 2;
  // 风险级别 1.低危 2.中危 3.高危
  int64 riskLevel = 3;
  // 是否启用
  bool enabled = 4;
  // 策略内容
  repeated string policies = 5;
  //命中策略后的操作 1.阻断 2.警告
  int64 hitAction = 6;
  // 命中策略后的回复
  string hitResponse = 7;
  int64 id = 8;
  // 创建时间
  google.protobuf.Timestamp createdAt = 9;
  // 更新时间
  google.protobuf.Timestamp updatedAt = 10;
  // 修改人
  int64 updatedBy = 11;
  // 修改人姓名
  string updatedByName = 12;
}

message CreateAgentRequest {
  // 智能体名称
  string name = 1;
  // 智能体头像
  string avatar = 2;
  // 欢迎语
  string welcome_msg = 3;
  // 兜底回复
  string fallback_msg = 4;
  // 创建者id
  int64 owner_id = 5;
  // 可见性对象类型
  int64 visibility_type = 6;
  // 可见对象id列表
  repeated int64 visible_to_user = 7;
  // 可见对象id列表
  repeated int64 visible_to_dept = 13;
  // 知识库id列表
  repeated int64 knowledge_base_ids = 8;
  // 智能体编排schema
  string schema = 9;
  // 是否公开
  bool is_public = 10;
  // 是否启用
  bool is_enabled = 11;
  // 描述
  string description = 12;
  // 是否显示引用文件
  bool showReferenceFile = 14;
  // 1. 内部模型 2. 外部模型
  int64 modelType = 15;
  // 模型id
  int64 modelID = 16;
  // 联网搜索
  bool internetSearch = 17;
  // 智能体类型 1：基础问答 2：检索深度问答 3：合同审核
  int64 agentType = 18;
  // 深度思考
  bool thinking = 19;
  // 深度思考模型id
  int64 thinkingModelID = 20;
  // 角色设定
  string role_setting = 21;
  // 安全策略
  repeated SecurityPolicy securityPolicies = 22;
  // 上传附件
  bool uploadFile = 23;
  // 语义缓存
  bool semanticCache = 24;
  // 可见对象id列表
  repeated int64 manageable_to_user = 25;
  // 点击头像
  string clicked_avatar = 26;
}

message CreateAgentReply {
  // 智能体id
  int64 id = 1;
  // 智能体名称
  string name = 2;
  // 智能体头像
  string avatar = 3;
  // 欢迎语
  string welcome_msg = 4;
  // 兜底回复
  string fallback_msg = 5;
  // 创建者id
  int64 owner_id = 6;
  // 可见性对象类型
  int64 visibility_type = 7;
  // 可见对象id列表
  repeated int64 visible_to_user = 8;
  // 可见对象id列表
  repeated int64 visible_to_dept = 13;
  // 知识库id列表
  repeated int64 knowledge_base_ids = 9;
  // 智能体编排schema
  string schema = 10;
  // 是否公开
  bool is_public = 11;
  // 是否启用
  bool is_enabled = 12;
  // 描述
  string description = 14;
  // 是否显示引用文件
  bool showReferenceFile = 15;
}

// =========================== update
message UpdateAgentRequest {
  // 智能体id
  int64 id = 1;
  // 智能体名称
  string name = 2;
  // 智能体头像
  string avatar = 3;
  // 欢迎语
  string welcome_msg = 4;
  // 兜底回复
  string fallback_msg = 5;
  // 创建者id
  int64 owner_id = 6;
  // 可见性对象类型
  int64 visibility_type = 7;
  // 可见对象id列表
  repeated int64 visible_to_user = 8;
  // 可见对象id列表
  repeated int64 visible_to_dept = 13;
  // 知识库id列表
  repeated int64 knowledge_base_ids = 9;
  // 智能体编排schema
  string schema = 10;
  // 是否公开
  bool is_public = 11;
  // 是否启用
  bool is_enabled = 12;
  // 描述
  string description = 14;
  // 是否显示引用文件
  bool showReferenceFile = 15;
  // 联网搜索
  bool internetSearch = 16;
  // 角色设定
  string role_setting = 17;
  // 深度思考
  bool thinking = 18;
  // 深度思考模型id
  int64 thinkingModelID = 19;
  // 策略，修改策略必须传id，创建策略id为0
  repeated SecurityPolicy securityPolicies = 20;
  // 删除的安全策略id列表
  repeated int64 deletedSecurityPolicyIDs = 21;
  // 上传附件
  bool uploadFile = 22;
  // 语义缓存
  bool semanticCache = 23;
  // 模型id
  int64 modelID = 24;
  // 可见对象id列表
  repeated int64 manageable_to_user = 25;
  // 点击头像地址
  string clicked_avatar = 26;
}

message UpdateAgentReply {
  // 智能体id
  int64 id = 1;
  // 智能体名称
  string name = 2;
  // 智能体头像
  string avatar = 3;
  // 欢迎语
  string welcome_msg = 4;
  // 兜底回复
  string fallback_msg = 5;
  // 创建者id
  int64 owner_id = 6;
  // 可见性对象类型
  int64 visibility_type = 7;
  // 可见对象id列表
  repeated int64 visible_to_user = 8;
  // 可见对象id列表
  repeated int64 visible_to_dept = 13;
  // 知识库id列表
  repeated int64 knowledge_base_ids = 9;
  // 智能体编排schema
  string schema = 10;
  // 是否公开
  bool is_public = 11;
  // 是否启用
  bool is_enabled = 12;
  // 描述
  string description = 14;
  // 是否显示引用文件
  bool showReferenceFile = 15;
}

// =========================== delete
message DeleteAgentRequest {
  // 智能体id
  int64 id = 1;
}
message DeleteAgentReply {}

// =========================== get
message GetAgentRequest {
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
  }];
}

message GetAgentReply {
  repeated GetAgentReplyItem agents = 1;
}

message GetAgentReplyItem {
  // 智能体id
  int64 id = 1;
  // 智能体名称
  string name = 2;
  // 头像地址
  string avatar_url = 17;
  // 智能体头像
  string avatar = 3;
  // 欢迎语
  string welcome_msg = 4;
  // 兜底回复
  string fallback_msg = 5;
  // 创建者id
  int64 owner_id = 6;
  // 创建者姓名
  string owner_name = 16;
  // 创建者头像
  string owner_avatar = 21;
  // 可见性对象类型
  int64 visibility_type = 7;
  // 可见对象id列表
  repeated int64 visible_to_user = 8;
  // 可见对象id列表
  repeated int64 visible_to_dept = 13;
  // 知识库id列表
  repeated int64 knowledge_base_ids = 9;
  // 智能体编排schema
  string schema = 10;
  // 是否公开
  bool is_public = 11;
  // 是否启用
  bool is_enabled = 12;
  // 描述
  string description = 14;
  // 更新时间
  google.protobuf.Timestamp updatedAt = 15;
  // 创建时间
  google.protobuf.Timestamp createdAt = 18;
  // 可见用户
  repeated UserInfo visible_users = 19;
  // 可见部门
  repeated DeptInfo visible_depts = 20;
  // 是否显示引用文件
  bool showReferenceFile = 22;
  // 模型名称
  string modelName = 23;
  // 模型类型
  int64 modelType = 24;
  // 模型
  int64 modelID = 25;
  // 知识库类型
  int64 knowledge_base_type = 26;
  // 联网搜索
  bool internetSearch = 27;
  // 是否启用联网搜索开关
  bool enableInternetSearchSwitch = 28;
  // 智能体类型 1：基础问答 2：检索深度问答 3：合同审核
  int64 agentType = 29;
  // 角色设定
  string roleSetting = 30;
  // 深度思考
  bool thinking = 31;
  // 深度思考模型id
  int64 thinkingModelID = 32;
  // 深度思考模型名称
  string thinkingModelName = 33;
  // 深度思考模型头像
  string thinkingModelAvatar = 34;
  // 模型头像
  string modelAvatar = 35;
  int64 modelDetailID = 36;
  // 深度思考启用状态 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
  int64 thinkingEnableStatus = 37;
  // 安全策略
  repeated SecurityPolicy securityPolicies = 38;
  // 上传附件
  bool uploadFile = 39;
  // 语义缓存
  bool semanticCache = 40;
  // 可管理对象id列表
  repeated int64 manageable_to_user = 41;
  // 可管理用户
  repeated UserInfo manageable_users = 42;
}

// =========================== page
message PageAgentRequest {
  int64 pageNum = 1 [(validate.rules).int64 = {gt: 0}];
  int64 pageSize = 2;
  // 是否展示在客户端
  bool showOnClient = 3;
  // 模型类型 0.所有 1.私有模型 2.网关模型
  int64 modelType = 4;
  // 是我创建的
  bool isMine = 5;
  // 智能体类别 1.AI工具 2.问答智能体 3.深度检索问答智能体 4.问答+深度检索智能体 5.常用智能体
  int64 agentCategoryType = 6;
  // 智能体名称
  string agentName = 7;
}

message PageAgentReply {
  int64 total = 1;
  repeated PageAgentReplyItem records = 2;
}

message PageAgentReplyItem {
  // 智能体id
  int64 id = 1;
  // 智能体名称
  string name = 2;
  // 智能体头像
  string avatar = 3;
  // 头像地址
  string avatar_url = 17;
  // 欢迎语
  string welcome_msg = 4;
  // 兜底回复
  string fallback_msg = 5;
  // 创建者id
  int64 owner_id = 6;
  // 创建者姓名
  string owner_name = 16;
  // 创建者头像
  string owner_avatar = 21;
  // 可见性对象类型
  int64 visibility_type = 7;
  // 可见对象id列表
  repeated int64 visible_to_user = 8;
  // 可见对象id列表
  repeated int64 visible_to_dept = 18;
  // 知识库id列表
  repeated int64 knowledge_base_ids = 9;
  // 智能体编排schema
  string schema = 10;
  // 是否公开
  bool is_public = 11;
  // 是否启用
  bool is_enabled = 12;
  // 描述
  string description = 13;
  // 更新时间
  google.protobuf.Timestamp updatedAt = 14;
  // 创建时间
  google.protobuf.Timestamp createdAt = 15;
  // 可见用户
  repeated UserInfo visible_users = 19;
  // 可见部门
  repeated DeptInfo visible_depts = 20;
  // 是否显示引用文件
  bool showReferenceFile = 22;
  // 模型名称
  string modelName = 23;
  // 模型类型
  int64 modelType = 24;
  // 模型
  int64 model = 25;
  // 知识库类型
  int64 knowledgeBaseType = 26;
  // 是否可联网搜索
  bool canInternetSearch = 27;
  int64 agentType = 28;
  // 深度思考开启状态 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
  int64 thinkingEnableStatus = 29;
  // 是否开启深度思考
  bool thinking = 30;
  // 上传文件
  bool uploadFile = 31;
  // 语义缓存
  bool semanticCache = 32;
  // 外网模型名称
  string modelDetailName = 33;
  // 可管理用户
  repeated UserInfo manageable_users = 34;
  // 可管理用户id列表
  repeated int64 manageable_to_user = 35;
  // 点击头像地址
  string clicked_avatar_url = 36;
}

message AskAgentRequest {
  message FileInfo {
    int64 fileRelationID = 1;
    string title = 2;
    int64 size = 3;
    string mimeType = 4;
    int64 userID = 5;
    string entityTag = 6;
    string preEntityTag = 7;
    int64 index = 8;
    int64 chunkIndex = 9;
    string fullPath = 10;
    int64 fromChatItemID = 11;
    int64 fromFileRelationID = 12;
  }
  string question = 1 [(validate.rules).string = {min_len: 1}];
  int64 chatID = 2;
  int64 roundID = 3;
  repeated FileInfo files = 4;
  int64 agentID = 5;
  // 是否多轮
  bool isMultiRound = 6;
  string pcName = 7;
  // 是否联网搜索
  bool internetSearch = 8;
  // 是否深度思考
  bool thinking = 9;
  // chatItemID, 如果传值则在此chatItemID下进行处理，不会对问题创建新的chatItem
  int64 chatItemID = 10;
  // 指定sheet知识库, 用于指定一个sheet文件问答，因为同一个sheet文件在每个知识库都是不一样的。
  int64 designateSheetKnowledgeBaseID = 11;
  // 已存在图表数据
  string existingTableData = 12;
}

message AskAgentReply {
  message FileInfo {
    int64 fileRelationID = 1;
    string title = 2;
    int64 size = 3;
    string mimeType = 4;
    int64 userID = 5;
    string entityTag = 6;
    string preEntityTag = 7;
    int64 index = 8;
    int64 chunkIndex = 9;
    int64 chunkSize = 10;
    repeated string images = 11;
    repeated string tags = 12;
    string fullPath = 13;
    int64 knowledgeBaseID = 14;
    string knowledgeBaseName = 15;
    int64 knowledgeBaseDataType = 16;
    string tableData = 17;
    string chartSchema = 18;
    string classPath = 19;
    int64 level = 20;
  }
  string answer = 1;
  repeated FileInfo files = 2;
  // 0:回答中,1:回答完成
  int64 status = 3;
  int64 roundID = 4;
  int64 type = 5;
  string reason = 6;
  int64 chatItemID = 7;
  string debugContent = 8;
}

message GetDefaultAvatarsRequest {
  // 1.部门应用图标  2.数字员工图标
  int64 avatar_type = 1;
}
message GetDefaultAvatarsReply {
  repeated GetDefaultAvatarItems avatars = 1;
}

message GetDefaultAvatarItems {
  string avatar = 1;
  string url = 2;
  string clicked_avatar = 3;
  string clicked_avatar_url = 4;
}

message GetFilePermissionByAgentIDRequest {
  int64 agentID = 1;
  int64 fileRelationID = 2;
}

message GetFilePermissionByAgentIDReply {
  bool hasPermission = 1;
}

message QueryModelAskClassificationDistributionRequest {
  // 1. 私有模型 2. 网关模型 3. 外网模型
  int64 modelType = 1;
  google.protobuf.Timestamp startTime = 2;
  google.protobuf.Timestamp endTime = 3;
}

message QueryModelAskClassificationDistributionReply {
  message ClassificationDistribution {
    // 分类名称
    string classificationName = 1;
    // 分类数量
    int64 count = 2;
  }
  repeated ClassificationDistribution classificationDistributions = 1;
}

message QueryUploadFileTypeDistributionRequest {
  // 1. 私有模型 2. 网关模型 3. 外网模型
  int64 modelType = 1;
  google.protobuf.Timestamp startTime = 2;
  google.protobuf.Timestamp endTime = 3;
}

message QueryUploadFileTypeDistributionReply {
  message FileTypeDistribution {
    // 文件类型
    string fileType = 1;
    // 文件数量
    int64 count = 2;
  }
  repeated FileTypeDistribution fileTypeDistributions = 1;
}

message QueryModelAskClassificationTop10Request {
  // 1. 员工top 10 2. 部门top 10
  int64 topType = 1;
  google.protobuf.Timestamp startTime = 2;
  google.protobuf.Timestamp endTime = 3;
  // 1. 私有模型 2. 网关模型
  int64 modelType = 4;
}

message QueryModelAskClassificationTop10Reply {
  message ClassificationDistribution {
    // 分类名称
    string classificationName = 1;
    // 分类数量
    int64 count = 2;
  }

  message QueryModelAskClassificationTop10ReplyItem {
    // 员工姓名/部门名称
    string name = 1;
    repeated ClassificationDistribution classificationDistributions = 2;
    // 总数
    int64 total = 3;
  }

  repeated QueryModelAskClassificationTop10ReplyItem items = 1;
}

message QueryUploadFileTypeTop10Request {
  // 1. 员工top 10 2. 部门top 10
  int64 topType = 1;
  google.protobuf.Timestamp startTime = 2;
  google.protobuf.Timestamp endTime = 3;
  // 1. 私有模型 2. 网关模型
  int64 modelType = 4;
}

message QueryUploadFileTypeTop10Reply {
  message FileTypeDistribution {
    // 文件类型
    string fileType = 1;
    // 文件数量
    int64 count = 2;
  }

  message QueryUploadFileTypeTop10ReplyItem {
    // 员工姓名/部门名称
    string name = 1;
    repeated FileTypeDistribution fileTypeDistributions = 2;
    // 总数
    int64 total = 3;
  }

  repeated QueryUploadFileTypeTop10ReplyItem items = 1;
}

message GetAllAgentInfoRequest {
  // 1. 私有模型 2. 网关模型
  int64 model_type = 1;
}

message GetAllAgentInfoReply {
  message AgentInfo {
    int64 id = 1;
    string name = 2;
  }
  repeated AgentInfo agents = 1;
}

message SaveAgentQueueWhiteListRequest {
  repeated int64 userIDs = 1;
}

message SaveAgentQueueWhiteListReply {}

message GetAgentQueueWhiteListRequest {}

message GetAgentQueueWhiteListReply {
  repeated GetAgentQueueWhiteListItem items = 1;
}

message GetAgentQueueWhiteListItem {
  int64 userID = 1;
  string userName = 2;
  string userAvatar = 3;
}

message UpdateAgentSortRequest {
  repeated UpdateAgentSortRequestItem items = 1;
}

message UpdateAgentSortRequestItem {
  int64 agentID = 1;
  int64 index = 2;
}

message UpdateAgentSortReply {}

message TransferAgentRequest {
  repeated int64 agentIDs = 1;
  int64 newOwnerID = 2;
}

message TransferAgentReply {}

message CheckQuestionSecurityRequest {
  message FileInfo {
    int64 fileRelationID = 1;
    string title = 2;
    int64 size = 3;
    string mimeType = 4;
    int64 userID = 5;
    string entityTag = 6;
    string preEntityTag = 7;
    int64 index = 8;
    int64 chunkIndex = 9;
    string fullPath = 10;
  }
  string question = 1 [(validate.rules).string = {min_len: 1}];
  int64 agentID = 2;
  repeated FileInfo uploadedFiles = 3;
  int64 chatID = 4;
  string pcName = 5;
}

message CheckQuestionSecurityReply {
  // 风险级别 1.低危 2.中危 3.高危
  int64 riskLevel = 1;
  // 1: 阻断 2: 警告
  int64 hitAction = 2;
  // 命中策略后的回复
  string hitResponse = 3;
  // 保存的chatItemID
  int64 chatItemID = 4;
}

message PageAgentSecurityLogsRequest {
  int64 pageNum = 1 [(validate.rules).int64 = {gt: 0}];
  int64 pageSize = 2;
  // 1.阻断 2.警告
  repeated int64 hitAction = 3;
  // 1. 低风险 2. 中风险 3. 高风险
  repeated int64 riskLevel = 4;
  string userName = 5;
  string deptName = 6;
  google.protobuf.Timestamp startTime = 7;
  google.protobuf.Timestamp endTime = 8;
}

message PageAgentSecurityLogsReply {
  int64 total = 1;
  repeated PageAgentSecurityLogsReplyItem records = 2;
}

message PageAgentSecurityLogsReplyItem {
  int64 id = 1;
  // 1. 低风险 2. 中风险 3. 高风险
  int64 riskLevel = 2;
  // 1.阻断 2.警告
  int64 hitAction = 3;
  int64 userID = 4;
  string userName = 5;
  string deptName = 6;
  string userAvatar = 7;
  int64 agentID = 8;
  string agentName = 9;
  // 1. 智能问答
  int64 actionCategory = 10;
  google.protobuf.Timestamp createdAt = 11;
  string pcName = 12;
  string agentAvatar = 13;
}

message GetAgentSecurityLogsCountRequest {}

message GetAgentSecurityLogsCountReply {
  int64 highRiskCount = 1; // 高风险数量
  int64 mediumRiskCount = 2; // 中风险数量
  int64 lowRiskCount = 3; // 低风险数量
  int64 blockedCount = 4; // 阻断数量
  int64 warningCount = 5; // 警告数量
}

message GetAgentSecurityLogDetailRequest {
  int64 id = 1;
}

message GetAgentSecurityLogDetailReply {
  int64 id = 1;
  // 1. 低风险 2. 中风险 3. 高风险
  int64 riskLevel = 2;
  // 1.阻断 2.警告
  int64 hitAction = 3;
  int64 userID = 4;
  string userName = 5;
  string deptName = 6;
  string userAvatar = 7;
  int64 agentID = 8;
  string agentName = 9;
  string agentAvatar = 16;
  // 1. 智能问答
  int64 actionCategory = 10;
  google.protobuf.Timestamp createdAt = 11;
  string pcName = 12;
  string question = 13;
  repeated string uploadedFiles = 14; // 上传的文件列表
  repeated SecurityPolicy securityPolicies = 15; // 命中的安全策略
  string agentDescription = 17; // 智能体描述
}

message GetUserAgentsAndKnowledgeBasesRequest {
  int64 userID = 1;
}

message GetUserAgentsAndKnowledgeBasesReply {
  message AgentInfo {
    int64 agentID = 1;
    string agentName = 2;
    string agentAvatar = 3;
  }

  message KnowledgeBaseInfo {
    int64 knowledgeBaseID = 1;
    string knowledgeBaseName = 2;
    // 知识库类型 1文档 2表格
    int32 dataType = 3;
  }

  repeated AgentInfo agents = 1;
  repeated KnowledgeBaseInfo knowledgeBases = 2;
}

message GenerateQuestionOptimizationRequest {
  string question = 1;
}

message GenerateQuestionOptimizationReply {
  repeated string questions = 1;
}

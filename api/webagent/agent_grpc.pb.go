// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: webagent/agent.proto

package webAgent

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Agent_CreateAgent_FullMethodName                             = "/api.webAgent.Agent/CreateAgent"
	Agent_GetAllAgentInfo_FullMethodName                         = "/api.webAgent.Agent/GetAllAgentInfo"
	Agent_UpdateAgent_FullMethodName                             = "/api.webAgent.Agent/UpdateAgent"
	Agent_DeleteAgent_FullMethodName                             = "/api.webAgent.Agent/DeleteAgent"
	Agent_GetAgent_FullMethodName                                = "/api.webAgent.Agent/GetAgent"
	Agent_PageAgent_FullMethodName                               = "/api.webAgent.Agent/PageAgent"
	Agent_GetDefaultAvatars_FullMethodName                       = "/api.webAgent.Agent/GetDefaultAvatars"
	Agent_GetFilePermissionByAgentID_FullMethodName              = "/api.webAgent.Agent/GetFilePermissionByAgentID"
	Agent_InternalModelChatPage_FullMethodName                   = "/api.webAgent.Agent/InternalModelChatPage"
	Agent_QueryModelAskClassificationDistribution_FullMethodName = "/api.webAgent.Agent/QueryModelAskClassificationDistribution"
	Agent_QueryUploadFileTypeDistribution_FullMethodName         = "/api.webAgent.Agent/QueryUploadFileTypeDistribution"
	Agent_QueryModelAskClassificationTop10_FullMethodName        = "/api.webAgent.Agent/QueryModelAskClassificationTop10"
	Agent_QueryUploadFileTypeTop10_FullMethodName                = "/api.webAgent.Agent/QueryUploadFileTypeTop10"
	Agent_SaveAgentQueueWhiteList_FullMethodName                 = "/api.webAgent.Agent/SaveAgentQueueWhiteList"
	Agent_GetAgentQueueWhiteList_FullMethodName                  = "/api.webAgent.Agent/GetAgentQueueWhiteList"
	Agent_UpdateAgentSort_FullMethodName                         = "/api.webAgent.Agent/UpdateAgentSort"
	Agent_TransferAgent_FullMethodName                           = "/api.webAgent.Agent/TransferAgent"
	Agent_CheckQuestionSecurity_FullMethodName                   = "/api.webAgent.Agent/CheckQuestionSecurity"
	Agent_PageAgentSecurityLogs_FullMethodName                   = "/api.webAgent.Agent/PageAgentSecurityLogs"
	Agent_GetAgentSecurityLogDetail_FullMethodName               = "/api.webAgent.Agent/GetAgentSecurityLogDetail"
	Agent_GetAgentSecurityLogsCount_FullMethodName               = "/api.webAgent.Agent/GetAgentSecurityLogsCount"
	Agent_GetUserAgentsAndKnowledgeBases_FullMethodName          = "/api.webAgent.Agent/GetUserAgentsAndKnowledgeBases"
	Agent_GenerateQuestionOptimization_FullMethodName            = "/api.webAgent.Agent/GenerateQuestionOptimization"
)

// AgentClient is the client API for Agent service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AgentClient interface {
	// 创建 Agent
	CreateAgent(ctx context.Context, in *CreateAgentRequest, opts ...grpc.CallOption) (*CreateAgentReply, error)
	// 查询所有Agent id/anme
	GetAllAgentInfo(ctx context.Context, in *GetAllAgentInfoRequest, opts ...grpc.CallOption) (*GetAllAgentInfoReply, error)
	// 更新 Agent
	UpdateAgent(ctx context.Context, in *UpdateAgentRequest, opts ...grpc.CallOption) (*UpdateAgentReply, error)
	// 删除 Agent
	DeleteAgent(ctx context.Context, in *DeleteAgentRequest, opts ...grpc.CallOption) (*DeleteAgentReply, error)
	// 查询 Agent
	GetAgent(ctx context.Context, in *GetAgentRequest, opts ...grpc.CallOption) (*GetAgentReply, error)
	// 分页 Agent
	PageAgent(ctx context.Context, in *PageAgentRequest, opts ...grpc.CallOption) (*PageAgentReply, error)
	// 获取智能体默认头像
	GetDefaultAvatars(ctx context.Context, in *GetDefaultAvatarsRequest, opts ...grpc.CallOption) (*GetDefaultAvatarsReply, error)
	// 判断用户是否有文件权限（关联智能体、知识库）
	GetFilePermissionByAgentID(ctx context.Context, in *GetFilePermissionByAgentIDRequest, opts ...grpc.CallOption) (*GetFilePermissionByAgentIDReply, error)
	// 内部模型聊天记录分页
	InternalModelChatPage(ctx context.Context, in *InternalModelChatPageRequest, opts ...grpc.CallOption) (*InternalModelChatPageReply, error)
	// 查询模型提问词分类分布
	QueryModelAskClassificationDistribution(ctx context.Context, in *QueryModelAskClassificationDistributionRequest, opts ...grpc.CallOption) (*QueryModelAskClassificationDistributionReply, error)
	// 查询上传附件文件类型分布
	QueryUploadFileTypeDistribution(ctx context.Context, in *QueryUploadFileTypeDistributionRequest, opts ...grpc.CallOption) (*QueryUploadFileTypeDistributionReply, error)
	// 查询模型提问词分类top10
	QueryModelAskClassificationTop10(ctx context.Context, in *QueryModelAskClassificationTop10Request, opts ...grpc.CallOption) (*QueryModelAskClassificationTop10Reply, error)
	// 查询上传附件文件类型top10
	QueryUploadFileTypeTop10(ctx context.Context, in *QueryUploadFileTypeTop10Request, opts ...grpc.CallOption) (*QueryUploadFileTypeTop10Reply, error)
	// 保存智能体排队白名单人员id列表
	SaveAgentQueueWhiteList(ctx context.Context, in *SaveAgentQueueWhiteListRequest, opts ...grpc.CallOption) (*SaveAgentQueueWhiteListReply, error)
	// 获取智能体排队白名单人员id列表
	GetAgentQueueWhiteList(ctx context.Context, in *GetAgentQueueWhiteListRequest, opts ...grpc.CallOption) (*GetAgentQueueWhiteListReply, error)
	// 更新智能体排序
	UpdateAgentSort(ctx context.Context, in *UpdateAgentSortRequest, opts ...grpc.CallOption) (*UpdateAgentSortReply, error)
	// 转让智能体
	TransferAgent(ctx context.Context, in *TransferAgentRequest, opts ...grpc.CallOption) (*TransferAgentReply, error)
	// 校验问题安全
	CheckQuestionSecurity(ctx context.Context, in *CheckQuestionSecurityRequest, opts ...grpc.CallOption) (*CheckQuestionSecurityReply, error)
	// 分页智能体安全策略数据管控
	PageAgentSecurityLogs(ctx context.Context, in *PageAgentSecurityLogsRequest, opts ...grpc.CallOption) (*PageAgentSecurityLogsReply, error)
	// 获取智能体安全策略数据管控详情
	GetAgentSecurityLogDetail(ctx context.Context, in *GetAgentSecurityLogDetailRequest, opts ...grpc.CallOption) (*GetAgentSecurityLogDetailReply, error)
	// 获取智能体安全策略数据管控数量
	GetAgentSecurityLogsCount(ctx context.Context, in *GetAgentSecurityLogsCountRequest, opts ...grpc.CallOption) (*GetAgentSecurityLogsCountReply, error)
	// 获取用户智能体列表、知识库列表
	GetUserAgentsAndKnowledgeBases(ctx context.Context, in *GetUserAgentsAndKnowledgeBasesRequest, opts ...grpc.CallOption) (*GetUserAgentsAndKnowledgeBasesReply, error)
	// 根据问题推荐问题
	GenerateQuestionOptimization(ctx context.Context, in *GenerateQuestionOptimizationRequest, opts ...grpc.CallOption) (*GenerateQuestionOptimizationReply, error)
}

type agentClient struct {
	cc grpc.ClientConnInterface
}

func NewAgentClient(cc grpc.ClientConnInterface) AgentClient {
	return &agentClient{cc}
}

func (c *agentClient) CreateAgent(ctx context.Context, in *CreateAgentRequest, opts ...grpc.CallOption) (*CreateAgentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAgentReply)
	err := c.cc.Invoke(ctx, Agent_CreateAgent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) GetAllAgentInfo(ctx context.Context, in *GetAllAgentInfoRequest, opts ...grpc.CallOption) (*GetAllAgentInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllAgentInfoReply)
	err := c.cc.Invoke(ctx, Agent_GetAllAgentInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) UpdateAgent(ctx context.Context, in *UpdateAgentRequest, opts ...grpc.CallOption) (*UpdateAgentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateAgentReply)
	err := c.cc.Invoke(ctx, Agent_UpdateAgent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) DeleteAgent(ctx context.Context, in *DeleteAgentRequest, opts ...grpc.CallOption) (*DeleteAgentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteAgentReply)
	err := c.cc.Invoke(ctx, Agent_DeleteAgent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) GetAgent(ctx context.Context, in *GetAgentRequest, opts ...grpc.CallOption) (*GetAgentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAgentReply)
	err := c.cc.Invoke(ctx, Agent_GetAgent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) PageAgent(ctx context.Context, in *PageAgentRequest, opts ...grpc.CallOption) (*PageAgentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PageAgentReply)
	err := c.cc.Invoke(ctx, Agent_PageAgent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) GetDefaultAvatars(ctx context.Context, in *GetDefaultAvatarsRequest, opts ...grpc.CallOption) (*GetDefaultAvatarsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDefaultAvatarsReply)
	err := c.cc.Invoke(ctx, Agent_GetDefaultAvatars_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) GetFilePermissionByAgentID(ctx context.Context, in *GetFilePermissionByAgentIDRequest, opts ...grpc.CallOption) (*GetFilePermissionByAgentIDReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFilePermissionByAgentIDReply)
	err := c.cc.Invoke(ctx, Agent_GetFilePermissionByAgentID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) InternalModelChatPage(ctx context.Context, in *InternalModelChatPageRequest, opts ...grpc.CallOption) (*InternalModelChatPageReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InternalModelChatPageReply)
	err := c.cc.Invoke(ctx, Agent_InternalModelChatPage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) QueryModelAskClassificationDistribution(ctx context.Context, in *QueryModelAskClassificationDistributionRequest, opts ...grpc.CallOption) (*QueryModelAskClassificationDistributionReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryModelAskClassificationDistributionReply)
	err := c.cc.Invoke(ctx, Agent_QueryModelAskClassificationDistribution_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) QueryUploadFileTypeDistribution(ctx context.Context, in *QueryUploadFileTypeDistributionRequest, opts ...grpc.CallOption) (*QueryUploadFileTypeDistributionReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryUploadFileTypeDistributionReply)
	err := c.cc.Invoke(ctx, Agent_QueryUploadFileTypeDistribution_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) QueryModelAskClassificationTop10(ctx context.Context, in *QueryModelAskClassificationTop10Request, opts ...grpc.CallOption) (*QueryModelAskClassificationTop10Reply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryModelAskClassificationTop10Reply)
	err := c.cc.Invoke(ctx, Agent_QueryModelAskClassificationTop10_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) QueryUploadFileTypeTop10(ctx context.Context, in *QueryUploadFileTypeTop10Request, opts ...grpc.CallOption) (*QueryUploadFileTypeTop10Reply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(QueryUploadFileTypeTop10Reply)
	err := c.cc.Invoke(ctx, Agent_QueryUploadFileTypeTop10_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) SaveAgentQueueWhiteList(ctx context.Context, in *SaveAgentQueueWhiteListRequest, opts ...grpc.CallOption) (*SaveAgentQueueWhiteListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SaveAgentQueueWhiteListReply)
	err := c.cc.Invoke(ctx, Agent_SaveAgentQueueWhiteList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) GetAgentQueueWhiteList(ctx context.Context, in *GetAgentQueueWhiteListRequest, opts ...grpc.CallOption) (*GetAgentQueueWhiteListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAgentQueueWhiteListReply)
	err := c.cc.Invoke(ctx, Agent_GetAgentQueueWhiteList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) UpdateAgentSort(ctx context.Context, in *UpdateAgentSortRequest, opts ...grpc.CallOption) (*UpdateAgentSortReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateAgentSortReply)
	err := c.cc.Invoke(ctx, Agent_UpdateAgentSort_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) TransferAgent(ctx context.Context, in *TransferAgentRequest, opts ...grpc.CallOption) (*TransferAgentReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TransferAgentReply)
	err := c.cc.Invoke(ctx, Agent_TransferAgent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) CheckQuestionSecurity(ctx context.Context, in *CheckQuestionSecurityRequest, opts ...grpc.CallOption) (*CheckQuestionSecurityReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckQuestionSecurityReply)
	err := c.cc.Invoke(ctx, Agent_CheckQuestionSecurity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) PageAgentSecurityLogs(ctx context.Context, in *PageAgentSecurityLogsRequest, opts ...grpc.CallOption) (*PageAgentSecurityLogsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PageAgentSecurityLogsReply)
	err := c.cc.Invoke(ctx, Agent_PageAgentSecurityLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) GetAgentSecurityLogDetail(ctx context.Context, in *GetAgentSecurityLogDetailRequest, opts ...grpc.CallOption) (*GetAgentSecurityLogDetailReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAgentSecurityLogDetailReply)
	err := c.cc.Invoke(ctx, Agent_GetAgentSecurityLogDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) GetAgentSecurityLogsCount(ctx context.Context, in *GetAgentSecurityLogsCountRequest, opts ...grpc.CallOption) (*GetAgentSecurityLogsCountReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAgentSecurityLogsCountReply)
	err := c.cc.Invoke(ctx, Agent_GetAgentSecurityLogsCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) GetUserAgentsAndKnowledgeBases(ctx context.Context, in *GetUserAgentsAndKnowledgeBasesRequest, opts ...grpc.CallOption) (*GetUserAgentsAndKnowledgeBasesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserAgentsAndKnowledgeBasesReply)
	err := c.cc.Invoke(ctx, Agent_GetUserAgentsAndKnowledgeBases_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *agentClient) GenerateQuestionOptimization(ctx context.Context, in *GenerateQuestionOptimizationRequest, opts ...grpc.CallOption) (*GenerateQuestionOptimizationReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateQuestionOptimizationReply)
	err := c.cc.Invoke(ctx, Agent_GenerateQuestionOptimization_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AgentServer is the server API for Agent service.
// All implementations must embed UnimplementedAgentServer
// for forward compatibility.
type AgentServer interface {
	// 创建 Agent
	CreateAgent(context.Context, *CreateAgentRequest) (*CreateAgentReply, error)
	// 查询所有Agent id/anme
	GetAllAgentInfo(context.Context, *GetAllAgentInfoRequest) (*GetAllAgentInfoReply, error)
	// 更新 Agent
	UpdateAgent(context.Context, *UpdateAgentRequest) (*UpdateAgentReply, error)
	// 删除 Agent
	DeleteAgent(context.Context, *DeleteAgentRequest) (*DeleteAgentReply, error)
	// 查询 Agent
	GetAgent(context.Context, *GetAgentRequest) (*GetAgentReply, error)
	// 分页 Agent
	PageAgent(context.Context, *PageAgentRequest) (*PageAgentReply, error)
	// 获取智能体默认头像
	GetDefaultAvatars(context.Context, *GetDefaultAvatarsRequest) (*GetDefaultAvatarsReply, error)
	// 判断用户是否有文件权限（关联智能体、知识库）
	GetFilePermissionByAgentID(context.Context, *GetFilePermissionByAgentIDRequest) (*GetFilePermissionByAgentIDReply, error)
	// 内部模型聊天记录分页
	InternalModelChatPage(context.Context, *InternalModelChatPageRequest) (*InternalModelChatPageReply, error)
	// 查询模型提问词分类分布
	QueryModelAskClassificationDistribution(context.Context, *QueryModelAskClassificationDistributionRequest) (*QueryModelAskClassificationDistributionReply, error)
	// 查询上传附件文件类型分布
	QueryUploadFileTypeDistribution(context.Context, *QueryUploadFileTypeDistributionRequest) (*QueryUploadFileTypeDistributionReply, error)
	// 查询模型提问词分类top10
	QueryModelAskClassificationTop10(context.Context, *QueryModelAskClassificationTop10Request) (*QueryModelAskClassificationTop10Reply, error)
	// 查询上传附件文件类型top10
	QueryUploadFileTypeTop10(context.Context, *QueryUploadFileTypeTop10Request) (*QueryUploadFileTypeTop10Reply, error)
	// 保存智能体排队白名单人员id列表
	SaveAgentQueueWhiteList(context.Context, *SaveAgentQueueWhiteListRequest) (*SaveAgentQueueWhiteListReply, error)
	// 获取智能体排队白名单人员id列表
	GetAgentQueueWhiteList(context.Context, *GetAgentQueueWhiteListRequest) (*GetAgentQueueWhiteListReply, error)
	// 更新智能体排序
	UpdateAgentSort(context.Context, *UpdateAgentSortRequest) (*UpdateAgentSortReply, error)
	// 转让智能体
	TransferAgent(context.Context, *TransferAgentRequest) (*TransferAgentReply, error)
	// 校验问题安全
	CheckQuestionSecurity(context.Context, *CheckQuestionSecurityRequest) (*CheckQuestionSecurityReply, error)
	// 分页智能体安全策略数据管控
	PageAgentSecurityLogs(context.Context, *PageAgentSecurityLogsRequest) (*PageAgentSecurityLogsReply, error)
	// 获取智能体安全策略数据管控详情
	GetAgentSecurityLogDetail(context.Context, *GetAgentSecurityLogDetailRequest) (*GetAgentSecurityLogDetailReply, error)
	// 获取智能体安全策略数据管控数量
	GetAgentSecurityLogsCount(context.Context, *GetAgentSecurityLogsCountRequest) (*GetAgentSecurityLogsCountReply, error)
	// 获取用户智能体列表、知识库列表
	GetUserAgentsAndKnowledgeBases(context.Context, *GetUserAgentsAndKnowledgeBasesRequest) (*GetUserAgentsAndKnowledgeBasesReply, error)
	// 根据问题推荐问题
	GenerateQuestionOptimization(context.Context, *GenerateQuestionOptimizationRequest) (*GenerateQuestionOptimizationReply, error)
	mustEmbedUnimplementedAgentServer()
}

// UnimplementedAgentServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAgentServer struct{}

func (UnimplementedAgentServer) CreateAgent(context.Context, *CreateAgentRequest) (*CreateAgentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAgent not implemented")
}
func (UnimplementedAgentServer) GetAllAgentInfo(context.Context, *GetAllAgentInfoRequest) (*GetAllAgentInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllAgentInfo not implemented")
}
func (UnimplementedAgentServer) UpdateAgent(context.Context, *UpdateAgentRequest) (*UpdateAgentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAgent not implemented")
}
func (UnimplementedAgentServer) DeleteAgent(context.Context, *DeleteAgentRequest) (*DeleteAgentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAgent not implemented")
}
func (UnimplementedAgentServer) GetAgent(context.Context, *GetAgentRequest) (*GetAgentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgent not implemented")
}
func (UnimplementedAgentServer) PageAgent(context.Context, *PageAgentRequest) (*PageAgentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PageAgent not implemented")
}
func (UnimplementedAgentServer) GetDefaultAvatars(context.Context, *GetDefaultAvatarsRequest) (*GetDefaultAvatarsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDefaultAvatars not implemented")
}
func (UnimplementedAgentServer) GetFilePermissionByAgentID(context.Context, *GetFilePermissionByAgentIDRequest) (*GetFilePermissionByAgentIDReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFilePermissionByAgentID not implemented")
}
func (UnimplementedAgentServer) InternalModelChatPage(context.Context, *InternalModelChatPageRequest) (*InternalModelChatPageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InternalModelChatPage not implemented")
}
func (UnimplementedAgentServer) QueryModelAskClassificationDistribution(context.Context, *QueryModelAskClassificationDistributionRequest) (*QueryModelAskClassificationDistributionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryModelAskClassificationDistribution not implemented")
}
func (UnimplementedAgentServer) QueryUploadFileTypeDistribution(context.Context, *QueryUploadFileTypeDistributionRequest) (*QueryUploadFileTypeDistributionReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUploadFileTypeDistribution not implemented")
}
func (UnimplementedAgentServer) QueryModelAskClassificationTop10(context.Context, *QueryModelAskClassificationTop10Request) (*QueryModelAskClassificationTop10Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryModelAskClassificationTop10 not implemented")
}
func (UnimplementedAgentServer) QueryUploadFileTypeTop10(context.Context, *QueryUploadFileTypeTop10Request) (*QueryUploadFileTypeTop10Reply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QueryUploadFileTypeTop10 not implemented")
}
func (UnimplementedAgentServer) SaveAgentQueueWhiteList(context.Context, *SaveAgentQueueWhiteListRequest) (*SaveAgentQueueWhiteListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveAgentQueueWhiteList not implemented")
}
func (UnimplementedAgentServer) GetAgentQueueWhiteList(context.Context, *GetAgentQueueWhiteListRequest) (*GetAgentQueueWhiteListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgentQueueWhiteList not implemented")
}
func (UnimplementedAgentServer) UpdateAgentSort(context.Context, *UpdateAgentSortRequest) (*UpdateAgentSortReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAgentSort not implemented")
}
func (UnimplementedAgentServer) TransferAgent(context.Context, *TransferAgentRequest) (*TransferAgentReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TransferAgent not implemented")
}
func (UnimplementedAgentServer) CheckQuestionSecurity(context.Context, *CheckQuestionSecurityRequest) (*CheckQuestionSecurityReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckQuestionSecurity not implemented")
}
func (UnimplementedAgentServer) PageAgentSecurityLogs(context.Context, *PageAgentSecurityLogsRequest) (*PageAgentSecurityLogsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PageAgentSecurityLogs not implemented")
}
func (UnimplementedAgentServer) GetAgentSecurityLogDetail(context.Context, *GetAgentSecurityLogDetailRequest) (*GetAgentSecurityLogDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgentSecurityLogDetail not implemented")
}
func (UnimplementedAgentServer) GetAgentSecurityLogsCount(context.Context, *GetAgentSecurityLogsCountRequest) (*GetAgentSecurityLogsCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAgentSecurityLogsCount not implemented")
}
func (UnimplementedAgentServer) GetUserAgentsAndKnowledgeBases(context.Context, *GetUserAgentsAndKnowledgeBasesRequest) (*GetUserAgentsAndKnowledgeBasesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserAgentsAndKnowledgeBases not implemented")
}
func (UnimplementedAgentServer) GenerateQuestionOptimization(context.Context, *GenerateQuestionOptimizationRequest) (*GenerateQuestionOptimizationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateQuestionOptimization not implemented")
}
func (UnimplementedAgentServer) mustEmbedUnimplementedAgentServer() {}
func (UnimplementedAgentServer) testEmbeddedByValue()               {}

// UnsafeAgentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AgentServer will
// result in compilation errors.
type UnsafeAgentServer interface {
	mustEmbedUnimplementedAgentServer()
}

func RegisterAgentServer(s grpc.ServiceRegistrar, srv AgentServer) {
	// If the following call pancis, it indicates UnimplementedAgentServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Agent_ServiceDesc, srv)
}

func _Agent_CreateAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).CreateAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_CreateAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).CreateAgent(ctx, req.(*CreateAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_GetAllAgentInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllAgentInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).GetAllAgentInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_GetAllAgentInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).GetAllAgentInfo(ctx, req.(*GetAllAgentInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_UpdateAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).UpdateAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_UpdateAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).UpdateAgent(ctx, req.(*UpdateAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_DeleteAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).DeleteAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_DeleteAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).DeleteAgent(ctx, req.(*DeleteAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_GetAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).GetAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_GetAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).GetAgent(ctx, req.(*GetAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_PageAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).PageAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_PageAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).PageAgent(ctx, req.(*PageAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_GetDefaultAvatars_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDefaultAvatarsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).GetDefaultAvatars(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_GetDefaultAvatars_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).GetDefaultAvatars(ctx, req.(*GetDefaultAvatarsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_GetFilePermissionByAgentID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFilePermissionByAgentIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).GetFilePermissionByAgentID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_GetFilePermissionByAgentID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).GetFilePermissionByAgentID(ctx, req.(*GetFilePermissionByAgentIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_InternalModelChatPage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InternalModelChatPageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).InternalModelChatPage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_InternalModelChatPage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).InternalModelChatPage(ctx, req.(*InternalModelChatPageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_QueryModelAskClassificationDistribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryModelAskClassificationDistributionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).QueryModelAskClassificationDistribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_QueryModelAskClassificationDistribution_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).QueryModelAskClassificationDistribution(ctx, req.(*QueryModelAskClassificationDistributionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_QueryUploadFileTypeDistribution_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryUploadFileTypeDistributionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).QueryUploadFileTypeDistribution(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_QueryUploadFileTypeDistribution_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).QueryUploadFileTypeDistribution(ctx, req.(*QueryUploadFileTypeDistributionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_QueryModelAskClassificationTop10_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryModelAskClassificationTop10Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).QueryModelAskClassificationTop10(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_QueryModelAskClassificationTop10_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).QueryModelAskClassificationTop10(ctx, req.(*QueryModelAskClassificationTop10Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_QueryUploadFileTypeTop10_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QueryUploadFileTypeTop10Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).QueryUploadFileTypeTop10(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_QueryUploadFileTypeTop10_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).QueryUploadFileTypeTop10(ctx, req.(*QueryUploadFileTypeTop10Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_SaveAgentQueueWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveAgentQueueWhiteListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).SaveAgentQueueWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_SaveAgentQueueWhiteList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).SaveAgentQueueWhiteList(ctx, req.(*SaveAgentQueueWhiteListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_GetAgentQueueWhiteList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgentQueueWhiteListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).GetAgentQueueWhiteList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_GetAgentQueueWhiteList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).GetAgentQueueWhiteList(ctx, req.(*GetAgentQueueWhiteListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_UpdateAgentSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAgentSortRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).UpdateAgentSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_UpdateAgentSort_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).UpdateAgentSort(ctx, req.(*UpdateAgentSortRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_TransferAgent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TransferAgentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).TransferAgent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_TransferAgent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).TransferAgent(ctx, req.(*TransferAgentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_CheckQuestionSecurity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckQuestionSecurityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).CheckQuestionSecurity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_CheckQuestionSecurity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).CheckQuestionSecurity(ctx, req.(*CheckQuestionSecurityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_PageAgentSecurityLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageAgentSecurityLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).PageAgentSecurityLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_PageAgentSecurityLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).PageAgentSecurityLogs(ctx, req.(*PageAgentSecurityLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_GetAgentSecurityLogDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgentSecurityLogDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).GetAgentSecurityLogDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_GetAgentSecurityLogDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).GetAgentSecurityLogDetail(ctx, req.(*GetAgentSecurityLogDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_GetAgentSecurityLogsCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAgentSecurityLogsCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).GetAgentSecurityLogsCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_GetAgentSecurityLogsCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).GetAgentSecurityLogsCount(ctx, req.(*GetAgentSecurityLogsCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_GetUserAgentsAndKnowledgeBases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAgentsAndKnowledgeBasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).GetUserAgentsAndKnowledgeBases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_GetUserAgentsAndKnowledgeBases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).GetUserAgentsAndKnowledgeBases(ctx, req.(*GetUserAgentsAndKnowledgeBasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Agent_GenerateQuestionOptimization_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateQuestionOptimizationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AgentServer).GenerateQuestionOptimization(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Agent_GenerateQuestionOptimization_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AgentServer).GenerateQuestionOptimization(ctx, req.(*GenerateQuestionOptimizationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Agent_ServiceDesc is the grpc.ServiceDesc for Agent service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Agent_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.webAgent.Agent",
	HandlerType: (*AgentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAgent",
			Handler:    _Agent_CreateAgent_Handler,
		},
		{
			MethodName: "GetAllAgentInfo",
			Handler:    _Agent_GetAllAgentInfo_Handler,
		},
		{
			MethodName: "UpdateAgent",
			Handler:    _Agent_UpdateAgent_Handler,
		},
		{
			MethodName: "DeleteAgent",
			Handler:    _Agent_DeleteAgent_Handler,
		},
		{
			MethodName: "GetAgent",
			Handler:    _Agent_GetAgent_Handler,
		},
		{
			MethodName: "PageAgent",
			Handler:    _Agent_PageAgent_Handler,
		},
		{
			MethodName: "GetDefaultAvatars",
			Handler:    _Agent_GetDefaultAvatars_Handler,
		},
		{
			MethodName: "GetFilePermissionByAgentID",
			Handler:    _Agent_GetFilePermissionByAgentID_Handler,
		},
		{
			MethodName: "InternalModelChatPage",
			Handler:    _Agent_InternalModelChatPage_Handler,
		},
		{
			MethodName: "QueryModelAskClassificationDistribution",
			Handler:    _Agent_QueryModelAskClassificationDistribution_Handler,
		},
		{
			MethodName: "QueryUploadFileTypeDistribution",
			Handler:    _Agent_QueryUploadFileTypeDistribution_Handler,
		},
		{
			MethodName: "QueryModelAskClassificationTop10",
			Handler:    _Agent_QueryModelAskClassificationTop10_Handler,
		},
		{
			MethodName: "QueryUploadFileTypeTop10",
			Handler:    _Agent_QueryUploadFileTypeTop10_Handler,
		},
		{
			MethodName: "SaveAgentQueueWhiteList",
			Handler:    _Agent_SaveAgentQueueWhiteList_Handler,
		},
		{
			MethodName: "GetAgentQueueWhiteList",
			Handler:    _Agent_GetAgentQueueWhiteList_Handler,
		},
		{
			MethodName: "UpdateAgentSort",
			Handler:    _Agent_UpdateAgentSort_Handler,
		},
		{
			MethodName: "TransferAgent",
			Handler:    _Agent_TransferAgent_Handler,
		},
		{
			MethodName: "CheckQuestionSecurity",
			Handler:    _Agent_CheckQuestionSecurity_Handler,
		},
		{
			MethodName: "PageAgentSecurityLogs",
			Handler:    _Agent_PageAgentSecurityLogs_Handler,
		},
		{
			MethodName: "GetAgentSecurityLogDetail",
			Handler:    _Agent_GetAgentSecurityLogDetail_Handler,
		},
		{
			MethodName: "GetAgentSecurityLogsCount",
			Handler:    _Agent_GetAgentSecurityLogsCount_Handler,
		},
		{
			MethodName: "GetUserAgentsAndKnowledgeBases",
			Handler:    _Agent_GetUserAgentsAndKnowledgeBases_Handler,
		},
		{
			MethodName: "GenerateQuestionOptimization",
			Handler:    _Agent_GenerateQuestionOptimization_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "webagent/agent.proto",
}

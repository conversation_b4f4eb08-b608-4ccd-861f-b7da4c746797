// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package webAgent

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

func IsErrorCallAiApi(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ERROR_CALL_AI_API.String() && e.Code == 500
}

func ErrorErrorCallAiApi(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_ERROR_CALL_AI_API.String(), fmt.Sprintf(format, args...))
}

func IsErrorCallAiApiReceive(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ERROR_CALL_AI_API_RECEIVE.String() && e.Code == 500
}

func ErrorErrorCallAiApiReceive(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_ERROR_CALL_AI_API_RECEIVE.String(), fmt.Sprintf(format, args...))
}

func IsErrorAgentNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ERROR_AGENT_NOT_FOUND.String() && e.Code == 500
}

func ErrorErrorAgentNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_ERROR_AGENT_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

func IsErrorModelNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_ERROR_MODEL_NOT_FOUND.String() && e.Code == 500
}

func ErrorErrorModelNotFound(format string, args ...interface{}) *errors.Error {
	return errors.New(500, ErrorReason_ERROR_MODEL_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

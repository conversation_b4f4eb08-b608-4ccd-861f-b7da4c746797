// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: health/health.proto

package health

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationHealthSayHello = "/api.health.Health/SayHello"
const OperationHealthTrace = "/api.health.Health/Trace"

type HealthHTTPServer interface {
	SayHello(context.Context, *HealthRequest) (*HealthReply, error)
	Trace(context.Context, *TraceRequest) (*TraceReply, error)
}

func RegisterHealthHTTPServer(s *http.Server, srv HealthHTTPServer) {
	r := s.Route("/")
	r.GET("/health/check", _Health_SayHello0_HTTP_Handler(srv))
	r.GET("/health/trace", _Health_Trace0_HTTP_Handler(srv))
}

func _Health_SayHello0_HTTP_Handler(srv HealthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in HealthRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHealthSayHello)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SayHello(ctx, req.(*HealthRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*HealthReply)
		return ctx.Result(200, reply)
	}
}

func _Health_Trace0_HTTP_Handler(srv HealthHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TraceRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationHealthTrace)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Trace(ctx, req.(*TraceRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TraceReply)
		return ctx.Result(200, reply)
	}
}

type HealthHTTPClient interface {
	SayHello(ctx context.Context, req *HealthRequest, opts ...http.CallOption) (rsp *HealthReply, err error)
	Trace(ctx context.Context, req *TraceRequest, opts ...http.CallOption) (rsp *TraceReply, err error)
}

type HealthHTTPClientImpl struct {
	cc *http.Client
}

func NewHealthHTTPClient(client *http.Client) HealthHTTPClient {
	return &HealthHTTPClientImpl{client}
}

func (c *HealthHTTPClientImpl) SayHello(ctx context.Context, in *HealthRequest, opts ...http.CallOption) (*HealthReply, error) {
	var out HealthReply
	pattern := "/health/check"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHealthSayHello))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *HealthHTTPClientImpl) Trace(ctx context.Context, in *TraceRequest, opts ...http.CallOption) (*TraceReply, error) {
	var out TraceReply
	pattern := "/health/trace"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationHealthTrace))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

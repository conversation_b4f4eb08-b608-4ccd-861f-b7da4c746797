// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: health/health.proto

package health

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on HealthRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HealthRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HealthRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HealthRequestMultiError, or
// nil if none found.
func (m *HealthRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *HealthRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return HealthRequestMultiError(errors)
	}

	return nil
}

// HealthRequestMultiError is an error wrapping multiple validation errors
// returned by HealthRequest.ValidateAll() if the designated constraints
// aren't met.
type HealthRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HealthRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HealthRequestMultiError) AllErrors() []error { return m }

// HealthRequestValidationError is the validation error returned by
// HealthRequest.Validate if the designated constraints aren't met.
type HealthRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HealthRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HealthRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HealthRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HealthRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HealthRequestValidationError) ErrorName() string { return "HealthRequestValidationError" }

// Error satisfies the builtin error interface
func (e HealthRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHealthRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HealthRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HealthRequestValidationError{}

// Validate checks the field values on HealthReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HealthReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HealthReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HealthReplyMultiError, or
// nil if none found.
func (m *HealthReply) ValidateAll() error {
	return m.validate(true)
}

func (m *HealthReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Message

	if len(errors) > 0 {
		return HealthReplyMultiError(errors)
	}

	return nil
}

// HealthReplyMultiError is an error wrapping multiple validation errors
// returned by HealthReply.ValidateAll() if the designated constraints aren't met.
type HealthReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HealthReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HealthReplyMultiError) AllErrors() []error { return m }

// HealthReplyValidationError is the validation error returned by
// HealthReply.Validate if the designated constraints aren't met.
type HealthReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HealthReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HealthReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HealthReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HealthReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HealthReplyValidationError) ErrorName() string { return "HealthReplyValidationError" }

// Error satisfies the builtin error interface
func (e HealthReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHealthReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HealthReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HealthReplyValidationError{}

// Validate checks the field values on TraceRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TraceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TraceRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TraceRequestMultiError, or
// nil if none found.
func (m *TraceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TraceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageNum() <= 0 {
		err := TraceRequestValidationError{
			field:  "PageNum",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val <= 0 || val > 10000 {
		err := TraceRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range (0, 10000]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TraceID

	if len(errors) > 0 {
		return TraceRequestMultiError(errors)
	}

	return nil
}

// TraceRequestMultiError is an error wrapping multiple validation errors
// returned by TraceRequest.ValidateAll() if the designated constraints aren't met.
type TraceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TraceRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TraceRequestMultiError) AllErrors() []error { return m }

// TraceRequestValidationError is the validation error returned by
// TraceRequest.Validate if the designated constraints aren't met.
type TraceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TraceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TraceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TraceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TraceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TraceRequestValidationError) ErrorName() string { return "TraceRequestValidationError" }

// Error satisfies the builtin error interface
func (e TraceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTraceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TraceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TraceRequestValidationError{}

// Validate checks the field values on TraceReply with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TraceReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TraceReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TraceReplyMultiError, or
// nil if none found.
func (m *TraceReply) ValidateAll() error {
	return m.validate(true)
}

func (m *TraceReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Trace

	// no validation rules for Logs

	if len(errors) > 0 {
		return TraceReplyMultiError(errors)
	}

	return nil
}

// TraceReplyMultiError is an error wrapping multiple validation errors
// returned by TraceReply.ValidateAll() if the designated constraints aren't met.
type TraceReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TraceReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TraceReplyMultiError) AllErrors() []error { return m }

// TraceReplyValidationError is the validation error returned by
// TraceReply.Validate if the designated constraints aren't met.
type TraceReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TraceReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TraceReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TraceReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TraceReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TraceReplyValidationError) ErrorName() string { return "TraceReplyValidationError" }

// Error satisfies the builtin error interface
func (e TraceReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTraceReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TraceReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TraceReplyValidationError{}

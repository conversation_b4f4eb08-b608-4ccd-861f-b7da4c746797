syntax = "proto3";

package api.health;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/any.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/health;health";

service Health {
  rpc SayHello(HealthRequest) returns (HealthReply) {
    option (google.api.http) = {get: "/health/check"};
  }

  rpc Trace(TraceRequest) returns (TraceReply) {
    option (google.api.http) = {get: "/health/trace"};
  }
}

message HealthRequest {}

message HealthReply {
  string message = 1;
}

message TraceRequest {
  int64 pageNum = 1 [(validate.rules).int64 = {gt: 0}];
  int64 pageSize = 2 [(validate.rules).int64 = {
    gt: 0
    lte: 10000
  }];
  string traceID = 3;
}

message TraceReply {
  string trace = 1;
  string logs = 2;
}

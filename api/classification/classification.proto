syntax = "proto3";

package api.classification;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/classification;classification";
option java_multiple_files = true;
option java_package = "api.classification";

service Classification {
  // 查询所有分类及文件数量
  rpc GetAllClassification(GetAllClassificationRequest) returns (GetAllClassificationReply) {
    option (google.api.http) = {get: "/classification/all"};
  }

  // 查询文件分类信息
  rpc GetFileClassificationTags(GetFileClassificationTagsRequest) returns (GetFileClassificationTagsReply) {
    option (google.api.http) = {
      post: "/classification/fileTags"
      body: "*"
    };
  }
}

message ClassificationFinishEvent {
  int64 fileRelationID = 1;
  string preEntityTag = 2;
  string entityTag = 3;
  string fileName = 4;
  int32 securityLevel = 5;
  repeated string tags = 6;
  string path = 7;
  int64 userID = 8;
  int64 tenantID = 9;
}

message GetAllClassificationRequest {
  string tagName = 1;
}

message GetAllClassificationReply {
  repeated ClassificationTag classificationTags = 1; // 一级分类列表
}

// 一级分类的消息
message ClassificationTag {
  int64 id = 1; // 一级分类ID
  string name = 2; // 一级分类名称
  repeated SubClassificationTag subClassificationTags = 4; // 二级分类列表
}

// 二级分类的消息
message SubClassificationTag {
  int64 id = 1; // 二级分类ID
  string name = 2; // 二级分类名称
}

message GetFileClassificationTagsRequest {
  repeated GetFileClassificationTagsRequestItem item = 1;
}

message GetFileClassificationTagsRequestItem {
  int64 fileRelationID = 1;
  string preEntityTag = 2;
  string entityTag = 3;
}

message GetFileClassificationTagsReply {
  repeated ClassificationFileTag classificationFileTags = 1;
}

message ClassificationFileTag {
  int64 id = 1;
  int64 fileRelationID = 2;
  string preEntityTag = 3;
  string entityTag = 4;
  string fileName = 5;
  string fileType = 6;
  int64 userID = 7;
  string userName = 8;
  int64 deptID = 9;
  string deptName = 10;
  string path = 11;
  repeated string tags = 12;
  int32 securityLevel = 13;
}

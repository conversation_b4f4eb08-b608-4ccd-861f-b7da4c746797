// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: classification/classification.proto

package classification

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ClassificationFinishEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClassificationFinishEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClassificationFinishEvent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClassificationFinishEventMultiError, or nil if none found.
func (m *ClassificationFinishEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *ClassificationFinishEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for PreEntityTag

	// no validation rules for EntityTag

	// no validation rules for FileName

	// no validation rules for SecurityLevel

	// no validation rules for Path

	// no validation rules for UserID

	// no validation rules for TenantID

	if len(errors) > 0 {
		return ClassificationFinishEventMultiError(errors)
	}

	return nil
}

// ClassificationFinishEventMultiError is an error wrapping multiple validation
// errors returned by ClassificationFinishEvent.ValidateAll() if the
// designated constraints aren't met.
type ClassificationFinishEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClassificationFinishEventMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClassificationFinishEventMultiError) AllErrors() []error { return m }

// ClassificationFinishEventValidationError is the validation error returned by
// ClassificationFinishEvent.Validate if the designated constraints aren't met.
type ClassificationFinishEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClassificationFinishEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClassificationFinishEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClassificationFinishEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClassificationFinishEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClassificationFinishEventValidationError) ErrorName() string {
	return "ClassificationFinishEventValidationError"
}

// Error satisfies the builtin error interface
func (e ClassificationFinishEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClassificationFinishEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClassificationFinishEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClassificationFinishEventValidationError{}

// Validate checks the field values on GetAllClassificationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllClassificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllClassificationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllClassificationRequestMultiError, or nil if none found.
func (m *GetAllClassificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllClassificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TagName

	if len(errors) > 0 {
		return GetAllClassificationRequestMultiError(errors)
	}

	return nil
}

// GetAllClassificationRequestMultiError is an error wrapping multiple
// validation errors returned by GetAllClassificationRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAllClassificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllClassificationRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllClassificationRequestMultiError) AllErrors() []error { return m }

// GetAllClassificationRequestValidationError is the validation error returned
// by GetAllClassificationRequest.Validate if the designated constraints
// aren't met.
type GetAllClassificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllClassificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllClassificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllClassificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllClassificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllClassificationRequestValidationError) ErrorName() string {
	return "GetAllClassificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllClassificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllClassificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllClassificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllClassificationRequestValidationError{}

// Validate checks the field values on GetAllClassificationReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllClassificationReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllClassificationReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllClassificationReplyMultiError, or nil if none found.
func (m *GetAllClassificationReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllClassificationReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClassificationTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllClassificationReplyValidationError{
						field:  fmt.Sprintf("ClassificationTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllClassificationReplyValidationError{
						field:  fmt.Sprintf("ClassificationTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllClassificationReplyValidationError{
					field:  fmt.Sprintf("ClassificationTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllClassificationReplyMultiError(errors)
	}

	return nil
}

// GetAllClassificationReplyMultiError is an error wrapping multiple validation
// errors returned by GetAllClassificationReply.ValidateAll() if the
// designated constraints aren't met.
type GetAllClassificationReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllClassificationReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllClassificationReplyMultiError) AllErrors() []error { return m }

// GetAllClassificationReplyValidationError is the validation error returned by
// GetAllClassificationReply.Validate if the designated constraints aren't met.
type GetAllClassificationReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllClassificationReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllClassificationReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllClassificationReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllClassificationReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllClassificationReplyValidationError) ErrorName() string {
	return "GetAllClassificationReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllClassificationReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllClassificationReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllClassificationReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllClassificationReplyValidationError{}

// Validate checks the field values on ClassificationTag with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ClassificationTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClassificationTag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClassificationTagMultiError, or nil if none found.
func (m *ClassificationTag) ValidateAll() error {
	return m.validate(true)
}

func (m *ClassificationTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	for idx, item := range m.GetSubClassificationTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ClassificationTagValidationError{
						field:  fmt.Sprintf("SubClassificationTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ClassificationTagValidationError{
						field:  fmt.Sprintf("SubClassificationTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ClassificationTagValidationError{
					field:  fmt.Sprintf("SubClassificationTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ClassificationTagMultiError(errors)
	}

	return nil
}

// ClassificationTagMultiError is an error wrapping multiple validation errors
// returned by ClassificationTag.ValidateAll() if the designated constraints
// aren't met.
type ClassificationTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClassificationTagMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClassificationTagMultiError) AllErrors() []error { return m }

// ClassificationTagValidationError is the validation error returned by
// ClassificationTag.Validate if the designated constraints aren't met.
type ClassificationTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClassificationTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClassificationTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClassificationTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClassificationTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClassificationTagValidationError) ErrorName() string {
	return "ClassificationTagValidationError"
}

// Error satisfies the builtin error interface
func (e ClassificationTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClassificationTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClassificationTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClassificationTagValidationError{}

// Validate checks the field values on SubClassificationTag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubClassificationTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubClassificationTag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubClassificationTagMultiError, or nil if none found.
func (m *SubClassificationTag) ValidateAll() error {
	return m.validate(true)
}

func (m *SubClassificationTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if len(errors) > 0 {
		return SubClassificationTagMultiError(errors)
	}

	return nil
}

// SubClassificationTagMultiError is an error wrapping multiple validation
// errors returned by SubClassificationTag.ValidateAll() if the designated
// constraints aren't met.
type SubClassificationTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubClassificationTagMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubClassificationTagMultiError) AllErrors() []error { return m }

// SubClassificationTagValidationError is the validation error returned by
// SubClassificationTag.Validate if the designated constraints aren't met.
type SubClassificationTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubClassificationTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubClassificationTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubClassificationTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubClassificationTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubClassificationTagValidationError) ErrorName() string {
	return "SubClassificationTagValidationError"
}

// Error satisfies the builtin error interface
func (e SubClassificationTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubClassificationTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubClassificationTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubClassificationTagValidationError{}

// Validate checks the field values on GetFileClassificationTagsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetFileClassificationTagsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileClassificationTagsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFileClassificationTagsRequestMultiError, or nil if none found.
func (m *GetFileClassificationTagsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileClassificationTagsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItem() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFileClassificationTagsRequestValidationError{
						field:  fmt.Sprintf("Item[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFileClassificationTagsRequestValidationError{
						field:  fmt.Sprintf("Item[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFileClassificationTagsRequestValidationError{
					field:  fmt.Sprintf("Item[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFileClassificationTagsRequestMultiError(errors)
	}

	return nil
}

// GetFileClassificationTagsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetFileClassificationTagsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetFileClassificationTagsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileClassificationTagsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileClassificationTagsRequestMultiError) AllErrors() []error { return m }

// GetFileClassificationTagsRequestValidationError is the validation error
// returned by GetFileClassificationTagsRequest.Validate if the designated
// constraints aren't met.
type GetFileClassificationTagsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileClassificationTagsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileClassificationTagsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileClassificationTagsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileClassificationTagsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileClassificationTagsRequestValidationError) ErrorName() string {
	return "GetFileClassificationTagsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileClassificationTagsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileClassificationTagsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileClassificationTagsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileClassificationTagsRequestValidationError{}

// Validate checks the field values on GetFileClassificationTagsRequestItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetFileClassificationTagsRequestItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileClassificationTagsRequestItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetFileClassificationTagsRequestItemMultiError, or nil if none found.
func (m *GetFileClassificationTagsRequestItem) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileClassificationTagsRequestItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for PreEntityTag

	// no validation rules for EntityTag

	if len(errors) > 0 {
		return GetFileClassificationTagsRequestItemMultiError(errors)
	}

	return nil
}

// GetFileClassificationTagsRequestItemMultiError is an error wrapping multiple
// validation errors returned by
// GetFileClassificationTagsRequestItem.ValidateAll() if the designated
// constraints aren't met.
type GetFileClassificationTagsRequestItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileClassificationTagsRequestItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileClassificationTagsRequestItemMultiError) AllErrors() []error { return m }

// GetFileClassificationTagsRequestItemValidationError is the validation error
// returned by GetFileClassificationTagsRequestItem.Validate if the designated
// constraints aren't met.
type GetFileClassificationTagsRequestItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileClassificationTagsRequestItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileClassificationTagsRequestItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileClassificationTagsRequestItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileClassificationTagsRequestItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileClassificationTagsRequestItemValidationError) ErrorName() string {
	return "GetFileClassificationTagsRequestItemValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileClassificationTagsRequestItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileClassificationTagsRequestItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileClassificationTagsRequestItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileClassificationTagsRequestItemValidationError{}

// Validate checks the field values on GetFileClassificationTagsReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileClassificationTagsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileClassificationTagsReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFileClassificationTagsReplyMultiError, or nil if none found.
func (m *GetFileClassificationTagsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileClassificationTagsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClassificationFileTags() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetFileClassificationTagsReplyValidationError{
						field:  fmt.Sprintf("ClassificationFileTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetFileClassificationTagsReplyValidationError{
						field:  fmt.Sprintf("ClassificationFileTags[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetFileClassificationTagsReplyValidationError{
					field:  fmt.Sprintf("ClassificationFileTags[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetFileClassificationTagsReplyMultiError(errors)
	}

	return nil
}

// GetFileClassificationTagsReplyMultiError is an error wrapping multiple
// validation errors returned by GetFileClassificationTagsReply.ValidateAll()
// if the designated constraints aren't met.
type GetFileClassificationTagsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileClassificationTagsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileClassificationTagsReplyMultiError) AllErrors() []error { return m }

// GetFileClassificationTagsReplyValidationError is the validation error
// returned by GetFileClassificationTagsReply.Validate if the designated
// constraints aren't met.
type GetFileClassificationTagsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileClassificationTagsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileClassificationTagsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileClassificationTagsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileClassificationTagsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileClassificationTagsReplyValidationError) ErrorName() string {
	return "GetFileClassificationTagsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileClassificationTagsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileClassificationTagsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileClassificationTagsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileClassificationTagsReplyValidationError{}

// Validate checks the field values on ClassificationFileTag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClassificationFileTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClassificationFileTag with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClassificationFileTagMultiError, or nil if none found.
func (m *ClassificationFileTag) ValidateAll() error {
	return m.validate(true)
}

func (m *ClassificationFileTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for FileRelationID

	// no validation rules for PreEntityTag

	// no validation rules for EntityTag

	// no validation rules for FileName

	// no validation rules for FileType

	// no validation rules for UserID

	// no validation rules for UserName

	// no validation rules for DeptID

	// no validation rules for DeptName

	// no validation rules for Path

	// no validation rules for SecurityLevel

	if len(errors) > 0 {
		return ClassificationFileTagMultiError(errors)
	}

	return nil
}

// ClassificationFileTagMultiError is an error wrapping multiple validation
// errors returned by ClassificationFileTag.ValidateAll() if the designated
// constraints aren't met.
type ClassificationFileTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClassificationFileTagMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClassificationFileTagMultiError) AllErrors() []error { return m }

// ClassificationFileTagValidationError is the validation error returned by
// ClassificationFileTag.Validate if the designated constraints aren't met.
type ClassificationFileTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClassificationFileTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClassificationFileTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClassificationFileTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClassificationFileTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClassificationFileTagValidationError) ErrorName() string {
	return "ClassificationFileTagValidationError"
}

// Error satisfies the builtin error interface
func (e ClassificationFileTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClassificationFileTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClassificationFileTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClassificationFileTagValidationError{}

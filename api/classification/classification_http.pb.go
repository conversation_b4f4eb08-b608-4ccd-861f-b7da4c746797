// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: classification/classification.proto

package classification

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationClassificationGetAllClassification = "/api.classification.Classification/GetAllClassification"
const OperationClassificationGetFileClassificationTags = "/api.classification.Classification/GetFileClassificationTags"

type ClassificationHTTPServer interface {
	// GetAllClassification 查询所有分类及文件数量
	GetAllClassification(context.Context, *GetAllClassificationRequest) (*GetAllClassificationReply, error)
	// GetFileClassificationTags 查询文件分类信息
	GetFileClassificationTags(context.Context, *GetFileClassificationTagsRequest) (*GetFileClassificationTagsReply, error)
}

func RegisterClassificationHTTPServer(s *http.Server, srv ClassificationHTTPServer) {
	r := s.Route("/")
	r.GET("/classification/all", _Classification_GetAllClassification0_HTTP_Handler(srv))
	r.POST("/classification/fileTags", _Classification_GetFileClassificationTags0_HTTP_Handler(srv))
}

func _Classification_GetAllClassification0_HTTP_Handler(srv ClassificationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAllClassificationRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationClassificationGetAllClassification)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAllClassification(ctx, req.(*GetAllClassificationRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAllClassificationReply)
		return ctx.Result(200, reply)
	}
}

func _Classification_GetFileClassificationTags0_HTTP_Handler(srv ClassificationHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetFileClassificationTagsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationClassificationGetFileClassificationTags)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFileClassificationTags(ctx, req.(*GetFileClassificationTagsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetFileClassificationTagsReply)
		return ctx.Result(200, reply)
	}
}

type ClassificationHTTPClient interface {
	GetAllClassification(ctx context.Context, req *GetAllClassificationRequest, opts ...http.CallOption) (rsp *GetAllClassificationReply, err error)
	GetFileClassificationTags(ctx context.Context, req *GetFileClassificationTagsRequest, opts ...http.CallOption) (rsp *GetFileClassificationTagsReply, err error)
}

type ClassificationHTTPClientImpl struct {
	cc *http.Client
}

func NewClassificationHTTPClient(client *http.Client) ClassificationHTTPClient {
	return &ClassificationHTTPClientImpl{client}
}

func (c *ClassificationHTTPClientImpl) GetAllClassification(ctx context.Context, in *GetAllClassificationRequest, opts ...http.CallOption) (*GetAllClassificationReply, error) {
	var out GetAllClassificationReply
	pattern := "/classification/all"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationClassificationGetAllClassification))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ClassificationHTTPClientImpl) GetFileClassificationTags(ctx context.Context, in *GetFileClassificationTagsRequest, opts ...http.CallOption) (*GetFileClassificationTagsReply, error) {
	var out GetFileClassificationTagsReply
	pattern := "/classification/fileTags"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationClassificationGetFileClassificationTags))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

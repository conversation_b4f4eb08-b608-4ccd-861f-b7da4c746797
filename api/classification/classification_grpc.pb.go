// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: classification/classification.proto

package classification

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Classification_GetAllClassification_FullMethodName      = "/api.classification.Classification/GetAllClassification"
	Classification_GetFileClassificationTags_FullMethodName = "/api.classification.Classification/GetFileClassificationTags"
)

// ClassificationClient is the client API for Classification service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ClassificationClient interface {
	// 查询所有分类及文件数量
	GetAllClassification(ctx context.Context, in *GetAllClassificationRequest, opts ...grpc.CallOption) (*GetAllClassificationReply, error)
	// 查询文件分类信息
	GetFileClassificationTags(ctx context.Context, in *GetFileClassificationTagsRequest, opts ...grpc.CallOption) (*GetFileClassificationTagsReply, error)
}

type classificationClient struct {
	cc grpc.ClientConnInterface
}

func NewClassificationClient(cc grpc.ClientConnInterface) ClassificationClient {
	return &classificationClient{cc}
}

func (c *classificationClient) GetAllClassification(ctx context.Context, in *GetAllClassificationRequest, opts ...grpc.CallOption) (*GetAllClassificationReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllClassificationReply)
	err := c.cc.Invoke(ctx, Classification_GetAllClassification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *classificationClient) GetFileClassificationTags(ctx context.Context, in *GetFileClassificationTagsRequest, opts ...grpc.CallOption) (*GetFileClassificationTagsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFileClassificationTagsReply)
	err := c.cc.Invoke(ctx, Classification_GetFileClassificationTags_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ClassificationServer is the server API for Classification service.
// All implementations must embed UnimplementedClassificationServer
// for forward compatibility.
type ClassificationServer interface {
	// 查询所有分类及文件数量
	GetAllClassification(context.Context, *GetAllClassificationRequest) (*GetAllClassificationReply, error)
	// 查询文件分类信息
	GetFileClassificationTags(context.Context, *GetFileClassificationTagsRequest) (*GetFileClassificationTagsReply, error)
	mustEmbedUnimplementedClassificationServer()
}

// UnimplementedClassificationServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedClassificationServer struct{}

func (UnimplementedClassificationServer) GetAllClassification(context.Context, *GetAllClassificationRequest) (*GetAllClassificationReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllClassification not implemented")
}
func (UnimplementedClassificationServer) GetFileClassificationTags(context.Context, *GetFileClassificationTagsRequest) (*GetFileClassificationTagsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileClassificationTags not implemented")
}
func (UnimplementedClassificationServer) mustEmbedUnimplementedClassificationServer() {}
func (UnimplementedClassificationServer) testEmbeddedByValue()                        {}

// UnsafeClassificationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ClassificationServer will
// result in compilation errors.
type UnsafeClassificationServer interface {
	mustEmbedUnimplementedClassificationServer()
}

func RegisterClassificationServer(s grpc.ServiceRegistrar, srv ClassificationServer) {
	// If the following call pancis, it indicates UnimplementedClassificationServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Classification_ServiceDesc, srv)
}

func _Classification_GetAllClassification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllClassificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClassificationServer).GetAllClassification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Classification_GetAllClassification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClassificationServer).GetAllClassification(ctx, req.(*GetAllClassificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Classification_GetFileClassificationTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileClassificationTagsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClassificationServer).GetFileClassificationTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Classification_GetFileClassificationTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClassificationServer).GetFileClassificationTags(ctx, req.(*GetFileClassificationTagsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Classification_ServiceDesc is the grpc.ServiceDesc for Classification service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Classification_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.classification.Classification",
	HandlerType: (*ClassificationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAllClassification",
			Handler:    _Classification_GetAllClassification_Handler,
		},
		{
			MethodName: "GetFileClassificationTags",
			Handler:    _Classification_GetFileClassificationTags_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "classification/classification.proto",
}

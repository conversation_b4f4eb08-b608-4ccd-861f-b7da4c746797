// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: classification/classification.proto

package classification

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ClassificationFinishEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID int64    `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	PreEntityTag   string   `protobuf:"bytes,2,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	EntityTag      string   `protobuf:"bytes,3,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	FileName       string   `protobuf:"bytes,4,opt,name=fileName,proto3" json:"fileName,omitempty"`
	SecurityLevel  int32    `protobuf:"varint,5,opt,name=securityLevel,proto3" json:"securityLevel,omitempty"`
	Tags           []string `protobuf:"bytes,6,rep,name=tags,proto3" json:"tags,omitempty"`
	Path           string   `protobuf:"bytes,7,opt,name=path,proto3" json:"path,omitempty"`
	UserID         int64    `protobuf:"varint,8,opt,name=userID,proto3" json:"userID,omitempty"`
	TenantID       int64    `protobuf:"varint,9,opt,name=tenantID,proto3" json:"tenantID,omitempty"`
}

func (x *ClassificationFinishEvent) Reset() {
	*x = ClassificationFinishEvent{}
	mi := &file_classification_classification_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClassificationFinishEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassificationFinishEvent) ProtoMessage() {}

func (x *ClassificationFinishEvent) ProtoReflect() protoreflect.Message {
	mi := &file_classification_classification_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassificationFinishEvent.ProtoReflect.Descriptor instead.
func (*ClassificationFinishEvent) Descriptor() ([]byte, []int) {
	return file_classification_classification_proto_rawDescGZIP(), []int{0}
}

func (x *ClassificationFinishEvent) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *ClassificationFinishEvent) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *ClassificationFinishEvent) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *ClassificationFinishEvent) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ClassificationFinishEvent) GetSecurityLevel() int32 {
	if x != nil {
		return x.SecurityLevel
	}
	return 0
}

func (x *ClassificationFinishEvent) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ClassificationFinishEvent) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ClassificationFinishEvent) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *ClassificationFinishEvent) GetTenantID() int64 {
	if x != nil {
		return x.TenantID
	}
	return 0
}

type GetAllClassificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagName string `protobuf:"bytes,1,opt,name=tagName,proto3" json:"tagName,omitempty"`
}

func (x *GetAllClassificationRequest) Reset() {
	*x = GetAllClassificationRequest{}
	mi := &file_classification_classification_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllClassificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllClassificationRequest) ProtoMessage() {}

func (x *GetAllClassificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_classification_classification_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllClassificationRequest.ProtoReflect.Descriptor instead.
func (*GetAllClassificationRequest) Descriptor() ([]byte, []int) {
	return file_classification_classification_proto_rawDescGZIP(), []int{1}
}

func (x *GetAllClassificationRequest) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

type GetAllClassificationReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClassificationTags []*ClassificationTag `protobuf:"bytes,1,rep,name=classificationTags,proto3" json:"classificationTags,omitempty"` // 一级分类列表
}

func (x *GetAllClassificationReply) Reset() {
	*x = GetAllClassificationReply{}
	mi := &file_classification_classification_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllClassificationReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllClassificationReply) ProtoMessage() {}

func (x *GetAllClassificationReply) ProtoReflect() protoreflect.Message {
	mi := &file_classification_classification_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllClassificationReply.ProtoReflect.Descriptor instead.
func (*GetAllClassificationReply) Descriptor() ([]byte, []int) {
	return file_classification_classification_proto_rawDescGZIP(), []int{2}
}

func (x *GetAllClassificationReply) GetClassificationTags() []*ClassificationTag {
	if x != nil {
		return x.ClassificationTags
	}
	return nil
}

// 一级分类的消息
type ClassificationTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                    int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                      // 一级分类ID
	Name                  string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                   // 一级分类名称
	SubClassificationTags []*SubClassificationTag `protobuf:"bytes,4,rep,name=subClassificationTags,proto3" json:"subClassificationTags,omitempty"` // 二级分类列表
}

func (x *ClassificationTag) Reset() {
	*x = ClassificationTag{}
	mi := &file_classification_classification_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClassificationTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassificationTag) ProtoMessage() {}

func (x *ClassificationTag) ProtoReflect() protoreflect.Message {
	mi := &file_classification_classification_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassificationTag.ProtoReflect.Descriptor instead.
func (*ClassificationTag) Descriptor() ([]byte, []int) {
	return file_classification_classification_proto_rawDescGZIP(), []int{3}
}

func (x *ClassificationTag) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClassificationTag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ClassificationTag) GetSubClassificationTags() []*SubClassificationTag {
	if x != nil {
		return x.SubClassificationTags
	}
	return nil
}

// 二级分类的消息
type SubClassificationTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`    // 二级分类ID
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // 二级分类名称
}

func (x *SubClassificationTag) Reset() {
	*x = SubClassificationTag{}
	mi := &file_classification_classification_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubClassificationTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubClassificationTag) ProtoMessage() {}

func (x *SubClassificationTag) ProtoReflect() protoreflect.Message {
	mi := &file_classification_classification_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubClassificationTag.ProtoReflect.Descriptor instead.
func (*SubClassificationTag) Descriptor() ([]byte, []int) {
	return file_classification_classification_proto_rawDescGZIP(), []int{4}
}

func (x *SubClassificationTag) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SubClassificationTag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type GetFileClassificationTagsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Item []*GetFileClassificationTagsRequestItem `protobuf:"bytes,1,rep,name=item,proto3" json:"item,omitempty"`
}

func (x *GetFileClassificationTagsRequest) Reset() {
	*x = GetFileClassificationTagsRequest{}
	mi := &file_classification_classification_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileClassificationTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileClassificationTagsRequest) ProtoMessage() {}

func (x *GetFileClassificationTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_classification_classification_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileClassificationTagsRequest.ProtoReflect.Descriptor instead.
func (*GetFileClassificationTagsRequest) Descriptor() ([]byte, []int) {
	return file_classification_classification_proto_rawDescGZIP(), []int{5}
}

func (x *GetFileClassificationTagsRequest) GetItem() []*GetFileClassificationTagsRequestItem {
	if x != nil {
		return x.Item
	}
	return nil
}

type GetFileClassificationTagsRequestItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID int64  `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	PreEntityTag   string `protobuf:"bytes,2,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	EntityTag      string `protobuf:"bytes,3,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
}

func (x *GetFileClassificationTagsRequestItem) Reset() {
	*x = GetFileClassificationTagsRequestItem{}
	mi := &file_classification_classification_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileClassificationTagsRequestItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileClassificationTagsRequestItem) ProtoMessage() {}

func (x *GetFileClassificationTagsRequestItem) ProtoReflect() protoreflect.Message {
	mi := &file_classification_classification_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileClassificationTagsRequestItem.ProtoReflect.Descriptor instead.
func (*GetFileClassificationTagsRequestItem) Descriptor() ([]byte, []int) {
	return file_classification_classification_proto_rawDescGZIP(), []int{6}
}

func (x *GetFileClassificationTagsRequestItem) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *GetFileClassificationTagsRequestItem) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *GetFileClassificationTagsRequestItem) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

type GetFileClassificationTagsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClassificationFileTags []*ClassificationFileTag `protobuf:"bytes,1,rep,name=classificationFileTags,proto3" json:"classificationFileTags,omitempty"`
}

func (x *GetFileClassificationTagsReply) Reset() {
	*x = GetFileClassificationTagsReply{}
	mi := &file_classification_classification_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileClassificationTagsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileClassificationTagsReply) ProtoMessage() {}

func (x *GetFileClassificationTagsReply) ProtoReflect() protoreflect.Message {
	mi := &file_classification_classification_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileClassificationTagsReply.ProtoReflect.Descriptor instead.
func (*GetFileClassificationTagsReply) Descriptor() ([]byte, []int) {
	return file_classification_classification_proto_rawDescGZIP(), []int{7}
}

func (x *GetFileClassificationTagsReply) GetClassificationFileTags() []*ClassificationFileTag {
	if x != nil {
		return x.ClassificationFileTags
	}
	return nil
}

type ClassificationFileTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FileRelationID int64    `protobuf:"varint,2,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	PreEntityTag   string   `protobuf:"bytes,3,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	EntityTag      string   `protobuf:"bytes,4,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	FileName       string   `protobuf:"bytes,5,opt,name=fileName,proto3" json:"fileName,omitempty"`
	FileType       string   `protobuf:"bytes,6,opt,name=fileType,proto3" json:"fileType,omitempty"`
	UserID         int64    `protobuf:"varint,7,opt,name=userID,proto3" json:"userID,omitempty"`
	UserName       string   `protobuf:"bytes,8,opt,name=userName,proto3" json:"userName,omitempty"`
	DeptID         int64    `protobuf:"varint,9,opt,name=deptID,proto3" json:"deptID,omitempty"`
	DeptName       string   `protobuf:"bytes,10,opt,name=deptName,proto3" json:"deptName,omitempty"`
	Path           string   `protobuf:"bytes,11,opt,name=path,proto3" json:"path,omitempty"`
	Tags           []string `protobuf:"bytes,12,rep,name=tags,proto3" json:"tags,omitempty"`
	SecurityLevel  int32    `protobuf:"varint,13,opt,name=securityLevel,proto3" json:"securityLevel,omitempty"`
}

func (x *ClassificationFileTag) Reset() {
	*x = ClassificationFileTag{}
	mi := &file_classification_classification_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClassificationFileTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClassificationFileTag) ProtoMessage() {}

func (x *ClassificationFileTag) ProtoReflect() protoreflect.Message {
	mi := &file_classification_classification_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClassificationFileTag.ProtoReflect.Descriptor instead.
func (*ClassificationFileTag) Descriptor() ([]byte, []int) {
	return file_classification_classification_proto_rawDescGZIP(), []int{8}
}

func (x *ClassificationFileTag) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ClassificationFileTag) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *ClassificationFileTag) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *ClassificationFileTag) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *ClassificationFileTag) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ClassificationFileTag) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *ClassificationFileTag) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *ClassificationFileTag) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *ClassificationFileTag) GetDeptID() int64 {
	if x != nil {
		return x.DeptID
	}
	return 0
}

func (x *ClassificationFileTag) GetDeptName() string {
	if x != nil {
		return x.DeptName
	}
	return ""
}

func (x *ClassificationFileTag) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ClassificationFileTag) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ClassificationFileTag) GetSecurityLevel() int32 {
	if x != nil {
		return x.SecurityLevel
	}
	return 0
}

var File_classification_classification_proto protoreflect.FileDescriptor

var file_classification_classification_proto_rawDesc = []byte{
	0x0a, 0x23, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xa3, 0x02, 0x0a, 0x19, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x26,
	0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72,
	0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61,
	0x74, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x49, 0x44, 0x22, 0x37, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0x72, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x55, 0x0a, 0x12,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61,
	0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x52,
	0x12, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x61, 0x67, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x11, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5e, 0x0a,
	0x15, 0x73, 0x75, 0x62, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x75, 0x62, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x52, 0x15, 0x73, 0x75, 0x62, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x22, 0x3a, 0x0a,
	0x14, 0x53, 0x75, 0x62, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x70, 0x0a, 0x20, 0x47, 0x65, 0x74,
	0x46, 0x69, 0x6c, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4c, 0x0a,
	0x04, 0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x04, 0x69, 0x74, 0x65, 0x6d, 0x22, 0x90, 0x01, 0x0a, 0x24,
	0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c,
	0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67,
	0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x22, 0x83,
	0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x61, 0x0a, 0x16, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x52, 0x16, 0x63, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65,
	0x54, 0x61, 0x67, 0x73, 0x22, 0xff, 0x02, 0x0a, 0x15, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26,
	0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72,
	0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x70, 0x74, 0x49, 0x44, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x64, 0x65, 0x70, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x12, 0x24, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x32, 0xd3, 0x02, 0x0a, 0x0e, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x93, 0x01, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x43,
	0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x6c, 0x6c, 0x12,
	0xaa, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x12, 0x34, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65,
	0x43, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61,
	0x67, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x3a,
	0x01, 0x2a, 0x22, 0x18, 0x2f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2f, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x61, 0x67, 0x73, 0x42, 0x62, 0x0a, 0x12,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x01, 0x5a, 0x4a, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e,
	0x75, 0x6d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x61, 0x69, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x3b, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_classification_classification_proto_rawDescOnce sync.Once
	file_classification_classification_proto_rawDescData = file_classification_classification_proto_rawDesc
)

func file_classification_classification_proto_rawDescGZIP() []byte {
	file_classification_classification_proto_rawDescOnce.Do(func() {
		file_classification_classification_proto_rawDescData = protoimpl.X.CompressGZIP(file_classification_classification_proto_rawDescData)
	})
	return file_classification_classification_proto_rawDescData
}

var file_classification_classification_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_classification_classification_proto_goTypes = []any{
	(*ClassificationFinishEvent)(nil),            // 0: api.classification.ClassificationFinishEvent
	(*GetAllClassificationRequest)(nil),          // 1: api.classification.GetAllClassificationRequest
	(*GetAllClassificationReply)(nil),            // 2: api.classification.GetAllClassificationReply
	(*ClassificationTag)(nil),                    // 3: api.classification.ClassificationTag
	(*SubClassificationTag)(nil),                 // 4: api.classification.SubClassificationTag
	(*GetFileClassificationTagsRequest)(nil),     // 5: api.classification.GetFileClassificationTagsRequest
	(*GetFileClassificationTagsRequestItem)(nil), // 6: api.classification.GetFileClassificationTagsRequestItem
	(*GetFileClassificationTagsReply)(nil),       // 7: api.classification.GetFileClassificationTagsReply
	(*ClassificationFileTag)(nil),                // 8: api.classification.ClassificationFileTag
}
var file_classification_classification_proto_depIdxs = []int32{
	3, // 0: api.classification.GetAllClassificationReply.classificationTags:type_name -> api.classification.ClassificationTag
	4, // 1: api.classification.ClassificationTag.subClassificationTags:type_name -> api.classification.SubClassificationTag
	6, // 2: api.classification.GetFileClassificationTagsRequest.item:type_name -> api.classification.GetFileClassificationTagsRequestItem
	8, // 3: api.classification.GetFileClassificationTagsReply.classificationFileTags:type_name -> api.classification.ClassificationFileTag
	1, // 4: api.classification.Classification.GetAllClassification:input_type -> api.classification.GetAllClassificationRequest
	5, // 5: api.classification.Classification.GetFileClassificationTags:input_type -> api.classification.GetFileClassificationTagsRequest
	2, // 6: api.classification.Classification.GetAllClassification:output_type -> api.classification.GetAllClassificationReply
	7, // 7: api.classification.Classification.GetFileClassificationTags:output_type -> api.classification.GetFileClassificationTagsReply
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_classification_classification_proto_init() }
func file_classification_classification_proto_init() {
	if File_classification_classification_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_classification_classification_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_classification_classification_proto_goTypes,
		DependencyIndexes: file_classification_classification_proto_depIdxs,
		MessageInfos:      file_classification_classification_proto_msgTypes,
	}.Build()
	File_classification_classification_proto = out.File
	file_classification_classification_proto_rawDesc = nil
	file_classification_classification_proto_goTypes = nil
	file_classification_classification_proto_depIdxs = nil
}

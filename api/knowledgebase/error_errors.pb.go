// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package knowledgebase

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

func IsKnowledgeBaseCommon(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_KNOWLEDGE_BASE_COMMON.String() && e.Code == 400
}

func ErrorKnowledgeBaseCommon(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_KNOWLEDGE_BASE_COMMON.String(), fmt.Sprintf(format, args...))
}

func IsKnowledgeBaseIllegalOperation(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_KNOWLEDGE_BASE_ILLEGAL_OPERATION.String() && e.Code == 400
}

func ErrorKnowledgeBaseIllegalOperation(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_KNOWLEDGE_BASE_ILLEGAL_OPERATION.String(), fmt.Sprintf(format, args...))
}

func IsKnowledgeBaseExistAgent(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_KNOWLEDGE_BASE_EXIST_AGENT.String() && e.Code == 400
}

func ErrorKnowledgeBaseExistAgent(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_KNOWLEDGE_BASE_EXIST_AGENT.String(), fmt.Sprintf(format, args...))
}

func IsKnowledgeBaseAddTooMuchFile(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == ErrorReason_KNOWLEDGE_BASE_ADD_TOO_MUCH_FILE.String() && e.Code == 400
}

func ErrorKnowledgeBaseAddTooMuchFile(format string, args ...interface{}) *errors.Error {
	return errors.New(400, ErrorReason_KNOWLEDGE_BASE_ADD_TOO_MUCH_FILE.String(), fmt.Sprintf(format, args...))
}

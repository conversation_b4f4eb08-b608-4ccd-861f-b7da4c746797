// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: knowledgebase/knowledgebase.proto

package knowledgebase

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on KnowledgeBaseInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeBaseInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeBaseInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeBaseInfoMultiError, or nil if none found.
func (m *KnowledgeBaseInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeBaseInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Public

	// no validation rules for DataType

	// no validation rules for UserID

	// no validation rules for UserName

	// no validation rules for UserAvatar

	for idx, item := range m.GetManagers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, KnowledgeBaseInfoValidationError{
						field:  fmt.Sprintf("Managers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, KnowledgeBaseInfoValidationError{
						field:  fmt.Sprintf("Managers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return KnowledgeBaseInfoValidationError{
					field:  fmt.Sprintf("Managers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEditableUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, KnowledgeBaseInfoValidationError{
						field:  fmt.Sprintf("EditableUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, KnowledgeBaseInfoValidationError{
						field:  fmt.Sprintf("EditableUsers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return KnowledgeBaseInfoValidationError{
					field:  fmt.Sprintf("EditableUsers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KnowledgeBaseInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KnowledgeBaseInfoValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KnowledgeBaseInfoValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KnowledgeBaseInfoMultiError(errors)
	}

	return nil
}

// KnowledgeBaseInfoMultiError is an error wrapping multiple validation errors
// returned by KnowledgeBaseInfo.ValidateAll() if the designated constraints
// aren't met.
type KnowledgeBaseInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeBaseInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeBaseInfoMultiError) AllErrors() []error { return m }

// KnowledgeBaseInfoValidationError is the validation error returned by
// KnowledgeBaseInfo.Validate if the designated constraints aren't met.
type KnowledgeBaseInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeBaseInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeBaseInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeBaseInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeBaseInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeBaseInfoValidationError) ErrorName() string {
	return "KnowledgeBaseInfoValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeBaseInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeBaseInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeBaseInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeBaseInfoValidationError{}

// Validate checks the field values on CreateKnowledgeBaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateKnowledgeBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateKnowledgeBaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateKnowledgeBaseRequestMultiError, or nil if none found.
func (m *CreateKnowledgeBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateKnowledgeBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetName()); l < 1 || l > 100 {
		err := CreateKnowledgeBaseRequestValidationError{
			field:  "Name",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Public

	if _, ok := _CreateKnowledgeBaseRequest_DataType_InLookup[m.GetDataType()]; !ok {
		err := CreateKnowledgeBaseRequestValidationError{
			field:  "DataType",
			reason: "value must be in list [KnowledgeBaseDataTypeDoc KnowledgeBaseDataTypeSheet]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetUserID() <= 0 {
		err := CreateKnowledgeBaseRequestValidationError{
			field:  "UserID",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateKnowledgeBaseRequestMultiError(errors)
	}

	return nil
}

// CreateKnowledgeBaseRequestMultiError is an error wrapping multiple
// validation errors returned by CreateKnowledgeBaseRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateKnowledgeBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateKnowledgeBaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateKnowledgeBaseRequestMultiError) AllErrors() []error { return m }

// CreateKnowledgeBaseRequestValidationError is the validation error returned
// by CreateKnowledgeBaseRequest.Validate if the designated constraints aren't met.
type CreateKnowledgeBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateKnowledgeBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateKnowledgeBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateKnowledgeBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateKnowledgeBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateKnowledgeBaseRequestValidationError) ErrorName() string {
	return "CreateKnowledgeBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateKnowledgeBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateKnowledgeBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateKnowledgeBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateKnowledgeBaseRequestValidationError{}

var _CreateKnowledgeBaseRequest_DataType_InLookup = map[KnowledgeBaseDataType]struct{}{
	1: {},
	2: {},
}

// Validate checks the field values on CreateKnowledgeBaseReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateKnowledgeBaseReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateKnowledgeBaseReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateKnowledgeBaseReplyMultiError, or nil if none found.
func (m *CreateKnowledgeBaseReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateKnowledgeBaseReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateKnowledgeBaseReplyMultiError(errors)
	}

	return nil
}

// CreateKnowledgeBaseReplyMultiError is an error wrapping multiple validation
// errors returned by CreateKnowledgeBaseReply.ValidateAll() if the designated
// constraints aren't met.
type CreateKnowledgeBaseReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateKnowledgeBaseReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateKnowledgeBaseReplyMultiError) AllErrors() []error { return m }

// CreateKnowledgeBaseReplyValidationError is the validation error returned by
// CreateKnowledgeBaseReply.Validate if the designated constraints aren't met.
type CreateKnowledgeBaseReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateKnowledgeBaseReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateKnowledgeBaseReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateKnowledgeBaseReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateKnowledgeBaseReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateKnowledgeBaseReplyValidationError) ErrorName() string {
	return "CreateKnowledgeBaseReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateKnowledgeBaseReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateKnowledgeBaseReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateKnowledgeBaseReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateKnowledgeBaseReplyValidationError{}

// Validate checks the field values on UpdateKnowledgeBaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateKnowledgeBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateKnowledgeBaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateKnowledgeBaseRequestMultiError, or nil if none found.
func (m *UpdateKnowledgeBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateKnowledgeBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := UpdateKnowledgeBaseRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetName()); l < 1 || l > 100 {
		err := UpdateKnowledgeBaseRequestValidationError{
			field:  "Name",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Public

	if m.OwnerOption != nil {

		if all {
			switch v := interface{}(m.GetOwnerOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateKnowledgeBaseRequestValidationError{
						field:  "OwnerOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateKnowledgeBaseRequestValidationError{
						field:  "OwnerOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOwnerOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateKnowledgeBaseRequestValidationError{
					field:  "OwnerOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return UpdateKnowledgeBaseRequestMultiError(errors)
	}

	return nil
}

// UpdateKnowledgeBaseRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateKnowledgeBaseRequest.ValidateAll() if
// the designated constraints aren't met.
type UpdateKnowledgeBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateKnowledgeBaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateKnowledgeBaseRequestMultiError) AllErrors() []error { return m }

// UpdateKnowledgeBaseRequestValidationError is the validation error returned
// by UpdateKnowledgeBaseRequest.Validate if the designated constraints aren't met.
type UpdateKnowledgeBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateKnowledgeBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateKnowledgeBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateKnowledgeBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateKnowledgeBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateKnowledgeBaseRequestValidationError) ErrorName() string {
	return "UpdateKnowledgeBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateKnowledgeBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateKnowledgeBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateKnowledgeBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateKnowledgeBaseRequestValidationError{}

// Validate checks the field values on UpdateKnowledgeBaseReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateKnowledgeBaseReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateKnowledgeBaseReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateKnowledgeBaseReplyMultiError, or nil if none found.
func (m *UpdateKnowledgeBaseReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateKnowledgeBaseReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateKnowledgeBaseReplyMultiError(errors)
	}

	return nil
}

// UpdateKnowledgeBaseReplyMultiError is an error wrapping multiple validation
// errors returned by UpdateKnowledgeBaseReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateKnowledgeBaseReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateKnowledgeBaseReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateKnowledgeBaseReplyMultiError) AllErrors() []error { return m }

// UpdateKnowledgeBaseReplyValidationError is the validation error returned by
// UpdateKnowledgeBaseReply.Validate if the designated constraints aren't met.
type UpdateKnowledgeBaseReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateKnowledgeBaseReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateKnowledgeBaseReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateKnowledgeBaseReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateKnowledgeBaseReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateKnowledgeBaseReplyValidationError) ErrorName() string {
	return "UpdateKnowledgeBaseReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateKnowledgeBaseReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateKnowledgeBaseReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateKnowledgeBaseReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateKnowledgeBaseReplyValidationError{}

// Validate checks the field values on ChangeKnowledgeBaseOwnerRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeKnowledgeBaseOwnerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeKnowledgeBaseOwnerRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ChangeKnowledgeBaseOwnerRequestMultiError, or nil if none found.
func (m *ChangeKnowledgeBaseOwnerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeKnowledgeBaseOwnerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetNewUserID() <= 0 {
		err := ChangeKnowledgeBaseOwnerRequestValidationError{
			field:  "NewUserID",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ChangeKnowledgeBaseOwnerRequestMultiError(errors)
	}

	return nil
}

// ChangeKnowledgeBaseOwnerRequestMultiError is an error wrapping multiple
// validation errors returned by ChangeKnowledgeBaseOwnerRequest.ValidateAll()
// if the designated constraints aren't met.
type ChangeKnowledgeBaseOwnerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeKnowledgeBaseOwnerRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeKnowledgeBaseOwnerRequestMultiError) AllErrors() []error { return m }

// ChangeKnowledgeBaseOwnerRequestValidationError is the validation error
// returned by ChangeKnowledgeBaseOwnerRequest.Validate if the designated
// constraints aren't met.
type ChangeKnowledgeBaseOwnerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeKnowledgeBaseOwnerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeKnowledgeBaseOwnerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeKnowledgeBaseOwnerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeKnowledgeBaseOwnerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeKnowledgeBaseOwnerRequestValidationError) ErrorName() string {
	return "ChangeKnowledgeBaseOwnerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeKnowledgeBaseOwnerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeKnowledgeBaseOwnerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeKnowledgeBaseOwnerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeKnowledgeBaseOwnerRequestValidationError{}

// Validate checks the field values on ChangeKnowledgeBaseOwnerReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ChangeKnowledgeBaseOwnerReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChangeKnowledgeBaseOwnerReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ChangeKnowledgeBaseOwnerReplyMultiError, or nil if none found.
func (m *ChangeKnowledgeBaseOwnerReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ChangeKnowledgeBaseOwnerReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ChangeKnowledgeBaseOwnerReplyMultiError(errors)
	}

	return nil
}

// ChangeKnowledgeBaseOwnerReplyMultiError is an error wrapping multiple
// validation errors returned by ChangeKnowledgeBaseOwnerReply.ValidateAll()
// if the designated constraints aren't met.
type ChangeKnowledgeBaseOwnerReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChangeKnowledgeBaseOwnerReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChangeKnowledgeBaseOwnerReplyMultiError) AllErrors() []error { return m }

// ChangeKnowledgeBaseOwnerReplyValidationError is the validation error
// returned by ChangeKnowledgeBaseOwnerReply.Validate if the designated
// constraints aren't met.
type ChangeKnowledgeBaseOwnerReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChangeKnowledgeBaseOwnerReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChangeKnowledgeBaseOwnerReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChangeKnowledgeBaseOwnerReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChangeKnowledgeBaseOwnerReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChangeKnowledgeBaseOwnerReplyValidationError) ErrorName() string {
	return "ChangeKnowledgeBaseOwnerReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ChangeKnowledgeBaseOwnerReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChangeKnowledgeBaseOwnerReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChangeKnowledgeBaseOwnerReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChangeKnowledgeBaseOwnerReplyValidationError{}

// Validate checks the field values on DeleteKnowledgeBaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteKnowledgeBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteKnowledgeBaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteKnowledgeBaseRequestMultiError, or nil if none found.
func (m *DeleteKnowledgeBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteKnowledgeBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := DeleteKnowledgeBaseRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteKnowledgeBaseRequestMultiError(errors)
	}

	return nil
}

// DeleteKnowledgeBaseRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteKnowledgeBaseRequest.ValidateAll() if
// the designated constraints aren't met.
type DeleteKnowledgeBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteKnowledgeBaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteKnowledgeBaseRequestMultiError) AllErrors() []error { return m }

// DeleteKnowledgeBaseRequestValidationError is the validation error returned
// by DeleteKnowledgeBaseRequest.Validate if the designated constraints aren't met.
type DeleteKnowledgeBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteKnowledgeBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteKnowledgeBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteKnowledgeBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteKnowledgeBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteKnowledgeBaseRequestValidationError) ErrorName() string {
	return "DeleteKnowledgeBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteKnowledgeBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteKnowledgeBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteKnowledgeBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteKnowledgeBaseRequestValidationError{}

// Validate checks the field values on DeleteKnowledgeBaseReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteKnowledgeBaseReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteKnowledgeBaseReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteKnowledgeBaseReplyMultiError, or nil if none found.
func (m *DeleteKnowledgeBaseReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteKnowledgeBaseReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteKnowledgeBaseReplyMultiError(errors)
	}

	return nil
}

// DeleteKnowledgeBaseReplyMultiError is an error wrapping multiple validation
// errors returned by DeleteKnowledgeBaseReply.ValidateAll() if the designated
// constraints aren't met.
type DeleteKnowledgeBaseReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteKnowledgeBaseReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteKnowledgeBaseReplyMultiError) AllErrors() []error { return m }

// DeleteKnowledgeBaseReplyValidationError is the validation error returned by
// DeleteKnowledgeBaseReply.Validate if the designated constraints aren't met.
type DeleteKnowledgeBaseReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteKnowledgeBaseReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteKnowledgeBaseReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteKnowledgeBaseReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteKnowledgeBaseReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteKnowledgeBaseReplyValidationError) ErrorName() string {
	return "DeleteKnowledgeBaseReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteKnowledgeBaseReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteKnowledgeBaseReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteKnowledgeBaseReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteKnowledgeBaseReplyValidationError{}

// Validate checks the field values on GetKnowledgeBasesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKnowledgeBasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKnowledgeBasesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKnowledgeBasesRequestMultiError, or nil if none found.
func (m *GetKnowledgeBasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKnowledgeBasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetKnowledgeBasesRequestMultiError(errors)
	}

	return nil
}

// GetKnowledgeBasesRequestMultiError is an error wrapping multiple validation
// errors returned by GetKnowledgeBasesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetKnowledgeBasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKnowledgeBasesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKnowledgeBasesRequestMultiError) AllErrors() []error { return m }

// GetKnowledgeBasesRequestValidationError is the validation error returned by
// GetKnowledgeBasesRequest.Validate if the designated constraints aren't met.
type GetKnowledgeBasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKnowledgeBasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKnowledgeBasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKnowledgeBasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKnowledgeBasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKnowledgeBasesRequestValidationError) ErrorName() string {
	return "GetKnowledgeBasesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetKnowledgeBasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKnowledgeBasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKnowledgeBasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKnowledgeBasesRequestValidationError{}

// Validate checks the field values on GetKnowledgeBasesReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetKnowledgeBasesReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetKnowledgeBasesReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetKnowledgeBasesReplyMultiError, or nil if none found.
func (m *GetKnowledgeBasesReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetKnowledgeBasesReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetKnowledgeBasesReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetKnowledgeBasesReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetKnowledgeBasesReplyValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetKnowledgeBasesReplyMultiError(errors)
	}

	return nil
}

// GetKnowledgeBasesReplyMultiError is an error wrapping multiple validation
// errors returned by GetKnowledgeBasesReply.ValidateAll() if the designated
// constraints aren't met.
type GetKnowledgeBasesReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetKnowledgeBasesReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetKnowledgeBasesReplyMultiError) AllErrors() []error { return m }

// GetKnowledgeBasesReplyValidationError is the validation error returned by
// GetKnowledgeBasesReply.Validate if the designated constraints aren't met.
type GetKnowledgeBasesReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetKnowledgeBasesReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetKnowledgeBasesReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetKnowledgeBasesReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetKnowledgeBasesReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetKnowledgeBasesReplyValidationError) ErrorName() string {
	return "GetKnowledgeBasesReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetKnowledgeBasesReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetKnowledgeBasesReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetKnowledgeBasesReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetKnowledgeBasesReplyValidationError{}

// Validate checks the field values on GetAllKnowledgeBasesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllKnowledgeBasesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllKnowledgeBasesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllKnowledgeBasesRequestMultiError, or nil if none found.
func (m *GetAllKnowledgeBasesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllKnowledgeBasesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetAllKnowledgeBasesRequestMultiError(errors)
	}

	return nil
}

// GetAllKnowledgeBasesRequestMultiError is an error wrapping multiple
// validation errors returned by GetAllKnowledgeBasesRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAllKnowledgeBasesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllKnowledgeBasesRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllKnowledgeBasesRequestMultiError) AllErrors() []error { return m }

// GetAllKnowledgeBasesRequestValidationError is the validation error returned
// by GetAllKnowledgeBasesRequest.Validate if the designated constraints
// aren't met.
type GetAllKnowledgeBasesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllKnowledgeBasesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllKnowledgeBasesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllKnowledgeBasesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllKnowledgeBasesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllKnowledgeBasesRequestValidationError) ErrorName() string {
	return "GetAllKnowledgeBasesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllKnowledgeBasesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllKnowledgeBasesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllKnowledgeBasesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllKnowledgeBasesRequestValidationError{}

// Validate checks the field values on GetAllKnowledgeBasesReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllKnowledgeBasesReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllKnowledgeBasesReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllKnowledgeBasesReplyMultiError, or nil if none found.
func (m *GetAllKnowledgeBasesReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllKnowledgeBasesReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllKnowledgeBasesReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllKnowledgeBasesReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllKnowledgeBasesReplyValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllKnowledgeBasesReplyMultiError(errors)
	}

	return nil
}

// GetAllKnowledgeBasesReplyMultiError is an error wrapping multiple validation
// errors returned by GetAllKnowledgeBasesReply.ValidateAll() if the
// designated constraints aren't met.
type GetAllKnowledgeBasesReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllKnowledgeBasesReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllKnowledgeBasesReplyMultiError) AllErrors() []error { return m }

// GetAllKnowledgeBasesReplyValidationError is the validation error returned by
// GetAllKnowledgeBasesReply.Validate if the designated constraints aren't met.
type GetAllKnowledgeBasesReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllKnowledgeBasesReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllKnowledgeBasesReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllKnowledgeBasesReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllKnowledgeBasesReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllKnowledgeBasesReplyValidationError) ErrorName() string {
	return "GetAllKnowledgeBasesReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllKnowledgeBasesReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllKnowledgeBasesReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllKnowledgeBasesReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllKnowledgeBasesReplyValidationError{}

// Validate checks the field values on PageKnowledgeBaseRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageKnowledgeBaseRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageKnowledgeBaseRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageKnowledgeBaseRequestMultiError, or nil if none found.
func (m *PageKnowledgeBaseRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageKnowledgeBaseRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageNum() <= 0 {
		err := PageKnowledgeBaseRequestValidationError{
			field:  "PageNum",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val <= 0 || val > 100 {
		err := PageKnowledgeBaseRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range (0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Name

	// no validation rules for All

	// no validation rules for DataType

	if len(errors) > 0 {
		return PageKnowledgeBaseRequestMultiError(errors)
	}

	return nil
}

// PageKnowledgeBaseRequestMultiError is an error wrapping multiple validation
// errors returned by PageKnowledgeBaseRequest.ValidateAll() if the designated
// constraints aren't met.
type PageKnowledgeBaseRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageKnowledgeBaseRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageKnowledgeBaseRequestMultiError) AllErrors() []error { return m }

// PageKnowledgeBaseRequestValidationError is the validation error returned by
// PageKnowledgeBaseRequest.Validate if the designated constraints aren't met.
type PageKnowledgeBaseRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageKnowledgeBaseRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageKnowledgeBaseRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageKnowledgeBaseRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageKnowledgeBaseRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageKnowledgeBaseRequestValidationError) ErrorName() string {
	return "PageKnowledgeBaseRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PageKnowledgeBaseRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageKnowledgeBaseRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageKnowledgeBaseRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageKnowledgeBaseRequestValidationError{}

// Validate checks the field values on PageKnowledgeBaseReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageKnowledgeBaseReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageKnowledgeBaseReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageKnowledgeBaseReplyMultiError, or nil if none found.
func (m *PageKnowledgeBaseReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PageKnowledgeBaseReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageKnowledgeBaseReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageKnowledgeBaseReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageKnowledgeBaseReplyValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PageKnowledgeBaseReplyMultiError(errors)
	}

	return nil
}

// PageKnowledgeBaseReplyMultiError is an error wrapping multiple validation
// errors returned by PageKnowledgeBaseReply.ValidateAll() if the designated
// constraints aren't met.
type PageKnowledgeBaseReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageKnowledgeBaseReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageKnowledgeBaseReplyMultiError) AllErrors() []error { return m }

// PageKnowledgeBaseReplyValidationError is the validation error returned by
// PageKnowledgeBaseReply.Validate if the designated constraints aren't met.
type PageKnowledgeBaseReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageKnowledgeBaseReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageKnowledgeBaseReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageKnowledgeBaseReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageKnowledgeBaseReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageKnowledgeBaseReplyValidationError) ErrorName() string {
	return "PageKnowledgeBaseReplyValidationError"
}

// Error satisfies the builtin error interface
func (e PageKnowledgeBaseReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageKnowledgeBaseReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageKnowledgeBaseReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageKnowledgeBaseReplyValidationError{}

// Validate checks the field values on AddKnowledgeBaseFileFromSearchRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *AddKnowledgeBaseFileFromSearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddKnowledgeBaseFileFromSearchRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AddKnowledgeBaseFileFromSearchRequestMultiError, or nil if none found.
func (m *AddKnowledgeBaseFileFromSearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddKnowledgeBaseFileFromSearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := AddKnowledgeBaseFileFromSearchRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Query

	// no validation rules for SearchType

	// no validation rules for FileType

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddKnowledgeBaseFileFromSearchRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddKnowledgeBaseFileFromSearchRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddKnowledgeBaseFileFromSearchRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddKnowledgeBaseFileFromSearchRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddKnowledgeBaseFileFromSearchRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddKnowledgeBaseFileFromSearchRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClassPath

	// no validation rules for Path

	if len(errors) > 0 {
		return AddKnowledgeBaseFileFromSearchRequestMultiError(errors)
	}

	return nil
}

// AddKnowledgeBaseFileFromSearchRequestMultiError is an error wrapping
// multiple validation errors returned by
// AddKnowledgeBaseFileFromSearchRequest.ValidateAll() if the designated
// constraints aren't met.
type AddKnowledgeBaseFileFromSearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddKnowledgeBaseFileFromSearchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddKnowledgeBaseFileFromSearchRequestMultiError) AllErrors() []error { return m }

// AddKnowledgeBaseFileFromSearchRequestValidationError is the validation error
// returned by AddKnowledgeBaseFileFromSearchRequest.Validate if the
// designated constraints aren't met.
type AddKnowledgeBaseFileFromSearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddKnowledgeBaseFileFromSearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddKnowledgeBaseFileFromSearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddKnowledgeBaseFileFromSearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddKnowledgeBaseFileFromSearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddKnowledgeBaseFileFromSearchRequestValidationError) ErrorName() string {
	return "AddKnowledgeBaseFileFromSearchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddKnowledgeBaseFileFromSearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddKnowledgeBaseFileFromSearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddKnowledgeBaseFileFromSearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddKnowledgeBaseFileFromSearchRequestValidationError{}

// Validate checks the field values on AddKnowledgeBaseFileFromSearchReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AddKnowledgeBaseFileFromSearchReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddKnowledgeBaseFileFromSearchReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AddKnowledgeBaseFileFromSearchReplyMultiError, or nil if none found.
func (m *AddKnowledgeBaseFileFromSearchReply) ValidateAll() error {
	return m.validate(true)
}

func (m *AddKnowledgeBaseFileFromSearchReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddKnowledgeBaseFileFromSearchReplyMultiError(errors)
	}

	return nil
}

// AddKnowledgeBaseFileFromSearchReplyMultiError is an error wrapping multiple
// validation errors returned by
// AddKnowledgeBaseFileFromSearchReply.ValidateAll() if the designated
// constraints aren't met.
type AddKnowledgeBaseFileFromSearchReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddKnowledgeBaseFileFromSearchReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddKnowledgeBaseFileFromSearchReplyMultiError) AllErrors() []error { return m }

// AddKnowledgeBaseFileFromSearchReplyValidationError is the validation error
// returned by AddKnowledgeBaseFileFromSearchReply.Validate if the designated
// constraints aren't met.
type AddKnowledgeBaseFileFromSearchReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddKnowledgeBaseFileFromSearchReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddKnowledgeBaseFileFromSearchReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddKnowledgeBaseFileFromSearchReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddKnowledgeBaseFileFromSearchReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddKnowledgeBaseFileFromSearchReplyValidationError) ErrorName() string {
	return "AddKnowledgeBaseFileFromSearchReplyValidationError"
}

// Error satisfies the builtin error interface
func (e AddKnowledgeBaseFileFromSearchReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddKnowledgeBaseFileFromSearchReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddKnowledgeBaseFileFromSearchReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddKnowledgeBaseFileFromSearchReplyValidationError{}

// Validate checks the field values on AddKnowledgeBaseFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddKnowledgeBaseFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddKnowledgeBaseFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddKnowledgeBaseFileRequestMultiError, or nil if none found.
func (m *AddKnowledgeBaseFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddKnowledgeBaseFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := AddKnowledgeBaseFileRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetFileRelationIDs()) < 1 {
		err := AddKnowledgeBaseFileRequestValidationError{
			field:  "FileRelationIDs",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for SkipQuickCreate

	if len(errors) > 0 {
		return AddKnowledgeBaseFileRequestMultiError(errors)
	}

	return nil
}

// AddKnowledgeBaseFileRequestMultiError is an error wrapping multiple
// validation errors returned by AddKnowledgeBaseFileRequest.ValidateAll() if
// the designated constraints aren't met.
type AddKnowledgeBaseFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddKnowledgeBaseFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddKnowledgeBaseFileRequestMultiError) AllErrors() []error { return m }

// AddKnowledgeBaseFileRequestValidationError is the validation error returned
// by AddKnowledgeBaseFileRequest.Validate if the designated constraints
// aren't met.
type AddKnowledgeBaseFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddKnowledgeBaseFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddKnowledgeBaseFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddKnowledgeBaseFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddKnowledgeBaseFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddKnowledgeBaseFileRequestValidationError) ErrorName() string {
	return "AddKnowledgeBaseFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddKnowledgeBaseFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddKnowledgeBaseFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddKnowledgeBaseFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddKnowledgeBaseFileRequestValidationError{}

// Validate checks the field values on AddKnowledgeBaseFileReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddKnowledgeBaseFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddKnowledgeBaseFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddKnowledgeBaseFileReplyMultiError, or nil if none found.
func (m *AddKnowledgeBaseFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *AddKnowledgeBaseFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AddKnowledgeBaseFileReplyMultiError(errors)
	}

	return nil
}

// AddKnowledgeBaseFileReplyMultiError is an error wrapping multiple validation
// errors returned by AddKnowledgeBaseFileReply.ValidateAll() if the
// designated constraints aren't met.
type AddKnowledgeBaseFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddKnowledgeBaseFileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddKnowledgeBaseFileReplyMultiError) AllErrors() []error { return m }

// AddKnowledgeBaseFileReplyValidationError is the validation error returned by
// AddKnowledgeBaseFileReply.Validate if the designated constraints aren't met.
type AddKnowledgeBaseFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddKnowledgeBaseFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddKnowledgeBaseFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddKnowledgeBaseFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddKnowledgeBaseFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddKnowledgeBaseFileReplyValidationError) ErrorName() string {
	return "AddKnowledgeBaseFileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e AddKnowledgeBaseFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddKnowledgeBaseFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddKnowledgeBaseFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddKnowledgeBaseFileReplyValidationError{}

// Validate checks the field values on DeleteKnowledgeBaseFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteKnowledgeBaseFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteKnowledgeBaseFileRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DeleteKnowledgeBaseFileRequestMultiError, or nil if none found.
func (m *DeleteKnowledgeBaseFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteKnowledgeBaseFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := DeleteKnowledgeBaseFileRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(m.GetFileRelationIDs()) < 1 {
		err := DeleteKnowledgeBaseFileRequestValidationError{
			field:  "FileRelationIDs",
			reason: "value must contain at least 1 item(s)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteKnowledgeBaseFileRequestMultiError(errors)
	}

	return nil
}

// DeleteKnowledgeBaseFileRequestMultiError is an error wrapping multiple
// validation errors returned by DeleteKnowledgeBaseFileRequest.ValidateAll()
// if the designated constraints aren't met.
type DeleteKnowledgeBaseFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteKnowledgeBaseFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteKnowledgeBaseFileRequestMultiError) AllErrors() []error { return m }

// DeleteKnowledgeBaseFileRequestValidationError is the validation error
// returned by DeleteKnowledgeBaseFileRequest.Validate if the designated
// constraints aren't met.
type DeleteKnowledgeBaseFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteKnowledgeBaseFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteKnowledgeBaseFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteKnowledgeBaseFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteKnowledgeBaseFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteKnowledgeBaseFileRequestValidationError) ErrorName() string {
	return "DeleteKnowledgeBaseFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteKnowledgeBaseFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteKnowledgeBaseFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteKnowledgeBaseFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteKnowledgeBaseFileRequestValidationError{}

// Validate checks the field values on DeleteKnowledgeBaseFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteKnowledgeBaseFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteKnowledgeBaseFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteKnowledgeBaseFileReplyMultiError, or nil if none found.
func (m *DeleteKnowledgeBaseFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteKnowledgeBaseFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteKnowledgeBaseFileReplyMultiError(errors)
	}

	return nil
}

// DeleteKnowledgeBaseFileReplyMultiError is an error wrapping multiple
// validation errors returned by DeleteKnowledgeBaseFileReply.ValidateAll() if
// the designated constraints aren't met.
type DeleteKnowledgeBaseFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteKnowledgeBaseFileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteKnowledgeBaseFileReplyMultiError) AllErrors() []error { return m }

// DeleteKnowledgeBaseFileReplyValidationError is the validation error returned
// by DeleteKnowledgeBaseFileReply.Validate if the designated constraints
// aren't met.
type DeleteKnowledgeBaseFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteKnowledgeBaseFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteKnowledgeBaseFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteKnowledgeBaseFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteKnowledgeBaseFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteKnowledgeBaseFileReplyValidationError) ErrorName() string {
	return "DeleteKnowledgeBaseFileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteKnowledgeBaseFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteKnowledgeBaseFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteKnowledgeBaseFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteKnowledgeBaseFileReplyValidationError{}

// Validate checks the field values on UpdateKnowledgeBaseFileMetadataRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateKnowledgeBaseFileMetadataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateKnowledgeBaseFileMetadataRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateKnowledgeBaseFileMetadataRequestMultiError, or nil if none found.
func (m *UpdateKnowledgeBaseFileMetadataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateKnowledgeBaseFileMetadataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := UpdateKnowledgeBaseFileMetadataRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFileRelationID() <= 0 {
		err := UpdateKnowledgeBaseFileMetadataRequestValidationError{
			field:  "FileRelationID",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Metadata

	if len(errors) > 0 {
		return UpdateKnowledgeBaseFileMetadataRequestMultiError(errors)
	}

	return nil
}

// UpdateKnowledgeBaseFileMetadataRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateKnowledgeBaseFileMetadataRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateKnowledgeBaseFileMetadataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateKnowledgeBaseFileMetadataRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateKnowledgeBaseFileMetadataRequestMultiError) AllErrors() []error { return m }

// UpdateKnowledgeBaseFileMetadataRequestValidationError is the validation
// error returned by UpdateKnowledgeBaseFileMetadataRequest.Validate if the
// designated constraints aren't met.
type UpdateKnowledgeBaseFileMetadataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateKnowledgeBaseFileMetadataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateKnowledgeBaseFileMetadataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateKnowledgeBaseFileMetadataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateKnowledgeBaseFileMetadataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateKnowledgeBaseFileMetadataRequestValidationError) ErrorName() string {
	return "UpdateKnowledgeBaseFileMetadataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateKnowledgeBaseFileMetadataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateKnowledgeBaseFileMetadataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateKnowledgeBaseFileMetadataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateKnowledgeBaseFileMetadataRequestValidationError{}

// Validate checks the field values on UpdateKnowledgeBaseFileMetadataReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateKnowledgeBaseFileMetadataReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateKnowledgeBaseFileMetadataReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateKnowledgeBaseFileMetadataReplyMultiError, or nil if none found.
func (m *UpdateKnowledgeBaseFileMetadataReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateKnowledgeBaseFileMetadataReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateKnowledgeBaseFileMetadataReplyMultiError(errors)
	}

	return nil
}

// UpdateKnowledgeBaseFileMetadataReplyMultiError is an error wrapping multiple
// validation errors returned by
// UpdateKnowledgeBaseFileMetadataReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateKnowledgeBaseFileMetadataReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateKnowledgeBaseFileMetadataReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateKnowledgeBaseFileMetadataReplyMultiError) AllErrors() []error { return m }

// UpdateKnowledgeBaseFileMetadataReplyValidationError is the validation error
// returned by UpdateKnowledgeBaseFileMetadataReply.Validate if the designated
// constraints aren't met.
type UpdateKnowledgeBaseFileMetadataReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateKnowledgeBaseFileMetadataReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateKnowledgeBaseFileMetadataReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateKnowledgeBaseFileMetadataReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateKnowledgeBaseFileMetadataReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateKnowledgeBaseFileMetadataReplyValidationError) ErrorName() string {
	return "UpdateKnowledgeBaseFileMetadataReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateKnowledgeBaseFileMetadataReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateKnowledgeBaseFileMetadataReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateKnowledgeBaseFileMetadataReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateKnowledgeBaseFileMetadataReplyValidationError{}

// Validate checks the field values on InnerUpdateKnowledgeBaseFileRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InnerUpdateKnowledgeBaseFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InnerUpdateKnowledgeBaseFileRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InnerUpdateKnowledgeBaseFileRequestMultiError, or nil if none found.
func (m *InnerUpdateKnowledgeBaseFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InnerUpdateKnowledgeBaseFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetId() <= 0 {
		err := InnerUpdateKnowledgeBaseFileRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetFileRelationID() <= 0 {
		err := InnerUpdateKnowledgeBaseFileRequestValidationError{
			field:  "FileRelationID",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.Metadata != nil {
		// no validation rules for Metadata
	}

	if m.Status != nil {

		if _, ok := _InnerUpdateKnowledgeBaseFileRequest_Status_InLookup[m.GetStatus()]; !ok {
			err := InnerUpdateKnowledgeBaseFileRequestValidationError{
				field:  "Status",
				reason: "value must be in list [KnowledgeBaseFileStatusWaiting KnowledgeBaseFileStatusProcessing KnowledgeBaseFileStatusSuccess KnowledgeBaseFileStatusFailed]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.FailedReason != nil {
		// no validation rules for FailedReason
	}

	if len(errors) > 0 {
		return InnerUpdateKnowledgeBaseFileRequestMultiError(errors)
	}

	return nil
}

// InnerUpdateKnowledgeBaseFileRequestMultiError is an error wrapping multiple
// validation errors returned by
// InnerUpdateKnowledgeBaseFileRequest.ValidateAll() if the designated
// constraints aren't met.
type InnerUpdateKnowledgeBaseFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InnerUpdateKnowledgeBaseFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InnerUpdateKnowledgeBaseFileRequestMultiError) AllErrors() []error { return m }

// InnerUpdateKnowledgeBaseFileRequestValidationError is the validation error
// returned by InnerUpdateKnowledgeBaseFileRequest.Validate if the designated
// constraints aren't met.
type InnerUpdateKnowledgeBaseFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InnerUpdateKnowledgeBaseFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InnerUpdateKnowledgeBaseFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InnerUpdateKnowledgeBaseFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InnerUpdateKnowledgeBaseFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InnerUpdateKnowledgeBaseFileRequestValidationError) ErrorName() string {
	return "InnerUpdateKnowledgeBaseFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InnerUpdateKnowledgeBaseFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInnerUpdateKnowledgeBaseFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InnerUpdateKnowledgeBaseFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InnerUpdateKnowledgeBaseFileRequestValidationError{}

var _InnerUpdateKnowledgeBaseFileRequest_Status_InLookup = map[KnowledgeBaseFileStatus]struct{}{
	0: {},
	1: {},
	2: {},
	3: {},
}

// Validate checks the field values on InnerUpdateKnowledgeBaseFileReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InnerUpdateKnowledgeBaseFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InnerUpdateKnowledgeBaseFileReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InnerUpdateKnowledgeBaseFileReplyMultiError, or nil if none found.
func (m *InnerUpdateKnowledgeBaseFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *InnerUpdateKnowledgeBaseFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return InnerUpdateKnowledgeBaseFileReplyMultiError(errors)
	}

	return nil
}

// InnerUpdateKnowledgeBaseFileReplyMultiError is an error wrapping multiple
// validation errors returned by
// InnerUpdateKnowledgeBaseFileReply.ValidateAll() if the designated
// constraints aren't met.
type InnerUpdateKnowledgeBaseFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InnerUpdateKnowledgeBaseFileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InnerUpdateKnowledgeBaseFileReplyMultiError) AllErrors() []error { return m }

// InnerUpdateKnowledgeBaseFileReplyValidationError is the validation error
// returned by InnerUpdateKnowledgeBaseFileReply.Validate if the designated
// constraints aren't met.
type InnerUpdateKnowledgeBaseFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InnerUpdateKnowledgeBaseFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InnerUpdateKnowledgeBaseFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InnerUpdateKnowledgeBaseFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InnerUpdateKnowledgeBaseFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InnerUpdateKnowledgeBaseFileReplyValidationError) ErrorName() string {
	return "InnerUpdateKnowledgeBaseFileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e InnerUpdateKnowledgeBaseFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInnerUpdateKnowledgeBaseFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InnerUpdateKnowledgeBaseFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InnerUpdateKnowledgeBaseFileReplyValidationError{}

// Validate checks the field values on PageKnowledgeBaseFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageKnowledgeBaseFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageKnowledgeBaseFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageKnowledgeBaseFileRequestMultiError, or nil if none found.
func (m *PageKnowledgeBaseFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageKnowledgeBaseFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageNum() <= 0 {
		err := PageKnowledgeBaseFileRequestValidationError{
			field:  "PageNum",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val <= 0 || val > 100 {
		err := PageKnowledgeBaseFileRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range (0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetId() <= 0 {
		err := PageKnowledgeBaseFileRequestValidationError{
			field:  "Id",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FileName

	if len(errors) > 0 {
		return PageKnowledgeBaseFileRequestMultiError(errors)
	}

	return nil
}

// PageKnowledgeBaseFileRequestMultiError is an error wrapping multiple
// validation errors returned by PageKnowledgeBaseFileRequest.ValidateAll() if
// the designated constraints aren't met.
type PageKnowledgeBaseFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageKnowledgeBaseFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageKnowledgeBaseFileRequestMultiError) AllErrors() []error { return m }

// PageKnowledgeBaseFileRequestValidationError is the validation error returned
// by PageKnowledgeBaseFileRequest.Validate if the designated constraints
// aren't met.
type PageKnowledgeBaseFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageKnowledgeBaseFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageKnowledgeBaseFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageKnowledgeBaseFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageKnowledgeBaseFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageKnowledgeBaseFileRequestValidationError) ErrorName() string {
	return "PageKnowledgeBaseFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PageKnowledgeBaseFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageKnowledgeBaseFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageKnowledgeBaseFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageKnowledgeBaseFileRequestValidationError{}

// Validate checks the field values on PageKnowledgeBaseFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageKnowledgeBaseFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageKnowledgeBaseFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageKnowledgeBaseFileReplyMultiError, or nil if none found.
func (m *PageKnowledgeBaseFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PageKnowledgeBaseFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageKnowledgeBaseFileReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageKnowledgeBaseFileReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageKnowledgeBaseFileReplyValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PageKnowledgeBaseFileReplyMultiError(errors)
	}

	return nil
}

// PageKnowledgeBaseFileReplyMultiError is an error wrapping multiple
// validation errors returned by PageKnowledgeBaseFileReply.ValidateAll() if
// the designated constraints aren't met.
type PageKnowledgeBaseFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageKnowledgeBaseFileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageKnowledgeBaseFileReplyMultiError) AllErrors() []error { return m }

// PageKnowledgeBaseFileReplyValidationError is the validation error returned
// by PageKnowledgeBaseFileReply.Validate if the designated constraints aren't met.
type PageKnowledgeBaseFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageKnowledgeBaseFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageKnowledgeBaseFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageKnowledgeBaseFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageKnowledgeBaseFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageKnowledgeBaseFileReplyValidationError) ErrorName() string {
	return "PageKnowledgeBaseFileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e PageKnowledgeBaseFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageKnowledgeBaseFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageKnowledgeBaseFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageKnowledgeBaseFileReplyValidationError{}

// Validate checks the field values on PageKnowledgeBaseFileReplyItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageKnowledgeBaseFileReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageKnowledgeBaseFileReplyItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PageKnowledgeBaseFileReplyItemMultiError, or nil if none found.
func (m *PageKnowledgeBaseFileReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *PageKnowledgeBaseFileReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DataType

	// no validation rules for Metadata

	// no validation rules for FileRelationID

	// no validation rules for FileName

	// no validation rules for FilePath

	// no validation rules for FileType

	// no validation rules for FileSize

	// no validation rules for PreEntityTag

	// no validation rules for EntityTag

	// no validation rules for FileStatus

	// no validation rules for FailedReason

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageKnowledgeBaseFileReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageKnowledgeBaseFileReplyItemValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageKnowledgeBaseFileReplyItemValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PageKnowledgeBaseFileReplyItemMultiError(errors)
	}

	return nil
}

// PageKnowledgeBaseFileReplyItemMultiError is an error wrapping multiple
// validation errors returned by PageKnowledgeBaseFileReplyItem.ValidateAll()
// if the designated constraints aren't met.
type PageKnowledgeBaseFileReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageKnowledgeBaseFileReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageKnowledgeBaseFileReplyItemMultiError) AllErrors() []error { return m }

// PageKnowledgeBaseFileReplyItemValidationError is the validation error
// returned by PageKnowledgeBaseFileReplyItem.Validate if the designated
// constraints aren't met.
type PageKnowledgeBaseFileReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageKnowledgeBaseFileReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageKnowledgeBaseFileReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageKnowledgeBaseFileReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageKnowledgeBaseFileReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageKnowledgeBaseFileReplyItemValidationError) ErrorName() string {
	return "PageKnowledgeBaseFileReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e PageKnowledgeBaseFileReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageKnowledgeBaseFileReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageKnowledgeBaseFileReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageKnowledgeBaseFileReplyItemValidationError{}

// Validate checks the field values on ExistKnowledgeBaseFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExistKnowledgeBaseFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExistKnowledgeBaseFileRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ExistKnowledgeBaseFileRequestMultiError, or nil if none found.
func (m *ExistKnowledgeBaseFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExistKnowledgeBaseFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for FileRelationID

	if len(errors) > 0 {
		return ExistKnowledgeBaseFileRequestMultiError(errors)
	}

	return nil
}

// ExistKnowledgeBaseFileRequestMultiError is an error wrapping multiple
// validation errors returned by ExistKnowledgeBaseFileRequest.ValidateAll()
// if the designated constraints aren't met.
type ExistKnowledgeBaseFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExistKnowledgeBaseFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExistKnowledgeBaseFileRequestMultiError) AllErrors() []error { return m }

// ExistKnowledgeBaseFileRequestValidationError is the validation error
// returned by ExistKnowledgeBaseFileRequest.Validate if the designated
// constraints aren't met.
type ExistKnowledgeBaseFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExistKnowledgeBaseFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExistKnowledgeBaseFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExistKnowledgeBaseFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExistKnowledgeBaseFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExistKnowledgeBaseFileRequestValidationError) ErrorName() string {
	return "ExistKnowledgeBaseFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExistKnowledgeBaseFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExistKnowledgeBaseFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExistKnowledgeBaseFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExistKnowledgeBaseFileRequestValidationError{}

// Validate checks the field values on ExistKnowledgeBaseFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExistKnowledgeBaseFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExistKnowledgeBaseFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExistKnowledgeBaseFileReplyMultiError, or nil if none found.
func (m *ExistKnowledgeBaseFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ExistKnowledgeBaseFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Exist

	if len(errors) > 0 {
		return ExistKnowledgeBaseFileReplyMultiError(errors)
	}

	return nil
}

// ExistKnowledgeBaseFileReplyMultiError is an error wrapping multiple
// validation errors returned by ExistKnowledgeBaseFileReply.ValidateAll() if
// the designated constraints aren't met.
type ExistKnowledgeBaseFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExistKnowledgeBaseFileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExistKnowledgeBaseFileReplyMultiError) AllErrors() []error { return m }

// ExistKnowledgeBaseFileReplyValidationError is the validation error returned
// by ExistKnowledgeBaseFileReply.Validate if the designated constraints
// aren't met.
type ExistKnowledgeBaseFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExistKnowledgeBaseFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExistKnowledgeBaseFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExistKnowledgeBaseFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExistKnowledgeBaseFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExistKnowledgeBaseFileReplyValidationError) ErrorName() string {
	return "ExistKnowledgeBaseFileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ExistKnowledgeBaseFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExistKnowledgeBaseFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExistKnowledgeBaseFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExistKnowledgeBaseFileReplyValidationError{}

// Validate checks the field values on GetFileKnowledgeBaseIDsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileKnowledgeBaseIDsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileKnowledgeBaseIDsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetFileKnowledgeBaseIDsRequestMultiError, or nil if none found.
func (m *GetFileKnowledgeBaseIDsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileKnowledgeBaseIDsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetFileRelationID() <= 0 {
		err := GetFileKnowledgeBaseIDsRequestValidationError{
			field:  "FileRelationID",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetFileKnowledgeBaseIDsRequestMultiError(errors)
	}

	return nil
}

// GetFileKnowledgeBaseIDsRequestMultiError is an error wrapping multiple
// validation errors returned by GetFileKnowledgeBaseIDsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetFileKnowledgeBaseIDsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileKnowledgeBaseIDsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileKnowledgeBaseIDsRequestMultiError) AllErrors() []error { return m }

// GetFileKnowledgeBaseIDsRequestValidationError is the validation error
// returned by GetFileKnowledgeBaseIDsRequest.Validate if the designated
// constraints aren't met.
type GetFileKnowledgeBaseIDsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileKnowledgeBaseIDsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileKnowledgeBaseIDsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileKnowledgeBaseIDsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileKnowledgeBaseIDsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileKnowledgeBaseIDsRequestValidationError) ErrorName() string {
	return "GetFileKnowledgeBaseIDsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileKnowledgeBaseIDsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileKnowledgeBaseIDsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileKnowledgeBaseIDsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileKnowledgeBaseIDsRequestValidationError{}

// Validate checks the field values on GetFileKnowledgeBaseIDsReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetFileKnowledgeBaseIDsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetFileKnowledgeBaseIDsReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetFileKnowledgeBaseIDsReplyMultiError, or nil if none found.
func (m *GetFileKnowledgeBaseIDsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetFileKnowledgeBaseIDsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetFileKnowledgeBaseIDsReplyMultiError(errors)
	}

	return nil
}

// GetFileKnowledgeBaseIDsReplyMultiError is an error wrapping multiple
// validation errors returned by GetFileKnowledgeBaseIDsReply.ValidateAll() if
// the designated constraints aren't met.
type GetFileKnowledgeBaseIDsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetFileKnowledgeBaseIDsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetFileKnowledgeBaseIDsReplyMultiError) AllErrors() []error { return m }

// GetFileKnowledgeBaseIDsReplyValidationError is the validation error returned
// by GetFileKnowledgeBaseIDsReply.Validate if the designated constraints
// aren't met.
type GetFileKnowledgeBaseIDsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetFileKnowledgeBaseIDsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetFileKnowledgeBaseIDsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetFileKnowledgeBaseIDsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetFileKnowledgeBaseIDsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetFileKnowledgeBaseIDsReplyValidationError) ErrorName() string {
	return "GetFileKnowledgeBaseIDsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetFileKnowledgeBaseIDsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetFileKnowledgeBaseIDsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetFileKnowledgeBaseIDsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetFileKnowledgeBaseIDsReplyValidationError{}

// Validate checks the field values on CountKnowledgeBaseFileRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountKnowledgeBaseFileRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountKnowledgeBaseFileRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CountKnowledgeBaseFileRequestMultiError, or nil if none found.
func (m *CountKnowledgeBaseFileRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CountKnowledgeBaseFileRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountKnowledgeBaseFileRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountKnowledgeBaseFileRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountKnowledgeBaseFileRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CountKnowledgeBaseFileRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CountKnowledgeBaseFileRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CountKnowledgeBaseFileRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CountKnowledgeBaseFileRequestMultiError(errors)
	}

	return nil
}

// CountKnowledgeBaseFileRequestMultiError is an error wrapping multiple
// validation errors returned by CountKnowledgeBaseFileRequest.ValidateAll()
// if the designated constraints aren't met.
type CountKnowledgeBaseFileRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountKnowledgeBaseFileRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountKnowledgeBaseFileRequestMultiError) AllErrors() []error { return m }

// CountKnowledgeBaseFileRequestValidationError is the validation error
// returned by CountKnowledgeBaseFileRequest.Validate if the designated
// constraints aren't met.
type CountKnowledgeBaseFileRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountKnowledgeBaseFileRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountKnowledgeBaseFileRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountKnowledgeBaseFileRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountKnowledgeBaseFileRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountKnowledgeBaseFileRequestValidationError) ErrorName() string {
	return "CountKnowledgeBaseFileRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CountKnowledgeBaseFileRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountKnowledgeBaseFileRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountKnowledgeBaseFileRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountKnowledgeBaseFileRequestValidationError{}

// Validate checks the field values on CountKnowledgeBaseFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountKnowledgeBaseFileReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountKnowledgeBaseFileReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CountKnowledgeBaseFileReplyMultiError, or nil if none found.
func (m *CountKnowledgeBaseFileReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CountKnowledgeBaseFileReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Count

	if len(errors) > 0 {
		return CountKnowledgeBaseFileReplyMultiError(errors)
	}

	return nil
}

// CountKnowledgeBaseFileReplyMultiError is an error wrapping multiple
// validation errors returned by CountKnowledgeBaseFileReply.ValidateAll() if
// the designated constraints aren't met.
type CountKnowledgeBaseFileReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountKnowledgeBaseFileReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountKnowledgeBaseFileReplyMultiError) AllErrors() []error { return m }

// CountKnowledgeBaseFileReplyValidationError is the validation error returned
// by CountKnowledgeBaseFileReply.Validate if the designated constraints
// aren't met.
type CountKnowledgeBaseFileReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountKnowledgeBaseFileReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountKnowledgeBaseFileReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountKnowledgeBaseFileReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountKnowledgeBaseFileReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountKnowledgeBaseFileReplyValidationError) ErrorName() string {
	return "CountKnowledgeBaseFileReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CountKnowledgeBaseFileReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountKnowledgeBaseFileReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountKnowledgeBaseFileReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountKnowledgeBaseFileReplyValidationError{}

// Validate checks the field values on FileAnalysisImmediatelyMessage with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FileAnalysisImmediatelyMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileAnalysisImmediatelyMessage with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FileAnalysisImmediatelyMessageMultiError, or nil if none found.
func (m *FileAnalysisImmediatelyMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *FileAnalysisImmediatelyMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for PreEntityTag

	// no validation rules for EntityTag

	// no validation rules for Index

	// no validation rules for ChunkIndex

	// no validation rules for ChunkSize

	if len(errors) > 0 {
		return FileAnalysisImmediatelyMessageMultiError(errors)
	}

	return nil
}

// FileAnalysisImmediatelyMessageMultiError is an error wrapping multiple
// validation errors returned by FileAnalysisImmediatelyMessage.ValidateAll()
// if the designated constraints aren't met.
type FileAnalysisImmediatelyMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileAnalysisImmediatelyMessageMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileAnalysisImmediatelyMessageMultiError) AllErrors() []error { return m }

// FileAnalysisImmediatelyMessageValidationError is the validation error
// returned by FileAnalysisImmediatelyMessage.Validate if the designated
// constraints aren't met.
type FileAnalysisImmediatelyMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileAnalysisImmediatelyMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileAnalysisImmediatelyMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileAnalysisImmediatelyMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileAnalysisImmediatelyMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileAnalysisImmediatelyMessageValidationError) ErrorName() string {
	return "FileAnalysisImmediatelyMessageValidationError"
}

// Error satisfies the builtin error interface
func (e FileAnalysisImmediatelyMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileAnalysisImmediatelyMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileAnalysisImmediatelyMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileAnalysisImmediatelyMessageValidationError{}

// Validate checks the field values on KnowledgeBaseInfo_UserInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KnowledgeBaseInfo_UserInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KnowledgeBaseInfo_UserInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KnowledgeBaseInfo_UserInfoMultiError, or nil if none found.
func (m *KnowledgeBaseInfo_UserInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *KnowledgeBaseInfo_UserInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserID

	// no validation rules for UserName

	// no validation rules for UserAvatar

	if len(errors) > 0 {
		return KnowledgeBaseInfo_UserInfoMultiError(errors)
	}

	return nil
}

// KnowledgeBaseInfo_UserInfoMultiError is an error wrapping multiple
// validation errors returned by KnowledgeBaseInfo_UserInfo.ValidateAll() if
// the designated constraints aren't met.
type KnowledgeBaseInfo_UserInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KnowledgeBaseInfo_UserInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KnowledgeBaseInfo_UserInfoMultiError) AllErrors() []error { return m }

// KnowledgeBaseInfo_UserInfoValidationError is the validation error returned
// by KnowledgeBaseInfo_UserInfo.Validate if the designated constraints aren't met.
type KnowledgeBaseInfo_UserInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KnowledgeBaseInfo_UserInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KnowledgeBaseInfo_UserInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KnowledgeBaseInfo_UserInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KnowledgeBaseInfo_UserInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KnowledgeBaseInfo_UserInfoValidationError) ErrorName() string {
	return "KnowledgeBaseInfo_UserInfoValidationError"
}

// Error satisfies the builtin error interface
func (e KnowledgeBaseInfo_UserInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKnowledgeBaseInfo_UserInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KnowledgeBaseInfo_UserInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KnowledgeBaseInfo_UserInfoValidationError{}

// Validate checks the field values on UpdateKnowledgeBaseRequest_OwnerOption
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateKnowledgeBaseRequest_OwnerOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateKnowledgeBaseRequest_OwnerOption with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateKnowledgeBaseRequest_OwnerOptionMultiError, or nil if none found.
func (m *UpdateKnowledgeBaseRequest_OwnerOption) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateKnowledgeBaseRequest_OwnerOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NewUserID

	if len(errors) > 0 {
		return UpdateKnowledgeBaseRequest_OwnerOptionMultiError(errors)
	}

	return nil
}

// UpdateKnowledgeBaseRequest_OwnerOptionMultiError is an error wrapping
// multiple validation errors returned by
// UpdateKnowledgeBaseRequest_OwnerOption.ValidateAll() if the designated
// constraints aren't met.
type UpdateKnowledgeBaseRequest_OwnerOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateKnowledgeBaseRequest_OwnerOptionMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateKnowledgeBaseRequest_OwnerOptionMultiError) AllErrors() []error { return m }

// UpdateKnowledgeBaseRequest_OwnerOptionValidationError is the validation
// error returned by UpdateKnowledgeBaseRequest_OwnerOption.Validate if the
// designated constraints aren't met.
type UpdateKnowledgeBaseRequest_OwnerOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateKnowledgeBaseRequest_OwnerOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateKnowledgeBaseRequest_OwnerOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateKnowledgeBaseRequest_OwnerOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateKnowledgeBaseRequest_OwnerOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateKnowledgeBaseRequest_OwnerOptionValidationError) ErrorName() string {
	return "UpdateKnowledgeBaseRequest_OwnerOptionValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateKnowledgeBaseRequest_OwnerOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateKnowledgeBaseRequest_OwnerOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateKnowledgeBaseRequest_OwnerOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateKnowledgeBaseRequest_OwnerOptionValidationError{}

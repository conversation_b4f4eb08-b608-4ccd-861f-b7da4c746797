// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: knowledgebase/knowledgebase.proto

package knowledgebase

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	KnowledgeBase_CreateKnowledgeBase_FullMethodName             = "/api.knowledgebase.KnowledgeBase/CreateKnowledgeBase"
	KnowledgeBase_UpdateKnowledgeBase_FullMethodName             = "/api.knowledgebase.KnowledgeBase/UpdateKnowledgeBase"
	KnowledgeBase_ChangeKnowledgeBaseOwner_FullMethodName        = "/api.knowledgebase.KnowledgeBase/ChangeKnowledgeBaseOwner"
	KnowledgeBase_DeleteKnowledgeBase_FullMethodName             = "/api.knowledgebase.KnowledgeBase/DeleteKnowledgeBase"
	KnowledgeBase_GetKnowledgeBases_FullMethodName               = "/api.knowledgebase.KnowledgeBase/GetKnowledgeBases"
	KnowledgeBase_GetAllKnowledgeBases_FullMethodName            = "/api.knowledgebase.KnowledgeBase/GetAllKnowledgeBases"
	KnowledgeBase_PageKnowledgeBase_FullMethodName               = "/api.knowledgebase.KnowledgeBase/PageKnowledgeBase"
	KnowledgeBase_AddKnowledgeBaseFileFromSearch_FullMethodName  = "/api.knowledgebase.KnowledgeBase/AddKnowledgeBaseFileFromSearch"
	KnowledgeBase_AddKnowledgeBaseFile_FullMethodName            = "/api.knowledgebase.KnowledgeBase/AddKnowledgeBaseFile"
	KnowledgeBase_DeleteKnowledgeBaseFile_FullMethodName         = "/api.knowledgebase.KnowledgeBase/DeleteKnowledgeBaseFile"
	KnowledgeBase_UpdateKnowledgeBaseFileMetadata_FullMethodName = "/api.knowledgebase.KnowledgeBase/UpdateKnowledgeBaseFileMetadata"
	KnowledgeBase_InnerUpdateKnowledgeBaseFile_FullMethodName    = "/api.knowledgebase.KnowledgeBase/InnerUpdateKnowledgeBaseFile"
	KnowledgeBase_PageKnowledgeBaseFile_FullMethodName           = "/api.knowledgebase.KnowledgeBase/PageKnowledgeBaseFile"
	KnowledgeBase_ExistKnowledgeBaseFile_FullMethodName          = "/api.knowledgebase.KnowledgeBase/ExistKnowledgeBaseFile"
	KnowledgeBase_GetFileKnowledgeBaseIDs_FullMethodName         = "/api.knowledgebase.KnowledgeBase/GetFileKnowledgeBaseIDs"
	KnowledgeBase_CountKnowledgeBaseFile_FullMethodName          = "/api.knowledgebase.KnowledgeBase/CountKnowledgeBaseFile"
)

// KnowledgeBaseClient is the client API for KnowledgeBase service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type KnowledgeBaseClient interface {
	// 创建知识库
	CreateKnowledgeBase(ctx context.Context, in *CreateKnowledgeBaseRequest, opts ...grpc.CallOption) (*CreateKnowledgeBaseReply, error)
	// 更新知识库
	UpdateKnowledgeBase(ctx context.Context, in *UpdateKnowledgeBaseRequest, opts ...grpc.CallOption) (*UpdateKnowledgeBaseReply, error)
	// 转移知识库所有者
	ChangeKnowledgeBaseOwner(ctx context.Context, in *ChangeKnowledgeBaseOwnerRequest, opts ...grpc.CallOption) (*ChangeKnowledgeBaseOwnerReply, error)
	// 删除知识库
	DeleteKnowledgeBase(ctx context.Context, in *DeleteKnowledgeBaseRequest, opts ...grpc.CallOption) (*DeleteKnowledgeBaseReply, error)
	// 获取知识库
	GetKnowledgeBases(ctx context.Context, in *GetKnowledgeBasesRequest, opts ...grpc.CallOption) (*GetKnowledgeBasesReply, error)
	// 获取所有知识库
	GetAllKnowledgeBases(ctx context.Context, in *GetAllKnowledgeBasesRequest, opts ...grpc.CallOption) (*GetAllKnowledgeBasesReply, error)
	// 分页获取知识库
	PageKnowledgeBase(ctx context.Context, in *PageKnowledgeBaseRequest, opts ...grpc.CallOption) (*PageKnowledgeBaseReply, error)
	// 知识库添加文件 根据搜索结果添加
	AddKnowledgeBaseFileFromSearch(ctx context.Context, in *AddKnowledgeBaseFileFromSearchRequest, opts ...grpc.CallOption) (*AddKnowledgeBaseFileFromSearchReply, error)
	// 知识库添加文件
	AddKnowledgeBaseFile(ctx context.Context, in *AddKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*AddKnowledgeBaseFileReply, error)
	// 知识库删除文件
	DeleteKnowledgeBaseFile(ctx context.Context, in *DeleteKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*DeleteKnowledgeBaseFileReply, error)
	// 更新知识库文件元数据
	UpdateKnowledgeBaseFileMetadata(ctx context.Context, in *UpdateKnowledgeBaseFileMetadataRequest, opts ...grpc.CallOption) (*UpdateKnowledgeBaseFileMetadataReply, error)
	// 更新知识库文件
	InnerUpdateKnowledgeBaseFile(ctx context.Context, in *InnerUpdateKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*InnerUpdateKnowledgeBaseFileReply, error)
	// 分页获取知识库文件
	PageKnowledgeBaseFile(ctx context.Context, in *PageKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*PageKnowledgeBaseFileReply, error)
	// 知识库文件是否存在
	ExistKnowledgeBaseFile(ctx context.Context, in *ExistKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*ExistKnowledgeBaseFileReply, error)
	// 获取文件相关知识库id
	GetFileKnowledgeBaseIDs(ctx context.Context, in *GetFileKnowledgeBaseIDsRequest, opts ...grpc.CallOption) (*GetFileKnowledgeBaseIDsReply, error)
	// 统计知识库文件数量
	CountKnowledgeBaseFile(ctx context.Context, in *CountKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*CountKnowledgeBaseFileReply, error)
}

type knowledgeBaseClient struct {
	cc grpc.ClientConnInterface
}

func NewKnowledgeBaseClient(cc grpc.ClientConnInterface) KnowledgeBaseClient {
	return &knowledgeBaseClient{cc}
}

func (c *knowledgeBaseClient) CreateKnowledgeBase(ctx context.Context, in *CreateKnowledgeBaseRequest, opts ...grpc.CallOption) (*CreateKnowledgeBaseReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateKnowledgeBaseReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_CreateKnowledgeBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) UpdateKnowledgeBase(ctx context.Context, in *UpdateKnowledgeBaseRequest, opts ...grpc.CallOption) (*UpdateKnowledgeBaseReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateKnowledgeBaseReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_UpdateKnowledgeBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) ChangeKnowledgeBaseOwner(ctx context.Context, in *ChangeKnowledgeBaseOwnerRequest, opts ...grpc.CallOption) (*ChangeKnowledgeBaseOwnerReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangeKnowledgeBaseOwnerReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_ChangeKnowledgeBaseOwner_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) DeleteKnowledgeBase(ctx context.Context, in *DeleteKnowledgeBaseRequest, opts ...grpc.CallOption) (*DeleteKnowledgeBaseReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteKnowledgeBaseReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_DeleteKnowledgeBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) GetKnowledgeBases(ctx context.Context, in *GetKnowledgeBasesRequest, opts ...grpc.CallOption) (*GetKnowledgeBasesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetKnowledgeBasesReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_GetKnowledgeBases_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) GetAllKnowledgeBases(ctx context.Context, in *GetAllKnowledgeBasesRequest, opts ...grpc.CallOption) (*GetAllKnowledgeBasesReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllKnowledgeBasesReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_GetAllKnowledgeBases_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) PageKnowledgeBase(ctx context.Context, in *PageKnowledgeBaseRequest, opts ...grpc.CallOption) (*PageKnowledgeBaseReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PageKnowledgeBaseReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_PageKnowledgeBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) AddKnowledgeBaseFileFromSearch(ctx context.Context, in *AddKnowledgeBaseFileFromSearchRequest, opts ...grpc.CallOption) (*AddKnowledgeBaseFileFromSearchReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddKnowledgeBaseFileFromSearchReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_AddKnowledgeBaseFileFromSearch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) AddKnowledgeBaseFile(ctx context.Context, in *AddKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*AddKnowledgeBaseFileReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddKnowledgeBaseFileReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_AddKnowledgeBaseFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) DeleteKnowledgeBaseFile(ctx context.Context, in *DeleteKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*DeleteKnowledgeBaseFileReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteKnowledgeBaseFileReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_DeleteKnowledgeBaseFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) UpdateKnowledgeBaseFileMetadata(ctx context.Context, in *UpdateKnowledgeBaseFileMetadataRequest, opts ...grpc.CallOption) (*UpdateKnowledgeBaseFileMetadataReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateKnowledgeBaseFileMetadataReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_UpdateKnowledgeBaseFileMetadata_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) InnerUpdateKnowledgeBaseFile(ctx context.Context, in *InnerUpdateKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*InnerUpdateKnowledgeBaseFileReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InnerUpdateKnowledgeBaseFileReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_InnerUpdateKnowledgeBaseFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) PageKnowledgeBaseFile(ctx context.Context, in *PageKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*PageKnowledgeBaseFileReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PageKnowledgeBaseFileReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_PageKnowledgeBaseFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) ExistKnowledgeBaseFile(ctx context.Context, in *ExistKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*ExistKnowledgeBaseFileReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExistKnowledgeBaseFileReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_ExistKnowledgeBaseFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) GetFileKnowledgeBaseIDs(ctx context.Context, in *GetFileKnowledgeBaseIDsRequest, opts ...grpc.CallOption) (*GetFileKnowledgeBaseIDsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFileKnowledgeBaseIDsReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_GetFileKnowledgeBaseIDs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *knowledgeBaseClient) CountKnowledgeBaseFile(ctx context.Context, in *CountKnowledgeBaseFileRequest, opts ...grpc.CallOption) (*CountKnowledgeBaseFileReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountKnowledgeBaseFileReply)
	err := c.cc.Invoke(ctx, KnowledgeBase_CountKnowledgeBaseFile_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// KnowledgeBaseServer is the server API for KnowledgeBase service.
// All implementations must embed UnimplementedKnowledgeBaseServer
// for forward compatibility.
type KnowledgeBaseServer interface {
	// 创建知识库
	CreateKnowledgeBase(context.Context, *CreateKnowledgeBaseRequest) (*CreateKnowledgeBaseReply, error)
	// 更新知识库
	UpdateKnowledgeBase(context.Context, *UpdateKnowledgeBaseRequest) (*UpdateKnowledgeBaseReply, error)
	// 转移知识库所有者
	ChangeKnowledgeBaseOwner(context.Context, *ChangeKnowledgeBaseOwnerRequest) (*ChangeKnowledgeBaseOwnerReply, error)
	// 删除知识库
	DeleteKnowledgeBase(context.Context, *DeleteKnowledgeBaseRequest) (*DeleteKnowledgeBaseReply, error)
	// 获取知识库
	GetKnowledgeBases(context.Context, *GetKnowledgeBasesRequest) (*GetKnowledgeBasesReply, error)
	// 获取所有知识库
	GetAllKnowledgeBases(context.Context, *GetAllKnowledgeBasesRequest) (*GetAllKnowledgeBasesReply, error)
	// 分页获取知识库
	PageKnowledgeBase(context.Context, *PageKnowledgeBaseRequest) (*PageKnowledgeBaseReply, error)
	// 知识库添加文件 根据搜索结果添加
	AddKnowledgeBaseFileFromSearch(context.Context, *AddKnowledgeBaseFileFromSearchRequest) (*AddKnowledgeBaseFileFromSearchReply, error)
	// 知识库添加文件
	AddKnowledgeBaseFile(context.Context, *AddKnowledgeBaseFileRequest) (*AddKnowledgeBaseFileReply, error)
	// 知识库删除文件
	DeleteKnowledgeBaseFile(context.Context, *DeleteKnowledgeBaseFileRequest) (*DeleteKnowledgeBaseFileReply, error)
	// 更新知识库文件元数据
	UpdateKnowledgeBaseFileMetadata(context.Context, *UpdateKnowledgeBaseFileMetadataRequest) (*UpdateKnowledgeBaseFileMetadataReply, error)
	// 更新知识库文件
	InnerUpdateKnowledgeBaseFile(context.Context, *InnerUpdateKnowledgeBaseFileRequest) (*InnerUpdateKnowledgeBaseFileReply, error)
	// 分页获取知识库文件
	PageKnowledgeBaseFile(context.Context, *PageKnowledgeBaseFileRequest) (*PageKnowledgeBaseFileReply, error)
	// 知识库文件是否存在
	ExistKnowledgeBaseFile(context.Context, *ExistKnowledgeBaseFileRequest) (*ExistKnowledgeBaseFileReply, error)
	// 获取文件相关知识库id
	GetFileKnowledgeBaseIDs(context.Context, *GetFileKnowledgeBaseIDsRequest) (*GetFileKnowledgeBaseIDsReply, error)
	// 统计知识库文件数量
	CountKnowledgeBaseFile(context.Context, *CountKnowledgeBaseFileRequest) (*CountKnowledgeBaseFileReply, error)
	mustEmbedUnimplementedKnowledgeBaseServer()
}

// UnimplementedKnowledgeBaseServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedKnowledgeBaseServer struct{}

func (UnimplementedKnowledgeBaseServer) CreateKnowledgeBase(context.Context, *CreateKnowledgeBaseRequest) (*CreateKnowledgeBaseReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseServer) UpdateKnowledgeBase(context.Context, *UpdateKnowledgeBaseRequest) (*UpdateKnowledgeBaseReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseServer) ChangeKnowledgeBaseOwner(context.Context, *ChangeKnowledgeBaseOwnerRequest) (*ChangeKnowledgeBaseOwnerReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangeKnowledgeBaseOwner not implemented")
}
func (UnimplementedKnowledgeBaseServer) DeleteKnowledgeBase(context.Context, *DeleteKnowledgeBaseRequest) (*DeleteKnowledgeBaseReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseServer) GetKnowledgeBases(context.Context, *GetKnowledgeBasesRequest) (*GetKnowledgeBasesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetKnowledgeBases not implemented")
}
func (UnimplementedKnowledgeBaseServer) GetAllKnowledgeBases(context.Context, *GetAllKnowledgeBasesRequest) (*GetAllKnowledgeBasesReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllKnowledgeBases not implemented")
}
func (UnimplementedKnowledgeBaseServer) PageKnowledgeBase(context.Context, *PageKnowledgeBaseRequest) (*PageKnowledgeBaseReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PageKnowledgeBase not implemented")
}
func (UnimplementedKnowledgeBaseServer) AddKnowledgeBaseFileFromSearch(context.Context, *AddKnowledgeBaseFileFromSearchRequest) (*AddKnowledgeBaseFileFromSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddKnowledgeBaseFileFromSearch not implemented")
}
func (UnimplementedKnowledgeBaseServer) AddKnowledgeBaseFile(context.Context, *AddKnowledgeBaseFileRequest) (*AddKnowledgeBaseFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddKnowledgeBaseFile not implemented")
}
func (UnimplementedKnowledgeBaseServer) DeleteKnowledgeBaseFile(context.Context, *DeleteKnowledgeBaseFileRequest) (*DeleteKnowledgeBaseFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteKnowledgeBaseFile not implemented")
}
func (UnimplementedKnowledgeBaseServer) UpdateKnowledgeBaseFileMetadata(context.Context, *UpdateKnowledgeBaseFileMetadataRequest) (*UpdateKnowledgeBaseFileMetadataReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateKnowledgeBaseFileMetadata not implemented")
}
func (UnimplementedKnowledgeBaseServer) InnerUpdateKnowledgeBaseFile(context.Context, *InnerUpdateKnowledgeBaseFileRequest) (*InnerUpdateKnowledgeBaseFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InnerUpdateKnowledgeBaseFile not implemented")
}
func (UnimplementedKnowledgeBaseServer) PageKnowledgeBaseFile(context.Context, *PageKnowledgeBaseFileRequest) (*PageKnowledgeBaseFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PageKnowledgeBaseFile not implemented")
}
func (UnimplementedKnowledgeBaseServer) ExistKnowledgeBaseFile(context.Context, *ExistKnowledgeBaseFileRequest) (*ExistKnowledgeBaseFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExistKnowledgeBaseFile not implemented")
}
func (UnimplementedKnowledgeBaseServer) GetFileKnowledgeBaseIDs(context.Context, *GetFileKnowledgeBaseIDsRequest) (*GetFileKnowledgeBaseIDsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFileKnowledgeBaseIDs not implemented")
}
func (UnimplementedKnowledgeBaseServer) CountKnowledgeBaseFile(context.Context, *CountKnowledgeBaseFileRequest) (*CountKnowledgeBaseFileReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountKnowledgeBaseFile not implemented")
}
func (UnimplementedKnowledgeBaseServer) mustEmbedUnimplementedKnowledgeBaseServer() {}
func (UnimplementedKnowledgeBaseServer) testEmbeddedByValue()                       {}

// UnsafeKnowledgeBaseServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to KnowledgeBaseServer will
// result in compilation errors.
type UnsafeKnowledgeBaseServer interface {
	mustEmbedUnimplementedKnowledgeBaseServer()
}

func RegisterKnowledgeBaseServer(s grpc.ServiceRegistrar, srv KnowledgeBaseServer) {
	// If the following call pancis, it indicates UnimplementedKnowledgeBaseServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&KnowledgeBase_ServiceDesc, srv)
}

func _KnowledgeBase_CreateKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateKnowledgeBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).CreateKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_CreateKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).CreateKnowledgeBase(ctx, req.(*CreateKnowledgeBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_UpdateKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKnowledgeBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).UpdateKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_UpdateKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).UpdateKnowledgeBase(ctx, req.(*UpdateKnowledgeBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_ChangeKnowledgeBaseOwner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangeKnowledgeBaseOwnerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).ChangeKnowledgeBaseOwner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_ChangeKnowledgeBaseOwner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).ChangeKnowledgeBaseOwner(ctx, req.(*ChangeKnowledgeBaseOwnerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_DeleteKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteKnowledgeBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).DeleteKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_DeleteKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).DeleteKnowledgeBase(ctx, req.(*DeleteKnowledgeBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_GetKnowledgeBases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetKnowledgeBasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).GetKnowledgeBases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_GetKnowledgeBases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).GetKnowledgeBases(ctx, req.(*GetKnowledgeBasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_GetAllKnowledgeBases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllKnowledgeBasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).GetAllKnowledgeBases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_GetAllKnowledgeBases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).GetAllKnowledgeBases(ctx, req.(*GetAllKnowledgeBasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_PageKnowledgeBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageKnowledgeBaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).PageKnowledgeBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_PageKnowledgeBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).PageKnowledgeBase(ctx, req.(*PageKnowledgeBaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_AddKnowledgeBaseFileFromSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddKnowledgeBaseFileFromSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).AddKnowledgeBaseFileFromSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_AddKnowledgeBaseFileFromSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).AddKnowledgeBaseFileFromSearch(ctx, req.(*AddKnowledgeBaseFileFromSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_AddKnowledgeBaseFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddKnowledgeBaseFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).AddKnowledgeBaseFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_AddKnowledgeBaseFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).AddKnowledgeBaseFile(ctx, req.(*AddKnowledgeBaseFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_DeleteKnowledgeBaseFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteKnowledgeBaseFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).DeleteKnowledgeBaseFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_DeleteKnowledgeBaseFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).DeleteKnowledgeBaseFile(ctx, req.(*DeleteKnowledgeBaseFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_UpdateKnowledgeBaseFileMetadata_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateKnowledgeBaseFileMetadataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).UpdateKnowledgeBaseFileMetadata(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_UpdateKnowledgeBaseFileMetadata_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).UpdateKnowledgeBaseFileMetadata(ctx, req.(*UpdateKnowledgeBaseFileMetadataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_InnerUpdateKnowledgeBaseFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InnerUpdateKnowledgeBaseFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).InnerUpdateKnowledgeBaseFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_InnerUpdateKnowledgeBaseFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).InnerUpdateKnowledgeBaseFile(ctx, req.(*InnerUpdateKnowledgeBaseFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_PageKnowledgeBaseFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageKnowledgeBaseFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).PageKnowledgeBaseFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_PageKnowledgeBaseFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).PageKnowledgeBaseFile(ctx, req.(*PageKnowledgeBaseFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_ExistKnowledgeBaseFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExistKnowledgeBaseFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).ExistKnowledgeBaseFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_ExistKnowledgeBaseFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).ExistKnowledgeBaseFile(ctx, req.(*ExistKnowledgeBaseFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_GetFileKnowledgeBaseIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFileKnowledgeBaseIDsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).GetFileKnowledgeBaseIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_GetFileKnowledgeBaseIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).GetFileKnowledgeBaseIDs(ctx, req.(*GetFileKnowledgeBaseIDsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _KnowledgeBase_CountKnowledgeBaseFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountKnowledgeBaseFileRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(KnowledgeBaseServer).CountKnowledgeBaseFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: KnowledgeBase_CountKnowledgeBaseFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(KnowledgeBaseServer).CountKnowledgeBaseFile(ctx, req.(*CountKnowledgeBaseFileRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// KnowledgeBase_ServiceDesc is the grpc.ServiceDesc for KnowledgeBase service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var KnowledgeBase_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.knowledgebase.KnowledgeBase",
	HandlerType: (*KnowledgeBaseServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateKnowledgeBase",
			Handler:    _KnowledgeBase_CreateKnowledgeBase_Handler,
		},
		{
			MethodName: "UpdateKnowledgeBase",
			Handler:    _KnowledgeBase_UpdateKnowledgeBase_Handler,
		},
		{
			MethodName: "ChangeKnowledgeBaseOwner",
			Handler:    _KnowledgeBase_ChangeKnowledgeBaseOwner_Handler,
		},
		{
			MethodName: "DeleteKnowledgeBase",
			Handler:    _KnowledgeBase_DeleteKnowledgeBase_Handler,
		},
		{
			MethodName: "GetKnowledgeBases",
			Handler:    _KnowledgeBase_GetKnowledgeBases_Handler,
		},
		{
			MethodName: "GetAllKnowledgeBases",
			Handler:    _KnowledgeBase_GetAllKnowledgeBases_Handler,
		},
		{
			MethodName: "PageKnowledgeBase",
			Handler:    _KnowledgeBase_PageKnowledgeBase_Handler,
		},
		{
			MethodName: "AddKnowledgeBaseFileFromSearch",
			Handler:    _KnowledgeBase_AddKnowledgeBaseFileFromSearch_Handler,
		},
		{
			MethodName: "AddKnowledgeBaseFile",
			Handler:    _KnowledgeBase_AddKnowledgeBaseFile_Handler,
		},
		{
			MethodName: "DeleteKnowledgeBaseFile",
			Handler:    _KnowledgeBase_DeleteKnowledgeBaseFile_Handler,
		},
		{
			MethodName: "UpdateKnowledgeBaseFileMetadata",
			Handler:    _KnowledgeBase_UpdateKnowledgeBaseFileMetadata_Handler,
		},
		{
			MethodName: "InnerUpdateKnowledgeBaseFile",
			Handler:    _KnowledgeBase_InnerUpdateKnowledgeBaseFile_Handler,
		},
		{
			MethodName: "PageKnowledgeBaseFile",
			Handler:    _KnowledgeBase_PageKnowledgeBaseFile_Handler,
		},
		{
			MethodName: "ExistKnowledgeBaseFile",
			Handler:    _KnowledgeBase_ExistKnowledgeBaseFile_Handler,
		},
		{
			MethodName: "GetFileKnowledgeBaseIDs",
			Handler:    _KnowledgeBase_GetFileKnowledgeBaseIDs_Handler,
		},
		{
			MethodName: "CountKnowledgeBaseFile",
			Handler:    _KnowledgeBase_CountKnowledgeBaseFile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "knowledgebase/knowledgebase.proto",
}

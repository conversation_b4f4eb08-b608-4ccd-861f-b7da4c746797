syntax = "proto3";

package api.knowledgebase;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "knowledgebase/enum.proto";
import "validate/validate.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase;knowledgebase";
option java_multiple_files = true;
option java_package = "api.knowledgebase";

service KnowledgeBase {
  // 创建知识库
  rpc CreateKnowledgeBase(CreateKnowledgeBaseRequest) returns (CreateKnowledgeBaseReply) {
    option (google.api.http) = {
      post: "/knowledgeBase/create"
      body: "*"
    };
  }

  // 更新知识库
  rpc UpdateKnowledgeBase(UpdateKnowledgeBaseRequest) returns (UpdateKnowledgeBaseReply) {
    option (google.api.http) = {
      post: "/knowledgeBase/update"
      body: "*"
    };
  }

  // 转移知识库所有者
  rpc ChangeKnowledgeBaseOwner(ChangeKnowledgeBaseOwnerRequest) returns (ChangeKnowledgeBaseOwnerReply) {
    option (google.api.http) = {
      post: "/knowledgeBase/changeKnowledgeBaseOwner"
      body: "*"
    };
  }

  // 删除知识库
  rpc DeleteKnowledgeBase(DeleteKnowledgeBaseRequest) returns (DeleteKnowledgeBaseReply) {
    option (google.api.http) = {
      post: "/knowledgeBase/delete"
      body: "*"
    };
  }

  // 获取知识库
  rpc GetKnowledgeBases(GetKnowledgeBasesRequest) returns (GetKnowledgeBasesReply) {
    option (google.api.http) = {
      post: "/knowledgeBase/get"
      body: "*"
    };
  }

  // 获取所有知识库
  rpc GetAllKnowledgeBases(GetAllKnowledgeBasesRequest) returns (GetAllKnowledgeBasesReply) {}

  // 分页获取知识库
  rpc PageKnowledgeBase(PageKnowledgeBaseRequest) returns (PageKnowledgeBaseReply) {
    option (google.api.http) = {get: "/knowledgeBase/page"};
  }

  // 知识库添加文件 根据搜索结果添加
  rpc AddKnowledgeBaseFileFromSearch(AddKnowledgeBaseFileFromSearchRequest) returns (AddKnowledgeBaseFileFromSearchReply) {
    option (google.api.http) = {
      post: "/knowledgeBase/addFileFromSearch"
      body: "*"
    };
  }

  // 知识库添加文件
  rpc AddKnowledgeBaseFile(AddKnowledgeBaseFileRequest) returns (AddKnowledgeBaseFileReply) {
    option (google.api.http) = {
      post: "/knowledgeBase/addFile"
      body: "*"
    };
  }

  // 知识库删除文件
  rpc DeleteKnowledgeBaseFile(DeleteKnowledgeBaseFileRequest) returns (DeleteKnowledgeBaseFileReply) {
    option (google.api.http) = {
      post: "/knowledgeBase/deleteFile"
      body: "*"
    };
  }

  // 更新知识库文件元数据
  rpc UpdateKnowledgeBaseFileMetadata(UpdateKnowledgeBaseFileMetadataRequest) returns (UpdateKnowledgeBaseFileMetadataReply) {
    option (google.api.http) = {
      post: "/knowledgeBase/updateFileMetadata"
      body: "*"
    };
  }

  // 更新知识库文件
  rpc InnerUpdateKnowledgeBaseFile(InnerUpdateKnowledgeBaseFileRequest) returns (InnerUpdateKnowledgeBaseFileReply) {}

  // 分页获取知识库文件
  rpc PageKnowledgeBaseFile(PageKnowledgeBaseFileRequest) returns (PageKnowledgeBaseFileReply) {
    option (google.api.http) = {get: "/knowledgeBase/pageFile"};
  }

  // 知识库文件是否存在
  rpc ExistKnowledgeBaseFile(ExistKnowledgeBaseFileRequest) returns (ExistKnowledgeBaseFileReply) {}

  // 获取文件相关知识库id
  rpc GetFileKnowledgeBaseIDs(GetFileKnowledgeBaseIDsRequest) returns (GetFileKnowledgeBaseIDsReply) {}

  // 统计知识库文件数量
  rpc CountKnowledgeBaseFile(CountKnowledgeBaseFileRequest) returns (CountKnowledgeBaseFileReply) {}
}

message KnowledgeBaseInfo {
  message UserInfo {
    // 拥有者用户id
    int64 userID = 1;
    // 拥有者用户名
    string userName = 2;
    // 拥有者头像
    string userAvatar = 3;
  }
  // 知识库id
  int64 id = 1;
  // 知识库名称
  string name = 2;
  // 是否公开
  bool public = 3;
  // 数据类型 1文档 2表格
  KnowledgeBaseDataType dataType = 10;
  // 拥有者用户id
  int64 userID = 4;
  // 拥有者用户名
  string userName = 5;
  // 拥有者头像
  string userAvatar = 6;
  // 可管理用户
  repeated UserInfo managers = 9;
  // 可编辑用户ids
  repeated UserInfo editableUsers = 8;
  // 创建时间
  google.protobuf.Timestamp createdAt = 7;
}

// CreateKnowledgeBase
message CreateKnowledgeBaseRequest {
  // 知识库名称
  string name = 1 [
    (validate.rules).string = {
      min_len: 1
      max_len: 100
    },
    (google.api.field_behavior) = REQUIRED
  ];
  // 是否公开
  bool public = 2 [(google.api.field_behavior) = REQUIRED];
  // 数据类型 1文档 2表格
  KnowledgeBaseDataType dataType = 10 [
    (validate.rules).enum = {
      in: [
        1,
        2
      ]
    },
    (google.api.field_behavior) = REQUIRED
  ];
  // 拥有者用户id
  int64 userID = 4 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  // 管理者用户id
  repeated int64 managerUserIDs = 5;
  // 可编辑用户ids
  repeated int64 editableUserIDs = 3;
}
message CreateKnowledgeBaseReply {
  // 知识库id
  int64 id = 1;
}

// UpdateKnowledgeBase
message UpdateKnowledgeBaseRequest {
  message OwnerOption {
    int64 newUserID = 1;
    repeated int64 managerUserIDs = 2;
  }
  // 知识库id
  int64 id = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  // 知识库名称
  string name = 2 [
    (validate.rules).string = {
      min_len: 1
      max_len: 100
    },
    (google.api.field_behavior) = REQUIRED
  ];
  // 是否公开
  bool public = 3 [(google.api.field_behavior) = REQUIRED];
  // 可编辑用户ids
  repeated int64 editableUserIDs = 4;
  // 管理员选项
  optional OwnerOption ownerOption = 5;
}
message UpdateKnowledgeBaseReply {}

// ChangeKnowledgeBaseOwner
message ChangeKnowledgeBaseOwnerRequest {
  // 知识库id
  repeated int64 ids = 1;
  // 转移至用户id
  int64 newUserID = 2 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
}
message ChangeKnowledgeBaseOwnerReply {}

// DeleteKnowledgeBase
message DeleteKnowledgeBaseRequest {
  // 知识库id
  int64 id = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
}
message DeleteKnowledgeBaseReply {}

// GetKnowledgeBases
message GetKnowledgeBasesRequest {
  // 知识库ids
  repeated int64 ids = 1;
}
message GetKnowledgeBasesReply {
  repeated KnowledgeBaseInfo records = 1;
}

// GetAllKnowledgeBases
message GetAllKnowledgeBasesRequest {}
message GetAllKnowledgeBasesReply {
  repeated KnowledgeBaseInfo records = 1;
}

// PageKnowledgeBase
message PageKnowledgeBaseRequest {
  int64 pageNum = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  int64 pageSize = 2 [
    (validate.rules).int64 = {
      gt: 0
      lte: 100
    },
    (google.api.field_behavior) = REQUIRED
  ];
  // 知识库名称
  string name = 3;
  // 是否全部 false:我可编辑的
  bool all = 4;
  // 数据类型 0全部 1文档 2表格
  int32 dataType = 10;
}
message PageKnowledgeBaseReply {
  int64 total = 1;
  repeated KnowledgeBaseInfo records = 2;
}

// AddKnowledgeBaseFileFromSearch
message AddKnowledgeBaseFileFromSearchRequest {
  // 知识库id
  int64 id = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  string query = 2;
  int64 searchType = 3;
  string fileType = 4;
  repeated int64 ownerIDs = 5;
  google.protobuf.Timestamp startTime = 6;
  google.protobuf.Timestamp endTime = 7;
  string classPath = 8;
  string path = 9;
}
message AddKnowledgeBaseFileFromSearchReply {}

// AddKnowledgeBaseFile
message AddKnowledgeBaseFileRequest {
  // 知识库id
  int64 id = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  // 文件ids
  repeated int64 fileRelationIDs = 2 [
    (validate.rules).repeated.min_items = 1,
    (google.api.field_behavior) = REQUIRED
  ];
  // 前端忽略这个参数，仅rpc使用
  bool skipQuickCreate = 3;
}
message AddKnowledgeBaseFileReply {}

// DeleteKnowledgeBaseFile
message DeleteKnowledgeBaseFileRequest {
  // 知识库id
  int64 id = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  // 文件ids
  repeated int64 fileRelationIDs = 2 [
    (validate.rules).repeated.min_items = 1,
    (google.api.field_behavior) = REQUIRED
  ];
}
message DeleteKnowledgeBaseFileReply {}

// UpdateKnowledgeBaseFileMetadata
message UpdateKnowledgeBaseFileMetadataRequest {
  // 知识库id
  int64 id = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  // 文件id
  int64 fileRelationID = 2 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  // 元数据
  string metadata = 3;
}
message UpdateKnowledgeBaseFileMetadataReply {}

// InnerUpdateKnowledgeBaseFileRequest
message InnerUpdateKnowledgeBaseFileRequest {
  // 知识库id
  int64 id = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  // 文件id
  int64 fileRelationID = 2 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  // 元数据
  optional string metadata = 3;
  // 文件状态 0等待 1处理中 2成功 3失败
  optional KnowledgeBaseFileStatus status = 4 [
    (validate.rules).enum = {
      in: [
        0,
        1,
        2,
        3
      ]
    },
    (google.api.field_behavior) = REQUIRED
  ];
  // 失败原因
  optional string failedReason = 5;
}
message InnerUpdateKnowledgeBaseFileReply {}

// PageKnowledgeBaseFile
message PageKnowledgeBaseFileRequest {
  int64 pageNum = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  int64 pageSize = 2 [
    (validate.rules).int64 = {
      gt: 0
      lte: 100
    },
    (google.api.field_behavior) = REQUIRED
  ];
  // 知识库id
  int64 id = 3 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  // 文件名称
  string fileName = 4;
}
message PageKnowledgeBaseFileReply {
  int64 total = 1;
  repeated PageKnowledgeBaseFileReplyItem records = 2;
}
message PageKnowledgeBaseFileReplyItem {
  // 数据类型 1文档 2表格
  KnowledgeBaseDataType dataType = 11;
  // 元数据 excel_schema
  string metadata = 12;
  // 文件id
  int64 fileRelationID = 1;
  // 文件名称
  string fileName = 2;
  // 文件路径
  string filePath = 3;
  // 文件类型
  string fileType = 4;
  // 文件大小
  int64 fileSize = 5;
  // preEntityTag
  string preEntityTag = 6;
  // entityTag
  string entityTag = 7;
  // 文件状态 0等待 1处理中 2成功 3失败
  KnowledgeBaseFileStatus fileStatus = 10;
  // 失败原因
  string failedReason = 13;
  // 文件分类
  repeated string fileCategories = 8;
  // 创建时间
  google.protobuf.Timestamp createdAt = 9;
}

// ExistKnowledgeBaseFile
message ExistKnowledgeBaseFileRequest {
  int64 id = 1;
  int64 fileRelationID = 2;
}
message ExistKnowledgeBaseFileReply {
  bool exist = 1;
}

// GetFileKnowledgeBaseIDs
message GetFileKnowledgeBaseIDsRequest {
  // 文件id
  int64 fileRelationID = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
}
message GetFileKnowledgeBaseIDsReply {
  // 文档知识库ids
  repeated int64 docKnowledgeBaseIDs = 1;
  // 表格知识库ids
  repeated int64 sheetKnowledgeBaseIDs = 2;
}

message CountKnowledgeBaseFileRequest {
  google.protobuf.Timestamp startTime = 1;
  google.protobuf.Timestamp endTime = 2;
}
message CountKnowledgeBaseFileReply {
  int64 count = 1;
}

message FileAnalysisImmediatelyMessage {
  int64 fileRelationID = 1;
  string preEntityTag = 2;
  string entityTag = 3;
  int64 index = 4;
  int64 chunkIndex = 5;
  int64 chunkSize = 6;
}

syntax = "proto3";

package api.knowledgebase;

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase;knowledgebase";
option java_multiple_files = true;
option java_package = "api.knowledgebase";

enum KnowledgeBaseDataType {
  KnowledgeBaseDataTypeUnknown = 0; // 未知
  KnowledgeBaseDataTypeDoc = 1; // 文档
  KnowledgeBaseDataTypeSheet = 2; // 表格
}

enum KnowledgeBaseFileStatus {
  KnowledgeBaseFileStatusWaiting = 0; // 等待
  KnowledgeBaseFileStatusProcessing = 1; // 处理中
  KnowledgeBaseFileStatusSuccess = 2; // 成功
  KnowledgeBaseFileStatusFailed = 3; // 失败
}

syntax = "proto3";

package api.knowledgebase;

import "errors/errors.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase;knowledgebase";

enum ErrorReason {
  option (errors.default_code) = 500;
  KNOWLEDGE_BASE_COMMON = 0 [(errors.code) = 400];
  KN<PERSON>LEDGE_BASE_ILLEGAL_OPERATION = 1 [(errors.code) = 400];
  KNOWLEDGE_BASE_EXIST_AGENT = 2 [(errors.code) = 400];
  KNOWLEDGE_BASE_ADD_TOO_MUCH_FILE = 3 [(errors.code) = 400];
}

// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: knowledgebase/knowledgebase.proto

package knowledgebase

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationKnowledgeBaseAddKnowledgeBaseFile = "/api.knowledgebase.KnowledgeBase/AddKnowledgeBaseFile"
const OperationKnowledgeBaseAddKnowledgeBaseFileFromSearch = "/api.knowledgebase.KnowledgeBase/AddKnowledgeBaseFileFromSearch"
const OperationKnowledgeBaseChangeKnowledgeBaseOwner = "/api.knowledgebase.KnowledgeBase/ChangeKnowledgeBaseOwner"
const OperationKnowledgeBaseCreateKnowledgeBase = "/api.knowledgebase.KnowledgeBase/CreateKnowledgeBase"
const OperationKnowledgeBaseDeleteKnowledgeBase = "/api.knowledgebase.KnowledgeBase/DeleteKnowledgeBase"
const OperationKnowledgeBaseDeleteKnowledgeBaseFile = "/api.knowledgebase.KnowledgeBase/DeleteKnowledgeBaseFile"
const OperationKnowledgeBaseGetKnowledgeBases = "/api.knowledgebase.KnowledgeBase/GetKnowledgeBases"
const OperationKnowledgeBasePageKnowledgeBase = "/api.knowledgebase.KnowledgeBase/PageKnowledgeBase"
const OperationKnowledgeBasePageKnowledgeBaseFile = "/api.knowledgebase.KnowledgeBase/PageKnowledgeBaseFile"
const OperationKnowledgeBaseUpdateKnowledgeBase = "/api.knowledgebase.KnowledgeBase/UpdateKnowledgeBase"
const OperationKnowledgeBaseUpdateKnowledgeBaseFileMetadata = "/api.knowledgebase.KnowledgeBase/UpdateKnowledgeBaseFileMetadata"

type KnowledgeBaseHTTPServer interface {
	// AddKnowledgeBaseFile 知识库添加文件
	AddKnowledgeBaseFile(context.Context, *AddKnowledgeBaseFileRequest) (*AddKnowledgeBaseFileReply, error)
	// AddKnowledgeBaseFileFromSearch 知识库添加文件 根据搜索结果添加
	AddKnowledgeBaseFileFromSearch(context.Context, *AddKnowledgeBaseFileFromSearchRequest) (*AddKnowledgeBaseFileFromSearchReply, error)
	// ChangeKnowledgeBaseOwner 转移知识库所有者
	ChangeKnowledgeBaseOwner(context.Context, *ChangeKnowledgeBaseOwnerRequest) (*ChangeKnowledgeBaseOwnerReply, error)
	// CreateKnowledgeBase 创建知识库
	CreateKnowledgeBase(context.Context, *CreateKnowledgeBaseRequest) (*CreateKnowledgeBaseReply, error)
	// DeleteKnowledgeBase 删除知识库
	DeleteKnowledgeBase(context.Context, *DeleteKnowledgeBaseRequest) (*DeleteKnowledgeBaseReply, error)
	// DeleteKnowledgeBaseFile 知识库删除文件
	DeleteKnowledgeBaseFile(context.Context, *DeleteKnowledgeBaseFileRequest) (*DeleteKnowledgeBaseFileReply, error)
	// GetKnowledgeBases 获取知识库
	GetKnowledgeBases(context.Context, *GetKnowledgeBasesRequest) (*GetKnowledgeBasesReply, error)
	// PageKnowledgeBase 分页获取知识库
	PageKnowledgeBase(context.Context, *PageKnowledgeBaseRequest) (*PageKnowledgeBaseReply, error)
	// PageKnowledgeBaseFile 分页获取知识库文件
	PageKnowledgeBaseFile(context.Context, *PageKnowledgeBaseFileRequest) (*PageKnowledgeBaseFileReply, error)
	// UpdateKnowledgeBase 更新知识库
	UpdateKnowledgeBase(context.Context, *UpdateKnowledgeBaseRequest) (*UpdateKnowledgeBaseReply, error)
	// UpdateKnowledgeBaseFileMetadata 更新知识库文件元数据
	UpdateKnowledgeBaseFileMetadata(context.Context, *UpdateKnowledgeBaseFileMetadataRequest) (*UpdateKnowledgeBaseFileMetadataReply, error)
}

func RegisterKnowledgeBaseHTTPServer(s *http.Server, srv KnowledgeBaseHTTPServer) {
	r := s.Route("/")
	r.POST("/knowledgeBase/create", _KnowledgeBase_CreateKnowledgeBase0_HTTP_Handler(srv))
	r.POST("/knowledgeBase/update", _KnowledgeBase_UpdateKnowledgeBase0_HTTP_Handler(srv))
	r.POST("/knowledgeBase/changeKnowledgeBaseOwner", _KnowledgeBase_ChangeKnowledgeBaseOwner0_HTTP_Handler(srv))
	r.POST("/knowledgeBase/delete", _KnowledgeBase_DeleteKnowledgeBase0_HTTP_Handler(srv))
	r.POST("/knowledgeBase/get", _KnowledgeBase_GetKnowledgeBases0_HTTP_Handler(srv))
	r.GET("/knowledgeBase/page", _KnowledgeBase_PageKnowledgeBase0_HTTP_Handler(srv))
	r.POST("/knowledgeBase/addFileFromSearch", _KnowledgeBase_AddKnowledgeBaseFileFromSearch0_HTTP_Handler(srv))
	r.POST("/knowledgeBase/addFile", _KnowledgeBase_AddKnowledgeBaseFile0_HTTP_Handler(srv))
	r.POST("/knowledgeBase/deleteFile", _KnowledgeBase_DeleteKnowledgeBaseFile0_HTTP_Handler(srv))
	r.POST("/knowledgeBase/updateFileMetadata", _KnowledgeBase_UpdateKnowledgeBaseFileMetadata0_HTTP_Handler(srv))
	r.GET("/knowledgeBase/pageFile", _KnowledgeBase_PageKnowledgeBaseFile0_HTTP_Handler(srv))
}

func _KnowledgeBase_CreateKnowledgeBase0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateKnowledgeBaseRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBaseCreateKnowledgeBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateKnowledgeBase(ctx, req.(*CreateKnowledgeBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateKnowledgeBaseReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_UpdateKnowledgeBase0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateKnowledgeBaseRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBaseUpdateKnowledgeBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateKnowledgeBase(ctx, req.(*UpdateKnowledgeBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateKnowledgeBaseReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_ChangeKnowledgeBaseOwner0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChangeKnowledgeBaseOwnerRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBaseChangeKnowledgeBaseOwner)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChangeKnowledgeBaseOwner(ctx, req.(*ChangeKnowledgeBaseOwnerRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ChangeKnowledgeBaseOwnerReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_DeleteKnowledgeBase0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteKnowledgeBaseRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBaseDeleteKnowledgeBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteKnowledgeBase(ctx, req.(*DeleteKnowledgeBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteKnowledgeBaseReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_GetKnowledgeBases0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetKnowledgeBasesRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBaseGetKnowledgeBases)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetKnowledgeBases(ctx, req.(*GetKnowledgeBasesRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetKnowledgeBasesReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_PageKnowledgeBase0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PageKnowledgeBaseRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBasePageKnowledgeBase)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PageKnowledgeBase(ctx, req.(*PageKnowledgeBaseRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PageKnowledgeBaseReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_AddKnowledgeBaseFileFromSearch0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddKnowledgeBaseFileFromSearchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBaseAddKnowledgeBaseFileFromSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddKnowledgeBaseFileFromSearch(ctx, req.(*AddKnowledgeBaseFileFromSearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddKnowledgeBaseFileFromSearchReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_AddKnowledgeBaseFile0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddKnowledgeBaseFileRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBaseAddKnowledgeBaseFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddKnowledgeBaseFile(ctx, req.(*AddKnowledgeBaseFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddKnowledgeBaseFileReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_DeleteKnowledgeBaseFile0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteKnowledgeBaseFileRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBaseDeleteKnowledgeBaseFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteKnowledgeBaseFile(ctx, req.(*DeleteKnowledgeBaseFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteKnowledgeBaseFileReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_UpdateKnowledgeBaseFileMetadata0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateKnowledgeBaseFileMetadataRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBaseUpdateKnowledgeBaseFileMetadata)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateKnowledgeBaseFileMetadata(ctx, req.(*UpdateKnowledgeBaseFileMetadataRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateKnowledgeBaseFileMetadataReply)
		return ctx.Result(200, reply)
	}
}

func _KnowledgeBase_PageKnowledgeBaseFile0_HTTP_Handler(srv KnowledgeBaseHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PageKnowledgeBaseFileRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationKnowledgeBasePageKnowledgeBaseFile)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PageKnowledgeBaseFile(ctx, req.(*PageKnowledgeBaseFileRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PageKnowledgeBaseFileReply)
		return ctx.Result(200, reply)
	}
}

type KnowledgeBaseHTTPClient interface {
	AddKnowledgeBaseFile(ctx context.Context, req *AddKnowledgeBaseFileRequest, opts ...http.CallOption) (rsp *AddKnowledgeBaseFileReply, err error)
	AddKnowledgeBaseFileFromSearch(ctx context.Context, req *AddKnowledgeBaseFileFromSearchRequest, opts ...http.CallOption) (rsp *AddKnowledgeBaseFileFromSearchReply, err error)
	ChangeKnowledgeBaseOwner(ctx context.Context, req *ChangeKnowledgeBaseOwnerRequest, opts ...http.CallOption) (rsp *ChangeKnowledgeBaseOwnerReply, err error)
	CreateKnowledgeBase(ctx context.Context, req *CreateKnowledgeBaseRequest, opts ...http.CallOption) (rsp *CreateKnowledgeBaseReply, err error)
	DeleteKnowledgeBase(ctx context.Context, req *DeleteKnowledgeBaseRequest, opts ...http.CallOption) (rsp *DeleteKnowledgeBaseReply, err error)
	DeleteKnowledgeBaseFile(ctx context.Context, req *DeleteKnowledgeBaseFileRequest, opts ...http.CallOption) (rsp *DeleteKnowledgeBaseFileReply, err error)
	GetKnowledgeBases(ctx context.Context, req *GetKnowledgeBasesRequest, opts ...http.CallOption) (rsp *GetKnowledgeBasesReply, err error)
	PageKnowledgeBase(ctx context.Context, req *PageKnowledgeBaseRequest, opts ...http.CallOption) (rsp *PageKnowledgeBaseReply, err error)
	PageKnowledgeBaseFile(ctx context.Context, req *PageKnowledgeBaseFileRequest, opts ...http.CallOption) (rsp *PageKnowledgeBaseFileReply, err error)
	UpdateKnowledgeBase(ctx context.Context, req *UpdateKnowledgeBaseRequest, opts ...http.CallOption) (rsp *UpdateKnowledgeBaseReply, err error)
	UpdateKnowledgeBaseFileMetadata(ctx context.Context, req *UpdateKnowledgeBaseFileMetadataRequest, opts ...http.CallOption) (rsp *UpdateKnowledgeBaseFileMetadataReply, err error)
}

type KnowledgeBaseHTTPClientImpl struct {
	cc *http.Client
}

func NewKnowledgeBaseHTTPClient(client *http.Client) KnowledgeBaseHTTPClient {
	return &KnowledgeBaseHTTPClientImpl{client}
}

func (c *KnowledgeBaseHTTPClientImpl) AddKnowledgeBaseFile(ctx context.Context, in *AddKnowledgeBaseFileRequest, opts ...http.CallOption) (*AddKnowledgeBaseFileReply, error) {
	var out AddKnowledgeBaseFileReply
	pattern := "/knowledgeBase/addFile"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationKnowledgeBaseAddKnowledgeBaseFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) AddKnowledgeBaseFileFromSearch(ctx context.Context, in *AddKnowledgeBaseFileFromSearchRequest, opts ...http.CallOption) (*AddKnowledgeBaseFileFromSearchReply, error) {
	var out AddKnowledgeBaseFileFromSearchReply
	pattern := "/knowledgeBase/addFileFromSearch"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationKnowledgeBaseAddKnowledgeBaseFileFromSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) ChangeKnowledgeBaseOwner(ctx context.Context, in *ChangeKnowledgeBaseOwnerRequest, opts ...http.CallOption) (*ChangeKnowledgeBaseOwnerReply, error) {
	var out ChangeKnowledgeBaseOwnerReply
	pattern := "/knowledgeBase/changeKnowledgeBaseOwner"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationKnowledgeBaseChangeKnowledgeBaseOwner))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) CreateKnowledgeBase(ctx context.Context, in *CreateKnowledgeBaseRequest, opts ...http.CallOption) (*CreateKnowledgeBaseReply, error) {
	var out CreateKnowledgeBaseReply
	pattern := "/knowledgeBase/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationKnowledgeBaseCreateKnowledgeBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) DeleteKnowledgeBase(ctx context.Context, in *DeleteKnowledgeBaseRequest, opts ...http.CallOption) (*DeleteKnowledgeBaseReply, error) {
	var out DeleteKnowledgeBaseReply
	pattern := "/knowledgeBase/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationKnowledgeBaseDeleteKnowledgeBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) DeleteKnowledgeBaseFile(ctx context.Context, in *DeleteKnowledgeBaseFileRequest, opts ...http.CallOption) (*DeleteKnowledgeBaseFileReply, error) {
	var out DeleteKnowledgeBaseFileReply
	pattern := "/knowledgeBase/deleteFile"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationKnowledgeBaseDeleteKnowledgeBaseFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) GetKnowledgeBases(ctx context.Context, in *GetKnowledgeBasesRequest, opts ...http.CallOption) (*GetKnowledgeBasesReply, error) {
	var out GetKnowledgeBasesReply
	pattern := "/knowledgeBase/get"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationKnowledgeBaseGetKnowledgeBases))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) PageKnowledgeBase(ctx context.Context, in *PageKnowledgeBaseRequest, opts ...http.CallOption) (*PageKnowledgeBaseReply, error) {
	var out PageKnowledgeBaseReply
	pattern := "/knowledgeBase/page"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationKnowledgeBasePageKnowledgeBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) PageKnowledgeBaseFile(ctx context.Context, in *PageKnowledgeBaseFileRequest, opts ...http.CallOption) (*PageKnowledgeBaseFileReply, error) {
	var out PageKnowledgeBaseFileReply
	pattern := "/knowledgeBase/pageFile"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationKnowledgeBasePageKnowledgeBaseFile))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) UpdateKnowledgeBase(ctx context.Context, in *UpdateKnowledgeBaseRequest, opts ...http.CallOption) (*UpdateKnowledgeBaseReply, error) {
	var out UpdateKnowledgeBaseReply
	pattern := "/knowledgeBase/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationKnowledgeBaseUpdateKnowledgeBase))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *KnowledgeBaseHTTPClientImpl) UpdateKnowledgeBaseFileMetadata(ctx context.Context, in *UpdateKnowledgeBaseFileMetadataRequest, opts ...http.CallOption) (*UpdateKnowledgeBaseFileMetadataReply, error) {
	var out UpdateKnowledgeBaseFileMetadataReply
	pattern := "/knowledgeBase/updateFileMetadata"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationKnowledgeBaseUpdateKnowledgeBaseFileMetadata))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

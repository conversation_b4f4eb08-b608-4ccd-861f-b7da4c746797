// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: knowledgebase/enum.proto

package knowledgebase

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KnowledgeBaseDataType int32

const (
	KnowledgeBaseDataType_KnowledgeBaseDataTypeUnknown KnowledgeBaseDataType = 0 // 未知
	KnowledgeBaseDataType_KnowledgeBaseDataTypeDoc     KnowledgeBaseDataType = 1 // 文档
	KnowledgeBaseDataType_KnowledgeBaseDataTypeSheet   KnowledgeBaseDataType = 2 // 表格
)

// Enum value maps for KnowledgeBaseDataType.
var (
	KnowledgeBaseDataType_name = map[int32]string{
		0: "KnowledgeBaseDataTypeUnknown",
		1: "KnowledgeBaseDataTypeDoc",
		2: "KnowledgeBaseDataTypeSheet",
	}
	KnowledgeBaseDataType_value = map[string]int32{
		"KnowledgeBaseDataTypeUnknown": 0,
		"KnowledgeBaseDataTypeDoc":     1,
		"KnowledgeBaseDataTypeSheet":   2,
	}
)

func (x KnowledgeBaseDataType) Enum() *KnowledgeBaseDataType {
	p := new(KnowledgeBaseDataType)
	*p = x
	return p
}

func (x KnowledgeBaseDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeBaseDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledgebase_enum_proto_enumTypes[0].Descriptor()
}

func (KnowledgeBaseDataType) Type() protoreflect.EnumType {
	return &file_knowledgebase_enum_proto_enumTypes[0]
}

func (x KnowledgeBaseDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeBaseDataType.Descriptor instead.
func (KnowledgeBaseDataType) EnumDescriptor() ([]byte, []int) {
	return file_knowledgebase_enum_proto_rawDescGZIP(), []int{0}
}

type KnowledgeBaseFileStatus int32

const (
	KnowledgeBaseFileStatus_KnowledgeBaseFileStatusWaiting    KnowledgeBaseFileStatus = 0 // 等待
	KnowledgeBaseFileStatus_KnowledgeBaseFileStatusProcessing KnowledgeBaseFileStatus = 1 // 处理中
	KnowledgeBaseFileStatus_KnowledgeBaseFileStatusSuccess    KnowledgeBaseFileStatus = 2 // 成功
	KnowledgeBaseFileStatus_KnowledgeBaseFileStatusFailed     KnowledgeBaseFileStatus = 3 // 失败
)

// Enum value maps for KnowledgeBaseFileStatus.
var (
	KnowledgeBaseFileStatus_name = map[int32]string{
		0: "KnowledgeBaseFileStatusWaiting",
		1: "KnowledgeBaseFileStatusProcessing",
		2: "KnowledgeBaseFileStatusSuccess",
		3: "KnowledgeBaseFileStatusFailed",
	}
	KnowledgeBaseFileStatus_value = map[string]int32{
		"KnowledgeBaseFileStatusWaiting":    0,
		"KnowledgeBaseFileStatusProcessing": 1,
		"KnowledgeBaseFileStatusSuccess":    2,
		"KnowledgeBaseFileStatusFailed":     3,
	}
)

func (x KnowledgeBaseFileStatus) Enum() *KnowledgeBaseFileStatus {
	p := new(KnowledgeBaseFileStatus)
	*p = x
	return p
}

func (x KnowledgeBaseFileStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KnowledgeBaseFileStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledgebase_enum_proto_enumTypes[1].Descriptor()
}

func (KnowledgeBaseFileStatus) Type() protoreflect.EnumType {
	return &file_knowledgebase_enum_proto_enumTypes[1]
}

func (x KnowledgeBaseFileStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KnowledgeBaseFileStatus.Descriptor instead.
func (KnowledgeBaseFileStatus) EnumDescriptor() ([]byte, []int) {
	return file_knowledgebase_enum_proto_rawDescGZIP(), []int{1}
}

var File_knowledgebase_enum_proto protoreflect.FileDescriptor

var file_knowledgebase_enum_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x2e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2a, 0x77, 0x0a,
	0x15, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x44, 0x6f, 0x63, 0x10, 0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x53,
	0x68, 0x65, 0x65, 0x74, 0x10, 0x02, 0x2a, 0xab, 0x01, 0x0a, 0x17, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x57, 0x61, 0x69,
	0x74, 0x69, 0x6e, 0x67, 0x10, 0x00, 0x12, 0x25, 0x0a, 0x21, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x22, 0x0a,
	0x1e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x10,
	0x02, 0x12, 0x21, 0x0a, 0x1d, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x61, 0x69, 0x6c,
	0x65, 0x64, 0x10, 0x03, 0x42, 0x5f, 0x0a, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x50, 0x01, 0x5a, 0x48, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f,
	0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x61,
	0x69, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x3b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x62, 0x61, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_knowledgebase_enum_proto_rawDescOnce sync.Once
	file_knowledgebase_enum_proto_rawDescData = file_knowledgebase_enum_proto_rawDesc
)

func file_knowledgebase_enum_proto_rawDescGZIP() []byte {
	file_knowledgebase_enum_proto_rawDescOnce.Do(func() {
		file_knowledgebase_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_knowledgebase_enum_proto_rawDescData)
	})
	return file_knowledgebase_enum_proto_rawDescData
}

var file_knowledgebase_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_knowledgebase_enum_proto_goTypes = []any{
	(KnowledgeBaseDataType)(0),   // 0: api.knowledgebase.KnowledgeBaseDataType
	(KnowledgeBaseFileStatus)(0), // 1: api.knowledgebase.KnowledgeBaseFileStatus
}
var file_knowledgebase_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_knowledgebase_enum_proto_init() }
func file_knowledgebase_enum_proto_init() {
	if File_knowledgebase_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_knowledgebase_enum_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_knowledgebase_enum_proto_goTypes,
		DependencyIndexes: file_knowledgebase_enum_proto_depIdxs,
		EnumInfos:         file_knowledgebase_enum_proto_enumTypes,
	}.Build()
	File_knowledgebase_enum_proto = out.File
	file_knowledgebase_enum_proto_rawDesc = nil
	file_knowledgebase_enum_proto_goTypes = nil
	file_knowledgebase_enum_proto_depIdxs = nil
}

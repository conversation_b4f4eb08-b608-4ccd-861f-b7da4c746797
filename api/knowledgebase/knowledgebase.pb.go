// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: knowledgebase/knowledgebase.proto

package knowledgebase

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KnowledgeBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 知识库名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 是否公开
	Public bool `protobuf:"varint,3,opt,name=public,proto3" json:"public,omitempty"`
	// 数据类型 1文档 2表格
	DataType KnowledgeBaseDataType `protobuf:"varint,10,opt,name=dataType,proto3,enum=api.knowledgebase.KnowledgeBaseDataType" json:"dataType,omitempty"`
	// 拥有者用户id
	UserID int64 `protobuf:"varint,4,opt,name=userID,proto3" json:"userID,omitempty"`
	// 拥有者用户名
	UserName string `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName,omitempty"`
	// 拥有者头像
	UserAvatar string `protobuf:"bytes,6,opt,name=userAvatar,proto3" json:"userAvatar,omitempty"`
	// 可管理用户
	Managers []*KnowledgeBaseInfo_UserInfo `protobuf:"bytes,9,rep,name=managers,proto3" json:"managers,omitempty"`
	// 可编辑用户ids
	EditableUsers []*KnowledgeBaseInfo_UserInfo `protobuf:"bytes,8,rep,name=editableUsers,proto3" json:"editableUsers,omitempty"`
	// 创建时间
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
}

func (x *KnowledgeBaseInfo) Reset() {
	*x = KnowledgeBaseInfo{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KnowledgeBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBaseInfo) ProtoMessage() {}

func (x *KnowledgeBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBaseInfo.ProtoReflect.Descriptor instead.
func (*KnowledgeBaseInfo) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{0}
}

func (x *KnowledgeBaseInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *KnowledgeBaseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *KnowledgeBaseInfo) GetPublic() bool {
	if x != nil {
		return x.Public
	}
	return false
}

func (x *KnowledgeBaseInfo) GetDataType() KnowledgeBaseDataType {
	if x != nil {
		return x.DataType
	}
	return KnowledgeBaseDataType_KnowledgeBaseDataTypeUnknown
}

func (x *KnowledgeBaseInfo) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *KnowledgeBaseInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *KnowledgeBaseInfo) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *KnowledgeBaseInfo) GetManagers() []*KnowledgeBaseInfo_UserInfo {
	if x != nil {
		return x.Managers
	}
	return nil
}

func (x *KnowledgeBaseInfo) GetEditableUsers() []*KnowledgeBaseInfo_UserInfo {
	if x != nil {
		return x.EditableUsers
	}
	return nil
}

func (x *KnowledgeBaseInfo) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// CreateKnowledgeBase
type CreateKnowledgeBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库名称
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 是否公开
	Public bool `protobuf:"varint,2,opt,name=public,proto3" json:"public,omitempty"`
	// 数据类型 1文档 2表格
	DataType KnowledgeBaseDataType `protobuf:"varint,10,opt,name=dataType,proto3,enum=api.knowledgebase.KnowledgeBaseDataType" json:"dataType,omitempty"`
	// 拥有者用户id
	UserID int64 `protobuf:"varint,4,opt,name=userID,proto3" json:"userID,omitempty"`
	// 管理者用户id
	ManagerUserIDs []int64 `protobuf:"varint,5,rep,packed,name=managerUserIDs,proto3" json:"managerUserIDs,omitempty"`
	// 可编辑用户ids
	EditableUserIDs []int64 `protobuf:"varint,3,rep,packed,name=editableUserIDs,proto3" json:"editableUserIDs,omitempty"`
}

func (x *CreateKnowledgeBaseRequest) Reset() {
	*x = CreateKnowledgeBaseRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateKnowledgeBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateKnowledgeBaseRequest) ProtoMessage() {}

func (x *CreateKnowledgeBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateKnowledgeBaseRequest.ProtoReflect.Descriptor instead.
func (*CreateKnowledgeBaseRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{1}
}

func (x *CreateKnowledgeBaseRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateKnowledgeBaseRequest) GetPublic() bool {
	if x != nil {
		return x.Public
	}
	return false
}

func (x *CreateKnowledgeBaseRequest) GetDataType() KnowledgeBaseDataType {
	if x != nil {
		return x.DataType
	}
	return KnowledgeBaseDataType_KnowledgeBaseDataTypeUnknown
}

func (x *CreateKnowledgeBaseRequest) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *CreateKnowledgeBaseRequest) GetManagerUserIDs() []int64 {
	if x != nil {
		return x.ManagerUserIDs
	}
	return nil
}

func (x *CreateKnowledgeBaseRequest) GetEditableUserIDs() []int64 {
	if x != nil {
		return x.EditableUserIDs
	}
	return nil
}

type CreateKnowledgeBaseReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *CreateKnowledgeBaseReply) Reset() {
	*x = CreateKnowledgeBaseReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateKnowledgeBaseReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateKnowledgeBaseReply) ProtoMessage() {}

func (x *CreateKnowledgeBaseReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateKnowledgeBaseReply.ProtoReflect.Descriptor instead.
func (*CreateKnowledgeBaseReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{2}
}

func (x *CreateKnowledgeBaseReply) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// UpdateKnowledgeBase
type UpdateKnowledgeBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 知识库名称
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// 是否公开
	Public bool `protobuf:"varint,3,opt,name=public,proto3" json:"public,omitempty"`
	// 可编辑用户ids
	EditableUserIDs []int64 `protobuf:"varint,4,rep,packed,name=editableUserIDs,proto3" json:"editableUserIDs,omitempty"`
	// 管理员选项
	OwnerOption *UpdateKnowledgeBaseRequest_OwnerOption `protobuf:"bytes,5,opt,name=ownerOption,proto3,oneof" json:"ownerOption,omitempty"`
}

func (x *UpdateKnowledgeBaseRequest) Reset() {
	*x = UpdateKnowledgeBaseRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateKnowledgeBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeBaseRequest) ProtoMessage() {}

func (x *UpdateKnowledgeBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeBaseRequest.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeBaseRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateKnowledgeBaseRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateKnowledgeBaseRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateKnowledgeBaseRequest) GetPublic() bool {
	if x != nil {
		return x.Public
	}
	return false
}

func (x *UpdateKnowledgeBaseRequest) GetEditableUserIDs() []int64 {
	if x != nil {
		return x.EditableUserIDs
	}
	return nil
}

func (x *UpdateKnowledgeBaseRequest) GetOwnerOption() *UpdateKnowledgeBaseRequest_OwnerOption {
	if x != nil {
		return x.OwnerOption
	}
	return nil
}

type UpdateKnowledgeBaseReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateKnowledgeBaseReply) Reset() {
	*x = UpdateKnowledgeBaseReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateKnowledgeBaseReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeBaseReply) ProtoMessage() {}

func (x *UpdateKnowledgeBaseReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeBaseReply.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeBaseReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{4}
}

// ChangeKnowledgeBaseOwner
type ChangeKnowledgeBaseOwnerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// 转移至用户id
	NewUserID int64 `protobuf:"varint,2,opt,name=newUserID,proto3" json:"newUserID,omitempty"`
}

func (x *ChangeKnowledgeBaseOwnerRequest) Reset() {
	*x = ChangeKnowledgeBaseOwnerRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeKnowledgeBaseOwnerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeKnowledgeBaseOwnerRequest) ProtoMessage() {}

func (x *ChangeKnowledgeBaseOwnerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeKnowledgeBaseOwnerRequest.ProtoReflect.Descriptor instead.
func (*ChangeKnowledgeBaseOwnerRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{5}
}

func (x *ChangeKnowledgeBaseOwnerRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ChangeKnowledgeBaseOwnerRequest) GetNewUserID() int64 {
	if x != nil {
		return x.NewUserID
	}
	return 0
}

type ChangeKnowledgeBaseOwnerReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ChangeKnowledgeBaseOwnerReply) Reset() {
	*x = ChangeKnowledgeBaseOwnerReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeKnowledgeBaseOwnerReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeKnowledgeBaseOwnerReply) ProtoMessage() {}

func (x *ChangeKnowledgeBaseOwnerReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeKnowledgeBaseOwnerReply.ProtoReflect.Descriptor instead.
func (*ChangeKnowledgeBaseOwnerReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{6}
}

// DeleteKnowledgeBase
type DeleteKnowledgeBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteKnowledgeBaseRequest) Reset() {
	*x = DeleteKnowledgeBaseRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteKnowledgeBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKnowledgeBaseRequest) ProtoMessage() {}

func (x *DeleteKnowledgeBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKnowledgeBaseRequest.ProtoReflect.Descriptor instead.
func (*DeleteKnowledgeBaseRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteKnowledgeBaseRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteKnowledgeBaseReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteKnowledgeBaseReply) Reset() {
	*x = DeleteKnowledgeBaseReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteKnowledgeBaseReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKnowledgeBaseReply) ProtoMessage() {}

func (x *DeleteKnowledgeBaseReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKnowledgeBaseReply.ProtoReflect.Descriptor instead.
func (*DeleteKnowledgeBaseReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{8}
}

// GetKnowledgeBases
type GetKnowledgeBasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
}

func (x *GetKnowledgeBasesRequest) Reset() {
	*x = GetKnowledgeBasesRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKnowledgeBasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKnowledgeBasesRequest) ProtoMessage() {}

func (x *GetKnowledgeBasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKnowledgeBasesRequest.ProtoReflect.Descriptor instead.
func (*GetKnowledgeBasesRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{9}
}

func (x *GetKnowledgeBasesRequest) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

type GetKnowledgeBasesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*KnowledgeBaseInfo `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *GetKnowledgeBasesReply) Reset() {
	*x = GetKnowledgeBasesReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetKnowledgeBasesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetKnowledgeBasesReply) ProtoMessage() {}

func (x *GetKnowledgeBasesReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetKnowledgeBasesReply.ProtoReflect.Descriptor instead.
func (*GetKnowledgeBasesReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{10}
}

func (x *GetKnowledgeBasesReply) GetRecords() []*KnowledgeBaseInfo {
	if x != nil {
		return x.Records
	}
	return nil
}

// GetAllKnowledgeBases
type GetAllKnowledgeBasesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetAllKnowledgeBasesRequest) Reset() {
	*x = GetAllKnowledgeBasesRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllKnowledgeBasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllKnowledgeBasesRequest) ProtoMessage() {}

func (x *GetAllKnowledgeBasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllKnowledgeBasesRequest.ProtoReflect.Descriptor instead.
func (*GetAllKnowledgeBasesRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{11}
}

type GetAllKnowledgeBasesReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Records []*KnowledgeBaseInfo `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *GetAllKnowledgeBasesReply) Reset() {
	*x = GetAllKnowledgeBasesReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllKnowledgeBasesReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllKnowledgeBasesReply) ProtoMessage() {}

func (x *GetAllKnowledgeBasesReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllKnowledgeBasesReply.ProtoReflect.Descriptor instead.
func (*GetAllKnowledgeBasesReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{12}
}

func (x *GetAllKnowledgeBasesReply) GetRecords() []*KnowledgeBaseInfo {
	if x != nil {
		return x.Records
	}
	return nil
}

// PageKnowledgeBase
type PageKnowledgeBaseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int64 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	// 知识库名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 是否全部 false:我可编辑的
	All bool `protobuf:"varint,4,opt,name=all,proto3" json:"all,omitempty"`
	// 数据类型 0全部 1文档 2表格
	DataType int32 `protobuf:"varint,10,opt,name=dataType,proto3" json:"dataType,omitempty"`
}

func (x *PageKnowledgeBaseRequest) Reset() {
	*x = PageKnowledgeBaseRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageKnowledgeBaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageKnowledgeBaseRequest) ProtoMessage() {}

func (x *PageKnowledgeBaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageKnowledgeBaseRequest.ProtoReflect.Descriptor instead.
func (*PageKnowledgeBaseRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{13}
}

func (x *PageKnowledgeBaseRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PageKnowledgeBaseRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageKnowledgeBaseRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PageKnowledgeBaseRequest) GetAll() bool {
	if x != nil {
		return x.All
	}
	return false
}

func (x *PageKnowledgeBaseRequest) GetDataType() int32 {
	if x != nil {
		return x.DataType
	}
	return 0
}

type PageKnowledgeBaseReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total   int64                `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Records []*KnowledgeBaseInfo `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *PageKnowledgeBaseReply) Reset() {
	*x = PageKnowledgeBaseReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageKnowledgeBaseReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageKnowledgeBaseReply) ProtoMessage() {}

func (x *PageKnowledgeBaseReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageKnowledgeBaseReply.ProtoReflect.Descriptor instead.
func (*PageKnowledgeBaseReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{14}
}

func (x *PageKnowledgeBaseReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PageKnowledgeBaseReply) GetRecords() []*KnowledgeBaseInfo {
	if x != nil {
		return x.Records
	}
	return nil
}

// AddKnowledgeBaseFileFromSearch
type AddKnowledgeBaseFileFromSearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Id         int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Query      string                 `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	SearchType int64                  `protobuf:"varint,3,opt,name=searchType,proto3" json:"searchType,omitempty"`
	FileType   string                 `protobuf:"bytes,4,opt,name=fileType,proto3" json:"fileType,omitempty"`
	OwnerIDs   []int64                `protobuf:"varint,5,rep,packed,name=ownerIDs,proto3" json:"ownerIDs,omitempty"`
	StartTime  *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime    *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=endTime,proto3" json:"endTime,omitempty"`
	ClassPath  string                 `protobuf:"bytes,8,opt,name=classPath,proto3" json:"classPath,omitempty"`
	Path       string                 `protobuf:"bytes,9,opt,name=path,proto3" json:"path,omitempty"`
}

func (x *AddKnowledgeBaseFileFromSearchRequest) Reset() {
	*x = AddKnowledgeBaseFileFromSearchRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddKnowledgeBaseFileFromSearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddKnowledgeBaseFileFromSearchRequest) ProtoMessage() {}

func (x *AddKnowledgeBaseFileFromSearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddKnowledgeBaseFileFromSearchRequest.ProtoReflect.Descriptor instead.
func (*AddKnowledgeBaseFileFromSearchRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{15}
}

func (x *AddKnowledgeBaseFileFromSearchRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddKnowledgeBaseFileFromSearchRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *AddKnowledgeBaseFileFromSearchRequest) GetSearchType() int64 {
	if x != nil {
		return x.SearchType
	}
	return 0
}

func (x *AddKnowledgeBaseFileFromSearchRequest) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *AddKnowledgeBaseFileFromSearchRequest) GetOwnerIDs() []int64 {
	if x != nil {
		return x.OwnerIDs
	}
	return nil
}

func (x *AddKnowledgeBaseFileFromSearchRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *AddKnowledgeBaseFileFromSearchRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *AddKnowledgeBaseFileFromSearchRequest) GetClassPath() string {
	if x != nil {
		return x.ClassPath
	}
	return ""
}

func (x *AddKnowledgeBaseFileFromSearchRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

type AddKnowledgeBaseFileFromSearchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddKnowledgeBaseFileFromSearchReply) Reset() {
	*x = AddKnowledgeBaseFileFromSearchReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddKnowledgeBaseFileFromSearchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddKnowledgeBaseFileFromSearchReply) ProtoMessage() {}

func (x *AddKnowledgeBaseFileFromSearchReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddKnowledgeBaseFileFromSearchReply.ProtoReflect.Descriptor instead.
func (*AddKnowledgeBaseFileFromSearchReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{16}
}

// AddKnowledgeBaseFile
type AddKnowledgeBaseFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 文件ids
	FileRelationIDs []int64 `protobuf:"varint,2,rep,packed,name=fileRelationIDs,proto3" json:"fileRelationIDs,omitempty"`
	// 前端忽略这个参数，仅rpc使用
	SkipQuickCreate bool `protobuf:"varint,3,opt,name=skipQuickCreate,proto3" json:"skipQuickCreate,omitempty"`
}

func (x *AddKnowledgeBaseFileRequest) Reset() {
	*x = AddKnowledgeBaseFileRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddKnowledgeBaseFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddKnowledgeBaseFileRequest) ProtoMessage() {}

func (x *AddKnowledgeBaseFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddKnowledgeBaseFileRequest.ProtoReflect.Descriptor instead.
func (*AddKnowledgeBaseFileRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{17}
}

func (x *AddKnowledgeBaseFileRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddKnowledgeBaseFileRequest) GetFileRelationIDs() []int64 {
	if x != nil {
		return x.FileRelationIDs
	}
	return nil
}

func (x *AddKnowledgeBaseFileRequest) GetSkipQuickCreate() bool {
	if x != nil {
		return x.SkipQuickCreate
	}
	return false
}

type AddKnowledgeBaseFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AddKnowledgeBaseFileReply) Reset() {
	*x = AddKnowledgeBaseFileReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddKnowledgeBaseFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddKnowledgeBaseFileReply) ProtoMessage() {}

func (x *AddKnowledgeBaseFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddKnowledgeBaseFileReply.ProtoReflect.Descriptor instead.
func (*AddKnowledgeBaseFileReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{18}
}

// DeleteKnowledgeBaseFile
type DeleteKnowledgeBaseFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 文件ids
	FileRelationIDs []int64 `protobuf:"varint,2,rep,packed,name=fileRelationIDs,proto3" json:"fileRelationIDs,omitempty"`
}

func (x *DeleteKnowledgeBaseFileRequest) Reset() {
	*x = DeleteKnowledgeBaseFileRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteKnowledgeBaseFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKnowledgeBaseFileRequest) ProtoMessage() {}

func (x *DeleteKnowledgeBaseFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKnowledgeBaseFileRequest.ProtoReflect.Descriptor instead.
func (*DeleteKnowledgeBaseFileRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteKnowledgeBaseFileRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteKnowledgeBaseFileRequest) GetFileRelationIDs() []int64 {
	if x != nil {
		return x.FileRelationIDs
	}
	return nil
}

type DeleteKnowledgeBaseFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteKnowledgeBaseFileReply) Reset() {
	*x = DeleteKnowledgeBaseFileReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteKnowledgeBaseFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteKnowledgeBaseFileReply) ProtoMessage() {}

func (x *DeleteKnowledgeBaseFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteKnowledgeBaseFileReply.ProtoReflect.Descriptor instead.
func (*DeleteKnowledgeBaseFileReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{20}
}

// UpdateKnowledgeBaseFileMetadata
type UpdateKnowledgeBaseFileMetadataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 文件id
	FileRelationID int64 `protobuf:"varint,2,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	// 元数据
	Metadata string `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *UpdateKnowledgeBaseFileMetadataRequest) Reset() {
	*x = UpdateKnowledgeBaseFileMetadataRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateKnowledgeBaseFileMetadataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeBaseFileMetadataRequest) ProtoMessage() {}

func (x *UpdateKnowledgeBaseFileMetadataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeBaseFileMetadataRequest.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeBaseFileMetadataRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateKnowledgeBaseFileMetadataRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateKnowledgeBaseFileMetadataRequest) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *UpdateKnowledgeBaseFileMetadataRequest) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

type UpdateKnowledgeBaseFileMetadataReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateKnowledgeBaseFileMetadataReply) Reset() {
	*x = UpdateKnowledgeBaseFileMetadataReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateKnowledgeBaseFileMetadataReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeBaseFileMetadataReply) ProtoMessage() {}

func (x *UpdateKnowledgeBaseFileMetadataReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeBaseFileMetadataReply.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeBaseFileMetadataReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{22}
}

// InnerUpdateKnowledgeBaseFileRequest
type InnerUpdateKnowledgeBaseFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 知识库id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 文件id
	FileRelationID int64 `protobuf:"varint,2,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	// 元数据
	Metadata *string `protobuf:"bytes,3,opt,name=metadata,proto3,oneof" json:"metadata,omitempty"`
	// 文件状态 0等待 1处理中 2成功 3失败
	Status *KnowledgeBaseFileStatus `protobuf:"varint,4,opt,name=status,proto3,enum=api.knowledgebase.KnowledgeBaseFileStatus,oneof" json:"status,omitempty"`
	// 失败原因
	FailedReason *string `protobuf:"bytes,5,opt,name=failedReason,proto3,oneof" json:"failedReason,omitempty"`
}

func (x *InnerUpdateKnowledgeBaseFileRequest) Reset() {
	*x = InnerUpdateKnowledgeBaseFileRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InnerUpdateKnowledgeBaseFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InnerUpdateKnowledgeBaseFileRequest) ProtoMessage() {}

func (x *InnerUpdateKnowledgeBaseFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InnerUpdateKnowledgeBaseFileRequest.ProtoReflect.Descriptor instead.
func (*InnerUpdateKnowledgeBaseFileRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{23}
}

func (x *InnerUpdateKnowledgeBaseFileRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *InnerUpdateKnowledgeBaseFileRequest) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *InnerUpdateKnowledgeBaseFileRequest) GetMetadata() string {
	if x != nil && x.Metadata != nil {
		return *x.Metadata
	}
	return ""
}

func (x *InnerUpdateKnowledgeBaseFileRequest) GetStatus() KnowledgeBaseFileStatus {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return KnowledgeBaseFileStatus_KnowledgeBaseFileStatusWaiting
}

func (x *InnerUpdateKnowledgeBaseFileRequest) GetFailedReason() string {
	if x != nil && x.FailedReason != nil {
		return *x.FailedReason
	}
	return ""
}

type InnerUpdateKnowledgeBaseFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *InnerUpdateKnowledgeBaseFileReply) Reset() {
	*x = InnerUpdateKnowledgeBaseFileReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InnerUpdateKnowledgeBaseFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InnerUpdateKnowledgeBaseFileReply) ProtoMessage() {}

func (x *InnerUpdateKnowledgeBaseFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InnerUpdateKnowledgeBaseFileReply.ProtoReflect.Descriptor instead.
func (*InnerUpdateKnowledgeBaseFileReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{24}
}

// PageKnowledgeBaseFile
type PageKnowledgeBaseFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int64 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	// 知识库id
	Id int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	// 文件名称
	FileName string `protobuf:"bytes,4,opt,name=fileName,proto3" json:"fileName,omitempty"`
}

func (x *PageKnowledgeBaseFileRequest) Reset() {
	*x = PageKnowledgeBaseFileRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageKnowledgeBaseFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageKnowledgeBaseFileRequest) ProtoMessage() {}

func (x *PageKnowledgeBaseFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageKnowledgeBaseFileRequest.ProtoReflect.Descriptor instead.
func (*PageKnowledgeBaseFileRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{25}
}

func (x *PageKnowledgeBaseFileRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PageKnowledgeBaseFileRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageKnowledgeBaseFileRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PageKnowledgeBaseFileRequest) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type PageKnowledgeBaseFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total   int64                             `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Records []*PageKnowledgeBaseFileReplyItem `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *PageKnowledgeBaseFileReply) Reset() {
	*x = PageKnowledgeBaseFileReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageKnowledgeBaseFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageKnowledgeBaseFileReply) ProtoMessage() {}

func (x *PageKnowledgeBaseFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageKnowledgeBaseFileReply.ProtoReflect.Descriptor instead.
func (*PageKnowledgeBaseFileReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{26}
}

func (x *PageKnowledgeBaseFileReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PageKnowledgeBaseFileReply) GetRecords() []*PageKnowledgeBaseFileReplyItem {
	if x != nil {
		return x.Records
	}
	return nil
}

type PageKnowledgeBaseFileReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 数据类型 1文档 2表格
	DataType KnowledgeBaseDataType `protobuf:"varint,11,opt,name=dataType,proto3,enum=api.knowledgebase.KnowledgeBaseDataType" json:"dataType,omitempty"`
	// 元数据 excel_schema
	Metadata string `protobuf:"bytes,12,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// 文件id
	FileRelationID int64 `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	// 文件名称
	FileName string `protobuf:"bytes,2,opt,name=fileName,proto3" json:"fileName,omitempty"`
	// 文件路径
	FilePath string `protobuf:"bytes,3,opt,name=filePath,proto3" json:"filePath,omitempty"`
	// 文件类型
	FileType string `protobuf:"bytes,4,opt,name=fileType,proto3" json:"fileType,omitempty"`
	// 文件大小
	FileSize int64 `protobuf:"varint,5,opt,name=fileSize,proto3" json:"fileSize,omitempty"`
	// preEntityTag
	PreEntityTag string `protobuf:"bytes,6,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	// entityTag
	EntityTag string `protobuf:"bytes,7,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	// 文件状态 0等待 1处理中 2成功 3失败
	FileStatus KnowledgeBaseFileStatus `protobuf:"varint,10,opt,name=fileStatus,proto3,enum=api.knowledgebase.KnowledgeBaseFileStatus" json:"fileStatus,omitempty"`
	// 失败原因
	FailedReason string `protobuf:"bytes,13,opt,name=failedReason,proto3" json:"failedReason,omitempty"`
	// 文件分类
	FileCategories []string `protobuf:"bytes,8,rep,name=fileCategories,proto3" json:"fileCategories,omitempty"`
	// 创建时间
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
}

func (x *PageKnowledgeBaseFileReplyItem) Reset() {
	*x = PageKnowledgeBaseFileReplyItem{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageKnowledgeBaseFileReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageKnowledgeBaseFileReplyItem) ProtoMessage() {}

func (x *PageKnowledgeBaseFileReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageKnowledgeBaseFileReplyItem.ProtoReflect.Descriptor instead.
func (*PageKnowledgeBaseFileReplyItem) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{27}
}

func (x *PageKnowledgeBaseFileReplyItem) GetDataType() KnowledgeBaseDataType {
	if x != nil {
		return x.DataType
	}
	return KnowledgeBaseDataType_KnowledgeBaseDataTypeUnknown
}

func (x *PageKnowledgeBaseFileReplyItem) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *PageKnowledgeBaseFileReplyItem) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *PageKnowledgeBaseFileReplyItem) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *PageKnowledgeBaseFileReplyItem) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *PageKnowledgeBaseFileReplyItem) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *PageKnowledgeBaseFileReplyItem) GetFileSize() int64 {
	if x != nil {
		return x.FileSize
	}
	return 0
}

func (x *PageKnowledgeBaseFileReplyItem) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *PageKnowledgeBaseFileReplyItem) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *PageKnowledgeBaseFileReplyItem) GetFileStatus() KnowledgeBaseFileStatus {
	if x != nil {
		return x.FileStatus
	}
	return KnowledgeBaseFileStatus_KnowledgeBaseFileStatusWaiting
}

func (x *PageKnowledgeBaseFileReplyItem) GetFailedReason() string {
	if x != nil {
		return x.FailedReason
	}
	return ""
}

func (x *PageKnowledgeBaseFileReplyItem) GetFileCategories() []string {
	if x != nil {
		return x.FileCategories
	}
	return nil
}

func (x *PageKnowledgeBaseFileReplyItem) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

// ExistKnowledgeBaseFile
type ExistKnowledgeBaseFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	FileRelationID int64 `protobuf:"varint,2,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
}

func (x *ExistKnowledgeBaseFileRequest) Reset() {
	*x = ExistKnowledgeBaseFileRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExistKnowledgeBaseFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExistKnowledgeBaseFileRequest) ProtoMessage() {}

func (x *ExistKnowledgeBaseFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExistKnowledgeBaseFileRequest.ProtoReflect.Descriptor instead.
func (*ExistKnowledgeBaseFileRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{28}
}

func (x *ExistKnowledgeBaseFileRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ExistKnowledgeBaseFileRequest) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

type ExistKnowledgeBaseFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exist bool `protobuf:"varint,1,opt,name=exist,proto3" json:"exist,omitempty"`
}

func (x *ExistKnowledgeBaseFileReply) Reset() {
	*x = ExistKnowledgeBaseFileReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExistKnowledgeBaseFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExistKnowledgeBaseFileReply) ProtoMessage() {}

func (x *ExistKnowledgeBaseFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExistKnowledgeBaseFileReply.ProtoReflect.Descriptor instead.
func (*ExistKnowledgeBaseFileReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{29}
}

func (x *ExistKnowledgeBaseFileReply) GetExist() bool {
	if x != nil {
		return x.Exist
	}
	return false
}

// GetFileKnowledgeBaseIDs
type GetFileKnowledgeBaseIDsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文件id
	FileRelationID int64 `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
}

func (x *GetFileKnowledgeBaseIDsRequest) Reset() {
	*x = GetFileKnowledgeBaseIDsRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileKnowledgeBaseIDsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileKnowledgeBaseIDsRequest) ProtoMessage() {}

func (x *GetFileKnowledgeBaseIDsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileKnowledgeBaseIDsRequest.ProtoReflect.Descriptor instead.
func (*GetFileKnowledgeBaseIDsRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{30}
}

func (x *GetFileKnowledgeBaseIDsRequest) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

type GetFileKnowledgeBaseIDsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 文档知识库ids
	DocKnowledgeBaseIDs []int64 `protobuf:"varint,1,rep,packed,name=docKnowledgeBaseIDs,proto3" json:"docKnowledgeBaseIDs,omitempty"`
	// 表格知识库ids
	SheetKnowledgeBaseIDs []int64 `protobuf:"varint,2,rep,packed,name=sheetKnowledgeBaseIDs,proto3" json:"sheetKnowledgeBaseIDs,omitempty"`
}

func (x *GetFileKnowledgeBaseIDsReply) Reset() {
	*x = GetFileKnowledgeBaseIDsReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFileKnowledgeBaseIDsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFileKnowledgeBaseIDsReply) ProtoMessage() {}

func (x *GetFileKnowledgeBaseIDsReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFileKnowledgeBaseIDsReply.ProtoReflect.Descriptor instead.
func (*GetFileKnowledgeBaseIDsReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{31}
}

func (x *GetFileKnowledgeBaseIDsReply) GetDocKnowledgeBaseIDs() []int64 {
	if x != nil {
		return x.DocKnowledgeBaseIDs
	}
	return nil
}

func (x *GetFileKnowledgeBaseIDsReply) GetSheetKnowledgeBaseIDs() []int64 {
	if x != nil {
		return x.SheetKnowledgeBaseIDs
	}
	return nil
}

type CountKnowledgeBaseFileRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *CountKnowledgeBaseFileRequest) Reset() {
	*x = CountKnowledgeBaseFileRequest{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountKnowledgeBaseFileRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountKnowledgeBaseFileRequest) ProtoMessage() {}

func (x *CountKnowledgeBaseFileRequest) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountKnowledgeBaseFileRequest.ProtoReflect.Descriptor instead.
func (*CountKnowledgeBaseFileRequest) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{32}
}

func (x *CountKnowledgeBaseFileRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *CountKnowledgeBaseFileRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type CountKnowledgeBaseFileReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *CountKnowledgeBaseFileReply) Reset() {
	*x = CountKnowledgeBaseFileReply{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountKnowledgeBaseFileReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountKnowledgeBaseFileReply) ProtoMessage() {}

func (x *CountKnowledgeBaseFileReply) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountKnowledgeBaseFileReply.ProtoReflect.Descriptor instead.
func (*CountKnowledgeBaseFileReply) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{33}
}

func (x *CountKnowledgeBaseFileReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type FileAnalysisImmediatelyMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID int64  `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	PreEntityTag   string `protobuf:"bytes,2,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	EntityTag      string `protobuf:"bytes,3,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	Index          int64  `protobuf:"varint,4,opt,name=index,proto3" json:"index,omitempty"`
	ChunkIndex     int64  `protobuf:"varint,5,opt,name=chunkIndex,proto3" json:"chunkIndex,omitempty"`
	ChunkSize      int64  `protobuf:"varint,6,opt,name=chunkSize,proto3" json:"chunkSize,omitempty"`
}

func (x *FileAnalysisImmediatelyMessage) Reset() {
	*x = FileAnalysisImmediatelyMessage{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileAnalysisImmediatelyMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileAnalysisImmediatelyMessage) ProtoMessage() {}

func (x *FileAnalysisImmediatelyMessage) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileAnalysisImmediatelyMessage.ProtoReflect.Descriptor instead.
func (*FileAnalysisImmediatelyMessage) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{34}
}

func (x *FileAnalysisImmediatelyMessage) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *FileAnalysisImmediatelyMessage) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *FileAnalysisImmediatelyMessage) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *FileAnalysisImmediatelyMessage) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *FileAnalysisImmediatelyMessage) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *FileAnalysisImmediatelyMessage) GetChunkSize() int64 {
	if x != nil {
		return x.ChunkSize
	}
	return 0
}

type KnowledgeBaseInfo_UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 拥有者用户id
	UserID int64 `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"`
	// 拥有者用户名
	UserName string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName,omitempty"`
	// 拥有者头像
	UserAvatar string `protobuf:"bytes,3,opt,name=userAvatar,proto3" json:"userAvatar,omitempty"`
}

func (x *KnowledgeBaseInfo_UserInfo) Reset() {
	*x = KnowledgeBaseInfo_UserInfo{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KnowledgeBaseInfo_UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KnowledgeBaseInfo_UserInfo) ProtoMessage() {}

func (x *KnowledgeBaseInfo_UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KnowledgeBaseInfo_UserInfo.ProtoReflect.Descriptor instead.
func (*KnowledgeBaseInfo_UserInfo) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{0, 0}
}

func (x *KnowledgeBaseInfo_UserInfo) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *KnowledgeBaseInfo_UserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *KnowledgeBaseInfo_UserInfo) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

type UpdateKnowledgeBaseRequest_OwnerOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NewUserID      int64   `protobuf:"varint,1,opt,name=newUserID,proto3" json:"newUserID,omitempty"`
	ManagerUserIDs []int64 `protobuf:"varint,2,rep,packed,name=managerUserIDs,proto3" json:"managerUserIDs,omitempty"`
}

func (x *UpdateKnowledgeBaseRequest_OwnerOption) Reset() {
	*x = UpdateKnowledgeBaseRequest_OwnerOption{}
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateKnowledgeBaseRequest_OwnerOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateKnowledgeBaseRequest_OwnerOption) ProtoMessage() {}

func (x *UpdateKnowledgeBaseRequest_OwnerOption) ProtoReflect() protoreflect.Message {
	mi := &file_knowledgebase_knowledgebase_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateKnowledgeBaseRequest_OwnerOption.ProtoReflect.Descriptor instead.
func (*UpdateKnowledgeBaseRequest_OwnerOption) Descriptor() ([]byte, []int) {
	return file_knowledgebase_knowledgebase_proto_rawDescGZIP(), []int{3, 0}
}

func (x *UpdateKnowledgeBaseRequest_OwnerOption) GetNewUserID() int64 {
	if x != nil {
		return x.NewUserID
	}
	return 0
}

func (x *UpdateKnowledgeBaseRequest_OwnerOption) GetManagerUserIDs() []int64 {
	if x != nil {
		return x.ManagerUserIDs
	}
	return nil
}

var File_knowledgebase_knowledgebase_proto protoreflect.FileDescriptor

var file_knowledgebase_knowledgebase_proto_rawDesc = []byte{
	0x0a, 0x21, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2f,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x62, 0x61, 0x73, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa3, 0x04, 0x0a, 0x11, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x44, 0x0a, 0x08, 0x64,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65,
	0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x49, 0x0a, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x73,
	0x12, 0x53, 0x0a, 0x0d, 0x65, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x65, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x1a,
	0x5e, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22,
	0xa6, 0x02, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xe0, 0x41,
	0x02, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x53, 0x0a,
	0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0d, 0xe0, 0x41, 0x02, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x18, 0x01, 0x18, 0x02, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x22, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x73, 0x12, 0x28,
	0x0a, 0x0f, 0x65, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x65, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x73, 0x22, 0x2a, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x22, 0xe8, 0x02, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x20, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x0c, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0x12, 0x28,
	0x0a, 0x0f, 0x65, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0f, 0x65, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x73, 0x12, 0x60, 0x0a, 0x0b, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4f, 0x77, 0x6e,
	0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0b, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x1a, 0x53, 0x0a, 0x0b, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x65, 0x77,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6e, 0x65,
	0x77, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x26, 0x0a, 0x0e, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x0e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x73, 0x42,
	0x0e, 0x0a, 0x0c, 0x5f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x1a, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x5d, 0x0a, 0x1f, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73,
	0x12, 0x28, 0x0a, 0x09, 0x6e, 0x65, 0x77, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x09, 0x6e, 0x65, 0x77, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x22, 0x1f, 0x0a, 0x1d, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x38, 0x0a, 0x1a, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1a, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x2c, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x03, 0x69, 0x64, 0x73, 0x22,
	0x58, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3e, 0x0a, 0x07, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0x1d, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x5b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x3e, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xac, 0x01, 0x0a, 0x18, 0x50, 0x61, 0x67, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x24, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0c, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x6c, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x03, 0x61, 0x6c, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x22, 0x6e, 0x0a, 0x16, 0x50, 0x61, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3e, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x72, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x73, 0x22, 0xd3, 0x02, 0x0a, 0x25, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f,
	0x6d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75,
	0x65, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x03, 0x52, 0x08,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x73, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73,
	0x73, 0x50, 0x61, 0x74, 0x68, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61,
	0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x22, 0x25, 0x0a, 0x23, 0x41, 0x64,
	0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x9a, 0x01, 0x0a, 0x1b, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x1a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a,
	0x0f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x42, 0x0b, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x0f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x44, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x73, 0x6b, 0x69, 0x70, 0x51, 0x75, 0x69, 0x63,
	0x6b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x73,
	0x6b, 0x69, 0x70, 0x51, 0x75, 0x69, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x22, 0x1b,
	0x0a, 0x19, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x73, 0x0a, 0x1e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x35, 0x0a, 0x0f, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x03, 0x42, 0x0b, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x0f, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x73,
	0x22, 0x1e, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x94, 0x01, 0x0a, 0x26, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22,
	0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42,
	0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x66, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0x26, 0x0a, 0x24, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0xc4, 0x02, 0x0a, 0x23, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x32, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02,
	0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x1f, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x88, 0x01, 0x01, 0x12, 0x5a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x11, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x0b, 0x82, 0x01, 0x08, 0x18,
	0x00, 0x18, 0x01, 0x18, 0x02, 0x18, 0x03, 0x48, 0x01, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x88, 0x01, 0x01, 0x12, 0x27, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0c, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a,
	0x09, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x09, 0x0a, 0x07, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x23, 0x0a, 0x21, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0xa6, 0x01, 0x0a, 0x1c,
	0x50, 0x61, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x07,
	0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e,
	0x75, 0x6d, 0x12, 0x28, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x42, 0x0c, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x18, 0x64,
	0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04,
	0x22, 0x02, 0x20, 0x00, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x7f, 0x0a, 0x1a, 0x50, 0x61, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x4b, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x73, 0x22, 0xae, 0x04, 0x0a, 0x1e, 0x50, 0x61, 0x67, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x44, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69,
	0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54,
	0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x54, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x54, 0x61, 0x67, 0x12, 0x4a, 0x0a, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x66, 0x69, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x22, 0x0a, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x38, 0x0a, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x57, 0x0a, 0x1d, 0x45, 0x78, 0x69, 0x73, 0x74, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22,
	0x33, 0x0a, 0x1b, 0x45, 0x78, 0x69, 0x73, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x78, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x05, 0x65,
	0x78, 0x69, 0x73, 0x74, 0x22, 0x54, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x44, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0x86, 0x01, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x44, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x30, 0x0a, 0x13, 0x64,
	0x6f, 0x63, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49,
	0x44, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52, 0x13, 0x64, 0x6f, 0x63, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x44, 0x73, 0x12, 0x34, 0x0a,
	0x15, 0x73, 0x68, 0x65, 0x65, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x15, 0x73, 0x68,
	0x65, 0x65, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x44, 0x73, 0x22, 0x8f, 0x01, 0x0a, 0x1d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x33, 0x0a, 0x1b, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xde, 0x01, 0x0a, 0x1e, 0x46,
	0x69, 0x6c, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x49, 0x6d, 0x6d, 0x65, 0x64,
	0x69, 0x61, 0x74, 0x65, 0x6c, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a,
	0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1e, 0x0a,
	0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1c, 0x0a,
	0x09, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x53, 0x69, 0x7a, 0x65, 0x32, 0x89, 0x13, 0x0a, 0x0d,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x93, 0x01,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a,
	0x01, 0x2a, 0x22, 0x15, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xb4, 0x01, 0x0a, 0x18, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x4f, 0x77,
	0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x32, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2c, 0x3a, 0x01, 0x2a, 0x22, 0x27, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x4f, 0x77, 0x6e, 0x65, 0x72,
	0x12, 0x93, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22,
	0x15, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x2f,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x8a, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2b, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a, 0x22,
	0x12, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x2f,
	0x67, 0x65, 0x74, 0x12, 0x76, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x88, 0x01, 0x0a, 0x11,
	0x50, 0x61, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x12, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x15, 0x12, 0x13, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x12, 0xbf, 0x01, 0x0a, 0x1e, 0x41, 0x64, 0x64, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x46,
	0x72, 0x6f, 0x6d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x38, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x41, 0x64,
	0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72, 0x6f, 0x6d,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x25, 0x3a, 0x01, 0x2a, 0x22, 0x20, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x61, 0x64, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x46, 0x72,
	0x6f, 0x6d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x97, 0x01, 0x0a, 0x14, 0x41, 0x64, 0x64,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x21, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1b, 0x3a, 0x01, 0x2a, 0x22, 0x16, 0x2f, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x61, 0x64, 0x64, 0x46, 0x69,
	0x6c, 0x65, 0x12, 0xa3, 0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x31,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0xc3, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x3a, 0x01, 0x2a, 0x22, 0x21, 0x2f, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x2f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x8e,
	0x01, 0x0a, 0x1c, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x6e, 0x65,
	0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12,
	0x98, 0x01, 0x0a, 0x15, 0x50, 0x61, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x19, 0x12, 0x17, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73,
	0x65, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x7c, 0x0a, 0x16, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x46, 0x69, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x69, 0x73, 0x74, 0x4b, 0x6e,
	0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x45, 0x78, 0x69, 0x73, 0x74,
	0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x7f, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x44, 0x73, 0x12, 0x31, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x44, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49,
	0x44, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x7c, 0x0a, 0x16, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46,
	0x69, 0x6c, 0x65, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x4b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x42, 0x5f, 0x0a, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x50, 0x01, 0x5a, 0x48,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2f, 0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65, 0x61,
	0x6d, 0x2f, 0x61, 0x69, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x3b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_knowledgebase_knowledgebase_proto_rawDescOnce sync.Once
	file_knowledgebase_knowledgebase_proto_rawDescData = file_knowledgebase_knowledgebase_proto_rawDesc
)

func file_knowledgebase_knowledgebase_proto_rawDescGZIP() []byte {
	file_knowledgebase_knowledgebase_proto_rawDescOnce.Do(func() {
		file_knowledgebase_knowledgebase_proto_rawDescData = protoimpl.X.CompressGZIP(file_knowledgebase_knowledgebase_proto_rawDescData)
	})
	return file_knowledgebase_knowledgebase_proto_rawDescData
}

var file_knowledgebase_knowledgebase_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_knowledgebase_knowledgebase_proto_goTypes = []any{
	(*KnowledgeBaseInfo)(nil),                      // 0: api.knowledgebase.KnowledgeBaseInfo
	(*CreateKnowledgeBaseRequest)(nil),             // 1: api.knowledgebase.CreateKnowledgeBaseRequest
	(*CreateKnowledgeBaseReply)(nil),               // 2: api.knowledgebase.CreateKnowledgeBaseReply
	(*UpdateKnowledgeBaseRequest)(nil),             // 3: api.knowledgebase.UpdateKnowledgeBaseRequest
	(*UpdateKnowledgeBaseReply)(nil),               // 4: api.knowledgebase.UpdateKnowledgeBaseReply
	(*ChangeKnowledgeBaseOwnerRequest)(nil),        // 5: api.knowledgebase.ChangeKnowledgeBaseOwnerRequest
	(*ChangeKnowledgeBaseOwnerReply)(nil),          // 6: api.knowledgebase.ChangeKnowledgeBaseOwnerReply
	(*DeleteKnowledgeBaseRequest)(nil),             // 7: api.knowledgebase.DeleteKnowledgeBaseRequest
	(*DeleteKnowledgeBaseReply)(nil),               // 8: api.knowledgebase.DeleteKnowledgeBaseReply
	(*GetKnowledgeBasesRequest)(nil),               // 9: api.knowledgebase.GetKnowledgeBasesRequest
	(*GetKnowledgeBasesReply)(nil),                 // 10: api.knowledgebase.GetKnowledgeBasesReply
	(*GetAllKnowledgeBasesRequest)(nil),            // 11: api.knowledgebase.GetAllKnowledgeBasesRequest
	(*GetAllKnowledgeBasesReply)(nil),              // 12: api.knowledgebase.GetAllKnowledgeBasesReply
	(*PageKnowledgeBaseRequest)(nil),               // 13: api.knowledgebase.PageKnowledgeBaseRequest
	(*PageKnowledgeBaseReply)(nil),                 // 14: api.knowledgebase.PageKnowledgeBaseReply
	(*AddKnowledgeBaseFileFromSearchRequest)(nil),  // 15: api.knowledgebase.AddKnowledgeBaseFileFromSearchRequest
	(*AddKnowledgeBaseFileFromSearchReply)(nil),    // 16: api.knowledgebase.AddKnowledgeBaseFileFromSearchReply
	(*AddKnowledgeBaseFileRequest)(nil),            // 17: api.knowledgebase.AddKnowledgeBaseFileRequest
	(*AddKnowledgeBaseFileReply)(nil),              // 18: api.knowledgebase.AddKnowledgeBaseFileReply
	(*DeleteKnowledgeBaseFileRequest)(nil),         // 19: api.knowledgebase.DeleteKnowledgeBaseFileRequest
	(*DeleteKnowledgeBaseFileReply)(nil),           // 20: api.knowledgebase.DeleteKnowledgeBaseFileReply
	(*UpdateKnowledgeBaseFileMetadataRequest)(nil), // 21: api.knowledgebase.UpdateKnowledgeBaseFileMetadataRequest
	(*UpdateKnowledgeBaseFileMetadataReply)(nil),   // 22: api.knowledgebase.UpdateKnowledgeBaseFileMetadataReply
	(*InnerUpdateKnowledgeBaseFileRequest)(nil),    // 23: api.knowledgebase.InnerUpdateKnowledgeBaseFileRequest
	(*InnerUpdateKnowledgeBaseFileReply)(nil),      // 24: api.knowledgebase.InnerUpdateKnowledgeBaseFileReply
	(*PageKnowledgeBaseFileRequest)(nil),           // 25: api.knowledgebase.PageKnowledgeBaseFileRequest
	(*PageKnowledgeBaseFileReply)(nil),             // 26: api.knowledgebase.PageKnowledgeBaseFileReply
	(*PageKnowledgeBaseFileReplyItem)(nil),         // 27: api.knowledgebase.PageKnowledgeBaseFileReplyItem
	(*ExistKnowledgeBaseFileRequest)(nil),          // 28: api.knowledgebase.ExistKnowledgeBaseFileRequest
	(*ExistKnowledgeBaseFileReply)(nil),            // 29: api.knowledgebase.ExistKnowledgeBaseFileReply
	(*GetFileKnowledgeBaseIDsRequest)(nil),         // 30: api.knowledgebase.GetFileKnowledgeBaseIDsRequest
	(*GetFileKnowledgeBaseIDsReply)(nil),           // 31: api.knowledgebase.GetFileKnowledgeBaseIDsReply
	(*CountKnowledgeBaseFileRequest)(nil),          // 32: api.knowledgebase.CountKnowledgeBaseFileRequest
	(*CountKnowledgeBaseFileReply)(nil),            // 33: api.knowledgebase.CountKnowledgeBaseFileReply
	(*FileAnalysisImmediatelyMessage)(nil),         // 34: api.knowledgebase.FileAnalysisImmediatelyMessage
	(*KnowledgeBaseInfo_UserInfo)(nil),             // 35: api.knowledgebase.KnowledgeBaseInfo.UserInfo
	(*UpdateKnowledgeBaseRequest_OwnerOption)(nil), // 36: api.knowledgebase.UpdateKnowledgeBaseRequest.OwnerOption
	(KnowledgeBaseDataType)(0),                     // 37: api.knowledgebase.KnowledgeBaseDataType
	(*timestamppb.Timestamp)(nil),                  // 38: google.protobuf.Timestamp
	(KnowledgeBaseFileStatus)(0),                   // 39: api.knowledgebase.KnowledgeBaseFileStatus
}
var file_knowledgebase_knowledgebase_proto_depIdxs = []int32{
	37, // 0: api.knowledgebase.KnowledgeBaseInfo.dataType:type_name -> api.knowledgebase.KnowledgeBaseDataType
	35, // 1: api.knowledgebase.KnowledgeBaseInfo.managers:type_name -> api.knowledgebase.KnowledgeBaseInfo.UserInfo
	35, // 2: api.knowledgebase.KnowledgeBaseInfo.editableUsers:type_name -> api.knowledgebase.KnowledgeBaseInfo.UserInfo
	38, // 3: api.knowledgebase.KnowledgeBaseInfo.createdAt:type_name -> google.protobuf.Timestamp
	37, // 4: api.knowledgebase.CreateKnowledgeBaseRequest.dataType:type_name -> api.knowledgebase.KnowledgeBaseDataType
	36, // 5: api.knowledgebase.UpdateKnowledgeBaseRequest.ownerOption:type_name -> api.knowledgebase.UpdateKnowledgeBaseRequest.OwnerOption
	0,  // 6: api.knowledgebase.GetKnowledgeBasesReply.records:type_name -> api.knowledgebase.KnowledgeBaseInfo
	0,  // 7: api.knowledgebase.GetAllKnowledgeBasesReply.records:type_name -> api.knowledgebase.KnowledgeBaseInfo
	0,  // 8: api.knowledgebase.PageKnowledgeBaseReply.records:type_name -> api.knowledgebase.KnowledgeBaseInfo
	38, // 9: api.knowledgebase.AddKnowledgeBaseFileFromSearchRequest.startTime:type_name -> google.protobuf.Timestamp
	38, // 10: api.knowledgebase.AddKnowledgeBaseFileFromSearchRequest.endTime:type_name -> google.protobuf.Timestamp
	39, // 11: api.knowledgebase.InnerUpdateKnowledgeBaseFileRequest.status:type_name -> api.knowledgebase.KnowledgeBaseFileStatus
	27, // 12: api.knowledgebase.PageKnowledgeBaseFileReply.records:type_name -> api.knowledgebase.PageKnowledgeBaseFileReplyItem
	37, // 13: api.knowledgebase.PageKnowledgeBaseFileReplyItem.dataType:type_name -> api.knowledgebase.KnowledgeBaseDataType
	39, // 14: api.knowledgebase.PageKnowledgeBaseFileReplyItem.fileStatus:type_name -> api.knowledgebase.KnowledgeBaseFileStatus
	38, // 15: api.knowledgebase.PageKnowledgeBaseFileReplyItem.createdAt:type_name -> google.protobuf.Timestamp
	38, // 16: api.knowledgebase.CountKnowledgeBaseFileRequest.startTime:type_name -> google.protobuf.Timestamp
	38, // 17: api.knowledgebase.CountKnowledgeBaseFileRequest.endTime:type_name -> google.protobuf.Timestamp
	1,  // 18: api.knowledgebase.KnowledgeBase.CreateKnowledgeBase:input_type -> api.knowledgebase.CreateKnowledgeBaseRequest
	3,  // 19: api.knowledgebase.KnowledgeBase.UpdateKnowledgeBase:input_type -> api.knowledgebase.UpdateKnowledgeBaseRequest
	5,  // 20: api.knowledgebase.KnowledgeBase.ChangeKnowledgeBaseOwner:input_type -> api.knowledgebase.ChangeKnowledgeBaseOwnerRequest
	7,  // 21: api.knowledgebase.KnowledgeBase.DeleteKnowledgeBase:input_type -> api.knowledgebase.DeleteKnowledgeBaseRequest
	9,  // 22: api.knowledgebase.KnowledgeBase.GetKnowledgeBases:input_type -> api.knowledgebase.GetKnowledgeBasesRequest
	11, // 23: api.knowledgebase.KnowledgeBase.GetAllKnowledgeBases:input_type -> api.knowledgebase.GetAllKnowledgeBasesRequest
	13, // 24: api.knowledgebase.KnowledgeBase.PageKnowledgeBase:input_type -> api.knowledgebase.PageKnowledgeBaseRequest
	15, // 25: api.knowledgebase.KnowledgeBase.AddKnowledgeBaseFileFromSearch:input_type -> api.knowledgebase.AddKnowledgeBaseFileFromSearchRequest
	17, // 26: api.knowledgebase.KnowledgeBase.AddKnowledgeBaseFile:input_type -> api.knowledgebase.AddKnowledgeBaseFileRequest
	19, // 27: api.knowledgebase.KnowledgeBase.DeleteKnowledgeBaseFile:input_type -> api.knowledgebase.DeleteKnowledgeBaseFileRequest
	21, // 28: api.knowledgebase.KnowledgeBase.UpdateKnowledgeBaseFileMetadata:input_type -> api.knowledgebase.UpdateKnowledgeBaseFileMetadataRequest
	23, // 29: api.knowledgebase.KnowledgeBase.InnerUpdateKnowledgeBaseFile:input_type -> api.knowledgebase.InnerUpdateKnowledgeBaseFileRequest
	25, // 30: api.knowledgebase.KnowledgeBase.PageKnowledgeBaseFile:input_type -> api.knowledgebase.PageKnowledgeBaseFileRequest
	28, // 31: api.knowledgebase.KnowledgeBase.ExistKnowledgeBaseFile:input_type -> api.knowledgebase.ExistKnowledgeBaseFileRequest
	30, // 32: api.knowledgebase.KnowledgeBase.GetFileKnowledgeBaseIDs:input_type -> api.knowledgebase.GetFileKnowledgeBaseIDsRequest
	32, // 33: api.knowledgebase.KnowledgeBase.CountKnowledgeBaseFile:input_type -> api.knowledgebase.CountKnowledgeBaseFileRequest
	2,  // 34: api.knowledgebase.KnowledgeBase.CreateKnowledgeBase:output_type -> api.knowledgebase.CreateKnowledgeBaseReply
	4,  // 35: api.knowledgebase.KnowledgeBase.UpdateKnowledgeBase:output_type -> api.knowledgebase.UpdateKnowledgeBaseReply
	6,  // 36: api.knowledgebase.KnowledgeBase.ChangeKnowledgeBaseOwner:output_type -> api.knowledgebase.ChangeKnowledgeBaseOwnerReply
	8,  // 37: api.knowledgebase.KnowledgeBase.DeleteKnowledgeBase:output_type -> api.knowledgebase.DeleteKnowledgeBaseReply
	10, // 38: api.knowledgebase.KnowledgeBase.GetKnowledgeBases:output_type -> api.knowledgebase.GetKnowledgeBasesReply
	12, // 39: api.knowledgebase.KnowledgeBase.GetAllKnowledgeBases:output_type -> api.knowledgebase.GetAllKnowledgeBasesReply
	14, // 40: api.knowledgebase.KnowledgeBase.PageKnowledgeBase:output_type -> api.knowledgebase.PageKnowledgeBaseReply
	16, // 41: api.knowledgebase.KnowledgeBase.AddKnowledgeBaseFileFromSearch:output_type -> api.knowledgebase.AddKnowledgeBaseFileFromSearchReply
	18, // 42: api.knowledgebase.KnowledgeBase.AddKnowledgeBaseFile:output_type -> api.knowledgebase.AddKnowledgeBaseFileReply
	20, // 43: api.knowledgebase.KnowledgeBase.DeleteKnowledgeBaseFile:output_type -> api.knowledgebase.DeleteKnowledgeBaseFileReply
	22, // 44: api.knowledgebase.KnowledgeBase.UpdateKnowledgeBaseFileMetadata:output_type -> api.knowledgebase.UpdateKnowledgeBaseFileMetadataReply
	24, // 45: api.knowledgebase.KnowledgeBase.InnerUpdateKnowledgeBaseFile:output_type -> api.knowledgebase.InnerUpdateKnowledgeBaseFileReply
	26, // 46: api.knowledgebase.KnowledgeBase.PageKnowledgeBaseFile:output_type -> api.knowledgebase.PageKnowledgeBaseFileReply
	29, // 47: api.knowledgebase.KnowledgeBase.ExistKnowledgeBaseFile:output_type -> api.knowledgebase.ExistKnowledgeBaseFileReply
	31, // 48: api.knowledgebase.KnowledgeBase.GetFileKnowledgeBaseIDs:output_type -> api.knowledgebase.GetFileKnowledgeBaseIDsReply
	33, // 49: api.knowledgebase.KnowledgeBase.CountKnowledgeBaseFile:output_type -> api.knowledgebase.CountKnowledgeBaseFileReply
	34, // [34:50] is the sub-list for method output_type
	18, // [18:34] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_knowledgebase_knowledgebase_proto_init() }
func file_knowledgebase_knowledgebase_proto_init() {
	if File_knowledgebase_knowledgebase_proto != nil {
		return
	}
	file_knowledgebase_enum_proto_init()
	file_knowledgebase_knowledgebase_proto_msgTypes[3].OneofWrappers = []any{}
	file_knowledgebase_knowledgebase_proto_msgTypes[23].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_knowledgebase_knowledgebase_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_knowledgebase_knowledgebase_proto_goTypes,
		DependencyIndexes: file_knowledgebase_knowledgebase_proto_depIdxs,
		MessageInfos:      file_knowledgebase_knowledgebase_proto_msgTypes,
	}.Build()
	File_knowledgebase_knowledgebase_proto = out.File
	file_knowledgebase_knowledgebase_proto_rawDesc = nil
	file_knowledgebase_knowledgebase_proto_goTypes = nil
	file_knowledgebase_knowledgebase_proto_depIdxs = nil
}

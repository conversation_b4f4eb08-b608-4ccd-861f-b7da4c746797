// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: knowledgebase/error.proto

package knowledgebase

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	ErrorReason_KNOWLEDGE_BASE_COMMON            ErrorReason = 0
	ErrorReason_KNOWLEDGE_BASE_ILLEGAL_OPERATION ErrorReason = 1
	ErrorReason_KNOWLEDGE_BASE_EXIST_AGENT       ErrorReason = 2
	ErrorReason_KNOWLEDGE_BASE_ADD_TOO_MUCH_FILE ErrorReason = 3
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0: "KNOWLEDGE_BASE_COMMON",
		1: "KNOWLEDGE_BASE_ILLEGAL_OPERATION",
		2: "KNOWLEDGE_BASE_EXIST_AGENT",
		3: "KNOWLEDGE_BASE_ADD_TOO_MUCH_FILE",
	}
	ErrorReason_value = map[string]int32{
		"KNOWLEDGE_BASE_COMMON":            0,
		"KNOWLEDGE_BASE_ILLEGAL_OPERATION": 1,
		"KNOWLEDGE_BASE_EXIST_AGENT":       2,
		"KNOWLEDGE_BASE_ADD_TOO_MUCH_FILE": 3,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_knowledgebase_error_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_knowledgebase_error_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_knowledgebase_error_proto_rawDescGZIP(), []int{0}
}

var File_knowledgebase_error_proto protoreflect.FileDescriptor

var file_knowledgebase_error_proto_rawDesc = []byte{
	0x0a, 0x19, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x2f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69,
	0x2e, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x1a, 0x13,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2a, 0xb2, 0x01, 0x0a, 0x0b, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x15, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45,
	0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x4f, 0x4e, 0x10, 0x00, 0x1a, 0x04,
	0xa8, 0x45, 0x90, 0x03, 0x12, 0x2a, 0x0a, 0x20, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47,
	0x45, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x49, 0x4c, 0x4c, 0x45, 0x47, 0x41, 0x4c, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03,
	0x12, 0x24, 0x0a, 0x1a, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45, 0x44, 0x47, 0x45, 0x5f, 0x42, 0x41,
	0x53, 0x45, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x47, 0x45, 0x4e, 0x54, 0x10, 0x02,
	0x1a, 0x04, 0xa8, 0x45, 0x90, 0x03, 0x12, 0x2a, 0x0a, 0x20, 0x4b, 0x4e, 0x4f, 0x57, 0x4c, 0x45,
	0x44, 0x47, 0x45, 0x5f, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x54, 0x4f, 0x4f,
	0x5f, 0x4d, 0x55, 0x43, 0x48, 0x5f, 0x46, 0x49, 0x4c, 0x45, 0x10, 0x03, 0x1a, 0x04, 0xa8, 0x45,
	0x90, 0x03, 0x1a, 0x04, 0xa0, 0x45, 0xf4, 0x03, 0x42, 0x4a, 0x5a, 0x48, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x69,
	0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x61, 0x69,
	0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64,
	0x67, 0x65, 0x62, 0x61, 0x73, 0x65, 0x3b, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65,
	0x62, 0x61, 0x73, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_knowledgebase_error_proto_rawDescOnce sync.Once
	file_knowledgebase_error_proto_rawDescData = file_knowledgebase_error_proto_rawDesc
)

func file_knowledgebase_error_proto_rawDescGZIP() []byte {
	file_knowledgebase_error_proto_rawDescOnce.Do(func() {
		file_knowledgebase_error_proto_rawDescData = protoimpl.X.CompressGZIP(file_knowledgebase_error_proto_rawDescData)
	})
	return file_knowledgebase_error_proto_rawDescData
}

var file_knowledgebase_error_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_knowledgebase_error_proto_goTypes = []any{
	(ErrorReason)(0), // 0: api.knowledgebase.ErrorReason
}
var file_knowledgebase_error_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_knowledgebase_error_proto_init() }
func file_knowledgebase_error_proto_init() {
	if File_knowledgebase_error_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_knowledgebase_error_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_knowledgebase_error_proto_goTypes,
		DependencyIndexes: file_knowledgebase_error_proto_depIdxs,
		EnumInfos:         file_knowledgebase_error_proto_enumTypes,
	}.Build()
	File_knowledgebase_error_proto = out.File
	file_knowledgebase_error_proto_rawDesc = nil
	file_knowledgebase_error_proto_goTypes = nil
	file_knowledgebase_error_proto_depIdxs = nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: externalmodel/externalmodel.proto

package externalmodel

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ExternalModelConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 解析类型 file, chat
	ParseType string `protobuf:"bytes,1,opt,name=parseType,proto3" json:"parseType,omitempty"`
	// 模型名称 通义千问 腾讯元宝 豆包 deepseek
	ModelName string `protobuf:"bytes,2,opt,name=modelName,proto3" json:"modelName,omitempty"`
	// host
	Host string `protobuf:"bytes,3,opt,name=host,proto3" json:"host,omitempty"`
	// schema前缀
	PreSchema string `protobuf:"bytes,4,opt,name=preSchema,proto3" json:"preSchema,omitempty"`
	// 解析规则 例 message.content.text
	ParseRule string `protobuf:"bytes,5,opt,name=parseRule,proto3" json:"parseRule,omitempty"`
	// session 解析规则, 仅在 parse_type 为 chat 时存在，例 conversation_id
	SessionParseRule string `protobuf:"bytes,6,opt,name=sessionParseRule,proto3" json:"sessionParseRule,omitempty"`
}

func (x *ExternalModelConfig) Reset() {
	*x = ExternalModelConfig{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalModelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalModelConfig) ProtoMessage() {}

func (x *ExternalModelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalModelConfig.ProtoReflect.Descriptor instead.
func (*ExternalModelConfig) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{0}
}

func (x *ExternalModelConfig) GetParseType() string {
	if x != nil {
		return x.ParseType
	}
	return ""
}

func (x *ExternalModelConfig) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ExternalModelConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *ExternalModelConfig) GetPreSchema() string {
	if x != nil {
		return x.PreSchema
	}
	return ""
}

func (x *ExternalModelConfig) GetParseRule() string {
	if x != nil {
		return x.ParseRule
	}
	return ""
}

func (x *ExternalModelConfig) GetSessionParseRule() string {
	if x != nil {
		return x.SessionParseRule
	}
	return ""
}

type ExternalModelFile struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID int64  `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	PreEntityTag   string `protobuf:"bytes,2,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	EntityTag      string `protobuf:"bytes,3,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	Name           string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	FullPath       string `protobuf:"bytes,5,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
	Size           int64  `protobuf:"varint,6,opt,name=size,proto3" json:"size,omitempty"`
	MimeType       string `protobuf:"bytes,7,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	Level          int64  `protobuf:"varint,8,opt,name=level,proto3" json:"level,omitempty"`
	ClassPath      string `protobuf:"bytes,9,opt,name=classPath,proto3" json:"classPath,omitempty"`
}

func (x *ExternalModelFile) Reset() {
	*x = ExternalModelFile{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ExternalModelFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExternalModelFile) ProtoMessage() {}

func (x *ExternalModelFile) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExternalModelFile.ProtoReflect.Descriptor instead.
func (*ExternalModelFile) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{1}
}

func (x *ExternalModelFile) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *ExternalModelFile) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *ExternalModelFile) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *ExternalModelFile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ExternalModelFile) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *ExternalModelFile) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ExternalModelFile) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *ExternalModelFile) GetLevel() int64 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *ExternalModelFile) GetClassPath() string {
	if x != nil {
		return x.ClassPath
	}
	return ""
}

type GetExternalModelConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetExternalModelConfigRequest) Reset() {
	*x = GetExternalModelConfigRequest{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetExternalModelConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExternalModelConfigRequest) ProtoMessage() {}

func (x *GetExternalModelConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExternalModelConfigRequest.ProtoReflect.Descriptor instead.
func (*GetExternalModelConfigRequest) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{2}
}

type GetExternalModelConfigReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*ExternalModelConfig `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetExternalModelConfigReply) Reset() {
	*x = GetExternalModelConfigReply{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetExternalModelConfigReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExternalModelConfigReply) ProtoMessage() {}

func (x *GetExternalModelConfigReply) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExternalModelConfigReply.ProtoReflect.Descriptor instead.
func (*GetExternalModelConfigReply) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{3}
}

func (x *GetExternalModelConfigReply) GetItems() []*ExternalModelConfig {
	if x != nil {
		return x.Items
	}
	return nil
}

type UploadExternalModelUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName  string                 `protobuf:"bytes,1,opt,name=modelName,proto3" json:"modelName,omitempty"`
	Question   string                 `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
	Files      []*ExternalModelFile   `protobuf:"bytes,3,rep,name=files,proto3" json:"files,omitempty"`
	PcName     string                 `protobuf:"bytes,4,opt,name=pcName,proto3" json:"pcName,omitempty"`
	HappenedAt *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=happenedAt,proto3" json:"happenedAt,omitempty"`
}

func (x *UploadExternalModelUsageRequest) Reset() {
	*x = UploadExternalModelUsageRequest{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadExternalModelUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadExternalModelUsageRequest) ProtoMessage() {}

func (x *UploadExternalModelUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadExternalModelUsageRequest.ProtoReflect.Descriptor instead.
func (*UploadExternalModelUsageRequest) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{4}
}

func (x *UploadExternalModelUsageRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *UploadExternalModelUsageRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *UploadExternalModelUsageRequest) GetFiles() []*ExternalModelFile {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *UploadExternalModelUsageRequest) GetPcName() string {
	if x != nil {
		return x.PcName
	}
	return ""
}

func (x *UploadExternalModelUsageRequest) GetHappenedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.HappenedAt
	}
	return nil
}

type UploadExternalModelUsageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UploadExternalModelUsageReply) Reset() {
	*x = UploadExternalModelUsageReply{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadExternalModelUsageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadExternalModelUsageReply) ProtoMessage() {}

func (x *UploadExternalModelUsageReply) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadExternalModelUsageReply.ProtoReflect.Descriptor instead.
func (*UploadExternalModelUsageReply) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{5}
}

type UpdateExternalModelUsageQuestionTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Question string `protobuf:"bytes,2,opt,name=question,proto3" json:"question,omitempty"`
}

func (x *UpdateExternalModelUsageQuestionTag) Reset() {
	*x = UpdateExternalModelUsageQuestionTag{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateExternalModelUsageQuestionTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExternalModelUsageQuestionTag) ProtoMessage() {}

func (x *UpdateExternalModelUsageQuestionTag) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExternalModelUsageQuestionTag.ProtoReflect.Descriptor instead.
func (*UpdateExternalModelUsageQuestionTag) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateExternalModelUsageQuestionTag) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateExternalModelUsageQuestionTag) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

type PageExternalModelUsageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int64 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	// 开始时间
	StartTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	// 结束时间
	EndTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	// 用户名
	UserName string `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName,omitempty"`
	// 部门名
	DeptName string `protobuf:"bytes,6,opt,name=deptName,proto3" json:"deptName,omitempty"`
	Id       int64  `protobuf:"varint,7,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *PageExternalModelUsageRequest) Reset() {
	*x = PageExternalModelUsageRequest{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageExternalModelUsageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageExternalModelUsageRequest) ProtoMessage() {}

func (x *PageExternalModelUsageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageExternalModelUsageRequest.ProtoReflect.Descriptor instead.
func (*PageExternalModelUsageRequest) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{7}
}

func (x *PageExternalModelUsageRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PageExternalModelUsageRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageExternalModelUsageRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *PageExternalModelUsageRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *PageExternalModelUsageRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *PageExternalModelUsageRequest) GetDeptName() string {
	if x != nil {
		return x.DeptName
	}
	return ""
}

func (x *PageExternalModelUsageRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type PageExternalModelUsageReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total   int64                              `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	Records []*PageExternalModelUsageReplyItem `protobuf:"bytes,2,rep,name=records,proto3" json:"records,omitempty"`
}

func (x *PageExternalModelUsageReply) Reset() {
	*x = PageExternalModelUsageReply{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageExternalModelUsageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageExternalModelUsageReply) ProtoMessage() {}

func (x *PageExternalModelUsageReply) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageExternalModelUsageReply.ProtoReflect.Descriptor instead.
func (*PageExternalModelUsageReply) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{8}
}

func (x *PageExternalModelUsageReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PageExternalModelUsageReply) GetRecords() []*PageExternalModelUsageReplyItem {
	if x != nil {
		return x.Records
	}
	return nil
}

type PageExternalModelUsageReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelName   string                 `protobuf:"bytes,2,opt,name=modelName,proto3" json:"modelName,omitempty"`
	ModelAvatar string                 `protobuf:"bytes,3,opt,name=modelAvatar,proto3" json:"modelAvatar,omitempty"`
	Question    string                 `protobuf:"bytes,4,opt,name=question,proto3" json:"question,omitempty"`
	QuestionTag string                 `protobuf:"bytes,5,opt,name=questionTag,proto3" json:"questionTag,omitempty"`
	Files       []*ExternalModelFile   `protobuf:"bytes,6,rep,name=files,proto3" json:"files,omitempty"`
	UserID      int64                  `protobuf:"varint,7,opt,name=userID,proto3" json:"userID,omitempty"`
	UserName    string                 `protobuf:"bytes,8,opt,name=userName,proto3" json:"userName,omitempty"`
	UserAvatar  string                 `protobuf:"bytes,13,opt,name=userAvatar,proto3" json:"userAvatar,omitempty"`
	DeptID      int64                  `protobuf:"varint,9,opt,name=deptID,proto3" json:"deptID,omitempty"`
	DeptName    string                 `protobuf:"bytes,10,opt,name=deptName,proto3" json:"deptName,omitempty"`
	PcName      string                 `protobuf:"bytes,11,opt,name=pcName,proto3" json:"pcName,omitempty"`
	HappenedAt  *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=happenedAt,proto3" json:"happenedAt,omitempty"`
}

func (x *PageExternalModelUsageReplyItem) Reset() {
	*x = PageExternalModelUsageReplyItem{}
	mi := &file_externalmodel_externalmodel_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageExternalModelUsageReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageExternalModelUsageReplyItem) ProtoMessage() {}

func (x *PageExternalModelUsageReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_externalmodel_externalmodel_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageExternalModelUsageReplyItem.ProtoReflect.Descriptor instead.
func (*PageExternalModelUsageReplyItem) Descriptor() ([]byte, []int) {
	return file_externalmodel_externalmodel_proto_rawDescGZIP(), []int{9}
}

func (x *PageExternalModelUsageReplyItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PageExternalModelUsageReplyItem) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *PageExternalModelUsageReplyItem) GetModelAvatar() string {
	if x != nil {
		return x.ModelAvatar
	}
	return ""
}

func (x *PageExternalModelUsageReplyItem) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *PageExternalModelUsageReplyItem) GetQuestionTag() string {
	if x != nil {
		return x.QuestionTag
	}
	return ""
}

func (x *PageExternalModelUsageReplyItem) GetFiles() []*ExternalModelFile {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *PageExternalModelUsageReplyItem) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *PageExternalModelUsageReplyItem) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *PageExternalModelUsageReplyItem) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *PageExternalModelUsageReplyItem) GetDeptID() int64 {
	if x != nil {
		return x.DeptID
	}
	return 0
}

func (x *PageExternalModelUsageReplyItem) GetDeptName() string {
	if x != nil {
		return x.DeptName
	}
	return ""
}

func (x *PageExternalModelUsageReplyItem) GetPcName() string {
	if x != nil {
		return x.PcName
	}
	return ""
}

func (x *PageExternalModelUsageReplyItem) GetHappenedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.HappenedAt
	}
	return nil
}

var File_externalmodel_externalmodel_proto protoreflect.FileDescriptor

var file_externalmodel_externalmodel_proto_rawDesc = []byte{
	0x0a, 0x21, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x11, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xcd, 0x01, 0x0a, 0x13, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x73, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x73,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x65, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x65, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x61, 0x72, 0x73, 0x65, 0x52, 0x75,
	0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x72, 0x73, 0x65, 0x52,
	0x75, 0x6c, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x72, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x73, 0x65, 0x52, 0x75, 0x6c, 0x65, 0x22,
	0x91, 0x02, 0x0a, 0x11, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66,
	0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x22, 0x0a,
	0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61,
	0x67, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x61,
	0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50,
	0x61, 0x74, 0x68, 0x22, 0x1f, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x22, 0x5b, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d,
	0x73, 0x22, 0xeb, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3a, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x0a, 0x68, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x68, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x41, 0x74, 0x22,
	0x1f, 0x0a, 0x1d, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x51, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0xa7, 0x02, 0x0a, 0x1d, 0x50, 0x61, 0x67, 0x65, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x24, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0a, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x28, 0x0a, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x0c, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x81, 0x01,
	0x0a, 0x1b, 0x50, 0x61, 0x67, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x4c, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x22, 0xc7, 0x03, 0x0a, 0x1f, 0x50, 0x61, 0x67, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x67,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x61, 0x67, 0x12, 0x3a, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61,
	0x72, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x70, 0x74, 0x49, 0x44, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x64, 0x65, 0x70, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x70, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x63, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x3a, 0x0a, 0x0a, 0x68, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x68, 0x61, 0x70, 0x70, 0x65, 0x6e, 0x65, 0x64, 0x41, 0x74, 0x32, 0xea, 0x03, 0x0a, 0x0d,
	0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x99, 0x01,
	0x0a, 0x16, 0x47, 0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x17, 0x12, 0x15, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0xa2, 0x01, 0x0a, 0x18, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x55, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x55,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x97,
	0x01, 0x0a, 0x16, 0x50, 0x61, 0x67, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x12, 0x30, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1b, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x15, 0x12, 0x13, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x42, 0x5f, 0x0a, 0x11, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x01, 0x5a,
	0x48, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2f, 0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65,
	0x61, 0x6d, 0x2f, 0x61, 0x69, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_externalmodel_externalmodel_proto_rawDescOnce sync.Once
	file_externalmodel_externalmodel_proto_rawDescData = file_externalmodel_externalmodel_proto_rawDesc
)

func file_externalmodel_externalmodel_proto_rawDescGZIP() []byte {
	file_externalmodel_externalmodel_proto_rawDescOnce.Do(func() {
		file_externalmodel_externalmodel_proto_rawDescData = protoimpl.X.CompressGZIP(file_externalmodel_externalmodel_proto_rawDescData)
	})
	return file_externalmodel_externalmodel_proto_rawDescData
}

var file_externalmodel_externalmodel_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_externalmodel_externalmodel_proto_goTypes = []any{
	(*ExternalModelConfig)(nil),                 // 0: api.externalmodel.ExternalModelConfig
	(*ExternalModelFile)(nil),                   // 1: api.externalmodel.ExternalModelFile
	(*GetExternalModelConfigRequest)(nil),       // 2: api.externalmodel.GetExternalModelConfigRequest
	(*GetExternalModelConfigReply)(nil),         // 3: api.externalmodel.GetExternalModelConfigReply
	(*UploadExternalModelUsageRequest)(nil),     // 4: api.externalmodel.UploadExternalModelUsageRequest
	(*UploadExternalModelUsageReply)(nil),       // 5: api.externalmodel.UploadExternalModelUsageReply
	(*UpdateExternalModelUsageQuestionTag)(nil), // 6: api.externalmodel.UpdateExternalModelUsageQuestionTag
	(*PageExternalModelUsageRequest)(nil),       // 7: api.externalmodel.PageExternalModelUsageRequest
	(*PageExternalModelUsageReply)(nil),         // 8: api.externalmodel.PageExternalModelUsageReply
	(*PageExternalModelUsageReplyItem)(nil),     // 9: api.externalmodel.PageExternalModelUsageReplyItem
	(*timestamppb.Timestamp)(nil),               // 10: google.protobuf.Timestamp
}
var file_externalmodel_externalmodel_proto_depIdxs = []int32{
	0,  // 0: api.externalmodel.GetExternalModelConfigReply.items:type_name -> api.externalmodel.ExternalModelConfig
	1,  // 1: api.externalmodel.UploadExternalModelUsageRequest.files:type_name -> api.externalmodel.ExternalModelFile
	10, // 2: api.externalmodel.UploadExternalModelUsageRequest.happenedAt:type_name -> google.protobuf.Timestamp
	10, // 3: api.externalmodel.PageExternalModelUsageRequest.startTime:type_name -> google.protobuf.Timestamp
	10, // 4: api.externalmodel.PageExternalModelUsageRequest.endTime:type_name -> google.protobuf.Timestamp
	9,  // 5: api.externalmodel.PageExternalModelUsageReply.records:type_name -> api.externalmodel.PageExternalModelUsageReplyItem
	1,  // 6: api.externalmodel.PageExternalModelUsageReplyItem.files:type_name -> api.externalmodel.ExternalModelFile
	10, // 7: api.externalmodel.PageExternalModelUsageReplyItem.happenedAt:type_name -> google.protobuf.Timestamp
	2,  // 8: api.externalmodel.ExternalModel.GetExternalModelConfig:input_type -> api.externalmodel.GetExternalModelConfigRequest
	4,  // 9: api.externalmodel.ExternalModel.UploadExternalModelUsage:input_type -> api.externalmodel.UploadExternalModelUsageRequest
	7,  // 10: api.externalmodel.ExternalModel.PageExternalModelUsage:input_type -> api.externalmodel.PageExternalModelUsageRequest
	3,  // 11: api.externalmodel.ExternalModel.GetExternalModelConfig:output_type -> api.externalmodel.GetExternalModelConfigReply
	5,  // 12: api.externalmodel.ExternalModel.UploadExternalModelUsage:output_type -> api.externalmodel.UploadExternalModelUsageReply
	8,  // 13: api.externalmodel.ExternalModel.PageExternalModelUsage:output_type -> api.externalmodel.PageExternalModelUsageReply
	11, // [11:14] is the sub-list for method output_type
	8,  // [8:11] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_externalmodel_externalmodel_proto_init() }
func file_externalmodel_externalmodel_proto_init() {
	if File_externalmodel_externalmodel_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_externalmodel_externalmodel_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_externalmodel_externalmodel_proto_goTypes,
		DependencyIndexes: file_externalmodel_externalmodel_proto_depIdxs,
		MessageInfos:      file_externalmodel_externalmodel_proto_msgTypes,
	}.Build()
	File_externalmodel_externalmodel_proto = out.File
	file_externalmodel_externalmodel_proto_rawDesc = nil
	file_externalmodel_externalmodel_proto_goTypes = nil
	file_externalmodel_externalmodel_proto_depIdxs = nil
}

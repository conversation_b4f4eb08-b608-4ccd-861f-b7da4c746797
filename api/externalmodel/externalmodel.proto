syntax = "proto3";

package api.externalmodel;

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/externalmodel;externalmodel";
option java_multiple_files = true;
option java_package = "api.externalmodel";

service ExternalModel {
  // 获取外部模型配置
  rpc GetExternalModelConfig(GetExternalModelConfigRequest) returns (GetExternalModelConfigReply) {
    option (google.api.http) = {get: "/externalModel/config"};
  }

  // 上传外部模型使用情况
  rpc UploadExternalModelUsage(UploadExternalModelUsageRequest) returns (UploadExternalModelUsageReply) {
    option (google.api.http) = {
      post: "/externalModel/upload"
      body: "*"
    };
  }

  // 分页获取外部模型使用情况
  rpc PageExternalModelUsage(PageExternalModelUsageRequest) returns (PageExternalModelUsageReply) {
    option (google.api.http) = {get: "/externalModel/page"};
  }
}

message ExternalModelConfig {
  // 解析类型 file, chat
  string parseType = 1;
  // 模型名称 通义千问 腾讯元宝 豆包 deepseek
  string modelName = 2;
  // host
  string host = 3;
  // schema前缀
  string preSchema = 4;
  // 解析规则 例 message.content.text
  string parseRule = 5;
  // session 解析规则, 仅在 parse_type 为 chat 时存在，例 conversation_id
  string sessionParseRule = 6;
}

message ExternalModelFile {
  int64 fileRelationID = 1;
  string preEntityTag = 2;
  string entityTag = 3;
  string name = 4;
  string fullPath = 5;
  int64 size = 6;
  string mimeType = 7;
  int64 level = 8;
  string classPath = 9;
}

message GetExternalModelConfigRequest {}
message GetExternalModelConfigReply {
  repeated ExternalModelConfig items = 1;
}

message UploadExternalModelUsageRequest {
  string modelName = 1;
  string question = 2;
  repeated ExternalModelFile files = 3;
  string pcName = 4;
  google.protobuf.Timestamp happenedAt = 5;
}
message UploadExternalModelUsageReply {}

message UpdateExternalModelUsageQuestionTag {
  int64 id = 1;
  string question = 2;
}

message PageExternalModelUsageRequest {
  int64 pageNum = 1 [
    (validate.rules).int64 = {gt: 0},
    (google.api.field_behavior) = REQUIRED
  ];
  int64 pageSize = 2 [
    (validate.rules).int64 = {
      gt: 0
      lte: 100
    },
    (google.api.field_behavior) = REQUIRED
  ];
  // 开始时间
  google.protobuf.Timestamp startTime = 3;
  // 结束时间
  google.protobuf.Timestamp endTime = 4;
  // 用户名
  string userName = 5;
  // 部门名
  string deptName = 6;
  int64 id = 7;
}
message PageExternalModelUsageReply {
  int64 total = 1;
  repeated PageExternalModelUsageReplyItem records = 2;
}
message PageExternalModelUsageReplyItem {
  int64 id = 1;
  string modelName = 2;
  string modelAvatar = 3;
  string question = 4;
  string questionTag = 5;
  repeated ExternalModelFile files = 6;
  int64 userID = 7;
  string userName = 8;
  string userAvatar = 13;
  int64 deptID = 9;
  string deptName = 10;
  string pcName = 11;
  google.protobuf.Timestamp happenedAt = 12;
}

// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: externalmodel/externalmodel.proto

package externalmodel

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationExternalModelGetExternalModelConfig = "/api.externalmodel.ExternalModel/GetExternalModelConfig"
const OperationExternalModelPageExternalModelUsage = "/api.externalmodel.ExternalModel/PageExternalModelUsage"
const OperationExternalModelUploadExternalModelUsage = "/api.externalmodel.ExternalModel/UploadExternalModelUsage"

type ExternalModelHTTPServer interface {
	// GetExternalModelConfig 获取外部模型配置
	GetExternalModelConfig(context.Context, *GetExternalModelConfigRequest) (*GetExternalModelConfigReply, error)
	// PageExternalModelUsage 分页获取外部模型使用情况
	PageExternalModelUsage(context.Context, *PageExternalModelUsageRequest) (*PageExternalModelUsageReply, error)
	// UploadExternalModelUsage 上传外部模型使用情况
	UploadExternalModelUsage(context.Context, *UploadExternalModelUsageRequest) (*UploadExternalModelUsageReply, error)
}

func RegisterExternalModelHTTPServer(s *http.Server, srv ExternalModelHTTPServer) {
	r := s.Route("/")
	r.GET("/externalModel/config", _ExternalModel_GetExternalModelConfig0_HTTP_Handler(srv))
	r.POST("/externalModel/upload", _ExternalModel_UploadExternalModelUsage0_HTTP_Handler(srv))
	r.GET("/externalModel/page", _ExternalModel_PageExternalModelUsage0_HTTP_Handler(srv))
}

func _ExternalModel_GetExternalModelConfig0_HTTP_Handler(srv ExternalModelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetExternalModelConfigRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationExternalModelGetExternalModelConfig)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetExternalModelConfig(ctx, req.(*GetExternalModelConfigRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetExternalModelConfigReply)
		return ctx.Result(200, reply)
	}
}

func _ExternalModel_UploadExternalModelUsage0_HTTP_Handler(srv ExternalModelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UploadExternalModelUsageRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationExternalModelUploadExternalModelUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UploadExternalModelUsage(ctx, req.(*UploadExternalModelUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UploadExternalModelUsageReply)
		return ctx.Result(200, reply)
	}
}

func _ExternalModel_PageExternalModelUsage0_HTTP_Handler(srv ExternalModelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PageExternalModelUsageRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationExternalModelPageExternalModelUsage)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PageExternalModelUsage(ctx, req.(*PageExternalModelUsageRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PageExternalModelUsageReply)
		return ctx.Result(200, reply)
	}
}

type ExternalModelHTTPClient interface {
	GetExternalModelConfig(ctx context.Context, req *GetExternalModelConfigRequest, opts ...http.CallOption) (rsp *GetExternalModelConfigReply, err error)
	PageExternalModelUsage(ctx context.Context, req *PageExternalModelUsageRequest, opts ...http.CallOption) (rsp *PageExternalModelUsageReply, err error)
	UploadExternalModelUsage(ctx context.Context, req *UploadExternalModelUsageRequest, opts ...http.CallOption) (rsp *UploadExternalModelUsageReply, err error)
}

type ExternalModelHTTPClientImpl struct {
	cc *http.Client
}

func NewExternalModelHTTPClient(client *http.Client) ExternalModelHTTPClient {
	return &ExternalModelHTTPClientImpl{client}
}

func (c *ExternalModelHTTPClientImpl) GetExternalModelConfig(ctx context.Context, in *GetExternalModelConfigRequest, opts ...http.CallOption) (*GetExternalModelConfigReply, error) {
	var out GetExternalModelConfigReply
	pattern := "/externalModel/config"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationExternalModelGetExternalModelConfig))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ExternalModelHTTPClientImpl) PageExternalModelUsage(ctx context.Context, in *PageExternalModelUsageRequest, opts ...http.CallOption) (*PageExternalModelUsageReply, error) {
	var out PageExternalModelUsageReply
	pattern := "/externalModel/page"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationExternalModelPageExternalModelUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ExternalModelHTTPClientImpl) UploadExternalModelUsage(ctx context.Context, in *UploadExternalModelUsageRequest, opts ...http.CallOption) (*UploadExternalModelUsageReply, error) {
	var out UploadExternalModelUsageReply
	pattern := "/externalModel/upload"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationExternalModelUploadExternalModelUsage))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: externalmodel/externalmodel.proto

package externalmodel

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ExternalModel_GetExternalModelConfig_FullMethodName   = "/api.externalmodel.ExternalModel/GetExternalModelConfig"
	ExternalModel_UploadExternalModelUsage_FullMethodName = "/api.externalmodel.ExternalModel/UploadExternalModelUsage"
	ExternalModel_PageExternalModelUsage_FullMethodName   = "/api.externalmodel.ExternalModel/PageExternalModelUsage"
)

// ExternalModelClient is the client API for ExternalModel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ExternalModelClient interface {
	// 获取外部模型配置
	GetExternalModelConfig(ctx context.Context, in *GetExternalModelConfigRequest, opts ...grpc.CallOption) (*GetExternalModelConfigReply, error)
	// 上传外部模型使用情况
	UploadExternalModelUsage(ctx context.Context, in *UploadExternalModelUsageRequest, opts ...grpc.CallOption) (*UploadExternalModelUsageReply, error)
	// 分页获取外部模型使用情况
	PageExternalModelUsage(ctx context.Context, in *PageExternalModelUsageRequest, opts ...grpc.CallOption) (*PageExternalModelUsageReply, error)
}

type externalModelClient struct {
	cc grpc.ClientConnInterface
}

func NewExternalModelClient(cc grpc.ClientConnInterface) ExternalModelClient {
	return &externalModelClient{cc}
}

func (c *externalModelClient) GetExternalModelConfig(ctx context.Context, in *GetExternalModelConfigRequest, opts ...grpc.CallOption) (*GetExternalModelConfigReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetExternalModelConfigReply)
	err := c.cc.Invoke(ctx, ExternalModel_GetExternalModelConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *externalModelClient) UploadExternalModelUsage(ctx context.Context, in *UploadExternalModelUsageRequest, opts ...grpc.CallOption) (*UploadExternalModelUsageReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UploadExternalModelUsageReply)
	err := c.cc.Invoke(ctx, ExternalModel_UploadExternalModelUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *externalModelClient) PageExternalModelUsage(ctx context.Context, in *PageExternalModelUsageRequest, opts ...grpc.CallOption) (*PageExternalModelUsageReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PageExternalModelUsageReply)
	err := c.cc.Invoke(ctx, ExternalModel_PageExternalModelUsage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ExternalModelServer is the server API for ExternalModel service.
// All implementations must embed UnimplementedExternalModelServer
// for forward compatibility.
type ExternalModelServer interface {
	// 获取外部模型配置
	GetExternalModelConfig(context.Context, *GetExternalModelConfigRequest) (*GetExternalModelConfigReply, error)
	// 上传外部模型使用情况
	UploadExternalModelUsage(context.Context, *UploadExternalModelUsageRequest) (*UploadExternalModelUsageReply, error)
	// 分页获取外部模型使用情况
	PageExternalModelUsage(context.Context, *PageExternalModelUsageRequest) (*PageExternalModelUsageReply, error)
	mustEmbedUnimplementedExternalModelServer()
}

// UnimplementedExternalModelServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedExternalModelServer struct{}

func (UnimplementedExternalModelServer) GetExternalModelConfig(context.Context, *GetExternalModelConfigRequest) (*GetExternalModelConfigReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExternalModelConfig not implemented")
}
func (UnimplementedExternalModelServer) UploadExternalModelUsage(context.Context, *UploadExternalModelUsageRequest) (*UploadExternalModelUsageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadExternalModelUsage not implemented")
}
func (UnimplementedExternalModelServer) PageExternalModelUsage(context.Context, *PageExternalModelUsageRequest) (*PageExternalModelUsageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PageExternalModelUsage not implemented")
}
func (UnimplementedExternalModelServer) mustEmbedUnimplementedExternalModelServer() {}
func (UnimplementedExternalModelServer) testEmbeddedByValue()                       {}

// UnsafeExternalModelServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExternalModelServer will
// result in compilation errors.
type UnsafeExternalModelServer interface {
	mustEmbedUnimplementedExternalModelServer()
}

func RegisterExternalModelServer(s grpc.ServiceRegistrar, srv ExternalModelServer) {
	// If the following call pancis, it indicates UnimplementedExternalModelServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ExternalModel_ServiceDesc, srv)
}

func _ExternalModel_GetExternalModelConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExternalModelConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalModelServer).GetExternalModelConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalModel_GetExternalModelConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalModelServer).GetExternalModelConfig(ctx, req.(*GetExternalModelConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExternalModel_UploadExternalModelUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadExternalModelUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalModelServer).UploadExternalModelUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalModel_UploadExternalModelUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalModelServer).UploadExternalModelUsage(ctx, req.(*UploadExternalModelUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExternalModel_PageExternalModelUsage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageExternalModelUsageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExternalModelServer).PageExternalModelUsage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExternalModel_PageExternalModelUsage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExternalModelServer).PageExternalModelUsage(ctx, req.(*PageExternalModelUsageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ExternalModel_ServiceDesc is the grpc.ServiceDesc for ExternalModel service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ExternalModel_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.externalmodel.ExternalModel",
	HandlerType: (*ExternalModelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetExternalModelConfig",
			Handler:    _ExternalModel_GetExternalModelConfig_Handler,
		},
		{
			MethodName: "UploadExternalModelUsage",
			Handler:    _ExternalModel_UploadExternalModelUsage_Handler,
		},
		{
			MethodName: "PageExternalModelUsage",
			Handler:    _ExternalModel_PageExternalModelUsage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "externalmodel/externalmodel.proto",
}

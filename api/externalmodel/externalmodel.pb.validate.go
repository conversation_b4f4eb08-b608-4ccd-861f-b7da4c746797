// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: externalmodel/externalmodel.proto

package externalmodel

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ExternalModelConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExternalModelConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExternalModelConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExternalModelConfigMultiError, or nil if none found.
func (m *ExternalModelConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ExternalModelConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ParseType

	// no validation rules for ModelName

	// no validation rules for Host

	// no validation rules for PreSchema

	// no validation rules for ParseRule

	// no validation rules for SessionParseRule

	if len(errors) > 0 {
		return ExternalModelConfigMultiError(errors)
	}

	return nil
}

// ExternalModelConfigMultiError is an error wrapping multiple validation
// errors returned by ExternalModelConfig.ValidateAll() if the designated
// constraints aren't met.
type ExternalModelConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExternalModelConfigMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExternalModelConfigMultiError) AllErrors() []error { return m }

// ExternalModelConfigValidationError is the validation error returned by
// ExternalModelConfig.Validate if the designated constraints aren't met.
type ExternalModelConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExternalModelConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExternalModelConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExternalModelConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExternalModelConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExternalModelConfigValidationError) ErrorName() string {
	return "ExternalModelConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ExternalModelConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExternalModelConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExternalModelConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExternalModelConfigValidationError{}

// Validate checks the field values on ExternalModelFile with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ExternalModelFile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExternalModelFile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExternalModelFileMultiError, or nil if none found.
func (m *ExternalModelFile) ValidateAll() error {
	return m.validate(true)
}

func (m *ExternalModelFile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for PreEntityTag

	// no validation rules for EntityTag

	// no validation rules for Name

	// no validation rules for FullPath

	// no validation rules for Size

	// no validation rules for MimeType

	// no validation rules for Level

	// no validation rules for ClassPath

	if len(errors) > 0 {
		return ExternalModelFileMultiError(errors)
	}

	return nil
}

// ExternalModelFileMultiError is an error wrapping multiple validation errors
// returned by ExternalModelFile.ValidateAll() if the designated constraints
// aren't met.
type ExternalModelFileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExternalModelFileMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExternalModelFileMultiError) AllErrors() []error { return m }

// ExternalModelFileValidationError is the validation error returned by
// ExternalModelFile.Validate if the designated constraints aren't met.
type ExternalModelFileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExternalModelFileValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExternalModelFileValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExternalModelFileValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExternalModelFileValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExternalModelFileValidationError) ErrorName() string {
	return "ExternalModelFileValidationError"
}

// Error satisfies the builtin error interface
func (e ExternalModelFileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExternalModelFile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExternalModelFileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExternalModelFileValidationError{}

// Validate checks the field values on GetExternalModelConfigRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExternalModelConfigRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExternalModelConfigRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetExternalModelConfigRequestMultiError, or nil if none found.
func (m *GetExternalModelConfigRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExternalModelConfigRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetExternalModelConfigRequestMultiError(errors)
	}

	return nil
}

// GetExternalModelConfigRequestMultiError is an error wrapping multiple
// validation errors returned by GetExternalModelConfigRequest.ValidateAll()
// if the designated constraints aren't met.
type GetExternalModelConfigRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExternalModelConfigRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExternalModelConfigRequestMultiError) AllErrors() []error { return m }

// GetExternalModelConfigRequestValidationError is the validation error
// returned by GetExternalModelConfigRequest.Validate if the designated
// constraints aren't met.
type GetExternalModelConfigRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExternalModelConfigRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExternalModelConfigRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExternalModelConfigRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExternalModelConfigRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExternalModelConfigRequestValidationError) ErrorName() string {
	return "GetExternalModelConfigRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetExternalModelConfigRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExternalModelConfigRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExternalModelConfigRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExternalModelConfigRequestValidationError{}

// Validate checks the field values on GetExternalModelConfigReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetExternalModelConfigReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetExternalModelConfigReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetExternalModelConfigReplyMultiError, or nil if none found.
func (m *GetExternalModelConfigReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetExternalModelConfigReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetExternalModelConfigReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetExternalModelConfigReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetExternalModelConfigReplyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetExternalModelConfigReplyMultiError(errors)
	}

	return nil
}

// GetExternalModelConfigReplyMultiError is an error wrapping multiple
// validation errors returned by GetExternalModelConfigReply.ValidateAll() if
// the designated constraints aren't met.
type GetExternalModelConfigReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetExternalModelConfigReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetExternalModelConfigReplyMultiError) AllErrors() []error { return m }

// GetExternalModelConfigReplyValidationError is the validation error returned
// by GetExternalModelConfigReply.Validate if the designated constraints
// aren't met.
type GetExternalModelConfigReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetExternalModelConfigReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetExternalModelConfigReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetExternalModelConfigReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetExternalModelConfigReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetExternalModelConfigReplyValidationError) ErrorName() string {
	return "GetExternalModelConfigReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetExternalModelConfigReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetExternalModelConfigReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetExternalModelConfigReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetExternalModelConfigReplyValidationError{}

// Validate checks the field values on UploadExternalModelUsageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadExternalModelUsageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadExternalModelUsageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadExternalModelUsageRequestMultiError, or nil if none found.
func (m *UploadExternalModelUsageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadExternalModelUsageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModelName

	// no validation rules for Question

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UploadExternalModelUsageRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UploadExternalModelUsageRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UploadExternalModelUsageRequestValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PcName

	if all {
		switch v := interface{}(m.GetHappenedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UploadExternalModelUsageRequestValidationError{
					field:  "HappenedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UploadExternalModelUsageRequestValidationError{
					field:  "HappenedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHappenedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UploadExternalModelUsageRequestValidationError{
				field:  "HappenedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UploadExternalModelUsageRequestMultiError(errors)
	}

	return nil
}

// UploadExternalModelUsageRequestMultiError is an error wrapping multiple
// validation errors returned by UploadExternalModelUsageRequest.ValidateAll()
// if the designated constraints aren't met.
type UploadExternalModelUsageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadExternalModelUsageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadExternalModelUsageRequestMultiError) AllErrors() []error { return m }

// UploadExternalModelUsageRequestValidationError is the validation error
// returned by UploadExternalModelUsageRequest.Validate if the designated
// constraints aren't met.
type UploadExternalModelUsageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadExternalModelUsageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadExternalModelUsageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadExternalModelUsageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadExternalModelUsageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadExternalModelUsageRequestValidationError) ErrorName() string {
	return "UploadExternalModelUsageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UploadExternalModelUsageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadExternalModelUsageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadExternalModelUsageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadExternalModelUsageRequestValidationError{}

// Validate checks the field values on UploadExternalModelUsageReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UploadExternalModelUsageReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UploadExternalModelUsageReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UploadExternalModelUsageReplyMultiError, or nil if none found.
func (m *UploadExternalModelUsageReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UploadExternalModelUsageReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UploadExternalModelUsageReplyMultiError(errors)
	}

	return nil
}

// UploadExternalModelUsageReplyMultiError is an error wrapping multiple
// validation errors returned by UploadExternalModelUsageReply.ValidateAll()
// if the designated constraints aren't met.
type UploadExternalModelUsageReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UploadExternalModelUsageReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UploadExternalModelUsageReplyMultiError) AllErrors() []error { return m }

// UploadExternalModelUsageReplyValidationError is the validation error
// returned by UploadExternalModelUsageReply.Validate if the designated
// constraints aren't met.
type UploadExternalModelUsageReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UploadExternalModelUsageReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UploadExternalModelUsageReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UploadExternalModelUsageReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UploadExternalModelUsageReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UploadExternalModelUsageReplyValidationError) ErrorName() string {
	return "UploadExternalModelUsageReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UploadExternalModelUsageReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUploadExternalModelUsageReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UploadExternalModelUsageReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UploadExternalModelUsageReplyValidationError{}

// Validate checks the field values on UpdateExternalModelUsageQuestionTag with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateExternalModelUsageQuestionTag) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateExternalModelUsageQuestionTag
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateExternalModelUsageQuestionTagMultiError, or nil if none found.
func (m *UpdateExternalModelUsageQuestionTag) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateExternalModelUsageQuestionTag) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Question

	if len(errors) > 0 {
		return UpdateExternalModelUsageQuestionTagMultiError(errors)
	}

	return nil
}

// UpdateExternalModelUsageQuestionTagMultiError is an error wrapping multiple
// validation errors returned by
// UpdateExternalModelUsageQuestionTag.ValidateAll() if the designated
// constraints aren't met.
type UpdateExternalModelUsageQuestionTagMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateExternalModelUsageQuestionTagMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateExternalModelUsageQuestionTagMultiError) AllErrors() []error { return m }

// UpdateExternalModelUsageQuestionTagValidationError is the validation error
// returned by UpdateExternalModelUsageQuestionTag.Validate if the designated
// constraints aren't met.
type UpdateExternalModelUsageQuestionTagValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateExternalModelUsageQuestionTagValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateExternalModelUsageQuestionTagValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateExternalModelUsageQuestionTagValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateExternalModelUsageQuestionTagValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateExternalModelUsageQuestionTagValidationError) ErrorName() string {
	return "UpdateExternalModelUsageQuestionTagValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateExternalModelUsageQuestionTagValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateExternalModelUsageQuestionTag.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateExternalModelUsageQuestionTagValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateExternalModelUsageQuestionTagValidationError{}

// Validate checks the field values on PageExternalModelUsageRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageExternalModelUsageRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageExternalModelUsageRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PageExternalModelUsageRequestMultiError, or nil if none found.
func (m *PageExternalModelUsageRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageExternalModelUsageRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageNum() <= 0 {
		err := PageExternalModelUsageRequestValidationError{
			field:  "PageNum",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val <= 0 || val > 100 {
		err := PageExternalModelUsageRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range (0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageExternalModelUsageRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageExternalModelUsageRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageExternalModelUsageRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageExternalModelUsageRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageExternalModelUsageRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageExternalModelUsageRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserName

	// no validation rules for DeptName

	// no validation rules for Id

	if len(errors) > 0 {
		return PageExternalModelUsageRequestMultiError(errors)
	}

	return nil
}

// PageExternalModelUsageRequestMultiError is an error wrapping multiple
// validation errors returned by PageExternalModelUsageRequest.ValidateAll()
// if the designated constraints aren't met.
type PageExternalModelUsageRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageExternalModelUsageRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageExternalModelUsageRequestMultiError) AllErrors() []error { return m }

// PageExternalModelUsageRequestValidationError is the validation error
// returned by PageExternalModelUsageRequest.Validate if the designated
// constraints aren't met.
type PageExternalModelUsageRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageExternalModelUsageRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageExternalModelUsageRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageExternalModelUsageRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageExternalModelUsageRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageExternalModelUsageRequestValidationError) ErrorName() string {
	return "PageExternalModelUsageRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PageExternalModelUsageRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageExternalModelUsageRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageExternalModelUsageRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageExternalModelUsageRequestValidationError{}

// Validate checks the field values on PageExternalModelUsageReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageExternalModelUsageReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageExternalModelUsageReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageExternalModelUsageReplyMultiError, or nil if none found.
func (m *PageExternalModelUsageReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PageExternalModelUsageReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageExternalModelUsageReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageExternalModelUsageReplyValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageExternalModelUsageReplyValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return PageExternalModelUsageReplyMultiError(errors)
	}

	return nil
}

// PageExternalModelUsageReplyMultiError is an error wrapping multiple
// validation errors returned by PageExternalModelUsageReply.ValidateAll() if
// the designated constraints aren't met.
type PageExternalModelUsageReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageExternalModelUsageReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageExternalModelUsageReplyMultiError) AllErrors() []error { return m }

// PageExternalModelUsageReplyValidationError is the validation error returned
// by PageExternalModelUsageReply.Validate if the designated constraints
// aren't met.
type PageExternalModelUsageReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageExternalModelUsageReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageExternalModelUsageReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageExternalModelUsageReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageExternalModelUsageReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageExternalModelUsageReplyValidationError) ErrorName() string {
	return "PageExternalModelUsageReplyValidationError"
}

// Error satisfies the builtin error interface
func (e PageExternalModelUsageReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageExternalModelUsageReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageExternalModelUsageReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageExternalModelUsageReplyValidationError{}

// Validate checks the field values on PageExternalModelUsageReplyItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageExternalModelUsageReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageExternalModelUsageReplyItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PageExternalModelUsageReplyItemMultiError, or nil if none found.
func (m *PageExternalModelUsageReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *PageExternalModelUsageReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ModelName

	// no validation rules for ModelAvatar

	// no validation rules for Question

	// no validation rules for QuestionTag

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageExternalModelUsageReplyItemValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageExternalModelUsageReplyItemValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageExternalModelUsageReplyItemValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for UserID

	// no validation rules for UserName

	// no validation rules for UserAvatar

	// no validation rules for DeptID

	// no validation rules for DeptName

	// no validation rules for PcName

	if all {
		switch v := interface{}(m.GetHappenedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PageExternalModelUsageReplyItemValidationError{
					field:  "HappenedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PageExternalModelUsageReplyItemValidationError{
					field:  "HappenedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHappenedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PageExternalModelUsageReplyItemValidationError{
				field:  "HappenedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PageExternalModelUsageReplyItemMultiError(errors)
	}

	return nil
}

// PageExternalModelUsageReplyItemMultiError is an error wrapping multiple
// validation errors returned by PageExternalModelUsageReplyItem.ValidateAll()
// if the designated constraints aren't met.
type PageExternalModelUsageReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageExternalModelUsageReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageExternalModelUsageReplyItemMultiError) AllErrors() []error { return m }

// PageExternalModelUsageReplyItemValidationError is the validation error
// returned by PageExternalModelUsageReplyItem.Validate if the designated
// constraints aren't met.
type PageExternalModelUsageReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageExternalModelUsageReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageExternalModelUsageReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageExternalModelUsageReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageExternalModelUsageReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageExternalModelUsageReplyItemValidationError) ErrorName() string {
	return "PageExternalModelUsageReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e PageExternalModelUsageReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageExternalModelUsageReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageExternalModelUsageReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageExternalModelUsageReplyItemValidationError{}

// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: aimodel/aimodel.proto

package aimodel

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAimodelCreateAimodel = "/api.aimodel.Aimodel/CreateAimodel"
const OperationAimodelDeleteAimodel = "/api.aimodel.Aimodel/DeleteAimodel"
const OperationAimodelGetAiModelCallCountTotalByTime = "/api.aimodel.Aimodel/GetAiModelCallCountTotalByTime"
const OperationAimodelGetAiModelCallDetailByDay = "/api.aimodel.Aimodel/GetAiModelCallDetailByDay"
const OperationAimodelListAimodel = "/api.aimodel.Aimodel/ListAimodel"
const OperationAimodelListExternalModel = "/api.aimodel.Aimodel/ListExternalModel"
const OperationAimodelPageAiModelUsageByAgentID = "/api.aimodel.Aimodel/PageAiModelUsageByAgentID"
const OperationAimodelUpdateAimodel = "/api.aimodel.Aimodel/UpdateAimodel"

type AimodelHTTPServer interface {
	// CreateAimodel 创建 Aimodel
	CreateAimodel(context.Context, *CreateAimodelRequest) (*CreateAimodelReply, error)
	// DeleteAimodel 删除 Aimodel
	DeleteAimodel(context.Context, *DeleteAimodelRequest) (*DeleteAimodelReply, error)
	// GetAiModelCallCountTotalByTime 按天获取模型调用次数
	GetAiModelCallCountTotalByTime(context.Context, *GetAiModelCallCountByTimeRequest) (*GetAiModelCallCountByTimeReply, error)
	// GetAiModelCallDetailByDay 获取模型请求、tokens消耗详情（按天）
	GetAiModelCallDetailByDay(context.Context, *GetAiModelCallDetailByDayRequest) (*GetAiModelCallDetailByDayReply, error)
	// ListAimodel 列表
	ListAimodel(context.Context, *GetAimodelRequest) (*GetAimodelReply, error)
	// ListExternalModel 外部模型列表
	ListExternalModel(context.Context, *ListExternalModelRequest) (*ListExternalModelReply, error)
	// PageAiModelUsageByAgentID 按智能体分页获取模型用量详情
	PageAiModelUsageByAgentID(context.Context, *PageAiModelUsageByAgentIDRequest) (*PageAiModelUsageByAgentIDReply, error)
	// UpdateAimodel 更新 Aimodel
	UpdateAimodel(context.Context, *UpdateAimodelRequest) (*UpdateAimodelReply, error)
}

func RegisterAimodelHTTPServer(s *http.Server, srv AimodelHTTPServer) {
	r := s.Route("/")
	r.POST("/aimodel/create", _Aimodel_CreateAimodel0_HTTP_Handler(srv))
	r.POST("/aimodel/update", _Aimodel_UpdateAimodel0_HTTP_Handler(srv))
	r.POST("/aimodel/delete", _Aimodel_DeleteAimodel0_HTTP_Handler(srv))
	r.POST("/aimodel/list", _Aimodel_ListAimodel0_HTTP_Handler(srv))
	r.POST("/aimodel/listExternalModel", _Aimodel_ListExternalModel0_HTTP_Handler(srv))
	r.POST("/aimodel/getAiModelCallCount", _Aimodel_GetAiModelCallCountTotalByTime0_HTTP_Handler(srv))
	r.POST("/aimodel/pageAiModelUsageByAgentID", _Aimodel_PageAiModelUsageByAgentID0_HTTP_Handler(srv))
	r.POST("/aimodel/getAiModelCallDetailByDay", _Aimodel_GetAiModelCallDetailByDay0_HTTP_Handler(srv))
}

func _Aimodel_CreateAimodel0_HTTP_Handler(srv AimodelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateAimodelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAimodelCreateAimodel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateAimodel(ctx, req.(*CreateAimodelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateAimodelReply)
		return ctx.Result(200, reply)
	}
}

func _Aimodel_UpdateAimodel0_HTTP_Handler(srv AimodelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateAimodelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAimodelUpdateAimodel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateAimodel(ctx, req.(*UpdateAimodelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateAimodelReply)
		return ctx.Result(200, reply)
	}
}

func _Aimodel_DeleteAimodel0_HTTP_Handler(srv AimodelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteAimodelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAimodelDeleteAimodel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteAimodel(ctx, req.(*DeleteAimodelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteAimodelReply)
		return ctx.Result(200, reply)
	}
}

func _Aimodel_ListAimodel0_HTTP_Handler(srv AimodelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAimodelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAimodelListAimodel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListAimodel(ctx, req.(*GetAimodelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAimodelReply)
		return ctx.Result(200, reply)
	}
}

func _Aimodel_ListExternalModel0_HTTP_Handler(srv AimodelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ListExternalModelRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAimodelListExternalModel)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ListExternalModel(ctx, req.(*ListExternalModelRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ListExternalModelReply)
		return ctx.Result(200, reply)
	}
}

func _Aimodel_GetAiModelCallCountTotalByTime0_HTTP_Handler(srv AimodelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAiModelCallCountByTimeRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAimodelGetAiModelCallCountTotalByTime)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAiModelCallCountTotalByTime(ctx, req.(*GetAiModelCallCountByTimeRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAiModelCallCountByTimeReply)
		return ctx.Result(200, reply)
	}
}

func _Aimodel_PageAiModelUsageByAgentID0_HTTP_Handler(srv AimodelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PageAiModelUsageByAgentIDRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAimodelPageAiModelUsageByAgentID)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PageAiModelUsageByAgentID(ctx, req.(*PageAiModelUsageByAgentIDRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PageAiModelUsageByAgentIDReply)
		return ctx.Result(200, reply)
	}
}

func _Aimodel_GetAiModelCallDetailByDay0_HTTP_Handler(srv AimodelHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAiModelCallDetailByDayRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAimodelGetAiModelCallDetailByDay)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAiModelCallDetailByDay(ctx, req.(*GetAiModelCallDetailByDayRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAiModelCallDetailByDayReply)
		return ctx.Result(200, reply)
	}
}

type AimodelHTTPClient interface {
	CreateAimodel(ctx context.Context, req *CreateAimodelRequest, opts ...http.CallOption) (rsp *CreateAimodelReply, err error)
	DeleteAimodel(ctx context.Context, req *DeleteAimodelRequest, opts ...http.CallOption) (rsp *DeleteAimodelReply, err error)
	GetAiModelCallCountTotalByTime(ctx context.Context, req *GetAiModelCallCountByTimeRequest, opts ...http.CallOption) (rsp *GetAiModelCallCountByTimeReply, err error)
	GetAiModelCallDetailByDay(ctx context.Context, req *GetAiModelCallDetailByDayRequest, opts ...http.CallOption) (rsp *GetAiModelCallDetailByDayReply, err error)
	ListAimodel(ctx context.Context, req *GetAimodelRequest, opts ...http.CallOption) (rsp *GetAimodelReply, err error)
	ListExternalModel(ctx context.Context, req *ListExternalModelRequest, opts ...http.CallOption) (rsp *ListExternalModelReply, err error)
	PageAiModelUsageByAgentID(ctx context.Context, req *PageAiModelUsageByAgentIDRequest, opts ...http.CallOption) (rsp *PageAiModelUsageByAgentIDReply, err error)
	UpdateAimodel(ctx context.Context, req *UpdateAimodelRequest, opts ...http.CallOption) (rsp *UpdateAimodelReply, err error)
}

type AimodelHTTPClientImpl struct {
	cc *http.Client
}

func NewAimodelHTTPClient(client *http.Client) AimodelHTTPClient {
	return &AimodelHTTPClientImpl{client}
}

func (c *AimodelHTTPClientImpl) CreateAimodel(ctx context.Context, in *CreateAimodelRequest, opts ...http.CallOption) (*CreateAimodelReply, error) {
	var out CreateAimodelReply
	pattern := "/aimodel/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAimodelCreateAimodel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AimodelHTTPClientImpl) DeleteAimodel(ctx context.Context, in *DeleteAimodelRequest, opts ...http.CallOption) (*DeleteAimodelReply, error) {
	var out DeleteAimodelReply
	pattern := "/aimodel/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAimodelDeleteAimodel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AimodelHTTPClientImpl) GetAiModelCallCountTotalByTime(ctx context.Context, in *GetAiModelCallCountByTimeRequest, opts ...http.CallOption) (*GetAiModelCallCountByTimeReply, error) {
	var out GetAiModelCallCountByTimeReply
	pattern := "/aimodel/getAiModelCallCount"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAimodelGetAiModelCallCountTotalByTime))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AimodelHTTPClientImpl) GetAiModelCallDetailByDay(ctx context.Context, in *GetAiModelCallDetailByDayRequest, opts ...http.CallOption) (*GetAiModelCallDetailByDayReply, error) {
	var out GetAiModelCallDetailByDayReply
	pattern := "/aimodel/getAiModelCallDetailByDay"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAimodelGetAiModelCallDetailByDay))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AimodelHTTPClientImpl) ListAimodel(ctx context.Context, in *GetAimodelRequest, opts ...http.CallOption) (*GetAimodelReply, error) {
	var out GetAimodelReply
	pattern := "/aimodel/list"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAimodelListAimodel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AimodelHTTPClientImpl) ListExternalModel(ctx context.Context, in *ListExternalModelRequest, opts ...http.CallOption) (*ListExternalModelReply, error) {
	var out ListExternalModelReply
	pattern := "/aimodel/listExternalModel"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAimodelListExternalModel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AimodelHTTPClientImpl) PageAiModelUsageByAgentID(ctx context.Context, in *PageAiModelUsageByAgentIDRequest, opts ...http.CallOption) (*PageAiModelUsageByAgentIDReply, error) {
	var out PageAiModelUsageByAgentIDReply
	pattern := "/aimodel/pageAiModelUsageByAgentID"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAimodelPageAiModelUsageByAgentID))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AimodelHTTPClientImpl) UpdateAimodel(ctx context.Context, in *UpdateAimodelRequest, opts ...http.CallOption) (*UpdateAimodelReply, error) {
	var out UpdateAimodelReply
	pattern := "/aimodel/update"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAimodelUpdateAimodel))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: aimodel/aimodel.proto

package aimodel

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Aimodel_CreateAimodel_FullMethodName                  = "/api.aimodel.Aimodel/CreateAimodel"
	Aimodel_UpdateAimodel_FullMethodName                  = "/api.aimodel.Aimodel/UpdateAimodel"
	Aimodel_DeleteAimodel_FullMethodName                  = "/api.aimodel.Aimodel/DeleteAimodel"
	Aimodel_ListAimodel_FullMethodName                    = "/api.aimodel.Aimodel/ListAimodel"
	Aimodel_ListExternalModel_FullMethodName              = "/api.aimodel.Aimodel/ListExternalModel"
	Aimodel_GetAiModelCallCountTotalByTime_FullMethodName = "/api.aimodel.Aimodel/GetAiModelCallCountTotalByTime"
	Aimodel_PageAiModelUsageByAgentID_FullMethodName      = "/api.aimodel.Aimodel/PageAiModelUsageByAgentID"
	Aimodel_GetAiModelCallDetailByDay_FullMethodName      = "/api.aimodel.Aimodel/GetAiModelCallDetailByDay"
)

// AimodelClient is the client API for Aimodel service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AimodelClient interface {
	// 创建 Aimodel
	CreateAimodel(ctx context.Context, in *CreateAimodelRequest, opts ...grpc.CallOption) (*CreateAimodelReply, error)
	// 更新 Aimodel
	UpdateAimodel(ctx context.Context, in *UpdateAimodelRequest, opts ...grpc.CallOption) (*UpdateAimodelReply, error)
	// 删除 Aimodel
	DeleteAimodel(ctx context.Context, in *DeleteAimodelRequest, opts ...grpc.CallOption) (*DeleteAimodelReply, error)
	// 列表
	ListAimodel(ctx context.Context, in *GetAimodelRequest, opts ...grpc.CallOption) (*GetAimodelReply, error)
	// 外部模型列表
	ListExternalModel(ctx context.Context, in *ListExternalModelRequest, opts ...grpc.CallOption) (*ListExternalModelReply, error)
	// 按天获取模型调用次数
	GetAiModelCallCountTotalByTime(ctx context.Context, in *GetAiModelCallCountByTimeRequest, opts ...grpc.CallOption) (*GetAiModelCallCountByTimeReply, error)
	// 按智能体分页获取模型用量详情
	PageAiModelUsageByAgentID(ctx context.Context, in *PageAiModelUsageByAgentIDRequest, opts ...grpc.CallOption) (*PageAiModelUsageByAgentIDReply, error)
	// 获取模型请求、tokens消耗详情（按天）
	GetAiModelCallDetailByDay(ctx context.Context, in *GetAiModelCallDetailByDayRequest, opts ...grpc.CallOption) (*GetAiModelCallDetailByDayReply, error)
}

type aimodelClient struct {
	cc grpc.ClientConnInterface
}

func NewAimodelClient(cc grpc.ClientConnInterface) AimodelClient {
	return &aimodelClient{cc}
}

func (c *aimodelClient) CreateAimodel(ctx context.Context, in *CreateAimodelRequest, opts ...grpc.CallOption) (*CreateAimodelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAimodelReply)
	err := c.cc.Invoke(ctx, Aimodel_CreateAimodel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimodelClient) UpdateAimodel(ctx context.Context, in *UpdateAimodelRequest, opts ...grpc.CallOption) (*UpdateAimodelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateAimodelReply)
	err := c.cc.Invoke(ctx, Aimodel_UpdateAimodel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimodelClient) DeleteAimodel(ctx context.Context, in *DeleteAimodelRequest, opts ...grpc.CallOption) (*DeleteAimodelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteAimodelReply)
	err := c.cc.Invoke(ctx, Aimodel_DeleteAimodel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimodelClient) ListAimodel(ctx context.Context, in *GetAimodelRequest, opts ...grpc.CallOption) (*GetAimodelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAimodelReply)
	err := c.cc.Invoke(ctx, Aimodel_ListAimodel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimodelClient) ListExternalModel(ctx context.Context, in *ListExternalModelRequest, opts ...grpc.CallOption) (*ListExternalModelReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListExternalModelReply)
	err := c.cc.Invoke(ctx, Aimodel_ListExternalModel_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimodelClient) GetAiModelCallCountTotalByTime(ctx context.Context, in *GetAiModelCallCountByTimeRequest, opts ...grpc.CallOption) (*GetAiModelCallCountByTimeReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAiModelCallCountByTimeReply)
	err := c.cc.Invoke(ctx, Aimodel_GetAiModelCallCountTotalByTime_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimodelClient) PageAiModelUsageByAgentID(ctx context.Context, in *PageAiModelUsageByAgentIDRequest, opts ...grpc.CallOption) (*PageAiModelUsageByAgentIDReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PageAiModelUsageByAgentIDReply)
	err := c.cc.Invoke(ctx, Aimodel_PageAiModelUsageByAgentID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aimodelClient) GetAiModelCallDetailByDay(ctx context.Context, in *GetAiModelCallDetailByDayRequest, opts ...grpc.CallOption) (*GetAiModelCallDetailByDayReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAiModelCallDetailByDayReply)
	err := c.cc.Invoke(ctx, Aimodel_GetAiModelCallDetailByDay_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AimodelServer is the server API for Aimodel service.
// All implementations must embed UnimplementedAimodelServer
// for forward compatibility.
type AimodelServer interface {
	// 创建 Aimodel
	CreateAimodel(context.Context, *CreateAimodelRequest) (*CreateAimodelReply, error)
	// 更新 Aimodel
	UpdateAimodel(context.Context, *UpdateAimodelRequest) (*UpdateAimodelReply, error)
	// 删除 Aimodel
	DeleteAimodel(context.Context, *DeleteAimodelRequest) (*DeleteAimodelReply, error)
	// 列表
	ListAimodel(context.Context, *GetAimodelRequest) (*GetAimodelReply, error)
	// 外部模型列表
	ListExternalModel(context.Context, *ListExternalModelRequest) (*ListExternalModelReply, error)
	// 按天获取模型调用次数
	GetAiModelCallCountTotalByTime(context.Context, *GetAiModelCallCountByTimeRequest) (*GetAiModelCallCountByTimeReply, error)
	// 按智能体分页获取模型用量详情
	PageAiModelUsageByAgentID(context.Context, *PageAiModelUsageByAgentIDRequest) (*PageAiModelUsageByAgentIDReply, error)
	// 获取模型请求、tokens消耗详情（按天）
	GetAiModelCallDetailByDay(context.Context, *GetAiModelCallDetailByDayRequest) (*GetAiModelCallDetailByDayReply, error)
	mustEmbedUnimplementedAimodelServer()
}

// UnimplementedAimodelServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAimodelServer struct{}

func (UnimplementedAimodelServer) CreateAimodel(context.Context, *CreateAimodelRequest) (*CreateAimodelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAimodel not implemented")
}
func (UnimplementedAimodelServer) UpdateAimodel(context.Context, *UpdateAimodelRequest) (*UpdateAimodelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAimodel not implemented")
}
func (UnimplementedAimodelServer) DeleteAimodel(context.Context, *DeleteAimodelRequest) (*DeleteAimodelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAimodel not implemented")
}
func (UnimplementedAimodelServer) ListAimodel(context.Context, *GetAimodelRequest) (*GetAimodelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAimodel not implemented")
}
func (UnimplementedAimodelServer) ListExternalModel(context.Context, *ListExternalModelRequest) (*ListExternalModelReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExternalModel not implemented")
}
func (UnimplementedAimodelServer) GetAiModelCallCountTotalByTime(context.Context, *GetAiModelCallCountByTimeRequest) (*GetAiModelCallCountByTimeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAiModelCallCountTotalByTime not implemented")
}
func (UnimplementedAimodelServer) PageAiModelUsageByAgentID(context.Context, *PageAiModelUsageByAgentIDRequest) (*PageAiModelUsageByAgentIDReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PageAiModelUsageByAgentID not implemented")
}
func (UnimplementedAimodelServer) GetAiModelCallDetailByDay(context.Context, *GetAiModelCallDetailByDayRequest) (*GetAiModelCallDetailByDayReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAiModelCallDetailByDay not implemented")
}
func (UnimplementedAimodelServer) mustEmbedUnimplementedAimodelServer() {}
func (UnimplementedAimodelServer) testEmbeddedByValue()                 {}

// UnsafeAimodelServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AimodelServer will
// result in compilation errors.
type UnsafeAimodelServer interface {
	mustEmbedUnimplementedAimodelServer()
}

func RegisterAimodelServer(s grpc.ServiceRegistrar, srv AimodelServer) {
	// If the following call pancis, it indicates UnimplementedAimodelServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Aimodel_ServiceDesc, srv)
}

func _Aimodel_CreateAimodel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAimodelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimodelServer).CreateAimodel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aimodel_CreateAimodel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimodelServer).CreateAimodel(ctx, req.(*CreateAimodelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aimodel_UpdateAimodel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAimodelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimodelServer).UpdateAimodel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aimodel_UpdateAimodel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimodelServer).UpdateAimodel(ctx, req.(*UpdateAimodelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aimodel_DeleteAimodel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAimodelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimodelServer).DeleteAimodel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aimodel_DeleteAimodel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimodelServer).DeleteAimodel(ctx, req.(*DeleteAimodelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aimodel_ListAimodel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAimodelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimodelServer).ListAimodel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aimodel_ListAimodel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimodelServer).ListAimodel(ctx, req.(*GetAimodelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aimodel_ListExternalModel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExternalModelRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimodelServer).ListExternalModel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aimodel_ListExternalModel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimodelServer).ListExternalModel(ctx, req.(*ListExternalModelRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aimodel_GetAiModelCallCountTotalByTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiModelCallCountByTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimodelServer).GetAiModelCallCountTotalByTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aimodel_GetAiModelCallCountTotalByTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimodelServer).GetAiModelCallCountTotalByTime(ctx, req.(*GetAiModelCallCountByTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aimodel_PageAiModelUsageByAgentID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageAiModelUsageByAgentIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimodelServer).PageAiModelUsageByAgentID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aimodel_PageAiModelUsageByAgentID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimodelServer).PageAiModelUsageByAgentID(ctx, req.(*PageAiModelUsageByAgentIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Aimodel_GetAiModelCallDetailByDay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiModelCallDetailByDayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AimodelServer).GetAiModelCallDetailByDay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Aimodel_GetAiModelCallDetailByDay_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AimodelServer).GetAiModelCallDetailByDay(ctx, req.(*GetAiModelCallDetailByDayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Aimodel_ServiceDesc is the grpc.ServiceDesc for Aimodel service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Aimodel_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.aimodel.Aimodel",
	HandlerType: (*AimodelServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAimodel",
			Handler:    _Aimodel_CreateAimodel_Handler,
		},
		{
			MethodName: "UpdateAimodel",
			Handler:    _Aimodel_UpdateAimodel_Handler,
		},
		{
			MethodName: "DeleteAimodel",
			Handler:    _Aimodel_DeleteAimodel_Handler,
		},
		{
			MethodName: "ListAimodel",
			Handler:    _Aimodel_ListAimodel_Handler,
		},
		{
			MethodName: "ListExternalModel",
			Handler:    _Aimodel_ListExternalModel_Handler,
		},
		{
			MethodName: "GetAiModelCallCountTotalByTime",
			Handler:    _Aimodel_GetAiModelCallCountTotalByTime_Handler,
		},
		{
			MethodName: "PageAiModelUsageByAgentID",
			Handler:    _Aimodel_PageAiModelUsageByAgentID_Handler,
		},
		{
			MethodName: "GetAiModelCallDetailByDay",
			Handler:    _Aimodel_GetAiModelCallDetailByDay_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "aimodel/aimodel.proto",
}

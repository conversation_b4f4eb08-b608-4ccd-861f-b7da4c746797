// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: aimodel/aimodel.proto

package aimodel

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AiModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelName string `protobuf:"bytes,2,opt,name=modelName,proto3" json:"modelName,omitempty"`
	// 1. 腾讯元宝
	Model             int64                  `protobuf:"varint,3,opt,name=model,proto3" json:"model,omitempty"`
	ApiKey            string                 `protobuf:"bytes,4,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	AvatarUrl         string                 `protobuf:"bytes,6,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"`
	CanInternetSearch bool                   `protobuf:"varint,7,opt,name=canInternetSearch,proto3" json:"canInternetSearch,omitempty"`
	Balance           float64                `protobuf:"fixed64,8,opt,name=balance,proto3" json:"balance,omitempty"` // 余额
	// 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
	ThinkingEnableStatus ModelThinkingEnableStatus `protobuf:"varint,9,opt,name=thinkingEnableStatus,proto3,enum=api.aimodel.ModelThinkingEnableStatus" json:"thinkingEnableStatus,omitempty"`
	ModelDetailName      string                    `protobuf:"bytes,10,opt,name=modelDetailName,proto3" json:"modelDetailName,omitempty"` // 模型详情名称
	// 后台地址
	BackgroundUrl string `protobuf:"bytes,11,opt,name=backgroundUrl,proto3" json:"backgroundUrl,omitempty"`
}

func (x *AiModel) Reset() {
	*x = AiModel{}
	mi := &file_aimodel_aimodel_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AiModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AiModel) ProtoMessage() {}

func (x *AiModel) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AiModel.ProtoReflect.Descriptor instead.
func (*AiModel) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{0}
}

func (x *AiModel) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AiModel) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *AiModel) GetModel() int64 {
	if x != nil {
		return x.Model
	}
	return 0
}

func (x *AiModel) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

func (x *AiModel) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AiModel) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *AiModel) GetCanInternetSearch() bool {
	if x != nil {
		return x.CanInternetSearch
	}
	return false
}

func (x *AiModel) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *AiModel) GetThinkingEnableStatus() ModelThinkingEnableStatus {
	if x != nil {
		return x.ThinkingEnableStatus
	}
	return ModelThinkingEnableStatus_ModelThinkingEnableStatusDisable
}

func (x *AiModel) GetModelDetailName() string {
	if x != nil {
		return x.ModelDetailName
	}
	return ""
}

func (x *AiModel) GetBackgroundUrl() string {
	if x != nil {
		return x.BackgroundUrl
	}
	return ""
}

type AiModelDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id                int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name              string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	ModelName         string `protobuf:"bytes,3,opt,name=model_name,json=modelName,proto3" json:"model_name,omitempty"`
	Url               string `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	CanInternetSearch bool   `protobuf:"varint,5,opt,name=canInternetSearch,proto3" json:"canInternetSearch,omitempty"`
	AvatarUrl         string `protobuf:"bytes,6,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"`
}

func (x *AiModelDetail) Reset() {
	*x = AiModelDetail{}
	mi := &file_aimodel_aimodel_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AiModelDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AiModelDetail) ProtoMessage() {}

func (x *AiModelDetail) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AiModelDetail.ProtoReflect.Descriptor instead.
func (*AiModelDetail) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{1}
}

func (x *AiModelDetail) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AiModelDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AiModelDetail) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *AiModelDetail) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *AiModelDetail) GetCanInternetSearch() bool {
	if x != nil {
		return x.CanInternetSearch
	}
	return false
}

func (x *AiModelDetail) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

// =========================== create
type CreateAimodelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName string `protobuf:"bytes,1,opt,name=modelName,proto3" json:"modelName,omitempty"`
	// 查询外部模型列表的id
	Model  int64  `protobuf:"varint,2,opt,name=model,proto3" json:"model,omitempty"`
	ApiKey string `protobuf:"bytes,3,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
}

func (x *CreateAimodelRequest) Reset() {
	*x = CreateAimodelRequest{}
	mi := &file_aimodel_aimodel_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAimodelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAimodelRequest) ProtoMessage() {}

func (x *CreateAimodelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAimodelRequest.ProtoReflect.Descriptor instead.
func (*CreateAimodelRequest) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{2}
}

func (x *CreateAimodelRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *CreateAimodelRequest) GetModel() int64 {
	if x != nil {
		return x.Model
	}
	return 0
}

func (x *CreateAimodelRequest) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type CreateAimodelItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateAimodelItem) Reset() {
	*x = CreateAimodelItem{}
	mi := &file_aimodel_aimodel_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAimodelItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAimodelItem) ProtoMessage() {}

func (x *CreateAimodelItem) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAimodelItem.ProtoReflect.Descriptor instead.
func (*CreateAimodelItem) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{3}
}

type CreateAimodelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *CreateAimodelReply) Reset() {
	*x = CreateAimodelReply{}
	mi := &file_aimodel_aimodel_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAimodelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAimodelReply) ProtoMessage() {}

func (x *CreateAimodelReply) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAimodelReply.ProtoReflect.Descriptor instead.
func (*CreateAimodelReply) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{4}
}

// =========================== update
type UpdateAimodelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelName string `protobuf:"bytes,2,opt,name=modelName,proto3" json:"modelName,omitempty"`
	// 1. 腾讯元宝
	Model  int64  `protobuf:"varint,3,opt,name=model,proto3" json:"model,omitempty"`
	ApiKey string `protobuf:"bytes,4,opt,name=apiKey,proto3" json:"apiKey,omitempty"`
}

func (x *UpdateAimodelRequest) Reset() {
	*x = UpdateAimodelRequest{}
	mi := &file_aimodel_aimodel_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAimodelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAimodelRequest) ProtoMessage() {}

func (x *UpdateAimodelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAimodelRequest.ProtoReflect.Descriptor instead.
func (*UpdateAimodelRequest) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateAimodelRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAimodelRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *UpdateAimodelRequest) GetModel() int64 {
	if x != nil {
		return x.Model
	}
	return 0
}

func (x *UpdateAimodelRequest) GetApiKey() string {
	if x != nil {
		return x.ApiKey
	}
	return ""
}

type UpdateAimodelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateAimodelReply) Reset() {
	*x = UpdateAimodelReply{}
	mi := &file_aimodel_aimodel_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAimodelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAimodelReply) ProtoMessage() {}

func (x *UpdateAimodelReply) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAimodelReply.ProtoReflect.Descriptor instead.
func (*UpdateAimodelReply) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{6}
}

// =========================== delete
type DeleteAimodelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *DeleteAimodelRequest) Reset() {
	*x = DeleteAimodelRequest{}
	mi := &file_aimodel_aimodel_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAimodelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAimodelRequest) ProtoMessage() {}

func (x *DeleteAimodelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAimodelRequest.ProtoReflect.Descriptor instead.
func (*DeleteAimodelRequest) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteAimodelRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteAimodelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteAimodelReply) Reset() {
	*x = DeleteAimodelReply{}
	mi := &file_aimodel_aimodel_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAimodelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAimodelReply) ProtoMessage() {}

func (x *DeleteAimodelReply) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAimodelReply.ProtoReflect.Descriptor instead.
func (*DeleteAimodelReply) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{8}
}

// =========================== get
type GetAimodelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName string `protobuf:"bytes,1,opt,name=modelName,proto3" json:"modelName,omitempty"`
}

func (x *GetAimodelRequest) Reset() {
	*x = GetAimodelRequest{}
	mi := &file_aimodel_aimodel_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAimodelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAimodelRequest) ProtoMessage() {}

func (x *GetAimodelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAimodelRequest.ProtoReflect.Descriptor instead.
func (*GetAimodelRequest) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{9}
}

func (x *GetAimodelRequest) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

type GetAimodelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*AiModel `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetAimodelReply) Reset() {
	*x = GetAimodelReply{}
	mi := &file_aimodel_aimodel_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAimodelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAimodelReply) ProtoMessage() {}

func (x *GetAimodelReply) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAimodelReply.ProtoReflect.Descriptor instead.
func (*GetAimodelReply) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{10}
}

func (x *GetAimodelReply) GetItems() []*AiModel {
	if x != nil {
		return x.Items
	}
	return nil
}

type ListExternalModelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ListExternalModelRequest) Reset() {
	*x = ListExternalModelRequest{}
	mi := &file_aimodel_aimodel_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListExternalModelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExternalModelRequest) ProtoMessage() {}

func (x *ListExternalModelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExternalModelRequest.ProtoReflect.Descriptor instead.
func (*ListExternalModelRequest) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{11}
}

type ListExternalModelReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*AiModelDetail `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *ListExternalModelReply) Reset() {
	*x = ListExternalModelReply{}
	mi := &file_aimodel_aimodel_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListExternalModelReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExternalModelReply) ProtoMessage() {}

func (x *ListExternalModelReply) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExternalModelReply.ProtoReflect.Descriptor instead.
func (*ListExternalModelReply) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{12}
}

func (x *ListExternalModelReply) GetItems() []*AiModelDetail {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetAiModelCallCountByTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *GetAiModelCallCountByTimeRequest) Reset() {
	*x = GetAiModelCallCountByTimeRequest{}
	mi := &file_aimodel_aimodel_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAiModelCallCountByTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAiModelCallCountByTimeRequest) ProtoMessage() {}

func (x *GetAiModelCallCountByTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAiModelCallCountByTimeRequest.ProtoReflect.Descriptor instead.
func (*GetAiModelCallCountByTimeRequest) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{13}
}

func (x *GetAiModelCallCountByTimeRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetAiModelCallCountByTimeRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type GetAiModelCallCountByTimeReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*GetAiModelCallCountByTimeReplyItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
}

func (x *GetAiModelCallCountByTimeReply) Reset() {
	*x = GetAiModelCallCountByTimeReply{}
	mi := &file_aimodel_aimodel_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAiModelCallCountByTimeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAiModelCallCountByTimeReply) ProtoMessage() {}

func (x *GetAiModelCallCountByTimeReply) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAiModelCallCountByTimeReply.ProtoReflect.Descriptor instead.
func (*GetAiModelCallCountByTimeReply) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{14}
}

func (x *GetAiModelCallCountByTimeReply) GetItems() []*GetAiModelCallCountByTimeReplyItem {
	if x != nil {
		return x.Items
	}
	return nil
}

type GetAiModelCallCountByTimeReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time  *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	Count int64                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *GetAiModelCallCountByTimeReplyItem) Reset() {
	*x = GetAiModelCallCountByTimeReplyItem{}
	mi := &file_aimodel_aimodel_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAiModelCallCountByTimeReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAiModelCallCountByTimeReplyItem) ProtoMessage() {}

func (x *GetAiModelCallCountByTimeReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAiModelCallCountByTimeReplyItem.ProtoReflect.Descriptor instead.
func (*GetAiModelCallCountByTimeReplyItem) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{15}
}

func (x *GetAiModelCallCountByTimeReplyItem) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *GetAiModelCallCountByTimeReplyItem) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type PageAiModelUsageByAgentIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int64 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	AgentId  int64 `protobuf:"varint,3,opt,name=agentId,proto3" json:"agentId,omitempty"`
}

func (x *PageAiModelUsageByAgentIDRequest) Reset() {
	*x = PageAiModelUsageByAgentIDRequest{}
	mi := &file_aimodel_aimodel_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageAiModelUsageByAgentIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageAiModelUsageByAgentIDRequest) ProtoMessage() {}

func (x *PageAiModelUsageByAgentIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageAiModelUsageByAgentIDRequest.ProtoReflect.Descriptor instead.
func (*PageAiModelUsageByAgentIDRequest) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{16}
}

func (x *PageAiModelUsageByAgentIDRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PageAiModelUsageByAgentIDRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageAiModelUsageByAgentIDRequest) GetAgentId() int64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

type PageAiModelUsageByAgentIDReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ModelName        string `protobuf:"bytes,2,opt,name=modelName,proto3" json:"modelName,omitempty"`
	ModelGatewayName string `protobuf:"bytes,3,opt,name=modelGatewayName,proto3" json:"modelGatewayName,omitempty"`
	UserId           int64  `protobuf:"varint,4,opt,name=userId,proto3" json:"userId,omitempty"`
	UserName         string `protobuf:"bytes,5,opt,name=userName,proto3" json:"userName,omitempty"`
	AgentId          int64  `protobuf:"varint,6,opt,name=agentId,proto3" json:"agentId,omitempty"`
	AgentName        string `protobuf:"bytes,7,opt,name=agentName,proto3" json:"agentName,omitempty"`
	Question         string `protobuf:"bytes,8,opt,name=question,proto3" json:"question,omitempty"`
	Answer           string `protobuf:"bytes,9,opt,name=answer,proto3" json:"answer,omitempty"`
	PromptTokens     int64  `protobuf:"varint,10,opt,name=promptTokens,proto3" json:"promptTokens,omitempty"`
	CompletionTokens int64  `protobuf:"varint,11,opt,name=completionTokens,proto3" json:"completionTokens,omitempty"`
	// 0: 未处理, 1: 成功, 2: 失败
	RequestStatus int64  `protobuf:"varint,12,opt,name=requestStatus,proto3" json:"requestStatus,omitempty"`
	ErrorCode     string `protobuf:"bytes,13,opt,name=errorCode,proto3" json:"errorCode,omitempty"`
	// 智能体头像
	AgentAvatar string `protobuf:"bytes,14,opt,name=agentAvatar,proto3" json:"agentAvatar,omitempty"`
	// 用户头像
	UserAvatar string `protobuf:"bytes,15,opt,name=userAvatar,proto3" json:"userAvatar,omitempty"`
	// 模型头像
	ModelDetailAvatar string `protobuf:"bytes,16,opt,name=modelDetailAvatar,proto3" json:"modelDetailAvatar,omitempty"`
}

func (x *PageAiModelUsageByAgentIDReplyItem) Reset() {
	*x = PageAiModelUsageByAgentIDReplyItem{}
	mi := &file_aimodel_aimodel_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageAiModelUsageByAgentIDReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageAiModelUsageByAgentIDReplyItem) ProtoMessage() {}

func (x *PageAiModelUsageByAgentIDReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageAiModelUsageByAgentIDReplyItem.ProtoReflect.Descriptor instead.
func (*PageAiModelUsageByAgentIDReplyItem) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{17}
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetModelGatewayName() string {
	if x != nil {
		return x.ModelGatewayName
	}
	return ""
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetUserId() int64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetAgentId() int64 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetPromptTokens() int64 {
	if x != nil {
		return x.PromptTokens
	}
	return 0
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetCompletionTokens() int64 {
	if x != nil {
		return x.CompletionTokens
	}
	return 0
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetRequestStatus() int64 {
	if x != nil {
		return x.RequestStatus
	}
	return 0
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetAgentAvatar() string {
	if x != nil {
		return x.AgentAvatar
	}
	return ""
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetUserAvatar() string {
	if x != nil {
		return x.UserAvatar
	}
	return ""
}

func (x *PageAiModelUsageByAgentIDReplyItem) GetModelDetailAvatar() string {
	if x != nil {
		return x.ModelDetailAvatar
	}
	return ""
}

type PageAiModelUsageByAgentIDReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*PageAiModelUsageByAgentIDReplyItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	Total int64                                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *PageAiModelUsageByAgentIDReply) Reset() {
	*x = PageAiModelUsageByAgentIDReply{}
	mi := &file_aimodel_aimodel_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageAiModelUsageByAgentIDReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageAiModelUsageByAgentIDReply) ProtoMessage() {}

func (x *PageAiModelUsageByAgentIDReply) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageAiModelUsageByAgentIDReply.ProtoReflect.Descriptor instead.
func (*PageAiModelUsageByAgentIDReply) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{18}
}

func (x *PageAiModelUsageByAgentIDReply) GetItems() []*PageAiModelUsageByAgentIDReplyItem {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *PageAiModelUsageByAgentIDReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type GetAiModelCallDetailByDayRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *GetAiModelCallDetailByDayRequest) Reset() {
	*x = GetAiModelCallDetailByDayRequest{}
	mi := &file_aimodel_aimodel_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAiModelCallDetailByDayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAiModelCallDetailByDayRequest) ProtoMessage() {}

func (x *GetAiModelCallDetailByDayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAiModelCallDetailByDayRequest.ProtoReflect.Descriptor instead.
func (*GetAiModelCallDetailByDayRequest) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{19}
}

func (x *GetAiModelCallDetailByDayRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *GetAiModelCallDetailByDayRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type ModelCallDetail struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ModelName        string `protobuf:"bytes,1,opt,name=modelName,proto3" json:"modelName,omitempty"`
	ApiCallCount     int64  `protobuf:"varint,2,opt,name=apiCallCount,proto3" json:"apiCallCount,omitempty"`         // 调用次数
	ApiErrCount      int64  `protobuf:"varint,3,opt,name=apiErrCount,proto3" json:"apiErrCount,omitempty"`           // 错误次数
	PromptTokens     int64  `protobuf:"varint,4,opt,name=promptTokens,proto3" json:"promptTokens,omitempty"`         // prompt tokens
	CompletionTokens int64  `protobuf:"varint,5,opt,name=completionTokens,proto3" json:"completionTokens,omitempty"` // completion tokens
}

func (x *ModelCallDetail) Reset() {
	*x = ModelCallDetail{}
	mi := &file_aimodel_aimodel_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelCallDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelCallDetail) ProtoMessage() {}

func (x *ModelCallDetail) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelCallDetail.ProtoReflect.Descriptor instead.
func (*ModelCallDetail) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{20}
}

func (x *ModelCallDetail) GetModelName() string {
	if x != nil {
		return x.ModelName
	}
	return ""
}

func (x *ModelCallDetail) GetApiCallCount() int64 {
	if x != nil {
		return x.ApiCallCount
	}
	return 0
}

func (x *ModelCallDetail) GetApiErrCount() int64 {
	if x != nil {
		return x.ApiErrCount
	}
	return 0
}

func (x *ModelCallDetail) GetPromptTokens() int64 {
	if x != nil {
		return x.PromptTokens
	}
	return 0
}

func (x *ModelCallDetail) GetCompletionTokens() int64 {
	if x != nil {
		return x.CompletionTokens
	}
	return 0
}

type GetAiModelCallDetailByDayReplyItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Time             *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=time,proto3" json:"time,omitempty"`
	ModelCallDetails []*ModelCallDetail     `protobuf:"bytes,2,rep,name=modelCallDetails,proto3" json:"modelCallDetails,omitempty"` // 模型调用详情
}

func (x *GetAiModelCallDetailByDayReplyItem) Reset() {
	*x = GetAiModelCallDetailByDayReplyItem{}
	mi := &file_aimodel_aimodel_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAiModelCallDetailByDayReplyItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAiModelCallDetailByDayReplyItem) ProtoMessage() {}

func (x *GetAiModelCallDetailByDayReplyItem) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAiModelCallDetailByDayReplyItem.ProtoReflect.Descriptor instead.
func (*GetAiModelCallDetailByDayReplyItem) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{21}
}

func (x *GetAiModelCallDetailByDayReplyItem) GetTime() *timestamppb.Timestamp {
	if x != nil {
		return x.Time
	}
	return nil
}

func (x *GetAiModelCallDetailByDayReplyItem) GetModelCallDetails() []*ModelCallDetail {
	if x != nil {
		return x.ModelCallDetails
	}
	return nil
}

type GetAiModelCallDetailByDayReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Items []*GetAiModelCallDetailByDayReplyItem `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"` // 按天的模型调用详情
}

func (x *GetAiModelCallDetailByDayReply) Reset() {
	*x = GetAiModelCallDetailByDayReply{}
	mi := &file_aimodel_aimodel_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAiModelCallDetailByDayReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAiModelCallDetailByDayReply) ProtoMessage() {}

func (x *GetAiModelCallDetailByDayReply) ProtoReflect() protoreflect.Message {
	mi := &file_aimodel_aimodel_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAiModelCallDetailByDayReply.ProtoReflect.Descriptor instead.
func (*GetAiModelCallDetailByDayReply) Descriptor() ([]byte, []int) {
	return file_aimodel_aimodel_proto_rawDescGZIP(), []int{22}
}

func (x *GetAiModelCallDetailByDayReply) GetItems() []*GetAiModelCallDetailByDayReplyItem {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_aimodel_aimodel_proto protoreflect.FileDescriptor

var file_aimodel_aimodel_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x1a, 0x12, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xb1, 0x03, 0x0a, 0x07, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c,
	0x12, 0x2c, 0x0a, 0x11, 0x63, 0x61, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6e,
	0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x18,
	0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x5a, 0x0a, 0x14, 0x74, 0x68, 0x69, 0x6e,
	0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x68, 0x69, 0x6e, 0x6b, 0x69,
	0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x14,
	0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x0d, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x55, 0x72, 0x6c, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x55, 0x72, 0x6c, 0x22, 0xb0, 0x01, 0x0a, 0x0d, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x11, 0x63,
	0x61, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x22, 0x62, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x22, 0x13, 0x0a, 0x11, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x74, 0x65, 0x6d,
	0x22, 0x14, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x72, 0x0a, 0x14, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x69, 0x4b, 0x65, 0x79, 0x22, 0x14, 0x0a, 0x12, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x26, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x14, 0x0a, 0x12, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x31,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x3d, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73,
	0x22, 0x1a, 0x0a, 0x18, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x4a, 0x0a, 0x16,
	0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x30, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x92, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74,
	0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x67, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x45, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x6a, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2e, 0x0a, 0x04,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x86, 0x01, 0x0a, 0x20, 0x50, 0x61, 0x67, 0x65, 0x41, 0x69, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e,
	0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x25, 0x0a, 0x08, 0x70, 0x61,
	0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa, 0x42,
	0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xa2, 0x04, 0x0a, 0x22,
	0x50, 0x61, 0x67, 0x65, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2a, 0x0a, 0x10, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67,
	0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x22, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73,
	0x12, 0x2a, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x12, 0x24, 0x0a, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x22, 0x7d, 0x0a, 0x1e, 0x50, 0x61, 0x67, 0x65, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55,
	0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x45, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e,
	0x50, 0x61, 0x67, 0x65, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65,
	0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0x92, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61,
	0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x44, 0x61, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34,
	0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0xc5, 0x01, 0x0a, 0x0f, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61,
	0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x61, 0x70, 0x69, 0x43, 0x61, 0x6c,
	0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x61, 0x70,
	0x69, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x70,
	0x69, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0b, 0x61, 0x70, 0x69, 0x45, 0x72, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c,
	0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x70, 0x72, 0x6f, 0x6d, 0x70, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73,
	0x12, 0x2a, 0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x73, 0x22, 0x9e, 0x01, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x44, 0x61, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x2e, 0x0a, 0x04, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x04, 0x74,
	0x69, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x10, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x10, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x67, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x44, 0x61, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x45, 0x0a, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x42, 0x79, 0x44, 0x61, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x49, 0x74, 0x65, 0x6d, 0x52,
	0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x32, 0xc6, 0x08, 0x0a, 0x07, 0x41, 0x69, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x6f, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14, 0x3a,
	0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x12, 0x6f, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x69, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x14,
	0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x6f, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x69,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x69, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1a, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x14, 0x3a, 0x01, 0x2a, 0x22, 0x0f, 0x2f, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x65, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x69, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x18, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x12, 0x3a, 0x01, 0x2a, 0x22, 0x0d, 0x2f,
	0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x86, 0x01, 0x0a,
	0x11, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x12, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x3a, 0x01, 0x2a, 0x22, 0x1a, 0x2f, 0x61, 0x69, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0xa5, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x74,
	0x61, 0x6c, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61,
	0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x3a, 0x01, 0x2a, 0x22,
	0x1c, 0x2f, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x67, 0x65, 0x74, 0x41, 0x69, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0xa6, 0x01,
	0x0a, 0x19, 0x50, 0x61, 0x67, 0x65, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61,
	0x67, 0x65, 0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x2d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x41, 0x69,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x41, 0x67, 0x65, 0x6e,
	0x74, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x41, 0x69, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x44, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a,
	0x01, 0x2a, 0x22, 0x22, 0x2f, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x61, 0x67,
	0x65, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x55, 0x73, 0x61, 0x67, 0x65, 0x42, 0x79, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0xa6, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x69,
	0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42,
	0x79, 0x44, 0x61, 0x79, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x44, 0x61, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x44, 0x61, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x3a, 0x01, 0x2a, 0x22, 0x22, 0x2f, 0x61, 0x69,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x67, 0x65, 0x74, 0x41, 0x69, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x43, 0x61, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x42, 0x79, 0x44, 0x61, 0x79, 0x42,
	0x4d, 0x0a, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x01,
	0x5a, 0x3c, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74,
	0x65, 0x61, 0x6d, 0x2f, 0x61, 0x69, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x3b, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_aimodel_aimodel_proto_rawDescOnce sync.Once
	file_aimodel_aimodel_proto_rawDescData = file_aimodel_aimodel_proto_rawDesc
)

func file_aimodel_aimodel_proto_rawDescGZIP() []byte {
	file_aimodel_aimodel_proto_rawDescOnce.Do(func() {
		file_aimodel_aimodel_proto_rawDescData = protoimpl.X.CompressGZIP(file_aimodel_aimodel_proto_rawDescData)
	})
	return file_aimodel_aimodel_proto_rawDescData
}

var file_aimodel_aimodel_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_aimodel_aimodel_proto_goTypes = []any{
	(*AiModel)(nil),                            // 0: api.aimodel.AiModel
	(*AiModelDetail)(nil),                      // 1: api.aimodel.AiModelDetail
	(*CreateAimodelRequest)(nil),               // 2: api.aimodel.CreateAimodelRequest
	(*CreateAimodelItem)(nil),                  // 3: api.aimodel.CreateAimodelItem
	(*CreateAimodelReply)(nil),                 // 4: api.aimodel.CreateAimodelReply
	(*UpdateAimodelRequest)(nil),               // 5: api.aimodel.UpdateAimodelRequest
	(*UpdateAimodelReply)(nil),                 // 6: api.aimodel.UpdateAimodelReply
	(*DeleteAimodelRequest)(nil),               // 7: api.aimodel.DeleteAimodelRequest
	(*DeleteAimodelReply)(nil),                 // 8: api.aimodel.DeleteAimodelReply
	(*GetAimodelRequest)(nil),                  // 9: api.aimodel.GetAimodelRequest
	(*GetAimodelReply)(nil),                    // 10: api.aimodel.GetAimodelReply
	(*ListExternalModelRequest)(nil),           // 11: api.aimodel.ListExternalModelRequest
	(*ListExternalModelReply)(nil),             // 12: api.aimodel.ListExternalModelReply
	(*GetAiModelCallCountByTimeRequest)(nil),   // 13: api.aimodel.GetAiModelCallCountByTimeRequest
	(*GetAiModelCallCountByTimeReply)(nil),     // 14: api.aimodel.GetAiModelCallCountByTimeReply
	(*GetAiModelCallCountByTimeReplyItem)(nil), // 15: api.aimodel.GetAiModelCallCountByTimeReplyItem
	(*PageAiModelUsageByAgentIDRequest)(nil),   // 16: api.aimodel.PageAiModelUsageByAgentIDRequest
	(*PageAiModelUsageByAgentIDReplyItem)(nil), // 17: api.aimodel.PageAiModelUsageByAgentIDReplyItem
	(*PageAiModelUsageByAgentIDReply)(nil),     // 18: api.aimodel.PageAiModelUsageByAgentIDReply
	(*GetAiModelCallDetailByDayRequest)(nil),   // 19: api.aimodel.GetAiModelCallDetailByDayRequest
	(*ModelCallDetail)(nil),                    // 20: api.aimodel.ModelCallDetail
	(*GetAiModelCallDetailByDayReplyItem)(nil), // 21: api.aimodel.GetAiModelCallDetailByDayReplyItem
	(*GetAiModelCallDetailByDayReply)(nil),     // 22: api.aimodel.GetAiModelCallDetailByDayReply
	(*timestamppb.Timestamp)(nil),              // 23: google.protobuf.Timestamp
	(ModelThinkingEnableStatus)(0),             // 24: api.aimodel.ModelThinkingEnableStatus
}
var file_aimodel_aimodel_proto_depIdxs = []int32{
	23, // 0: api.aimodel.AiModel.createdAt:type_name -> google.protobuf.Timestamp
	24, // 1: api.aimodel.AiModel.thinkingEnableStatus:type_name -> api.aimodel.ModelThinkingEnableStatus
	0,  // 2: api.aimodel.GetAimodelReply.items:type_name -> api.aimodel.AiModel
	1,  // 3: api.aimodel.ListExternalModelReply.items:type_name -> api.aimodel.AiModelDetail
	23, // 4: api.aimodel.GetAiModelCallCountByTimeRequest.startTime:type_name -> google.protobuf.Timestamp
	23, // 5: api.aimodel.GetAiModelCallCountByTimeRequest.endTime:type_name -> google.protobuf.Timestamp
	15, // 6: api.aimodel.GetAiModelCallCountByTimeReply.items:type_name -> api.aimodel.GetAiModelCallCountByTimeReplyItem
	23, // 7: api.aimodel.GetAiModelCallCountByTimeReplyItem.time:type_name -> google.protobuf.Timestamp
	17, // 8: api.aimodel.PageAiModelUsageByAgentIDReply.items:type_name -> api.aimodel.PageAiModelUsageByAgentIDReplyItem
	23, // 9: api.aimodel.GetAiModelCallDetailByDayRequest.startTime:type_name -> google.protobuf.Timestamp
	23, // 10: api.aimodel.GetAiModelCallDetailByDayRequest.endTime:type_name -> google.protobuf.Timestamp
	23, // 11: api.aimodel.GetAiModelCallDetailByDayReplyItem.time:type_name -> google.protobuf.Timestamp
	20, // 12: api.aimodel.GetAiModelCallDetailByDayReplyItem.modelCallDetails:type_name -> api.aimodel.ModelCallDetail
	21, // 13: api.aimodel.GetAiModelCallDetailByDayReply.items:type_name -> api.aimodel.GetAiModelCallDetailByDayReplyItem
	2,  // 14: api.aimodel.Aimodel.CreateAimodel:input_type -> api.aimodel.CreateAimodelRequest
	5,  // 15: api.aimodel.Aimodel.UpdateAimodel:input_type -> api.aimodel.UpdateAimodelRequest
	7,  // 16: api.aimodel.Aimodel.DeleteAimodel:input_type -> api.aimodel.DeleteAimodelRequest
	9,  // 17: api.aimodel.Aimodel.ListAimodel:input_type -> api.aimodel.GetAimodelRequest
	11, // 18: api.aimodel.Aimodel.ListExternalModel:input_type -> api.aimodel.ListExternalModelRequest
	13, // 19: api.aimodel.Aimodel.GetAiModelCallCountTotalByTime:input_type -> api.aimodel.GetAiModelCallCountByTimeRequest
	16, // 20: api.aimodel.Aimodel.PageAiModelUsageByAgentID:input_type -> api.aimodel.PageAiModelUsageByAgentIDRequest
	19, // 21: api.aimodel.Aimodel.GetAiModelCallDetailByDay:input_type -> api.aimodel.GetAiModelCallDetailByDayRequest
	4,  // 22: api.aimodel.Aimodel.CreateAimodel:output_type -> api.aimodel.CreateAimodelReply
	6,  // 23: api.aimodel.Aimodel.UpdateAimodel:output_type -> api.aimodel.UpdateAimodelReply
	8,  // 24: api.aimodel.Aimodel.DeleteAimodel:output_type -> api.aimodel.DeleteAimodelReply
	10, // 25: api.aimodel.Aimodel.ListAimodel:output_type -> api.aimodel.GetAimodelReply
	12, // 26: api.aimodel.Aimodel.ListExternalModel:output_type -> api.aimodel.ListExternalModelReply
	14, // 27: api.aimodel.Aimodel.GetAiModelCallCountTotalByTime:output_type -> api.aimodel.GetAiModelCallCountByTimeReply
	18, // 28: api.aimodel.Aimodel.PageAiModelUsageByAgentID:output_type -> api.aimodel.PageAiModelUsageByAgentIDReply
	22, // 29: api.aimodel.Aimodel.GetAiModelCallDetailByDay:output_type -> api.aimodel.GetAiModelCallDetailByDayReply
	22, // [22:30] is the sub-list for method output_type
	14, // [14:22] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_aimodel_aimodel_proto_init() }
func file_aimodel_aimodel_proto_init() {
	if File_aimodel_aimodel_proto != nil {
		return
	}
	file_aimodel_enum_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aimodel_aimodel_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_aimodel_aimodel_proto_goTypes,
		DependencyIndexes: file_aimodel_aimodel_proto_depIdxs,
		MessageInfos:      file_aimodel_aimodel_proto_msgTypes,
	}.Build()
	File_aimodel_aimodel_proto = out.File
	file_aimodel_aimodel_proto_rawDesc = nil
	file_aimodel_aimodel_proto_goTypes = nil
	file_aimodel_aimodel_proto_depIdxs = nil
}

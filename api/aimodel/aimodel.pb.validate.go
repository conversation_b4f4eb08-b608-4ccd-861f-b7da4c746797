// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: aimodel/aimodel.proto

package aimodel

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AiModel with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AiModel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AiModel with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AiModelMultiError, or nil if none found.
func (m *AiModel) ValidateAll() error {
	return m.validate(true)
}

func (m *AiModel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ModelName

	// no validation rules for Model

	// no validation rules for ApiKey

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AiModelValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AiModelValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AiModelValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AvatarUrl

	// no validation rules for CanInternetSearch

	// no validation rules for Balance

	// no validation rules for ThinkingEnableStatus

	// no validation rules for ModelDetailName

	// no validation rules for BackgroundUrl

	if len(errors) > 0 {
		return AiModelMultiError(errors)
	}

	return nil
}

// AiModelMultiError is an error wrapping multiple validation errors returned
// by AiModel.ValidateAll() if the designated constraints aren't met.
type AiModelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AiModelMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AiModelMultiError) AllErrors() []error { return m }

// AiModelValidationError is the validation error returned by AiModel.Validate
// if the designated constraints aren't met.
type AiModelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AiModelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AiModelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AiModelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AiModelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AiModelValidationError) ErrorName() string { return "AiModelValidationError" }

// Error satisfies the builtin error interface
func (e AiModelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAiModel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AiModelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AiModelValidationError{}

// Validate checks the field values on AiModelDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AiModelDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AiModelDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AiModelDetailMultiError, or
// nil if none found.
func (m *AiModelDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *AiModelDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for ModelName

	// no validation rules for Url

	// no validation rules for CanInternetSearch

	// no validation rules for AvatarUrl

	if len(errors) > 0 {
		return AiModelDetailMultiError(errors)
	}

	return nil
}

// AiModelDetailMultiError is an error wrapping multiple validation errors
// returned by AiModelDetail.ValidateAll() if the designated constraints
// aren't met.
type AiModelDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AiModelDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AiModelDetailMultiError) AllErrors() []error { return m }

// AiModelDetailValidationError is the validation error returned by
// AiModelDetail.Validate if the designated constraints aren't met.
type AiModelDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AiModelDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AiModelDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AiModelDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AiModelDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AiModelDetailValidationError) ErrorName() string { return "AiModelDetailValidationError" }

// Error satisfies the builtin error interface
func (e AiModelDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAiModelDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AiModelDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AiModelDetailValidationError{}

// Validate checks the field values on CreateAimodelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAimodelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAimodelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAimodelRequestMultiError, or nil if none found.
func (m *CreateAimodelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAimodelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModelName

	// no validation rules for Model

	// no validation rules for ApiKey

	if len(errors) > 0 {
		return CreateAimodelRequestMultiError(errors)
	}

	return nil
}

// CreateAimodelRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAimodelRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAimodelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAimodelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAimodelRequestMultiError) AllErrors() []error { return m }

// CreateAimodelRequestValidationError is the validation error returned by
// CreateAimodelRequest.Validate if the designated constraints aren't met.
type CreateAimodelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAimodelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAimodelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAimodelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAimodelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAimodelRequestValidationError) ErrorName() string {
	return "CreateAimodelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAimodelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAimodelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAimodelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAimodelRequestValidationError{}

// Validate checks the field values on CreateAimodelItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateAimodelItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAimodelItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAimodelItemMultiError, or nil if none found.
func (m *CreateAimodelItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAimodelItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateAimodelItemMultiError(errors)
	}

	return nil
}

// CreateAimodelItemMultiError is an error wrapping multiple validation errors
// returned by CreateAimodelItem.ValidateAll() if the designated constraints
// aren't met.
type CreateAimodelItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAimodelItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAimodelItemMultiError) AllErrors() []error { return m }

// CreateAimodelItemValidationError is the validation error returned by
// CreateAimodelItem.Validate if the designated constraints aren't met.
type CreateAimodelItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAimodelItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAimodelItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAimodelItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAimodelItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAimodelItemValidationError) ErrorName() string {
	return "CreateAimodelItemValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAimodelItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAimodelItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAimodelItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAimodelItemValidationError{}

// Validate checks the field values on CreateAimodelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAimodelReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAimodelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAimodelReplyMultiError, or nil if none found.
func (m *CreateAimodelReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAimodelReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return CreateAimodelReplyMultiError(errors)
	}

	return nil
}

// CreateAimodelReplyMultiError is an error wrapping multiple validation errors
// returned by CreateAimodelReply.ValidateAll() if the designated constraints
// aren't met.
type CreateAimodelReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAimodelReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAimodelReplyMultiError) AllErrors() []error { return m }

// CreateAimodelReplyValidationError is the validation error returned by
// CreateAimodelReply.Validate if the designated constraints aren't met.
type CreateAimodelReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAimodelReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAimodelReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAimodelReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAimodelReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAimodelReplyValidationError) ErrorName() string {
	return "CreateAimodelReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAimodelReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAimodelReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAimodelReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAimodelReplyValidationError{}

// Validate checks the field values on UpdateAimodelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAimodelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAimodelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAimodelRequestMultiError, or nil if none found.
func (m *UpdateAimodelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAimodelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ModelName

	// no validation rules for Model

	// no validation rules for ApiKey

	if len(errors) > 0 {
		return UpdateAimodelRequestMultiError(errors)
	}

	return nil
}

// UpdateAimodelRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAimodelRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAimodelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAimodelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAimodelRequestMultiError) AllErrors() []error { return m }

// UpdateAimodelRequestValidationError is the validation error returned by
// UpdateAimodelRequest.Validate if the designated constraints aren't met.
type UpdateAimodelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAimodelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAimodelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAimodelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAimodelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAimodelRequestValidationError) ErrorName() string {
	return "UpdateAimodelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAimodelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAimodelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAimodelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAimodelRequestValidationError{}

// Validate checks the field values on UpdateAimodelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAimodelReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAimodelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAimodelReplyMultiError, or nil if none found.
func (m *UpdateAimodelReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAimodelReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateAimodelReplyMultiError(errors)
	}

	return nil
}

// UpdateAimodelReplyMultiError is an error wrapping multiple validation errors
// returned by UpdateAimodelReply.ValidateAll() if the designated constraints
// aren't met.
type UpdateAimodelReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAimodelReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAimodelReplyMultiError) AllErrors() []error { return m }

// UpdateAimodelReplyValidationError is the validation error returned by
// UpdateAimodelReply.Validate if the designated constraints aren't met.
type UpdateAimodelReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAimodelReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAimodelReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAimodelReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAimodelReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAimodelReplyValidationError) ErrorName() string {
	return "UpdateAimodelReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAimodelReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAimodelReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAimodelReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAimodelReplyValidationError{}

// Validate checks the field values on DeleteAimodelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAimodelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAimodelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAimodelRequestMultiError, or nil if none found.
func (m *DeleteAimodelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAimodelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteAimodelRequestMultiError(errors)
	}

	return nil
}

// DeleteAimodelRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteAimodelRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteAimodelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAimodelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAimodelRequestMultiError) AllErrors() []error { return m }

// DeleteAimodelRequestValidationError is the validation error returned by
// DeleteAimodelRequest.Validate if the designated constraints aren't met.
type DeleteAimodelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAimodelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAimodelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAimodelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAimodelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAimodelRequestValidationError) ErrorName() string {
	return "DeleteAimodelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAimodelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAimodelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAimodelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAimodelRequestValidationError{}

// Validate checks the field values on DeleteAimodelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteAimodelReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteAimodelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteAimodelReplyMultiError, or nil if none found.
func (m *DeleteAimodelReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteAimodelReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteAimodelReplyMultiError(errors)
	}

	return nil
}

// DeleteAimodelReplyMultiError is an error wrapping multiple validation errors
// returned by DeleteAimodelReply.ValidateAll() if the designated constraints
// aren't met.
type DeleteAimodelReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteAimodelReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteAimodelReplyMultiError) AllErrors() []error { return m }

// DeleteAimodelReplyValidationError is the validation error returned by
// DeleteAimodelReply.Validate if the designated constraints aren't met.
type DeleteAimodelReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteAimodelReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteAimodelReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteAimodelReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteAimodelReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteAimodelReplyValidationError) ErrorName() string {
	return "DeleteAimodelReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteAimodelReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteAimodelReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteAimodelReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteAimodelReplyValidationError{}

// Validate checks the field values on GetAimodelRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAimodelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAimodelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAimodelRequestMultiError, or nil if none found.
func (m *GetAimodelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAimodelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModelName

	if len(errors) > 0 {
		return GetAimodelRequestMultiError(errors)
	}

	return nil
}

// GetAimodelRequestMultiError is an error wrapping multiple validation errors
// returned by GetAimodelRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAimodelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAimodelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAimodelRequestMultiError) AllErrors() []error { return m }

// GetAimodelRequestValidationError is the validation error returned by
// GetAimodelRequest.Validate if the designated constraints aren't met.
type GetAimodelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAimodelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAimodelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAimodelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAimodelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAimodelRequestValidationError) ErrorName() string {
	return "GetAimodelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAimodelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAimodelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAimodelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAimodelRequestValidationError{}

// Validate checks the field values on GetAimodelReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAimodelReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAimodelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAimodelReplyMultiError, or nil if none found.
func (m *GetAimodelReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAimodelReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAimodelReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAimodelReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAimodelReplyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAimodelReplyMultiError(errors)
	}

	return nil
}

// GetAimodelReplyMultiError is an error wrapping multiple validation errors
// returned by GetAimodelReply.ValidateAll() if the designated constraints
// aren't met.
type GetAimodelReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAimodelReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAimodelReplyMultiError) AllErrors() []error { return m }

// GetAimodelReplyValidationError is the validation error returned by
// GetAimodelReply.Validate if the designated constraints aren't met.
type GetAimodelReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAimodelReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAimodelReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAimodelReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAimodelReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAimodelReplyValidationError) ErrorName() string { return "GetAimodelReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetAimodelReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAimodelReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAimodelReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAimodelReplyValidationError{}

// Validate checks the field values on ListExternalModelRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExternalModelRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExternalModelRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExternalModelRequestMultiError, or nil if none found.
func (m *ListExternalModelRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExternalModelRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ListExternalModelRequestMultiError(errors)
	}

	return nil
}

// ListExternalModelRequestMultiError is an error wrapping multiple validation
// errors returned by ListExternalModelRequest.ValidateAll() if the designated
// constraints aren't met.
type ListExternalModelRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExternalModelRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExternalModelRequestMultiError) AllErrors() []error { return m }

// ListExternalModelRequestValidationError is the validation error returned by
// ListExternalModelRequest.Validate if the designated constraints aren't met.
type ListExternalModelRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExternalModelRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExternalModelRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExternalModelRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExternalModelRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExternalModelRequestValidationError) ErrorName() string {
	return "ListExternalModelRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListExternalModelRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExternalModelRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExternalModelRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExternalModelRequestValidationError{}

// Validate checks the field values on ListExternalModelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListExternalModelReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListExternalModelReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListExternalModelReplyMultiError, or nil if none found.
func (m *ListExternalModelReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListExternalModelReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListExternalModelReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListExternalModelReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListExternalModelReplyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListExternalModelReplyMultiError(errors)
	}

	return nil
}

// ListExternalModelReplyMultiError is an error wrapping multiple validation
// errors returned by ListExternalModelReply.ValidateAll() if the designated
// constraints aren't met.
type ListExternalModelReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListExternalModelReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListExternalModelReplyMultiError) AllErrors() []error { return m }

// ListExternalModelReplyValidationError is the validation error returned by
// ListExternalModelReply.Validate if the designated constraints aren't met.
type ListExternalModelReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListExternalModelReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListExternalModelReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListExternalModelReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListExternalModelReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListExternalModelReplyValidationError) ErrorName() string {
	return "ListExternalModelReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListExternalModelReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListExternalModelReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListExternalModelReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListExternalModelReplyValidationError{}

// Validate checks the field values on GetAiModelCallCountByTimeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAiModelCallCountByTimeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAiModelCallCountByTimeRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAiModelCallCountByTimeRequestMultiError, or nil if none found.
func (m *GetAiModelCallCountByTimeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAiModelCallCountByTimeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAiModelCallCountByTimeRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAiModelCallCountByTimeRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAiModelCallCountByTimeRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAiModelCallCountByTimeRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAiModelCallCountByTimeRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAiModelCallCountByTimeRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAiModelCallCountByTimeRequestMultiError(errors)
	}

	return nil
}

// GetAiModelCallCountByTimeRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAiModelCallCountByTimeRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAiModelCallCountByTimeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAiModelCallCountByTimeRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAiModelCallCountByTimeRequestMultiError) AllErrors() []error { return m }

// GetAiModelCallCountByTimeRequestValidationError is the validation error
// returned by GetAiModelCallCountByTimeRequest.Validate if the designated
// constraints aren't met.
type GetAiModelCallCountByTimeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAiModelCallCountByTimeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAiModelCallCountByTimeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAiModelCallCountByTimeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAiModelCallCountByTimeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAiModelCallCountByTimeRequestValidationError) ErrorName() string {
	return "GetAiModelCallCountByTimeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAiModelCallCountByTimeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAiModelCallCountByTimeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAiModelCallCountByTimeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAiModelCallCountByTimeRequestValidationError{}

// Validate checks the field values on GetAiModelCallCountByTimeReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAiModelCallCountByTimeReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAiModelCallCountByTimeReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAiModelCallCountByTimeReplyMultiError, or nil if none found.
func (m *GetAiModelCallCountByTimeReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAiModelCallCountByTimeReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAiModelCallCountByTimeReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAiModelCallCountByTimeReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAiModelCallCountByTimeReplyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAiModelCallCountByTimeReplyMultiError(errors)
	}

	return nil
}

// GetAiModelCallCountByTimeReplyMultiError is an error wrapping multiple
// validation errors returned by GetAiModelCallCountByTimeReply.ValidateAll()
// if the designated constraints aren't met.
type GetAiModelCallCountByTimeReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAiModelCallCountByTimeReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAiModelCallCountByTimeReplyMultiError) AllErrors() []error { return m }

// GetAiModelCallCountByTimeReplyValidationError is the validation error
// returned by GetAiModelCallCountByTimeReply.Validate if the designated
// constraints aren't met.
type GetAiModelCallCountByTimeReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAiModelCallCountByTimeReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAiModelCallCountByTimeReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAiModelCallCountByTimeReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAiModelCallCountByTimeReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAiModelCallCountByTimeReplyValidationError) ErrorName() string {
	return "GetAiModelCallCountByTimeReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAiModelCallCountByTimeReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAiModelCallCountByTimeReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAiModelCallCountByTimeReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAiModelCallCountByTimeReplyValidationError{}

// Validate checks the field values on GetAiModelCallCountByTimeReplyItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAiModelCallCountByTimeReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAiModelCallCountByTimeReplyItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAiModelCallCountByTimeReplyItemMultiError, or nil if none found.
func (m *GetAiModelCallCountByTimeReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAiModelCallCountByTimeReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAiModelCallCountByTimeReplyItemValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAiModelCallCountByTimeReplyItemValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAiModelCallCountByTimeReplyItemValidationError{
				field:  "Time",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Count

	if len(errors) > 0 {
		return GetAiModelCallCountByTimeReplyItemMultiError(errors)
	}

	return nil
}

// GetAiModelCallCountByTimeReplyItemMultiError is an error wrapping multiple
// validation errors returned by
// GetAiModelCallCountByTimeReplyItem.ValidateAll() if the designated
// constraints aren't met.
type GetAiModelCallCountByTimeReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAiModelCallCountByTimeReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAiModelCallCountByTimeReplyItemMultiError) AllErrors() []error { return m }

// GetAiModelCallCountByTimeReplyItemValidationError is the validation error
// returned by GetAiModelCallCountByTimeReplyItem.Validate if the designated
// constraints aren't met.
type GetAiModelCallCountByTimeReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAiModelCallCountByTimeReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAiModelCallCountByTimeReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAiModelCallCountByTimeReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAiModelCallCountByTimeReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAiModelCallCountByTimeReplyItemValidationError) ErrorName() string {
	return "GetAiModelCallCountByTimeReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e GetAiModelCallCountByTimeReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAiModelCallCountByTimeReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAiModelCallCountByTimeReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAiModelCallCountByTimeReplyItemValidationError{}

// Validate checks the field values on PageAiModelUsageByAgentIDRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PageAiModelUsageByAgentIDRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageAiModelUsageByAgentIDRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PageAiModelUsageByAgentIDRequestMultiError, or nil if none found.
func (m *PageAiModelUsageByAgentIDRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageAiModelUsageByAgentIDRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageNum() <= 0 {
		err := PageAiModelUsageByAgentIDRequestValidationError{
			field:  "PageNum",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val <= 0 || val > 100 {
		err := PageAiModelUsageByAgentIDRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range (0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AgentId

	if len(errors) > 0 {
		return PageAiModelUsageByAgentIDRequestMultiError(errors)
	}

	return nil
}

// PageAiModelUsageByAgentIDRequestMultiError is an error wrapping multiple
// validation errors returned by
// PageAiModelUsageByAgentIDRequest.ValidateAll() if the designated
// constraints aren't met.
type PageAiModelUsageByAgentIDRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageAiModelUsageByAgentIDRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageAiModelUsageByAgentIDRequestMultiError) AllErrors() []error { return m }

// PageAiModelUsageByAgentIDRequestValidationError is the validation error
// returned by PageAiModelUsageByAgentIDRequest.Validate if the designated
// constraints aren't met.
type PageAiModelUsageByAgentIDRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageAiModelUsageByAgentIDRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageAiModelUsageByAgentIDRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageAiModelUsageByAgentIDRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageAiModelUsageByAgentIDRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageAiModelUsageByAgentIDRequestValidationError) ErrorName() string {
	return "PageAiModelUsageByAgentIDRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PageAiModelUsageByAgentIDRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageAiModelUsageByAgentIDRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageAiModelUsageByAgentIDRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageAiModelUsageByAgentIDRequestValidationError{}

// Validate checks the field values on PageAiModelUsageByAgentIDReplyItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PageAiModelUsageByAgentIDReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageAiModelUsageByAgentIDReplyItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PageAiModelUsageByAgentIDReplyItemMultiError, or nil if none found.
func (m *PageAiModelUsageByAgentIDReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *PageAiModelUsageByAgentIDReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ModelName

	// no validation rules for ModelGatewayName

	// no validation rules for UserId

	// no validation rules for UserName

	// no validation rules for AgentId

	// no validation rules for AgentName

	// no validation rules for Question

	// no validation rules for Answer

	// no validation rules for PromptTokens

	// no validation rules for CompletionTokens

	// no validation rules for RequestStatus

	// no validation rules for ErrorCode

	// no validation rules for AgentAvatar

	// no validation rules for UserAvatar

	// no validation rules for ModelDetailAvatar

	if len(errors) > 0 {
		return PageAiModelUsageByAgentIDReplyItemMultiError(errors)
	}

	return nil
}

// PageAiModelUsageByAgentIDReplyItemMultiError is an error wrapping multiple
// validation errors returned by
// PageAiModelUsageByAgentIDReplyItem.ValidateAll() if the designated
// constraints aren't met.
type PageAiModelUsageByAgentIDReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageAiModelUsageByAgentIDReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageAiModelUsageByAgentIDReplyItemMultiError) AllErrors() []error { return m }

// PageAiModelUsageByAgentIDReplyItemValidationError is the validation error
// returned by PageAiModelUsageByAgentIDReplyItem.Validate if the designated
// constraints aren't met.
type PageAiModelUsageByAgentIDReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageAiModelUsageByAgentIDReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageAiModelUsageByAgentIDReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageAiModelUsageByAgentIDReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageAiModelUsageByAgentIDReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageAiModelUsageByAgentIDReplyItemValidationError) ErrorName() string {
	return "PageAiModelUsageByAgentIDReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e PageAiModelUsageByAgentIDReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageAiModelUsageByAgentIDReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageAiModelUsageByAgentIDReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageAiModelUsageByAgentIDReplyItemValidationError{}

// Validate checks the field values on PageAiModelUsageByAgentIDReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PageAiModelUsageByAgentIDReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageAiModelUsageByAgentIDReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// PageAiModelUsageByAgentIDReplyMultiError, or nil if none found.
func (m *PageAiModelUsageByAgentIDReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PageAiModelUsageByAgentIDReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageAiModelUsageByAgentIDReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageAiModelUsageByAgentIDReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageAiModelUsageByAgentIDReplyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return PageAiModelUsageByAgentIDReplyMultiError(errors)
	}

	return nil
}

// PageAiModelUsageByAgentIDReplyMultiError is an error wrapping multiple
// validation errors returned by PageAiModelUsageByAgentIDReply.ValidateAll()
// if the designated constraints aren't met.
type PageAiModelUsageByAgentIDReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageAiModelUsageByAgentIDReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageAiModelUsageByAgentIDReplyMultiError) AllErrors() []error { return m }

// PageAiModelUsageByAgentIDReplyValidationError is the validation error
// returned by PageAiModelUsageByAgentIDReply.Validate if the designated
// constraints aren't met.
type PageAiModelUsageByAgentIDReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageAiModelUsageByAgentIDReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageAiModelUsageByAgentIDReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageAiModelUsageByAgentIDReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageAiModelUsageByAgentIDReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageAiModelUsageByAgentIDReplyValidationError) ErrorName() string {
	return "PageAiModelUsageByAgentIDReplyValidationError"
}

// Error satisfies the builtin error interface
func (e PageAiModelUsageByAgentIDReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageAiModelUsageByAgentIDReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageAiModelUsageByAgentIDReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageAiModelUsageByAgentIDReplyValidationError{}

// Validate checks the field values on GetAiModelCallDetailByDayRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAiModelCallDetailByDayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAiModelCallDetailByDayRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAiModelCallDetailByDayRequestMultiError, or nil if none found.
func (m *GetAiModelCallDetailByDayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAiModelCallDetailByDayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAiModelCallDetailByDayRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAiModelCallDetailByDayRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAiModelCallDetailByDayRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAiModelCallDetailByDayRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAiModelCallDetailByDayRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAiModelCallDetailByDayRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAiModelCallDetailByDayRequestMultiError(errors)
	}

	return nil
}

// GetAiModelCallDetailByDayRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAiModelCallDetailByDayRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAiModelCallDetailByDayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAiModelCallDetailByDayRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAiModelCallDetailByDayRequestMultiError) AllErrors() []error { return m }

// GetAiModelCallDetailByDayRequestValidationError is the validation error
// returned by GetAiModelCallDetailByDayRequest.Validate if the designated
// constraints aren't met.
type GetAiModelCallDetailByDayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAiModelCallDetailByDayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAiModelCallDetailByDayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAiModelCallDetailByDayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAiModelCallDetailByDayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAiModelCallDetailByDayRequestValidationError) ErrorName() string {
	return "GetAiModelCallDetailByDayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAiModelCallDetailByDayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAiModelCallDetailByDayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAiModelCallDetailByDayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAiModelCallDetailByDayRequestValidationError{}

// Validate checks the field values on ModelCallDetail with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ModelCallDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModelCallDetail with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModelCallDetailMultiError, or nil if none found.
func (m *ModelCallDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *ModelCallDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ModelName

	// no validation rules for ApiCallCount

	// no validation rules for ApiErrCount

	// no validation rules for PromptTokens

	// no validation rules for CompletionTokens

	if len(errors) > 0 {
		return ModelCallDetailMultiError(errors)
	}

	return nil
}

// ModelCallDetailMultiError is an error wrapping multiple validation errors
// returned by ModelCallDetail.ValidateAll() if the designated constraints
// aren't met.
type ModelCallDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModelCallDetailMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModelCallDetailMultiError) AllErrors() []error { return m }

// ModelCallDetailValidationError is the validation error returned by
// ModelCallDetail.Validate if the designated constraints aren't met.
type ModelCallDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModelCallDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModelCallDetailValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModelCallDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModelCallDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModelCallDetailValidationError) ErrorName() string { return "ModelCallDetailValidationError" }

// Error satisfies the builtin error interface
func (e ModelCallDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModelCallDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModelCallDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModelCallDetailValidationError{}

// Validate checks the field values on GetAiModelCallDetailByDayReplyItem with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAiModelCallDetailByDayReplyItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAiModelCallDetailByDayReplyItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAiModelCallDetailByDayReplyItemMultiError, or nil if none found.
func (m *GetAiModelCallDetailByDayReplyItem) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAiModelCallDetailByDayReplyItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAiModelCallDetailByDayReplyItemValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAiModelCallDetailByDayReplyItemValidationError{
					field:  "Time",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAiModelCallDetailByDayReplyItemValidationError{
				field:  "Time",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetModelCallDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAiModelCallDetailByDayReplyItemValidationError{
						field:  fmt.Sprintf("ModelCallDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAiModelCallDetailByDayReplyItemValidationError{
						field:  fmt.Sprintf("ModelCallDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAiModelCallDetailByDayReplyItemValidationError{
					field:  fmt.Sprintf("ModelCallDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAiModelCallDetailByDayReplyItemMultiError(errors)
	}

	return nil
}

// GetAiModelCallDetailByDayReplyItemMultiError is an error wrapping multiple
// validation errors returned by
// GetAiModelCallDetailByDayReplyItem.ValidateAll() if the designated
// constraints aren't met.
type GetAiModelCallDetailByDayReplyItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAiModelCallDetailByDayReplyItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAiModelCallDetailByDayReplyItemMultiError) AllErrors() []error { return m }

// GetAiModelCallDetailByDayReplyItemValidationError is the validation error
// returned by GetAiModelCallDetailByDayReplyItem.Validate if the designated
// constraints aren't met.
type GetAiModelCallDetailByDayReplyItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAiModelCallDetailByDayReplyItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAiModelCallDetailByDayReplyItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAiModelCallDetailByDayReplyItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAiModelCallDetailByDayReplyItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAiModelCallDetailByDayReplyItemValidationError) ErrorName() string {
	return "GetAiModelCallDetailByDayReplyItemValidationError"
}

// Error satisfies the builtin error interface
func (e GetAiModelCallDetailByDayReplyItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAiModelCallDetailByDayReplyItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAiModelCallDetailByDayReplyItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAiModelCallDetailByDayReplyItemValidationError{}

// Validate checks the field values on GetAiModelCallDetailByDayReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAiModelCallDetailByDayReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAiModelCallDetailByDayReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAiModelCallDetailByDayReplyMultiError, or nil if none found.
func (m *GetAiModelCallDetailByDayReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAiModelCallDetailByDayReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAiModelCallDetailByDayReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAiModelCallDetailByDayReplyValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAiModelCallDetailByDayReplyValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAiModelCallDetailByDayReplyMultiError(errors)
	}

	return nil
}

// GetAiModelCallDetailByDayReplyMultiError is an error wrapping multiple
// validation errors returned by GetAiModelCallDetailByDayReply.ValidateAll()
// if the designated constraints aren't met.
type GetAiModelCallDetailByDayReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAiModelCallDetailByDayReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAiModelCallDetailByDayReplyMultiError) AllErrors() []error { return m }

// GetAiModelCallDetailByDayReplyValidationError is the validation error
// returned by GetAiModelCallDetailByDayReply.Validate if the designated
// constraints aren't met.
type GetAiModelCallDetailByDayReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAiModelCallDetailByDayReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAiModelCallDetailByDayReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAiModelCallDetailByDayReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAiModelCallDetailByDayReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAiModelCallDetailByDayReplyValidationError) ErrorName() string {
	return "GetAiModelCallDetailByDayReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetAiModelCallDetailByDayReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAiModelCallDetailByDayReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAiModelCallDetailByDayReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAiModelCallDetailByDayReplyValidationError{}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: aimodel/enum.proto

package aimodel

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModelType int32

const (
	ModelType_ModelTypeUnknown  ModelType = 0 // 未知模型类型
	ModelType_ModelTypeInternal ModelType = 1 // 内部模型
	ModelType_ModelTypeExternal ModelType = 2 // 外部模型
)

// Enum value maps for ModelType.
var (
	ModelType_name = map[int32]string{
		0: "ModelTypeUnknown",
		1: "ModelTypeInternal",
		2: "ModelTypeExternal",
	}
	ModelType_value = map[string]int32{
		"ModelTypeUnknown":  0,
		"ModelTypeInternal": 1,
		"ModelTypeExternal": 2,
	}
)

func (x ModelType) Enum() *ModelType {
	p := new(ModelType)
	*p = x
	return p
}

func (x ModelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelType) Descriptor() protoreflect.EnumDescriptor {
	return file_aimodel_enum_proto_enumTypes[0].Descriptor()
}

func (ModelType) Type() protoreflect.EnumType {
	return &file_aimodel_enum_proto_enumTypes[0]
}

func (x ModelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelType.Descriptor instead.
func (ModelType) EnumDescriptor() ([]byte, []int) {
	return file_aimodel_enum_proto_rawDescGZIP(), []int{0}
}

type Model int32

const (
	Model_ModelUnknown        Model = 0
	Model_ModelTencentYuanBao Model = 1 // 腾讯元宝
)

// Enum value maps for Model.
var (
	Model_name = map[int32]string{
		0: "ModelUnknown",
		1: "ModelTencentYuanBao",
	}
	Model_value = map[string]int32{
		"ModelUnknown":        0,
		"ModelTencentYuanBao": 1,
	}
)

func (x Model) Enum() *Model {
	p := new(Model)
	*p = x
	return p
}

func (x Model) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Model) Descriptor() protoreflect.EnumDescriptor {
	return file_aimodel_enum_proto_enumTypes[1].Descriptor()
}

func (Model) Type() protoreflect.EnumType {
	return &file_aimodel_enum_proto_enumTypes[1]
}

func (x Model) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Model.Descriptor instead.
func (Model) EnumDescriptor() ([]byte, []int) {
	return file_aimodel_enum_proto_rawDescGZIP(), []int{1}
}

type ModelThinkingEnableStatus int32

const (
	ModelThinkingEnableStatus_ModelThinkingEnableStatusDisable       ModelThinkingEnableStatus = 0 //  禁用思考
	ModelThinkingEnableStatus_ModelThinkingEnableStatusEnable        ModelThinkingEnableStatus = 1 // 启用思考
	ModelThinkingEnableStatus_ModelThinkingEnableStatusDynamicEnable ModelThinkingEnableStatus = 2 // 动态启用思考 可开可关
)

// Enum value maps for ModelThinkingEnableStatus.
var (
	ModelThinkingEnableStatus_name = map[int32]string{
		0: "ModelThinkingEnableStatusDisable",
		1: "ModelThinkingEnableStatusEnable",
		2: "ModelThinkingEnableStatusDynamicEnable",
	}
	ModelThinkingEnableStatus_value = map[string]int32{
		"ModelThinkingEnableStatusDisable":       0,
		"ModelThinkingEnableStatusEnable":        1,
		"ModelThinkingEnableStatusDynamicEnable": 2,
	}
)

func (x ModelThinkingEnableStatus) Enum() *ModelThinkingEnableStatus {
	p := new(ModelThinkingEnableStatus)
	*p = x
	return p
}

func (x ModelThinkingEnableStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModelThinkingEnableStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_aimodel_enum_proto_enumTypes[2].Descriptor()
}

func (ModelThinkingEnableStatus) Type() protoreflect.EnumType {
	return &file_aimodel_enum_proto_enumTypes[2]
}

func (x ModelThinkingEnableStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModelThinkingEnableStatus.Descriptor instead.
func (ModelThinkingEnableStatus) EnumDescriptor() ([]byte, []int) {
	return file_aimodel_enum_proto_rawDescGZIP(), []int{2}
}

var File_aimodel_enum_proto protoreflect.FileDescriptor

var file_aimodel_enum_proto_rawDesc = []byte{
	0x0a, 0x12, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2a, 0x4f, 0x0a, 0x09, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14,
	0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f,
	0x77, 0x6e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x10, 0x02, 0x2a, 0x32, 0x0a, 0x05, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x0c, 0x4d,
	0x6f, 0x64, 0x65, 0x6c, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x17, 0x0a,
	0x13, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x65, 0x6e, 0x63, 0x65, 0x6e, 0x74, 0x59, 0x75, 0x61,
	0x6e, 0x42, 0x61, 0x6f, 0x10, 0x01, 0x2a, 0x92, 0x01, 0x0a, 0x19, 0x4d, 0x6f, 0x64, 0x65, 0x6c,
	0x54, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x24, 0x0a, 0x20, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x68, 0x69,
	0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x44, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x54, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x01, 0x12,
	0x2a, 0x0a, 0x26, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x54, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x79, 0x6e, 0x61,
	0x6d, 0x69, 0x63, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x10, 0x02, 0x42, 0x4d, 0x0a, 0x0b, 0x61,
	0x70, 0x69, 0x2e, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x50, 0x01, 0x5a, 0x3c, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x2f, 0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65, 0x61, 0x6d, 0x2f,
	0x61, 0x69, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x69, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x3b, 0x61, 0x69, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_aimodel_enum_proto_rawDescOnce sync.Once
	file_aimodel_enum_proto_rawDescData = file_aimodel_enum_proto_rawDesc
)

func file_aimodel_enum_proto_rawDescGZIP() []byte {
	file_aimodel_enum_proto_rawDescOnce.Do(func() {
		file_aimodel_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_aimodel_enum_proto_rawDescData)
	})
	return file_aimodel_enum_proto_rawDescData
}

var file_aimodel_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_aimodel_enum_proto_goTypes = []any{
	(ModelType)(0),                 // 0: api.aimodel.ModelType
	(Model)(0),                     // 1: api.aimodel.Model
	(ModelThinkingEnableStatus)(0), // 2: api.aimodel.ModelThinkingEnableStatus
}
var file_aimodel_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_aimodel_enum_proto_init() }
func file_aimodel_enum_proto_init() {
	if File_aimodel_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_aimodel_enum_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_aimodel_enum_proto_goTypes,
		DependencyIndexes: file_aimodel_enum_proto_depIdxs,
		EnumInfos:         file_aimodel_enum_proto_enumTypes,
	}.Build()
	File_aimodel_enum_proto = out.File
	file_aimodel_enum_proto_rawDesc = nil
	file_aimodel_enum_proto_goTypes = nil
	file_aimodel_enum_proto_depIdxs = nil
}

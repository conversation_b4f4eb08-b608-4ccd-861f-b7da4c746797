syntax = "proto3";

package api.aimodel;

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/aimodel;aimodel";
option java_multiple_files = true;
option java_package = "api.aimodel";

enum ModelType {
  ModelTypeUnknown = 0; // 未知模型类型
  ModelTypeInternal = 1; // 内部模型
  ModelTypeExternal = 2; // 外部模型
}

enum Model {
  ModelUnknown = 0;
  ModelTencentYuanBao = 1; // 腾讯元宝
}

enum ModelThinkingEnableStatus {
  ModelThinkingEnableStatusDisable = 0; //  禁用思考
  ModelThinkingEnableStatusEnable = 1; // 启用思考
  ModelThinkingEnableStatusDynamicEnable = 2; // 动态启用思考 可开可关
}

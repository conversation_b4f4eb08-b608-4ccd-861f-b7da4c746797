syntax = "proto3";

package api.aimodel;

import "aimodel/enum.proto";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/aimodel;aimodel";
option java_multiple_files = true;
option java_package = "api.aimodel";

service Aimodel {
  // 创建 Aimodel
  rpc CreateAimodel(CreateAimodelRequest) returns (CreateAimodelReply) {
    option (google.api.http) = {
      post: "/aimodel/create"
      body: "*"
    };
  }
  // 更新 Aimodel
  rpc UpdateAimodel(UpdateAimodelRequest) returns (UpdateAimodelReply) {
    option (google.api.http) = {
      post: "/aimodel/update"
      body: "*"
    };
  }
  // 删除 Aimodel
  rpc DeleteAimodel(DeleteAimodelRequest) returns (DeleteAimodelReply) {
    option (google.api.http) = {
      post: "/aimodel/delete"
      body: "*"
    };
  }
  // 列表
  rpc ListAimodel(GetAimodelRequest) returns (GetAimodelReply) {
    option (google.api.http) = {
      post: "/aimodel/list"
      body: "*"
    };
  }

  // 外部模型列表
  rpc ListExternalModel(ListExternalModelRequest) returns (ListExternalModelReply) {
    option (google.api.http) = {
      post: "/aimodel/listExternalModel"
      body: "*"
    };
  }

  // 按天获取模型调用次数
  rpc GetAiModelCallCountTotalByTime(GetAiModelCallCountByTimeRequest) returns (GetAiModelCallCountByTimeReply) {
    option (google.api.http) = {
      post: "/aimodel/getAiModelCallCount"
      body: "*"
    };
  }

  // 按智能体分页获取模型用量详情
  rpc PageAiModelUsageByAgentID(PageAiModelUsageByAgentIDRequest) returns (PageAiModelUsageByAgentIDReply) {
    option (google.api.http) = {
      post: "/aimodel/pageAiModelUsageByAgentID"
      body: "*"
    };
  }

  // 获取模型请求、tokens消耗详情（按天）
  rpc GetAiModelCallDetailByDay(GetAiModelCallDetailByDayRequest) returns (GetAiModelCallDetailByDayReply) {
    option (google.api.http) = {
      post: "/aimodel/getAiModelCallDetailByDay"
      body: "*"
    };
  }
}

message AiModel {
  int64 id = 1;
  string modelName = 2;
  // 1. 腾讯元宝
  int64 model = 3;
  string apiKey = 4;
  google.protobuf.Timestamp createdAt = 5;
  string avatarUrl = 6;
  bool canInternetSearch = 7;
  double balance = 8; // 余额
  // 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
  ModelThinkingEnableStatus thinkingEnableStatus = 9;
  string modelDetailName = 10; // 模型详情名称
  // 后台地址
  string backgroundUrl = 11;
}

message AiModelDetail {
  int64 id = 1;
  string name = 2;
  string model_name = 3;
  string url = 4;
  bool canInternetSearch = 5;
  string avatarUrl = 6;
}

// =========================== create
message CreateAimodelRequest {
  string modelName = 1;
  // 查询外部模型列表的id
  int64 model = 2;
  string apiKey = 3;
}
message CreateAimodelItem {}
message CreateAimodelReply {}

// =========================== update
message UpdateAimodelRequest {
  int64 id = 1;
  string modelName = 2;
  // 1. 腾讯元宝
  int64 model = 3;
  string apiKey = 4;
}
message UpdateAimodelReply {}

// =========================== delete
message DeleteAimodelRequest {
  int64 id = 1;
}
message DeleteAimodelReply {}

// =========================== get
message GetAimodelRequest {
  string modelName = 1;
}
message GetAimodelReply {
  repeated AiModel items = 1;
}

message ListExternalModelRequest {}

message ListExternalModelReply {
  repeated AiModelDetail items = 1;
}

message GetAiModelCallCountByTimeRequest {
  google.protobuf.Timestamp startTime = 1;
  google.protobuf.Timestamp endTime = 2;
}

message GetAiModelCallCountByTimeReply {
  repeated GetAiModelCallCountByTimeReplyItem items = 1;
}

message GetAiModelCallCountByTimeReplyItem {
  google.protobuf.Timestamp time = 1;
  int64 count = 2;
}

message PageAiModelUsageByAgentIDRequest {
  int64 pageNum = 1 [(validate.rules).int64 = {gt: 0}];
  int64 pageSize = 2 [(validate.rules).int64 = {
    gt: 0
    lte: 100
  }];
  int64 agentId = 3;
}

message PageAiModelUsageByAgentIDReplyItem {
  int64 id = 1;
  string modelName = 2;
  string modelGatewayName = 3;
  int64 userId = 4;
  string userName = 5;
  int64 agentId = 6;
  string agentName = 7;
  string question = 8;
  string answer = 9;
  int64 promptTokens = 10;
  int64 completionTokens = 11;
  // 0: 未处理, 1: 成功, 2: 失败
  int64 requestStatus = 12;
  string errorCode = 13;
  // 智能体头像
  string agentAvatar = 14;
  // 用户头像
  string userAvatar = 15;
  // 模型头像
  string modelDetailAvatar = 16;
}

message PageAiModelUsageByAgentIDReply {
  repeated PageAiModelUsageByAgentIDReplyItem items = 1;
  int64 total = 2;
}

message GetAiModelCallDetailByDayRequest {
  google.protobuf.Timestamp startTime = 1;
  google.protobuf.Timestamp endTime = 2;
}

message ModelCallDetail {
  string modelName = 1;
  int64 apiCallCount = 2; // 调用次数
  int64 apiErrCount = 3; // 错误次数
  int64 promptTokens = 4; // prompt tokens
  int64 completionTokens = 5; // completion tokens
}

message GetAiModelCallDetailByDayReplyItem {
  google.protobuf.Timestamp time = 1;
  repeated ModelCallDetail modelCallDetails = 2; // 模型调用详情
}

message GetAiModelCallDetailByDayReply {
  repeated GetAiModelCallDetailByDayReplyItem items = 1; // 按天的模型调用详情
}

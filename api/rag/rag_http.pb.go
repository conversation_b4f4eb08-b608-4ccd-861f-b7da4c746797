// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             (unknown)
// source: rag/rag.proto

package rag

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRagAgreeChatItem = "/api.rag.Rag/AgreeChatItem"
const OperationRagChatDetail = "/api.rag.Rag/ChatDetail"
const OperationRagCreateChat = "/api.rag.Rag/CreateChat"
const OperationRagDeleteChat = "/api.rag.Rag/DeleteChat"
const OperationRagFullTextSearch = "/api.rag.Rag/FullTextSearch"
const OperationRagPageChat = "/api.rag.Rag/PageChat"
const OperationRagRenameChat = "/api.rag.Rag/RenameChat"
const OperationRagUpdateChatItemSuggestQuestions = "/api.rag.Rag/UpdateChatItemSuggestQuestions"

type RagHTTPServer interface {
	// AgreeChatItem 赞/踩回答
	AgreeChatItem(context.Context, *AgreeChatItemRequest) (*AgreeChatItemReply, error)
	// ChatDetail 聊天详情
	ChatDetail(context.Context, *ChatDetailRequest) (*ChatDetailReply, error)
	// CreateChat 创建聊天
	CreateChat(context.Context, *CreateChatRequest) (*CreateChatReply, error)
	// DeleteChat 删除聊天
	DeleteChat(context.Context, *DeleteChatRequest) (*DeleteChatReply, error)
	// FullTextSearch 全文搜索
	FullTextSearch(context.Context, *FullTextSearchRequest) (*FullTextSearchReply, error)
	// PageChat 分页聊天
	PageChat(context.Context, *PageChatRequest) (*PageChatReply, error)
	// RenameChat 重命名聊天
	RenameChat(context.Context, *RenameChatRequest) (*RenameChatReply, error)
	// UpdateChatItemSuggestQuestions 更新对话推荐问题列表
	UpdateChatItemSuggestQuestions(context.Context, *UpdateChatItemSuggestQuestionsRequest) (*UpdateChatItemSuggestQuestionsReply, error)
}

func RegisterRagHTTPServer(s *http.Server, srv RagHTTPServer) {
	r := s.Route("/")
	r.POST("/rag/rename", _Rag_RenameChat0_HTTP_Handler(srv))
	r.POST("/rag/create", _Rag_CreateChat0_HTTP_Handler(srv))
	r.POST("/rag/delete", _Rag_DeleteChat0_HTTP_Handler(srv))
	r.GET("/rag/detail", _Rag_ChatDetail0_HTTP_Handler(srv))
	r.GET("/rag/page", _Rag_PageChat0_HTTP_Handler(srv))
	r.POST("/rag/search", _Rag_FullTextSearch0_HTTP_Handler(srv))
	r.POST("/rag/agree", _Rag_AgreeChatItem0_HTTP_Handler(srv))
	r.POST("/rag/update/suggest/questions", _Rag_UpdateChatItemSuggestQuestions0_HTTP_Handler(srv))
}

func _Rag_RenameChat0_HTTP_Handler(srv RagHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RenameChatRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRagRenameChat)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RenameChat(ctx, req.(*RenameChatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RenameChatReply)
		return ctx.Result(200, reply)
	}
}

func _Rag_CreateChat0_HTTP_Handler(srv RagHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CreateChatRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRagCreateChat)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.CreateChat(ctx, req.(*CreateChatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*CreateChatReply)
		return ctx.Result(200, reply)
	}
}

func _Rag_DeleteChat0_HTTP_Handler(srv RagHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DeleteChatRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRagDeleteChat)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteChat(ctx, req.(*DeleteChatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteChatReply)
		return ctx.Result(200, reply)
	}
}

func _Rag_ChatDetail0_HTTP_Handler(srv RagHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ChatDetailRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRagChatDetail)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ChatDetail(ctx, req.(*ChatDetailRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ChatDetailReply)
		return ctx.Result(200, reply)
	}
}

func _Rag_PageChat0_HTTP_Handler(srv RagHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in PageChatRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRagPageChat)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.PageChat(ctx, req.(*PageChatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*PageChatReply)
		return ctx.Result(200, reply)
	}
}

func _Rag_FullTextSearch0_HTTP_Handler(srv RagHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in FullTextSearchRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRagFullTextSearch)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.FullTextSearch(ctx, req.(*FullTextSearchRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*FullTextSearchReply)
		return ctx.Result(200, reply)
	}
}

func _Rag_AgreeChatItem0_HTTP_Handler(srv RagHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AgreeChatItemRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRagAgreeChatItem)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AgreeChatItem(ctx, req.(*AgreeChatItemRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AgreeChatItemReply)
		return ctx.Result(200, reply)
	}
}

func _Rag_UpdateChatItemSuggestQuestions0_HTTP_Handler(srv RagHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in UpdateChatItemSuggestQuestionsRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRagUpdateChatItemSuggestQuestions)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.UpdateChatItemSuggestQuestions(ctx, req.(*UpdateChatItemSuggestQuestionsRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*UpdateChatItemSuggestQuestionsReply)
		return ctx.Result(200, reply)
	}
}

type RagHTTPClient interface {
	AgreeChatItem(ctx context.Context, req *AgreeChatItemRequest, opts ...http.CallOption) (rsp *AgreeChatItemReply, err error)
	ChatDetail(ctx context.Context, req *ChatDetailRequest, opts ...http.CallOption) (rsp *ChatDetailReply, err error)
	CreateChat(ctx context.Context, req *CreateChatRequest, opts ...http.CallOption) (rsp *CreateChatReply, err error)
	DeleteChat(ctx context.Context, req *DeleteChatRequest, opts ...http.CallOption) (rsp *DeleteChatReply, err error)
	FullTextSearch(ctx context.Context, req *FullTextSearchRequest, opts ...http.CallOption) (rsp *FullTextSearchReply, err error)
	PageChat(ctx context.Context, req *PageChatRequest, opts ...http.CallOption) (rsp *PageChatReply, err error)
	RenameChat(ctx context.Context, req *RenameChatRequest, opts ...http.CallOption) (rsp *RenameChatReply, err error)
	UpdateChatItemSuggestQuestions(ctx context.Context, req *UpdateChatItemSuggestQuestionsRequest, opts ...http.CallOption) (rsp *UpdateChatItemSuggestQuestionsReply, err error)
}

type RagHTTPClientImpl struct {
	cc *http.Client
}

func NewRagHTTPClient(client *http.Client) RagHTTPClient {
	return &RagHTTPClientImpl{client}
}

func (c *RagHTTPClientImpl) AgreeChatItem(ctx context.Context, in *AgreeChatItemRequest, opts ...http.CallOption) (*AgreeChatItemReply, error) {
	var out AgreeChatItemReply
	pattern := "/rag/agree"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRagAgreeChatItem))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RagHTTPClientImpl) ChatDetail(ctx context.Context, in *ChatDetailRequest, opts ...http.CallOption) (*ChatDetailReply, error) {
	var out ChatDetailReply
	pattern := "/rag/detail"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRagChatDetail))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RagHTTPClientImpl) CreateChat(ctx context.Context, in *CreateChatRequest, opts ...http.CallOption) (*CreateChatReply, error) {
	var out CreateChatReply
	pattern := "/rag/create"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRagCreateChat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RagHTTPClientImpl) DeleteChat(ctx context.Context, in *DeleteChatRequest, opts ...http.CallOption) (*DeleteChatReply, error) {
	var out DeleteChatReply
	pattern := "/rag/delete"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRagDeleteChat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RagHTTPClientImpl) FullTextSearch(ctx context.Context, in *FullTextSearchRequest, opts ...http.CallOption) (*FullTextSearchReply, error) {
	var out FullTextSearchReply
	pattern := "/rag/search"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRagFullTextSearch))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RagHTTPClientImpl) PageChat(ctx context.Context, in *PageChatRequest, opts ...http.CallOption) (*PageChatReply, error) {
	var out PageChatReply
	pattern := "/rag/page"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRagPageChat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RagHTTPClientImpl) RenameChat(ctx context.Context, in *RenameChatRequest, opts ...http.CallOption) (*RenameChatReply, error) {
	var out RenameChatReply
	pattern := "/rag/rename"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRagRenameChat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RagHTTPClientImpl) UpdateChatItemSuggestQuestions(ctx context.Context, in *UpdateChatItemSuggestQuestionsRequest, opts ...http.CallOption) (*UpdateChatItemSuggestQuestionsReply, error) {
	var out UpdateChatItemSuggestQuestionsReply
	pattern := "/rag/update/suggest/questions"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRagUpdateChatItemSuggestQuestions))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

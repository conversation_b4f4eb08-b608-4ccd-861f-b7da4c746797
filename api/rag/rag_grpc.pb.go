// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: rag/rag.proto

package rag

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Rag_RenameChat_FullMethodName                     = "/api.rag.Rag/RenameChat"
	Rag_CreateChat_FullMethodName                     = "/api.rag.Rag/CreateChat"
	Rag_DeleteChat_FullMethodName                     = "/api.rag.Rag/DeleteChat"
	Rag_ChatDetail_FullMethodName                     = "/api.rag.Rag/ChatDetail"
	Rag_PageChat_FullMethodName                       = "/api.rag.Rag/PageChat"
	Rag_FullTextSearch_FullMethodName                 = "/api.rag.Rag/FullTextSearch"
	Rag_AgreeChatItem_FullMethodName                  = "/api.rag.Rag/AgreeChatItem"
	Rag_UpdateChatItemSuggestQuestions_FullMethodName = "/api.rag.Rag/UpdateChatItemSuggestQuestions"
	Rag_ChatCount_FullMethodName                      = "/api.rag.Rag/ChatCount"
)

// RagClient is the client API for Rag service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RagClient interface {
	// 重命名聊天
	RenameChat(ctx context.Context, in *RenameChatRequest, opts ...grpc.CallOption) (*RenameChatReply, error)
	// 创建聊天
	CreateChat(ctx context.Context, in *CreateChatRequest, opts ...grpc.CallOption) (*CreateChatReply, error)
	// 删除聊天
	DeleteChat(ctx context.Context, in *DeleteChatRequest, opts ...grpc.CallOption) (*DeleteChatReply, error)
	// 聊天详情
	ChatDetail(ctx context.Context, in *ChatDetailRequest, opts ...grpc.CallOption) (*ChatDetailReply, error)
	// 分页聊天
	PageChat(ctx context.Context, in *PageChatRequest, opts ...grpc.CallOption) (*PageChatReply, error)
	// 全文搜索
	FullTextSearch(ctx context.Context, in *FullTextSearchRequest, opts ...grpc.CallOption) (*FullTextSearchReply, error)
	// 赞/踩回答
	AgreeChatItem(ctx context.Context, in *AgreeChatItemRequest, opts ...grpc.CallOption) (*AgreeChatItemReply, error)
	// 更新对话推荐问题列表
	UpdateChatItemSuggestQuestions(ctx context.Context, in *UpdateChatItemSuggestQuestionsRequest, opts ...grpc.CallOption) (*UpdateChatItemSuggestQuestionsReply, error)
	// 对话数查询
	ChatCount(ctx context.Context, in *ChatCountRequest, opts ...grpc.CallOption) (*ChatCountReply, error)
}

type ragClient struct {
	cc grpc.ClientConnInterface
}

func NewRagClient(cc grpc.ClientConnInterface) RagClient {
	return &ragClient{cc}
}

func (c *ragClient) RenameChat(ctx context.Context, in *RenameChatRequest, opts ...grpc.CallOption) (*RenameChatReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RenameChatReply)
	err := c.cc.Invoke(ctx, Rag_RenameChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ragClient) CreateChat(ctx context.Context, in *CreateChatRequest, opts ...grpc.CallOption) (*CreateChatReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateChatReply)
	err := c.cc.Invoke(ctx, Rag_CreateChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ragClient) DeleteChat(ctx context.Context, in *DeleteChatRequest, opts ...grpc.CallOption) (*DeleteChatReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteChatReply)
	err := c.cc.Invoke(ctx, Rag_DeleteChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ragClient) ChatDetail(ctx context.Context, in *ChatDetailRequest, opts ...grpc.CallOption) (*ChatDetailReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChatDetailReply)
	err := c.cc.Invoke(ctx, Rag_ChatDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ragClient) PageChat(ctx context.Context, in *PageChatRequest, opts ...grpc.CallOption) (*PageChatReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PageChatReply)
	err := c.cc.Invoke(ctx, Rag_PageChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ragClient) FullTextSearch(ctx context.Context, in *FullTextSearchRequest, opts ...grpc.CallOption) (*FullTextSearchReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FullTextSearchReply)
	err := c.cc.Invoke(ctx, Rag_FullTextSearch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ragClient) AgreeChatItem(ctx context.Context, in *AgreeChatItemRequest, opts ...grpc.CallOption) (*AgreeChatItemReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AgreeChatItemReply)
	err := c.cc.Invoke(ctx, Rag_AgreeChatItem_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ragClient) UpdateChatItemSuggestQuestions(ctx context.Context, in *UpdateChatItemSuggestQuestionsRequest, opts ...grpc.CallOption) (*UpdateChatItemSuggestQuestionsReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateChatItemSuggestQuestionsReply)
	err := c.cc.Invoke(ctx, Rag_UpdateChatItemSuggestQuestions_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ragClient) ChatCount(ctx context.Context, in *ChatCountRequest, opts ...grpc.CallOption) (*ChatCountReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChatCountReply)
	err := c.cc.Invoke(ctx, Rag_ChatCount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RagServer is the server API for Rag service.
// All implementations must embed UnimplementedRagServer
// for forward compatibility.
type RagServer interface {
	// 重命名聊天
	RenameChat(context.Context, *RenameChatRequest) (*RenameChatReply, error)
	// 创建聊天
	CreateChat(context.Context, *CreateChatRequest) (*CreateChatReply, error)
	// 删除聊天
	DeleteChat(context.Context, *DeleteChatRequest) (*DeleteChatReply, error)
	// 聊天详情
	ChatDetail(context.Context, *ChatDetailRequest) (*ChatDetailReply, error)
	// 分页聊天
	PageChat(context.Context, *PageChatRequest) (*PageChatReply, error)
	// 全文搜索
	FullTextSearch(context.Context, *FullTextSearchRequest) (*FullTextSearchReply, error)
	// 赞/踩回答
	AgreeChatItem(context.Context, *AgreeChatItemRequest) (*AgreeChatItemReply, error)
	// 更新对话推荐问题列表
	UpdateChatItemSuggestQuestions(context.Context, *UpdateChatItemSuggestQuestionsRequest) (*UpdateChatItemSuggestQuestionsReply, error)
	// 对话数查询
	ChatCount(context.Context, *ChatCountRequest) (*ChatCountReply, error)
	mustEmbedUnimplementedRagServer()
}

// UnimplementedRagServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRagServer struct{}

func (UnimplementedRagServer) RenameChat(context.Context, *RenameChatRequest) (*RenameChatReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenameChat not implemented")
}
func (UnimplementedRagServer) CreateChat(context.Context, *CreateChatRequest) (*CreateChatReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateChat not implemented")
}
func (UnimplementedRagServer) DeleteChat(context.Context, *DeleteChatRequest) (*DeleteChatReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteChat not implemented")
}
func (UnimplementedRagServer) ChatDetail(context.Context, *ChatDetailRequest) (*ChatDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChatDetail not implemented")
}
func (UnimplementedRagServer) PageChat(context.Context, *PageChatRequest) (*PageChatReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PageChat not implemented")
}
func (UnimplementedRagServer) FullTextSearch(context.Context, *FullTextSearchRequest) (*FullTextSearchReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FullTextSearch not implemented")
}
func (UnimplementedRagServer) AgreeChatItem(context.Context, *AgreeChatItemRequest) (*AgreeChatItemReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AgreeChatItem not implemented")
}
func (UnimplementedRagServer) UpdateChatItemSuggestQuestions(context.Context, *UpdateChatItemSuggestQuestionsRequest) (*UpdateChatItemSuggestQuestionsReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateChatItemSuggestQuestions not implemented")
}
func (UnimplementedRagServer) ChatCount(context.Context, *ChatCountRequest) (*ChatCountReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChatCount not implemented")
}
func (UnimplementedRagServer) mustEmbedUnimplementedRagServer() {}
func (UnimplementedRagServer) testEmbeddedByValue()             {}

// UnsafeRagServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RagServer will
// result in compilation errors.
type UnsafeRagServer interface {
	mustEmbedUnimplementedRagServer()
}

func RegisterRagServer(s grpc.ServiceRegistrar, srv RagServer) {
	// If the following call pancis, it indicates UnimplementedRagServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Rag_ServiceDesc, srv)
}

func _Rag_RenameChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenameChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RagServer).RenameChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rag_RenameChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RagServer).RenameChat(ctx, req.(*RenameChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rag_CreateChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RagServer).CreateChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rag_CreateChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RagServer).CreateChat(ctx, req.(*CreateChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rag_DeleteChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RagServer).DeleteChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rag_DeleteChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RagServer).DeleteChat(ctx, req.(*DeleteChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rag_ChatDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatDetailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RagServer).ChatDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rag_ChatDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RagServer).ChatDetail(ctx, req.(*ChatDetailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rag_PageChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PageChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RagServer).PageChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rag_PageChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RagServer).PageChat(ctx, req.(*PageChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rag_FullTextSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FullTextSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RagServer).FullTextSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rag_FullTextSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RagServer).FullTextSearch(ctx, req.(*FullTextSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rag_AgreeChatItem_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AgreeChatItemRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RagServer).AgreeChatItem(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rag_AgreeChatItem_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RagServer).AgreeChatItem(ctx, req.(*AgreeChatItemRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rag_UpdateChatItemSuggestQuestions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateChatItemSuggestQuestionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RagServer).UpdateChatItemSuggestQuestions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rag_UpdateChatItemSuggestQuestions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RagServer).UpdateChatItemSuggestQuestions(ctx, req.(*UpdateChatItemSuggestQuestionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Rag_ChatCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatCountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RagServer).ChatCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Rag_ChatCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RagServer).ChatCount(ctx, req.(*ChatCountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Rag_ServiceDesc is the grpc.ServiceDesc for Rag service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Rag_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.rag.Rag",
	HandlerType: (*RagServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RenameChat",
			Handler:    _Rag_RenameChat_Handler,
		},
		{
			MethodName: "CreateChat",
			Handler:    _Rag_CreateChat_Handler,
		},
		{
			MethodName: "DeleteChat",
			Handler:    _Rag_DeleteChat_Handler,
		},
		{
			MethodName: "ChatDetail",
			Handler:    _Rag_ChatDetail_Handler,
		},
		{
			MethodName: "PageChat",
			Handler:    _Rag_PageChat_Handler,
		},
		{
			MethodName: "FullTextSearch",
			Handler:    _Rag_FullTextSearch_Handler,
		},
		{
			MethodName: "AgreeChatItem",
			Handler:    _Rag_AgreeChatItem_Handler,
		},
		{
			MethodName: "UpdateChatItemSuggestQuestions",
			Handler:    _Rag_UpdateChatItemSuggestQuestions_Handler,
		},
		{
			MethodName: "ChatCount",
			Handler:    _Rag_ChatCount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "rag/rag.proto",
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: rag/rag.proto

package rag

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Chat struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChatID  int64  `protobuf:"varint,1,opt,name=chatID,proto3" json:"chatID,omitempty"`
	Name    string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	AgentID int64  `protobuf:"varint,3,opt,name=agentID,proto3" json:"agentID,omitempty"`
	// 1:普通RAG 2:智能体
	ChatType          int64                  `protobuf:"varint,4,opt,name=chatType,proto3" json:"chatType,omitempty"`
	AgentName         string                 `protobuf:"bytes,5,opt,name=agentName,proto3" json:"agentName,omitempty"`
	AgentAvatar       string                 `protobuf:"bytes,6,opt,name=agentAvatar,proto3" json:"agentAvatar,omitempty"`
	KnowledgeBaseType int64                  `protobuf:"varint,7,opt,name=knowledgeBaseType,proto3" json:"knowledgeBaseType,omitempty"`
	ModelType         int64                  `protobuf:"varint,8,opt,name=modelType,proto3" json:"modelType,omitempty"`
	CanInternetSearch bool                   `protobuf:"varint,9,opt,name=canInternetSearch,proto3" json:"canInternetSearch,omitempty"`
	AgentType         int64                  `protobuf:"varint,10,opt,name=agentType,proto3" json:"agentType,omitempty"`
	FallbackMsg       string                 `protobuf:"bytes,11,opt,name=fallbackMsg,proto3" json:"fallbackMsg,omitempty"`
	CreatedAt         *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	// 深度思考开启状态 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
	ThinkingEnableStatus int64 `protobuf:"varint,13,opt,name=thinkingEnableStatus,proto3" json:"thinkingEnableStatus,omitempty"`
	// 是否开启深度思考
	Thinking bool `protobuf:"varint,14,opt,name=thinking,proto3" json:"thinking,omitempty"`
}

func (x *Chat) Reset() {
	*x = Chat{}
	mi := &file_rag_rag_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Chat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chat) ProtoMessage() {}

func (x *Chat) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chat.ProtoReflect.Descriptor instead.
func (*Chat) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{0}
}

func (x *Chat) GetChatID() int64 {
	if x != nil {
		return x.ChatID
	}
	return 0
}

func (x *Chat) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Chat) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *Chat) GetChatType() int64 {
	if x != nil {
		return x.ChatType
	}
	return 0
}

func (x *Chat) GetAgentName() string {
	if x != nil {
		return x.AgentName
	}
	return ""
}

func (x *Chat) GetAgentAvatar() string {
	if x != nil {
		return x.AgentAvatar
	}
	return ""
}

func (x *Chat) GetKnowledgeBaseType() int64 {
	if x != nil {
		return x.KnowledgeBaseType
	}
	return 0
}

func (x *Chat) GetModelType() int64 {
	if x != nil {
		return x.ModelType
	}
	return 0
}

func (x *Chat) GetCanInternetSearch() bool {
	if x != nil {
		return x.CanInternetSearch
	}
	return false
}

func (x *Chat) GetAgentType() int64 {
	if x != nil {
		return x.AgentType
	}
	return 0
}

func (x *Chat) GetFallbackMsg() string {
	if x != nil {
		return x.FallbackMsg
	}
	return ""
}

func (x *Chat) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Chat) GetThinkingEnableStatus() int64 {
	if x != nil {
		return x.ThinkingEnableStatus
	}
	return 0
}

func (x *Chat) GetThinking() bool {
	if x != nil {
		return x.Thinking
	}
	return false
}

type ChatItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64       `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ChatID      int64       `protobuf:"varint,2,opt,name=chatID,proto3" json:"chatID,omitempty"`
	ObjectID    int64       `protobuf:"varint,3,opt,name=objectID,proto3" json:"objectID,omitempty"`
	ObjectType  int64       `protobuf:"varint,4,opt,name=objectType,proto3" json:"objectType,omitempty"`
	Message     string      `protobuf:"bytes,5,opt,name=message,proto3" json:"message,omitempty"`
	Refs        []*FileInfo `protobuf:"bytes,6,rep,name=refs,proto3" json:"refs,omitempty"`
	RoundID     int64       `protobuf:"varint,7,opt,name=roundID,proto3" json:"roundID,omitempty"`
	Reason      string      `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason,omitempty"`
	AgreeStatus int64       `protobuf:"varint,9,opt,name=agreeStatus,proto3" json:"agreeStatus,omitempty"` // 1:赞同 2:点踩
	// 1:阻断  2.警告
	HitAction       int64  `protobuf:"varint,10,opt,name=hitAction,proto3" json:"hitAction,omitempty"`
	HitResponse     string `protobuf:"bytes,11,opt,name=hitResponse,proto3" json:"hitResponse,omitempty"`
	HitContinueSend bool   `protobuf:"varint,12,opt,name=hitContinueSend,proto3" json:"hitContinueSend,omitempty"`
	// 是否是网络搜索
	IsInternetSearch bool     `protobuf:"varint,13,opt,name=isInternetSearch,proto3" json:"isInternetSearch,omitempty"`
	SuggestQuestions []string `protobuf:"bytes,14,rep,name=suggestQuestions,proto3" json:"suggestQuestions,omitempty"`
}

func (x *ChatItem) Reset() {
	*x = ChatItem{}
	mi := &file_rag_rag_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatItem) ProtoMessage() {}

func (x *ChatItem) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatItem.ProtoReflect.Descriptor instead.
func (*ChatItem) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{1}
}

func (x *ChatItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ChatItem) GetChatID() int64 {
	if x != nil {
		return x.ChatID
	}
	return 0
}

func (x *ChatItem) GetObjectID() int64 {
	if x != nil {
		return x.ObjectID
	}
	return 0
}

func (x *ChatItem) GetObjectType() int64 {
	if x != nil {
		return x.ObjectType
	}
	return 0
}

func (x *ChatItem) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChatItem) GetRefs() []*FileInfo {
	if x != nil {
		return x.Refs
	}
	return nil
}

func (x *ChatItem) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *ChatItem) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ChatItem) GetAgreeStatus() int64 {
	if x != nil {
		return x.AgreeStatus
	}
	return 0
}

func (x *ChatItem) GetHitAction() int64 {
	if x != nil {
		return x.HitAction
	}
	return 0
}

func (x *ChatItem) GetHitResponse() string {
	if x != nil {
		return x.HitResponse
	}
	return ""
}

func (x *ChatItem) GetHitContinueSend() bool {
	if x != nil {
		return x.HitContinueSend
	}
	return false
}

func (x *ChatItem) GetIsInternetSearch() bool {
	if x != nil {
		return x.IsInternetSearch
	}
	return false
}

func (x *ChatItem) GetSuggestQuestions() []string {
	if x != nil {
		return x.SuggestQuestions
	}
	return nil
}

// ask
type AskRagRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Question string                    `protobuf:"bytes,1,opt,name=question,proto3" json:"question,omitempty"`
	ChatID   int64                     `protobuf:"varint,2,opt,name=chatID,proto3" json:"chatID,omitempty"`
	RoundID  int64                     `protobuf:"varint,3,opt,name=roundID,proto3" json:"roundID,omitempty"`
	Files    []*AskRagRequest_FileInfo `protobuf:"bytes,4,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *AskRagRequest) Reset() {
	*x = AskRagRequest{}
	mi := &file_rag_rag_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskRagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskRagRequest) ProtoMessage() {}

func (x *AskRagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskRagRequest.ProtoReflect.Descriptor instead.
func (*AskRagRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{2}
}

func (x *AskRagRequest) GetQuestion() string {
	if x != nil {
		return x.Question
	}
	return ""
}

func (x *AskRagRequest) GetChatID() int64 {
	if x != nil {
		return x.ChatID
	}
	return 0
}

func (x *AskRagRequest) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *AskRagRequest) GetFiles() []*AskRagRequest_FileInfo {
	if x != nil {
		return x.Files
	}
	return nil
}

type AskRagReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Answer string                  `protobuf:"bytes,1,opt,name=answer,proto3" json:"answer,omitempty"`
	Files  []*AskRagReply_FileInfo `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`
	// 0:回答中,1:回答完成
	Status  int64 `protobuf:"varint,3,opt,name=status,proto3" json:"status,omitempty"`
	RoundID int64 `protobuf:"varint,4,opt,name=roundID,proto3" json:"roundID,omitempty"`
	Type    int64 `protobuf:"varint,5,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *AskRagReply) Reset() {
	*x = AskRagReply{}
	mi := &file_rag_rag_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskRagReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskRagReply) ProtoMessage() {}

func (x *AskRagReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskRagReply.ProtoReflect.Descriptor instead.
func (*AskRagReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{3}
}

func (x *AskRagReply) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

func (x *AskRagReply) GetFiles() []*AskRagReply_FileInfo {
	if x != nil {
		return x.Files
	}
	return nil
}

func (x *AskRagReply) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AskRagReply) GetRoundID() int64 {
	if x != nil {
		return x.RoundID
	}
	return 0
}

func (x *AskRagReply) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

// create chat
type CreateChatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name    string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	AgentID int64  `protobuf:"varint,2,opt,name=agentID,proto3" json:"agentID,omitempty"`
	// 1:普通RAG 2:智能体
	ChatType int64 `protobuf:"varint,3,opt,name=chatType,proto3" json:"chatType,omitempty"`
}

func (x *CreateChatRequest) Reset() {
	*x = CreateChatRequest{}
	mi := &file_rag_rag_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatRequest) ProtoMessage() {}

func (x *CreateChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatRequest.ProtoReflect.Descriptor instead.
func (*CreateChatRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{4}
}

func (x *CreateChatRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateChatRequest) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

func (x *CreateChatRequest) GetChatType() int64 {
	if x != nil {
		return x.ChatType
	}
	return 0
}

type CreateChatReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChatID int64 `protobuf:"varint,1,opt,name=chatID,proto3" json:"chatID,omitempty"`
}

func (x *CreateChatReply) Reset() {
	*x = CreateChatReply{}
	mi := &file_rag_rag_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateChatReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateChatReply) ProtoMessage() {}

func (x *CreateChatReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateChatReply.ProtoReflect.Descriptor instead.
func (*CreateChatReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{5}
}

func (x *CreateChatReply) GetChatID() int64 {
	if x != nil {
		return x.ChatID
	}
	return 0
}

// rename chat
type RenameChatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChatID int64  `protobuf:"varint,1,opt,name=chatID,proto3" json:"chatID,omitempty"`
	Name   string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *RenameChatRequest) Reset() {
	*x = RenameChatRequest{}
	mi := &file_rag_rag_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RenameChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenameChatRequest) ProtoMessage() {}

func (x *RenameChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenameChatRequest.ProtoReflect.Descriptor instead.
func (*RenameChatRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{6}
}

func (x *RenameChatRequest) GetChatID() int64 {
	if x != nil {
		return x.ChatID
	}
	return 0
}

func (x *RenameChatRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type RenameChatReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *RenameChatReply) Reset() {
	*x = RenameChatReply{}
	mi := &file_rag_rag_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RenameChatReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenameChatReply) ProtoMessage() {}

func (x *RenameChatReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenameChatReply.ProtoReflect.Descriptor instead.
func (*RenameChatReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{7}
}

// delete chat
type DeleteChatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChatIDs []int64 `protobuf:"varint,1,rep,packed,name=chatIDs,proto3" json:"chatIDs,omitempty"`
}

func (x *DeleteChatRequest) Reset() {
	*x = DeleteChatRequest{}
	mi := &file_rag_rag_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChatRequest) ProtoMessage() {}

func (x *DeleteChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChatRequest.ProtoReflect.Descriptor instead.
func (*DeleteChatRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteChatRequest) GetChatIDs() []int64 {
	if x != nil {
		return x.ChatIDs
	}
	return nil
}

type DeleteChatReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *DeleteChatReply) Reset() {
	*x = DeleteChatReply{}
	mi := &file_rag_rag_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteChatReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteChatReply) ProtoMessage() {}

func (x *DeleteChatReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteChatReply.ProtoReflect.Descriptor instead.
func (*DeleteChatReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{9}
}

// chat detail
type ChatDetailRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChatID int64 `protobuf:"varint,1,opt,name=chatID,proto3" json:"chatID,omitempty"`
}

func (x *ChatDetailRequest) Reset() {
	*x = ChatDetailRequest{}
	mi := &file_rag_rag_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatDetailRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatDetailRequest) ProtoMessage() {}

func (x *ChatDetailRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatDetailRequest.ProtoReflect.Descriptor instead.
func (*ChatDetailRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{10}
}

func (x *ChatDetailRequest) GetChatID() int64 {
	if x != nil {
		return x.ChatID
	}
	return 0
}

type ChatDetailReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChatItems []*ChatItem `protobuf:"bytes,1,rep,name=chatItems,proto3" json:"chatItems,omitempty"`
}

func (x *ChatDetailReply) Reset() {
	*x = ChatDetailReply{}
	mi := &file_rag_rag_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatDetailReply) ProtoMessage() {}

func (x *ChatDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatDetailReply.ProtoReflect.Descriptor instead.
func (*ChatDetailReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{11}
}

func (x *ChatDetailReply) GetChatItems() []*ChatItem {
	if x != nil {
		return x.ChatItems
	}
	return nil
}

type PageChatRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PageNum  int64 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	AgentID  int64 `protobuf:"varint,3,opt,name=agentID,proto3" json:"agentID,omitempty"`
}

func (x *PageChatRequest) Reset() {
	*x = PageChatRequest{}
	mi := &file_rag_rag_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageChatRequest) ProtoMessage() {}

func (x *PageChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageChatRequest.ProtoReflect.Descriptor instead.
func (*PageChatRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{12}
}

func (x *PageChatRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PageChatRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PageChatRequest) GetAgentID() int64 {
	if x != nil {
		return x.AgentID
	}
	return 0
}

type PageChatReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Chats []*Chat `protobuf:"bytes,1,rep,name=chats,proto3" json:"chats,omitempty"`
	Total int64   `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *PageChatReply) Reset() {
	*x = PageChatReply{}
	mi := &file_rag_rag_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PageChatReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageChatReply) ProtoMessage() {}

func (x *PageChatReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageChatReply.ProtoReflect.Descriptor instead.
func (*PageChatReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{13}
}

func (x *PageChatReply) GetChats() []*Chat {
	if x != nil {
		return x.Chats
	}
	return nil
}

func (x *PageChatReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type Documents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Text            string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	EntityTag       string                 `protobuf:"bytes,2,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag    string                 `protobuf:"bytes,3,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	FileRelationID  int64                  `protobuf:"varint,4,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	UpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	Title           string                 `protobuf:"bytes,6,opt,name=title,proto3" json:"title,omitempty"`
	UserID          int64                  `protobuf:"varint,7,opt,name=userID,proto3" json:"userID,omitempty"`
	UserName        string                 `protobuf:"bytes,8,opt,name=userName,proto3" json:"userName,omitempty"`
	FullPath        string                 `protobuf:"bytes,9,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
	TagNames        []string               `protobuf:"bytes,10,rep,name=tagNames,proto3" json:"tagNames,omitempty"`
	ClassPath       string                 `protobuf:"bytes,14,opt,name=classPath,proto3" json:"classPath,omitempty"`
	MimeType        string                 `protobuf:"bytes,11,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	Size            int64                  `protobuf:"varint,12,opt,name=size,proto3" json:"size,omitempty"`
	CanDoAiProcess  bool                   `protobuf:"varint,13,opt,name=canDoAiProcess,proto3" json:"canDoAiProcess,omitempty"`
	InKnowledgeBase bool                   `protobuf:"varint,15,opt,name=inKnowledgeBase,proto3" json:"inKnowledgeBase,omitempty"`
}

func (x *Documents) Reset() {
	*x = Documents{}
	mi := &file_rag_rag_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Documents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Documents) ProtoMessage() {}

func (x *Documents) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Documents.ProtoReflect.Descriptor instead.
func (*Documents) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{14}
}

func (x *Documents) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *Documents) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *Documents) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *Documents) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *Documents) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Documents) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Documents) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *Documents) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Documents) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *Documents) GetTagNames() []string {
	if x != nil {
		return x.TagNames
	}
	return nil
}

func (x *Documents) GetClassPath() string {
	if x != nil {
		return x.ClassPath
	}
	return ""
}

func (x *Documents) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *Documents) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *Documents) GetCanDoAiProcess() bool {
	if x != nil {
		return x.CanDoAiProcess
	}
	return false
}

func (x *Documents) GetInKnowledgeBase() bool {
	if x != nil {
		return x.InKnowledgeBase
	}
	return false
}

type FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID        int64    `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	Title                 string   `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Size                  int64    `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	MimeType              string   `protobuf:"bytes,4,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	UserID                int64    `protobuf:"varint,5,opt,name=userID,proto3" json:"userID,omitempty"`
	EntityTag             string   `protobuf:"bytes,6,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag          string   `protobuf:"bytes,7,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	Index                 int64    `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	ChunkIndex            int64    `protobuf:"varint,9,opt,name=chunkIndex,proto3" json:"chunkIndex,omitempty"`
	Images                []string `protobuf:"bytes,10,rep,name=images,proto3" json:"images,omitempty"`
	FullPath              string   `protobuf:"bytes,11,opt,name=fullPath,proto3" json:"fullPath,omitempty"`
	KnowledgeBaseID       int64    `protobuf:"varint,12,opt,name=knowledgeBaseID,proto3" json:"knowledgeBaseID,omitempty"`
	KnowledgeBaseName     string   `protobuf:"bytes,13,opt,name=knowledgeBaseName,proto3" json:"knowledgeBaseName,omitempty"`
	KnowledgeBaseDataType int64    `protobuf:"varint,14,opt,name=knowledgeBaseDataType,proto3" json:"knowledgeBaseDataType,omitempty"`
	TableData             string   `protobuf:"bytes,15,opt,name=tableData,proto3" json:"tableData,omitempty"`
	ChartSchema           string   `protobuf:"bytes,16,opt,name=chartSchema,proto3" json:"chartSchema,omitempty"`
	FromChatItemID        int64    `protobuf:"varint,17,opt,name=fromChatItemID,proto3" json:"fromChatItemID,omitempty"`
	FromFileRelationID    int64    `protobuf:"varint,18,opt,name=fromFileRelationID,proto3" json:"fromFileRelationID,omitempty"`
}

func (x *FileInfo) Reset() {
	*x = FileInfo{}
	mi := &file_rag_rag_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileInfo) ProtoMessage() {}

func (x *FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileInfo.ProtoReflect.Descriptor instead.
func (*FileInfo) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{15}
}

func (x *FileInfo) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *FileInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *FileInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FileInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *FileInfo) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *FileInfo) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *FileInfo) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *FileInfo) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *FileInfo) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

func (x *FileInfo) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *FileInfo) GetFullPath() string {
	if x != nil {
		return x.FullPath
	}
	return ""
}

func (x *FileInfo) GetKnowledgeBaseID() int64 {
	if x != nil {
		return x.KnowledgeBaseID
	}
	return 0
}

func (x *FileInfo) GetKnowledgeBaseName() string {
	if x != nil {
		return x.KnowledgeBaseName
	}
	return ""
}

func (x *FileInfo) GetKnowledgeBaseDataType() int64 {
	if x != nil {
		return x.KnowledgeBaseDataType
	}
	return 0
}

func (x *FileInfo) GetTableData() string {
	if x != nil {
		return x.TableData
	}
	return ""
}

func (x *FileInfo) GetChartSchema() string {
	if x != nil {
		return x.ChartSchema
	}
	return ""
}

func (x *FileInfo) GetFromChatItemID() int64 {
	if x != nil {
		return x.FromChatItemID
	}
	return 0
}

func (x *FileInfo) GetFromFileRelationID() int64 {
	if x != nil {
		return x.FromFileRelationID
	}
	return 0
}

type FullTextSearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query          string                 `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	SearchType     int64                  `protobuf:"varint,2,opt,name=searchType,proto3" json:"searchType,omitempty"`
	FileType       string                 `protobuf:"bytes,3,opt,name=fileType,proto3" json:"fileType,omitempty"`
	OwnerIDs       []int64                `protobuf:"varint,4,rep,packed,name=ownerIDs,proto3" json:"ownerIDs,omitempty"`
	StartTime      *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime        *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=endTime,proto3" json:"endTime,omitempty"`
	PageNum        int64                  `protobuf:"varint,9,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize       int64                  `protobuf:"varint,10,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	ClassPath      string                 `protobuf:"bytes,11,opt,name=classPath,proto3" json:"classPath,omitempty"`
	Path           string                 `protobuf:"bytes,12,opt,name=path,proto3" json:"path,omitempty"`
	FilterSameFile bool                   `protobuf:"varint,13,opt,name=filterSameFile,proto3" json:"filterSameFile,omitempty"`
}

func (x *FullTextSearchRequest) Reset() {
	*x = FullTextSearchRequest{}
	mi := &file_rag_rag_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullTextSearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullTextSearchRequest) ProtoMessage() {}

func (x *FullTextSearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullTextSearchRequest.ProtoReflect.Descriptor instead.
func (*FullTextSearchRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{16}
}

func (x *FullTextSearchRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *FullTextSearchRequest) GetSearchType() int64 {
	if x != nil {
		return x.SearchType
	}
	return 0
}

func (x *FullTextSearchRequest) GetFileType() string {
	if x != nil {
		return x.FileType
	}
	return ""
}

func (x *FullTextSearchRequest) GetOwnerIDs() []int64 {
	if x != nil {
		return x.OwnerIDs
	}
	return nil
}

func (x *FullTextSearchRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *FullTextSearchRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *FullTextSearchRequest) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *FullTextSearchRequest) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *FullTextSearchRequest) GetClassPath() string {
	if x != nil {
		return x.ClassPath
	}
	return ""
}

func (x *FullTextSearchRequest) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *FullTextSearchRequest) GetFilterSameFile() bool {
	if x != nil {
		return x.FilterSameFile
	}
	return false
}

type FullTextSearchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query    string       `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	TsQuery  []string     `protobuf:"bytes,6,rep,name=tsQuery,proto3" json:"tsQuery,omitempty"`
	PageNum  int64        `protobuf:"varint,2,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64        `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Refs     []*Documents `protobuf:"bytes,4,rep,name=refs,proto3" json:"refs,omitempty"`
	Total    int64        `protobuf:"varint,5,opt,name=total,proto3" json:"total,omitempty"`
}

func (x *FullTextSearchReply) Reset() {
	*x = FullTextSearchReply{}
	mi := &file_rag_rag_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FullTextSearchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullTextSearchReply) ProtoMessage() {}

func (x *FullTextSearchReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullTextSearchReply.ProtoReflect.Descriptor instead.
func (*FullTextSearchReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{17}
}

func (x *FullTextSearchReply) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *FullTextSearchReply) GetTsQuery() []string {
	if x != nil {
		return x.TsQuery
	}
	return nil
}

func (x *FullTextSearchReply) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *FullTextSearchReply) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *FullTextSearchReply) GetRefs() []*Documents {
	if x != nil {
		return x.Refs
	}
	return nil
}

func (x *FullTextSearchReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type ChatCountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=endTime,proto3" json:"endTime,omitempty"`
}

func (x *ChatCountRequest) Reset() {
	*x = ChatCountRequest{}
	mi := &file_rag_rag_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatCountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatCountRequest) ProtoMessage() {}

func (x *ChatCountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatCountRequest.ProtoReflect.Descriptor instead.
func (*ChatCountRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{18}
}

func (x *ChatCountRequest) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *ChatCountRequest) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

type ChatCountReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *ChatCountReply) Reset() {
	*x = ChatCountReply{}
	mi := &file_rag_rag_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatCountReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatCountReply) ProtoMessage() {}

func (x *ChatCountReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatCountReply.ProtoReflect.Descriptor instead.
func (*ChatCountReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{19}
}

func (x *ChatCountReply) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type AgreeChatItemRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 1:赞同 2:点踩
	AgreeStatus int64 `protobuf:"varint,1,opt,name=agreeStatus,proto3" json:"agreeStatus,omitempty"`
	ChatItemID  int64 `protobuf:"varint,2,opt,name=chatItemID,proto3" json:"chatItemID,omitempty"`
}

func (x *AgreeChatItemRequest) Reset() {
	*x = AgreeChatItemRequest{}
	mi := &file_rag_rag_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgreeChatItemRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgreeChatItemRequest) ProtoMessage() {}

func (x *AgreeChatItemRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgreeChatItemRequest.ProtoReflect.Descriptor instead.
func (*AgreeChatItemRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{20}
}

func (x *AgreeChatItemRequest) GetAgreeStatus() int64 {
	if x != nil {
		return x.AgreeStatus
	}
	return 0
}

func (x *AgreeChatItemRequest) GetChatItemID() int64 {
	if x != nil {
		return x.ChatItemID
	}
	return 0
}

type AgreeChatItemReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AgreeChatItemReply) Reset() {
	*x = AgreeChatItemReply{}
	mi := &file_rag_rag_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AgreeChatItemReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AgreeChatItemReply) ProtoMessage() {}

func (x *AgreeChatItemReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AgreeChatItemReply.ProtoReflect.Descriptor instead.
func (*AgreeChatItemReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{21}
}

type UpdateChatItemSuggestQuestionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ChatItemID       int64    `protobuf:"varint,1,opt,name=chatItemID,proto3" json:"chatItemID,omitempty"`
	SuggestQuestions []string `protobuf:"bytes,2,rep,name=suggestQuestions,proto3" json:"suggestQuestions,omitempty"`
}

func (x *UpdateChatItemSuggestQuestionsRequest) Reset() {
	*x = UpdateChatItemSuggestQuestionsRequest{}
	mi := &file_rag_rag_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateChatItemSuggestQuestionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateChatItemSuggestQuestionsRequest) ProtoMessage() {}

func (x *UpdateChatItemSuggestQuestionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateChatItemSuggestQuestionsRequest.ProtoReflect.Descriptor instead.
func (*UpdateChatItemSuggestQuestionsRequest) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateChatItemSuggestQuestionsRequest) GetChatItemID() int64 {
	if x != nil {
		return x.ChatItemID
	}
	return 0
}

func (x *UpdateChatItemSuggestQuestionsRequest) GetSuggestQuestions() []string {
	if x != nil {
		return x.SuggestQuestions
	}
	return nil
}

type UpdateChatItemSuggestQuestionsReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UpdateChatItemSuggestQuestionsReply) Reset() {
	*x = UpdateChatItemSuggestQuestionsReply{}
	mi := &file_rag_rag_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateChatItemSuggestQuestionsReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateChatItemSuggestQuestionsReply) ProtoMessage() {}

func (x *UpdateChatItemSuggestQuestionsReply) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateChatItemSuggestQuestionsReply.ProtoReflect.Descriptor instead.
func (*UpdateChatItemSuggestQuestionsReply) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{23}
}

type AskRagRequest_FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID int64  `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	Title          string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Size           int64  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	MimeType       string `protobuf:"bytes,4,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	UserID         int64  `protobuf:"varint,5,opt,name=userID,proto3" json:"userID,omitempty"`
	EntityTag      string `protobuf:"bytes,6,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag   string `protobuf:"bytes,7,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	Index          int64  `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	ChunkIndex     int64  `protobuf:"varint,9,opt,name=chunkIndex,proto3" json:"chunkIndex,omitempty"`
}

func (x *AskRagRequest_FileInfo) Reset() {
	*x = AskRagRequest_FileInfo{}
	mi := &file_rag_rag_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskRagRequest_FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskRagRequest_FileInfo) ProtoMessage() {}

func (x *AskRagRequest_FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskRagRequest_FileInfo.ProtoReflect.Descriptor instead.
func (*AskRagRequest_FileInfo) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{2, 0}
}

func (x *AskRagRequest_FileInfo) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *AskRagRequest_FileInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AskRagRequest_FileInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *AskRagRequest_FileInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *AskRagRequest_FileInfo) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *AskRagRequest_FileInfo) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *AskRagRequest_FileInfo) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *AskRagRequest_FileInfo) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *AskRagRequest_FileInfo) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

type AskRagReply_FileInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileRelationID int64  `protobuf:"varint,1,opt,name=fileRelationID,proto3" json:"fileRelationID,omitempty"`
	Title          string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Size           int64  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	MimeType       string `protobuf:"bytes,4,opt,name=mimeType,proto3" json:"mimeType,omitempty"`
	UserID         int64  `protobuf:"varint,5,opt,name=userID,proto3" json:"userID,omitempty"`
	EntityTag      string `protobuf:"bytes,6,opt,name=entityTag,proto3" json:"entityTag,omitempty"`
	PreEntityTag   string `protobuf:"bytes,7,opt,name=preEntityTag,proto3" json:"preEntityTag,omitempty"`
	Index          int64  `protobuf:"varint,8,opt,name=index,proto3" json:"index,omitempty"`
	ChunkIndex     int64  `protobuf:"varint,9,opt,name=chunkIndex,proto3" json:"chunkIndex,omitempty"`
}

func (x *AskRagReply_FileInfo) Reset() {
	*x = AskRagReply_FileInfo{}
	mi := &file_rag_rag_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AskRagReply_FileInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AskRagReply_FileInfo) ProtoMessage() {}

func (x *AskRagReply_FileInfo) ProtoReflect() protoreflect.Message {
	mi := &file_rag_rag_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AskRagReply_FileInfo.ProtoReflect.Descriptor instead.
func (*AskRagReply_FileInfo) Descriptor() ([]byte, []int) {
	return file_rag_rag_proto_rawDescGZIP(), []int{3, 0}
}

func (x *AskRagReply_FileInfo) GetFileRelationID() int64 {
	if x != nil {
		return x.FileRelationID
	}
	return 0
}

func (x *AskRagReply_FileInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AskRagReply_FileInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *AskRagReply_FileInfo) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *AskRagReply_FileInfo) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *AskRagReply_FileInfo) GetEntityTag() string {
	if x != nil {
		return x.EntityTag
	}
	return ""
}

func (x *AskRagReply_FileInfo) GetPreEntityTag() string {
	if x != nil {
		return x.PreEntityTag
	}
	return ""
}

func (x *AskRagReply_FileInfo) GetIndex() int64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *AskRagReply_FileInfo) GetChunkIndex() int64 {
	if x != nil {
		return x.ChunkIndex
	}
	return 0
}

var File_rag_rag_proto protoreflect.FileDescriptor

var file_rag_rag_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x72, 0x61, 0x67, 0x2f, 0x72, 0x61, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x07, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xec, 0x03, 0x0a, 0x04, 0x43, 0x68, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x68, 0x61,
	0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49,
	0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12,
	0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x63, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x2c, 0x0a, 0x11, 0x6b,
	0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x61, 0x6e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x63, 0x61, 0x6e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53,
	0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x4d,
	0x73, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x61, 0x6c, 0x6c, 0x62, 0x61,
	0x63, 0x6b, 0x4d, 0x73, 0x67, 0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12,
	0x32, 0x0a, 0x14, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x14, 0x74,
	0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x74, 0x68, 0x69, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x22,
	0xc5, 0x03, 0x0a, 0x08, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06,
	0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x68,
	0x61, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x44,
	0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x72, 0x65,
	0x66, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x61, 0x67, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x72, 0x65, 0x66,
	0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x68, 0x69, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x68, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x68, 0x69, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x0f, 0x68, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x74,
	0x69, 0x6e, 0x75, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x68, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x53, 0x65, 0x6e, 0x64, 0x12,
	0x2a, 0x0a, 0x10, 0x69, 0x73, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x2a, 0x0a, 0x10, 0x73,
	0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa8, 0x03, 0x0a, 0x0d, 0x41, 0x73, 0x6b, 0x52,
	0x61, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x08, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16,
	0x0a, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06,
	0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x44,
	0x12, 0x35, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x41, 0x73, 0x6b, 0x52, 0x61, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x1a, 0x88, 0x02, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05,
	0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64,
	0x65, 0x78, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x22, 0xab, 0x03, 0x0a, 0x0b, 0x41, 0x73, 0x6b, 0x52, 0x61, 0x67, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x05, 0x66, 0x69,
	0x6c, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x61, 0x67, 0x2e, 0x41, 0x73, 0x6b, 0x52, 0x61, 0x67, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e,
	0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49,
	0x44, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x1a, 0x88, 0x02, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04,
	0x73, 0x69, 0x7a, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72,
	0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x22, 0x66, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08,
	0x63, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08,
	0x63, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x29, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x68, 0x61, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x63, 0x68, 0x61,
	0x74, 0x49, 0x44, 0x22, 0x51, 0x0a, 0x11, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x61,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x74,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20,
	0x00, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x12, 0x1b, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x11, 0x0a, 0x0f, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x2d, 0x0a, 0x11, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18,
	0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x03, 0x52,
	0x07, 0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x73, 0x22, 0x11, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x34, 0x0a, 0x11, 0x43,
	0x68, 0x61, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1f, 0x0a, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49,
	0x44, 0x22, 0x42, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61,
	0x67, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x09, 0x63, 0x68, 0x61, 0x74,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x75, 0x0a, 0x0f, 0x50, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65,
	0x4e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02,
	0x20, 0x00, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x25, 0x0a, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x09, 0xfa,
	0x42, 0x06, 0x22, 0x04, 0x18, 0x64, 0x20, 0x00, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x22, 0x4a, 0x0a, 0x0d,
	0x50, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x23, 0x0a,
	0x05, 0x63, 0x68, 0x61, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x52, 0x05, 0x63, 0x68, 0x61,
	0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xe5, 0x03, 0x0a, 0x09, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x26, 0x0a, 0x0e,
	0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x44, 0x12, 0x38, 0x0a, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c,
	0x50, 0x61, 0x74, 0x68, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c,
	0x50, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1a,
	0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x26,
	0x0a, 0x0e, 0x63, 0x61, 0x6e, 0x44, 0x6f, 0x41, 0x69, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x63, 0x61, 0x6e, 0x44, 0x6f, 0x41, 0x69, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x12, 0x28, 0x0a, 0x0f, 0x69, 0x6e, 0x4b, 0x6e, 0x6f, 0x77,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0f, 0x69, 0x6e, 0x4b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x22, 0xe2, 0x04, 0x0a, 0x08, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a,
	0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6d, 0x69, 0x6d, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x44, 0x12, 0x1c, 0x0a, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61, 0x67,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61,
	0x67, 0x12, 0x22, 0x0a, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x54, 0x61,
	0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x54, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1e, 0x0a, 0x0a, 0x63,
	0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6c, 0x6c, 0x50, 0x61, 0x74, 0x68, 0x12,
	0x28, 0x0a, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65,
	0x49, 0x44, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0f, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65,
	0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x49, 0x44, 0x12, 0x2c, 0x0a, 0x11, 0x6b, 0x6e, 0x6f,
	0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x42,
	0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x6b, 0x6e, 0x6f, 0x77, 0x6c,
	0x65, 0x64, 0x67, 0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x0e, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15, 0x6b, 0x6e, 0x6f, 0x77, 0x6c, 0x65, 0x64, 0x67,
	0x65, 0x42, 0x61, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x68, 0x61, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x68, 0x61, 0x72, 0x74, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x12, 0x26, 0x0a,
	0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x44, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x68, 0x61, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x49, 0x44, 0x12, 0x2e, 0x0a, 0x12, 0x66, 0x72, 0x6f, 0x6d, 0x46, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x12, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x12, 0x66, 0x72, 0x6f, 0x6d, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0x85, 0x03, 0x0a, 0x15, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65,
	0x78, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x03, 0x52, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x44, 0x73, 0x12, 0x38, 0x0a,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x61, 0x74, 0x68,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x50, 0x61, 0x74,
	0x68, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x26, 0x0a, 0x0e, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53,
	0x61, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x61, 0x6d, 0x65, 0x46, 0x69, 0x6c, 0x65, 0x22, 0xb9, 0x01,
	0x0a, 0x13, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x71, 0x75, 0x65, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x74,
	0x73, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x74, 0x73,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x26, 0x0a, 0x04, 0x72,
	0x65, 0x66, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x72, 0x61, 0x67, 0x2e, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x04, 0x72,
	0x65, 0x66, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0x82, 0x01, 0x0a, 0x10, 0x43, 0x68,
	0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x38,
	0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x26,
	0x0a, 0x0e, 0x43, 0x68, 0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x58, 0x0a, 0x14, 0x41, 0x67, 0x72, 0x65, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20,
	0x0a, 0x0b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x67, 0x72, 0x65, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x44, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x44,
	0x22, 0x14, 0x0a, 0x12, 0x41, 0x67, 0x72, 0x65, 0x65, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x73, 0x0a, 0x25, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x51,
	0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x44, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0a, 0x63, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x49, 0x44, 0x12,
	0x2a, 0x0a, 0x10, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x67, 0x67, 0x65,
	0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x25, 0x0a, 0x23, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x67,
	0x67, 0x65, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x32, 0xfd, 0x06, 0x0a, 0x03, 0x52, 0x61, 0x67, 0x12, 0x5a, 0x0a, 0x0a, 0x52, 0x65,
	0x6e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x61, 0x74, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x61, 0x67, 0x2e, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x52,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x16,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x72, 0x61, 0x67, 0x2f,
	0x72, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5a, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x10, 0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x72, 0x61, 0x67, 0x2f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x12, 0x5a, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74,
	0x12, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x68, 0x61,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10, 0x3a, 0x01,
	0x2a, 0x22, 0x0b, 0x2f, 0x72, 0x61, 0x67, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x57,
	0x0a, 0x0a, 0x43, 0x68, 0x61, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72,
	0x61, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x13, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0d, 0x12, 0x0b, 0x2f, 0x72, 0x61, 0x67,
	0x2f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x4f, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x12, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x50, 0x61,
	0x67, 0x65, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x68, 0x61, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x11, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x0b, 0x12, 0x09, 0x2f,
	0x72, 0x61, 0x67, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x12, 0x66, 0x0a, 0x0e, 0x46, 0x75, 0x6c, 0x6c,
	0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x61, 0x67, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x72, 0x61, 0x67, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x54, 0x65, 0x78, 0x74, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x10,
	0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x72, 0x61, 0x67, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68,
	0x12, 0x62, 0x0a, 0x0d, 0x41, 0x67, 0x72, 0x65, 0x65, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x41, 0x67, 0x72, 0x65,
	0x65, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x41, 0x67, 0x72, 0x65, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x15, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x0f, 0x3a, 0x01, 0x2a, 0x22, 0x0a, 0x2f, 0x72, 0x61, 0x67, 0x2f, 0x61,
	0x67, 0x72, 0x65, 0x65, 0x12, 0xa8, 0x01, 0x0a, 0x1e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61,
	0x67, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61,
	0x67, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x53, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x3a, 0x01, 0x2a,
	0x22, 0x1d, 0x2f, 0x72, 0x61, 0x67, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x73, 0x75,
	0x67, 0x67, 0x65, 0x73, 0x74, 0x2f, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x41, 0x0a, 0x09, 0x43, 0x68, 0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61,
	0x67, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x00, 0x42, 0x41, 0x0a, 0x07, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x50, 0x01, 0x5a,
	0x34, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2f, 0x69, 0x6e, 0x6e, 0x6f, 0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65,
	0x61, 0x6d, 0x2f, 0x61, 0x69, 0x2d, 0x77, 0x65, 0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x61,
	0x67, 0x3b, 0x72, 0x61, 0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_rag_rag_proto_rawDescOnce sync.Once
	file_rag_rag_proto_rawDescData = file_rag_rag_proto_rawDesc
)

func file_rag_rag_proto_rawDescGZIP() []byte {
	file_rag_rag_proto_rawDescOnce.Do(func() {
		file_rag_rag_proto_rawDescData = protoimpl.X.CompressGZIP(file_rag_rag_proto_rawDescData)
	})
	return file_rag_rag_proto_rawDescData
}

var file_rag_rag_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_rag_rag_proto_goTypes = []any{
	(*Chat)(nil),                                  // 0: api.rag.Chat
	(*ChatItem)(nil),                              // 1: api.rag.ChatItem
	(*AskRagRequest)(nil),                         // 2: api.rag.AskRagRequest
	(*AskRagReply)(nil),                           // 3: api.rag.AskRagReply
	(*CreateChatRequest)(nil),                     // 4: api.rag.CreateChatRequest
	(*CreateChatReply)(nil),                       // 5: api.rag.CreateChatReply
	(*RenameChatRequest)(nil),                     // 6: api.rag.RenameChatRequest
	(*RenameChatReply)(nil),                       // 7: api.rag.RenameChatReply
	(*DeleteChatRequest)(nil),                     // 8: api.rag.DeleteChatRequest
	(*DeleteChatReply)(nil),                       // 9: api.rag.DeleteChatReply
	(*ChatDetailRequest)(nil),                     // 10: api.rag.ChatDetailRequest
	(*ChatDetailReply)(nil),                       // 11: api.rag.ChatDetailReply
	(*PageChatRequest)(nil),                       // 12: api.rag.PageChatRequest
	(*PageChatReply)(nil),                         // 13: api.rag.PageChatReply
	(*Documents)(nil),                             // 14: api.rag.Documents
	(*FileInfo)(nil),                              // 15: api.rag.FileInfo
	(*FullTextSearchRequest)(nil),                 // 16: api.rag.FullTextSearchRequest
	(*FullTextSearchReply)(nil),                   // 17: api.rag.FullTextSearchReply
	(*ChatCountRequest)(nil),                      // 18: api.rag.ChatCountRequest
	(*ChatCountReply)(nil),                        // 19: api.rag.ChatCountReply
	(*AgreeChatItemRequest)(nil),                  // 20: api.rag.AgreeChatItemRequest
	(*AgreeChatItemReply)(nil),                    // 21: api.rag.AgreeChatItemReply
	(*UpdateChatItemSuggestQuestionsRequest)(nil), // 22: api.rag.UpdateChatItemSuggestQuestionsRequest
	(*UpdateChatItemSuggestQuestionsReply)(nil),   // 23: api.rag.UpdateChatItemSuggestQuestionsReply
	(*AskRagRequest_FileInfo)(nil),                // 24: api.rag.AskRagRequest.FileInfo
	(*AskRagReply_FileInfo)(nil),                  // 25: api.rag.AskRagReply.FileInfo
	(*timestamppb.Timestamp)(nil),                 // 26: google.protobuf.Timestamp
}
var file_rag_rag_proto_depIdxs = []int32{
	26, // 0: api.rag.Chat.createdAt:type_name -> google.protobuf.Timestamp
	15, // 1: api.rag.ChatItem.refs:type_name -> api.rag.FileInfo
	24, // 2: api.rag.AskRagRequest.files:type_name -> api.rag.AskRagRequest.FileInfo
	25, // 3: api.rag.AskRagReply.files:type_name -> api.rag.AskRagReply.FileInfo
	1,  // 4: api.rag.ChatDetailReply.chatItems:type_name -> api.rag.ChatItem
	0,  // 5: api.rag.PageChatReply.chats:type_name -> api.rag.Chat
	26, // 6: api.rag.Documents.updatedAt:type_name -> google.protobuf.Timestamp
	26, // 7: api.rag.FullTextSearchRequest.startTime:type_name -> google.protobuf.Timestamp
	26, // 8: api.rag.FullTextSearchRequest.endTime:type_name -> google.protobuf.Timestamp
	14, // 9: api.rag.FullTextSearchReply.refs:type_name -> api.rag.Documents
	26, // 10: api.rag.ChatCountRequest.startTime:type_name -> google.protobuf.Timestamp
	26, // 11: api.rag.ChatCountRequest.endTime:type_name -> google.protobuf.Timestamp
	6,  // 12: api.rag.Rag.RenameChat:input_type -> api.rag.RenameChatRequest
	4,  // 13: api.rag.Rag.CreateChat:input_type -> api.rag.CreateChatRequest
	8,  // 14: api.rag.Rag.DeleteChat:input_type -> api.rag.DeleteChatRequest
	10, // 15: api.rag.Rag.ChatDetail:input_type -> api.rag.ChatDetailRequest
	12, // 16: api.rag.Rag.PageChat:input_type -> api.rag.PageChatRequest
	16, // 17: api.rag.Rag.FullTextSearch:input_type -> api.rag.FullTextSearchRequest
	20, // 18: api.rag.Rag.AgreeChatItem:input_type -> api.rag.AgreeChatItemRequest
	22, // 19: api.rag.Rag.UpdateChatItemSuggestQuestions:input_type -> api.rag.UpdateChatItemSuggestQuestionsRequest
	18, // 20: api.rag.Rag.ChatCount:input_type -> api.rag.ChatCountRequest
	7,  // 21: api.rag.Rag.RenameChat:output_type -> api.rag.RenameChatReply
	5,  // 22: api.rag.Rag.CreateChat:output_type -> api.rag.CreateChatReply
	9,  // 23: api.rag.Rag.DeleteChat:output_type -> api.rag.DeleteChatReply
	11, // 24: api.rag.Rag.ChatDetail:output_type -> api.rag.ChatDetailReply
	13, // 25: api.rag.Rag.PageChat:output_type -> api.rag.PageChatReply
	17, // 26: api.rag.Rag.FullTextSearch:output_type -> api.rag.FullTextSearchReply
	21, // 27: api.rag.Rag.AgreeChatItem:output_type -> api.rag.AgreeChatItemReply
	23, // 28: api.rag.Rag.UpdateChatItemSuggestQuestions:output_type -> api.rag.UpdateChatItemSuggestQuestionsReply
	19, // 29: api.rag.Rag.ChatCount:output_type -> api.rag.ChatCountReply
	21, // [21:30] is the sub-list for method output_type
	12, // [12:21] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_rag_rag_proto_init() }
func file_rag_rag_proto_init() {
	if File_rag_rag_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rag_rag_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_rag_rag_proto_goTypes,
		DependencyIndexes: file_rag_rag_proto_depIdxs,
		MessageInfos:      file_rag_rag_proto_msgTypes,
	}.Build()
	File_rag_rag_proto = out.File
	file_rag_rag_proto_rawDesc = nil
	file_rag_rag_proto_goTypes = nil
	file_rag_rag_proto_depIdxs = nil
}

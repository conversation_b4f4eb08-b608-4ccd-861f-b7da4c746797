// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: rag/rag.proto

package rag

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on Chat with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Chat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Chat with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ChatMultiError, or nil if none found.
func (m *Chat) ValidateAll() error {
	return m.validate(true)
}

func (m *Chat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatID

	// no validation rules for Name

	// no validation rules for AgentID

	// no validation rules for ChatType

	// no validation rules for AgentName

	// no validation rules for AgentAvatar

	// no validation rules for KnowledgeBaseType

	// no validation rules for ModelType

	// no validation rules for CanInternetSearch

	// no validation rules for AgentType

	// no validation rules for FallbackMsg

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChatValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChatValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChatValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ThinkingEnableStatus

	// no validation rules for Thinking

	if len(errors) > 0 {
		return ChatMultiError(errors)
	}

	return nil
}

// ChatMultiError is an error wrapping multiple validation errors returned by
// Chat.ValidateAll() if the designated constraints aren't met.
type ChatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatMultiError) AllErrors() []error { return m }

// ChatValidationError is the validation error returned by Chat.Validate if the
// designated constraints aren't met.
type ChatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatValidationError) ErrorName() string { return "ChatValidationError" }

// Error satisfies the builtin error interface
func (e ChatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatValidationError{}

// Validate checks the field values on ChatItem with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ChatItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ChatItemMultiError, or nil
// if none found.
func (m *ChatItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ChatID

	// no validation rules for ObjectID

	// no validation rules for ObjectType

	// no validation rules for Message

	for idx, item := range m.GetRefs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChatItemValidationError{
						field:  fmt.Sprintf("Refs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChatItemValidationError{
						field:  fmt.Sprintf("Refs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChatItemValidationError{
					field:  fmt.Sprintf("Refs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for RoundID

	// no validation rules for Reason

	// no validation rules for AgreeStatus

	// no validation rules for HitAction

	// no validation rules for HitResponse

	// no validation rules for HitContinueSend

	// no validation rules for IsInternetSearch

	if len(errors) > 0 {
		return ChatItemMultiError(errors)
	}

	return nil
}

// ChatItemMultiError is an error wrapping multiple validation errors returned
// by ChatItem.ValidateAll() if the designated constraints aren't met.
type ChatItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatItemMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatItemMultiError) AllErrors() []error { return m }

// ChatItemValidationError is the validation error returned by
// ChatItem.Validate if the designated constraints aren't met.
type ChatItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatItemValidationError) ErrorName() string { return "ChatItemValidationError" }

// Error satisfies the builtin error interface
func (e ChatItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatItemValidationError{}

// Validate checks the field values on AskRagRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AskRagRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AskRagRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AskRagRequestMultiError, or
// nil if none found.
func (m *AskRagRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AskRagRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetQuestion()) < 1 {
		err := AskRagRequestValidationError{
			field:  "Question",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ChatID

	// no validation rules for RoundID

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AskRagRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AskRagRequestValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AskRagRequestValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AskRagRequestMultiError(errors)
	}

	return nil
}

// AskRagRequestMultiError is an error wrapping multiple validation errors
// returned by AskRagRequest.ValidateAll() if the designated constraints
// aren't met.
type AskRagRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AskRagRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AskRagRequestMultiError) AllErrors() []error { return m }

// AskRagRequestValidationError is the validation error returned by
// AskRagRequest.Validate if the designated constraints aren't met.
type AskRagRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AskRagRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AskRagRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AskRagRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AskRagRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AskRagRequestValidationError) ErrorName() string { return "AskRagRequestValidationError" }

// Error satisfies the builtin error interface
func (e AskRagRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAskRagRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AskRagRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AskRagRequestValidationError{}

// Validate checks the field values on AskRagReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AskRagReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AskRagReply with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AskRagReplyMultiError, or
// nil if none found.
func (m *AskRagReply) ValidateAll() error {
	return m.validate(true)
}

func (m *AskRagReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Answer

	for idx, item := range m.GetFiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AskRagReplyValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AskRagReplyValidationError{
						field:  fmt.Sprintf("Files[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AskRagReplyValidationError{
					field:  fmt.Sprintf("Files[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Status

	// no validation rules for RoundID

	// no validation rules for Type

	if len(errors) > 0 {
		return AskRagReplyMultiError(errors)
	}

	return nil
}

// AskRagReplyMultiError is an error wrapping multiple validation errors
// returned by AskRagReply.ValidateAll() if the designated constraints aren't met.
type AskRagReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AskRagReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AskRagReplyMultiError) AllErrors() []error { return m }

// AskRagReplyValidationError is the validation error returned by
// AskRagReply.Validate if the designated constraints aren't met.
type AskRagReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AskRagReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AskRagReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AskRagReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AskRagReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AskRagReplyValidationError) ErrorName() string { return "AskRagReplyValidationError" }

// Error satisfies the builtin error interface
func (e AskRagReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAskRagReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AskRagReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AskRagReplyValidationError{}

// Validate checks the field values on CreateChatRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateChatRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateChatRequestMultiError, or nil if none found.
func (m *CreateChatRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := CreateChatRequestValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AgentID

	// no validation rules for ChatType

	if len(errors) > 0 {
		return CreateChatRequestMultiError(errors)
	}

	return nil
}

// CreateChatRequestMultiError is an error wrapping multiple validation errors
// returned by CreateChatRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateChatRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatRequestMultiError) AllErrors() []error { return m }

// CreateChatRequestValidationError is the validation error returned by
// CreateChatRequest.Validate if the designated constraints aren't met.
type CreateChatRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatRequestValidationError) ErrorName() string {
	return "CreateChatRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateChatRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatRequestValidationError{}

// Validate checks the field values on CreateChatReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateChatReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateChatReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateChatReplyMultiError, or nil if none found.
func (m *CreateChatReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateChatReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatID

	if len(errors) > 0 {
		return CreateChatReplyMultiError(errors)
	}

	return nil
}

// CreateChatReplyMultiError is an error wrapping multiple validation errors
// returned by CreateChatReply.ValidateAll() if the designated constraints
// aren't met.
type CreateChatReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateChatReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateChatReplyMultiError) AllErrors() []error { return m }

// CreateChatReplyValidationError is the validation error returned by
// CreateChatReply.Validate if the designated constraints aren't met.
type CreateChatReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateChatReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateChatReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateChatReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateChatReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateChatReplyValidationError) ErrorName() string { return "CreateChatReplyValidationError" }

// Error satisfies the builtin error interface
func (e CreateChatReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateChatReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateChatReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateChatReplyValidationError{}

// Validate checks the field values on RenameChatRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RenameChatRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenameChatRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenameChatRequestMultiError, or nil if none found.
func (m *RenameChatRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RenameChatRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetChatID() <= 0 {
		err := RenameChatRequestValidationError{
			field:  "ChatID",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetName()) < 1 {
		err := RenameChatRequestValidationError{
			field:  "Name",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return RenameChatRequestMultiError(errors)
	}

	return nil
}

// RenameChatRequestMultiError is an error wrapping multiple validation errors
// returned by RenameChatRequest.ValidateAll() if the designated constraints
// aren't met.
type RenameChatRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenameChatRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenameChatRequestMultiError) AllErrors() []error { return m }

// RenameChatRequestValidationError is the validation error returned by
// RenameChatRequest.Validate if the designated constraints aren't met.
type RenameChatRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenameChatRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenameChatRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenameChatRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenameChatRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenameChatRequestValidationError) ErrorName() string {
	return "RenameChatRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RenameChatRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenameChatRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenameChatRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenameChatRequestValidationError{}

// Validate checks the field values on RenameChatReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RenameChatReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenameChatReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenameChatReplyMultiError, or nil if none found.
func (m *RenameChatReply) ValidateAll() error {
	return m.validate(true)
}

func (m *RenameChatReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return RenameChatReplyMultiError(errors)
	}

	return nil
}

// RenameChatReplyMultiError is an error wrapping multiple validation errors
// returned by RenameChatReply.ValidateAll() if the designated constraints
// aren't met.
type RenameChatReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenameChatReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenameChatReplyMultiError) AllErrors() []error { return m }

// RenameChatReplyValidationError is the validation error returned by
// RenameChatReply.Validate if the designated constraints aren't met.
type RenameChatReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenameChatReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenameChatReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenameChatReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenameChatReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenameChatReplyValidationError) ErrorName() string { return "RenameChatReplyValidationError" }

// Error satisfies the builtin error interface
func (e RenameChatReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenameChatReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenameChatReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenameChatReplyValidationError{}

// Validate checks the field values on DeleteChatRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteChatRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteChatRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteChatRequestMultiError, or nil if none found.
func (m *DeleteChatRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteChatRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteChatRequestMultiError(errors)
	}

	return nil
}

// DeleteChatRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteChatRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteChatRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteChatRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteChatRequestMultiError) AllErrors() []error { return m }

// DeleteChatRequestValidationError is the validation error returned by
// DeleteChatRequest.Validate if the designated constraints aren't met.
type DeleteChatRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteChatRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteChatRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteChatRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteChatRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteChatRequestValidationError) ErrorName() string {
	return "DeleteChatRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteChatRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteChatRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteChatRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteChatRequestValidationError{}

// Validate checks the field values on DeleteChatReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteChatReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteChatReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteChatReplyMultiError, or nil if none found.
func (m *DeleteChatReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteChatReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteChatReplyMultiError(errors)
	}

	return nil
}

// DeleteChatReplyMultiError is an error wrapping multiple validation errors
// returned by DeleteChatReply.ValidateAll() if the designated constraints
// aren't met.
type DeleteChatReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteChatReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteChatReplyMultiError) AllErrors() []error { return m }

// DeleteChatReplyValidationError is the validation error returned by
// DeleteChatReply.Validate if the designated constraints aren't met.
type DeleteChatReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteChatReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteChatReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteChatReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteChatReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteChatReplyValidationError) ErrorName() string { return "DeleteChatReplyValidationError" }

// Error satisfies the builtin error interface
func (e DeleteChatReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteChatReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteChatReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteChatReplyValidationError{}

// Validate checks the field values on ChatDetailRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChatDetailRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatDetailRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChatDetailRequestMultiError, or nil if none found.
func (m *ChatDetailRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatDetailRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetChatID() <= 0 {
		err := ChatDetailRequestValidationError{
			field:  "ChatID",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ChatDetailRequestMultiError(errors)
	}

	return nil
}

// ChatDetailRequestMultiError is an error wrapping multiple validation errors
// returned by ChatDetailRequest.ValidateAll() if the designated constraints
// aren't met.
type ChatDetailRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatDetailRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatDetailRequestMultiError) AllErrors() []error { return m }

// ChatDetailRequestValidationError is the validation error returned by
// ChatDetailRequest.Validate if the designated constraints aren't met.
type ChatDetailRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatDetailRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatDetailRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatDetailRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatDetailRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatDetailRequestValidationError) ErrorName() string {
	return "ChatDetailRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ChatDetailRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatDetailRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatDetailRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatDetailRequestValidationError{}

// Validate checks the field values on ChatDetailReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChatDetailReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatDetailReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChatDetailReplyMultiError, or nil if none found.
func (m *ChatDetailReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatDetailReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetChatItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ChatDetailReplyValidationError{
						field:  fmt.Sprintf("ChatItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ChatDetailReplyValidationError{
						field:  fmt.Sprintf("ChatItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ChatDetailReplyValidationError{
					field:  fmt.Sprintf("ChatItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ChatDetailReplyMultiError(errors)
	}

	return nil
}

// ChatDetailReplyMultiError is an error wrapping multiple validation errors
// returned by ChatDetailReply.ValidateAll() if the designated constraints
// aren't met.
type ChatDetailReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatDetailReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatDetailReplyMultiError) AllErrors() []error { return m }

// ChatDetailReplyValidationError is the validation error returned by
// ChatDetailReply.Validate if the designated constraints aren't met.
type ChatDetailReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatDetailReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatDetailReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatDetailReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatDetailReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatDetailReplyValidationError) ErrorName() string { return "ChatDetailReplyValidationError" }

// Error satisfies the builtin error interface
func (e ChatDetailReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatDetailReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatDetailReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatDetailReplyValidationError{}

// Validate checks the field values on PageChatRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PageChatRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageChatRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PageChatRequestMultiError, or nil if none found.
func (m *PageChatRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PageChatRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPageNum() <= 0 {
		err := PageChatRequestValidationError{
			field:  "PageNum",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if val := m.GetPageSize(); val <= 0 || val > 100 {
		err := PageChatRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range (0, 100]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for AgentID

	if len(errors) > 0 {
		return PageChatRequestMultiError(errors)
	}

	return nil
}

// PageChatRequestMultiError is an error wrapping multiple validation errors
// returned by PageChatRequest.ValidateAll() if the designated constraints
// aren't met.
type PageChatRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageChatRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageChatRequestMultiError) AllErrors() []error { return m }

// PageChatRequestValidationError is the validation error returned by
// PageChatRequest.Validate if the designated constraints aren't met.
type PageChatRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageChatRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageChatRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageChatRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageChatRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageChatRequestValidationError) ErrorName() string { return "PageChatRequestValidationError" }

// Error satisfies the builtin error interface
func (e PageChatRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageChatRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageChatRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageChatRequestValidationError{}

// Validate checks the field values on PageChatReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PageChatReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PageChatReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PageChatReplyMultiError, or
// nil if none found.
func (m *PageChatReply) ValidateAll() error {
	return m.validate(true)
}

func (m *PageChatReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetChats() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PageChatReplyValidationError{
						field:  fmt.Sprintf("Chats[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PageChatReplyValidationError{
						field:  fmt.Sprintf("Chats[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PageChatReplyValidationError{
					field:  fmt.Sprintf("Chats[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return PageChatReplyMultiError(errors)
	}

	return nil
}

// PageChatReplyMultiError is an error wrapping multiple validation errors
// returned by PageChatReply.ValidateAll() if the designated constraints
// aren't met.
type PageChatReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PageChatReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PageChatReplyMultiError) AllErrors() []error { return m }

// PageChatReplyValidationError is the validation error returned by
// PageChatReply.Validate if the designated constraints aren't met.
type PageChatReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PageChatReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PageChatReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PageChatReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PageChatReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PageChatReplyValidationError) ErrorName() string { return "PageChatReplyValidationError" }

// Error satisfies the builtin error interface
func (e PageChatReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPageChatReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PageChatReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PageChatReplyValidationError{}

// Validate checks the field values on Documents with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Documents) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Documents with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DocumentsMultiError, or nil
// if none found.
func (m *Documents) ValidateAll() error {
	return m.validate(true)
}

func (m *Documents) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Text

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for FileRelationID

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DocumentsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DocumentsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DocumentsValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Title

	// no validation rules for UserID

	// no validation rules for UserName

	// no validation rules for FullPath

	// no validation rules for ClassPath

	// no validation rules for MimeType

	// no validation rules for Size

	// no validation rules for CanDoAiProcess

	// no validation rules for InKnowledgeBase

	if len(errors) > 0 {
		return DocumentsMultiError(errors)
	}

	return nil
}

// DocumentsMultiError is an error wrapping multiple validation errors returned
// by Documents.ValidateAll() if the designated constraints aren't met.
type DocumentsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DocumentsMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DocumentsMultiError) AllErrors() []error { return m }

// DocumentsValidationError is the validation error returned by
// Documents.Validate if the designated constraints aren't met.
type DocumentsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DocumentsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DocumentsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DocumentsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DocumentsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DocumentsValidationError) ErrorName() string { return "DocumentsValidationError" }

// Error satisfies the builtin error interface
func (e DocumentsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDocuments.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DocumentsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DocumentsValidationError{}

// Validate checks the field values on FileInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FileInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FileInfoMultiError, or nil
// if none found.
func (m *FileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for Title

	// no validation rules for Size

	// no validation rules for MimeType

	// no validation rules for UserID

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for Index

	// no validation rules for ChunkIndex

	// no validation rules for FullPath

	// no validation rules for KnowledgeBaseID

	// no validation rules for KnowledgeBaseName

	// no validation rules for KnowledgeBaseDataType

	// no validation rules for TableData

	// no validation rules for ChartSchema

	// no validation rules for FromChatItemID

	// no validation rules for FromFileRelationID

	if len(errors) > 0 {
		return FileInfoMultiError(errors)
	}

	return nil
}

// FileInfoMultiError is an error wrapping multiple validation errors returned
// by FileInfo.ValidateAll() if the designated constraints aren't met.
type FileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FileInfoMultiError) AllErrors() []error { return m }

// FileInfoValidationError is the validation error returned by
// FileInfo.Validate if the designated constraints aren't met.
type FileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FileInfoValidationError) ErrorName() string { return "FileInfoValidationError" }

// Error satisfies the builtin error interface
func (e FileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FileInfoValidationError{}

// Validate checks the field values on FullTextSearchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FullTextSearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullTextSearchRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FullTextSearchRequestMultiError, or nil if none found.
func (m *FullTextSearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FullTextSearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Query

	// no validation rules for SearchType

	// no validation rules for FileType

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullTextSearchRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullTextSearchRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullTextSearchRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullTextSearchRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullTextSearchRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullTextSearchRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PageNum

	// no validation rules for PageSize

	// no validation rules for ClassPath

	// no validation rules for Path

	// no validation rules for FilterSameFile

	if len(errors) > 0 {
		return FullTextSearchRequestMultiError(errors)
	}

	return nil
}

// FullTextSearchRequestMultiError is an error wrapping multiple validation
// errors returned by FullTextSearchRequest.ValidateAll() if the designated
// constraints aren't met.
type FullTextSearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullTextSearchRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullTextSearchRequestMultiError) AllErrors() []error { return m }

// FullTextSearchRequestValidationError is the validation error returned by
// FullTextSearchRequest.Validate if the designated constraints aren't met.
type FullTextSearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullTextSearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullTextSearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullTextSearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullTextSearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullTextSearchRequestValidationError) ErrorName() string {
	return "FullTextSearchRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FullTextSearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullTextSearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullTextSearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullTextSearchRequestValidationError{}

// Validate checks the field values on FullTextSearchReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FullTextSearchReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullTextSearchReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FullTextSearchReplyMultiError, or nil if none found.
func (m *FullTextSearchReply) ValidateAll() error {
	return m.validate(true)
}

func (m *FullTextSearchReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Query

	// no validation rules for PageNum

	// no validation rules for PageSize

	for idx, item := range m.GetRefs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FullTextSearchReplyValidationError{
						field:  fmt.Sprintf("Refs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FullTextSearchReplyValidationError{
						field:  fmt.Sprintf("Refs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FullTextSearchReplyValidationError{
					field:  fmt.Sprintf("Refs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Total

	if len(errors) > 0 {
		return FullTextSearchReplyMultiError(errors)
	}

	return nil
}

// FullTextSearchReplyMultiError is an error wrapping multiple validation
// errors returned by FullTextSearchReply.ValidateAll() if the designated
// constraints aren't met.
type FullTextSearchReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullTextSearchReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullTextSearchReplyMultiError) AllErrors() []error { return m }

// FullTextSearchReplyValidationError is the validation error returned by
// FullTextSearchReply.Validate if the designated constraints aren't met.
type FullTextSearchReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullTextSearchReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullTextSearchReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullTextSearchReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullTextSearchReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullTextSearchReplyValidationError) ErrorName() string {
	return "FullTextSearchReplyValidationError"
}

// Error satisfies the builtin error interface
func (e FullTextSearchReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullTextSearchReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullTextSearchReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullTextSearchReplyValidationError{}

// Validate checks the field values on ChatCountRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ChatCountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatCountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ChatCountRequestMultiError, or nil if none found.
func (m *ChatCountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatCountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChatCountRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChatCountRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChatCountRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ChatCountRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ChatCountRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ChatCountRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ChatCountRequestMultiError(errors)
	}

	return nil
}

// ChatCountRequestMultiError is an error wrapping multiple validation errors
// returned by ChatCountRequest.ValidateAll() if the designated constraints
// aren't met.
type ChatCountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatCountRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatCountRequestMultiError) AllErrors() []error { return m }

// ChatCountRequestValidationError is the validation error returned by
// ChatCountRequest.Validate if the designated constraints aren't met.
type ChatCountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatCountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatCountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatCountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatCountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatCountRequestValidationError) ErrorName() string { return "ChatCountRequestValidationError" }

// Error satisfies the builtin error interface
func (e ChatCountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatCountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatCountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatCountRequestValidationError{}

// Validate checks the field values on ChatCountReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ChatCountReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ChatCountReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ChatCountReplyMultiError,
// or nil if none found.
func (m *ChatCountReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ChatCountReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Count

	if len(errors) > 0 {
		return ChatCountReplyMultiError(errors)
	}

	return nil
}

// ChatCountReplyMultiError is an error wrapping multiple validation errors
// returned by ChatCountReply.ValidateAll() if the designated constraints
// aren't met.
type ChatCountReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ChatCountReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ChatCountReplyMultiError) AllErrors() []error { return m }

// ChatCountReplyValidationError is the validation error returned by
// ChatCountReply.Validate if the designated constraints aren't met.
type ChatCountReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ChatCountReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ChatCountReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ChatCountReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ChatCountReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ChatCountReplyValidationError) ErrorName() string { return "ChatCountReplyValidationError" }

// Error satisfies the builtin error interface
func (e ChatCountReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sChatCountReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ChatCountReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ChatCountReplyValidationError{}

// Validate checks the field values on AgreeChatItemRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgreeChatItemRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgreeChatItemRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgreeChatItemRequestMultiError, or nil if none found.
func (m *AgreeChatItemRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AgreeChatItemRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgreeStatus

	// no validation rules for ChatItemID

	if len(errors) > 0 {
		return AgreeChatItemRequestMultiError(errors)
	}

	return nil
}

// AgreeChatItemRequestMultiError is an error wrapping multiple validation
// errors returned by AgreeChatItemRequest.ValidateAll() if the designated
// constraints aren't met.
type AgreeChatItemRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgreeChatItemRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgreeChatItemRequestMultiError) AllErrors() []error { return m }

// AgreeChatItemRequestValidationError is the validation error returned by
// AgreeChatItemRequest.Validate if the designated constraints aren't met.
type AgreeChatItemRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgreeChatItemRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgreeChatItemRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgreeChatItemRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgreeChatItemRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgreeChatItemRequestValidationError) ErrorName() string {
	return "AgreeChatItemRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AgreeChatItemRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgreeChatItemRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgreeChatItemRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgreeChatItemRequestValidationError{}

// Validate checks the field values on AgreeChatItemReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AgreeChatItemReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AgreeChatItemReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AgreeChatItemReplyMultiError, or nil if none found.
func (m *AgreeChatItemReply) ValidateAll() error {
	return m.validate(true)
}

func (m *AgreeChatItemReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AgreeChatItemReplyMultiError(errors)
	}

	return nil
}

// AgreeChatItemReplyMultiError is an error wrapping multiple validation errors
// returned by AgreeChatItemReply.ValidateAll() if the designated constraints
// aren't met.
type AgreeChatItemReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AgreeChatItemReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AgreeChatItemReplyMultiError) AllErrors() []error { return m }

// AgreeChatItemReplyValidationError is the validation error returned by
// AgreeChatItemReply.Validate if the designated constraints aren't met.
type AgreeChatItemReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AgreeChatItemReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AgreeChatItemReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AgreeChatItemReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AgreeChatItemReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AgreeChatItemReplyValidationError) ErrorName() string {
	return "AgreeChatItemReplyValidationError"
}

// Error satisfies the builtin error interface
func (e AgreeChatItemReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAgreeChatItemReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AgreeChatItemReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AgreeChatItemReplyValidationError{}

// Validate checks the field values on UpdateChatItemSuggestQuestionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateChatItemSuggestQuestionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateChatItemSuggestQuestionsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateChatItemSuggestQuestionsRequestMultiError, or nil if none found.
func (m *UpdateChatItemSuggestQuestionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateChatItemSuggestQuestionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ChatItemID

	if len(errors) > 0 {
		return UpdateChatItemSuggestQuestionsRequestMultiError(errors)
	}

	return nil
}

// UpdateChatItemSuggestQuestionsRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateChatItemSuggestQuestionsRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateChatItemSuggestQuestionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateChatItemSuggestQuestionsRequestMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateChatItemSuggestQuestionsRequestMultiError) AllErrors() []error { return m }

// UpdateChatItemSuggestQuestionsRequestValidationError is the validation error
// returned by UpdateChatItemSuggestQuestionsRequest.Validate if the
// designated constraints aren't met.
type UpdateChatItemSuggestQuestionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateChatItemSuggestQuestionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateChatItemSuggestQuestionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateChatItemSuggestQuestionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateChatItemSuggestQuestionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateChatItemSuggestQuestionsRequestValidationError) ErrorName() string {
	return "UpdateChatItemSuggestQuestionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateChatItemSuggestQuestionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateChatItemSuggestQuestionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateChatItemSuggestQuestionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateChatItemSuggestQuestionsRequestValidationError{}

// Validate checks the field values on UpdateChatItemSuggestQuestionsReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateChatItemSuggestQuestionsReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateChatItemSuggestQuestionsReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateChatItemSuggestQuestionsReplyMultiError, or nil if none found.
func (m *UpdateChatItemSuggestQuestionsReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateChatItemSuggestQuestionsReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return UpdateChatItemSuggestQuestionsReplyMultiError(errors)
	}

	return nil
}

// UpdateChatItemSuggestQuestionsReplyMultiError is an error wrapping multiple
// validation errors returned by
// UpdateChatItemSuggestQuestionsReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateChatItemSuggestQuestionsReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateChatItemSuggestQuestionsReplyMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateChatItemSuggestQuestionsReplyMultiError) AllErrors() []error { return m }

// UpdateChatItemSuggestQuestionsReplyValidationError is the validation error
// returned by UpdateChatItemSuggestQuestionsReply.Validate if the designated
// constraints aren't met.
type UpdateChatItemSuggestQuestionsReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateChatItemSuggestQuestionsReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateChatItemSuggestQuestionsReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateChatItemSuggestQuestionsReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateChatItemSuggestQuestionsReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateChatItemSuggestQuestionsReplyValidationError) ErrorName() string {
	return "UpdateChatItemSuggestQuestionsReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateChatItemSuggestQuestionsReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateChatItemSuggestQuestionsReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateChatItemSuggestQuestionsReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateChatItemSuggestQuestionsReplyValidationError{}

// Validate checks the field values on AskRagRequest_FileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AskRagRequest_FileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AskRagRequest_FileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AskRagRequest_FileInfoMultiError, or nil if none found.
func (m *AskRagRequest_FileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AskRagRequest_FileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for Title

	// no validation rules for Size

	// no validation rules for MimeType

	// no validation rules for UserID

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for Index

	// no validation rules for ChunkIndex

	if len(errors) > 0 {
		return AskRagRequest_FileInfoMultiError(errors)
	}

	return nil
}

// AskRagRequest_FileInfoMultiError is an error wrapping multiple validation
// errors returned by AskRagRequest_FileInfo.ValidateAll() if the designated
// constraints aren't met.
type AskRagRequest_FileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AskRagRequest_FileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AskRagRequest_FileInfoMultiError) AllErrors() []error { return m }

// AskRagRequest_FileInfoValidationError is the validation error returned by
// AskRagRequest_FileInfo.Validate if the designated constraints aren't met.
type AskRagRequest_FileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AskRagRequest_FileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AskRagRequest_FileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AskRagRequest_FileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AskRagRequest_FileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AskRagRequest_FileInfoValidationError) ErrorName() string {
	return "AskRagRequest_FileInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AskRagRequest_FileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAskRagRequest_FileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AskRagRequest_FileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AskRagRequest_FileInfoValidationError{}

// Validate checks the field values on AskRagReply_FileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AskRagReply_FileInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AskRagReply_FileInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AskRagReply_FileInfoMultiError, or nil if none found.
func (m *AskRagReply_FileInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AskRagReply_FileInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FileRelationID

	// no validation rules for Title

	// no validation rules for Size

	// no validation rules for MimeType

	// no validation rules for UserID

	// no validation rules for EntityTag

	// no validation rules for PreEntityTag

	// no validation rules for Index

	// no validation rules for ChunkIndex

	if len(errors) > 0 {
		return AskRagReply_FileInfoMultiError(errors)
	}

	return nil
}

// AskRagReply_FileInfoMultiError is an error wrapping multiple validation
// errors returned by AskRagReply_FileInfo.ValidateAll() if the designated
// constraints aren't met.
type AskRagReply_FileInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AskRagReply_FileInfoMultiError) Error() string {
	msgs := make([]string, 0, len(m))
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AskRagReply_FileInfoMultiError) AllErrors() []error { return m }

// AskRagReply_FileInfoValidationError is the validation error returned by
// AskRagReply_FileInfo.Validate if the designated constraints aren't met.
type AskRagReply_FileInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AskRagReply_FileInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AskRagReply_FileInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AskRagReply_FileInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AskRagReply_FileInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AskRagReply_FileInfoValidationError) ErrorName() string {
	return "AskRagReply_FileInfoValidationError"
}

// Error satisfies the builtin error interface
func (e AskRagReply_FileInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAskRagReply_FileInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AskRagReply_FileInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AskRagReply_FileInfoValidationError{}

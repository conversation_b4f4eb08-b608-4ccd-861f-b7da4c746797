syntax = "proto3";

package api.rag;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "gitlab.minum.cloud/innovationteam/ai-web/api/rag;rag";
option java_multiple_files = true;
option java_package = "api.rag";

service Rag {
  //	// 提问
  //	rpc AskRag (AskRagRequest) returns (stream AskRagReply){
  //		option (google.api.http) = {
  //			post: "/rag/ask"
  //			body: "*"
  //		};
  //	};

  // 重命名聊天
  rpc RenameChat(RenameChatRequest) returns (RenameChatReply) {
    option (google.api.http) = {
      post: "/rag/rename"
      body: "*"
    };
  }

  // 创建聊天
  rpc CreateChat(CreateChatRequest) returns (CreateChatReply) {
    option (google.api.http) = {
      post: "/rag/create"
      body: "*"
    };
  }

  // 删除聊天
  rpc DeleteChat(DeleteChatRequest) returns (DeleteChatReply) {
    option (google.api.http) = {
      post: "/rag/delete"
      body: "*"
    };
  }

  // 聊天详情
  rpc ChatDetail(ChatDetailRequest) returns (ChatDetailReply) {
    option (google.api.http) = {get: "/rag/detail"};
  }

  // 分页聊天
  rpc PageChat(PageChatRequest) returns (PageChatReply) {
    option (google.api.http) = {get: "/rag/page"};
  }

  // 全文搜索
  rpc FullTextSearch(FullTextSearchRequest) returns (FullTextSearchReply) {
    option (google.api.http) = {
      post: "/rag/search"
      body: "*"
    };
  }

  // 赞/踩回答
  rpc AgreeChatItem(AgreeChatItemRequest) returns (AgreeChatItemReply) {
    option (google.api.http) = {
      post: "/rag/agree"
      body: "*"
    };
  }

  // 更新对话推荐问题列表
  rpc UpdateChatItemSuggestQuestions(UpdateChatItemSuggestQuestionsRequest) returns (UpdateChatItemSuggestQuestionsReply) {
    option (google.api.http) = {
      post: "/rag/update/suggest/questions"
      body: "*"
    };
  }

  // 对话数查询
  rpc ChatCount(ChatCountRequest) returns (ChatCountReply) {}
}

message Chat {
  int64 chatID = 1;
  string name = 2;
  int64 agentID = 3;
  // 1:普通RAG 2:智能体
  int64 chatType = 4;
  string agentName = 5;
  string agentAvatar = 6;
  int64 knowledgeBaseType = 7;
  int64 modelType = 8;
  bool canInternetSearch = 9;
  int64 agentType = 10;
  string fallbackMsg = 11;
  google.protobuf.Timestamp createdAt = 12;
  // 深度思考开启状态 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
  int64 thinkingEnableStatus = 13;

  // 是否开启深度思考
  bool thinking = 14;
}

message ChatItem {
  int64 id = 1;
  int64 chatID = 2;
  int64 objectID = 3;
  int64 objectType = 4;
  string message = 5;
  repeated FileInfo refs = 6;
  int64 roundID = 7;
  string reason = 8;
  int64 agreeStatus = 9; // 1:赞同 2:点踩
  // 1:阻断  2.警告
  int64 hitAction = 10;
  string hitResponse = 11;
  bool hitContinueSend = 12;
  // 是否是网络搜索
  bool isInternetSearch = 13;
  repeated string suggestQuestions = 14;
}

// ask
message AskRagRequest {
  message FileInfo {
    int64 fileRelationID = 1;
    string title = 2;
    int64 size = 3;
    string mimeType = 4;
    int64 userID = 5;
    string entityTag = 6;
    string preEntityTag = 7;
    int64 index = 8;
    int64 chunkIndex = 9;
  }
  string question = 1 [(validate.rules).string = {min_len: 1}];
  int64 chatID = 2;
  int64 roundID = 3;
  repeated FileInfo files = 4;
}

message AskRagReply {
  message FileInfo {
    int64 fileRelationID = 1;
    string title = 2;
    int64 size = 3;
    string mimeType = 4;
    int64 userID = 5;
    string entityTag = 6;
    string preEntityTag = 7;
    int64 index = 8;
    int64 chunkIndex = 9;
  }
  string answer = 1;
  repeated FileInfo files = 2;
  // 0:回答中,1:回答完成
  int64 status = 3;
  int64 roundID = 4;
  int64 type = 5;
}

// create chat
message CreateChatRequest {
  string name = 1 [(validate.rules).string = {min_len: 1}];
  int64 agentID = 2;
  // 1:普通RAG 2:智能体
  int64 chatType = 3;
}

message CreateChatReply {
  int64 chatID = 1;
}

// rename chat
message RenameChatRequest {
  int64 chatID = 1 [(validate.rules).int64 = {gt: 0}];
  string name = 2 [(validate.rules).string = {min_len: 1}];
}

message RenameChatReply {}

// delete chat
message DeleteChatRequest {
  repeated int64 chatIDs = 1;
}

message DeleteChatReply {}

// chat detail
message ChatDetailRequest {
  int64 chatID = 1 [(validate.rules).int64 = {gt: 0}];
}

message ChatDetailReply {
  repeated ChatItem chatItems = 1;
}

message PageChatRequest {
  int64 pageNum = 1 [(validate.rules).int64 = {gt: 0}];
  int64 pageSize = 2 [(validate.rules).int64 = {
    gt: 0
    lte: 100
  }];
  int64 agentID = 3;
}

message PageChatReply {
  repeated Chat chats = 1;
  int64 total = 2;
}

message Documents {
  string text = 1;
  string entityTag = 2;
  string preEntityTag = 3;
  int64 fileRelationID = 4;
  google.protobuf.Timestamp updatedAt = 5;
  string title = 6;
  int64 userID = 7;
  string userName = 8;
  string fullPath = 9;
  repeated string tagNames = 10;
  string classPath = 14;
  string mimeType = 11;
  int64 size = 12;
  bool canDoAiProcess = 13;
  bool inKnowledgeBase = 15;
}

message FileInfo {
  int64 fileRelationID = 1;
  string title = 2;
  int64 size = 3;
  string mimeType = 4;
  int64 userID = 5;
  string entityTag = 6;
  string preEntityTag = 7;
  int64 index = 8;
  int64 chunkIndex = 9;
  repeated string images = 10;
  string fullPath = 11;
  int64 knowledgeBaseID = 12;
  string knowledgeBaseName = 13;
  int64 knowledgeBaseDataType = 14;
  string tableData = 15;
  string chartSchema = 16;
  int64 fromChatItemID = 17;
  int64 fromFileRelationID = 18;
}

message FullTextSearchRequest {
  string query = 1;
  int64 searchType = 2;
  string fileType = 3;
  repeated int64 ownerIDs = 4;
  google.protobuf.Timestamp startTime = 5;
  google.protobuf.Timestamp endTime = 6;
  int64 pageNum = 9;
  int64 pageSize = 10;
  string classPath = 11;
  string path = 12;
  bool filterSameFile = 13;
}

message FullTextSearchReply {
  string query = 1;
  repeated string tsQuery = 6;
  int64 pageNum = 2;
  int64 pageSize = 3;
  repeated Documents refs = 4;
  int64 total = 5;
}

message ChatCountRequest {
  google.protobuf.Timestamp startTime = 1;
  google.protobuf.Timestamp endTime = 2;
}

message ChatCountReply {
  int64 count = 1;
}

message AgreeChatItemRequest {
  // 1:赞同 2:点踩
  int64 agreeStatus = 1;
  int64 chatItemID = 2;
}

message AgreeChatItemReply {}

message UpdateChatItemSuggestQuestionsRequest {
  int64 chatItemID = 1;
  repeated string suggestQuestions = 2;
}

message UpdateChatItemSuggestQuestionsReply {}

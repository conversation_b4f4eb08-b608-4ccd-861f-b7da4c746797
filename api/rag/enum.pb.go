// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        (unknown)
// source: rag/enum.proto

package rag

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChatType int32

const (
	ChatType_ChatTypeUnknown ChatType = 0
	ChatType_ChatTypeRag     ChatType = 1
)

// Enum value maps for ChatType.
var (
	ChatType_name = map[int32]string{
		0: "ChatTypeUnknown",
		1: "ChatTypeRag",
	}
	ChatType_value = map[string]int32{
		"ChatTypeUnknown": 0,
		"ChatTypeRag":     1,
	}
)

func (x ChatType) Enum() *ChatType {
	p := new(ChatType)
	*p = x
	return p
}

func (x ChatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatType) Descriptor() protoreflect.EnumDescriptor {
	return file_rag_enum_proto_enumTypes[0].Descriptor()
}

func (ChatType) Type() protoreflect.EnumType {
	return &file_rag_enum_proto_enumTypes[0]
}

func (x ChatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatType.Descriptor instead.
func (ChatType) EnumDescriptor() ([]byte, []int) {
	return file_rag_enum_proto_rawDescGZIP(), []int{0}
}

type ChatObjectType int32

const (
	ChatObjectType_ChatObjectTypeUnknown ChatObjectType = 0
	ChatObjectType_ChatObjectTypeUser    ChatObjectType = 1
	ChatObjectType_ChatObjectTypeSystem  ChatObjectType = 2
)

// Enum value maps for ChatObjectType.
var (
	ChatObjectType_name = map[int32]string{
		0: "ChatObjectTypeUnknown",
		1: "ChatObjectTypeUser",
		2: "ChatObjectTypeSystem",
	}
	ChatObjectType_value = map[string]int32{
		"ChatObjectTypeUnknown": 0,
		"ChatObjectTypeUser":    1,
		"ChatObjectTypeSystem":  2,
	}
)

func (x ChatObjectType) Enum() *ChatObjectType {
	p := new(ChatObjectType)
	*p = x
	return p
}

func (x ChatObjectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatObjectType) Descriptor() protoreflect.EnumDescriptor {
	return file_rag_enum_proto_enumTypes[1].Descriptor()
}

func (ChatObjectType) Type() protoreflect.EnumType {
	return &file_rag_enum_proto_enumTypes[1]
}

func (x ChatObjectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatObjectType.Descriptor instead.
func (ChatObjectType) EnumDescriptor() ([]byte, []int) {
	return file_rag_enum_proto_rawDescGZIP(), []int{1}
}

var File_rag_enum_proto protoreflect.FileDescriptor

var file_rag_enum_proto_rawDesc = []byte{
	0x0a, 0x0e, 0x72, 0x61, 0x67, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x07, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x61, 0x67, 0x2a, 0x30, 0x0a, 0x08, 0x43, 0x68, 0x61,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x68, 0x61, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x68,
	0x61, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x61, 0x67, 0x10, 0x01, 0x2a, 0x5d, 0x0a, 0x0e, 0x43,
	0x68, 0x61, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a,
	0x15, 0x43, 0x68, 0x61, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x74,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x55, 0x73, 0x65, 0x72, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x74, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x10, 0x02, 0x42, 0x41, 0x0a, 0x07, 0x61, 0x70,
	0x69, 0x2e, 0x72, 0x61, 0x67, 0x50, 0x01, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x6d, 0x69, 0x6e, 0x75, 0x6d, 0x2e, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2f, 0x69, 0x6e, 0x6e, 0x6f,
	0x76, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x74, 0x65, 0x61, 0x6d, 0x2f, 0x61, 0x69, 0x2d, 0x77, 0x65,
	0x62, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x61, 0x67, 0x3b, 0x72, 0x61, 0x67, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_rag_enum_proto_rawDescOnce sync.Once
	file_rag_enum_proto_rawDescData = file_rag_enum_proto_rawDesc
)

func file_rag_enum_proto_rawDescGZIP() []byte {
	file_rag_enum_proto_rawDescOnce.Do(func() {
		file_rag_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_rag_enum_proto_rawDescData)
	})
	return file_rag_enum_proto_rawDescData
}

var file_rag_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_rag_enum_proto_goTypes = []any{
	(ChatType)(0),       // 0: api.rag.ChatType
	(ChatObjectType)(0), // 1: api.rag.ChatObjectType
}
var file_rag_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_rag_enum_proto_init() }
func file_rag_enum_proto_init() {
	if File_rag_enum_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_rag_enum_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_rag_enum_proto_goTypes,
		DependencyIndexes: file_rag_enum_proto_depIdxs,
		EnumInfos:         file_rag_enum_proto_enumTypes,
	}.Build()
	File_rag_enum_proto = out.File
	file_rag_enum_proto_rawDesc = nil
	file_rag_enum_proto_goTypes = nil
	file_rag_enum_proto_depIdxs = nil
}

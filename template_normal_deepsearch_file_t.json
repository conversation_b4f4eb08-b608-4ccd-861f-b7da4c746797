{"nodes": [{"data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"name": "question", "required": true, "type": "string"}], "trigger_parameters": [{"name": "question", "required": true, "type": "string"}]}, "id": "100001", "meta": {"position": {"x": -5.0622048232387264, "y": -268.91942046855735}}, "type": "1"}, {"data": {"nodeMeta": {"description": "从知识库中搜索相关的20个文件", "icon": "https://example.com/kb-icon.jpg", "subTitle": "知识库", "title": "知识库检索相关文件", "mainColor": "#FF9900"}, "inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "query"}], "settingOnError": {}}, "outputs": [{"name": "output", "type": "string"}], "version": "1"}, "id": "100010", "meta": {"position": {"x": 300, "y": -200}}, "type": "7"}, {"data": {"nodeMeta": {"description": "分割成块，并且rerank", "icon": "https://example.com/kb-icon.jpg", "subTitle": "筛选最关联的chunk", "title": "筛选最关联的chunk", "mainColor": "#FF9900"}, "inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "array<integer>", "value": {"content": {"blockID": "100001", "name": "file_relation_ids", "source": "block-output"}, "type": "ref"}}, "name": "file_relation_ids"}], "settingOnError": {}}, "outputs": [{"name": "output", "type": "string"}], "version": "1"}, "id": "1000066", "meta": {"position": {"x": 300, "y": -200}}, "type": "9"}, {"data": {"inputs": {"branches": [{"condition": {"conditions": [{"left": {"input": {"type": "integer", "value": {"content": {"blockID": "100001", "name": "designate_sheet_knowledge_base_id", "source": "block-output"}, "type": "ref"}}}, "operator": 1, "right": {"input": {"type": "integer", "value": {"content": 0, "type": "literal"}}}}], "logic": 2}}]}, "nodeMeta": {"description": "连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行否则分支", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Condition-v2.jpg", "mainColor": "#00B2B2", "subTitle": "选择器", "title": "选择器"}}, "id": "100022", "meta": {"position": {"x": 1003.4008038127196, "y": -500.14795762394647}}, "type": "8"}, {"data": {"nodeMeta": {"description": "指定sheet文件问答", "icon": "https://example.com/kb-icon.jpg", "subTitle": "指定sheet文件问答", "title": "指定sheet文件问答", "mainColor": "#FF9900"}, "inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "array<integer>", "value": {"content": {"blockID": "100001", "name": "file_relation_ids", "source": "block-output"}, "type": "ref"}}, "name": "file_relation_ids"}, {"input": {"type": "integer", "value": {"content": {"blockID": "100001", "name": "designate_sheet_knowledge_base_id", "source": "block-output"}, "type": "ref"}}, "name": "designate_sheet_knowledge_base_id"}, {"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "existing_table_data", "source": "block-output"}, "type": "ref"}}, "name": "existing_table_data"}], "settingOnError": {}}, "outputs": [{"name": "output", "type": "string"}], "version": "1"}, "id": "100066", "meta": {"position": {"x": 300, "y": -200}}, "type": "11"}, {"data": {"nodeMeta": {"description": "从密数万象中提取文件内容摘要", "icon": "https://example.com/kb-icon.jpg", "subTitle": "密数万象文件摘要提取", "title": "密数万象文件摘要提取", "mainColor": "#FF9900"}, "inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "array<integer>", "value": {"content": {"blockID": "100001", "name": "file_relation_ids", "source": "block-output"}, "type": "ref"}}, "name": "file_relation_ids"}], "settingOnError": {}}, "outputs": [{"name": "output", "type": "string"}], "version": "1"}, "id": "100006", "meta": {"position": {"x": 300, "y": -200}}, "type": "5"}, {"data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "string", "value": {"content": {"blockID": "100006", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "file_summary"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "1", "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "内部模型调用", "type": "literal"}}, "name": "modleName"}, {"input": {"type": "string", "value": {"content": "balance", "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0", "type": "literal"}}, "name": "temperature"}, {"input": {"type": "float", "value": {"content": "0.7", "type": "literal"}}, "name": "topP"}, {"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "integer", "value": {"content": "8192", "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "string", "value": {"content": "你是密数万象智能助手, 属于江苏大道云隐科技有限公司的产品。请参考以下文件内容信息: <file_summary>{{file_summary}}</file_summary> 用不超过2000个字回答以下问题: <question>{{question}}</question> 1.仔细阅读问题和参考内容。2.从参考中找出与问题相关的部分。3.根据相关部分构建答案，要确保答案完整且能准确回答问题。请在直接给出你的答案。4. 回答请简单直接。", "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "mainColor": "#5C62FF", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "id": "100008", "meta": {"position": {"x": 643.4008038127196, "y": -198.51641826352517}}, "type": "3"}, {"data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "string", "value": {"content": {"blockID": "100010", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "kb_search_result"}, {"input": {"type": "string", "value": {"content": {"blockID": "100011", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "sheet_search_result"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "1", "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "内部模型调用", "type": "literal"}}, "name": "modleName"}, {"input": {"type": "string", "value": {"content": "balance", "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0", "type": "literal"}}, "name": "temperature"}, {"input": {"type": "float", "value": {"content": "0.7", "type": "literal"}}, "name": "topP"}, {"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "integer", "value": {"content": "8192", "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "string", "value": {"content": "你是密数万象智能助手, 属于江苏大道云隐科技有限公司的产品。请参考以下文件内容信息: <kb_search_result>{{kb_search_result}}</kb_search_result> <sheet_search_result> {{sheet_search_result}} </sheet_search_result> 用不超过2000个字回答以下问题: <question>{{question}}</question> 1.仔细阅读问题和参考内容。2.从参考中找出与问题相关的部分。3.根据相关部分构建答案，要确保答案完整且能准确回答问题。请在直接给出你的答案。4. 若<sheet_search_result>不为空，则代表生成图表成功。", "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "mainColor": "#5C62FF", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "id": "100009", "meta": {"position": {"x": 643.4008038127196, "y": -198.51641826352517}}, "type": "3"}, {"data": {"nodeMeta": {"description": "从问数知识库里查询出图表", "icon": "https://example.com/kb-icon.jpg", "subTitle": "查询问数知识库", "title": "查询问数知识库", "mainColor": "#FF9900"}, "inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}], "settingOnError": {}}, "outputs": [{"name": "output", "type": "string"}], "version": "1"}, "id": "100011", "meta": {"position": {"x": 300, "y": -200}}, "type": "10"}, {"data": {"inputs": {"branches": [{"condition": {"conditions": [{"left": {"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "file_relation_ids", "source": "block-output"}, "type": "ref"}}}, "operator": 7, "right": {"input": {"type": "string", "value": {"content": "0", "type": "literal"}}}}], "logic": 2}}]}, "nodeMeta": {"description": "连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行否则分支", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Condition-v2.jpg", "mainColor": "#00B2B2", "subTitle": "选择器", "title": "选择器"}}, "id": "100002", "meta": {"position": {"x": 1003.4008038127196, "y": -500.14795762394647}}, "type": "8"}, {"data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100010", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "file_search_result"}, {"input": {"type": "string", "value": {"content": {"blockID": "100008", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "output_from_direct_files"}], "terminatePlan": "returnVariables"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "id": "999999", "meta": {"position": {"x": 1554.125851898408, "y": -184.56641826352518}}, "type": "2"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "100002", "sourcePortID": ""}, {"sourceNodeID": "100002", "targetNodeID": "100010", "sourcePortID": "true"}, {"sourceNodeID": "100002", "targetNodeID": "100022", "sourcePortID": "false"}, {"sourceNodeID": "100022", "targetNodeID": "100006", "sourcePortID": "true"}, {"sourceNodeID": "100022", "targetNodeID": "100066", "sourcePortID": "false"}, {"sourceNodeID": "100066", "targetNodeID": "999999", "sourcePortID": ""}, {"sourceNodeID": "100010", "targetNodeID": "100011", "sourcePortID": ""}, {"sourceNodeID": "100011", "targetNodeID": "100009", "sourcePortID": ""}, {"sourceNodeID": "100009", "targetNodeID": "999999", "sourcePortID": ""}, {"sourceNodeID": "100006", "targetNodeID": "100008", "sourcePortID": ""}, {"sourceNodeID": "100008", "targetNodeID": "999999", "sourcePortID": ""}], "versions": {"loop": "v2"}}
#!/usr/bin/env just --justfile

[private]
default:
    @just -l

run:
   go run ./cmd/server

# tidy mod
tidy:
  go mod tidy -v
  cd api && go mod tidy -v

# init env
init:
    make init

doc-server:
    sfs -p 9524


# generate internal proto
config:
    make config

# generate api proto
api:
    make api

# build
build:
    make build

# generate
generate:
    @make generate

ent name:
    @cd internal/data && ent new {{name}}

add file:
    kratos proto add {{file}}

proto file:
    kratos proto server {{file}} -t internal/service
    just api

doc dir:
    cd api && buf generate --path ./{{dir}} --template ./buf.openapi.gen.yaml
    rm -rf doc/{{dir}}
    mkdir -p doc/{{dir}} && mv doc/openapi.yaml doc/{{dir}}/openapi.yaml

# go vet project
vet:
    go vet ./...

# generate all
all: api config generate

# 清空模板
remove_greeter:
    @rm -rf api/helloworld/v1/greeter.proto
    @rm -rf api/helloworld/v1/greeter.pb.go
    @rm -rf api/helloworld/v1/greeter.pb.validate.go
    @rm -rf api/helloworld/v1/greeter_http.pb.go
    @rm -rf internal/biz/greeter.go
    @sed -i 's/NewGreeterUsecase,//g' internal/biz/biz.go
    @sed -i 's/NewGreeterUsecase//g' internal/biz/biz.go
    @rm -rf internal/data/repo_greeter.go
    @sed -i 's/NewGreeterRepo,//g' internal/data/data.go
    @sed -i 's/NewGreeterRepo//g' internal/data/data.go
    @rm -rf internal/service/greeter.go
    @sed -i 's/NewGreeterService,//g' internal/service/service.go
    @sed -i 's/NewGreeterService//g' internal/service/service.go
    @sed -i 's/, greeter \*service\.GreeterService//g' internal/server/grpc.go
    @sed -i 's/, greeter \*service\.GreeterService//g' internal/server/http.go
    @sed -i 's/, greeter \*service\.GreeterService//g' internal/server/kafka.go
    @sed -i 's/v1.RegisterGreeterServer(srv, greeter)//g' internal/server/grpc.go
    @sed -i 's/v1.RegisterGreeterHTTPServer(srv, greeter)//g' internal/server/http.go
    @sed -i 's/_ = kafka.RegisterSubscriber(srv, ctx, "sayHello", "greeter", false, greeter.SayHelloForKafka)//g' internal/server/kafka.go
    @sed -i 's/greeter//g' internal/server/kafka.go
    @go fmt ./...

# format and check
check:
    @make check


infra:
    docker compose -f docker-compose.infra.dev.yaml up
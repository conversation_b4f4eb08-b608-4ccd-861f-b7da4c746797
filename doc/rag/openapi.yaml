# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Rag API
    version: 0.0.1
paths:
    /rag/agree:
        post:
            tags:
                - Rag
            description: 赞/踩回答
            operationId: Rag_AgreeChatItem
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AgreeChatItemRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AgreeChatItemReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /rag/create:
        post:
            tags:
                - Rag
            description: 创建聊天
            operationId: Rag_CreateChat
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateChatRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateChatReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /rag/delete:
        post:
            tags:
                - Rag
            description: 删除聊天
            operationId: Rag_DeleteChat
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DeleteChatRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DeleteChatReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /rag/detail:
        get:
            tags:
                - Rag
            description: 聊天详情
            operationId: Rag_ChatDetail
            parameters:
                - name: chatID
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ChatDetailReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /rag/page:
        get:
            tags:
                - Rag
            description: 分页聊天
            operationId: Rag_PageChat
            parameters:
                - name: pageNum
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: agentID
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PageChatReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /rag/rename:
        post:
            tags:
                - Rag
            description: 重命名聊天
            operationId: Rag_RenameChat
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/RenameChatRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/RenameChatReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /rag/search:
        post:
            tags:
                - Rag
            description: 全文搜索
            operationId: Rag_FullTextSearch
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/FullTextSearchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/FullTextSearchReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /rag/update/suggest/questions:
        post:
            tags:
                - Rag
            description: 更新对话推荐问题列表
            operationId: Rag_UpdateChatItemSuggestQuestions
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateChatItemSuggestQuestionsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UpdateChatItemSuggestQuestionsReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        AgreeChatItemReply:
            type: object
            properties: {}
        AgreeChatItemRequest:
            type: object
            properties:
                agreeStatus:
                    type: integer
                    description: 1:赞同 2:点踩
                    format: int64
                chatItemID:
                    type: integer
                    format: int64
        Chat:
            type: object
            properties:
                chatID:
                    type: integer
                    format: int64
                name:
                    type: string
                agentID:
                    type: integer
                    format: int64
                chatType:
                    type: integer
                    description: 1:普通RAG 2:智能体
                    format: int64
                agentName:
                    type: string
                agentAvatar:
                    type: string
                knowledgeBaseType:
                    type: integer
                    format: int64
                modelType:
                    type: integer
                    format: int64
                canInternetSearch:
                    type: boolean
                agentType:
                    type: integer
                    format: int64
                fallbackMsg:
                    type: string
                createdAt:
                    type: string
                    format: date-time
                thinkingEnableStatus:
                    type: integer
                    description: 深度思考开启状态 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
                    format: int64
                thinking:
                    type: boolean
                    description: 是否开启深度思考
        ChatDetailReply:
            type: object
            properties:
                chatItems:
                    type: array
                    items:
                        $ref: '#/components/schemas/ChatItem'
        ChatItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                chatID:
                    type: integer
                    format: int64
                objectID:
                    type: integer
                    format: int64
                objectType:
                    type: integer
                    format: int64
                message:
                    type: string
                refs:
                    type: array
                    items:
                        $ref: '#/components/schemas/FileInfo'
                roundID:
                    type: integer
                    format: int64
                reason:
                    type: string
                agreeStatus:
                    type: integer
                    format: int64
                hitAction:
                    type: integer
                    description: 1:阻断  2.警告
                    format: int64
                hitResponse:
                    type: string
                hitContinueSend:
                    type: boolean
                isInternetSearch:
                    type: boolean
                    description: 是否是网络搜索
                suggestQuestions:
                    type: array
                    items:
                        type: string
        CreateChatReply:
            type: object
            properties:
                chatID:
                    type: integer
                    format: int64
        CreateChatRequest:
            type: object
            properties:
                name:
                    type: string
                agentID:
                    type: integer
                    format: int64
                chatType:
                    type: integer
                    description: 1:普通RAG 2:智能体
                    format: int64
            description: create chat
        DeleteChatReply:
            type: object
            properties: {}
        DeleteChatRequest:
            type: object
            properties:
                chatIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
            description: delete chat
        Documents:
            type: object
            properties:
                text:
                    type: string
                entityTag:
                    type: string
                preEntityTag:
                    type: string
                fileRelationID:
                    type: integer
                    format: int64
                updatedAt:
                    type: string
                    format: date-time
                title:
                    type: string
                userID:
                    type: integer
                    format: int64
                userName:
                    type: string
                fullPath:
                    type: string
                tagNames:
                    type: array
                    items:
                        type: string
                classPath:
                    type: string
                mimeType:
                    type: string
                size:
                    type: integer
                    format: int64
                canDoAiProcess:
                    type: boolean
                inKnowledgeBase:
                    type: boolean
        FileInfo:
            type: object
            properties:
                fileRelationID:
                    type: integer
                    format: int64
                title:
                    type: string
                size:
                    type: integer
                    format: int64
                mimeType:
                    type: string
                userID:
                    type: integer
                    format: int64
                entityTag:
                    type: string
                preEntityTag:
                    type: string
                index:
                    type: integer
                    format: int64
                chunkIndex:
                    type: integer
                    format: int64
                images:
                    type: array
                    items:
                        type: string
                fullPath:
                    type: string
                knowledgeBaseID:
                    type: integer
                    format: int64
                knowledgeBaseName:
                    type: string
                knowledgeBaseDataType:
                    type: integer
                    format: int64
                tableData:
                    type: string
                chartSchema:
                    type: string
                fromChatItemID:
                    type: integer
                    format: int64
                fromFileRelationID:
                    type: integer
                    format: int64
        FullTextSearchReply:
            type: object
            properties:
                query:
                    type: string
                tsQuery:
                    type: array
                    items:
                        type: string
                pageNum:
                    type: integer
                    format: int64
                pageSize:
                    type: integer
                    format: int64
                refs:
                    type: array
                    items:
                        $ref: '#/components/schemas/Documents'
                total:
                    type: integer
                    format: int64
        FullTextSearchRequest:
            type: object
            properties:
                query:
                    type: string
                searchType:
                    type: integer
                    format: int64
                fileType:
                    type: string
                ownerIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
                startTime:
                    type: string
                    format: date-time
                endTime:
                    type: string
                    format: date-time
                pageNum:
                    type: integer
                    format: int64
                pageSize:
                    type: integer
                    format: int64
                classPath:
                    type: string
                path:
                    type: string
                filterSameFile:
                    type: boolean
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        PageChatReply:
            type: object
            properties:
                chats:
                    type: array
                    items:
                        $ref: '#/components/schemas/Chat'
                total:
                    type: integer
                    format: int64
        RenameChatReply:
            type: object
            properties: {}
        RenameChatRequest:
            type: object
            properties:
                chatID:
                    type: integer
                    format: int64
                name:
                    type: string
            description: rename chat
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        UpdateChatItemSuggestQuestionsReply:
            type: object
            properties: {}
        UpdateChatItemSuggestQuestionsRequest:
            type: object
            properties:
                chatItemID:
                    type: integer
                    format: int64
                suggestQuestions:
                    type: array
                    items:
                        type: string
tags:
    - name: Rag

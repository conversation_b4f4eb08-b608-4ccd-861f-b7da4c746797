# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Aimodel API
    version: 0.0.1
paths:
    /aimodel/create:
        post:
            tags:
                - Aimodel
            description: 创建 Aimodel
            operationId: Aimodel_CreateAimodel
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateAimodelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateAimodelReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /aimodel/delete:
        post:
            tags:
                - Aimodel
            description: 删除 Aimodel
            operationId: Aimodel_DeleteAimodel
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DeleteAimodelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DeleteAimodelReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /aimodel/getAiModelCallCount:
        post:
            tags:
                - Aimodel
            description: 按天获取模型调用次数
            operationId: Aimodel_GetAiModelCallCountTotalByTime
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetAiModelCallCountByTimeRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAiModelCallCountByTimeReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /aimodel/getAiModelCallDetailByDay:
        post:
            tags:
                - Aimodel
            description: 获取模型请求、tokens消耗详情（按天）
            operationId: Aimodel_GetAiModelCallDetailByDay
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetAiModelCallDetailByDayRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAiModelCallDetailByDayReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /aimodel/list:
        post:
            tags:
                - Aimodel
            description: 列表
            operationId: Aimodel_ListAimodel
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetAimodelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAimodelReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /aimodel/listExternalModel:
        post:
            tags:
                - Aimodel
            description: 外部模型列表
            operationId: Aimodel_ListExternalModel
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ListExternalModelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListExternalModelReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /aimodel/pageAiModelUsageByAgentID:
        post:
            tags:
                - Aimodel
            description: 按智能体分页获取模型用量详情
            operationId: Aimodel_PageAiModelUsageByAgentID
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/PageAiModelUsageByAgentIDRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PageAiModelUsageByAgentIDReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /aimodel/update:
        post:
            tags:
                - Aimodel
            description: 更新 Aimodel
            operationId: Aimodel_UpdateAimodel
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateAimodelRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UpdateAimodelReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        AiModel:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                modelName:
                    type: string
                model:
                    type: integer
                    description: 1. 腾讯元宝
                    format: int64
                apiKey:
                    type: string
                createdAt:
                    type: string
                    format: date-time
                avatarUrl:
                    type: string
                canInternetSearch:
                    type: boolean
                balance:
                    type: number
                    format: double
                thinkingEnableStatus:
                    type: integer
                    description: 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
                    format: enum
                modelDetailName:
                    type: string
                backgroundUrl:
                    type: string
                    description: 后台地址
        AiModelDetail:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                modelName:
                    type: string
                url:
                    type: string
                canInternetSearch:
                    type: boolean
                avatarUrl:
                    type: string
        CreateAimodelReply:
            type: object
            properties: {}
        CreateAimodelRequest:
            type: object
            properties:
                modelName:
                    type: string
                model:
                    type: integer
                    description: 查询外部模型列表的id
                    format: int64
                apiKey:
                    type: string
            description: =========================== create
        DeleteAimodelReply:
            type: object
            properties: {}
        DeleteAimodelRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
            description: =========================== delete
        GetAiModelCallCountByTimeReply:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAiModelCallCountByTimeReplyItem'
        GetAiModelCallCountByTimeReplyItem:
            type: object
            properties:
                time:
                    type: string
                    format: date-time
                count:
                    type: integer
                    format: int64
        GetAiModelCallCountByTimeRequest:
            type: object
            properties:
                startTime:
                    type: string
                    format: date-time
                endTime:
                    type: string
                    format: date-time
        GetAiModelCallDetailByDayReply:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAiModelCallDetailByDayReplyItem'
        GetAiModelCallDetailByDayReplyItem:
            type: object
            properties:
                time:
                    type: string
                    format: date-time
                modelCallDetails:
                    type: array
                    items:
                        $ref: '#/components/schemas/ModelCallDetail'
        GetAiModelCallDetailByDayRequest:
            type: object
            properties:
                startTime:
                    type: string
                    format: date-time
                endTime:
                    type: string
                    format: date-time
        GetAimodelReply:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/AiModel'
        GetAimodelRequest:
            type: object
            properties:
                modelName:
                    type: string
            description: =========================== get
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListExternalModelReply:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/AiModelDetail'
        ListExternalModelRequest:
            type: object
            properties: {}
        ModelCallDetail:
            type: object
            properties:
                modelName:
                    type: string
                apiCallCount:
                    type: integer
                    format: int64
                apiErrCount:
                    type: integer
                    format: int64
                promptTokens:
                    type: integer
                    format: int64
                completionTokens:
                    type: integer
                    format: int64
        PageAiModelUsageByAgentIDReply:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/PageAiModelUsageByAgentIDReplyItem'
                total:
                    type: integer
                    format: int64
        PageAiModelUsageByAgentIDReplyItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                modelName:
                    type: string
                modelGatewayName:
                    type: string
                userId:
                    type: integer
                    format: int64
                userName:
                    type: string
                agentId:
                    type: integer
                    format: int64
                agentName:
                    type: string
                question:
                    type: string
                answer:
                    type: string
                promptTokens:
                    type: integer
                    format: int64
                completionTokens:
                    type: integer
                    format: int64
                requestStatus:
                    type: integer
                    description: '0: 未处理, 1: 成功, 2: 失败'
                    format: int64
                errorCode:
                    type: string
                agentAvatar:
                    type: string
                    description: 智能体头像
                userAvatar:
                    type: string
                    description: 用户头像
                modelDetailAvatar:
                    type: string
                    description: 模型头像
        PageAiModelUsageByAgentIDRequest:
            type: object
            properties:
                pageNum:
                    type: integer
                    format: int64
                pageSize:
                    type: integer
                    format: int64
                agentId:
                    type: integer
                    format: int64
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        UpdateAimodelReply:
            type: object
            properties: {}
        UpdateAimodelRequest:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                modelName:
                    type: string
                model:
                    type: integer
                    description: 1. 腾讯元宝
                    format: int64
                apiKey:
                    type: string
            description: =========================== update
tags:
    - name: Aimodel

# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Filecategory API
    version: 0.0.1
paths:
    /filecategory/create:
        post:
            tags:
                - Filecategory
            description: 创建 Filecategory
            operationId: Filecategory_CreateFilecategory
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateFilecategoryRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateFilecategoryReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /filecategory/get:
        get:
            tags:
                - Filecategory
            description: 查询 Filecategory
            operationId: Filecategory_GetFilecategory
            parameters:
                - name: ids
                  in: query
                  schema:
                    type: array
                    items:
                        type: integer
                        format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetFilecategoryReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /filecategory/page:
        get:
            tags:
                - Filecategory
            description: 分页 Filecategory
            operationId: Filecategory_PageFilecategory
            parameters:
                - name: pageNum
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PageFilecategoryReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        CreateFilecategoryItem:
            type: object
            properties:
                categoryName:
                    type: array
                    items:
                        type: string
                fileRelationID:
                    type: integer
                    format: int64
        CreateFilecategoryReply:
            type: object
            properties: {}
        CreateFilecategoryRequest:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/CreateFilecategoryItem'
            description: =========================== create
        GetFilecategoryReply:
            type: object
            properties: {}
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        PageFilecategoryReply:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                records:
                    type: array
                    items:
                        $ref: '#/components/schemas/PageFilecategoryReplyItem'
        PageFilecategoryReplyItem:
            type: object
            properties:
                categoryID:
                    type: integer
                    format: int64
                categoryName:
                    type: string
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: Filecategory

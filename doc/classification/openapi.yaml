# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Classification API
    version: 0.0.1
paths:
    /classification/all:
        get:
            tags:
                - Classification
            description: 查询所有分类及文件数量
            operationId: Classification_GetAllClassification
            parameters:
                - name: tagName
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAllClassificationReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /classification/fileTags:
        post:
            tags:
                - Classification
            description: 查询文件分类信息
            operationId: Classification_GetFileClassificationTags
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetFileClassificationTagsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetFileClassificationTagsReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        ClassificationFileTag:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                fileRelationID:
                    type: integer
                    format: int64
                preEntityTag:
                    type: string
                entityTag:
                    type: string
                fileName:
                    type: string
                fileType:
                    type: string
                userID:
                    type: integer
                    format: int64
                userName:
                    type: string
                deptID:
                    type: integer
                    format: int64
                deptName:
                    type: string
                path:
                    type: string
                tags:
                    type: array
                    items:
                        type: string
                securityLevel:
                    type: integer
                    format: int32
        ClassificationTag:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
                subClassificationTags:
                    type: array
                    items:
                        $ref: '#/components/schemas/SubClassificationTag'
            description: 一级分类的消息
        GetAllClassificationReply:
            type: object
            properties:
                classificationTags:
                    type: array
                    items:
                        $ref: '#/components/schemas/ClassificationTag'
        GetFileClassificationTagsReply:
            type: object
            properties:
                classificationFileTags:
                    type: array
                    items:
                        $ref: '#/components/schemas/ClassificationFileTag'
        GetFileClassificationTagsRequest:
            type: object
            properties:
                item:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetFileClassificationTagsRequestItem'
        GetFileClassificationTagsRequestItem:
            type: object
            properties:
                fileRelationID:
                    type: integer
                    format: int64
                preEntityTag:
                    type: string
                entityTag:
                    type: string
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        SubClassificationTag:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
            description: 二级分类的消息
tags:
    - name: Classification

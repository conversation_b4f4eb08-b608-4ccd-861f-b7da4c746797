# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: KnowledgeBase API
    version: 0.0.1
paths:
    /knowledgeBase/addFile:
        post:
            tags:
                - KnowledgeBase
            description: 知识库添加文件
            operationId: KnowledgeBase_AddKnowledgeBaseFile
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddKnowledgeBaseFileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AddKnowledgeBaseFileReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/addFileFromSearch:
        post:
            tags:
                - KnowledgeBase
            description: 知识库添加文件 根据搜索结果添加
            operationId: KnowledgeBase_AddKnowledgeBaseFileFromSearch
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/AddKnowledgeBaseFileFromSearchRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/AddKnowledgeBaseFileFromSearchReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/changeKnowledgeBaseOwner:
        post:
            tags:
                - KnowledgeBase
            description: 转移知识库所有者
            operationId: KnowledgeBase_ChangeKnowledgeBaseOwner
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ChangeKnowledgeBaseOwnerRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ChangeKnowledgeBaseOwnerReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/create:
        post:
            tags:
                - KnowledgeBase
            description: 创建知识库
            operationId: KnowledgeBase_CreateKnowledgeBase
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateKnowledgeBaseRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateKnowledgeBaseReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/delete:
        post:
            tags:
                - KnowledgeBase
            description: 删除知识库
            operationId: KnowledgeBase_DeleteKnowledgeBase
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DeleteKnowledgeBaseRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DeleteKnowledgeBaseReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/deleteFile:
        post:
            tags:
                - KnowledgeBase
            description: 知识库删除文件
            operationId: KnowledgeBase_DeleteKnowledgeBaseFile
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DeleteKnowledgeBaseFileRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DeleteKnowledgeBaseFileReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/get:
        post:
            tags:
                - KnowledgeBase
            description: 获取知识库
            operationId: KnowledgeBase_GetKnowledgeBases
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GetKnowledgeBasesRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetKnowledgeBasesReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/page:
        get:
            tags:
                - KnowledgeBase
            description: 分页获取知识库
            operationId: KnowledgeBase_PageKnowledgeBase
            parameters:
                - name: pageNum
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: name
                  in: query
                  description: 知识库名称
                  schema:
                    type: string
                - name: all
                  in: query
                  description: 是否全部 false:我可编辑的
                  schema:
                    type: boolean
                - name: dataType
                  in: query
                  description: 数据类型 0全部 1文档 2表格
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PageKnowledgeBaseReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/pageFile:
        get:
            tags:
                - KnowledgeBase
            description: 分页获取知识库文件
            operationId: KnowledgeBase_PageKnowledgeBaseFile
            parameters:
                - name: pageNum
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: id
                  in: query
                  description: 知识库id
                  schema:
                    type: integer
                    format: int64
                - name: fileName
                  in: query
                  description: 文件名称
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PageKnowledgeBaseFileReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/update:
        post:
            tags:
                - KnowledgeBase
            description: 更新知识库
            operationId: KnowledgeBase_UpdateKnowledgeBase
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateKnowledgeBaseRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UpdateKnowledgeBaseReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /knowledgeBase/updateFileMetadata:
        post:
            tags:
                - KnowledgeBase
            description: 更新知识库文件元数据
            operationId: KnowledgeBase_UpdateKnowledgeBaseFileMetadata
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateKnowledgeBaseFileMetadataRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UpdateKnowledgeBaseFileMetadataReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        AddKnowledgeBaseFileFromSearchReply:
            type: object
            properties: {}
        AddKnowledgeBaseFileFromSearchRequest:
            required:
                - id
            type: object
            properties:
                id:
                    type: integer
                    description: 知识库id
                    format: int64
                query:
                    type: string
                searchType:
                    type: integer
                    format: int64
                fileType:
                    type: string
                ownerIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
                startTime:
                    type: string
                    format: date-time
                endTime:
                    type: string
                    format: date-time
                classPath:
                    type: string
                path:
                    type: string
            description: AddKnowledgeBaseFileFromSearch
        AddKnowledgeBaseFileReply:
            type: object
            properties: {}
        AddKnowledgeBaseFileRequest:
            required:
                - id
                - fileRelationIDs
            type: object
            properties:
                id:
                    type: integer
                    description: 知识库id
                    format: int64
                fileRelationIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 文件ids
                skipQuickCreate:
                    type: boolean
                    description: 前端忽略这个参数，仅rpc使用
            description: AddKnowledgeBaseFile
        ChangeKnowledgeBaseOwnerReply:
            type: object
            properties: {}
        ChangeKnowledgeBaseOwnerRequest:
            required:
                - newUserID
            type: object
            properties:
                ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 知识库id
                newUserID:
                    type: integer
                    description: 转移至用户id
                    format: int64
            description: ChangeKnowledgeBaseOwner
        CreateKnowledgeBaseReply:
            type: object
            properties:
                id:
                    type: integer
                    description: 知识库id
                    format: int64
        CreateKnowledgeBaseRequest:
            required:
                - name
                - public
                - dataType
                - userID
            type: object
            properties:
                name:
                    type: string
                    description: 知识库名称
                public:
                    type: boolean
                    description: 是否公开
                dataType:
                    type: integer
                    description: 数据类型 1文档 2表格
                    format: enum
                userID:
                    type: integer
                    description: 拥有者用户id
                    format: int64
                managerUserIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 管理者用户id
                editableUserIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可编辑用户ids
            description: CreateKnowledgeBase
        DeleteKnowledgeBaseFileReply:
            type: object
            properties: {}
        DeleteKnowledgeBaseFileRequest:
            required:
                - id
                - fileRelationIDs
            type: object
            properties:
                id:
                    type: integer
                    description: 知识库id
                    format: int64
                fileRelationIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 文件ids
            description: DeleteKnowledgeBaseFile
        DeleteKnowledgeBaseReply:
            type: object
            properties: {}
        DeleteKnowledgeBaseRequest:
            required:
                - id
            type: object
            properties:
                id:
                    type: integer
                    description: 知识库id
                    format: int64
            description: DeleteKnowledgeBase
        GetKnowledgeBasesReply:
            type: object
            properties:
                records:
                    type: array
                    items:
                        $ref: '#/components/schemas/KnowledgeBaseInfo'
        GetKnowledgeBasesRequest:
            type: object
            properties:
                ids:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 知识库ids
            description: GetKnowledgeBases
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        KnowledgeBaseInfo:
            type: object
            properties:
                id:
                    type: integer
                    description: 知识库id
                    format: int64
                name:
                    type: string
                    description: 知识库名称
                public:
                    type: boolean
                    description: 是否公开
                dataType:
                    type: integer
                    description: 数据类型 1文档 2表格
                    format: enum
                userID:
                    type: integer
                    description: 拥有者用户id
                    format: int64
                userName:
                    type: string
                    description: 拥有者用户名
                userAvatar:
                    type: string
                    description: 拥有者头像
                managers:
                    type: array
                    items:
                        $ref: '#/components/schemas/KnowledgeBaseInfo_UserInfo'
                    description: 可管理用户
                editableUsers:
                    type: array
                    items:
                        $ref: '#/components/schemas/KnowledgeBaseInfo_UserInfo'
                    description: 可编辑用户ids
                createdAt:
                    type: string
                    description: 创建时间
                    format: date-time
        KnowledgeBaseInfo_UserInfo:
            type: object
            properties:
                userID:
                    type: integer
                    description: 拥有者用户id
                    format: int64
                userName:
                    type: string
                    description: 拥有者用户名
                userAvatar:
                    type: string
                    description: 拥有者头像
        PageKnowledgeBaseFileReply:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                records:
                    type: array
                    items:
                        $ref: '#/components/schemas/PageKnowledgeBaseFileReplyItem'
        PageKnowledgeBaseFileReplyItem:
            type: object
            properties:
                dataType:
                    type: integer
                    description: 数据类型 1文档 2表格
                    format: enum
                metadata:
                    type: string
                    description: 元数据 excel_schema
                fileRelationID:
                    type: integer
                    description: 文件id
                    format: int64
                fileName:
                    type: string
                    description: 文件名称
                filePath:
                    type: string
                    description: 文件路径
                fileType:
                    type: string
                    description: 文件类型
                fileSize:
                    type: integer
                    description: 文件大小
                    format: int64
                preEntityTag:
                    type: string
                    description: preEntityTag
                entityTag:
                    type: string
                    description: entityTag
                fileStatus:
                    type: integer
                    description: 文件状态 0等待 1处理中 2成功 3失败
                    format: enum
                failedReason:
                    type: string
                    description: 失败原因
                fileCategories:
                    type: array
                    items:
                        type: string
                    description: 文件分类
                createdAt:
                    type: string
                    description: 创建时间
                    format: date-time
        PageKnowledgeBaseReply:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                records:
                    type: array
                    items:
                        $ref: '#/components/schemas/KnowledgeBaseInfo'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        UpdateKnowledgeBaseFileMetadataReply:
            type: object
            properties: {}
        UpdateKnowledgeBaseFileMetadataRequest:
            required:
                - id
                - fileRelationID
            type: object
            properties:
                id:
                    type: integer
                    description: 知识库id
                    format: int64
                fileRelationID:
                    type: integer
                    description: 文件id
                    format: int64
                metadata:
                    type: string
                    description: 元数据
            description: UpdateKnowledgeBaseFileMetadata
        UpdateKnowledgeBaseReply:
            type: object
            properties: {}
        UpdateKnowledgeBaseRequest:
            required:
                - id
                - name
                - public
            type: object
            properties:
                id:
                    type: integer
                    description: 知识库id
                    format: int64
                name:
                    type: string
                    description: 知识库名称
                public:
                    type: boolean
                    description: 是否公开
                editableUserIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可编辑用户ids
                ownerOption:
                    $ref: '#/components/schemas/UpdateKnowledgeBaseRequest_OwnerOption'
            description: UpdateKnowledgeBase
        UpdateKnowledgeBaseRequest_OwnerOption:
            type: object
            properties:
                newUserID:
                    type: integer
                    format: int64
                managerUserIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
tags:
    - name: KnowledgeBase

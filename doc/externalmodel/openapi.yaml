# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ExternalModel API
    version: 0.0.1
paths:
    /externalModel/config:
        get:
            tags:
                - ExternalModel
            description: 获取外部模型配置
            operationId: ExternalModel_GetExternalModelConfig
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetExternalModelConfigReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /externalModel/page:
        get:
            tags:
                - ExternalModel
            description: 分页获取外部模型使用情况
            operationId: ExternalModel_PageExternalModelUsage
            parameters:
                - name: pageNum
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: startTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: startTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
                - name: endTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: endTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
                - name: userName
                  in: query
                  description: 用户名
                  schema:
                    type: string
                - name: deptName
                  in: query
                  description: 部门名
                  schema:
                    type: string
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PageExternalModelUsageReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /externalModel/upload:
        post:
            tags:
                - ExternalModel
            description: 上传外部模型使用情况
            operationId: ExternalModel_UploadExternalModelUsage
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UploadExternalModelUsageRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UploadExternalModelUsageReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        ExternalModelConfig:
            type: object
            properties:
                parseType:
                    type: string
                    description: 解析类型 file, chat
                modelName:
                    type: string
                    description: 模型名称 通义千问 腾讯元宝 豆包 deepseek
                host:
                    type: string
                    description: host
                preSchema:
                    type: string
                    description: schema前缀
                parseRule:
                    type: string
                    description: 解析规则 例 message.content.text
                sessionParseRule:
                    type: string
                    description: session 解析规则, 仅在 parse_type 为 chat 时存在，例 conversation_id
        ExternalModelFile:
            type: object
            properties:
                fileRelationID:
                    type: integer
                    format: int64
                preEntityTag:
                    type: string
                entityTag:
                    type: string
                name:
                    type: string
                fullPath:
                    type: string
                size:
                    type: integer
                    format: int64
                mimeType:
                    type: string
                level:
                    type: integer
                    format: int64
                classPath:
                    type: string
        GetExternalModelConfigReply:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/ExternalModelConfig'
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        PageExternalModelUsageReply:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                records:
                    type: array
                    items:
                        $ref: '#/components/schemas/PageExternalModelUsageReplyItem'
        PageExternalModelUsageReplyItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                modelName:
                    type: string
                modelAvatar:
                    type: string
                question:
                    type: string
                questionTag:
                    type: string
                files:
                    type: array
                    items:
                        $ref: '#/components/schemas/ExternalModelFile'
                userID:
                    type: integer
                    format: int64
                userName:
                    type: string
                userAvatar:
                    type: string
                deptID:
                    type: integer
                    format: int64
                deptName:
                    type: string
                pcName:
                    type: string
                happenedAt:
                    type: string
                    format: date-time
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        UploadExternalModelUsageReply:
            type: object
            properties: {}
        UploadExternalModelUsageRequest:
            type: object
            properties:
                modelName:
                    type: string
                question:
                    type: string
                files:
                    type: array
                    items:
                        $ref: '#/components/schemas/ExternalModelFile'
                pcName:
                    type: string
                happenedAt:
                    type: string
                    format: date-time
tags:
    - name: ExternalModel

# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Agent API
    version: 0.0.1
paths:
    /agent/all:
        get:
            tags:
                - Agent
            description: 查询所有Agent id/anme
            operationId: Agent_GetAllAgentInfo
            parameters:
                - name: modelType
                  in: query
                  description: 1. 私有模型 2. 网关模型
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAllAgentInfoReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/checkQuestionSecurity:
        post:
            tags:
                - Agent
            description: 校验问题安全
            operationId: Agent_CheckQuestionSecurity
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CheckQuestionSecurityRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CheckQuestionSecurityReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/create:
        post:
            tags:
                - Agent
            description: 创建 Agent
            operationId: Agent_CreateAgent
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateAgentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/CreateAgentReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/defaultAvatars:
        get:
            tags:
                - Agent
            description: 获取智能体默认头像
            operationId: Agent_GetDefaultAvatars
            parameters:
                - name: avatarType
                  in: query
                  description: 1.部门应用图标  2.数字员工图标
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetDefaultAvatarsReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/delete:
        post:
            tags:
                - Agent
            description: 删除 Agent
            operationId: Agent_DeleteAgent
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DeleteAgentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DeleteAgentReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/filePermission:
        get:
            tags:
                - Agent
            description: 判断用户是否有文件权限（关联智能体、知识库）
            operationId: Agent_GetFilePermissionByAgentID
            parameters:
                - name: agentID
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: fileRelationID
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetFilePermissionByAgentIDReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/generateQuestionOptimization:
        post:
            tags:
                - Agent
            description: 根据问题推荐问题
            operationId: Agent_GenerateQuestionOptimization
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/GenerateQuestionOptimizationRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GenerateQuestionOptimizationReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/get:
        get:
            tags:
                - Agent
            description: 查询 Agent
            operationId: Agent_GetAgent
            parameters:
                - name: ids
                  in: query
                  schema:
                    type: array
                    items:
                        type: integer
                        format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAgentReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/internalModelChatPage:
        post:
            tags:
                - Agent
            description: 内部模型聊天记录分页
            operationId: Agent_InternalModelChatPage
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/InternalModelChatPageRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/InternalModelChatPageReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/modelAskClassificationDistribution:
        get:
            tags:
                - Agent
            description: 查询模型提问词分类分布
            operationId: Agent_QueryModelAskClassificationDistribution
            parameters:
                - name: modelType
                  in: query
                  description: 1. 私有模型 2. 网关模型 3. 外网模型
                  schema:
                    type: integer
                    format: int64
                - name: startTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: startTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
                - name: endTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: endTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryModelAskClassificationDistributionReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/modelAskClassificationTop10:
        get:
            tags:
                - Agent
            description: 查询模型提问词分类top10
            operationId: Agent_QueryModelAskClassificationTop10
            parameters:
                - name: topType
                  in: query
                  description: 1. 员工top 10 2. 部门top 10
                  schema:
                    type: integer
                    format: int64
                - name: startTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: startTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
                - name: endTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: endTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
                - name: modelType
                  in: query
                  description: 1. 私有模型 2. 网关模型
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryModelAskClassificationTop10Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/page:
        get:
            tags:
                - Agent
            description: 分页 Agent
            operationId: Agent_PageAgent
            parameters:
                - name: pageNum
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: showOnClient
                  in: query
                  description: 是否展示在客户端
                  schema:
                    type: boolean
                - name: modelType
                  in: query
                  description: 模型类型 0.所有 1.私有模型 2.网关模型
                  schema:
                    type: integer
                    format: int64
                - name: isMine
                  in: query
                  description: 是我创建的
                  schema:
                    type: boolean
                - name: agentCategoryType
                  in: query
                  description: 智能体类别 1.AI工具 2.问答智能体 3.深度检索问答智能体 4.问答+深度检索智能体
                  schema:
                    type: integer
                    format: int64
                - name: agentName
                  in: query
                  description: 智能体名称
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PageAgentReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/queue/whiteList:
        get:
            tags:
                - Agent
            description: 获取智能体排队白名单人员id列表
            operationId: Agent_GetAgentQueueWhiteList
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAgentQueueWhiteListReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - Agent
            description: 保存智能体排队白名单人员id列表
            operationId: Agent_SaveAgentQueueWhiteList
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SaveAgentQueueWhiteListRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SaveAgentQueueWhiteListReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/security/logs:
        get:
            tags:
                - Agent
            description: 分页智能体安全策略数据管控
            operationId: Agent_PageAgentSecurityLogs
            parameters:
                - name: pageNum
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: pageSize
                  in: query
                  schema:
                    type: integer
                    format: int64
                - name: hitAction
                  in: query
                  description: 1.阻断 2.警告
                  schema:
                    type: array
                    items:
                        type: integer
                        format: int64
                - name: riskLevel
                  in: query
                  description: 1. 低风险 2. 中风险 3. 高风险
                  schema:
                    type: array
                    items:
                        type: integer
                        format: int64
                - name: userName
                  in: query
                  schema:
                    type: string
                - name: deptName
                  in: query
                  schema:
                    type: string
                - name: startTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: startTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
                - name: endTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: endTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/PageAgentSecurityLogsReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/security/logs/count:
        get:
            tags:
                - Agent
            description: 获取智能体安全策略数据管控数量
            operationId: Agent_GetAgentSecurityLogsCount
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAgentSecurityLogsCountReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/security/logs/detail:
        get:
            tags:
                - Agent
            description: 获取智能体安全策略数据管控详情
            operationId: Agent_GetAgentSecurityLogDetail
            parameters:
                - name: id
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetAgentSecurityLogDetailReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/transfer:
        post:
            tags:
                - Agent
            description: 转让智能体
            operationId: Agent_TransferAgent
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/TransferAgentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TransferAgentReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/update:
        post:
            tags:
                - Agent
            description: 更新 Agent
            operationId: Agent_UpdateAgent
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateAgentRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UpdateAgentReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/update/sort:
        post:
            tags:
                - Agent
            description: 更新智能体排序
            operationId: Agent_UpdateAgentSort
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateAgentSortRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/UpdateAgentSortReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/uploadFileTypeDistribution:
        get:
            tags:
                - Agent
            description: 查询上传附件文件类型分布
            operationId: Agent_QueryUploadFileTypeDistribution
            parameters:
                - name: modelType
                  in: query
                  description: 1. 私有模型 2. 网关模型 3. 外网模型
                  schema:
                    type: integer
                    format: int64
                - name: startTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: startTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
                - name: endTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: endTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryUploadFileTypeDistributionReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/uploadFileTypeTop10:
        get:
            tags:
                - Agent
            description: 查询上传附件文件类型top10
            operationId: Agent_QueryUploadFileTypeTop10
            parameters:
                - name: topType
                  in: query
                  description: 1. 员工top 10 2. 部门top 10
                  schema:
                    type: integer
                    format: int64
                - name: startTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: startTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
                - name: endTime.seconds
                  in: query
                  description: Represents seconds of UTC time since Unix epoch 1970-01-01T00:00:00Z. Must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive.
                  schema:
                    type: integer
                    format: int64
                - name: endTime.nanos
                  in: query
                  description: Non-negative fractions of a second at nanosecond resolution. Negative second values with fractions must still have non-negative nanos values that count forward in time. Must be from 0 to 999,999,999 inclusive.
                  schema:
                    type: integer
                    format: int32
                - name: modelType
                  in: query
                  description: 1. 私有模型 2. 网关模型
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/QueryUploadFileTypeTop10Reply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /agent/user/agentsAndKnowledgeBases:
        get:
            tags:
                - Agent
            description: 获取用户智能体列表、知识库列表
            operationId: Agent_GetUserAgentsAndKnowledgeBases
            parameters:
                - name: userID
                  in: query
                  schema:
                    type: integer
                    format: int64
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/GetUserAgentsAndKnowledgeBasesReply'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        AskAgentReply_FileInfo:
            type: object
            properties:
                fileRelationID:
                    type: integer
                    format: int64
                title:
                    type: string
                size:
                    type: integer
                    format: int64
                mimeType:
                    type: string
                userID:
                    type: integer
                    format: int64
                entityTag:
                    type: string
                preEntityTag:
                    type: string
                index:
                    type: integer
                    format: int64
                chunkIndex:
                    type: integer
                    format: int64
                chunkSize:
                    type: integer
                    format: int64
                images:
                    type: array
                    items:
                        type: string
                tags:
                    type: array
                    items:
                        type: string
                fullPath:
                    type: string
                knowledgeBaseID:
                    type: integer
                    format: int64
                knowledgeBaseName:
                    type: string
                knowledgeBaseDataType:
                    type: integer
                    format: int64
                tableData:
                    type: string
                chartSchema:
                    type: string
                classPath:
                    type: string
                level:
                    type: integer
                    format: int64
        CheckQuestionSecurityReply:
            type: object
            properties:
                riskLevel:
                    type: integer
                    description: 风险级别 1.低危 2.中危 3.高危
                    format: int64
                hitAction:
                    type: integer
                    description: '1: 阻断 2: 警告'
                    format: int64
                hitResponse:
                    type: string
                    description: 命中策略后的回复
                chatItemID:
                    type: integer
                    description: 保存的chatItemID
                    format: int64
        CheckQuestionSecurityRequest:
            type: object
            properties:
                question:
                    type: string
                agentID:
                    type: integer
                    format: int64
                uploadedFiles:
                    type: array
                    items:
                        $ref: '#/components/schemas/CheckQuestionSecurityRequest_FileInfo'
                chatID:
                    type: integer
                    format: int64
                pcName:
                    type: string
        CheckQuestionSecurityRequest_FileInfo:
            type: object
            properties:
                fileRelationID:
                    type: integer
                    format: int64
                title:
                    type: string
                size:
                    type: integer
                    format: int64
                mimeType:
                    type: string
                userID:
                    type: integer
                    format: int64
                entityTag:
                    type: string
                preEntityTag:
                    type: string
                index:
                    type: integer
                    format: int64
                chunkIndex:
                    type: integer
                    format: int64
                fullPath:
                    type: string
        CreateAgentReply:
            type: object
            properties:
                id:
                    type: integer
                    description: 智能体id
                    format: int64
                name:
                    type: string
                    description: 智能体名称
                avatar:
                    type: string
                    description: 智能体头像
                welcomeMsg:
                    type: string
                    description: 欢迎语
                fallbackMsg:
                    type: string
                    description: 兜底回复
                ownerId:
                    type: integer
                    description: 创建者id
                    format: int64
                visibilityType:
                    type: integer
                    description: 可见性对象类型
                    format: int64
                visibleToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                visibleToDept:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                knowledgeBaseIds:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 知识库id列表
                schema:
                    type: string
                    description: 智能体编排schema
                isPublic:
                    type: boolean
                    description: 是否公开
                isEnabled:
                    type: boolean
                    description: 是否启用
                description:
                    type: string
                    description: 描述
                showReferenceFile:
                    type: boolean
                    description: 是否显示引用文件
        CreateAgentRequest:
            type: object
            properties:
                name:
                    type: string
                    description: 智能体名称
                avatar:
                    type: string
                    description: 智能体头像
                welcomeMsg:
                    type: string
                    description: 欢迎语
                fallbackMsg:
                    type: string
                    description: 兜底回复
                ownerId:
                    type: integer
                    description: 创建者id
                    format: int64
                visibilityType:
                    type: integer
                    description: 可见性对象类型
                    format: int64
                visibleToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                visibleToDept:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                knowledgeBaseIds:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 知识库id列表
                schema:
                    type: string
                    description: 智能体编排schema
                isPublic:
                    type: boolean
                    description: 是否公开
                isEnabled:
                    type: boolean
                    description: 是否启用
                description:
                    type: string
                    description: 描述
                showReferenceFile:
                    type: boolean
                    description: 是否显示引用文件
                modelType:
                    type: integer
                    description: 1. 内部模型 2. 外部模型
                    format: int64
                modelID:
                    type: integer
                    description: 模型id
                    format: int64
                internetSearch:
                    type: boolean
                    description: 联网搜索
                agentType:
                    type: integer
                    description: 智能体类型 1：基础问答 2：检索深度问答 3：合同审核
                    format: int64
                thinking:
                    type: boolean
                    description: 深度思考
                thinkingModelID:
                    type: integer
                    description: 深度思考模型id
                    format: int64
                roleSetting:
                    type: string
                    description: 角色设定
                securityPolicies:
                    type: array
                    items:
                        $ref: '#/components/schemas/SecurityPolicy'
                    description: 安全策略
                uploadFile:
                    type: boolean
                    description: 上传附件
                semanticCache:
                    type: boolean
                    description: 语义缓存
                manageableToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                clickedAvatar:
                    type: string
                    description: 点击头像
        DeleteAgentReply:
            type: object
            properties: {}
        DeleteAgentRequest:
            type: object
            properties:
                id:
                    type: integer
                    description: 智能体id
                    format: int64
            description: =========================== delete
        DeptInfo:
            type: object
            properties:
                id:
                    type: integer
                    description: 部门id
                    format: int64
                name:
                    type: string
                    description: 部门名称
        GenerateQuestionOptimizationReply:
            type: object
            properties:
                questions:
                    type: array
                    items:
                        type: string
        GenerateQuestionOptimizationRequest:
            type: object
            properties:
                question:
                    type: string
        GetAgentQueueWhiteListItem:
            type: object
            properties:
                userID:
                    type: integer
                    format: int64
                userName:
                    type: string
                userAvatar:
                    type: string
        GetAgentQueueWhiteListReply:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAgentQueueWhiteListItem'
        GetAgentReply:
            type: object
            properties:
                agents:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAgentReplyItem'
        GetAgentReplyItem:
            type: object
            properties:
                id:
                    type: integer
                    description: 智能体id
                    format: int64
                name:
                    type: string
                    description: 智能体名称
                avatarUrl:
                    type: string
                    description: 头像地址
                avatar:
                    type: string
                    description: 智能体头像
                welcomeMsg:
                    type: string
                    description: 欢迎语
                fallbackMsg:
                    type: string
                    description: 兜底回复
                ownerId:
                    type: integer
                    description: 创建者id
                    format: int64
                ownerName:
                    type: string
                    description: 创建者姓名
                ownerAvatar:
                    type: string
                    description: 创建者头像
                visibilityType:
                    type: integer
                    description: 可见性对象类型
                    format: int64
                visibleToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                visibleToDept:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                knowledgeBaseIds:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 知识库id列表
                schema:
                    type: string
                    description: 智能体编排schema
                isPublic:
                    type: boolean
                    description: 是否公开
                isEnabled:
                    type: boolean
                    description: 是否启用
                description:
                    type: string
                    description: 描述
                updatedAt:
                    type: string
                    description: 更新时间
                    format: date-time
                createdAt:
                    type: string
                    description: 创建时间
                    format: date-time
                visibleUsers:
                    type: array
                    items:
                        $ref: '#/components/schemas/UserInfo'
                    description: 可见用户
                visibleDepts:
                    type: array
                    items:
                        $ref: '#/components/schemas/DeptInfo'
                    description: 可见部门
                showReferenceFile:
                    type: boolean
                    description: 是否显示引用文件
                modelName:
                    type: string
                    description: 模型名称
                modelType:
                    type: integer
                    description: 模型类型
                    format: int64
                modelID:
                    type: integer
                    description: 模型
                    format: int64
                knowledgeBaseType:
                    type: integer
                    description: 知识库类型
                    format: int64
                internetSearch:
                    type: boolean
                    description: 联网搜索
                enableInternetSearchSwitch:
                    type: boolean
                    description: 是否启用联网搜索开关
                agentType:
                    type: integer
                    description: 智能体类型 1：基础问答 2：检索深度问答 3：合同审核
                    format: int64
                roleSetting:
                    type: string
                    description: 角色设定
                thinking:
                    type: boolean
                    description: 深度思考
                thinkingModelID:
                    type: integer
                    description: 深度思考模型id
                    format: int64
                thinkingModelName:
                    type: string
                    description: 深度思考模型名称
                thinkingModelAvatar:
                    type: string
                    description: 深度思考模型头像
                modelAvatar:
                    type: string
                    description: 模型头像
                modelDetailID:
                    type: integer
                    format: int64
                thinkingEnableStatus:
                    type: integer
                    description: 深度思考启用状态 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
                    format: int64
                securityPolicies:
                    type: array
                    items:
                        $ref: '#/components/schemas/SecurityPolicy'
                    description: 安全策略
                uploadFile:
                    type: boolean
                    description: 上传附件
                semanticCache:
                    type: boolean
                    description: 语义缓存
                manageableToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可管理对象id列表
                manageableUsers:
                    type: array
                    items:
                        $ref: '#/components/schemas/UserInfo'
                    description: 可管理用户
        GetAgentSecurityLogDetailReply:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                riskLevel:
                    type: integer
                    description: 1. 低风险 2. 中风险 3. 高风险
                    format: int64
                hitAction:
                    type: integer
                    description: 1.阻断 2.警告
                    format: int64
                userID:
                    type: integer
                    format: int64
                userName:
                    type: string
                deptName:
                    type: string
                userAvatar:
                    type: string
                agentID:
                    type: integer
                    format: int64
                agentName:
                    type: string
                agentAvatar:
                    type: string
                actionCategory:
                    type: integer
                    description: 1. 智能问答
                    format: int64
                createdAt:
                    type: string
                    format: date-time
                pcName:
                    type: string
                question:
                    type: string
                uploadedFiles:
                    type: array
                    items:
                        type: string
                securityPolicies:
                    type: array
                    items:
                        $ref: '#/components/schemas/SecurityPolicy'
                agentDescription:
                    type: string
        GetAgentSecurityLogsCountReply:
            type: object
            properties:
                highRiskCount:
                    type: integer
                    format: int64
                mediumRiskCount:
                    type: integer
                    format: int64
                lowRiskCount:
                    type: integer
                    format: int64
                blockedCount:
                    type: integer
                    format: int64
                warningCount:
                    type: integer
                    format: int64
        GetAllAgentInfoReply:
            type: object
            properties:
                agents:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetAllAgentInfoReply_AgentInfo'
        GetAllAgentInfoReply_AgentInfo:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                name:
                    type: string
        GetDefaultAvatarItems:
            type: object
            properties:
                avatar:
                    type: string
                url:
                    type: string
                clickedAvatar:
                    type: string
                clickedAvatarUrl:
                    type: string
        GetDefaultAvatarsReply:
            type: object
            properties:
                avatars:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetDefaultAvatarItems'
        GetFilePermissionByAgentIDReply:
            type: object
            properties:
                hasPermission:
                    type: boolean
        GetUserAgentsAndKnowledgeBasesReply:
            type: object
            properties:
                agents:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetUserAgentsAndKnowledgeBasesReply_AgentInfo'
                knowledgeBases:
                    type: array
                    items:
                        $ref: '#/components/schemas/GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo'
        GetUserAgentsAndKnowledgeBasesReply_AgentInfo:
            type: object
            properties:
                agentID:
                    type: integer
                    format: int64
                agentName:
                    type: string
                agentAvatar:
                    type: string
        GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo:
            type: object
            properties:
                knowledgeBaseID:
                    type: integer
                    format: int64
                knowledgeBaseName:
                    type: string
                dataType:
                    type: integer
                    description: 知识库类型 1文档 2表格
                    format: int32
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        InternalModelChatPageReply:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                records:
                    type: array
                    items:
                        $ref: '#/components/schemas/InternalModelChatPageReplyItem'
        InternalModelChatPageReplyItem:
            type: object
            properties:
                message:
                    type: string
                username:
                    type: string
                userID:
                    type: integer
                    format: int64
                userAvatar:
                    type: string
                deptName:
                    type: string
                deptID:
                    type: integer
                    format: int64
                agentName:
                    type: string
                agentAvatar:
                    type: string
                createdAt:
                    type: string
                    format: date-time
                pcName:
                    type: string
                id:
                    type: integer
                    format: int64
                class:
                    type: string
                    description: 提示词分类
                refFiesText:
                    type: string
                    description: 引用文件
                refFiles:
                    type: array
                    items:
                        $ref: '#/components/schemas/AskAgentReply_FileInfo'
                    description: 引用文件
        InternalModelChatPageRequest:
            type: object
            properties:
                pageNum:
                    type: integer
                    format: int64
                pageSize:
                    type: integer
                    format: int64
                userName:
                    type: string
                deptName:
                    type: string
                startTime:
                    type: string
                    format: date-time
                endTime:
                    type: string
                    format: date-time
                chatItemID:
                    type: integer
                    format: int64
                agentID:
                    type: integer
                    description: 私有模型id
                    format: int64
                modelID:
                    type: integer
                    description: 网关模型id
                    format: int64
                searchfile:
                    type: array
                    items:
                        type: string
                    description: 搜索文件类型
                noRefFiles:
                    type: boolean
                    description: 搜索无附件
                searchModel:
                    type: boolean
                    description: 是否是网关模型
                class:
                    type: string
                    description: 文件分类
        PageAgentReply:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                records:
                    type: array
                    items:
                        $ref: '#/components/schemas/PageAgentReplyItem'
        PageAgentReplyItem:
            type: object
            properties:
                id:
                    type: integer
                    description: 智能体id
                    format: int64
                name:
                    type: string
                    description: 智能体名称
                avatar:
                    type: string
                    description: 智能体头像
                avatarUrl:
                    type: string
                    description: 头像地址
                welcomeMsg:
                    type: string
                    description: 欢迎语
                fallbackMsg:
                    type: string
                    description: 兜底回复
                ownerId:
                    type: integer
                    description: 创建者id
                    format: int64
                ownerName:
                    type: string
                    description: 创建者姓名
                ownerAvatar:
                    type: string
                    description: 创建者头像
                visibilityType:
                    type: integer
                    description: 可见性对象类型
                    format: int64
                visibleToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                visibleToDept:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                knowledgeBaseIds:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 知识库id列表
                schema:
                    type: string
                    description: 智能体编排schema
                isPublic:
                    type: boolean
                    description: 是否公开
                isEnabled:
                    type: boolean
                    description: 是否启用
                description:
                    type: string
                    description: 描述
                updatedAt:
                    type: string
                    description: 更新时间
                    format: date-time
                createdAt:
                    type: string
                    description: 创建时间
                    format: date-time
                visibleUsers:
                    type: array
                    items:
                        $ref: '#/components/schemas/UserInfo'
                    description: 可见用户
                visibleDepts:
                    type: array
                    items:
                        $ref: '#/components/schemas/DeptInfo'
                    description: 可见部门
                showReferenceFile:
                    type: boolean
                    description: 是否显示引用文件
                modelName:
                    type: string
                    description: 模型名称
                modelType:
                    type: integer
                    description: 模型类型
                    format: int64
                model:
                    type: integer
                    description: 模型
                    format: int64
                knowledgeBaseType:
                    type: integer
                    description: 知识库类型
                    format: int64
                canInternetSearch:
                    type: boolean
                    description: 是否可联网搜索
                agentType:
                    type: integer
                    format: int64
                thinkingEnableStatus:
                    type: integer
                    description: 深度思考开启状态 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
                    format: int64
                thinking:
                    type: boolean
                    description: 是否开启深度思考
                uploadFile:
                    type: boolean
                    description: 上传文件
                semanticCache:
                    type: boolean
                    description: 语义缓存
                modelDetailName:
                    type: string
                    description: 外网模型名称
                manageableUsers:
                    type: array
                    items:
                        $ref: '#/components/schemas/UserInfo'
                    description: 可管理用户
                manageableToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可管理用户id列表
                clickedAvatarUrl:
                    type: string
                    description: 点击头像地址
        PageAgentSecurityLogsReply:
            type: object
            properties:
                total:
                    type: integer
                    format: int64
                records:
                    type: array
                    items:
                        $ref: '#/components/schemas/PageAgentSecurityLogsReplyItem'
        PageAgentSecurityLogsReplyItem:
            type: object
            properties:
                id:
                    type: integer
                    format: int64
                riskLevel:
                    type: integer
                    description: 1. 低风险 2. 中风险 3. 高风险
                    format: int64
                hitAction:
                    type: integer
                    description: 1.阻断 2.警告
                    format: int64
                userID:
                    type: integer
                    format: int64
                userName:
                    type: string
                deptName:
                    type: string
                userAvatar:
                    type: string
                agentID:
                    type: integer
                    format: int64
                agentName:
                    type: string
                actionCategory:
                    type: integer
                    description: 1. 智能问答
                    format: int64
                createdAt:
                    type: string
                    format: date-time
                pcName:
                    type: string
                agentAvatar:
                    type: string
        QueryModelAskClassificationDistributionReply:
            type: object
            properties:
                classificationDistributions:
                    type: array
                    items:
                        $ref: '#/components/schemas/QueryModelAskClassificationDistributionReply_ClassificationDistribution'
        QueryModelAskClassificationDistributionReply_ClassificationDistribution:
            type: object
            properties:
                classificationName:
                    type: string
                    description: 分类名称
                count:
                    type: integer
                    description: 分类数量
                    format: int64
        QueryModelAskClassificationTop10Reply:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem'
        QueryModelAskClassificationTop10Reply_ClassificationDistribution:
            type: object
            properties:
                classificationName:
                    type: string
                    description: 分类名称
                count:
                    type: integer
                    description: 分类数量
                    format: int64
        QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem:
            type: object
            properties:
                name:
                    type: string
                    description: 员工姓名/部门名称
                classificationDistributions:
                    type: array
                    items:
                        $ref: '#/components/schemas/QueryModelAskClassificationTop10Reply_ClassificationDistribution'
                total:
                    type: integer
                    description: 总数
                    format: int64
        QueryUploadFileTypeDistributionReply:
            type: object
            properties:
                fileTypeDistributions:
                    type: array
                    items:
                        $ref: '#/components/schemas/QueryUploadFileTypeDistributionReply_FileTypeDistribution'
        QueryUploadFileTypeDistributionReply_FileTypeDistribution:
            type: object
            properties:
                fileType:
                    type: string
                    description: 文件类型
                count:
                    type: integer
                    description: 文件数量
                    format: int64
        QueryUploadFileTypeTop10Reply:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem'
        QueryUploadFileTypeTop10Reply_FileTypeDistribution:
            type: object
            properties:
                fileType:
                    type: string
                    description: 文件类型
                count:
                    type: integer
                    description: 文件数量
                    format: int64
        QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem:
            type: object
            properties:
                name:
                    type: string
                    description: 员工姓名/部门名称
                fileTypeDistributions:
                    type: array
                    items:
                        $ref: '#/components/schemas/QueryUploadFileTypeTop10Reply_FileTypeDistribution'
                total:
                    type: integer
                    description: 总数
                    format: int64
        SaveAgentQueueWhiteListReply:
            type: object
            properties: {}
        SaveAgentQueueWhiteListRequest:
            type: object
            properties:
                userIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
        SecurityPolicy:
            type: object
            properties:
                name:
                    type: string
                policyCategory:
                    type: integer
                    description: 策略分类 1.敏感信息匹配
                    format: int64
                riskLevel:
                    type: integer
                    description: 风险级别 1.低危 2.中危 3.高危
                    format: int64
                enabled:
                    type: boolean
                    description: 是否启用
                policies:
                    type: array
                    items:
                        type: string
                    description: 策略内容
                hitAction:
                    type: integer
                    description: 命中策略后的操作 1.阻断 2.警告
                    format: int64
                hitResponse:
                    type: string
                    description: 命中策略后的回复
                id:
                    type: integer
                    format: int64
                createdAt:
                    type: string
                    description: 创建时间
                    format: date-time
                updatedAt:
                    type: string
                    description: 更新时间
                    format: date-time
                updatedBy:
                    type: integer
                    description: 修改人
                    format: int64
                updatedByName:
                    type: string
                    description: 修改人姓名
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        TransferAgentReply:
            type: object
            properties: {}
        TransferAgentRequest:
            type: object
            properties:
                agentIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
                newOwnerID:
                    type: integer
                    format: int64
        UpdateAgentReply:
            type: object
            properties:
                id:
                    type: integer
                    description: 智能体id
                    format: int64
                name:
                    type: string
                    description: 智能体名称
                avatar:
                    type: string
                    description: 智能体头像
                welcomeMsg:
                    type: string
                    description: 欢迎语
                fallbackMsg:
                    type: string
                    description: 兜底回复
                ownerId:
                    type: integer
                    description: 创建者id
                    format: int64
                visibilityType:
                    type: integer
                    description: 可见性对象类型
                    format: int64
                visibleToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                visibleToDept:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                knowledgeBaseIds:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 知识库id列表
                schema:
                    type: string
                    description: 智能体编排schema
                isPublic:
                    type: boolean
                    description: 是否公开
                isEnabled:
                    type: boolean
                    description: 是否启用
                description:
                    type: string
                    description: 描述
                showReferenceFile:
                    type: boolean
                    description: 是否显示引用文件
        UpdateAgentRequest:
            type: object
            properties:
                id:
                    type: integer
                    description: 智能体id
                    format: int64
                name:
                    type: string
                    description: 智能体名称
                avatar:
                    type: string
                    description: 智能体头像
                welcomeMsg:
                    type: string
                    description: 欢迎语
                fallbackMsg:
                    type: string
                    description: 兜底回复
                ownerId:
                    type: integer
                    description: 创建者id
                    format: int64
                visibilityType:
                    type: integer
                    description: 可见性对象类型
                    format: int64
                visibleToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                visibleToDept:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
                knowledgeBaseIds:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 知识库id列表
                schema:
                    type: string
                    description: 智能体编排schema
                isPublic:
                    type: boolean
                    description: 是否公开
                isEnabled:
                    type: boolean
                    description: 是否启用
                description:
                    type: string
                    description: 描述
                showReferenceFile:
                    type: boolean
                    description: 是否显示引用文件
                internetSearch:
                    type: boolean
                    description: 联网搜索
                roleSetting:
                    type: string
                    description: 角色设定
                thinking:
                    type: boolean
                    description: 深度思考
                thinkingModelID:
                    type: integer
                    description: 深度思考模型id
                    format: int64
                securityPolicies:
                    type: array
                    items:
                        $ref: '#/components/schemas/SecurityPolicy'
                    description: 策略，修改策略必须传id，创建策略id为0
                deletedSecurityPolicyIDs:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 删除的安全策略id列表
                uploadFile:
                    type: boolean
                    description: 上传附件
                semanticCache:
                    type: boolean
                    description: 语义缓存
                modelID:
                    type: integer
                    description: 模型id
                    format: int64
                manageableToUser:
                    type: array
                    items:
                        type: integer
                        format: int64
                    description: 可见对象id列表
            description: =========================== update
        UpdateAgentSortReply:
            type: object
            properties: {}
        UpdateAgentSortRequest:
            type: object
            properties:
                items:
                    type: array
                    items:
                        $ref: '#/components/schemas/UpdateAgentSortRequestItem'
        UpdateAgentSortRequestItem:
            type: object
            properties:
                agentID:
                    type: integer
                    format: int64
                index:
                    type: integer
                    format: int64
        UserInfo:
            type: object
            properties:
                id:
                    type: integer
                    description: 用户id
                    format: int64
                name:
                    type: string
                    description: 用户名称
                avatar:
                    type: string
                    description: 用户头像
tags:
    - name: Agent

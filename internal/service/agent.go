package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"time"

	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/samber/lo"
	"gitlab.minum.cloud/BackendTeam/admin/api/checklicense"
	"gitlab.minum.cloud/BackendTeam/admin/api/dept"
	"gitlab.minum.cloud/BackendTeam/admin/api/user"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/BackendTeam/pkg/metrics"
	vm "gitlab.minum.cloud/BackendTeam/pkg/victoriametrics-client"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-api/aiapi/agent"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/timestamppb"

	pbModel "gitlab.minum.cloud/innovationteam/ai-web/api/aimodel"
	pbRag "gitlab.minum.cloud/innovationteam/ai-web/api/rag"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

type AgentService struct {
	pb.UnimplementedAgentServer
	usecase               *biz.AgentUsecase
	logger                *zapadapter.Logger
	chatUc                *biz.ChatUsecase
	chatItemUc            *biz.ChatItemUsecase
	rpc                   rpc.Client
	conf                  *conf.Bootstrap
	aiModelUc             *biz.AiModelUsecase
	classificationUseCase *biz.ClassificationUseCase
	aiModelUsageUseCase   *biz.AiModelUsageUseCase
}

func RegisterUserHttpHandler(srv *http.Server, s *AgentService) {
	srv.Route("/agent").POST("/avatar", func(c http.Context) error {
		h := c.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return s.UploadAvatar(c, ctx)
		})
		_, err := h(c, nil)
		if err != nil {
			return err
		}
		return c.Result(200, nil)
	})
}

func NewAgentService(usecase *biz.AgentUsecase,
	logger *zapadapter.Logger,
	chatUc *biz.ChatUsecase,
	chatItemUc *biz.ChatItemUsecase,
	rpc rpc.Client,
	conf *conf.Bootstrap,
	aiModelUc *biz.AiModelUsecase,
	classificationUseCase *biz.ClassificationUseCase,
	aiModelUsageUseCase *biz.AiModelUsageUseCase,
) *AgentService {
	return &AgentService{
		usecase:               usecase,
		logger:                logger,
		chatUc:                chatUc,
		chatItemUc:            chatItemUc,
		rpc:                   rpc,
		conf:                  conf,
		aiModelUc:             aiModelUc,
		classificationUseCase: classificationUseCase,
		aiModelUsageUseCase:   aiModelUsageUseCase,
	}
}

func (s *AgentService) InternalModelChatPage(ctx context.Context, req *pb.InternalModelChatPageRequest) (*pb.InternalModelChatPageReply, error) {
	var (
		startTime, endTime time.Time
	)
	if req.StartTime != nil {
		startTime = req.StartTime.AsTime()
	}
	if req.EndTime != nil {
		endTime = req.EndTime.AsTime()
	}
	items, total, err := s.usecase.InternalModelChatPage(ctx, req.ChatItemID, req.UserName, req.DeptName, startTime, endTime, req.SearchModel, req.ModelID, req.AgentID, req.Searchfile, req.NoRefFiles, req.Class)
	if err != nil {
		return nil, err
	}

	var files []*pb.AskAgentReply_FileInfo
	for _, item := range items {
		if item.RefFiesText != "" {
			var tmpFiles []*pb.AskAgentReply_FileInfo
			err = json.Unmarshal([]byte(item.RefFiesText), &tmpFiles)
			if err != nil {
				return nil, err
			}
			files = append(files, tmpFiles...)
			item.RefFiles = tmpFiles
		}
	}

	if len(files) > 0 {
		fileTags, err := s.classificationUseCase.GetClassificationFileTags(ctx,
			lox.Map(files, func(item *pb.AskAgentReply_FileInfo) *biz.FileInfo {
				return &biz.FileInfo{
					FileRelationID: item.FileRelationID,
					PreEntityTag:   item.PreEntityTag,
					EntityTag:      item.EntityTag,
				}
			}),
		)
		if err != nil {
			return nil, err
		}

		fileTagByEntityTag := lo.SliceToMap(fileTags, func(item *biz.ClassificationFileTags) (string, *biz.ClassificationFileTags) {
			return item.EntityTag, item
		})
		classificationTagByKey := make(map[string]*biz.ClassificationFileTags)
		for _, tag := range fileTags {
			classificationTagByKey[fmt.Sprintf("%d-%s-%s", tag.FileRelationID, tag.PreEntityTag, tag.EntityTag)] = tag
		}

		for _, item := range items {
			for _, file := range item.RefFiles {
				tags := fileTagByEntityTag[file.EntityTag]
				if tags != nil {
					file.Tags = tags.Tags
				}
				if tag, ok := classificationTagByKey[fmt.Sprintf("%d-%s-%s", file.FileRelationID, file.PreEntityTag, file.EntityTag)]; ok {
					file.ClassPath = tag.Path
					file.Level = int64(tag.SecurityLevel)
				}
			}
		}
	}

	return &pb.InternalModelChatPageReply{
		Total:   total,
		Records: items,
	}, nil
}

func (s *AgentService) CreateAgent(ctx context.Context, req *pb.CreateAgentRequest) (*pb.CreateAgentReply, error) {

	// 判断license
	_, err := s.rpc.LicenseCheck(ctx, &checklicense.LicenseCheckRequest{
		LicenseType: checklicense.LicenseType_AIWorkbench,
	})
	if err != nil {
		return nil, err
	}

	//schema读取文件
	schema, err := os.ReadFile("template.json")
	if err != nil {
		return nil, err
	}

	if s.conf.Agent.MixExternalModel {
		schema, err = os.ReadFile("template_mix.json")
		if err != nil {
			return nil, err
		}
	} else {
		if req.GetAgentType() == int64(pb.AgentType_AgentTypeDeepSearchRag) && req.GetModelType() == int64(pbModel.ModelType_ModelTypeInternal) {
			schema, err = os.ReadFile("template_normal_deepsearch_file.json")
			if err != nil {
				return nil, err
			}
		} else if req.GetAgentType() == int64(pb.AgentType_AgentTypeDeepSearchRag) && req.GetModelType() == int64(pbModel.ModelType_ModelTypeExternal) {
			schema, err = os.ReadFile("template_external.json")
			if err != nil {
				return nil, err
			}
		}
	}

	agent, err := s.usecase.CreateAgent(ctx, &biz.Agent{
		Name:             req.Name,
		Description:      req.Description,
		Avatar:           req.Avatar,
		ClickedAvatar:    req.ClickedAvatar,
		WelcomeMsg:       req.WelcomeMsg,
		FallbackMsg:      req.FallbackMsg,
		OwnerID:          req.OwnerId,
		VisibilityType:   pb.VisibilityType(req.VisibilityType),
		VisibleToUser:    req.VisibleToUser,
		VisibleToDept:    req.VisibleToDept,
		ManageableToUser: req.ManageableToUser,
		KnowledgeBaseIds: req.KnowledgeBaseIds,
		Schema:           string(schema),
		IsPublic:         req.IsPublic,
		IsEnabled:        req.IsEnabled,
		IsRefFiles:       req.ShowReferenceFile,
		ModelType:        req.ModelType,
		ModelID:          req.ModelID,
		InternetSearch:   req.InternetSearch,
		AgentType:        req.AgentType,
		RoleSetting:      req.RoleSetting,
		Thinking:         req.Thinking,
		ThinkingModelID:  req.ThinkingModelID,
		UploadFile:       req.UploadFile,
		SemanticCache:    req.SemanticCache,
	}, conv.AiAgentSecurityPolicy.FromRequests(req.SecurityPolicies))
	if err != nil {
		return nil, err
	}

	return &pb.CreateAgentReply{
		Id:                agent.ID,
		Name:              agent.Name,
		Avatar:            agent.Avatar,
		WelcomeMsg:        agent.WelcomeMsg,
		FallbackMsg:       agent.FallbackMsg,
		OwnerId:           agent.OwnerID,
		VisibilityType:    int64(agent.VisibilityType),
		VisibleToUser:     req.VisibleToUser,
		VisibleToDept:     req.VisibleToDept,
		KnowledgeBaseIds:  agent.KnowledgeBaseIds,
		Schema:            agent.Schema,
		IsPublic:          agent.IsPublic,
		IsEnabled:         agent.IsEnabled,
		Description:       agent.Description,
		ShowReferenceFile: agent.IsRefFiles,
	}, nil
}
func (s *AgentService) UpdateAgent(ctx context.Context, req *pb.UpdateAgentRequest) (*pb.UpdateAgentReply, error) {
	agent, err := s.usecase.UpdateAgent(ctx, &biz.Agent{
		ID:               req.Id,
		Name:             req.Name,
		Avatar:           req.Avatar,
		WelcomeMsg:       req.WelcomeMsg,
		FallbackMsg:      req.FallbackMsg,
		OwnerID:          req.OwnerId,
		VisibilityType:   pb.VisibilityType(req.VisibilityType),
		VisibleToUser:    req.VisibleToUser,
		VisibleToDept:    req.VisibleToDept,
		ManageableToUser: req.ManageableToUser,
		KnowledgeBaseIds: req.KnowledgeBaseIds,
		Schema:           req.Schema,
		IsPublic:         req.IsPublic,
		IsEnabled:        req.IsEnabled,
		Description:      req.Description,
		IsRefFiles:       req.ShowReferenceFile,
		InternetSearch:   req.InternetSearch,
		UploadFile:       req.UploadFile,
		SemanticCache:    req.SemanticCache,
		RoleSetting:      req.RoleSetting,
		Thinking:         req.Thinking,
		ThinkingModelID:  req.ThinkingModelID,
		ModelID:          req.ModelID,
		ClickedAvatar:    req.ClickedAvatar,
	}, conv.AiAgentSecurityPolicy.FromRequests(req.SecurityPolicies), req.DeletedSecurityPolicyIDs)
	if err != nil {
		return nil, err
	}
	return &pb.UpdateAgentReply{
		Id:                agent.ID,
		Name:              agent.Name,
		Avatar:            agent.Avatar,
		WelcomeMsg:        agent.WelcomeMsg,
		FallbackMsg:       agent.FallbackMsg,
		OwnerId:           agent.OwnerID,
		VisibilityType:    int64(agent.VisibilityType),
		VisibleToUser:     agent.VisibleToUser,
		VisibleToDept:     agent.VisibleToDept,
		KnowledgeBaseIds:  agent.KnowledgeBaseIds,
		Schema:            agent.Schema,
		IsPublic:          agent.IsPublic,
		IsEnabled:         agent.IsEnabled,
		Description:       agent.Description,
		ShowReferenceFile: agent.IsRefFiles,
	}, nil
}
func (s *AgentService) DeleteAgent(ctx context.Context, req *pb.DeleteAgentRequest) (*pb.DeleteAgentReply, error) {
	err := s.usecase.DeleteAgent(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.DeleteAgentReply{}, nil
}
func (s *AgentService) GetAgent(ctx context.Context, req *pb.GetAgentRequest) (*pb.GetAgentReply, error) {
	agents, err := s.usecase.GetAgent(ctx, req.Ids)
	if err != nil {
		return nil, err
	}
	for i, item := range agents {
		if item.ModelType == int64(pbModel.ModelType_ModelTypeExternal) {
			aiModel, err := s.aiModelUc.Get(ctx, item.ModelID)
			if err != nil {
				return nil, err
			}
			if aiModel != nil {
				agents[i].ModelName = aiModel.ModelName
				agents[i].ModelDetailID = aiModel.Model
				if aiModel.ModelDetail != nil {
					agents[i].ModelAvatar = aiModel.ModelDetail.AvatarUrl
					agents[i].CanInternetSearch = aiModel.ModelDetail.CanInternetSearch
				}
			}

			if item.ThinkingModelID > 0 {
				thinkingModel, err := s.aiModelUc.Get(ctx, item.ThinkingModelID)
				if err != nil {
					s.logger.Error("get thinking model", zap.Error(err))
					continue
				}
				if thinkingModel != nil && thinkingModel.ModelDetail != nil {
					agents[i].ThinkingModelName = thinkingModel.ModelName
					agents[i].ThinkingModelAvatar = thinkingModel.ModelDetail.AvatarUrl
					agents[i].ThinkingEnableStatus = thinkingModel.ModelDetail.ThinkingEnableStatus
				}
			}
		}

		users, err := s.rpc.GetUser(ctx, &user.GetUserRequest{Ids: []int64{item.OwnerID}, HasDeleted: true})
		if err != nil {
			return nil, err
		}
		if len(users.Items) == 0 {
			continue
		}
		agents[i].OwnerName = users.Items[0].GetNickName()
		agents[i].OwnerAvatar = users.Items[0].GetAvatarURL()
		if len(item.VisibleToUser) > 0 {
			visibleUsers, err := s.rpc.GetUser(ctx, &user.GetUserRequest{Ids: item.VisibleToUser, HasDeleted: true})
			if err != nil {
				return nil, err
			}
			if visibleUsers != nil && len(visibleUsers.Items) > 0 {
				agents[i].VisibleUsers = lox.Map(visibleUsers.Items, func(item *user.UserInfo) *biz.UserInfo {
					return &biz.UserInfo{
						ID:        item.Id,
						Username:  item.NickName,
						AvatarUrl: item.AvatarURL,
					}
				})
			}
		}
		if len(item.ManageableToUser) > 0 {
			manageableUsers, err := s.rpc.GetUser(ctx, &user.GetUserRequest{Ids: item.ManageableToUser, HasDeleted: true})
			if err != nil {
				return nil, err
			}
			agents[i].ManageableUsers = lox.Map(manageableUsers.Items, func(item *user.UserInfo) *biz.UserInfo {
				return &biz.UserInfo{
					ID:        item.Id,
					Username:  item.NickName,
					AvatarUrl: item.AvatarURL,
				}
			})
		}
		if len(item.VisibleToDept) > 0 {
			visibleDepts, err := s.rpc.GetDept(ctx, &dept.GetDeptRequest{Ids: item.VisibleToDept})
			if err != nil {
				return nil, err
			}
			if visibleDepts != nil && len(visibleDepts.Items) > 0 {
				agents[i].VisibleDepts = lox.Map(visibleDepts.Items, func(item *dept.DeptInfo) *biz.DeptInfo {
					return &biz.DeptInfo{
						ID:   item.Id,
						Name: item.Name,
					}
				})
			}
		}
	}
	return &pb.GetAgentReply{
		Agents: lox.Map(agents, func(item *biz.Agent) *pb.GetAgentReplyItem {
			return &pb.GetAgentReplyItem{
				Id:               item.ID,
				Name:             item.Name,
				Avatar:           item.Avatar,
				AvatarUrl:        item.AvatarUrl,
				WelcomeMsg:       item.WelcomeMsg,
				FallbackMsg:      item.FallbackMsg,
				OwnerId:          item.OwnerID,
				OwnerName:        item.OwnerName,
				OwnerAvatar:      item.OwnerAvatar,
				VisibilityType:   int64(item.VisibilityType),
				VisibleToUser:    item.VisibleToUser,
				VisibleToDept:    item.VisibleToDept,
				KnowledgeBaseIds: item.KnowledgeBaseIds,
				Schema:           item.Schema,
				IsPublic:         item.IsPublic,
				IsEnabled:        item.IsEnabled,
				VisibleUsers: lox.Map(item.VisibleUsers, func(item *biz.UserInfo) *pb.UserInfo {
					return &pb.UserInfo{
						Id:     item.ID,
						Name:   item.Username,
						Avatar: item.AvatarUrl,
					}
				}),
				VisibleDepts: lox.Map(item.VisibleDepts, func(item *biz.DeptInfo) *pb.DeptInfo {
					return &pb.DeptInfo{
						Id:   item.ID,
						Name: item.Name,
					}
				}),
				ManageableUsers: lox.Map(item.ManageableUsers, func(item *biz.UserInfo) *pb.UserInfo {
					return &pb.UserInfo{
						Id:     item.ID,
						Name:   item.Username,
						Avatar: item.AvatarUrl,
					}
				}),
				Description:                item.Description,
				ShowReferenceFile:          item.IsRefFiles,
				ModelName:                  item.ModelName,
				ModelID:                    item.ModelID,
				ModelDetailID:              item.ModelDetailID,
				ModelType:                  item.ModelType,
				KnowledgeBaseType:          item.KnowledgeBaseType,
				EnableInternetSearchSwitch: item.CanInternetSearch,
				InternetSearch:             item.InternetSearch,
				AgentType:                  item.AgentType,
				RoleSetting:                item.RoleSetting,
				Thinking:                   item.Thinking,
				ThinkingModelID:            item.ThinkingModelID,
				ModelAvatar:                item.ModelAvatar,
				ThinkingModelAvatar:        item.ThinkingModelAvatar,
				ThinkingModelName:          item.ThinkingModelName,
				ThinkingEnableStatus:       item.ThinkingEnableStatus,
				SecurityPolicies:           conv.AiAgentSecurityPolicy.ToResponses(item.SecurityPolicies),
				UploadFile:                 item.UploadFile,
				SemanticCache:              item.SemanticCache,
			}
		}),
	}, nil
}
func (s *AgentService) PageAgent(ctx context.Context, req *pb.PageAgentRequest) (*pb.PageAgentReply, error) {

	agents, total, err := s.usecase.PageAgents(ctx, req.GetShowOnClient(), req.GetModelType(), req.GetIsMine(), req.GetAgentCategoryType(), req.GetAgentName())
	if err != nil {
		return nil, err
	}
	for i, item := range agents {

		if item.ModelType == int64(pbModel.ModelType_ModelTypeExternal) {
			aiModel, err := s.aiModelUc.Get(ctx, item.ModelID)
			if err != nil {
				s.logger.Error("get ai model", zap.Error(err))
				continue
			}
			if aiModel != nil {
				agents[i].ModelDetailID = aiModel.Model
				if aiModel.ModelDetail != nil {
					agents[i].ModelDetailName = aiModel.ModelDetail.Name
				}
			}
			if item.ThinkingModelID > 0 {
				thinkingModel, err := s.aiModelUc.Get(ctx, item.ThinkingModelID)
				if err != nil {
					s.logger.Error("get thinking model", zap.Error(err))
					continue
				}
				if thinkingModel != nil && thinkingModel.ModelDetail != nil {
					agents[i].ThinkingModelName = thinkingModel.ModelName
					agents[i].ThinkingModelAvatar = thinkingModel.ModelDetail.AvatarUrl
					agents[i].ThinkingEnableStatus = thinkingModel.ModelDetail.ThinkingEnableStatus
				}
			}
		} else if item.ModelType == int64(pbModel.ModelType_ModelTypeInternal) {
			agents[i].ThinkingEnableStatus = int64(pbModel.ModelThinkingEnableStatus_ModelThinkingEnableStatusDynamicEnable)
		}

		users, err := s.rpc.GetUser(ctx, &user.GetUserRequest{Ids: []int64{item.OwnerID}, HasDeleted: true})
		if err != nil {
			return nil, err
		}
		if len(users.Items) == 0 {
			continue
		}
		agents[i].OwnerName = users.Items[0].GetNickName()
		agents[i].OwnerAvatar = users.Items[0].GetAvatarURL()
		if len(item.VisibleToUser) > 0 {
			visibleUsers, err := s.rpc.GetUser(ctx, &user.GetUserRequest{Ids: item.VisibleToUser, HasDeleted: true})
			if err != nil {
				return nil, err
			}
			if visibleUsers != nil && len(visibleUsers.Items) > 0 {
				agents[i].VisibleUsers = lox.Map(visibleUsers.Items, func(item *user.UserInfo) *biz.UserInfo {
					return &biz.UserInfo{
						ID:        item.Id,
						Username:  item.NickName,
						AvatarUrl: item.AvatarURL,
					}
				})
			}
		}
		if len(item.VisibleToDept) > 0 {
			visibleDepts, err := s.rpc.GetDept(ctx, &dept.GetDeptRequest{Ids: item.VisibleToDept})
			if err != nil {
				return nil, err
			}
			if visibleDepts != nil && len(visibleDepts.Items) > 0 {
				agents[i].VisibleDepts = lox.Map(visibleDepts.Items, func(item *dept.DeptInfo) *biz.DeptInfo {
					return &biz.DeptInfo{
						ID:   item.Id,
						Name: item.Name,
					}
				})
			}
		}
		if len(item.ManageableToUser) > 0 {
			manageableUsers, err := s.rpc.GetUser(ctx, &user.GetUserRequest{Ids: item.ManageableToUser, HasDeleted: true})
			if err != nil {
				return nil, err
			}
			if manageableUsers != nil && len(manageableUsers.Items) > 0 {
				agents[i].ManageableUsers = lox.Map(manageableUsers.Items, func(item *user.UserInfo) *biz.UserInfo {
					return &biz.UserInfo{
						ID:        item.Id,
						Username:  item.NickName,
						AvatarUrl: item.AvatarURL,
					}
				})
			}
		}
	}

	return &pb.PageAgentReply{
		Records: lox.Map(agents, func(item *biz.Agent) *pb.PageAgentReplyItem {
			return &pb.PageAgentReplyItem{
				Id:               item.ID,
				Name:             item.Name,
				Avatar:           item.Avatar,
				AvatarUrl:        item.AvatarUrl,
				ClickedAvatarUrl: item.ClickedAvatarUrl,
				WelcomeMsg:       item.WelcomeMsg,
				FallbackMsg:      item.FallbackMsg,
				OwnerId:          item.OwnerID,
				OwnerName:        item.OwnerName,
				OwnerAvatar:      item.OwnerAvatar,
				VisibilityType:   int64(item.VisibilityType),
				KnowledgeBaseIds: item.KnowledgeBaseIds,
				VisibleToUser:    item.VisibleToUser,
				VisibleToDept:    item.VisibleToDept,
				ManageableToUser: item.ManageableToUser,
				//Schema:         item.Schema,
				IsPublic:  item.IsPublic,
				IsEnabled: item.IsEnabled,
				VisibleUsers: lox.Map(item.VisibleUsers, func(item *biz.UserInfo) *pb.UserInfo {
					return &pb.UserInfo{
						Id:     item.ID,
						Name:   item.Username,
						Avatar: item.AvatarUrl,
					}
				}),
				VisibleDepts: lox.Map(item.VisibleDepts, func(item *biz.DeptInfo) *pb.DeptInfo {
					return &pb.DeptInfo{
						Id:   item.ID,
						Name: item.Name,
					}
				}),
				ManageableUsers: lox.Map(item.ManageableUsers, func(item *biz.UserInfo) *pb.UserInfo {
					return &pb.UserInfo{
						Id:     item.ID,
						Name:   item.Username,
						Avatar: item.AvatarUrl,
					}
				}),
				Description:          item.Description,
				ShowReferenceFile:    item.IsRefFiles,
				ModelName:            item.ModelName,
				Model:                item.ModelDetailID,
				ModelType:            item.ModelType,
				KnowledgeBaseType:    item.KnowledgeBaseType,
				CanInternetSearch:    item.CanInternetSearch,
				AgentType:            item.AgentType,
				Thinking:             item.Thinking,
				ThinkingEnableStatus: item.ThinkingEnableStatus,
				UploadFile:           item.UploadFile,
				SemanticCache:        item.SemanticCache,
				UpdatedAt:            timestamppb.New(item.UpdatedAt),
				ModelDetailName:      item.ModelDetailName,
			}
		}),
		Total: total,
	}, nil
}

func (s *AgentService) UploadAvatar(ctx http.Context, ctx2 context.Context) (string, error) {
	req := ctx.Request()
	file, header, err := req.FormFile("file")
	if err != nil {
		return "", fmt.Errorf("service upload file: %w", err)
	}
	defer file.Close()
	key, url, err := s.usecase.UploadAvatar(ctx2, file, header)
	if err != nil {
		return "", err
	}
	resp, err := json.Marshal(struct {
		Key string `json:"key"`
		Url string `json:"url"`
	}{Key: key, Url: url})
	if err != nil {
		return "", err
	}
	_, err = ctx.Response().Write(resp)
	if err != nil {
		return "", err
	}

	return key, nil
}

func RegisterAgentChatHttpHandler(srv *http.Server, s *AgentService) {
	srv.Route("/agent").POST("/chat", func(c http.Context) error {
		h := c.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			session, ok := authz.GetSession(ctx)
			newCtx := context.Background()
			if ok {
				tr, ok := transport.FromServerContext(ctx)
				if ok {
					newCtx = transport.NewClientContext(newCtx, tr)
				}
				newCtx = authz.SetSession(newCtx, session)
			}
			return nil, s.Chat(c, newCtx)
		})
		res, err := h(c, nil)
		if err != nil {
			return err
		}
		return c.Result(200, res)
	})

	srv.Route("/agent").GET("/image", func(c http.Context) error {
		h := c.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return nil, s.GetImage(c, ctx)
		})
		res, err := h(c, nil)
		if err != nil {
			return err
		}
		return c.Result(200, res)
	})
}

func (r *AgentService) GetDefaultAvatars(ctx context.Context, request *pb.GetDefaultAvatarsRequest) (*pb.GetDefaultAvatarsReply, error) {
	avatars, err := r.usecase.GetDefaultAgentAvatars(ctx, request.GetAvatarType())
	if err != nil {
		return nil, err
	}
	return &pb.GetDefaultAvatarsReply{
		Avatars: lo.Map(avatars, func(item *biz.AgentAvatar, index int) *pb.GetDefaultAvatarItems {
			return &pb.GetDefaultAvatarItems{
				Avatar:           item.Avatar,
				Url:              item.AvatarUrl,
				ClickedAvatar:    item.ClickedAvatar,
				ClickedAvatarUrl: item.ClickedAvatarUrl,
			}
		}),
	}, nil
}

func (r *AgentService) GetFilePermissionByAgentID(ctx context.Context, request *pb.GetFilePermissionByAgentIDRequest) (*pb.GetFilePermissionByAgentIDReply, error) {
	hasPermission, err := r.usecase.GetFilePermissionByAgent(ctx, request.GetAgentID(), request.GetFileRelationID())
	if err != nil {
		return &pb.GetFilePermissionByAgentIDReply{HasPermission: false}, err
	}
	return &pb.GetFilePermissionByAgentIDReply{HasPermission: hasPermission}, nil
}

func (r *AgentService) GetImage(httpCtx http.Context, ctx context.Context) error {
	req := httpCtx.Request()
	key := req.URL.Query().Get("imageKey")
	if key == "" {
		return errors.BadRequest("key is empty", "key不能为空")
	}

	image, err := r.rpc.GetImage(ctx, key)
	if err != nil {
		r.logger.Error("get image", zap.Error(err))
		return err
	}

	httpCtx.Header().Set("Content-Type", "image/jpeg")
	httpCtx.Header().Set("Content-Length", fmt.Sprintf("%d", len(image)))
	_, err = httpCtx.Response().Write(image)
	if err != nil {
		r.logger.Error("write image", zap.Error(err))
		return err
	}
	return nil
}

func (r *AgentService) Chat(httpCtx http.Context, ctx context.Context) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	var req *pb.AskAgentRequest
	if err := httpCtx.Bind(&req); err != nil {
		return err
	}

	agents, err := r.usecase.GetAgent(ctx, []int64{req.AgentID})
	if err != nil {
		r.logger.Error("get agent", zap.Error(err))
		return err
	}
	if len(agents) == 0 {
		return errors.BadRequest("agent not found", "智能体不存在")
	}
	if agents[0].IsEnabled == false {
		return errors.BadRequest("agent is disabled", "智能体已禁用")
	}

	session, ok := authz.GetSession(ctx)
	if !ok {
		return fmt.Errorf("session not found")
	}

	users, err := r.rpc.GetUser(ctx, &user.GetUserRequest{Ids: []int64{session.UserID}, HasDeleted: true})
	if err != nil {
		r.logger.Error("get user", zap.Error(err))
		return err
	}
	if len(users.GetItems()) == 0 {
		return errors.BadRequest("user not found", "用户不存在")
	}

	if agents[0].VisibilityType == pb.VisibilityType_VisibilityTypeUser {
		if lo.Contains(agents[0].ManageableToUser, session.UserID) == false && lo.Contains(agents[0].VisibleToUser, session.UserID) == false && lo.Contains(agents[0].VisibleToDept, users.GetItems()[0].DeptID) == false && session.UserID != agents[0].OwnerID {
			return errors.Forbidden("user not visible", "无权限使用该智能体")
		}
	}

	switch agents[0].ModelType {
	case int64(pb.ModelType_ModelTypeInternal):
		metrics.CreateSituationAwareness(vm.AccidentInternalModelCall, users.GetItems()[0].Id, users.GetItems()[0].DeptID)
	case int64(pb.ModelType_ModelTypeGateway):
		metrics.CreateSituationAwareness(vm.AccidentGatewayModelCall, users.GetItems()[0].Id, users.GetItems()[0].DeptID)
	default:
	}

	err = r.usecase.UpdateAgentUseCount(ctx, agents[0].ID)
	if err != nil {
		r.logger.Error("update agent use count", zap.Error(err))
	}

	var roundId int64 = 0
	refFilesStr := ""
	refFileRelationIDs := make([]int64, 0)
	mineTypes := make([]string, 0)
	if req.Files != nil {
		refFilesMarshal, err := json.Marshal(req.Files)
		if err != nil {
			r.logger.Error("marshal ref files", zap.Error(err))
			return err
		}
		refFilesStr = string(refFilesMarshal)

		for _, file := range req.Files {
			refFileRelationIDs = append(refFileRelationIDs, file.GetFileRelationID())
			mineTypes = append(mineTypes, file.GetMimeType())
		}
	}

	savedChatItem := &biz.ChatItem{}

	if req.GetChatID() > 0 {
		if req.ChatItemID == 0 {
			savedChatItem, err = r.chatItemUc.Save(ctx, &biz.ChatItem{
				ChatID:     req.GetChatID(),
				ObjectID:   session.UserID,
				ObjectType: int64(pb.ChatObjectType_ChatObjectTypeUser),
				Message:    req.GetQuestion(),
				RefFiles:   refFilesStr,
				TenantID:   session.TenantID,
				RoundID:    roundId,
				PcName:     req.PcName,
				MineTypes:  mineTypes,
			})
			if err != nil {
				r.logger.Error("save chat item", zap.Error(err))
				return err
			}
		} else {
			savedChatItem.ID = req.ChatItemID
			savedChatItem.HitContinueSend = true
			err = r.chatItemUc.UpdateHitContinueSend(ctx, savedChatItem)
			if err != nil {
				r.logger.Error("update chat item hit continue send", zap.Error(err))
				return err
			}
		}
	}

	metrics.AgentQuestionLength(int64(len([]rune(req.GetQuestion()))))

	aiChatItem := &biz.ChatItem{}
	// 预创建回答记录
	if req.GetChatID() > 0 {
		aiChatItem, err = r.chatItemUc.Save(ctx, &biz.ChatItem{})
		if err != nil {
			r.logger.Error("save chat item", zap.Error(err))
			return err
		}
	}

	var resMessage string
	var resReason string
	var promptTokens int64
	var completionTokens int64
	replyFiles := map[string]*pb.AskAgentReply_FileInfo{}
	resFileRelationIDs := make([]int64, 0)

	httpCtx.Header().Set("Cache-Control", "no-cache")
	httpCtx.Header().Set("Connection", "keep-alive")
	httpCtx.Header().Set("Content-Type", "text/event-stream;charset=UTF-8")
	httpCtx.Header().Set("X-Accel-Buffering", "no") // 禁止nginx缓存

	// 设置响应头为SSE
	contentType := "text/event-stream"
	reader, writer := io.Pipe()

	var errorCode = ""
	cachedPayloadsStr := ""
	isCached := false

	go func() {
		select {
		case <-ctx.Done(): // 客户端关闭连接时，ctx.Done() 会被触发
			metrics.AgentStop()
			r.logger.Info("client closed connection, shutting down writer and reader, save chat item")
			err = writer.Close()
			if err != nil {
				return
			}
			err = reader.Close()
			if err != nil {
				return
			}

			if agents[0].ModelType == int64(pbModel.ModelType_ModelTypeExternal) {
				var requestStatus = pb.CallAgentStatus_CallAgentStatusSuccess

				if (resReason == "" && resMessage == "") || errorCode != "" {
					requestStatus = pb.CallAgentStatus_CallAgentStatusFail
				}

				var modelName = ""
				var modelGatewayName = ""
				var modelDetailID = 0
				aiModel, err := r.aiModelUc.Get(context.Background(), agents[0].ModelID)
				if err != nil {
					r.logger.Error("get ai model", zap.Error(err))
					return
				}
				if aiModel != nil {
					modelName = aiModel.ModelName
				}
				if aiModel.ModelDetail != nil {
					modelGatewayName = aiModel.ModelDetail.Name
					modelDetailID = int(aiModel.ModelDetail.ID)
				}
				answer := ""
				if resReason != "" {
					answer = fmt.Sprintf("<reason> %s </reason>", resReason)
				}
				if resMessage != "" {
					answer += fmt.Sprintf("\n\n %s", resMessage)
				}

				if req.Thinking && agents[0].ThinkingModelID > 0 && resReason != "" {
					thinkingModel, err := r.aiModelUc.Get(context.Background(), agents[0].ThinkingModelID)
					if err != nil {
						r.logger.Error("get thinking model", zap.Error(err))
						return
					}
					if thinkingModel != nil && thinkingModel.ModelDetail != nil {
						modelName = thinkingModel.ModelName
						modelGatewayName = thinkingModel.ModelDetail.Name
						modelDetailID = int(thinkingModel.ModelDetail.ID)
					}
				}
				err = r.aiModelUsageUseCase.Create(context.Background(), &biz.AiModelUsage{
					PromptTokens:     promptTokens,
					CompletionTokens: completionTokens,
					UserID:           session.UserID,
					UserName:         users.GetItems()[0].NickName,
					AgentID:          req.GetAgentID(),
					Question:         req.GetQuestion(),
					Answer:           answer,
					RequestStatus:    int8(requestStatus),
					ModelName:        modelName,
					ModelGatewayName: modelGatewayName,
					AgentName:        agents[0].Name,
					ErrorCode:        errorCode,
					ModelDetailID:    int64(modelDetailID),
				})
				if err != nil {
					r.logger.Error("create ai model usage", zap.Error(err))
				}
			}

			// 获取所有replyFiles的value
			replyFilesStr := ""

			if cachedPayloadsStr != "" {
				replyFilesStr = cachedPayloadsStr
			} else {
				replyFilesList := make([]*pb.AskAgentReply_FileInfo, 0)
				for _, v := range replyFiles {
					replyFilesList = append(replyFilesList, v)
				}
				replyFilesStrMarshal, err := json.Marshal(replyFilesList)
				if err != nil {
					r.logger.Error("marshal reply files", zap.Error(err))
					return
				}
				replyFilesStr = string(replyFilesStrMarshal)
			}

			if aiChatItem != nil {
				_, err = r.chatItemUc.Update(context.Background(), &biz.ChatItem{
					ID:               aiChatItem.ID,
					ChatID:           req.GetChatID(),
					ObjectID:         session.UserID,
					ObjectType:       int64(pb.ChatObjectType_ChatObjectTypeSystem),
					Message:          resMessage,
					RefFiles:         replyFilesStr,
					TenantID:         session.TenantID,
					RoundID:          roundId,
					Reason:           resReason,
					IsInternetSearch: req.GetInternetSearch(),
				})
				if err != nil {
					r.logger.Error("save chat item", zap.Error(err))
				}
			}

			if !isCached && agents[0].SemanticCache {
				_, err = r.rpc.CheckAndSaveQuestionSemanticCache(context.Background(), &agent.CheckAndSaveQuestionSemanticCacheRequest{
					Question: req.GetQuestion(),
					Answer:   resMessage,
					RefFiles: replyFilesStr,
					AgentID:  req.AgentID,
				})
				if err != nil {
					r.logger.Error("check and save question semantic cache", zap.Error(err))
				}
			}

			metrics.AgentAnswerLength(int64(len([]rune(resMessage))))
			return
		}
	}()

	go func() {
		defer cancel()

		startTime := time.Now()
		res, err := r.rpc.CallAgent(ctx, &agent.CallAgentRequest{
			Query:                         req.Question,
			RoundID:                       req.RoundID,
			FileRelationIDs:               refFileRelationIDs,
			AgentID:                       req.AgentID,
			ChatID:                        req.ChatID,
			IsMultiRound:                  req.IsMultiRound,
			AiChatItemID:                  savedChatItem.ID,
			InternetSearch:                req.InternetSearch,
			Thinking:                      req.Thinking,
			DesignateSheetKnowledgeBaseID: req.DesignateSheetKnowledgeBaseID,
			ExistingTableData:             req.ExistingTableData,
		}, grpc.MaxCallRecvMsgSize(100*1024*1024))
		if err != nil {
			r.logger.Error("call ai-api ", zap.Error(err))
			errorCode = "500"
			return
		}

		var endTime time.Time
		for {
			item, err := res.Recv()
			if err != nil {
				if err == io.EOF {
					r.logger.Info("recv from ai-api EOF")
				} else {
					r.logger.Error("recv from ai-api ", zap.Error(err))
					errorCode = "500"
				}
				return
			}

			if endTime.IsZero() {
				endTime = time.Now()
				metrics.AgentFirstToken(endTime.Sub(startTime))
			}

			if item.GetUsage() != nil {
				if item.GetUsage().GetPromptTokens() != 0 {
					promptTokens = item.GetUsage().GetPromptTokens()
				}
				if item.GetUsage().GetCompletionTokens() != 0 {
					completionTokens = item.GetUsage().GetCompletionTokens()
				}
			}

			if len(item.GetPayloads()) == 0 && item.GetType() == 0 {
				resMessage += item.GetContent()
				resReason += item.GetReason()
			}
			payloads := item.GetPayloads()

			roundId = item.GetRoundID()

			debugContent := item.GetDebugContent()

			cachedPayloadsStr = item.GetCachePayloads()

			if !isCached {
				isCached = item.GetIsCached()
			}

			files := make([]*pb.AskAgentReply_FileInfo, 0)
			if cachedPayloadsStr != "" {
				var refFiles []*pbRag.FileInfo
				err := json.Unmarshal([]byte(cachedPayloadsStr), &refFiles)
				if err != nil {
					r.logger.Error("unmarshal ref files", zap.Error(err))
				}
				for _, f := range refFiles {
					files = append(files, &pb.AskAgentReply_FileInfo{
						FileRelationID:        f.FileRelationID,
						Title:                 f.GetTitle(),
						Size:                  f.GetSize(),
						MimeType:              f.GetMimeType(),
						UserID:                f.GetUserID(),
						EntityTag:             f.GetEntityTag(),
						PreEntityTag:          f.GetPreEntityTag(),
						Index:                 f.GetIndex(),
						ChunkIndex:            f.GetChunkIndex(),
						Images:                f.GetImages(),
						FullPath:              f.GetFullPath(),
						KnowledgeBaseID:       f.GetKnowledgeBaseID(),
						KnowledgeBaseName:     f.GetKnowledgeBaseName(),
						KnowledgeBaseDataType: f.GetKnowledgeBaseDataType(),
						TableData:             f.GetTableData(),
						ChartSchema:           f.GetChartSchema(),
					})
				}
			} else {
				for _, payload := range payloads {
					resFileRelationIDs = append(resFileRelationIDs, payload.GetFileRelationID())
					files = append(files, &pb.AskAgentReply_FileInfo{
						FileRelationID:        payload.GetFileRelationID(),
						Title:                 payload.GetName(),
						Size:                  payload.GetSize(),
						MimeType:              payload.GetMimeType(),
						UserID:                payload.GetUserID(),
						EntityTag:             payload.GetEntityTag(),
						PreEntityTag:          payload.GetPreEntityTag(),
						Index:                 payload.GetIndex(),
						ChunkIndex:            payload.GetChunkIndex(),
						Images:                payload.GetImageKeys(),
						FullPath:              payload.GetFullPath(),
						KnowledgeBaseID:       payload.GetKnowledgeBaseID(),
						KnowledgeBaseName:     payload.GetKnowledgeBaseName(),
						KnowledgeBaseDataType: payload.GetKnowledgeBaseDataType(),
						TableData:             payload.GetTableData(),
						ChartSchema:           payload.GetChartSchema(),
					})

					replyFiles[fmt.Sprintf("%d-%d", payload.FileRelationID, payload.ChunkIndex)] = &pb.AskAgentReply_FileInfo{
						FileRelationID:        payload.GetFileRelationID(),
						Title:                 payload.GetName(),
						Size:                  payload.GetSize(),
						MimeType:              payload.GetMimeType(),
						UserID:                payload.GetUserID(),
						EntityTag:             payload.GetEntityTag(),
						PreEntityTag:          payload.GetPreEntityTag(),
						Index:                 payload.GetIndex(),
						ChunkIndex:            payload.GetChunkIndex(),
						ChunkSize:             payload.GetChunkSize(),
						Images:                payload.GetImageKeys(),
						FullPath:              payload.GetFullPath(),
						KnowledgeBaseID:       payload.GetKnowledgeBaseID(),
						KnowledgeBaseName:     payload.GetKnowledgeBaseName(),
						KnowledgeBaseDataType: payload.GetKnowledgeBaseDataType(),
						TableData:             payload.GetTableData(),
						ChartSchema:           payload.GetChartSchema(),
					}
				}
			}

			if len(debugContent) > 0 {
				maxChunkSize := 128
				totalLen := len(debugContent)
				for start := 0; start < totalLen; start += maxChunkSize {
					end := start + maxChunkSize
					if end > totalLen {
						end = totalLen
					}
					chunk := debugContent[start:end]
					reply := &pb.AskAgentReply{
						Answer:       item.GetContent(),
						Files:        files,
						Status:       item.GetStatus(),
						RoundID:      item.GetRoundID(),
						Type:         item.GetType(),
						Reason:       item.GetReason(),
						ChatItemID:   aiChatItem.ID,
						DebugContent: chunk,
					}
					// 发送数据
					replyMarshal, err := json.Marshal(reply)
					if err != nil {
						r.logger.Error("marshal reply ", zap.Error(err))
						return
					}

					data := fmt.Sprintf("data: %s\n\n", replyMarshal)

					_, err = writer.Write([]byte(data))
					if err != nil {
						r.logger.Error("write data", zap.Error(err))
						return
					}
					if flusher, ok := httpCtx.Response().(http.Flusher); ok && flusher != nil {
						flusher.Flush() // Flush the data immediately
					}
				}
			} else {
				reply := &pb.AskAgentReply{
					Answer:     item.GetContent(),
					Files:      files,
					Status:     item.GetStatus(),
					RoundID:    item.GetRoundID(),
					Type:       item.GetType(),
					Reason:     item.GetReason(),
					ChatItemID: aiChatItem.ID,
				}
				// 发送数据
				replyMarshal, err := json.Marshal(reply)
				if err != nil {
					r.logger.Error("marshal reply ", zap.Error(err))
					return
				}

				data := fmt.Sprintf("data: %s\n\n", replyMarshal)

				_, err = writer.Write([]byte(data))
				if err != nil {
					r.logger.Error("write data", zap.Error(err))
					return
				}
				if flusher, ok := httpCtx.Response().(http.Flusher); ok && flusher != nil {
					flusher.Flush() // Flush the data immediately
				}
			}

			//data := fmt.Sprintf("data: %s\n\n", replyMarshal)
			//fmt.Println(data)
			//
			//_, err = writer.Write([]byte(data))
			//if err != nil {
			//	r.logger.Error("write data", zap.Error(err))
			//	return
			//}
			//if flusher, ok := httpCtx.Response().(http.Flusher); ok && flusher != nil {
			//	flusher.Flush() // Flush the data immediately
			//}
		}
	}()

	return httpCtx.Stream(200, contentType, reader)
}

func (s *AgentService) QueryModelAskClassificationDistribution(ctx context.Context, req *pb.QueryModelAskClassificationDistributionRequest) (*pb.QueryModelAskClassificationDistributionReply, error) {
	var startTime, endTime time.Time
	if req.GetStartTime().IsValid() {
		startTime = req.GetStartTime().AsTime()
	}
	if req.GetEndTime().IsValid() {
		endTime = req.GetEndTime().AsTime()
	}

	distribution, err := s.chatItemUc.QuerySecondaryClassificationDistribution(ctx, req.GetModelType(), startTime, endTime)
	if err != nil {
		return nil, err
	}
	return &pb.QueryModelAskClassificationDistributionReply{
		ClassificationDistributions: lox.Map(distribution, func(item *biz.Classification) *pb.QueryModelAskClassificationDistributionReply_ClassificationDistribution {
			return &pb.QueryModelAskClassificationDistributionReply_ClassificationDistribution{
				ClassificationName: item.Name,
				Count:              item.Count,
			}
		}),
	}, nil
}

func (s *AgentService) QueryModelAskClassificationTop10(ctx context.Context, req *pb.QueryModelAskClassificationTop10Request) (*pb.QueryModelAskClassificationTop10Reply, error) {
	result, err := s.chatItemUc.QuerySecondaryClassificationTopKCount(ctx, req.GetModelType(), req.GetTopType(), req.GetStartTime().AsTime(), req.GetEndTime().AsTime(), 10)
	if err != nil {
		return nil, err
	}
	return &pb.QueryModelAskClassificationTop10Reply{
		Items: lox.Map(result, func(item *biz.ClassificationObject) *pb.QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem {
			return &pb.QueryModelAskClassificationTop10Reply_QueryModelAskClassificationTop10ReplyItem{
				Name:  item.ObjectName,
				Total: item.Count,
				ClassificationDistributions: lox.Map(item.Classifications, func(item *biz.Classification) *pb.QueryModelAskClassificationTop10Reply_ClassificationDistribution {
					return &pb.QueryModelAskClassificationTop10Reply_ClassificationDistribution{
						ClassificationName: item.Name,
						Count:              item.Count,
					}
				}),
			}
		}),
	}, nil
}

func (s *AgentService) QueryUploadFileTypeDistribution(ctx context.Context, req *pb.QueryUploadFileTypeDistributionRequest) (*pb.QueryUploadFileTypeDistributionReply, error) {
	var startTime, endTime time.Time
	if req.GetStartTime().IsValid() {
		startTime = req.GetStartTime().AsTime()
	}
	if req.GetEndTime().IsValid() {
		endTime = req.GetEndTime().AsTime()
	}

	distribution, err := s.chatItemUc.QueryUploadFileTypesDistribution(ctx, req.GetModelType(), startTime, endTime)
	if err != nil {
		return nil, err
	}
	usefulFileTypes := []string{"docx", "doc", "pdf", "txt", "xls", "xlsx", "ppt", "pptx"}
	distribution = lox.Filter(distribution, func(item *biz.FileType) bool {
		return lo.Contains(usefulFileTypes, item.Name)
	})
	return &pb.QueryUploadFileTypeDistributionReply{
		FileTypeDistributions: lox.Map(distribution, func(item *biz.FileType) *pb.QueryUploadFileTypeDistributionReply_FileTypeDistribution {
			return &pb.QueryUploadFileTypeDistributionReply_FileTypeDistribution{
				FileType: item.Name,
				Count:    item.Count,
			}
		}),
	}, nil
}

func (s *AgentService) QueryUploadFileTypeTop10(ctx context.Context, req *pb.QueryUploadFileTypeTop10Request) (*pb.QueryUploadFileTypeTop10Reply, error) {
	result, err := s.chatItemUc.QueryUploadFileTypesTopKCount(ctx, req.GetModelType(), req.GetTopType(), req.GetStartTime().AsTime(), req.GetEndTime().AsTime(), 10)
	if err != nil {
		return nil, err
	}
	return &pb.QueryUploadFileTypeTop10Reply{
		Items: lox.Map(result, func(item *biz.FileTypesObject) *pb.QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem {
			return &pb.QueryUploadFileTypeTop10Reply_QueryUploadFileTypeTop10ReplyItem{
				Name:  item.ObjectName,
				Total: item.Count,
				FileTypeDistributions: lox.Map(item.FileTypes, func(item *biz.FileType) *pb.QueryUploadFileTypeTop10Reply_FileTypeDistribution {
					return &pb.QueryUploadFileTypeTop10Reply_FileTypeDistribution{
						FileType: item.Name,
						Count:    item.Count,
					}
				}),
			}
		}),
	}, nil
}

func (s *AgentService) GetAllAgentInfo(ctx context.Context, req *pb.GetAllAgentInfoRequest) (*pb.GetAllAgentInfoReply, error) {
	agents, err := s.usecase.GetAllAgent(ctx, req.GetModelType())
	if err != nil {
		return nil, err
	}
	return &pb.GetAllAgentInfoReply{
		Agents: lox.Map(agents, func(item *biz.Agent) *pb.GetAllAgentInfoReply_AgentInfo {
			return &pb.GetAllAgentInfoReply_AgentInfo{
				Id:   item.ID,
				Name: item.Name,
			}
		}),
	}, nil
}

func (s *AgentService) SaveAgentQueueWhiteList(ctx context.Context, req *pb.SaveAgentQueueWhiteListRequest) (*pb.SaveAgentQueueWhiteListReply, error) {
	err := s.usecase.SaveGlobalAgentWhiteList(ctx, req.GetUserIDs())
	if err != nil {
		return nil, err
	}
	return &pb.SaveAgentQueueWhiteListReply{}, nil
}

func (s *AgentService) GetAgentQueueWhiteList(ctx context.Context, req *pb.GetAgentQueueWhiteListRequest) (*pb.GetAgentQueueWhiteListReply, error) {
	userInfos, err := s.usecase.GetGlobalAgentWhiteList(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.GetAgentQueueWhiteListReply{
		Items: lox.Map(userInfos, func(item *biz.UserInfo) *pb.GetAgentQueueWhiteListItem {
			return &pb.GetAgentQueueWhiteListItem{
				UserID:     item.ID,
				UserName:   item.Username,
				UserAvatar: item.AvatarUrl,
			}
		}),
	}, nil
}

func (s *AgentService) UpdateAgentSort(ctx context.Context, req *pb.UpdateAgentSortRequest) (*pb.UpdateAgentSortReply, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, fmt.Errorf("session not found")
	}
	err := s.usecase.SaveAgentOrders(ctx, lox.Map(req.GetItems(), func(item *pb.UpdateAgentSortRequestItem) *biz.UserAgentOrder {
		return &biz.UserAgentOrder{
			AgentID: item.AgentID,
			UserID:  session.UserID,
			Index:   item.Index,
		}
	}))
	if err != nil {
		return nil, err
	}
	return &pb.UpdateAgentSortReply{}, nil
}

func (s *AgentService) TransferAgent(ctx context.Context, req *pb.TransferAgentRequest) (*pb.TransferAgentReply, error) {
	err := s.usecase.TransferAgent(ctx, req.GetAgentIDs(), req.GetNewOwnerID())
	if err != nil {
		s.logger.Error("transfer agent", zap.Error(err))
		return nil, err
	}
	return &pb.TransferAgentReply{}, nil
}

func (s *AgentService) CheckQuestionSecurity(ctx context.Context, req *pb.CheckQuestionSecurityRequest) (*pb.CheckQuestionSecurityReply, error) {

	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, fmt.Errorf("session not found")
	}
	deptUser, err := s.rpc.GetDeptUser(ctx, &user.GetDeptUserRequest{UserIDs: []int64{session.UserID}})
	if err != nil {
		s.logger.Error("get dept user", zap.Error(err))
		return nil, err
	}
	if len(deptUser.GetItems()) == 0 {
		return nil, errors.BadRequest("dept user not found", "部门用户不存在")
	}

	id := req.GetAgentID()
	agents, err := s.usecase.GetAgent(ctx, []int64{id})
	if err != nil {
		s.logger.Error("get agent", zap.Error(err))
		return nil, err
	}
	if len(agents) == 0 {
		return nil, errors.BadRequest("agent not found", "智能体不存在")
	}

	refFilesStr := ""
	refFilesStrs := make([]string, 0)
	refFileRelationIDs := make([]int64, 0)
	mineTypes := make([]string, 0)
	if req.UploadedFiles != nil {
		refFilesMarshal, err := json.Marshal(req.UploadedFiles)
		if err != nil {
			s.logger.Error("marshal ref files", zap.Error(err))
			return nil, err
		}
		refFilesStr = string(refFilesMarshal)

		for _, file := range req.UploadedFiles {
			refFileRelationIDs = append(refFileRelationIDs, file.GetFileRelationID())
			mineTypes = append(mineTypes, file.GetMimeType())

			refFileMarshal, err := json.Marshal(file)
			if err != nil {
				s.logger.Error("marshal ref file", zap.Error(err))
				continue
			}
			refFilesStrs = append(refFilesStrs, string(refFileMarshal))
		}
	}

	policies := agents[0].SecurityPolicies
	if len(policies) == 0 {
		return &pb.CheckQuestionSecurityReply{
			HitAction: 0,
		}, nil
	}
	hitAction := 0
	hitResponse := ""
	riskLevel := 0
	logs := make([]*biz.AiAgentSecurityLog, 0)
	hitPolicies := make([]string, 0)
	for _, policy := range policies {
		if !policy.Enabled {
			continue
		}
		check, err := s.rpc.QuestionSecurityCheck(ctx, &agent.QuestionSecurityCheckRequest{
			Question: req.GetQuestion(),
			Policies: policy.Policies,
		})
		if err != nil {
			s.logger.Error("question security check", zap.Error(err))
			return nil, err
		}
		if check.GetHit() {
			if hitAction == 0 || (hitAction == int(pb.HitAction_HitActionAlert) && policy.HitAction == pb.HitAction_HitActionBlock) {
				hitAction = int(policy.HitAction)
				hitResponse = policy.HitResponse
				riskLevel = int(policy.RiskLevel)
			}
			policyStr, err := json.Marshal(policy)
			if err != nil {
				s.logger.Error("marshal policy", zap.Error(err))
				continue
			}
			hitPolicies = append(hitPolicies, string(policyStr))
		}
	}
	logs = append(logs, &biz.AiAgentSecurityLog{
		RiskLevel:        pb.RiskLevel(riskLevel),
		UserID:           session.UserID,
		UserName:         deptUser.GetItems()[0].GetNickName(),
		DeptID:           deptUser.GetItems()[0].GetDeptID(),
		DeptName:         deptUser.GetItems()[0].GetDeptName(),
		PcName:           deptUser.GetItems()[0].GetPcName(),
		AgentID:          req.GetAgentID(),
		AgentName:        agents[0].Name,
		AgentDescription: agents[0].Description,
		ActionCategory:   1,
		HitAction:        pb.HitAction(hitAction),
		Question:         req.GetQuestion(),
		UploadedFiles:    refFilesStrs,
		HitPolicies:      hitPolicies,
	})
	// record log
	err = s.usecase.CreateAgentSecurityLogs(ctx, logs)
	if err != nil {
		s.logger.Error("create agent security logs", zap.Error(err))
		return nil, err
	}

	savedChatItemID := int64(0)
	// record chat item
	if hitAction != int(pb.HitAction_HitActionUnknown) {
		savedChatItem, err := s.chatItemUc.Save(ctx, &biz.ChatItem{
			ChatID:      req.GetChatID(),
			ObjectID:    session.UserID,
			ObjectType:  1,
			Message:     req.GetQuestion(),
			TenantID:    1,
			RefFiles:    refFilesStr,
			RoundID:     0,
			PcName:      req.GetPcName(),
			MineTypes:   mineTypes,
			HitAction:   pb.HitAction(hitAction),
			HitResponse: hitResponse,
		})
		if err != nil {
			s.logger.Error("save chat item", zap.Error(err))
			return nil, err
		}
		savedChatItemID = savedChatItem.ID
	}

	return &pb.CheckQuestionSecurityReply{
		HitAction:   int64(hitAction),
		RiskLevel:   int64(riskLevel),
		HitResponse: hitResponse,
		ChatItemID:  savedChatItemID,
	}, nil
}

func (s *AgentService) PageAgentSecurityLogs(ctx context.Context, req *pb.PageAgentSecurityLogsRequest) (*pb.PageAgentSecurityLogsReply, error) {
	var startTime, endTime time.Time
	if req.StartTime.IsValid() {
		startTime = req.StartTime.AsTime()
	}
	if req.EndTime.IsValid() {
		endTime = req.EndTime.AsTime()
	}
	logs, total, err := s.usecase.PageAgentSecurityLogs(ctx, req.GetHitAction(), req.GetRiskLevel(), req.GetUserName(), req.GetDeptName(), startTime, endTime)
	if err != nil {
		s.logger.Error("page agent security logs", zap.Error(err))
		return nil, err
	}
	return &pb.PageAgentSecurityLogsReply{
		Total: total,
		Records: lox.Map(logs, func(item *biz.AiAgentSecurityLog) *pb.PageAgentSecurityLogsReplyItem {
			return &pb.PageAgentSecurityLogsReplyItem{
				Id:             item.ID,
				RiskLevel:      int64(item.RiskLevel),
				HitAction:      int64(item.HitAction),
				UserID:         item.UserID,
				UserName:       item.UserName,
				DeptName:       item.DeptName,
				AgentName:      item.AgentName,
				AgentID:        item.AgentID,
				AgentAvatar:    item.AgentAvatar,
				ActionCategory: item.ActionCategory,
				CreatedAt:      timestamppb.New(item.CreatedAt),
				PcName:         item.PcName,
				UserAvatar:     item.UserAvatar,
			}
		}),
	}, nil
}

func (s *AgentService) GetAgentSecurityLogDetail(ctx context.Context, req *pb.GetAgentSecurityLogDetailRequest) (*pb.GetAgentSecurityLogDetailReply, error) {
	log, err := s.usecase.GetAgentSecurityLogByID(ctx, req.GetId())
	if err != nil {
		s.logger.Error("get agent security log detail", zap.Error(err))
		return nil, err
	}
	return &pb.GetAgentSecurityLogDetailReply{
		Id:             log.ID,
		RiskLevel:      int64(log.RiskLevel),
		HitAction:      int64(log.HitAction),
		UserID:         log.UserID,
		UserName:       log.UserName,
		UserAvatar:     log.UserAvatar,
		DeptName:       log.DeptName,
		AgentName:      log.AgentName,
		AgentID:        log.AgentID,
		AgentAvatar:    log.AgentAvatar,
		ActionCategory: log.ActionCategory,
		CreatedAt:      timestamppb.New(log.CreatedAt),
		PcName:         log.PcName,
		Question:       log.Question,
		UploadedFiles:  log.UploadedFiles,
		SecurityPolicies: lox.Map(log.HitPoliciesStructured, func(item *biz.AiAgentSecurityPolicy) *pb.SecurityPolicy {
			return &pb.SecurityPolicy{
				RiskLevel: int64(item.RiskLevel),
				Name:      item.Name,
				HitAction: int64(item.HitAction),
			}
		}),
		AgentDescription: log.AgentDescription,
	}, nil
}

func (s *AgentService) GetAgentSecurityLogsCount(ctx context.Context, req *pb.GetAgentSecurityLogsCountRequest) (*pb.GetAgentSecurityLogsCountReply, error) {
	highRiskCount, mediumRiskCount, lowRiskCount, blockCount, warningCount, err := s.usecase.GetAgentSecurityLogCount(ctx)
	if err != nil {
		s.logger.Error("get agent security logs count", zap.Error(err))
		return nil, err
	}
	return &pb.GetAgentSecurityLogsCountReply{
		HighRiskCount:   highRiskCount,
		MediumRiskCount: mediumRiskCount,
		LowRiskCount:    lowRiskCount,
		BlockedCount:    blockCount,
		WarningCount:    warningCount,
	}, nil
}

func (s *AgentService) GetUserAgentsAndKnowledgeBases(ctx context.Context, req *pb.GetUserAgentsAndKnowledgeBasesRequest) (*pb.GetUserAgentsAndKnowledgeBasesReply, error) {
	agents, knowledgeBases, err := s.usecase.GetUserAgentsAndKnowledgeBases(ctx, req.GetUserID())
	if err != nil {
		s.logger.Error("get user agents and knowledge bases", zap.Error(err))
		return nil, err
	}
	return &pb.GetUserAgentsAndKnowledgeBasesReply{
		Agents: lox.Map(agents, func(item *biz.Agent) *pb.GetUserAgentsAndKnowledgeBasesReply_AgentInfo {
			return &pb.GetUserAgentsAndKnowledgeBasesReply_AgentInfo{
				AgentID:     item.ID,
				AgentName:   item.Name,
				AgentAvatar: item.AvatarUrl,
			}
		}),
		KnowledgeBases: lox.Map(knowledgeBases, func(item *biz.KnowledgeBase) *pb.GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo {
			return &pb.GetUserAgentsAndKnowledgeBasesReply_KnowledgeBaseInfo{
				KnowledgeBaseID:   item.ID,
				KnowledgeBaseName: item.Name,
				DataType:          int32(item.DataType),
			}
		}),
	}, nil
}

func (s *AgentService) GenerateQuestionOptimization(ctx context.Context, req *pb.GenerateQuestionOptimizationRequest) (*pb.GenerateQuestionOptimizationReply, error) {
	reply, err := s.rpc.GenerateQuestionOptimization(ctx, &agent.GenerateQuestionOptimizationRequest{
		Question: req.GetQuestion(),
	})
	if err != nil {
		s.logger.Error("generate question optimization", zap.Error(err))
		return nil, err
	}
	return &pb.GenerateQuestionOptimizationReply{
		Questions: reply.GetQuestions(),
	}, nil
}

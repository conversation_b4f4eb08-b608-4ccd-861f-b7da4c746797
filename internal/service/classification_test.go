package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/classification"
)

func TestAllClassificationTags(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	tags, err := classificationService.GetAllClassification(ctx, &pb.GetAllClassificationRequest{
		TagName: "",
	})
	assert.Nil(t, err)
	t.Log(tags)
}

func TestGetClassficationFileTags(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	tags, err := classificationService.GetFileClassificationTags(ctx, &pb.GetFileClassificationTagsRequest{
		Item: []*pb.GetFileClassificationTagsRequestItem{
			{
				FileRelationID: 14536,
				PreEntityTag:   "\\",
				EntityTag:      "bc480e986a28c73b06a92d825caa92ef808209af8b0f96373c2242e576368d64",
			},
			{
				FileRelationID: 42811,
				PreEntityTag:   "73682caf885e83954ea72fe6ece2f8a0d7d1acb535545ee3c40f04a4209b3d0b",
				EntityTag:      "2de9d4ea0026cecad587dbdbb0543bef01df81053250a07ed8390bc7030754bd",
			},
		},
	})
	assert.NoError(t, err)
	t.Log(tags)
}

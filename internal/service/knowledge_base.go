package service

import (
	"context"
	"fmt"
	"slices"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"gitlab.minum.cloud/BackendTeam/admin/api/checklicense"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	vfspb "gitlab.minum.cloud/BackendTeam/store/api/vfs"
	"gitlab.minum.cloud/innovationteam/ai-api/aiapi/search"
	"go.uber.org/zap"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/kafka"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

type KnowledgeBaseService struct {
	pb.UnimplementedKnowledgeBaseServer
	logger      *zapadapter.Logger
	cfg         *conf.Bootstrap
	rpcClient   rpc.Client
	kafkaClient *kafka.Client
	redisClient *redis.Client
	uc          *biz.KnowledgeBaseUseCase
}

func NewKnowledgeBaseService(
	logger *zapadapter.Logger,
	cfg *conf.Bootstrap,
	rpcClient rpc.Client,
	kafkaClient *kafka.Client,
	redisClient *redis.Client,
	uc *biz.KnowledgeBaseUseCase,
) *KnowledgeBaseService {
	return &KnowledgeBaseService{
		logger:      logger,
		cfg:         cfg,
		rpcClient:   rpcClient,
		kafkaClient: kafkaClient,
		redisClient: redisClient,
		uc:          uc,
	}
}

func (s *KnowledgeBaseService) CreateKnowledgeBase(ctx context.Context, req *pb.CreateKnowledgeBaseRequest) (*pb.CreateKnowledgeBaseReply, error) {
	// LICENSE CHECK
	if _, err := s.rpcClient.LicenseCheck(ctx, &checklicense.LicenseCheckRequest{LicenseType: checklicense.LicenseType_AIWorkbench}); err != nil {
		return nil, err
	}

	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	knowledgeBaseID, err := s.uc.CreateKnowledgeBase(ctx, session.TenantID, req.Name, req.Public, req.DataType, req.UserID, req.ManagerUserIDs, req.EditableUserIDs)
	if err != nil {
		return nil, err
	}

	return &pb.CreateKnowledgeBaseReply{
		Id: knowledgeBaseID,
	}, nil
}

func (s *KnowledgeBaseService) UpdateKnowledgeBase(ctx context.Context, req *pb.UpdateKnowledgeBaseRequest) (*pb.UpdateKnowledgeBaseReply, error) {
	// LICENSE CHECK
	if _, err := s.rpcClient.LicenseCheck(ctx, &checklicense.LicenseCheckRequest{LicenseType: checklicense.LicenseType_AIWorkbench}); err != nil {
		return nil, err
	}

	var ownerOption *biz.OwnerOption
	if req.OwnerOption != nil {
		ownerOption = &biz.OwnerOption{
			NewUserID:      req.OwnerOption.NewUserID,
			ManagerUserIDs: req.OwnerOption.ManagerUserIDs,
		}
	}

	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	/// kb

	knowledgeBase, err := s.uc.GetKnowledgeBase(ctx, session.TenantID, req.Id)
	if err != nil {
		return nil, err
	}

	if knowledgeBase.UserID != session.UserID {
		ownerOption = nil
		if !slices.Contains(knowledgeBase.ManagerUserIDs, session.UserID) {
			return nil, biz.ErrKnowledgeBaseIllegalOperation
		}
	}

	/// do

	if err := s.uc.UpdateKnowledgeBase(ctx, session.TenantID, req.Id, req.Name, req.Public, req.EditableUserIDs, ownerOption); err != nil {
		return nil, err
	}

	return &pb.UpdateKnowledgeBaseReply{}, nil
}

func (s *KnowledgeBaseService) ChangeKnowledgeBaseOwner(ctx context.Context, req *pb.ChangeKnowledgeBaseOwnerRequest) (*pb.ChangeKnowledgeBaseOwnerReply, error) {
	// LICENSE CHECK
	if _, err := s.rpcClient.LicenseCheck(ctx, &checklicense.LicenseCheckRequest{LicenseType: checklicense.LicenseType_AIWorkbench}); err != nil {
		return nil, err
	}

	_, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	if err := s.uc.ChangeKnowledgeBaseOwner(ctx, req.Ids, req.NewUserID); err != nil {
		return nil, err
	}

	return &pb.ChangeKnowledgeBaseOwnerReply{}, nil
}

func (s *KnowledgeBaseService) DeleteKnowledgeBase(ctx context.Context, req *pb.DeleteKnowledgeBaseRequest) (*pb.DeleteKnowledgeBaseReply, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	/// kb

	knowledgeBase, err := s.uc.GetKnowledgeBase(ctx, session.TenantID, req.Id)
	if err != nil {
		return nil, err
	}

	if knowledgeBase.UserID != session.UserID {
		return nil, biz.ErrKnowledgeBaseIllegalOperation
	}

	/// do

	if err := s.uc.DeleteKnowledgeBase(ctx, session.TenantID, session.UserID, req.Id); err != nil {
		return nil, err
	}

	return &pb.DeleteKnowledgeBaseReply{}, nil
}

func (s *KnowledgeBaseService) GetKnowledgeBases(ctx context.Context, req *pb.GetKnowledgeBasesRequest) (*pb.GetKnowledgeBasesReply, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	items, err := s.uc.GetKnowledgeBases(ctx, session.TenantID, req.Ids)
	if err != nil {
		return nil, err
	}

	return &pb.GetKnowledgeBasesReply{Records: items}, nil
}

func (s *KnowledgeBaseService) GetAllKnowledgeBases(ctx context.Context, req *pb.GetAllKnowledgeBasesRequest) (*pb.GetAllKnowledgeBasesReply, error) {
	items, err := s.uc.GetAllKnowledgeBases(ctx)
	if err != nil {
		return nil, err
	}

	return &pb.GetAllKnowledgeBasesReply{Records: items}, nil
}

func (s *KnowledgeBaseService) PageKnowledgeBase(ctx context.Context, req *pb.PageKnowledgeBaseRequest) (*pb.PageKnowledgeBaseReply, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	items, total, err := s.uc.PageKnowledgeBase(ctx, session.TenantID, session.UserID, req.Name, req.All, req.DataType)
	if err != nil {
		return nil, err
	}

	return &pb.PageKnowledgeBaseReply{Total: total, Records: items}, nil
}

func (s *KnowledgeBaseService) AddKnowledgeBaseFileFromSearch(ctx context.Context, req *pb.AddKnowledgeBaseFileFromSearchRequest) (*pb.AddKnowledgeBaseFileFromSearchReply, error) {
	pageNum := 1
	for {
		searchReply, err := s.rpcClient.FullTextSearch(ctx, &search.FullTextSearchRequest{
			Query:      req.Query,
			SearchType: req.SearchType,
			FileType:   req.FileType,
			OwnerIDs:   req.OwnerIDs,
			StartTime:  req.StartTime,
			EndTime:    req.EndTime,
			ClassPath:  req.ClassPath,
			Path:       req.Path,
			PageNum:    int64(pageNum),
			PageSize:   100,
		})
		if err != nil {
			return nil, fmt.Errorf("search file: %w", err)
		}
		if searchReply.Total > 10000 {
			return nil, biz.ErrKnowledgeBaseAddTooMuchFile
		}
		if len(searchReply.Refs) == 0 {
			break
		}

		var fileRelationIDs []int64
		for _, ref := range searchReply.Refs {
			fileRelationIDs = append(fileRelationIDs, ref.FileRelationID)
		}

		if _, err := s.AddKnowledgeBaseFile(ctx, &pb.AddKnowledgeBaseFileRequest{
			Id:              req.Id,
			FileRelationIDs: fileRelationIDs,
		}); err != nil {
			return nil, err
		}

		pageNum += 1
	}

	return &pb.AddKnowledgeBaseFileFromSearchReply{}, nil
}

func (s *KnowledgeBaseService) AddKnowledgeBaseFile(ctx context.Context, req *pb.AddKnowledgeBaseFileRequest) (*pb.AddKnowledgeBaseFileReply, error) {
	// LICENSE CHECK
	if _, err := s.rpcClient.LicenseCheck(ctx, &checklicense.LicenseCheckRequest{LicenseType: checklicense.LicenseType_AIWorkbench}); err != nil {
		return nil, err
	}

	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	/// kb

	knowledgeBase, err := s.uc.GetKnowledgeBase(ctx, session.TenantID, req.Id)
	if err != nil {
		return nil, err
	}

	if knowledgeBase.UserID != session.UserID &&
		!slices.Contains(knowledgeBase.ManagerUserIDs, session.UserID) &&
		!slices.Contains(knowledgeBase.EditableUserIDs, session.UserID) {
		return nil, biz.ErrKnowledgeBaseIllegalOperation
	}

	/// do

	filteredFileRelationIDs, err := s.uc.FilterKnowledgeBaseFile(ctx, req.Id, req.FileRelationIDs)
	if err != nil {
		return nil, err
	}

	if err := s.uc.AddKnowledgeBaseFile(ctx, knowledgeBase, filteredFileRelationIDs); err != nil {
		return nil, err
	}

	if knowledgeBase.DataType == pb.KnowledgeBaseDataType_KnowledgeBaseDataTypeSheet {
		for _, fileRelationID := range filteredFileRelationIDs {
			s.sendProcessSheetKnowledgeBaseFileMessage(ctx, req.Id, fileRelationID, "")
		}
	}

	if req.SkipQuickCreate {
		return &pb.AddKnowledgeBaseFileReply{}, nil
	} else {
		for _, fileRelationID := range filteredFileRelationIDs {
			err := s.kafkaClient.SendQuickCreateMessage(ctx, &vfspb.FileQuickCreateEvent{
				FileRelationID:  fileRelationID,
				KnowledgeBaseID: req.Id,
			})
			if err != nil {
				s.logger.Error("send quick create message failed", zap.Error(err), zap.Int64("fileRelationID", fileRelationID), zap.Int64("knowledgeBaseID", req.Id))
			}
		}
		return &pb.AddKnowledgeBaseFileReply{}, nil
	}
}

func (s *KnowledgeBaseService) DeleteKnowledgeBaseFile(ctx context.Context, req *pb.DeleteKnowledgeBaseFileRequest) (*pb.DeleteKnowledgeBaseFileReply, error) {
	// LICENSE CHECK
	if _, err := s.rpcClient.LicenseCheck(ctx, &checklicense.LicenseCheckRequest{LicenseType: checklicense.LicenseType_AIWorkbench}); err != nil {
		return nil, err
	}

	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	/// kb

	knowledgeBase, err := s.uc.GetKnowledgeBase(ctx, session.TenantID, req.Id)
	if err != nil {
		return nil, err
	}

	if knowledgeBase.UserID != session.UserID &&
		!slices.Contains(knowledgeBase.ManagerUserIDs, session.UserID) &&
		!slices.Contains(knowledgeBase.EditableUserIDs, session.UserID) {
		return nil, biz.ErrKnowledgeBaseIllegalOperation
	}

	/// do

	if err := s.uc.DeleteKnowledgeBaseFile(ctx, knowledgeBase, req.FileRelationIDs); err != nil {
		return nil, err
	}

	if s.cfg.KnowledgeGraph.Enable {
		s.logger.Info("try delete knowledge entity relation", zap.Int64s("fileRelationIDs", req.FileRelationIDs), zap.Int64("knowledgeBaseID", req.Id))
		for _, fileRelationID := range req.FileRelationIDs {
			if _, err := s.rpcClient.DeleteKnowledgeEntityRelation(ctx, &vfspb.DeleteKnowledgeEntityRelationRequest{
				KnowledgeBaseIDs: []int64{req.Id},
				RelationID:       fileRelationID,
			}); err != nil {
				s.logger.Error("delete knowledge entity relation failed", zap.Error(err), zap.Int64("fileRelationID", fileRelationID), zap.Int64("knowledgeBaseID", req.Id))
			}
		}
	}

	return &pb.DeleteKnowledgeBaseFileReply{}, nil
}

func (s *KnowledgeBaseService) UpdateKnowledgeBaseFileMetadata(ctx context.Context, req *pb.UpdateKnowledgeBaseFileMetadataRequest) (*pb.UpdateKnowledgeBaseFileMetadataReply, error) {
	// LICENSE CHECK
	if _, err := s.rpcClient.LicenseCheck(ctx, &checklicense.LicenseCheckRequest{LicenseType: checklicense.LicenseType_AIWorkbench}); err != nil {
		return nil, err
	}

	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	/// kb

	knowledgeBase, err := s.uc.GetKnowledgeBase(ctx, session.TenantID, req.Id)
	if err != nil {
		return nil, err
	}

	if knowledgeBase.UserID != session.UserID &&
		!slices.Contains(knowledgeBase.ManagerUserIDs, session.UserID) &&
		!slices.Contains(knowledgeBase.EditableUserIDs, session.UserID) {
		return nil, biz.ErrKnowledgeBaseIllegalOperation
	}

	if knowledgeBase.DataType != pb.KnowledgeBaseDataType_KnowledgeBaseDataTypeSheet {
		return nil, biz.ErrKnowledgeBaseIllegalOperation
	}

	/// do

	status := pb.KnowledgeBaseFileStatus_KnowledgeBaseFileStatusWaiting
	failedReason := ""
	if err := s.uc.UpdateKnowledgeBaseFile(ctx, req.Id, req.FileRelationID, &req.Metadata, &status, &failedReason); err != nil {
		return nil, fmt.Errorf("update knowledge base file metadata: %w", err)
	}

	s.sendProcessSheetKnowledgeBaseFileMessage(ctx, req.Id, req.FileRelationID, req.Metadata)

	return &pb.UpdateKnowledgeBaseFileMetadataReply{}, nil
}

func (s *KnowledgeBaseService) sendProcessSheetKnowledgeBaseFileMessage(ctx context.Context, id, fileRelationID int64, metadata string) {
	messageID := uuid.NewString()
	if err := s.redisClient.Set(ctx, fmt.Sprintf("ProcessSheetKnowledgeBaseFileEvent:%d-%d", id, fileRelationID), messageID, time.Duration(30*24*time.Hour)).Err(); err != nil {
		s.logger.Error(
			"set ProcessSheetKnowledgeBaseFileEvent message id",
			zap.Error(err),
			zap.Int64("knowledgeBaseID", id),
			zap.Int64("fileRelationID", fileRelationID),
			zap.String("messageID", messageID),
		)
	}

	if err := s.kafkaClient.SendProcessSheetKnowledgeBaseFileMessage(ctx, &vfspb.ProcessSheetKnowledgeBaseFileEvent{
		MessageID:       messageID,
		KnowledgeBaseID: id,
		FileRelationID:  fileRelationID,
		Metadata:        metadata,
	}); err != nil {
		s.logger.Error(
			"send process sheet knowledge base file message failed",
			zap.Error(err),
			zap.Int64("knowledgeBaseID", id),
			zap.Int64("fileRelationID", fileRelationID),
			zap.String("metadata", metadata),
		)
	}
}

func (s *KnowledgeBaseService) InnerUpdateKnowledgeBaseFile(ctx context.Context, req *pb.InnerUpdateKnowledgeBaseFileRequest) (*pb.InnerUpdateKnowledgeBaseFileReply, error) {
	if err := s.uc.UpdateKnowledgeBaseFile(ctx, req.Id, req.FileRelationID, req.Metadata, req.Status, req.FailedReason); err != nil {
		return nil, fmt.Errorf("update knowledge base file metadata: %w", err)
	}
	return &pb.InnerUpdateKnowledgeBaseFileReply{}, nil
}

func (s *KnowledgeBaseService) PageKnowledgeBaseFile(ctx context.Context, req *pb.PageKnowledgeBaseFileRequest) (*pb.PageKnowledgeBaseFileReply, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	/// kb

	knowledgeBase, err := s.uc.GetKnowledgeBase(ctx, session.TenantID, req.Id)
	if err != nil {
		return nil, err
	}

	/// do

	items, total, err := s.uc.PageKnowledgeBaseFile(ctx, knowledgeBase, req.FileName)
	if err != nil {
		return nil, err
	}

	return &pb.PageKnowledgeBaseFileReply{
		Total:   total,
		Records: items,
	}, nil
}

func (s *KnowledgeBaseService) ExistKnowledgeBaseFile(ctx context.Context, req *pb.ExistKnowledgeBaseFileRequest) (*pb.ExistKnowledgeBaseFileReply, error) {
	exist, err := s.uc.ExistKnowledgeBaseFile(ctx, req.Id, req.FileRelationID)
	if err != nil {
		return nil, fmt.Errorf("check exist knowledge base file: %w", err)
	}
	return &pb.ExistKnowledgeBaseFileReply{Exist: exist}, nil
}

func (s *KnowledgeBaseService) GetFileKnowledgeBaseIDs(ctx context.Context, req *pb.GetFileKnowledgeBaseIDsRequest) (*pb.GetFileKnowledgeBaseIDsReply, error) {
	docKnowledgeBaseIDs, err := s.uc.GetFileKnowledgeBaseIDs(ctx, pb.KnowledgeBaseDataType_KnowledgeBaseDataTypeDoc, req.FileRelationID)
	if err != nil {
		return nil, fmt.Errorf("get file knowledge base ids: %w", err)
	}

	sheetKnowledgeBaseIDs, err := s.uc.GetFileKnowledgeBaseIDs(ctx, pb.KnowledgeBaseDataType_KnowledgeBaseDataTypeSheet, req.FileRelationID)
	if err != nil {
		return nil, fmt.Errorf("get file knowledge base ids: %w", err)
	}

	return &pb.GetFileKnowledgeBaseIDsReply{
		DocKnowledgeBaseIDs:   docKnowledgeBaseIDs,
		SheetKnowledgeBaseIDs: sheetKnowledgeBaseIDs,
	}, nil
}

func (s *KnowledgeBaseService) CountKnowledgeBaseFile(ctx context.Context, req *pb.CountKnowledgeBaseFileRequest) (*pb.CountKnowledgeBaseFileReply, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	var startTime, endTime time.Time
	if req.GetStartTime().IsValid() {
		startTime = req.GetStartTime().AsTime()
	}
	if req.GetEndTime().IsValid() {
		endTime = req.GetEndTime().AsTime()
	}

	count, err := s.uc.CountKnowledgeBaseFile(ctx, session.TenantID, startTime, endTime)
	if err != nil {
		return nil, err
	}

	return &pb.CountKnowledgeBaseFileReply{
		Count: count,
	}, nil
}

package service

import (
	"context"
	"time"

	"github.com/tx7do/kratos-transport/broker"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/externalmodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/kafka"
)

type ExternalModelService struct {
	pb.UnimplementedExternalModelServer
	logger      *zapadapter.Logger
	kafkaClient *kafka.Client
	uc          *biz.ExternalModelUseCase
}

func NewExternalModelService(logger *zapadapter.Logger, kafkaClient *kafka.Client, uc *biz.ExternalModelUseCase) *ExternalModelService {
	return &ExternalModelService{
		logger:      logger,
		kafkaClient: kafkaClient,
		uc:          uc,
	}
}

func (s *ExternalModelService) GetExternalModelConfig(ctx context.Context, req *pb.GetExternalModelConfigRequest) (*pb.GetExternalModelConfigReply, error) {
	items, err := s.uc.GetExternalModelConfig(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.GetExternalModelConfigReply{Items: items}, nil
}

func (s *ExternalModelService) UploadExternalModelUsage(ctx context.Context, req *pb.UploadExternalModelUsageRequest) (*pb.UploadExternalModelUsageReply, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, biz.ErrAuthSessionNotFound
	}

	var happenedAt time.Time
	if req.GetHappenedAt().IsValid() {
		happenedAt = req.GetHappenedAt().AsTime()
	}

	files := conv.ExternalModelFile.FromPbExternalModelFiles(req.Files)
	id, err := s.uc.CreateExternalModelUsage(ctx, session.UserID, req.ModelName, req.Question, req.PcName, files, happenedAt)
	if err != nil {
		return nil, err
	}

	if err := s.kafkaClient.SendExternalModelQuestionTagMessage(ctx, &pb.UpdateExternalModelUsageQuestionTag{Id: id, Question: req.Question}); err != nil {
		s.logger.Error("SendExternalModelQuestionTagMessage failed", zap.Error(err))
	}

	return &pb.UploadExternalModelUsageReply{}, nil
}

func (s *ExternalModelService) UpdateExternalModelUsageQuestionTag(ctx context.Context, topic string, headers broker.Headers, message *pb.UpdateExternalModelUsageQuestionTag) error {
	err := s.uc.UpdateQuestionTag(ctx, message.Id, message.Question)
	if err != nil {
		s.logger.Error("UpdateExternalModelUsageQuestionTag failed", zap.Error(err))
	}
	return nil
}

func (s *ExternalModelService) PageExternalModelUsage(ctx context.Context, req *pb.PageExternalModelUsageRequest) (*pb.PageExternalModelUsageReply, error) {
	var startTime, endTime time.Time
	if req.GetStartTime().IsValid() {
		startTime = req.GetStartTime().AsTime()
	}
	if req.GetEndTime().IsValid() {
		endTime = req.GetEndTime().AsTime()
	}

	externalModelUsages, total, err := s.uc.PageExternalModelUsage(ctx, req.Id, req.UserName, req.DeptName, startTime, endTime)
	if err != nil {
		return nil, err
	}

	var items []*pb.PageExternalModelUsageReplyItem
	for _, u := range externalModelUsages {
		items = append(items, &pb.PageExternalModelUsageReplyItem{
			Id:          u.ID,
			ModelName:   u.ModelName,
			ModelAvatar: u.ModelAvatar,
			Question:    u.Question,
			QuestionTag: u.QuestionTag,
			Files:       conv.ExternalModelFile.ToPbExternalModelFiles(u.Files),
			UserID:      u.UserID,
			UserName:    u.UserName,
			UserAvatar:  u.UserAvatar,
			DeptID:      u.DeptID,
			DeptName:    u.DeptName,
			PcName:      u.PcName,
			HappenedAt:  timestamppb.New(u.HappenedAt),
		})
	}

	return &pb.PageExternalModelUsageReply{Records: items, Total: total}, nil
}

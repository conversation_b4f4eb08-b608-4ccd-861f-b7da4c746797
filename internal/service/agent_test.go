package service_test

import (
	"context"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/timestamppb"
	"testing"
	"time"

	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"gitlab.minum.cloud/BackendTeam/pkg/middleware/page"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
)

func TestCreateAgent(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	agent, err := agentService.CreateAgent(ctx, &pb.CreateAgentRequest{
		Name:              "tesaaaaaaa",
		Description:       "test",
		Avatar:            "test",
		WelcomeMsg:        "test",
		FallbackMsg:       "test",
		OwnerId:           1,
		VisibilityType:    int64(pb.VisibilityType_VisibilityTypeAll),
		VisibleToUser:     []int64{1, 2},
		VisibleToDept:     []int64{2, 3},
		KnowledgeBaseIds:  []int64{1},
		Schema:            "test",
		IsPublic:          true,
		IsEnabled:         true,
		ShowReferenceFile: true,
		ModelType:         2,
		ModelID:           2,
		RoleSetting:       "123123",
		Thinking:          true,
		ThinkingModelID:   2,
		SecurityPolicies: []*pb.SecurityPolicy{
			{
				Name:           "test",
				PolicyCategory: int64(pb.PolicyCategory_PolicyCategorySensitiveInformationMatching),
				RiskLevel:      int64(pb.RiskLevel_RiskLevelHigh),
				Enabled:        true,
				Policies:       []string{"policy1", "policy2"},
				HitAction:      int64(pb.HitAction_HitActionBlock),
				HitResponse:    "This is a hit response",
			},
			{
				Name:           "test2",
				PolicyCategory: int64(pb.PolicyCategory_PolicyCategorySensitiveInformationMatching),
				RiskLevel:      int64(pb.RiskLevel_RiskLevelMedium),
				Enabled:        true,
				Policies:       []string{"policy3", "policy4"},
				HitAction:      int64(pb.HitAction_HitActionAlert),
				HitResponse:    "This is a hit response",
			},
		},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(agent)

}

func TestUpdateAgent(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	agent, err := agentService.UpdateAgent(ctx, &pb.UpdateAgentRequest{
		Id:                326,
		Name:              "213123fdfsdafsdafsdafsad",
		Description:       "test",
		Avatar:            "test",
		WelcomeMsg:        "test",
		FallbackMsg:       "test",
		OwnerId:           1,
		VisibilityType:    int64(pb.VisibilityType_VisibilityTypeAll),
		VisibleToUser:     []int64{1, 2},
		VisibleToDept:     []int64{2, 3},
		KnowledgeBaseIds:  []int64{1},
		Schema:            "test",
		IsPublic:          true,
		IsEnabled:         true,
		ShowReferenceFile: true,
		RoleSetting:       "123123",
		Thinking:          true,
		ThinkingModelID:   2,
		SecurityPolicies: []*pb.SecurityPolicy{
			{
				Id:             1,
				Name:           "test",
				PolicyCategory: int64(pb.PolicyCategory_PolicyCategorySensitiveInformationMatching),
				RiskLevel:      int64(pb.RiskLevel_RiskLevelHigh),
				Enabled:        true,
				Policies:       []string{"policy1", "policy2"},
				HitAction:      int64(pb.HitAction_HitActionBlock),
				HitResponse:    "This is a hit response",
			},
			{
				Id:             4,
				Name:           "test4",
				PolicyCategory: int64(pb.PolicyCategory_PolicyCategorySensitiveInformationMatching),
				RiskLevel:      int64(pb.RiskLevel_RiskLevelLow),
				Enabled:        true,
				Policies:       []string{"policy5", "policy6"},
				HitAction:      int64(pb.HitAction_HitActionAlert),
				HitResponse:    "This is a hit 1221231",
			},
		},
		//DeletedSecurityPolicyIDs: []int64{2},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(agent)
}

func TestGetAgent(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	agent, err := agentService.GetAgent(ctx, &pb.GetAgentRequest{
		Ids: []int64{641},
	})
	if err != nil {
		t.Fatal(err)
	}

	t.Log(agent)

}

func TestPageAgent(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   15,
	})

	ctx = page.With(ctx, 1, 10)

	agent, err := agentService.PageAgent(ctx, &pb.PageAgentRequest{
		ModelType:         1,
		AgentCategoryType: 4,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(agent)
}

func TestDeleteAgent(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   5,
	})

	_, err := agentService.DeleteAgent(ctx, &pb.DeleteAgentRequest{
		Id: 4,
	})
	if err != nil {
		t.Fatal(err)
	}
}

func TestGetAllDefaultAgentAvatars(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	agent, err := agentService.GetDefaultAvatars(ctx, &pb.GetDefaultAvatarsRequest{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(agent)
}

func TestGetFilePermissionByAgentID(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	ctx = page.With(ctx, 1, 100)
	hasPermissionReply, err := agentService.InternalModelChatPage(ctx, &pb.InternalModelChatPageRequest{
		ChatItemID: 891,
		StartTime:  nil,
		EndTime:    nil,
		//SearchModel: true,
		//ModelID:     1,
		//AgentID:     1,
		Searchfile: []string{"docx", "doc"},
		Class:      "1",
		NoRefFiles: false,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(hasPermissionReply)
}

func TestQueryModelAskClassificationDistribution(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	distribution, err := agentService.QueryModelAskClassificationDistribution(ctx, &pb.QueryModelAskClassificationDistributionRequest{
		ModelType: 1,
		StartTime: timestamppb.New(time.Now().Add(-1 * 1 * time.Hour)),
		EndTime:   timestamppb.New(time.Now()),
	})
	assert.Nil(t, err)
	assert.NotNil(t, distribution)
}

func TestQueryModelAskClassificationTop10(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	top10, err := agentService.QueryModelAskClassificationTop10(ctx, &pb.QueryModelAskClassificationTop10Request{
		ModelType: 1,
		TopType:   2,
		StartTime: timestamppb.New(time.Now().Add(-1 * 24 * time.Hour)),
		EndTime:   timestamppb.New(time.Now()),
	})
	assert.Nil(t, err)
	assert.NotNil(t, top10)
}

func TestQueryFileTypesDistribution(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	distribution, err := agentService.QueryUploadFileTypeDistribution(ctx, &pb.QueryUploadFileTypeDistributionRequest{
		ModelType: 1,
		StartTime: timestamppb.New(time.Now().Add(-1 * 100 * time.Hour)),
		EndTime:   timestamppb.New(time.Now()),
	})
	assert.Nil(t, err)
	assert.NotNil(t, distribution)
}

func TestQueryFileTypesTop10(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	top10, err := agentService.QueryUploadFileTypeTop10(ctx, &pb.QueryUploadFileTypeTop10Request{
		ModelType: 1,
		TopType:   2,
		StartTime: timestamppb.New(time.Now().Add(-1 * 24 * time.Hour)),
		EndTime:   timestamppb.New(time.Now()),
	})
	assert.Nil(t, err)
	assert.NotNil(t, top10)
}

func TestAgentService_SaveAgentQueueWhiteList(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	_, err := agentService.SaveAgentQueueWhiteList(ctx, &pb.SaveAgentQueueWhiteListRequest{
		UserIDs: []int64{1, 2},
	})
	if err != nil {
		t.Fatal(err)
	}
}

func TestAgentService_GetAgentQueueWhiteList(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	userIDs, err := agentService.GetAgentQueueWhiteList(ctx, &pb.GetAgentQueueWhiteListRequest{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(userIDs)
}

func TestSaveUserAgentOrder(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	_, err := agentService.UpdateAgentSort(ctx, &pb.UpdateAgentSortRequest{
		Items: []*pb.UpdateAgentSortRequestItem{
			{
				AgentID: 1,
				Index:   1,
			},
			{
				AgentID: 3,
				Index:   3,
			},
			{
				AgentID: 2,
				Index:   2,
			},
			{
				AgentID: 4,
				Index:   5,
			},
			{
				AgentID: 5,
				Index:   4,
			},
		},
	})
	if err != nil {
		t.Fatal(err)
	}
}

func TestCheckAgentSecurity(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	security, err := agentService.CheckQuestionSecurity(ctx, &pb.CheckQuestionSecurityRequest{
		Question: "公司上个月利润有多少？",
		AgentID:  326,
		ChatID:   999999,
		PcName:   "test-pc",
		UploadedFiles: []*pb.CheckQuestionSecurityRequest_FileInfo{
			{
				FileRelationID: 989898,
				Title:          "test file1",
				Size:           1024,
				MimeType:       "txt",
				UserID:         1,
				EntityTag:      "etag123",
				PreEntityTag:   "pre-entity-tag",
				Index:          0,
				ChunkIndex:     20,
				FullPath:       "/var/files/test_file1.txt",
			},
			{
				FileRelationID: 983398,
				Title:          "test file3",
				Size:           10242,
				MimeType:       "pdf",
				UserID:         2,
				EntityTag:      "e3343243",
				PreEntityTag:   "pre-entity-tag12312312",
				Index:          0,
				ChunkIndex:     21,
				FullPath:       "/var/files/test_file3.pdf",
			},
		},
	})

	if err != nil {
		t.Fatal(err)
	}
	t.Log(security)
}

func TestGetAgentSecurityLogsCount(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	count, err := agentService.GetAgentSecurityLogsCount(ctx, &pb.GetAgentSecurityLogsCountRequest{})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(count)

}

func TestGetAgentSecurityLogDetail(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	detail, err := agentService.GetAgentSecurityLogDetail(ctx, &pb.GetAgentSecurityLogDetailRequest{
		Id: 28,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(detail)
}

func TestPageAgentSecurityLogs(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	logs, err := agentService.PageAgentSecurityLogs(ctx, &pb.PageAgentSecurityLogsRequest{
		RiskLevel: []int64{3, 2},
		HitAction: []int64{1, 2},
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(logs)

}

func TestGetUserAgentsAndKnowledgeBases(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	reply, err := agentService.GetUserAgentsAndKnowledgeBases(ctx, &pb.GetUserAgentsAndKnowledgeBasesRequest{
		UserID: 1,
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(reply)
}

func TestGenerateQuestionOptimization(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	optimization, err := agentService.GenerateQuestionOptimization(ctx, &pb.GenerateQuestionOptimizationRequest{
		Question: "销售的平均销售多少钱？",
	})
	if err != nil {
		t.Fatal(err)
	}
	t.Log(optimization)

}

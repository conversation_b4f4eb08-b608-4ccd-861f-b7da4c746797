package service_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/externalmodel"
)

func Test_GetExternalModelConfig(t *testing.T) {
	ctx := context.Background()
	reply, err := externalModelService.GetExternalModelConfig(ctx, &pb.GetExternalModelConfigRequest{})
	assert.NoError(t, err)
	t.Log(reply)
}

func Test_UploadExternalModelUsage(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := externalModelService.UploadExternalModelUsage(ctx, &pb.UploadExternalModelUsageRequest{
		ModelName: "豆包",
		Question:  "你好",
		Files: []*pb.ExternalModelFile{
			{
				FileRelationID: 492344,
				PreEntityTag:   "\\",
				EntityTag:      "83095b1dd939c2965c07c204b85bf392c707efa4a0a63a50d1c0d2055cd0bf39",
				Name:           "GeneratedPluginRegistrant.swift",
				FullPath:       "test",
				Size:           2816,
				MimeType:       "swift",
			},
		},
		PcName:     "test",
		HappenedAt: timestamppb.Now(),
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func Test_PageExternalModelUsage(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := externalModelService.PageExternalModelUsage(ctx, &pb.PageExternalModelUsageRequest{
		PageNum:   1,
		PageSize:  10,
		StartTime: timestamppb.New(time.Time{}),
		EndTime:   timestamppb.Now(),
		UserName:  "",
		DeptName:  "",
	})
	assert.NoError(t, err)
	t.Log(reply)
}

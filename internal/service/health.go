package service

import (
	"context"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"

	skywalking_client "gitlab.minum.cloud/BackendTeam/pkg/skywalking-client"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/health"
)

type HealthService struct {
	pb.UnimplementedHealthServer
	bc     *conf.Bootstrap
	client skywalking_client.Client
}

func NewHealthService(bc *conf.Bootstrap, client skywalking_client.Client) *HealthService {
	return &HealthService{
		bc:     bc,
		client: client,
	}
}

func (s *HealthService) SayHello(ctx context.Context, req *pb.HealthRequest) (*pb.HealthReply, error) {
	return &pb.HealthReply{
		Message: "There ai-web",
	}, nil
}

func (s *HealthService) Trace(ctx context.Context, req *pb.TraceRequest) (*pb.TraceReply, error) {
	trace, err := s.client.QueryTrace(ctx, req.<PERSON><PERSON>)
	if err != nil {
		return nil, err
	}

	logs, err := s.client.QueryLogs(ctx, req.TraceID)
	if err != nil {
		return nil, err
	}

	return &pb.TraceReply{
		Trace: trace,
		Logs:  logs,
	}, nil
}

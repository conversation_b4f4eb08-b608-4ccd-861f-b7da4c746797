package service_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
)

func TestCreateKnowledgeBase(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.CreateKnowledgeBase(ctx, &pb.CreateKnowledgeBaseRequest{
		Name:            "test",
		Public:          true,
		DataType:        pb.KnowledgeBaseDataType_KnowledgeBaseDataTypeSheet,
		UserID:          3,
		ManagerUserIDs:  []int64{2},
		EditableUserIDs: []int64{1},
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestUpdateKnowledgeBase(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.UpdateKnowledgeBase(ctx, &pb.UpdateKnowledgeBaseRequest{
		Id:              1,
		Name:            "test",
		Public:          false,
		EditableUserIDs: []int64{2},
		OwnerOption: &pb.UpdateKnowledgeBaseRequest_OwnerOption{
			NewUserID:      1,
			ManagerUserIDs: []int64{1, 2},
		},
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestChangeKnowledgeBaseOwner(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.ChangeKnowledgeBaseOwner(ctx, &pb.ChangeKnowledgeBaseOwnerRequest{
		Ids:       []int64{1},
		NewUserID: 1,
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestDeleteKnowledgeBase(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.DeleteKnowledgeBase(ctx, &pb.DeleteKnowledgeBaseRequest{
		Id: 1,
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestGetKnowledgeBases(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.GetKnowledgeBases(ctx, &pb.GetKnowledgeBasesRequest{
		Ids: []int64{1},
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestPageKnowledgeBase(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.PageKnowledgeBase(ctx, &pb.PageKnowledgeBaseRequest{
		PageNum:  1,
		PageSize: 10,
		Name:     "",
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestAddKnowledgeBaseFile(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.AddKnowledgeBaseFile(ctx, &pb.AddKnowledgeBaseFileRequest{
		Id:              1,
		FileRelationIDs: []int64{1, 2},
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestDeleteKnowledgeBaseFile(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.DeleteKnowledgeBaseFile(ctx, &pb.DeleteKnowledgeBaseFileRequest{
		Id:              1,
		FileRelationIDs: []int64{1},
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestPageKnowledgeBaseFile(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.PageKnowledgeBaseFile(ctx, &pb.PageKnowledgeBaseFileRequest{
		PageNum:  1,
		PageSize: 10,
		Id:       2,
		FileName: "",
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func Test_ExistKnowledgeBaseFile(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.ExistKnowledgeBaseFile(ctx, &pb.ExistKnowledgeBaseFileRequest{
		Id:             1,
		FileRelationID: 1,
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestGetFileKnowledgeBaseIDs(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})

	reply, err := knowledgeBaseService.GetFileKnowledgeBaseIDs(ctx, &pb.GetFileKnowledgeBaseIDsRequest{
		FileRelationID: 1,
	})
	assert.NoError(t, err)
	t.Log(reply)
}

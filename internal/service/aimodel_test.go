package service_test

import (
	"context"
	"github.com/stretchr/testify/assert"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/aimodel"
	"google.golang.org/protobuf/types/known/timestamppb"
	"testing"
	"time"
)

func TestAimodelService_CreateAimodel(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	_, err := aiModelService.CreateAimodel(ctx, &pb.CreateAimodelRequest{
		ModelName: "test",
		Model:     1,
		ApiKey:    "123123123",
	})
	assert.Nil(t, err)
}

func TestAimodelService_UpdateAimodel(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	_, err := aiModelService.UpdateAimodel(ctx, &pb.UpdateAimodelRequest{
		Id:        1,
		ModelName: "test2",
		Model:     2,
		ApiKey:    "adfsdf",
	})
	assert.Nil(t, err)
}

func TestAimodelService_DeleteAimodel(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	_, err := aiModelService.DeleteAimodel(ctx, &pb.DeleteAimodelRequest{
		Id: 8,
	})
	assert.Nil(t, err)
}

func TestAimodelService_ListAimodel(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	aimodel, err := aiModelService.ListAimodel(ctx, nil)
	assert.Nil(t, err)
	t.Log(aimodel)
}

func TestAimodelService_ListExternalModel(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	aimodel, err := aiModelService.ListExternalModel(ctx, nil)
	assert.Nil(t, err)
	t.Log(aimodel)
}

func TestPageAiModelUsage(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	usage, err := aiModelService.PageAiModelUsageByAgentID(ctx, &pb.PageAiModelUsageByAgentIDRequest{
		AgentId: 1,
	})
	assert.Nil(t, err)
	assert.NotNil(t, usage)
}

func TestGetAiModelCallDetailByDay(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	usage, err := aiModelService.GetAiModelCallDetailByDay(ctx, &pb.GetAiModelCallDetailByDayRequest{
		StartTime: timestamppb.New(time.Now().AddDate(0, 0, -7)),
		EndTime:   timestamppb.New(time.Now()),
	})
	assert.Nil(t, err)
	assert.NotNil(t, usage)
}

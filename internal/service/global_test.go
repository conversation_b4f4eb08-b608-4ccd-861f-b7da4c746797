package service_test

import (
	"os"

	knacos "github.com/go-kratos/kratos/contrib/config/nacos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/go-kratos/kratos/v2/log"
	_ "github.com/lib/pq"
	"github.com/nacos-group/nacos-sdk-go/clients"
	"github.com/nacos-group/nacos-sdk-go/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/common/constant"
	"github.com/nacos-group/nacos-sdk-go/vo"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	_ "go.uber.org/automaxprocs"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data"
	_ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/service"
)

var (
	Name                  = "ai-web"
	Version               = "v3.1"
	id, _                 = os.Hostname()
	nacosHost             = "**************"
	nacosPort             = 8848
	nacosDataID           = "ai-web.yaml"
	nacosGroup            = "DEFAULT_GROUP"
	cfg                   *conf.Bootstrap
	ragService            *service.RagService
	classificationService *service.ClassificationService
	agentService          *service.AgentService
	knowledgeBaseService  *service.KnowledgeBaseService
	externalModelService  *service.ExternalModelService
	aiModelService        *service.AimodelService
)

func init() {
	var (
		logger      = newLog()
		nacosConfig = newNacosConfigParam(nacosHost, uint64(nacosPort))
		config      = newConfig(nacosConfig, logger)
	)

	defer config.Close()

	if err := config.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := config.Scan(&bc); err != nil {
		panic(err)
	}

	cfg = &bc

	wire(&bc, logger, nacosConfig)
}

func newLog() *zapadapter.Logger {
	cfg := zap.NewProductionEncoderConfig()
	cfg.EncodeTime = zapcore.RFC3339TimeEncoder
	encoder := zapcore.NewJSONEncoder(cfg)
	writeSyncer := zapcore.AddSync(os.Stderr)
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.DebugLevel)

	z := zap.New(core,
		zap.AddCaller(),
		zap.Fields(
			zap.String("service.id", id),
			zap.String("service.name", Name),
			zap.String("service.version", Version),
		),
	)

	logger := zapadapter.NewLogger(z)
	log.SetLogger(logger)

	return logger
}

func newNacosConfigParam(host string, port uint64) conf.NacosConfig {
	param := vo.NacosClientParam{
		ClientConfig: &constant.ClientConfig{
			TimeoutMs:           5000,
			NotLoadCacheAtStart: true,
			LogDir:              "/tmp/nacos/log",
			CacheDir:            "/tmp/nacos/cache",
			LogLevel:            "debug",
		},
		ServerConfigs: []constant.ServerConfig{
			*constant.NewServerConfig(host, port),
		},
	}

	return conf.NacosConfig{
		DataID: nacosDataID,
		Group:  nacosGroup,
		Param:  param,
	}
}

func newConfig(nacosConf conf.NacosConfig, logger *zapadapter.Logger) config.Config {
	c := config.New(
		config.WithSource(
			// file.NewSource(flagconf),
			knacos.NewConfigSource(newNacosConfigClient(nacosConf, logger),
				knacos.WithDataID(nacosConf.DataID),
				knacos.WithGroup(nacosConf.Group),
			),
		),
		config.WithDecoder(func(src *config.KeyValue, target map[string]interface{}) error {
			return encoding.GetCodec("yaml").Unmarshal(src.Value, &target)
		}),
	)

	return c
}

func newNacosConfigClient(nacosConf conf.NacosConfig, logger *zapadapter.Logger) config_client.IConfigClient {
	configClient, err := clients.NewConfigClient(nacosConf.Param)
	if err != nil {
		logger.Error("new nacos config client", zap.Error(err))
		return nil
	}

	return configClient
}

var rpcClient rpc.Client

func wire(bootstrap *conf.Bootstrap, logger *zapadapter.Logger, nacosConfig conf.NacosConfig) {
	iNamingClient := data.NewNacosNamingClient(nacosConfig, logger)
	discovery := data.NewDiscovery(nacosConfig, iNamingClient, logger)
	rpcClient, _ = rpc.NewClient(logger, discovery, bootstrap)
	kafkaClient := data.NewKafka(bootstrap, logger)
	dataData, _, _ := data.NewData(bootstrap, kafkaClient, logger)
	chatRepo := data.NewChatRepo(dataData, logger)
	chatItemRepo := data.NewChatItemRepo(dataData, logger)
	dbTransaction := data.NewDbTransaction(dataData)
	cryptoClient, _ := data.NewCryptoClient(logger)
	ossClient := data.NewOSSClient(logger, bootstrap, cryptoClient, rpcClient)
	agentRepo := data.NewAgentRepo(dataData, logger)
	qdrantClient, _ := data.NewQdrantClient(bootstrap)
	knowledgeBaseRepo := data.NewKnowledgeBaseRepo(dataData, logger)
	knowledgeBaseFileRepo := data.NewKnowledgeBaseFileRepo(dataData, logger)
	classificationFilesRepo := data.NewClassificationFilesRepo(dataData, logger)
	externalModelRepo := data.NewExternalModelRepo(dataData, logger)
	redisClient := data.NewRedis(dataData)
	classificationUseCase := biz.NewClassificationUseCase(logger, redisClient, classificationFilesRepo, rpcClient)
	classificationService = service.NewClassificationService(logger, classificationUseCase)
	knowledgeBaseUseCase := biz.NewKnowledgeBaseUseCase(logger, dbTransaction, bootstrap, qdrantClient, rpcClient, knowledgeBaseRepo, knowledgeBaseFileRepo, agentRepo, classificationFilesRepo)
	knowledgeBaseService = service.NewKnowledgeBaseService(logger, bootstrap, rpcClient, kafkaClient, redisClient, knowledgeBaseUseCase)
	aiModelRepo := data.NewAiModelRepo(dataData, logger)
	aiModelDetailRepo := data.NewAiModelDetailRepo(dataData, logger)
	aiModelUsecase := biz.NewAiModelUseCase(aiModelRepo, agentRepo, aiModelDetailRepo, logger, ossClient)
	chatUsecase := biz.NewChatUsecase(chatRepo, chatItemRepo, dbTransaction, logger, agentRepo, ossClient, aiModelRepo, aiModelDetailRepo)
	chatItemUsecase := biz.NewChatItemUsecase(chatItemRepo, externalModelRepo, dbTransaction, logger, rpcClient)
	externalModelUseCase := biz.NewExternalModelUseCase(logger, dbTransaction, bootstrap, rpcClient, ossClient, externalModelRepo, classificationFilesRepo)

	ragService = service.NewRagService(chatUsecase, chatItemUsecase, knowledgeBaseUseCase, rpcClient, discovery, logger)
	defaultAgentAvatarRepo := data.NewDefaultAgentAvatarRepoRepo(dataData, logger)
	userAgentOrderRepo := data.NewUserAgentOrderRepo(dataData, logger)
	aiAgentSecurityPolicyRepo := data.NewAiAgentSecurityPolicyRepo(dataData, logger)
	aiAgentSecurityLogRepo := data.NewAiAgentSecurityLogRepo(dataData, logger)
	agentUsecase := biz.NewAgentUsecase(agentRepo, defaultAgentAvatarRepo, logger, dbTransaction, ossClient, rpcClient, knowledgeBaseRepo, knowledgeBaseFileRepo, chatItemRepo, chatRepo, aiModelRepo, redisClient, userAgentOrderRepo, aiAgentSecurityPolicyRepo, aiAgentSecurityLogRepo)
	aiModelUsageRepo := data.NewAiModelUsageRepo(dataData, logger)
	aiModelUsageUseCase := biz.NewAiModelUsageUseCase(logger, aiModelUsageRepo, agentRepo, aiModelDetailRepo, rpcClient, ossClient)
	aiModelService = service.NewAimodelService(aiModelUsecase, rpcClient, aiModelUsageUseCase)
	agentService = service.NewAgentService(agentUsecase, logger, chatUsecase, chatItemUsecase, rpcClient, bootstrap, aiModelUsecase, classificationUseCase, aiModelUsageUseCase)
	externalModelService = service.NewExternalModelService(logger, kafkaClient, externalModelUseCase)
}

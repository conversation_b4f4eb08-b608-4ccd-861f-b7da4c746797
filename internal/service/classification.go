package service

import (
	"context"

	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/classification"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
)

type ClassificationService struct {
	pb.UnimplementedClassificationServer
	logger                *zapadapter.Logger
	classificationUseCase *biz.ClassificationUseCase
}

func NewClassificationService(logger *zapadapter.Logger, classificationUseCase *biz.ClassificationUseCase) *ClassificationService {
	return &ClassificationService{
		logger:                logger,
		classificationUseCase: classificationUseCase,
	}
}

func (s *ClassificationService) GetAllClassification(ctx context.Context, req *pb.GetAllClassificationRequest) (*pb.GetAllClassificationReply, error) {
	return &pb.GetAllClassificationReply{}, nil
}

func (s *ClassificationService) GetFileClassificationTags(ctx context.Context, req *pb.GetFileClassificationTagsRequest) (*pb.GetFileClassificationTagsReply, error) {
	var fileInfos []*biz.FileInfo
	for _, item := range req.Item {
		fileInfos = append(fileInfos, &biz.FileInfo{
			FileRelationID: item.FileRelationID,
			PreEntityTag:   item.PreEntityTag,
			EntityTag:      item.EntityTag,
		})
	}

	fileTags, err := s.classificationUseCase.GetClassificationFileTags(ctx, fileInfos)
	if err != nil {
		return nil, err
	}

	var classificationFileTags []*pb.ClassificationFileTag
	for _, fileTag := range fileTags {
		classificationFileTags = append(classificationFileTags, &pb.ClassificationFileTag{
			Id:             fileTag.ID,
			FileRelationID: fileTag.FileRelationID,
			PreEntityTag:   fileTag.PreEntityTag,
			EntityTag:      fileTag.EntityTag,
			FileName:       fileTag.FileName,
			FileType:       fileTag.FileType,
			UserID:         fileTag.UserID,
			UserName:       fileTag.UserName,
			DeptID:         fileTag.DeptID,
			DeptName:       fileTag.DeptName,
			Path:           fileTag.Path,
			Tags:           fileTag.Tags,
			SecurityLevel:  fileTag.SecurityLevel,
		})
	}

	return &pb.GetFileClassificationTagsReply{ClassificationFileTags: classificationFileTags}, nil
}

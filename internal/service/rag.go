package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"slices"
	"time"

	"github.com/go-kratos/kratos/v2/registry"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/samber/lo"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-api/aiapi/search"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/rag"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

type RagService struct {
	pb.UnimplementedRagServer
	logger     *zapadapter.Logger
	chatUc     *biz.ChatUsecase
	chatItemUc *biz.ChatItemUsecase
	kbUc       *biz.KnowledgeBaseUseCase
	rpc        rpc.Client
	discovery  registry.Discovery
}

func NewRagService(chatUc *biz.ChatUsecase, chatItemUc *biz.ChatItemUsecase, kbUc *biz.KnowledgeBaseUseCase, rpc rpc.Client, discovery registry.Discovery, logger *zapadapter.Logger) *RagService {
	return &RagService{
		logger:     logger,
		chatUc:     chatUc,
		chatItemUc: chatItemUc,
		kbUc:       kbUc,
		rpc:        rpc,
		discovery:  discovery,
	}
}

func RegisterRagChatHttpHandler(srv *http.Server, s *RagService) {
	srv.Route("/rag").POST("/chat", func(c http.Context) error {
		h := c.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			session, ok := authz.GetSession(ctx)
			newCtx := context.Background()
			if ok {
				tr, ok := transport.FromServerContext(ctx)
				if ok {
					newCtx = transport.NewClientContext(newCtx, tr)
				}
				newCtx = authz.SetSession(newCtx, session)
			}
			return nil, s.Chat(c, newCtx)
		})
		res, err := h(c, nil)
		if err != nil {
			return err
		}
		return c.Result(200, res)
	})
}

func (r *RagService) Chat(httpCtx http.Context, ctx context.Context) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	var req *pb.AskRagRequest
	if err := httpCtx.Bind(&req); err != nil {
		return err
	}

	session, ok := authz.GetSession(ctx)
	if !ok {
		return fmt.Errorf("session not found")
	}

	var roundId int64 = 0
	refFilesStr := ""
	refFileRelationIDs := make([]int64, 0)
	if req.Files != nil {
		refFilesMarshal, err := json.Marshal(req.Files)
		if err != nil {
			r.logger.Error("marshal ref files", zap.Error(err))
			return err
		}
		refFilesStr = string(refFilesMarshal)

		for _, file := range req.Files {
			refFileRelationIDs = append(refFileRelationIDs, file.GetFileRelationID())
		}
	}

	_, err := r.chatItemUc.Save(ctx, &biz.ChatItem{
		ChatID:     req.GetChatID(),
		ObjectID:   session.UserID,
		ObjectType: int64(pb.ChatObjectType_ChatObjectTypeUser),
		Message:    req.GetQuestion(),
		RefFiles:   refFilesStr,
		TenantID:   session.TenantID,
		RoundID:    roundId,
	})
	if err != nil {
		r.logger.Error("save chat item", zap.Error(err))
		return err
	}

	var resMessage string
	replyFilesStr := ""
	resFileRelationIDs := make([]int64, 0)

	httpCtx.Header().Set("Cache-Control", "no-cache")
	httpCtx.Header().Set("Connection", "keep-alive")
	httpCtx.Header().Set("Content-Type", "text/event-stream")
	httpCtx.Header().Set("X-Accel-Buffering", "no") // 禁止nginx缓存

	// 设置响应头为SSE
	contentType := "text/event-stream"
	reader, writer := io.Pipe()

	go func() {
		select {
		case <-ctx.Done(): // 客户端关闭连接时，ctx.Done() 会被触发
			r.logger.Info("client closed connection, shutting down writer and reader, save chat item")
			err = writer.Close()
			if err != nil {
				return
			}
			err = reader.Close()
			if err != nil {
				return
			}
			_, err := r.chatItemUc.Save(context.Background(), &biz.ChatItem{
				ChatID:     req.GetChatID(),
				ObjectID:   session.UserID,
				ObjectType: int64(pb.ChatObjectType_ChatObjectTypeSystem),
				Message:    resMessage,
				RefFiles:   replyFilesStr,
				TenantID:   session.TenantID,
				RoundID:    roundId,
			})
			if err != nil {
				r.logger.Error("save chat item", zap.Error(err))
			}
			return
		}
	}()

	go func() {
		defer cancel()

		res, err := r.rpc.QASearch(ctx, &search.QASearchRequest{
			Query:           req.Question,
			RoundID:         req.RoundID,
			FileRelationIDs: refFileRelationIDs,
		})
		if err != nil {
			r.logger.Error("call ai-api ", zap.Error(err))
			return
		}

		for {
			item, err := res.Recv()
			if err != nil {
				if err == io.EOF {
					r.logger.Info("recv from ai-api EOF")
				} else {
					r.logger.Error("recv from ai-api ", zap.Error(err))
				}
				return
			}
			if len(item.GetPayloads()) == 0 {
				resMessage += item.GetContent()
			}

			payloads := item.GetPayloads()
			roundId = item.GetRoundID()

			uniquePayloads := lo.UniqBy(payloads, func(p *search.ChatPayload) string {
				return fmt.Sprintf("%d", p.GetFileRelationID())
			})

			files := make([]*pb.AskRagReply_FileInfo, 0)
			for _, payload := range uniquePayloads {
				resFileRelationIDs = append(resFileRelationIDs, payload.GetFileRelationID())
				files = append(files, &pb.AskRagReply_FileInfo{
					FileRelationID: payload.GetFileRelationID(),
					Title:          payload.GetName(),
					Size:           payload.GetSize(),
					MimeType:       payload.GetMimeType(),
					UserID:         payload.GetUserID(),
					EntityTag:      payload.GetEntityTag(),
					PreEntityTag:   payload.GetPreEntityTag(),
					Index:          payload.GetIndex(),
					ChunkIndex:     payload.GetChunkIndex(),
				})
			}
			if len(files) > 0 {
				replyFilesMarshal, err := json.Marshal(files)
				if err != nil {
					r.logger.Error("marshal reply files", zap.Error(err))
					return
				}
				replyFilesStr = string(replyFilesMarshal)
			}

			reply := &pb.AskRagReply{
				Answer:  item.GetContent(),
				Files:   files,
				Status:  item.GetStatus(),
				RoundID: item.GetRoundID(),
				Type:    item.GetType(),
			}

			// 发送数据
			replyMarshal, err := json.Marshal(reply)
			if err != nil {
				r.logger.Error("marshal reply ", zap.Error(err))
				return
			}
			data := fmt.Sprintf("data: %s\n\n", string(replyMarshal))
			_, err = writer.Write([]byte(data))
			if err != nil {
				r.logger.Error("write data", zap.Error(err))
				return
			}
			if flusher, ok := httpCtx.Response().(http.Flusher); ok && flusher != nil {
				flusher.Flush() // Flush the data immediately
			}
			time.Sleep(50 * time.Millisecond)
		}
	}()

	return httpCtx.Stream(200, contentType, reader)
}

func (r *RagService) CreateChat(ctx context.Context, req *pb.CreateChatRequest) (*pb.CreateChatReply, error) {
	chat, err := r.chatUc.Save(ctx, req.Name, req.ChatType, req.AgentID)
	if err != nil {
		r.logger.Error("create chat", zap.Error(err))
		return nil, err
	}
	return &pb.CreateChatReply{
		ChatID: chat.ID,
	}, nil
}

// 聊天详情
func (r *RagService) ChatDetail(ctx context.Context, req *pb.ChatDetailRequest) (*pb.ChatDetailReply, error) {
	chat, err := r.chatUc.FindByID(ctx, req.ChatID)
	if err != nil {
		r.logger.Error("chat detail", zap.Error(err))
		return nil, err
	}
	return &pb.ChatDetailReply{
		ChatItems: lox.Map(chat.ChatItems, func(item *biz.ChatItem) *pb.ChatItem {
			res := &pb.ChatItem{
				Id:               item.ID,
				ChatID:           item.ChatID,
				ObjectID:         item.ObjectID,
				ObjectType:       int64(item.ObjectType),
				Message:          item.Message,
				RoundID:          item.RoundID,
				Reason:           item.Reason,
				AgreeStatus:      item.AgreeStatus,
				HitAction:        int64(item.HitAction),
				HitResponse:      item.HitResponse,
				HitContinueSend:  item.HitContinueSend,
				IsInternetSearch: item.IsInternetSearch,
				SuggestQuestions: item.SuggestQuestions,
			}
			if item.RefFiles != "" {
				var refFiles []*pb.FileInfo
				err := json.Unmarshal([]byte(item.RefFiles), &refFiles)
				if err != nil {
					r.logger.Error("unmarshal ref files", zap.Error(err))
				}
				res.Refs = refFiles
			}
			return res
		}),
	}, nil
}

// 重命名聊天
func (r *RagService) RenameChat(ctx context.Context, req *pb.RenameChatRequest) (*pb.RenameChatReply, error) {
	_, err := r.chatUc.Update(ctx, &biz.Chat{
		ID:   req.GetChatID(),
		Name: req.GetName(),
	})
	if err != nil {
		r.logger.Error("rename chat", zap.Error(err))
		return nil, err
	}
	return &pb.RenameChatReply{}, nil
}

// 删除聊天
func (r *RagService) DeleteChat(ctx context.Context, req *pb.DeleteChatRequest) (*pb.DeleteChatReply, error) {
	err := r.chatUc.Delete(ctx, req.GetChatIDs())
	if err != nil {
		r.logger.Error("delete chat", zap.Error(err))
		return nil, err
	}
	return &pb.DeleteChatReply{}, nil
}

func (r *RagService) PageChat(ctx context.Context, req *pb.PageChatRequest) (*pb.PageChatReply, error) {
	chats, total, err := r.chatUc.Page(ctx, req.GetAgentID())
	if err != nil {
		r.logger.Error("page chat", zap.Error(err))
		return nil, err

	}
	return &pb.PageChatReply{
		Total: total,
		Chats: lox.Map(chats, func(item *biz.Chat) *pb.Chat {
			return &pb.Chat{
				ChatID:               item.ID,
				Name:                 item.Name,
				ChatType:             int64(item.ChatType),
				AgentID:              item.AgentID,
				AgentAvatar:          item.AgentAvatar,
				AgentName:            item.AgentName,
				KnowledgeBaseType:    item.AgentKnowledgeBaseType,
				ModelType:            item.ModelType,
				CanInternetSearch:    item.CanInternetSearch,
				AgentType:            item.AgentType,
				FallbackMsg:          item.FallbackMsg,
				CreatedAt:            timestamppb.New(item.CreatedAt),
				Thinking:             item.Thinking,
				ThinkingEnableStatus: item.ThinkingEnableStatus,
			}
		}),
	}, nil
}

func (r *RagService) ChatCount(ctx context.Context, req *pb.ChatCountRequest) (*pb.ChatCountReply, error) {
	startTime := req.GetStartTime().AsTime()
	endTime := req.GetEndTime().AsTime()
	count, err := r.chatItemUc.GetChatCount(ctx, startTime, endTime)
	if err != nil {
		r.logger.Error("chat count", zap.Error(err))
		return nil, err
	}
	return &pb.ChatCountReply{
		Count: count,
	}, nil
}

func (r *RagService) FullTextSearch(ctx context.Context, req *pb.FullTextSearchRequest) (*pb.FullTextSearchReply, error) {
	textSearch, err := r.rpc.FullTextSearch(ctx, &search.FullTextSearchRequest{
		Query:          req.Query,
		SearchType:     req.SearchType,
		FileType:       req.FileType,
		OwnerIDs:       req.OwnerIDs,
		StartTime:      req.StartTime,
		EndTime:        req.EndTime,
		ClassPath:      req.ClassPath,
		Path:           req.Path,
		FilterSameFile: req.FilterSameFile,
		PageNum:        req.PageNum,
		PageSize:       req.PageSize,
	})
	if err != nil {
		r.logger.Error("full text search", zap.Error(err))
		return nil, err
	}

	var fileRelationIDs []int64
	for _, ref := range textSearch.Refs {
		fileRelationIDs = append(fileRelationIDs, ref.FileRelationID)
	}
	inKnowledgeBaseFileIDs, err := r.kbUc.GetInKnowledgeBaseFileIDs(ctx, fileRelationIDs)
	if err != nil {
		r.logger.Error("get in knowledge base file IDs", zap.Error(err))
		return nil, err
	}

	return &pb.FullTextSearchReply{
		Query:    textSearch.Query,
		TsQuery:  textSearch.TsQuery,
		PageNum:  textSearch.PageNum,
		PageSize: textSearch.PageSize,
		Total:    textSearch.Total,
		Refs: lox.Map(textSearch.Refs, func(item *search.Documents) *pb.Documents {
			return &pb.Documents{
				Text:            item.Text,
				EntityTag:       item.EntityTag,
				PreEntityTag:    item.PreEntityTag,
				FileRelationID:  item.FileRelationID,
				UpdatedAt:       item.UpdatedAt,
				Title:           item.Title,
				UserID:          item.UserID,
				UserName:        item.UserName,
				FullPath:        item.FullPath,
				TagNames:        item.TagNames,
				ClassPath:       item.ClassPath,
				Size:            item.Size,
				MimeType:        item.MimeType,
				CanDoAiProcess:  item.CanDoAiProcess,
				InKnowledgeBase: slices.Contains(inKnowledgeBaseFileIDs, item.FileRelationID),
			}
		}),
	}, nil
}

func (r *RagService) AgreeChatItem(ctx context.Context, req *pb.AgreeChatItemRequest) (*pb.AgreeChatItemReply, error) {
	err := r.chatItemUc.UpdateChatItemAgreeStatus(ctx, req.GetChatItemID(), req.GetAgreeStatus())
	if err != nil {
		r.logger.Error("agree chat item", zap.Error(err))
		return nil, err
	}
	return &pb.AgreeChatItemReply{}, nil
}

func (r *RagService) UpdateChatItemSuggestQuestions(ctx context.Context, req *pb.UpdateChatItemSuggestQuestionsRequest) (*pb.UpdateChatItemSuggestQuestionsReply, error) {
	if req.SuggestQuestions == nil || len(req.SuggestQuestions) == 0 {
		return &pb.UpdateChatItemSuggestQuestionsReply{}, nil
	}
	err := r.chatItemUc.UpdateChatItemSuggestQuestions(ctx, req.GetChatItemID(), req.GetSuggestQuestions())
	if err != nil {
		r.logger.Error("update chat item suggest questions", zap.Error(err))
		return nil, err
	}
	return &pb.UpdateChatItemSuggestQuestionsReply{}, nil
}

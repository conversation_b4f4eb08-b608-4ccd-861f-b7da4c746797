package service_test

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/rag"
)

func TestChatCount(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	count, err := ragService.ChatCount(ctx, &pb.ChatCountRequest{
		StartTime: timestamppb.New(time.Now().Add(-time.Hour * 24 * 30)),
		EndTime:   timestamppb.New(time.Now()),
	})
	assert.Nil(t, err)
	t.Log(count)
}

func TestPageChat(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   17,
	})
	chat, err := ragService.PageChat(ctx, &pb.PageChatRequest{
		PageNum:  1,
		PageSize: 100,
	})
	assert.Nil(t, err)
	t.Log(chat)
}

func TestName(t *testing.T) {
	image, err := rpcClient.GetImage(context.Background(), "image/d043783fb7cd70cd0b027b546a88096abdd78e1411f4f5e17e9221bd4902f403")
	if err != nil {
		t.Fatal(err)
	}
	// 转base64
	fmt.Println(base64.StdEncoding.EncodeToString(image))
	f, err := os.Create("as.html")
	if err != nil {
		t.Fatal(err)
	}
	defer f.Close()
	f.Write(image)
}

func TestUpdateChatItemAgreeStatus(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	_, err := ragService.AgreeChatItem(ctx, &pb.AgreeChatItemRequest{
		ChatItemID:  2,
		AgreeStatus: 1,
	})
	assert.Nil(t, err)
}

func Test_FullTextSearch(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   10,
	})

	reply, err := ragService.FullTextSearch(ctx, &pb.FullTextSearchRequest{
		Query:      "企业",
		SearchType: 1,
		ClassPath:  "总部",
		PageNum:    1,
		PageSize:   10,
	})
	assert.NoError(t, err)
	t.Log(reply)
}

func TestRagService_DeleteChat(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	_, err := ragService.DeleteChat(ctx, &pb.DeleteChatRequest{
		ChatIDs: []int64{180, 181},
	})
	assert.Nil(t, err)
}

func TestUpdateChatItemSuggestQuestions(t *testing.T) {
	ctx := context.Background()
	ctx = authz.SetSession(ctx, &authz.Session{
		TenantID: 1,
		UserID:   1,
	})
	_, err := ragService.UpdateChatItemSuggestQuestions(ctx, &pb.UpdateChatItemSuggestQuestionsRequest{
		ChatItemID:       3026,
		SuggestQuestions: []string{"1", "2", "3"},
	})
	assert.Nil(t, err)
}

package service

import (
	"context"
	"errors"
	"gitlab.minum.cloud/BackendTeam/admin/api/checklicense"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
	"strings"
	"time"

	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/aimodel"
)

type AimodelService struct {
	pb.UnimplementedAimodelServer
	usecase             *biz.AiModelUsecase
	rpc                 rpc.Client
	aiModelUsageUsecase *biz.AiModelUsageUseCase
}

func NewAimodelService(
	usecase *biz.AiModelUsecase,
	rpc rpc.Client,
	aiModelUsageUsecase *biz.AiModelUsageUseCase,
) *AimodelService {
	return &AimodelService{
		usecase:             usecase,
		rpc:                 rpc,
		aiModelUsageUsecase: aiModelUsageUsecase,
	}
}

func (s *AimodelService) CreateAimodel(ctx context.Context, req *pb.CreateAimodelRequest) (*pb.CreateAimodelReply, error) {

	// 判断license
	_, err := s.rpc.LicenseCheck(ctx, &checklicense.LicenseCheckRequest{
		LicenseType: checklicense.LicenseType_AIWorkbench,
	})
	if err != nil {
		return nil, err
	}

	_, err = s.usecase.Save(ctx, &biz.AiModel{
		ModelName: req.ModelName,
		Model:     req.Model,
		ApiKey:    req.ApiKey,
	})
	if err != nil {
		return nil, err
	}
	return &pb.CreateAimodelReply{}, nil
}
func (s *AimodelService) UpdateAimodel(ctx context.Context, req *pb.UpdateAimodelRequest) (*pb.UpdateAimodelReply, error) {
	_, err := s.usecase.Update(ctx, &biz.AiModel{
		ID:        req.Id,
		ModelName: req.ModelName,
		Model:     req.Model,
		ApiKey:    req.ApiKey,
	})
	if err != nil {
		return nil, err
	}
	return &pb.UpdateAimodelReply{}, nil
}
func (s *AimodelService) DeleteAimodel(ctx context.Context, req *pb.DeleteAimodelRequest) (*pb.DeleteAimodelReply, error) {
	err := s.usecase.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &pb.DeleteAimodelReply{}, nil
}
func (s *AimodelService) ListAimodel(ctx context.Context, req *pb.GetAimodelRequest) (*pb.GetAimodelReply, error) {
	res, err := s.usecase.List(ctx, req.GetModelName())
	if err != nil {
		return nil, err
	}
	return &pb.GetAimodelReply{
		Items: lox.Map(res, func(aiModel *biz.AiModel) *pb.AiModel {
			avatarUrl := ""
			canInternetSearch := false
			modelDetailName := ""
			if aiModel.ModelDetail != nil {
				avatarUrl = aiModel.ModelDetail.AvatarUrl
				canInternetSearch = aiModel.ModelDetail.CanInternetSearch
				modelDetailName = aiModel.ModelDetail.Name
			}
			maskedApiKey := aiModel.ApiKey
			if len(maskedApiKey) > 12 {
				lastFour := maskedApiKey[len(maskedApiKey)-4:] // 后4位
				firstEight := maskedApiKey[:8]                 // 前8位
				stars := strings.Repeat("*", 24)               // 固定24个*号
				maskedApiKey = firstEight + stars + lastFour
			}
			return &pb.AiModel{
				Id:                   aiModel.ID,
				ModelName:            aiModel.ModelName,
				Model:                aiModel.Model,
				ApiKey:               maskedApiKey,
				CreatedAt:            timestamppb.New(aiModel.CreatedAt),
				AvatarUrl:            avatarUrl,
				CanInternetSearch:    canInternetSearch,
				Balance:              aiModel.Balance,
				ThinkingEnableStatus: pb.ModelThinkingEnableStatus(aiModel.ThinkingEnableStatus),
				ModelDetailName:      modelDetailName,
				BackgroundUrl:        aiModel.BackgroundUrl,
			}
		}),
	}, nil
}

func (s *AimodelService) ListExternalModel(ctx context.Context, req *pb.ListExternalModelRequest) (*pb.ListExternalModelReply, error) {
	res, err := s.usecase.ListModelDetail(ctx)
	if err != nil {
		return nil, err
	}
	return &pb.ListExternalModelReply{
		Items: lox.Map(res, func(aiModelDetail *biz.AiModelDetail) *pb.AiModelDetail {
			return &pb.AiModelDetail{
				Id:                aiModelDetail.ID,
				Name:              aiModelDetail.Name,
				ModelName:         aiModelDetail.ModelName,
				Url:               aiModelDetail.Url,
				CanInternetSearch: aiModelDetail.CanInternetSearch,
				AvatarUrl:         aiModelDetail.AvatarUrl,
			}
		}),
	}, nil
}

func (s *AimodelService) PageAiModelUsageByAgentID(ctx context.Context, req *pb.PageAiModelUsageByAgentIDRequest) (*pb.PageAiModelUsageByAgentIDReply, error) {
	res, total, err := s.aiModelUsageUsecase.PageByAgentID(ctx, req.AgentId)
	if err != nil {
		return nil, err
	}
	return &pb.PageAiModelUsageByAgentIDReply{
		Total: total,
		Items: lox.Map(res, func(usage *biz.AiModelUsage) *pb.PageAiModelUsageByAgentIDReplyItem {
			return &pb.PageAiModelUsageByAgentIDReplyItem{
				Id:                usage.ID,
				ModelName:         usage.ModelName,
				ModelGatewayName:  usage.ModelGatewayName,
				ModelDetailAvatar: usage.ModelDetailAvatar,
				AgentId:           usage.AgentID,
				AgentName:         usage.AgentName,
				AgentAvatar:       usage.AgentAvatar,
				UserId:            usage.UserID,
				UserName:          usage.UserName,
				UserAvatar:        usage.UserAvatar,
				Question:          usage.Question,
				Answer:            usage.Answer,
				PromptTokens:      usage.PromptTokens,
				CompletionTokens:  usage.CompletionTokens,
				RequestStatus:     int64(usage.RequestStatus),
				ErrorCode:         usage.ErrorCode,
			}
		}),
	}, nil
}

func (s *AimodelService) GetAiModelCallDetailByDay(ctx context.Context, req *pb.GetAiModelCallDetailByDayRequest) (*pb.GetAiModelCallDetailByDayReply, error) {

	startTime := req.StartTime.AsTime()
	endTime := req.EndTime.AsTime()

	if startTime.IsZero() || endTime.IsZero() {
		return nil, errors.New("start time or end time is zero")
	}

	if startTime.After(endTime) {
		return nil, errors.New("start time cannot be after end time")
	}

	// 获取start和end中间的所有日期
	dates := make([]time.Time, 0)
	for d := startTime; d.Before(endTime) || d.Equal(endTime); d = d.AddDate(0, 0, 1) {
		dates = append(dates, d)
	}

	var res []*pb.GetAiModelCallDetailByDayReplyItem
	for i := range dates {
		res = append(res, &pb.GetAiModelCallDetailByDayReplyItem{
			Time: timestamppb.New(dates[i]),
		})
	}
	if len(res) == 0 {
		return nil, nil
	}

	aiModelDetails, err := s.usecase.ListModelDetail(ctx)
	if err != nil {
		return nil, err
	}

	for i := range aiModelDetails {
		usageDetails, err := s.aiModelUsageUsecase.GetAiModelUsageDetailGroupByDay(ctx, aiModelDetails[i].ID, startTime, endTime)
		if err != nil {
			return nil, err
		}
		if len(usageDetails) == 0 {
			continue
		}
		for j := range usageDetails {
			day := usageDetails[j].Day
			// 找到对应的日期, 在同一天
			for k := range res {
				year, month, d := res[k].Time.AsTime().Date()
				year2, month2, d2 := day.Date()
				if year == year2 && month == month2 && d == d2 {
					res[k].ModelCallDetails = append(res[k].ModelCallDetails, &pb.ModelCallDetail{
						ModelName:        aiModelDetails[i].Name,
						ApiCallCount:     usageDetails[j].CallSuccessCount,
						ApiErrCount:      usageDetails[j].CallFailCount,
						PromptTokens:     usageDetails[j].PromptTokens,
						CompletionTokens: usageDetails[j].CompletionTokens,
					})
					break
				}
			}
		}
	}
	for i := range res {
		if len(res[i].ModelCallDetails) < len(aiModelDetails) {
			// 补齐没有数据的模型
			for j := range aiModelDetails {
				found := false
				for k := range res[i].ModelCallDetails {
					if res[i].ModelCallDetails[k].ModelName == aiModelDetails[j].Name {
						found = true
						break
					}
				}
				if !found {
					res[i].ModelCallDetails = append(res[i].ModelCallDetails, &pb.ModelCallDetail{
						ModelName:        aiModelDetails[j].Name,
						ApiCallCount:     0,
						ApiErrCount:      0,
						PromptTokens:     0,
						CompletionTokens: 0,
					})
				}
			}
		}
	}

	return &pb.GetAiModelCallDetailByDayReply{
		Items: res,
	}, nil
}

package data

import (
	"errors"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/go-kratos/kratos/contrib/registry/nacos/v2"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/google/wire"
	_ "github.com/lib/pq"
	"github.com/nacos-group/nacos-sdk-go/clients"
	"github.com/nacos-group/nacos-sdk-go/clients/naming_client"
	"github.com/qdrant/go-client/qdrant"
	"github.com/redis/go-redis/v9"
	"gitlab.minum.cloud/BackendTeam/pkg/crypto"
	skywalking_client "gitlab.minum.cloud/BackendTeam/pkg/skywalking-client"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"go.uber.org/zap"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/http"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/kafka"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/oss"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewData,
	NewDbTransaction,
	NewKafka,
	NewRedis,
	NewNacosNamingClient,
	NewDiscovery,
	NewRegistrar,
	NewQdrantClient,
	NewChatRepo,
	NewChatItemRepo,
	NewClassificationFilesRepo,
	NewKnowledgeBaseRepo,
	NewKnowledgeBaseFileRepo,
	http.NewClient,
	rpc.NewClient,
	NewAgentRepo,
	NewCryptoClient,
	NewOSSClient,
	NewDefaultAgentAvatarRepoRepo,
	NewSkyWalkingClient,
	NewAtomicQuestionsRepo,
	NewAiModelRepo,
	NewAiModelDetailRepo,
	NewUserAgentOrderRepo,
	NewAiModelUsageRepo,
	NewAiAgentSecurityPolicyRepo,
	NewAiAgentSecurityLogRepo,
	NewExternalModelRepo,
)

// Data .
type Data struct {
	db  *ent.Database
	rdb *redis.Client
}

// NewDbTransaction .
func NewDbTransaction(data *Data) biz.DbTransaction {
	return data.db
}

func NewKafka(cfg *conf.Bootstrap, logger *zapadapter.Logger) *kafka.Client {
	k, err := kafka.NewKafka(cfg)
	if err != nil {
		logger.Error("new kafka", zap.Error(err))
	}

	return k
}

func NewNacosNamingClient(naconfConf conf.NacosConfig, logger *zapadapter.Logger) naming_client.INamingClient {
	namingClient, err := clients.NewNamingClient(naconfConf.Param)
	if err != nil {
		logger.Error("new nacos naming client", zap.Error(err))
		return nil
	}

	return namingClient
}

func NewDiscovery(naconfConf conf.NacosConfig, client naming_client.INamingClient, logger *zapadapter.Logger) registry.Discovery {
	if client == nil {
		logger.Error("new discovery", zap.Error(errors.New("nacos client is nil")))
		return nil
	}
	return nacos.New(client, nacos.WithGroup(naconfConf.Group))
}

func NewRegistrar(naconfConf conf.NacosConfig, client naming_client.INamingClient, logger *zapadapter.Logger) registry.Registrar {
	if client == nil {
		logger.Error("new registrar", zap.Error(errors.New("nacos client is nil")))
		return nil
	}

	return nacos.New(client, nacos.WithGroup(naconfConf.Group))
}

// NewData .
func NewData(cfg *conf.Bootstrap, k *kafka.Client, logger *zapadapter.Logger) (*Data, func(), error) {
	drv, err := sql.Open(
		cfg.Data.Database.Driver,
		cfg.Data.Database.Source,
	)
	if err != nil {
		logger.Error("opening connection", zap.String("driver", cfg.Data.Database.Driver), zap.Error(err))
		return nil, nil, err
	}

	rdb := redis.NewClient(&redis.Options{
		Addr:         cfg.Data.Redis.Addr,
		Password:     cfg.Data.Redis.Password,
		DB:           int(cfg.Data.Redis.Db),
		WriteTimeout: cfg.Data.Redis.WriteTimeout.AsDuration(),
		ReadTimeout:  cfg.Data.Redis.ReadTimeout.AsDuration(),
	})

	d := &Data{
		db:  ent.NewDatabase(ent.Driver(drv), ent.Debug()),
		rdb: rdb,
	}

	return d, func() {
		if err := drv.Close(); err != nil {
			logger.Error("close database", zap.Error(err))
		}
		logger.Info("closing the data resources success")

		if err := k.Close(); err != nil {
			logger.Error("close kafka", zap.Error(err))
		}
		logger.Info("close kafka client success")

		if err := rdb.Close(); err != nil {
			logger.Error("close redis", zap.Error(err))
		}
		logger.Info("close redis client success")
	}, nil
}

func NewRedis(data *Data) *redis.Client {
	return data.rdb
}

func NewQdrantClient(cfg *conf.Bootstrap) (*qdrant.Client, error) {
	return qdrant.NewClient(&qdrant.Config{
		Host: cfg.Vs.Host,
		Port: int(cfg.Vs.Port),
	})
}

func NewOSSClient(logger *zapadapter.Logger, cfg *conf.Bootstrap, crypto *crypto.Client, rpcClient rpc.Client) *oss.Client {
	return oss.NewClient(logger, cfg, crypto, rpcClient)
}

func NewCryptoClient(logger *zapadapter.Logger) (*crypto.Client, error) {
	return crypto.NewClient(5*time.Minute, logger.Logger)
}

func NewSkyWalkingClient(cfg *conf.Bootstrap) skywalking_client.Client {
	return skywalking_client.NewClient(cfg.Server.SkyWalkingQueryAddress)
}

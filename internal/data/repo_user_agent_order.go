package data

import (
	"context"
	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/useragentorder"
)

type userAgentOrderRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (u userAgentOrderRepo) GetUserAgentOrder(ctx context.Context, userID int64) ([]*biz.UserAgentOrder, error) {
	userAgentOrders, err := u.data.db.UserAgentOrder(ctx).Query().Where(useragentorder.UserIDEQ(userID)).All(ctx)
	if err != nil {
		return nil, err
	}
	return conv.UserAgentOrder.FromEnts(userAgentOrders), nil
}

func (u userAgentOrderRepo) Save(ctx context.Context, orders []*biz.UserAgentOrder) error {
	err := u.data.db.InTx(ctx, func(ctx context.Context) error {
		bulk := make([]*ent.UserAgentOrderCreate, 0)
		for _, order := range orders {
			bulk = append(bulk, u.data.db.UserAgentOrder(ctx).Create().SetUserID(order.UserID).SetAgentID(order.AgentID).SetOrderIndex(order.Index))
		}
		err := u.data.db.UserAgentOrder(ctx).CreateBulk(bulk...).OnConflict(sql.ConflictColumns(useragentorder.FieldUserID, useragentorder.FieldAgentID)).
			UpdateNewValues().Exec(ctx)
		return err
	})
	return err
}

func NewUserAgentOrderRepo(
	data *Data,
	log *zapadapter.Logger,
) biz.UserAgentOrderRepo {
	return &userAgentOrderRepo{
		data: data,
		log:  log,
	}
}

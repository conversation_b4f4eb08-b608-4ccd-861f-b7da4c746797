package data

import (
	"context"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritylog"
	"go.uber.org/zap"
	"time"
)

type aiAgentSecurityLogRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (a aiAgentSecurityLogRepo) GetRiskCount(ctx context.Context) (highRiskCount, midRiskCount, lowRiskCount int, err error) {
	highRiskCount, err = a.data.db.AiAgentSecurityLog(ctx).Query().Where(aiagentsecuritylog.RiskLevelEQ(int64(pb.RiskLevel_RiskLevelHigh))).Count(ctx)
	if err != nil {
		a.log.Error("failed to count high risk ai agent security logs", zap.Error(err))
		return 0, 0, 0, err
	}
	midRiskCount, err = a.data.db.AiAgentSecurityLog(ctx).Query().Where(aiagentsecuritylog.RiskLevelEQ(int64(pb.RiskLevel_RiskLevelMedium))).Count(ctx)
	if err != nil {
		a.log.Error("failed to count medium risk ai agent security logs", zap.Error(err))
		return 0, 0, 0, err
	}
	lowRiskCount, err = a.data.db.AiAgentSecurityLog(ctx).Query().Where(aiagentsecuritylog.RiskLevelEQ(int64(pb.RiskLevel_RiskLevelLow))).Count(ctx)
	if err != nil {
		a.log.Error("failed to count low risk ai agent security logs", zap.Error(err))
		return 0, 0, 0, err
	}
	return
}

func (a aiAgentSecurityLogRepo) GetHitActionCount(ctx context.Context) (blockCount, warningCount int, err error) {
	blockCount, err = a.data.db.AiAgentSecurityLog(ctx).Query().Where(aiagentsecuritylog.HitActionEQ(int64(pb.HitAction_HitActionBlock))).Count(ctx)
	if err != nil {
		a.log.Error("failed to count block ai agent security logs", zap.Error(err))
		return 0, 0, err
	}
	warningCount, err = a.data.db.AiAgentSecurityLog(ctx).Query().Where(aiagentsecuritylog.HitActionEQ(int64(pb.HitAction_HitActionAlert))).Count(ctx)
	if err != nil {
		a.log.Error("failed to count warning ai agent security logs", zap.Error(err))
		return 0, 0, err
	}
	return
}

func (a aiAgentSecurityLogRepo) Page(ctx context.Context, riskLevel, hitAction []int64, userName, deptName string, startTime, endTime time.Time) ([]*biz.AiAgentSecurityLog, int64, error) {
	// Build the query with filters
	query := a.data.db.AiAgentSecurityLog(ctx).Query().
		WhereOn(len(riskLevel) > 0, aiagentsecuritylog.RiskLevelIn(riskLevel...)).
		WhereOn(len(hitAction) > 0, aiagentsecuritylog.HitActionIn(hitAction...)).
		WhereOn(userName != "", aiagentsecuritylog.UserNameContains(userName)).
		WhereOn(deptName != "", aiagentsecuritylog.DeptNameContains(deptName)).
		WhereOn(!startTime.IsZero(), aiagentsecuritylog.CreatedAtGTE(startTime)).
		WhereOn(!endTime.IsZero(), aiagentsecuritylog.CreatedAtLTE(endTime))

	// Execute the query and get the count
	count, err := query.Count(ctx)
	if err != nil {
		a.log.Error("failed to count ai agent security logs", zap.Error(err))
		return nil, 0, err
	}

	// Get the logs
	logs, err := query.Order(aiagentsecuritylog.ByCreatedAt(sql.OrderDesc())).Paginate(ctx).All(ctx)
	if err != nil {
		a.log.Error("failed to fetch ai agent security logs", zap.Error(err))
		return nil, 0, err
	}

	return conv.AiAgentSecurityLog.FromEnts(logs), int64(count), nil
}

func (a aiAgentSecurityLogRepo) Get(ctx context.Context, id int64) (*biz.AiAgentSecurityLog, error) {
	agentSecurityLog, err := a.data.db.AiAgentSecurityLog(ctx).Query().Where(aiagentsecuritylog.IDEQ(id)).First(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil // Return nil if not found
		}
		a.log.Error("failed to get ai agent security log", zap.Error(err))
		return nil, err
	}
	return conv.AiAgentSecurityLog.FromEnt(agentSecurityLog), nil
}

func (a aiAgentSecurityLogRepo) Create(ctx context.Context, policies []*biz.AiAgentSecurityLog) ([]*biz.AiAgentSecurityLog, error) {
	if len(policies) == 0 {
		return nil, nil
	}
	creates := lox.Map(policies, func(item *biz.AiAgentSecurityLog) *ent.AiAgentSecurityLogCreate {
		return a.data.db.AiAgentSecurityLog(ctx).Create().
			SetAgentID(item.AgentID).
			SetRiskLevel(int64(item.RiskLevel)).
			SetUserID(item.UserID).
			SetUserName(item.UserName).
			SetDeptID(item.DeptID).
			SetDeptName(item.DeptName).
			SetPcName(item.PcName).
			SetAgentName(item.AgentName).
			SetAgentDescription(item.AgentDescription).
			SetActionCategory(item.ActionCategory).
			SetHitAction(int64(item.HitAction)).
			SetQuestion(item.Question).
			SetUploadedFiles((*pq.StringArray)(&item.UploadedFiles)).
			SetHitPolicies((*pq.StringArray)(&item.HitPolicies))
	})

	securityLogs, err := a.data.db.AiAgentSecurityLog(ctx).CreateBulk(creates...).Save(ctx)
	if err != nil {
		a.log.Error("failed to create ai agent security logs", zap.Error(err))
		return nil, err
	}
	return conv.AiAgentSecurityLog.FromEnts(securityLogs), nil
}

func (a aiAgentSecurityLogRepo) ListByAgentID(ctx context.Context, agentID int64) ([]*biz.AiAgentSecurityLog, error) {
	//TODO implement me
	panic("implement me")
}

func (a aiAgentSecurityLogRepo) Update(ctx context.Context, policies *biz.AiAgentSecurityLog) (*biz.AiAgentSecurityLog, error) {
	//TODO implement me
	panic("implement me")
}

func (a aiAgentSecurityLogRepo) Delete(ctx context.Context, id int64) error {
	//TODO implement me
	panic("implement me")
}

func NewAiAgentSecurityLogRepo(data *Data, logger *zapadapter.Logger) biz.AiAgentSecurityLogRepo {
	return &aiAgentSecurityLogRepo{
		data: data,
		log:  logger,
	}
}

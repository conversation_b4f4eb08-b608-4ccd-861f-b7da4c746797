package data

import (
	"context"
	"fmt"
	"strings"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/classificationfiles"
)

type classificationFilesRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func NewClassificationFilesRepo(data *Data, logger *zapadapter.Logger) biz.ClassificationFilesRepo {
	return &classificationFilesRepo{
		data: data,
		log:  logger,
	}
}

func (repo classificationFilesRepo) GetFileTags(ctx context.Context, fileRelationIDs []int64) (map[int64][]string, error) {
	if len(fileRelationIDs) == 0 {
		return nil, nil
	}

	classificationFiles, err := repo.data.db.ClassificationFiles(ctx).
		Query().
		Where(classificationfiles.FileRelationIDIn(fileRelationIDs...)).
		Where(classificationfiles.TreeTypeEQ(0)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("query classification files: %w", err)
	}

	res := make(map[int64][]string)
	for _, classificationFile := range classificationFiles {
		items := strings.Split(classificationFile.Path, ".")
		if len(items) > 2 {
			res[classificationFile.FileRelationID] = []string{items[1]}
		}
	}

	return res, nil
}

func (repo classificationFilesRepo) GetClassificationFileTags(ctx context.Context, fileInfos []*biz.FileInfo) ([]*biz.ClassificationFileTags, error) {
	if len(fileInfos) == 0 {
		return nil, nil
	}

	classificationFiles, err := repo.data.db.ClassificationFiles(ctx).
		Query().
		Where(classificationfiles.TreeTypeEQ(0)).
		Modify(func(cf *sql.Selector) {
			var ps []*sql.Predicate
			for _, fileInfo := range fileInfos {
				ps = append(ps, sql.And(
					sql.EQ(cf.C(classificationfiles.FieldFileRelationID), fileInfo.FileRelationID),
					sql.EQ(cf.C(classificationfiles.FieldPreEntityTag), fileInfo.PreEntityTag),
					sql.EQ(cf.C(classificationfiles.FieldEntityTag), fileInfo.EntityTag),
				))
			}
			cf.Where(sql.Or(ps...))
		}).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("query classification files: %w", err)
	}

	var classificationFileTags []*biz.ClassificationFileTags
	for _, classificationFile := range classificationFiles {
		var tags []string
		items := strings.Split(classificationFile.Path, ".")
		if len(items) > 2 {
			tags = []string{items[1]}
		}

		var deptID int64
		deptIDs := ([]int64)(*classificationFile.DeptIds)
		if len(deptIDs) > 0 {
			deptID = deptIDs[0]
		}

		classificationFileTags = append(classificationFileTags, &biz.ClassificationFileTags{
			ID:             classificationFile.ID,
			FileRelationID: classificationFile.FileRelationID,
			PreEntityTag:   classificationFile.PreEntityTag,
			EntityTag:      classificationFile.EntityTag,
			FileName:       classificationFile.Filename,
			FileType:       classificationFile.MimeType,
			UserID:         classificationFile.UserID,
			UserName:       classificationFile.UserName,
			DeptID:         deptID,
			DeptName:       classificationFile.DeptName,
			Path:           classificationFile.Path,
			Tags:           tags,
			SecurityLevel:  classificationFile.SecurityLevel,
		})
	}

	return classificationFileTags, nil
}

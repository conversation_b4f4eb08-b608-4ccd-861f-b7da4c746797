package data

import (
	"context"
	"entgo.io/ent/dialect/sql"
	"fmt"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	aimodel_pb "gitlab.minum.cloud/innovationteam/ai-web/api/aimodel"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

type agentRepo struct {
	data *Data
	log  *zapadapter.Logger
}

// FindByUserID implements biz.AgentRepo.
func (repo *agentRepo) FindByUserID(ctx context.Context, userID int64) ([]*biz.Agent, error) {
	agents, err := repo.data.db.AiAgent(ctx).Query().Where(aiagent.OwnerID(userID)).All(ctx)
	if err != nil {
		return nil, err
	}
	return conv.Agent.FromEnts(ctx, agents), nil
}

func (repo *agentRepo) FindByModelId(ctx context.Context, modelID int64) ([]*biz.Agent, error) {
	agents, err := repo.data.db.AiAgent(ctx).Query().Where(aiagent.ModelIDEQ(modelID)).All(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil
		}
		return nil, err
	}
	return conv.Agent.FromEnts(ctx, agents), nil
}

func (repo *agentRepo) GetAllAgent(ctx context.Context, modelType int64) ([]*biz.Agent, error) {
	agents, err := repo.data.db.AiAgent(ctx).Query().WhereOn(modelType > 0, aiagent.ModelTypeEQ(modelType)).All(ctx)
	if err != nil {
		return nil, err
	}
	return conv.Agent.FromEnts(ctx, agents), nil
}

func (repo *agentRepo) FindByID(ctx context.Context, id int64) (*biz.Agent, error) {
	ctx = mixin.SkipSoftDelete(ctx)
	agent, err := repo.data.db.AiAgent(ctx).Query().Where(aiagent.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return conv.Agent.FromEnt(ctx, agent), nil
}

func (repo *agentRepo) UpdateUseCount(ctx context.Context, agentID int64, useCount int64) error {
	_, err := repo.data.db.AiAgent(ctx).UpdateOneID(agentID).SetUseCount(useCount).Save(ctx)
	return err
}

func (repo *agentRepo) Count(ctx context.Context) (int64, error) {
	count, err := repo.data.db.AiAgent(ctx).Query().Count(ctx)
	if err != nil {
		return 0, err
	}
	return int64(count), nil
}

// Delete implements biz.AgentRepo.
func (repo *agentRepo) Delete(ctx context.Context, id int64) error {
	return repo.data.db.AiAgent(ctx).DeleteOneID(id).Exec(ctx)
}

// FindByIDs implements biz.AgentRepo.
func (repo *agentRepo) FindByIDs(ctx context.Context, ids []int64) ([]*biz.Agent, error) {
	agents, err := repo.data.db.AiAgent(ctx).Query().Where(aiagent.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return conv.Agent.FromEnts(ctx, agents), nil
}

// Page implements biz.AgentRepo.
func (repo *agentRepo) Page(ctx context.Context, userID int64, deptID int64, showOnClient bool, modelType int64, isMine bool, agentType []int64, agentName string, isCommonlyUsed bool) ([]*biz.Agent, int64, error) {
	var predicate *sql.Predicate
	var managePredicate *sql.Predicate
	if showOnClient {
		predicate = sql.And(sql.EQ(aiagent.FieldOwnerID, userID), sql.EQ(aiagent.FieldIsEnabled, true))
		managePredicate = sql.And(sql.EQ(aiagent.FieldIsEnabled, true), sql.ExprP(fmt.Sprintf("%d = ANY(manageable_to_user)", userID)))
	} else {
		predicate = sql.EQ(aiagent.FieldOwnerID, userID)
		managePredicate = sql.ExprP(fmt.Sprintf("%d = ANY(manageable_to_user)", userID))
	}
	condition := repo.data.db.AiAgent(ctx).Query().Where(func(s *sql.Selector) {
		s.Where(
			sql.Or(
				predicate,
				managePredicate,
				sql.And(
					sql.EQ(aiagent.FieldVisibilityType, int8(pb.VisibilityType_VisibilityTypeAll)),
					sql.EQ(aiagent.FieldIsEnabled, true),
				),
				sql.And(
					sql.EQ(aiagent.FieldVisibilityType, int8(pb.VisibilityType_VisibilityTypeUser)),
					sql.Or(
						sql.ExprP(fmt.Sprintf("%d = ANY(visible_to_user)", userID)),
						sql.ExprP(fmt.Sprintf("%d = ANY(visible_to_dept)", deptID)),
					),
					sql.EQ(aiagent.FieldIsEnabled, true),
				),
			),
		)
		if isMine {
			s.Where(sql.Or(sql.EQ(aiagent.FieldOwnerID, userID), sql.ExprP(fmt.Sprintf("%d = ANY(manageable_to_user)", userID))))
		}
		if modelType != 0 {
			s.Where(sql.EQ(aiagent.FieldModelType, modelType))
		}
		if len(agentType) > 0 {
			args := make([]interface{}, len(agentType))
			for i, v := range agentType {
				args[i] = v
			}
			s.Where(sql.In(aiagent.FieldAgentType, args...))
		}
		if agentName != "" {
			s.Where(sql.ContainsFold(aiagent.FieldName, agentName))
		}

	}).Order(aiagent.ByCreatedAt(sql.OrderDesc()))

	if isCommonlyUsed {
		condition = condition.WithAiChat(func(query *ent.AiChatQuery) {
			// 按照数量排序agent
		})
	}

	total, err := condition.Count(ctx)
	if err != nil {
		return nil, 0, err
	}
	agents, err := condition.Paginate(ctx).All(ctx)
	if err != nil {
		return nil, 0, err
	}
	return conv.Agent.FromEnts(ctx, agents), int64(total), nil
}

// Save implements biz.AgentRepo.
func (repo *agentRepo) Save(ctx context.Context, agent *biz.Agent) (*biz.Agent, error) {
	if agent.ModelType == 0 {
		agent.ModelType = int64(aimodel_pb.ModelType_ModelTypeInternal)
	}
	aiAgent, err := repo.data.db.AiAgent(ctx).Create().SetName(agent.Name).
		SetDescription(agent.Description).
		SetAvatar(agent.Avatar).
		SetClickedAvatar(agent.ClickedAvatar).
		SetWelcomeMsg(agent.WelcomeMsg).
		SetFallbackMsg(agent.FallbackMsg).
		SetOwnerID(agent.OwnerID).
		SetVisibilityType(int8(agent.VisibilityType)).
		SetVisibleToUser((*pq.Int64Array)(&agent.VisibleToUser)).
		SetVisibleToDept((*pq.Int64Array)(&agent.VisibleToDept)).
		SetSchema(agent.Schema).SetIsPublic(agent.IsPublic).
		SetKnowledgeBaseIds((*pq.Int64Array)(&agent.KnowledgeBaseIds)).
		SetIsRefFiles(agent.IsRefFiles).
		SetModelType(agent.ModelType).
		SetModelID(agent.ModelID).
		SetIsEnabled(agent.IsEnabled).
		SetInternetSearch(agent.InternetSearch).
		SetAgentType(agent.AgentType).
		SetRoleSetting(agent.RoleSetting).
		SetThinking(agent.Thinking).
		SetThinkingModelID(agent.ThinkingModelID).
		SetUploadFile(agent.UploadFile).
		SetSemanticCache(agent.SemanticCache).
		SetManageableToUser((*pq.Int64Array)(&agent.ManageableToUser)).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return conv.Agent.FromEnt(ctx, aiAgent), nil
}

// Update implements biz.AgentRepo.
func (repo *agentRepo) Update(ctx context.Context, agent *biz.Agent) (*biz.Agent, error) {
	update := repo.data.db.AiAgent(ctx).UpdateOneID(agent.ID)
	if agent.Name != "" {
		update = update.SetName(agent.Name)
	}
	if agent.Description != "" {
		update = update.SetDescription(agent.Description)
	}
	if agent.Avatar != "" {
		update = update.SetAvatar(agent.Avatar)
	}
	if agent.ClickedAvatar != "" {
		update = update.SetClickedAvatar(agent.ClickedAvatar)
	}
	if agent.WelcomeMsg != "" {
		update = update.SetWelcomeMsg(agent.WelcomeMsg)
	}
	if agent.FallbackMsg != "" {
		update = update.SetFallbackMsg(agent.FallbackMsg)
	}
	if agent.OwnerID != 0 {
		update = update.SetOwnerID(agent.OwnerID)
	}
	if agent.Schema != "" {
		update = update.SetSchema(agent.Schema)
	}
	if agent.ModelID > 0 {
		update = update.SetModelID(agent.ModelID)
	}
	update = update.SetKnowledgeBaseIds((*pq.Int64Array)(&agent.KnowledgeBaseIds))
	update = update.SetVisibilityType(int8(agent.VisibilityType))
	update = update.SetVisibleToUser((*pq.Int64Array)(&agent.VisibleToUser))
	update = update.SetVisibleToDept((*pq.Int64Array)(&agent.VisibleToDept))
	update = update.SetManageableToUser((*pq.Int64Array)(&agent.ManageableToUser))
	update = update.SetIsPublic(agent.IsPublic)
	update = update.SetIsEnabled(agent.IsEnabled)
	update = update.SetIsRefFiles(agent.IsRefFiles)
	update = update.SetInternetSearch(agent.InternetSearch)
	update = update.SetRoleSetting(agent.RoleSetting)
	update = update.SetThinking(agent.Thinking)
	update = update.SetThinkingModelID(agent.ThinkingModelID)
	update = update.SetUploadFile(agent.UploadFile)
	update = update.SetSemanticCache(agent.SemanticCache)
	aiAgent, err := update.Save(ctx)
	if err != nil {
		return nil, err
	}
	return conv.Agent.FromEnt(ctx, aiAgent), nil
}

func (repo *agentRepo) ExistKnowledgeBaseAgent(ctx context.Context, knowledgeBaseID int64) (bool, error) {
	return repo.data.db.AiAgent(ctx).
		Query().
		Where(func(s *sql.Selector) {
			s.Where(sql.ExprP(fmt.Sprintf("%d = ANY(knowledge_base_ids)", knowledgeBaseID)))
		}).
		Exist(ctx)
}

func NewAgentRepo(data *Data, logger *zapadapter.Logger) biz.AgentRepo {
	return &agentRepo{
		data: data,
		log:  logger,
	}
}

package data

import (
	"context"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"

	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
)

type chatRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (repo *chatRepo) GetByIDs(ctx context.Context, ids []int64) ([]*biz.Chat, error) {
	aiChats, err := repo.data.db.AiChat(ctx).Query().Where(aichat.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	return conv.Chat.FromEnts(ctx, aiChats), nil
}

// Delete implements biz.ChatRepo.
func (repo *chatRepo) Delete(ctx context.Context, ids []int64) error {
	_, err := repo.data.db.AiChat(ctx).Delete().Where(aichat.IDIn(ids...)).Exec(ctx)
	return err
}

// Page implements biz.ChatRepo.
func (repo *chatRepo) Page(ctx context.Context, userID int64, agentID int64) ([]*biz.Chat, int64, error) {
	query := repo.data.db.AiChat(ctx).Query().Where(aichat.UserIDEQ(userID)).WhereOn(agentID > 0, aichat.AgentID(agentID))
	total, err := query.Count(ctx)
	if err != nil {
		return nil, 0, err
	}
	aiChats, err := query.Paginate(ctx).Order(ent.Desc(aichat.FieldCreatedAt)).All(ctx)
	if err != nil {
		return nil, 0, err
	}
	return conv.Chat.FromEnts(ctx, aiChats), int64(total), nil
}

// FindByID implements biz.ChatRepo.
func (repo *chatRepo) FindByID(ctx context.Context, id int64) (*biz.Chat, error) {
	aiChat, err := repo.data.db.AiChat(ctx).Get(ctx, id)
	if err != nil {
		return nil, err
	}

	return conv.Chat.FromEnt(ctx, aiChat), nil
}

// ListAll implements biz.ChatRepo.
func (repo *chatRepo) ListAll(ctx context.Context, userID int64) ([]*biz.Chat, error) {
	aiChats, err := repo.data.db.AiChat(ctx).Query().Where(aichat.UserID(userID)).All(ctx)
	if err != nil {
		return nil, err
	}

	return conv.Chat.FromEnts(ctx, aiChats), nil
}

// Save implements biz.ChatRepo.
func (repo *chatRepo) Save(ctx context.Context, chat *biz.Chat) (*biz.Chat, error) {
	aiChat, err := repo.data.db.AiChat(ctx).Create().SetName(chat.Name).SetAgentID(chat.AgentID).SetChatType(int64(chat.ChatType.Number())).SetUserID(chat.UserID).SetTenantID(chat.TenantID).Save(ctx)
	if err != nil {
		return nil, err
	}
	return conv.Chat.FromEnt(ctx, aiChat), nil
}

// Update implements biz.ChatRepo.
func (repo *chatRepo) Update(ctx context.Context, chat *biz.Chat) (*biz.Chat, error) {
	if chat == nil {
		return nil, nil
	}

	update := repo.data.db.AiChat(ctx).UpdateOneID(chat.ID)

	if chat.Name != "" {
		update = update.SetName(chat.Name)
	}

	aiChat, err := update.Save(ctx)
	if err != nil {
		return nil, err
	}

	return conv.Chat.FromEnt(ctx, aiChat), nil
}

func NewChatRepo(data *Data, logger *zapadapter.Logger) biz.ChatRepo {
	return &chatRepo{
		data: data,
		log:  logger,
	}
}

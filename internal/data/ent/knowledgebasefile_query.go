// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebasefile"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// KnowledgeBaseFileQuery is the builder for querying KnowledgeBaseFile entities.
type KnowledgeBaseFileQuery struct {
	config
	ctx        *QueryContext
	order      []knowledgebasefile.OrderOption
	inters     []Interceptor
	predicates []predicate.KnowledgeBaseFile
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the KnowledgeBaseFileQuery builder.
func (kbfq *KnowledgeBaseFileQuery) Where(ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileQuery {
	kbfq.predicates = append(kbfq.predicates, ps...)
	return kbfq
}

// Limit the number of records to be returned by this query.
func (kbfq *KnowledgeBaseFileQuery) Limit(limit int) *KnowledgeBaseFileQuery {
	kbfq.ctx.Limit = &limit
	return kbfq
}

// Offset to start from.
func (kbfq *KnowledgeBaseFileQuery) Offset(offset int) *KnowledgeBaseFileQuery {
	kbfq.ctx.Offset = &offset
	return kbfq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (kbfq *KnowledgeBaseFileQuery) Unique(unique bool) *KnowledgeBaseFileQuery {
	kbfq.ctx.Unique = &unique
	return kbfq
}

// Order specifies how the records should be ordered.
func (kbfq *KnowledgeBaseFileQuery) Order(o ...knowledgebasefile.OrderOption) *KnowledgeBaseFileQuery {
	kbfq.order = append(kbfq.order, o...)
	return kbfq
}

// First returns the first KnowledgeBaseFile entity from the query.
// Returns a *NotFoundError when no KnowledgeBaseFile was found.
func (kbfq *KnowledgeBaseFileQuery) First(ctx context.Context) (*KnowledgeBaseFile, error) {
	nodes, err := kbfq.Limit(1).All(setContextOp(ctx, kbfq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{knowledgebasefile.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (kbfq *KnowledgeBaseFileQuery) FirstX(ctx context.Context) *KnowledgeBaseFile {
	node, err := kbfq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first KnowledgeBaseFile ID from the query.
// Returns a *NotFoundError when no KnowledgeBaseFile ID was found.
func (kbfq *KnowledgeBaseFileQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = kbfq.Limit(1).IDs(setContextOp(ctx, kbfq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{knowledgebasefile.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (kbfq *KnowledgeBaseFileQuery) FirstIDX(ctx context.Context) int64 {
	id, err := kbfq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single KnowledgeBaseFile entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one KnowledgeBaseFile entity is found.
// Returns a *NotFoundError when no KnowledgeBaseFile entities are found.
func (kbfq *KnowledgeBaseFileQuery) Only(ctx context.Context) (*KnowledgeBaseFile, error) {
	nodes, err := kbfq.Limit(2).All(setContextOp(ctx, kbfq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{knowledgebasefile.Label}
	default:
		return nil, &NotSingularError{knowledgebasefile.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (kbfq *KnowledgeBaseFileQuery) OnlyX(ctx context.Context) *KnowledgeBaseFile {
	node, err := kbfq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only KnowledgeBaseFile ID in the query.
// Returns a *NotSingularError when more than one KnowledgeBaseFile ID is found.
// Returns a *NotFoundError when no entities are found.
func (kbfq *KnowledgeBaseFileQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = kbfq.Limit(2).IDs(setContextOp(ctx, kbfq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{knowledgebasefile.Label}
	default:
		err = &NotSingularError{knowledgebasefile.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (kbfq *KnowledgeBaseFileQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := kbfq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of KnowledgeBaseFiles.
func (kbfq *KnowledgeBaseFileQuery) All(ctx context.Context) ([]*KnowledgeBaseFile, error) {
	ctx = setContextOp(ctx, kbfq.ctx, "All")
	if err := kbfq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*KnowledgeBaseFile, *KnowledgeBaseFileQuery]()
	return withInterceptors[[]*KnowledgeBaseFile](ctx, kbfq, qr, kbfq.inters)
}

// AllX is like All, but panics if an error occurs.
func (kbfq *KnowledgeBaseFileQuery) AllX(ctx context.Context) []*KnowledgeBaseFile {
	nodes, err := kbfq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of KnowledgeBaseFile IDs.
func (kbfq *KnowledgeBaseFileQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if kbfq.ctx.Unique == nil && kbfq.path != nil {
		kbfq.Unique(true)
	}
	ctx = setContextOp(ctx, kbfq.ctx, "IDs")
	if err = kbfq.Select(knowledgebasefile.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (kbfq *KnowledgeBaseFileQuery) IDsX(ctx context.Context) []int64 {
	ids, err := kbfq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (kbfq *KnowledgeBaseFileQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, kbfq.ctx, "Count")
	if err := kbfq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, kbfq, querierCount[*KnowledgeBaseFileQuery](), kbfq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (kbfq *KnowledgeBaseFileQuery) CountX(ctx context.Context) int {
	count, err := kbfq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (kbfq *KnowledgeBaseFileQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, kbfq.ctx, "Exist")
	switch _, err := kbfq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (kbfq *KnowledgeBaseFileQuery) ExistX(ctx context.Context) bool {
	exist, err := kbfq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the KnowledgeBaseFileQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (kbfq *KnowledgeBaseFileQuery) Clone() *KnowledgeBaseFileQuery {
	if kbfq == nil {
		return nil
	}
	return &KnowledgeBaseFileQuery{
		config:     kbfq.config,
		ctx:        kbfq.ctx.Clone(),
		order:      append([]knowledgebasefile.OrderOption{}, kbfq.order...),
		inters:     append([]Interceptor{}, kbfq.inters...),
		predicates: append([]predicate.KnowledgeBaseFile{}, kbfq.predicates...),
		// clone intermediate query.
		sql:  kbfq.sql.Clone(),
		path: kbfq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.KnowledgeBaseFile.Query().
//		GroupBy(knowledgebasefile.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (kbfq *KnowledgeBaseFileQuery) GroupBy(field string, fields ...string) *KnowledgeBaseFileGroupBy {
	kbfq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &KnowledgeBaseFileGroupBy{build: kbfq}
	grbuild.flds = &kbfq.ctx.Fields
	grbuild.label = knowledgebasefile.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//	}
//
//	client.KnowledgeBaseFile.Query().
//		Select(knowledgebasefile.FieldTenantID).
//		Scan(ctx, &v)
func (kbfq *KnowledgeBaseFileQuery) Select(fields ...string) *KnowledgeBaseFileSelect {
	kbfq.ctx.Fields = append(kbfq.ctx.Fields, fields...)
	sbuild := &KnowledgeBaseFileSelect{KnowledgeBaseFileQuery: kbfq}
	sbuild.label = knowledgebasefile.Label
	sbuild.flds, sbuild.scan = &kbfq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a KnowledgeBaseFileSelect configured with the given aggregations.
func (kbfq *KnowledgeBaseFileQuery) Aggregate(fns ...AggregateFunc) *KnowledgeBaseFileSelect {
	return kbfq.Select().Aggregate(fns...)
}

func (kbfq *KnowledgeBaseFileQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range kbfq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, kbfq); err != nil {
				return err
			}
		}
	}
	for _, f := range kbfq.ctx.Fields {
		if !knowledgebasefile.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if kbfq.path != nil {
		prev, err := kbfq.path(ctx)
		if err != nil {
			return err
		}
		kbfq.sql = prev
	}
	return nil
}

func (kbfq *KnowledgeBaseFileQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*KnowledgeBaseFile, error) {
	var (
		nodes = []*KnowledgeBaseFile{}
		_spec = kbfq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*KnowledgeBaseFile).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &KnowledgeBaseFile{config: kbfq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(kbfq.modifiers) > 0 {
		_spec.Modifiers = kbfq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, kbfq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (kbfq *KnowledgeBaseFileQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := kbfq.querySpec()
	if len(kbfq.modifiers) > 0 {
		_spec.Modifiers = kbfq.modifiers
	}
	_spec.Node.Columns = kbfq.ctx.Fields
	if len(kbfq.ctx.Fields) > 0 {
		_spec.Unique = kbfq.ctx.Unique != nil && *kbfq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, kbfq.driver, _spec)
}

func (kbfq *KnowledgeBaseFileQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(knowledgebasefile.Table, knowledgebasefile.Columns, sqlgraph.NewFieldSpec(knowledgebasefile.FieldID, field.TypeInt64))
	_spec.From = kbfq.sql
	if unique := kbfq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if kbfq.path != nil {
		_spec.Unique = true
	}
	if fields := kbfq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, knowledgebasefile.FieldID)
		for i := range fields {
			if fields[i] != knowledgebasefile.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := kbfq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := kbfq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := kbfq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := kbfq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (kbfq *KnowledgeBaseFileQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(kbfq.driver.Dialect())
	t1 := builder.Table(knowledgebasefile.Table)
	columns := kbfq.ctx.Fields
	if len(columns) == 0 {
		columns = knowledgebasefile.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if kbfq.sql != nil {
		selector = kbfq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if kbfq.ctx.Unique != nil && *kbfq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range kbfq.modifiers {
		m(selector)
	}
	for _, p := range kbfq.predicates {
		p(selector)
	}
	for _, p := range kbfq.order {
		p(selector)
	}
	if offset := kbfq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := kbfq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (kbfq *KnowledgeBaseFileQuery) ForUpdate(opts ...sql.LockOption) *KnowledgeBaseFileQuery {
	if kbfq.driver.Dialect() == dialect.Postgres {
		kbfq.Unique(false)
	}
	kbfq.modifiers = append(kbfq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return kbfq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (kbfq *KnowledgeBaseFileQuery) ForShare(opts ...sql.LockOption) *KnowledgeBaseFileQuery {
	if kbfq.driver.Dialect() == dialect.Postgres {
		kbfq.Unique(false)
	}
	kbfq.modifiers = append(kbfq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return kbfq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (kbfq *KnowledgeBaseFileQuery) Modify(modifiers ...func(s *sql.Selector)) *KnowledgeBaseFileSelect {
	kbfq.modifiers = append(kbfq.modifiers, modifiers...)
	return kbfq.Select()
}

// KnowledgeBaseFileGroupBy is the group-by builder for KnowledgeBaseFile entities.
type KnowledgeBaseFileGroupBy struct {
	selector
	build *KnowledgeBaseFileQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (kbfgb *KnowledgeBaseFileGroupBy) Aggregate(fns ...AggregateFunc) *KnowledgeBaseFileGroupBy {
	kbfgb.fns = append(kbfgb.fns, fns...)
	return kbfgb
}

// Scan applies the selector query and scans the result into the given value.
func (kbfgb *KnowledgeBaseFileGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, kbfgb.build.ctx, "GroupBy")
	if err := kbfgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*KnowledgeBaseFileQuery, *KnowledgeBaseFileGroupBy](ctx, kbfgb.build, kbfgb, kbfgb.build.inters, v)
}

func (kbfgb *KnowledgeBaseFileGroupBy) sqlScan(ctx context.Context, root *KnowledgeBaseFileQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(kbfgb.fns))
	for _, fn := range kbfgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*kbfgb.flds)+len(kbfgb.fns))
		for _, f := range *kbfgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*kbfgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := kbfgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// KnowledgeBaseFileSelect is the builder for selecting fields of KnowledgeBaseFile entities.
type KnowledgeBaseFileSelect struct {
	*KnowledgeBaseFileQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (kbfs *KnowledgeBaseFileSelect) Aggregate(fns ...AggregateFunc) *KnowledgeBaseFileSelect {
	kbfs.fns = append(kbfs.fns, fns...)
	return kbfs
}

// Scan applies the selector query and scans the result into the given value.
func (kbfs *KnowledgeBaseFileSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, kbfs.ctx, "Select")
	if err := kbfs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*KnowledgeBaseFileQuery, *KnowledgeBaseFileSelect](ctx, kbfs.KnowledgeBaseFileQuery, kbfs, kbfs.inters, v)
}

func (kbfs *KnowledgeBaseFileSelect) sqlScan(ctx context.Context, root *KnowledgeBaseFileQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(kbfs.fns))
	for _, fn := range kbfs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*kbfs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := kbfs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (kbfs *KnowledgeBaseFileSelect) Modify(modifiers ...func(s *sql.Selector)) *KnowledgeBaseFileSelect {
	kbfs.modifiers = append(kbfs.modifiers, modifiers...)
	return kbfs
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/classificationfiles"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ClassificationFilesUpdate is the builder for updating ClassificationFiles entities.
type ClassificationFilesUpdate struct {
	config
	hooks     []Hook
	mutation  *ClassificationFilesMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the ClassificationFilesUpdate builder.
func (cfu *ClassificationFilesUpdate) Where(ps ...predicate.ClassificationFiles) *ClassificationFilesUpdate {
	cfu.mutation.Where(ps...)
	return cfu
}

// SetName sets the "name" field.
func (cfu *ClassificationFilesUpdate) SetName(s string) *ClassificationFilesUpdate {
	cfu.mutation.SetName(s)
	return cfu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableName(s *string) *ClassificationFilesUpdate {
	if s != nil {
		cfu.SetName(*s)
	}
	return cfu
}

// SetFileRelationID sets the "file_relation_id" field.
func (cfu *ClassificationFilesUpdate) SetFileRelationID(i int64) *ClassificationFilesUpdate {
	cfu.mutation.ResetFileRelationID()
	cfu.mutation.SetFileRelationID(i)
	return cfu
}

// SetNillableFileRelationID sets the "file_relation_id" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableFileRelationID(i *int64) *ClassificationFilesUpdate {
	if i != nil {
		cfu.SetFileRelationID(*i)
	}
	return cfu
}

// AddFileRelationID adds i to the "file_relation_id" field.
func (cfu *ClassificationFilesUpdate) AddFileRelationID(i int64) *ClassificationFilesUpdate {
	cfu.mutation.AddFileRelationID(i)
	return cfu
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (cfu *ClassificationFilesUpdate) SetPreEntityTag(s string) *ClassificationFilesUpdate {
	cfu.mutation.SetPreEntityTag(s)
	return cfu
}

// SetNillablePreEntityTag sets the "pre_entity_tag" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillablePreEntityTag(s *string) *ClassificationFilesUpdate {
	if s != nil {
		cfu.SetPreEntityTag(*s)
	}
	return cfu
}

// SetEntityTag sets the "entity_tag" field.
func (cfu *ClassificationFilesUpdate) SetEntityTag(s string) *ClassificationFilesUpdate {
	cfu.mutation.SetEntityTag(s)
	return cfu
}

// SetNillableEntityTag sets the "entity_tag" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableEntityTag(s *string) *ClassificationFilesUpdate {
	if s != nil {
		cfu.SetEntityTag(*s)
	}
	return cfu
}

// SetFilename sets the "filename" field.
func (cfu *ClassificationFilesUpdate) SetFilename(s string) *ClassificationFilesUpdate {
	cfu.mutation.SetFilename(s)
	return cfu
}

// SetNillableFilename sets the "filename" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableFilename(s *string) *ClassificationFilesUpdate {
	if s != nil {
		cfu.SetFilename(*s)
	}
	return cfu
}

// SetMimeType sets the "mime_type" field.
func (cfu *ClassificationFilesUpdate) SetMimeType(s string) *ClassificationFilesUpdate {
	cfu.mutation.SetMimeType(s)
	return cfu
}

// SetNillableMimeType sets the "mime_type" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableMimeType(s *string) *ClassificationFilesUpdate {
	if s != nil {
		cfu.SetMimeType(*s)
	}
	return cfu
}

// SetUserID sets the "user_id" field.
func (cfu *ClassificationFilesUpdate) SetUserID(i int64) *ClassificationFilesUpdate {
	cfu.mutation.ResetUserID()
	cfu.mutation.SetUserID(i)
	return cfu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableUserID(i *int64) *ClassificationFilesUpdate {
	if i != nil {
		cfu.SetUserID(*i)
	}
	return cfu
}

// AddUserID adds i to the "user_id" field.
func (cfu *ClassificationFilesUpdate) AddUserID(i int64) *ClassificationFilesUpdate {
	cfu.mutation.AddUserID(i)
	return cfu
}

// SetUserName sets the "user_name" field.
func (cfu *ClassificationFilesUpdate) SetUserName(s string) *ClassificationFilesUpdate {
	cfu.mutation.SetUserName(s)
	return cfu
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableUserName(s *string) *ClassificationFilesUpdate {
	if s != nil {
		cfu.SetUserName(*s)
	}
	return cfu
}

// SetDeptIds sets the "dept_ids" field.
func (cfu *ClassificationFilesUpdate) SetDeptIds(pq *pq.Int64Array) *ClassificationFilesUpdate {
	cfu.mutation.SetDeptIds(pq)
	return cfu
}

// SetDeptName sets the "dept_name" field.
func (cfu *ClassificationFilesUpdate) SetDeptName(s string) *ClassificationFilesUpdate {
	cfu.mutation.SetDeptName(s)
	return cfu
}

// SetNillableDeptName sets the "dept_name" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableDeptName(s *string) *ClassificationFilesUpdate {
	if s != nil {
		cfu.SetDeptName(*s)
	}
	return cfu
}

// SetPath sets the "path" field.
func (cfu *ClassificationFilesUpdate) SetPath(s string) *ClassificationFilesUpdate {
	cfu.mutation.SetPath(s)
	return cfu
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillablePath(s *string) *ClassificationFilesUpdate {
	if s != nil {
		cfu.SetPath(*s)
	}
	return cfu
}

// SetSecurityLevel sets the "security_level" field.
func (cfu *ClassificationFilesUpdate) SetSecurityLevel(i int32) *ClassificationFilesUpdate {
	cfu.mutation.ResetSecurityLevel()
	cfu.mutation.SetSecurityLevel(i)
	return cfu
}

// SetNillableSecurityLevel sets the "security_level" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableSecurityLevel(i *int32) *ClassificationFilesUpdate {
	if i != nil {
		cfu.SetSecurityLevel(*i)
	}
	return cfu
}

// AddSecurityLevel adds i to the "security_level" field.
func (cfu *ClassificationFilesUpdate) AddSecurityLevel(i int32) *ClassificationFilesUpdate {
	cfu.mutation.AddSecurityLevel(i)
	return cfu
}

// SetNodeLevel sets the "node_level" field.
func (cfu *ClassificationFilesUpdate) SetNodeLevel(i int32) *ClassificationFilesUpdate {
	cfu.mutation.ResetNodeLevel()
	cfu.mutation.SetNodeLevel(i)
	return cfu
}

// SetNillableNodeLevel sets the "node_level" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableNodeLevel(i *int32) *ClassificationFilesUpdate {
	if i != nil {
		cfu.SetNodeLevel(*i)
	}
	return cfu
}

// AddNodeLevel adds i to the "node_level" field.
func (cfu *ClassificationFilesUpdate) AddNodeLevel(i int32) *ClassificationFilesUpdate {
	cfu.mutation.AddNodeLevel(i)
	return cfu
}

// SetCheckStatus sets the "check_status" field.
func (cfu *ClassificationFilesUpdate) SetCheckStatus(i int32) *ClassificationFilesUpdate {
	cfu.mutation.ResetCheckStatus()
	cfu.mutation.SetCheckStatus(i)
	return cfu
}

// SetNillableCheckStatus sets the "check_status" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableCheckStatus(i *int32) *ClassificationFilesUpdate {
	if i != nil {
		cfu.SetCheckStatus(*i)
	}
	return cfu
}

// AddCheckStatus adds i to the "check_status" field.
func (cfu *ClassificationFilesUpdate) AddCheckStatus(i int32) *ClassificationFilesUpdate {
	cfu.mutation.AddCheckStatus(i)
	return cfu
}

// SetTreeType sets the "tree_type" field.
func (cfu *ClassificationFilesUpdate) SetTreeType(i int32) *ClassificationFilesUpdate {
	cfu.mutation.ResetTreeType()
	cfu.mutation.SetTreeType(i)
	return cfu
}

// SetNillableTreeType sets the "tree_type" field if the given value is not nil.
func (cfu *ClassificationFilesUpdate) SetNillableTreeType(i *int32) *ClassificationFilesUpdate {
	if i != nil {
		cfu.SetTreeType(*i)
	}
	return cfu
}

// AddTreeType adds i to the "tree_type" field.
func (cfu *ClassificationFilesUpdate) AddTreeType(i int32) *ClassificationFilesUpdate {
	cfu.mutation.AddTreeType(i)
	return cfu
}

// Mutation returns the ClassificationFilesMutation object of the builder.
func (cfu *ClassificationFilesUpdate) Mutation() *ClassificationFilesMutation {
	return cfu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cfu *ClassificationFilesUpdate) Save(ctx context.Context) (int, error) {
	return withHooks(ctx, cfu.sqlSave, cfu.mutation, cfu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cfu *ClassificationFilesUpdate) SaveX(ctx context.Context) int {
	affected, err := cfu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cfu *ClassificationFilesUpdate) Exec(ctx context.Context) error {
	_, err := cfu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cfu *ClassificationFilesUpdate) ExecX(ctx context.Context) {
	if err := cfu.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (cfu *ClassificationFilesUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ClassificationFilesUpdate {
	cfu.modifiers = append(cfu.modifiers, modifiers...)
	return cfu
}

func (cfu *ClassificationFilesUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(classificationfiles.Table, classificationfiles.Columns, sqlgraph.NewFieldSpec(classificationfiles.FieldID, field.TypeInt64))
	if ps := cfu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cfu.mutation.Name(); ok {
		_spec.SetField(classificationfiles.FieldName, field.TypeString, value)
	}
	if value, ok := cfu.mutation.FileRelationID(); ok {
		_spec.SetField(classificationfiles.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := cfu.mutation.AddedFileRelationID(); ok {
		_spec.AddField(classificationfiles.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := cfu.mutation.PreEntityTag(); ok {
		_spec.SetField(classificationfiles.FieldPreEntityTag, field.TypeString, value)
	}
	if value, ok := cfu.mutation.EntityTag(); ok {
		_spec.SetField(classificationfiles.FieldEntityTag, field.TypeString, value)
	}
	if value, ok := cfu.mutation.Filename(); ok {
		_spec.SetField(classificationfiles.FieldFilename, field.TypeString, value)
	}
	if value, ok := cfu.mutation.MimeType(); ok {
		_spec.SetField(classificationfiles.FieldMimeType, field.TypeString, value)
	}
	if value, ok := cfu.mutation.UserID(); ok {
		_spec.SetField(classificationfiles.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := cfu.mutation.AddedUserID(); ok {
		_spec.AddField(classificationfiles.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := cfu.mutation.UserName(); ok {
		_spec.SetField(classificationfiles.FieldUserName, field.TypeString, value)
	}
	if value, ok := cfu.mutation.DeptIds(); ok {
		_spec.SetField(classificationfiles.FieldDeptIds, field.TypeOther, value)
	}
	if value, ok := cfu.mutation.DeptName(); ok {
		_spec.SetField(classificationfiles.FieldDeptName, field.TypeString, value)
	}
	if value, ok := cfu.mutation.Path(); ok {
		_spec.SetField(classificationfiles.FieldPath, field.TypeString, value)
	}
	if value, ok := cfu.mutation.SecurityLevel(); ok {
		_spec.SetField(classificationfiles.FieldSecurityLevel, field.TypeInt32, value)
	}
	if value, ok := cfu.mutation.AddedSecurityLevel(); ok {
		_spec.AddField(classificationfiles.FieldSecurityLevel, field.TypeInt32, value)
	}
	if value, ok := cfu.mutation.NodeLevel(); ok {
		_spec.SetField(classificationfiles.FieldNodeLevel, field.TypeInt32, value)
	}
	if value, ok := cfu.mutation.AddedNodeLevel(); ok {
		_spec.AddField(classificationfiles.FieldNodeLevel, field.TypeInt32, value)
	}
	if value, ok := cfu.mutation.CheckStatus(); ok {
		_spec.SetField(classificationfiles.FieldCheckStatus, field.TypeInt32, value)
	}
	if value, ok := cfu.mutation.AddedCheckStatus(); ok {
		_spec.AddField(classificationfiles.FieldCheckStatus, field.TypeInt32, value)
	}
	if value, ok := cfu.mutation.TreeType(); ok {
		_spec.SetField(classificationfiles.FieldTreeType, field.TypeInt32, value)
	}
	if value, ok := cfu.mutation.AddedTreeType(); ok {
		_spec.AddField(classificationfiles.FieldTreeType, field.TypeInt32, value)
	}
	_spec.AddModifiers(cfu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, cfu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{classificationfiles.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cfu.mutation.done = true
	return n, nil
}

// ClassificationFilesUpdateOne is the builder for updating a single ClassificationFiles entity.
type ClassificationFilesUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *ClassificationFilesMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetName sets the "name" field.
func (cfuo *ClassificationFilesUpdateOne) SetName(s string) *ClassificationFilesUpdateOne {
	cfuo.mutation.SetName(s)
	return cfuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableName(s *string) *ClassificationFilesUpdateOne {
	if s != nil {
		cfuo.SetName(*s)
	}
	return cfuo
}

// SetFileRelationID sets the "file_relation_id" field.
func (cfuo *ClassificationFilesUpdateOne) SetFileRelationID(i int64) *ClassificationFilesUpdateOne {
	cfuo.mutation.ResetFileRelationID()
	cfuo.mutation.SetFileRelationID(i)
	return cfuo
}

// SetNillableFileRelationID sets the "file_relation_id" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableFileRelationID(i *int64) *ClassificationFilesUpdateOne {
	if i != nil {
		cfuo.SetFileRelationID(*i)
	}
	return cfuo
}

// AddFileRelationID adds i to the "file_relation_id" field.
func (cfuo *ClassificationFilesUpdateOne) AddFileRelationID(i int64) *ClassificationFilesUpdateOne {
	cfuo.mutation.AddFileRelationID(i)
	return cfuo
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (cfuo *ClassificationFilesUpdateOne) SetPreEntityTag(s string) *ClassificationFilesUpdateOne {
	cfuo.mutation.SetPreEntityTag(s)
	return cfuo
}

// SetNillablePreEntityTag sets the "pre_entity_tag" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillablePreEntityTag(s *string) *ClassificationFilesUpdateOne {
	if s != nil {
		cfuo.SetPreEntityTag(*s)
	}
	return cfuo
}

// SetEntityTag sets the "entity_tag" field.
func (cfuo *ClassificationFilesUpdateOne) SetEntityTag(s string) *ClassificationFilesUpdateOne {
	cfuo.mutation.SetEntityTag(s)
	return cfuo
}

// SetNillableEntityTag sets the "entity_tag" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableEntityTag(s *string) *ClassificationFilesUpdateOne {
	if s != nil {
		cfuo.SetEntityTag(*s)
	}
	return cfuo
}

// SetFilename sets the "filename" field.
func (cfuo *ClassificationFilesUpdateOne) SetFilename(s string) *ClassificationFilesUpdateOne {
	cfuo.mutation.SetFilename(s)
	return cfuo
}

// SetNillableFilename sets the "filename" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableFilename(s *string) *ClassificationFilesUpdateOne {
	if s != nil {
		cfuo.SetFilename(*s)
	}
	return cfuo
}

// SetMimeType sets the "mime_type" field.
func (cfuo *ClassificationFilesUpdateOne) SetMimeType(s string) *ClassificationFilesUpdateOne {
	cfuo.mutation.SetMimeType(s)
	return cfuo
}

// SetNillableMimeType sets the "mime_type" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableMimeType(s *string) *ClassificationFilesUpdateOne {
	if s != nil {
		cfuo.SetMimeType(*s)
	}
	return cfuo
}

// SetUserID sets the "user_id" field.
func (cfuo *ClassificationFilesUpdateOne) SetUserID(i int64) *ClassificationFilesUpdateOne {
	cfuo.mutation.ResetUserID()
	cfuo.mutation.SetUserID(i)
	return cfuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableUserID(i *int64) *ClassificationFilesUpdateOne {
	if i != nil {
		cfuo.SetUserID(*i)
	}
	return cfuo
}

// AddUserID adds i to the "user_id" field.
func (cfuo *ClassificationFilesUpdateOne) AddUserID(i int64) *ClassificationFilesUpdateOne {
	cfuo.mutation.AddUserID(i)
	return cfuo
}

// SetUserName sets the "user_name" field.
func (cfuo *ClassificationFilesUpdateOne) SetUserName(s string) *ClassificationFilesUpdateOne {
	cfuo.mutation.SetUserName(s)
	return cfuo
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableUserName(s *string) *ClassificationFilesUpdateOne {
	if s != nil {
		cfuo.SetUserName(*s)
	}
	return cfuo
}

// SetDeptIds sets the "dept_ids" field.
func (cfuo *ClassificationFilesUpdateOne) SetDeptIds(pq *pq.Int64Array) *ClassificationFilesUpdateOne {
	cfuo.mutation.SetDeptIds(pq)
	return cfuo
}

// SetDeptName sets the "dept_name" field.
func (cfuo *ClassificationFilesUpdateOne) SetDeptName(s string) *ClassificationFilesUpdateOne {
	cfuo.mutation.SetDeptName(s)
	return cfuo
}

// SetNillableDeptName sets the "dept_name" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableDeptName(s *string) *ClassificationFilesUpdateOne {
	if s != nil {
		cfuo.SetDeptName(*s)
	}
	return cfuo
}

// SetPath sets the "path" field.
func (cfuo *ClassificationFilesUpdateOne) SetPath(s string) *ClassificationFilesUpdateOne {
	cfuo.mutation.SetPath(s)
	return cfuo
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillablePath(s *string) *ClassificationFilesUpdateOne {
	if s != nil {
		cfuo.SetPath(*s)
	}
	return cfuo
}

// SetSecurityLevel sets the "security_level" field.
func (cfuo *ClassificationFilesUpdateOne) SetSecurityLevel(i int32) *ClassificationFilesUpdateOne {
	cfuo.mutation.ResetSecurityLevel()
	cfuo.mutation.SetSecurityLevel(i)
	return cfuo
}

// SetNillableSecurityLevel sets the "security_level" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableSecurityLevel(i *int32) *ClassificationFilesUpdateOne {
	if i != nil {
		cfuo.SetSecurityLevel(*i)
	}
	return cfuo
}

// AddSecurityLevel adds i to the "security_level" field.
func (cfuo *ClassificationFilesUpdateOne) AddSecurityLevel(i int32) *ClassificationFilesUpdateOne {
	cfuo.mutation.AddSecurityLevel(i)
	return cfuo
}

// SetNodeLevel sets the "node_level" field.
func (cfuo *ClassificationFilesUpdateOne) SetNodeLevel(i int32) *ClassificationFilesUpdateOne {
	cfuo.mutation.ResetNodeLevel()
	cfuo.mutation.SetNodeLevel(i)
	return cfuo
}

// SetNillableNodeLevel sets the "node_level" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableNodeLevel(i *int32) *ClassificationFilesUpdateOne {
	if i != nil {
		cfuo.SetNodeLevel(*i)
	}
	return cfuo
}

// AddNodeLevel adds i to the "node_level" field.
func (cfuo *ClassificationFilesUpdateOne) AddNodeLevel(i int32) *ClassificationFilesUpdateOne {
	cfuo.mutation.AddNodeLevel(i)
	return cfuo
}

// SetCheckStatus sets the "check_status" field.
func (cfuo *ClassificationFilesUpdateOne) SetCheckStatus(i int32) *ClassificationFilesUpdateOne {
	cfuo.mutation.ResetCheckStatus()
	cfuo.mutation.SetCheckStatus(i)
	return cfuo
}

// SetNillableCheckStatus sets the "check_status" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableCheckStatus(i *int32) *ClassificationFilesUpdateOne {
	if i != nil {
		cfuo.SetCheckStatus(*i)
	}
	return cfuo
}

// AddCheckStatus adds i to the "check_status" field.
func (cfuo *ClassificationFilesUpdateOne) AddCheckStatus(i int32) *ClassificationFilesUpdateOne {
	cfuo.mutation.AddCheckStatus(i)
	return cfuo
}

// SetTreeType sets the "tree_type" field.
func (cfuo *ClassificationFilesUpdateOne) SetTreeType(i int32) *ClassificationFilesUpdateOne {
	cfuo.mutation.ResetTreeType()
	cfuo.mutation.SetTreeType(i)
	return cfuo
}

// SetNillableTreeType sets the "tree_type" field if the given value is not nil.
func (cfuo *ClassificationFilesUpdateOne) SetNillableTreeType(i *int32) *ClassificationFilesUpdateOne {
	if i != nil {
		cfuo.SetTreeType(*i)
	}
	return cfuo
}

// AddTreeType adds i to the "tree_type" field.
func (cfuo *ClassificationFilesUpdateOne) AddTreeType(i int32) *ClassificationFilesUpdateOne {
	cfuo.mutation.AddTreeType(i)
	return cfuo
}

// Mutation returns the ClassificationFilesMutation object of the builder.
func (cfuo *ClassificationFilesUpdateOne) Mutation() *ClassificationFilesMutation {
	return cfuo.mutation
}

// Where appends a list predicates to the ClassificationFilesUpdate builder.
func (cfuo *ClassificationFilesUpdateOne) Where(ps ...predicate.ClassificationFiles) *ClassificationFilesUpdateOne {
	cfuo.mutation.Where(ps...)
	return cfuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (cfuo *ClassificationFilesUpdateOne) Select(field string, fields ...string) *ClassificationFilesUpdateOne {
	cfuo.fields = append([]string{field}, fields...)
	return cfuo
}

// Save executes the query and returns the updated ClassificationFiles entity.
func (cfuo *ClassificationFilesUpdateOne) Save(ctx context.Context) (*ClassificationFiles, error) {
	return withHooks(ctx, cfuo.sqlSave, cfuo.mutation, cfuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cfuo *ClassificationFilesUpdateOne) SaveX(ctx context.Context) *ClassificationFiles {
	node, err := cfuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (cfuo *ClassificationFilesUpdateOne) Exec(ctx context.Context) error {
	_, err := cfuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cfuo *ClassificationFilesUpdateOne) ExecX(ctx context.Context) {
	if err := cfuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (cfuo *ClassificationFilesUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ClassificationFilesUpdateOne {
	cfuo.modifiers = append(cfuo.modifiers, modifiers...)
	return cfuo
}

func (cfuo *ClassificationFilesUpdateOne) sqlSave(ctx context.Context) (_node *ClassificationFiles, err error) {
	_spec := sqlgraph.NewUpdateSpec(classificationfiles.Table, classificationfiles.Columns, sqlgraph.NewFieldSpec(classificationfiles.FieldID, field.TypeInt64))
	id, ok := cfuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "ClassificationFiles.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := cfuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, classificationfiles.FieldID)
		for _, f := range fields {
			if !classificationfiles.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != classificationfiles.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := cfuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cfuo.mutation.Name(); ok {
		_spec.SetField(classificationfiles.FieldName, field.TypeString, value)
	}
	if value, ok := cfuo.mutation.FileRelationID(); ok {
		_spec.SetField(classificationfiles.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := cfuo.mutation.AddedFileRelationID(); ok {
		_spec.AddField(classificationfiles.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := cfuo.mutation.PreEntityTag(); ok {
		_spec.SetField(classificationfiles.FieldPreEntityTag, field.TypeString, value)
	}
	if value, ok := cfuo.mutation.EntityTag(); ok {
		_spec.SetField(classificationfiles.FieldEntityTag, field.TypeString, value)
	}
	if value, ok := cfuo.mutation.Filename(); ok {
		_spec.SetField(classificationfiles.FieldFilename, field.TypeString, value)
	}
	if value, ok := cfuo.mutation.MimeType(); ok {
		_spec.SetField(classificationfiles.FieldMimeType, field.TypeString, value)
	}
	if value, ok := cfuo.mutation.UserID(); ok {
		_spec.SetField(classificationfiles.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := cfuo.mutation.AddedUserID(); ok {
		_spec.AddField(classificationfiles.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := cfuo.mutation.UserName(); ok {
		_spec.SetField(classificationfiles.FieldUserName, field.TypeString, value)
	}
	if value, ok := cfuo.mutation.DeptIds(); ok {
		_spec.SetField(classificationfiles.FieldDeptIds, field.TypeOther, value)
	}
	if value, ok := cfuo.mutation.DeptName(); ok {
		_spec.SetField(classificationfiles.FieldDeptName, field.TypeString, value)
	}
	if value, ok := cfuo.mutation.Path(); ok {
		_spec.SetField(classificationfiles.FieldPath, field.TypeString, value)
	}
	if value, ok := cfuo.mutation.SecurityLevel(); ok {
		_spec.SetField(classificationfiles.FieldSecurityLevel, field.TypeInt32, value)
	}
	if value, ok := cfuo.mutation.AddedSecurityLevel(); ok {
		_spec.AddField(classificationfiles.FieldSecurityLevel, field.TypeInt32, value)
	}
	if value, ok := cfuo.mutation.NodeLevel(); ok {
		_spec.SetField(classificationfiles.FieldNodeLevel, field.TypeInt32, value)
	}
	if value, ok := cfuo.mutation.AddedNodeLevel(); ok {
		_spec.AddField(classificationfiles.FieldNodeLevel, field.TypeInt32, value)
	}
	if value, ok := cfuo.mutation.CheckStatus(); ok {
		_spec.SetField(classificationfiles.FieldCheckStatus, field.TypeInt32, value)
	}
	if value, ok := cfuo.mutation.AddedCheckStatus(); ok {
		_spec.AddField(classificationfiles.FieldCheckStatus, field.TypeInt32, value)
	}
	if value, ok := cfuo.mutation.TreeType(); ok {
		_spec.SetField(classificationfiles.FieldTreeType, field.TypeInt32, value)
	}
	if value, ok := cfuo.mutation.AddedTreeType(); ok {
		_spec.AddField(classificationfiles.FieldTreeType, field.TypeInt32, value)
	}
	_spec.AddModifiers(cfuo.modifiers...)
	_node = &ClassificationFiles{config: cfuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, cfuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{classificationfiles.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	cfuo.mutation.done = true
	return _node, nil
}

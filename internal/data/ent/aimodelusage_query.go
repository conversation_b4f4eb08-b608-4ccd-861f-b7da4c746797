// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiModelUsageQuery is the builder for querying AiModelUsage entities.
type AiModelUsageQuery struct {
	config
	ctx        *QueryContext
	order      []aimodelusage.OrderOption
	inters     []Interceptor
	predicates []predicate.AiModelUsage
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AiModelUsageQuery builder.
func (amuq *AiModelUsageQuery) Where(ps ...predicate.AiModelUsage) *AiModelUsageQuery {
	amuq.predicates = append(amuq.predicates, ps...)
	return amuq
}

// Limit the number of records to be returned by this query.
func (amuq *AiModelUsageQuery) Limit(limit int) *AiModelUsageQuery {
	amuq.ctx.Limit = &limit
	return amuq
}

// Offset to start from.
func (amuq *AiModelUsageQuery) Offset(offset int) *AiModelUsageQuery {
	amuq.ctx.Offset = &offset
	return amuq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (amuq *AiModelUsageQuery) Unique(unique bool) *AiModelUsageQuery {
	amuq.ctx.Unique = &unique
	return amuq
}

// Order specifies how the records should be ordered.
func (amuq *AiModelUsageQuery) Order(o ...aimodelusage.OrderOption) *AiModelUsageQuery {
	amuq.order = append(amuq.order, o...)
	return amuq
}

// First returns the first AiModelUsage entity from the query.
// Returns a *NotFoundError when no AiModelUsage was found.
func (amuq *AiModelUsageQuery) First(ctx context.Context) (*AiModelUsage, error) {
	nodes, err := amuq.Limit(1).All(setContextOp(ctx, amuq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{aimodelusage.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (amuq *AiModelUsageQuery) FirstX(ctx context.Context) *AiModelUsage {
	node, err := amuq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AiModelUsage ID from the query.
// Returns a *NotFoundError when no AiModelUsage ID was found.
func (amuq *AiModelUsageQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = amuq.Limit(1).IDs(setContextOp(ctx, amuq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{aimodelusage.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (amuq *AiModelUsageQuery) FirstIDX(ctx context.Context) int64 {
	id, err := amuq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AiModelUsage entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AiModelUsage entity is found.
// Returns a *NotFoundError when no AiModelUsage entities are found.
func (amuq *AiModelUsageQuery) Only(ctx context.Context) (*AiModelUsage, error) {
	nodes, err := amuq.Limit(2).All(setContextOp(ctx, amuq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{aimodelusage.Label}
	default:
		return nil, &NotSingularError{aimodelusage.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (amuq *AiModelUsageQuery) OnlyX(ctx context.Context) *AiModelUsage {
	node, err := amuq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AiModelUsage ID in the query.
// Returns a *NotSingularError when more than one AiModelUsage ID is found.
// Returns a *NotFoundError when no entities are found.
func (amuq *AiModelUsageQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = amuq.Limit(2).IDs(setContextOp(ctx, amuq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{aimodelusage.Label}
	default:
		err = &NotSingularError{aimodelusage.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (amuq *AiModelUsageQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := amuq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AiModelUsages.
func (amuq *AiModelUsageQuery) All(ctx context.Context) ([]*AiModelUsage, error) {
	ctx = setContextOp(ctx, amuq.ctx, "All")
	if err := amuq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AiModelUsage, *AiModelUsageQuery]()
	return withInterceptors[[]*AiModelUsage](ctx, amuq, qr, amuq.inters)
}

// AllX is like All, but panics if an error occurs.
func (amuq *AiModelUsageQuery) AllX(ctx context.Context) []*AiModelUsage {
	nodes, err := amuq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AiModelUsage IDs.
func (amuq *AiModelUsageQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if amuq.ctx.Unique == nil && amuq.path != nil {
		amuq.Unique(true)
	}
	ctx = setContextOp(ctx, amuq.ctx, "IDs")
	if err = amuq.Select(aimodelusage.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (amuq *AiModelUsageQuery) IDsX(ctx context.Context) []int64 {
	ids, err := amuq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (amuq *AiModelUsageQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, amuq.ctx, "Count")
	if err := amuq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, amuq, querierCount[*AiModelUsageQuery](), amuq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (amuq *AiModelUsageQuery) CountX(ctx context.Context) int {
	count, err := amuq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (amuq *AiModelUsageQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, amuq.ctx, "Exist")
	switch _, err := amuq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (amuq *AiModelUsageQuery) ExistX(ctx context.Context) bool {
	exist, err := amuq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AiModelUsageQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (amuq *AiModelUsageQuery) Clone() *AiModelUsageQuery {
	if amuq == nil {
		return nil
	}
	return &AiModelUsageQuery{
		config:     amuq.config,
		ctx:        amuq.ctx.Clone(),
		order:      append([]aimodelusage.OrderOption{}, amuq.order...),
		inters:     append([]Interceptor{}, amuq.inters...),
		predicates: append([]predicate.AiModelUsage{}, amuq.predicates...),
		// clone intermediate query.
		sql:  amuq.sql.Clone(),
		path: amuq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AiModelUsage.Query().
//		GroupBy(aimodelusage.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (amuq *AiModelUsageQuery) GroupBy(field string, fields ...string) *AiModelUsageGroupBy {
	amuq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AiModelUsageGroupBy{build: amuq}
	grbuild.flds = &amuq.ctx.Fields
	grbuild.label = aimodelusage.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.AiModelUsage.Query().
//		Select(aimodelusage.FieldCreatedAt).
//		Scan(ctx, &v)
func (amuq *AiModelUsageQuery) Select(fields ...string) *AiModelUsageSelect {
	amuq.ctx.Fields = append(amuq.ctx.Fields, fields...)
	sbuild := &AiModelUsageSelect{AiModelUsageQuery: amuq}
	sbuild.label = aimodelusage.Label
	sbuild.flds, sbuild.scan = &amuq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AiModelUsageSelect configured with the given aggregations.
func (amuq *AiModelUsageQuery) Aggregate(fns ...AggregateFunc) *AiModelUsageSelect {
	return amuq.Select().Aggregate(fns...)
}

func (amuq *AiModelUsageQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range amuq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, amuq); err != nil {
				return err
			}
		}
	}
	for _, f := range amuq.ctx.Fields {
		if !aimodelusage.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if amuq.path != nil {
		prev, err := amuq.path(ctx)
		if err != nil {
			return err
		}
		amuq.sql = prev
	}
	return nil
}

func (amuq *AiModelUsageQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AiModelUsage, error) {
	var (
		nodes = []*AiModelUsage{}
		_spec = amuq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AiModelUsage).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AiModelUsage{config: amuq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(amuq.modifiers) > 0 {
		_spec.Modifiers = amuq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, amuq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (amuq *AiModelUsageQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := amuq.querySpec()
	if len(amuq.modifiers) > 0 {
		_spec.Modifiers = amuq.modifiers
	}
	_spec.Node.Columns = amuq.ctx.Fields
	if len(amuq.ctx.Fields) > 0 {
		_spec.Unique = amuq.ctx.Unique != nil && *amuq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, amuq.driver, _spec)
}

func (amuq *AiModelUsageQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(aimodelusage.Table, aimodelusage.Columns, sqlgraph.NewFieldSpec(aimodelusage.FieldID, field.TypeInt64))
	_spec.From = amuq.sql
	if unique := amuq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if amuq.path != nil {
		_spec.Unique = true
	}
	if fields := amuq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aimodelusage.FieldID)
		for i := range fields {
			if fields[i] != aimodelusage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := amuq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := amuq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := amuq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := amuq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (amuq *AiModelUsageQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(amuq.driver.Dialect())
	t1 := builder.Table(aimodelusage.Table)
	columns := amuq.ctx.Fields
	if len(columns) == 0 {
		columns = aimodelusage.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if amuq.sql != nil {
		selector = amuq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if amuq.ctx.Unique != nil && *amuq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range amuq.modifiers {
		m(selector)
	}
	for _, p := range amuq.predicates {
		p(selector)
	}
	for _, p := range amuq.order {
		p(selector)
	}
	if offset := amuq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := amuq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (amuq *AiModelUsageQuery) ForUpdate(opts ...sql.LockOption) *AiModelUsageQuery {
	if amuq.driver.Dialect() == dialect.Postgres {
		amuq.Unique(false)
	}
	amuq.modifiers = append(amuq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return amuq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (amuq *AiModelUsageQuery) ForShare(opts ...sql.LockOption) *AiModelUsageQuery {
	if amuq.driver.Dialect() == dialect.Postgres {
		amuq.Unique(false)
	}
	amuq.modifiers = append(amuq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return amuq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (amuq *AiModelUsageQuery) Modify(modifiers ...func(s *sql.Selector)) *AiModelUsageSelect {
	amuq.modifiers = append(amuq.modifiers, modifiers...)
	return amuq.Select()
}

// AiModelUsageGroupBy is the group-by builder for AiModelUsage entities.
type AiModelUsageGroupBy struct {
	selector
	build *AiModelUsageQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (amugb *AiModelUsageGroupBy) Aggregate(fns ...AggregateFunc) *AiModelUsageGroupBy {
	amugb.fns = append(amugb.fns, fns...)
	return amugb
}

// Scan applies the selector query and scans the result into the given value.
func (amugb *AiModelUsageGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, amugb.build.ctx, "GroupBy")
	if err := amugb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiModelUsageQuery, *AiModelUsageGroupBy](ctx, amugb.build, amugb, amugb.build.inters, v)
}

func (amugb *AiModelUsageGroupBy) sqlScan(ctx context.Context, root *AiModelUsageQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(amugb.fns))
	for _, fn := range amugb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*amugb.flds)+len(amugb.fns))
		for _, f := range *amugb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*amugb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := amugb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AiModelUsageSelect is the builder for selecting fields of AiModelUsage entities.
type AiModelUsageSelect struct {
	*AiModelUsageQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (amus *AiModelUsageSelect) Aggregate(fns ...AggregateFunc) *AiModelUsageSelect {
	amus.fns = append(amus.fns, fns...)
	return amus
}

// Scan applies the selector query and scans the result into the given value.
func (amus *AiModelUsageSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, amus.ctx, "Select")
	if err := amus.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiModelUsageQuery, *AiModelUsageSelect](ctx, amus.AiModelUsageQuery, amus, amus.inters, v)
}

func (amus *AiModelUsageSelect) sqlScan(ctx context.Context, root *AiModelUsageQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(amus.fns))
	for _, fn := range amus.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*amus.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := amus.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (amus *AiModelUsageSelect) Modify(modifiers ...func(s *sql.Selector)) *AiModelUsageSelect {
	amus.modifiers = append(amus.modifiers, modifiers...)
	return amus
}

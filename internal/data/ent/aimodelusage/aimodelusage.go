// Code generated by ent, DO NOT EDIT.

package aimodelusage

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the aimodelusage type in the database.
	Label = "ai_model_usage"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldModelDetailID holds the string denoting the model_detail_id field in the database.
	FieldModelDetailID = "model_detail_id"
	// FieldModelName holds the string denoting the model_name field in the database.
	FieldModelName = "model_name"
	// FieldModelGatewayName holds the string denoting the model_gateway_name field in the database.
	FieldModelGatewayName = "model_gateway_name"
	// FieldAgentID holds the string denoting the agent_id field in the database.
	FieldAgentID = "agent_id"
	// FieldAgentName holds the string denoting the agent_name field in the database.
	FieldAgentName = "agent_name"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldUserName holds the string denoting the user_name field in the database.
	FieldUserName = "user_name"
	// FieldQuestion holds the string denoting the question field in the database.
	FieldQuestion = "question"
	// FieldAnswer holds the string denoting the answer field in the database.
	FieldAnswer = "answer"
	// FieldPromptTokens holds the string denoting the prompt_tokens field in the database.
	FieldPromptTokens = "prompt_tokens"
	// FieldCompletionTokens holds the string denoting the completion_tokens field in the database.
	FieldCompletionTokens = "completion_tokens"
	// FieldRequestStatus holds the string denoting the request_status field in the database.
	FieldRequestStatus = "request_status"
	// FieldErrorCode holds the string denoting the error_code field in the database.
	FieldErrorCode = "error_code"
	// Table holds the table name of the aimodelusage in the database.
	Table = "ai_model_usage"
)

// Columns holds all SQL columns for aimodelusage fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldModelDetailID,
	FieldModelName,
	FieldModelGatewayName,
	FieldAgentID,
	FieldAgentName,
	FieldUserID,
	FieldUserName,
	FieldQuestion,
	FieldAnswer,
	FieldPromptTokens,
	FieldCompletionTokens,
	FieldRequestStatus,
	FieldErrorCode,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultDeletedAt holds the default value on creation for the "deleted_at" field.
	DefaultDeletedAt time.Time
	// DefaultRequestStatus holds the default value on creation for the "request_status" field.
	DefaultRequestStatus int8
	// DefaultErrorCode holds the default value on creation for the "error_code" field.
	DefaultErrorCode string
)

// OrderOption defines the ordering options for the AiModelUsage queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByModelDetailID orders the results by the model_detail_id field.
func ByModelDetailID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelDetailID, opts...).ToFunc()
}

// ByModelName orders the results by the model_name field.
func ByModelName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelName, opts...).ToFunc()
}

// ByModelGatewayName orders the results by the model_gateway_name field.
func ByModelGatewayName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelGatewayName, opts...).ToFunc()
}

// ByAgentID orders the results by the agent_id field.
func ByAgentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAgentID, opts...).ToFunc()
}

// ByAgentName orders the results by the agent_name field.
func ByAgentName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAgentName, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByUserName orders the results by the user_name field.
func ByUserName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserName, opts...).ToFunc()
}

// ByQuestion orders the results by the question field.
func ByQuestion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldQuestion, opts...).ToFunc()
}

// ByAnswer orders the results by the answer field.
func ByAnswer(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAnswer, opts...).ToFunc()
}

// ByPromptTokens orders the results by the prompt_tokens field.
func ByPromptTokens(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPromptTokens, opts...).ToFunc()
}

// ByCompletionTokens orders the results by the completion_tokens field.
func ByCompletionTokens(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCompletionTokens, opts...).ToFunc()
}

// ByRequestStatus orders the results by the request_status field.
func ByRequestStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRequestStatus, opts...).ToFunc()
}

// ByErrorCode orders the results by the error_code field.
func ByErrorCode(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldErrorCode, opts...).ToFunc()
}

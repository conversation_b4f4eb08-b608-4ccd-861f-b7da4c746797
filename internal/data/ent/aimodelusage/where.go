// Code generated by ent, DO NOT EDIT.

package aimodelusage

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldDeletedAt, v))
}

// ModelDetailID applies equality check predicate on the "model_detail_id" field. It's identical to ModelDetailIDEQ.
func ModelDetailID(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldModelDetailID, v))
}

// ModelName applies equality check predicate on the "model_name" field. It's identical to ModelNameEQ.
func ModelName(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldModelName, v))
}

// ModelGatewayName applies equality check predicate on the "model_gateway_name" field. It's identical to ModelGatewayNameEQ.
func ModelGatewayName(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldModelGatewayName, v))
}

// AgentID applies equality check predicate on the "agent_id" field. It's identical to AgentIDEQ.
func AgentID(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldAgentID, v))
}

// AgentName applies equality check predicate on the "agent_name" field. It's identical to AgentNameEQ.
func AgentName(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldAgentName, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldUserID, v))
}

// UserName applies equality check predicate on the "user_name" field. It's identical to UserNameEQ.
func UserName(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldUserName, v))
}

// Question applies equality check predicate on the "question" field. It's identical to QuestionEQ.
func Question(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldQuestion, v))
}

// Answer applies equality check predicate on the "answer" field. It's identical to AnswerEQ.
func Answer(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldAnswer, v))
}

// PromptTokens applies equality check predicate on the "prompt_tokens" field. It's identical to PromptTokensEQ.
func PromptTokens(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldPromptTokens, v))
}

// CompletionTokens applies equality check predicate on the "completion_tokens" field. It's identical to CompletionTokensEQ.
func CompletionTokens(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldCompletionTokens, v))
}

// RequestStatus applies equality check predicate on the "request_status" field. It's identical to RequestStatusEQ.
func RequestStatus(v int8) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldRequestStatus, v))
}

// ErrorCode applies equality check predicate on the "error_code" field. It's identical to ErrorCodeEQ.
func ErrorCode(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldErrorCode, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotNull(FieldDeletedAt))
}

// ModelDetailIDEQ applies the EQ predicate on the "model_detail_id" field.
func ModelDetailIDEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldModelDetailID, v))
}

// ModelDetailIDNEQ applies the NEQ predicate on the "model_detail_id" field.
func ModelDetailIDNEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldModelDetailID, v))
}

// ModelDetailIDIn applies the In predicate on the "model_detail_id" field.
func ModelDetailIDIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldModelDetailID, vs...))
}

// ModelDetailIDNotIn applies the NotIn predicate on the "model_detail_id" field.
func ModelDetailIDNotIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldModelDetailID, vs...))
}

// ModelDetailIDGT applies the GT predicate on the "model_detail_id" field.
func ModelDetailIDGT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldModelDetailID, v))
}

// ModelDetailIDGTE applies the GTE predicate on the "model_detail_id" field.
func ModelDetailIDGTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldModelDetailID, v))
}

// ModelDetailIDLT applies the LT predicate on the "model_detail_id" field.
func ModelDetailIDLT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldModelDetailID, v))
}

// ModelDetailIDLTE applies the LTE predicate on the "model_detail_id" field.
func ModelDetailIDLTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldModelDetailID, v))
}

// ModelNameEQ applies the EQ predicate on the "model_name" field.
func ModelNameEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldModelName, v))
}

// ModelNameNEQ applies the NEQ predicate on the "model_name" field.
func ModelNameNEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldModelName, v))
}

// ModelNameIn applies the In predicate on the "model_name" field.
func ModelNameIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldModelName, vs...))
}

// ModelNameNotIn applies the NotIn predicate on the "model_name" field.
func ModelNameNotIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldModelName, vs...))
}

// ModelNameGT applies the GT predicate on the "model_name" field.
func ModelNameGT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldModelName, v))
}

// ModelNameGTE applies the GTE predicate on the "model_name" field.
func ModelNameGTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldModelName, v))
}

// ModelNameLT applies the LT predicate on the "model_name" field.
func ModelNameLT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldModelName, v))
}

// ModelNameLTE applies the LTE predicate on the "model_name" field.
func ModelNameLTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldModelName, v))
}

// ModelNameContains applies the Contains predicate on the "model_name" field.
func ModelNameContains(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContains(FieldModelName, v))
}

// ModelNameHasPrefix applies the HasPrefix predicate on the "model_name" field.
func ModelNameHasPrefix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasPrefix(FieldModelName, v))
}

// ModelNameHasSuffix applies the HasSuffix predicate on the "model_name" field.
func ModelNameHasSuffix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasSuffix(FieldModelName, v))
}

// ModelNameEqualFold applies the EqualFold predicate on the "model_name" field.
func ModelNameEqualFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEqualFold(FieldModelName, v))
}

// ModelNameContainsFold applies the ContainsFold predicate on the "model_name" field.
func ModelNameContainsFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContainsFold(FieldModelName, v))
}

// ModelGatewayNameEQ applies the EQ predicate on the "model_gateway_name" field.
func ModelGatewayNameEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldModelGatewayName, v))
}

// ModelGatewayNameNEQ applies the NEQ predicate on the "model_gateway_name" field.
func ModelGatewayNameNEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldModelGatewayName, v))
}

// ModelGatewayNameIn applies the In predicate on the "model_gateway_name" field.
func ModelGatewayNameIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldModelGatewayName, vs...))
}

// ModelGatewayNameNotIn applies the NotIn predicate on the "model_gateway_name" field.
func ModelGatewayNameNotIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldModelGatewayName, vs...))
}

// ModelGatewayNameGT applies the GT predicate on the "model_gateway_name" field.
func ModelGatewayNameGT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldModelGatewayName, v))
}

// ModelGatewayNameGTE applies the GTE predicate on the "model_gateway_name" field.
func ModelGatewayNameGTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldModelGatewayName, v))
}

// ModelGatewayNameLT applies the LT predicate on the "model_gateway_name" field.
func ModelGatewayNameLT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldModelGatewayName, v))
}

// ModelGatewayNameLTE applies the LTE predicate on the "model_gateway_name" field.
func ModelGatewayNameLTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldModelGatewayName, v))
}

// ModelGatewayNameContains applies the Contains predicate on the "model_gateway_name" field.
func ModelGatewayNameContains(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContains(FieldModelGatewayName, v))
}

// ModelGatewayNameHasPrefix applies the HasPrefix predicate on the "model_gateway_name" field.
func ModelGatewayNameHasPrefix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasPrefix(FieldModelGatewayName, v))
}

// ModelGatewayNameHasSuffix applies the HasSuffix predicate on the "model_gateway_name" field.
func ModelGatewayNameHasSuffix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasSuffix(FieldModelGatewayName, v))
}

// ModelGatewayNameEqualFold applies the EqualFold predicate on the "model_gateway_name" field.
func ModelGatewayNameEqualFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEqualFold(FieldModelGatewayName, v))
}

// ModelGatewayNameContainsFold applies the ContainsFold predicate on the "model_gateway_name" field.
func ModelGatewayNameContainsFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContainsFold(FieldModelGatewayName, v))
}

// AgentIDEQ applies the EQ predicate on the "agent_id" field.
func AgentIDEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldAgentID, v))
}

// AgentIDNEQ applies the NEQ predicate on the "agent_id" field.
func AgentIDNEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldAgentID, v))
}

// AgentIDIn applies the In predicate on the "agent_id" field.
func AgentIDIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldAgentID, vs...))
}

// AgentIDNotIn applies the NotIn predicate on the "agent_id" field.
func AgentIDNotIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldAgentID, vs...))
}

// AgentIDGT applies the GT predicate on the "agent_id" field.
func AgentIDGT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldAgentID, v))
}

// AgentIDGTE applies the GTE predicate on the "agent_id" field.
func AgentIDGTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldAgentID, v))
}

// AgentIDLT applies the LT predicate on the "agent_id" field.
func AgentIDLT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldAgentID, v))
}

// AgentIDLTE applies the LTE predicate on the "agent_id" field.
func AgentIDLTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldAgentID, v))
}

// AgentNameEQ applies the EQ predicate on the "agent_name" field.
func AgentNameEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldAgentName, v))
}

// AgentNameNEQ applies the NEQ predicate on the "agent_name" field.
func AgentNameNEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldAgentName, v))
}

// AgentNameIn applies the In predicate on the "agent_name" field.
func AgentNameIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldAgentName, vs...))
}

// AgentNameNotIn applies the NotIn predicate on the "agent_name" field.
func AgentNameNotIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldAgentName, vs...))
}

// AgentNameGT applies the GT predicate on the "agent_name" field.
func AgentNameGT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldAgentName, v))
}

// AgentNameGTE applies the GTE predicate on the "agent_name" field.
func AgentNameGTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldAgentName, v))
}

// AgentNameLT applies the LT predicate on the "agent_name" field.
func AgentNameLT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldAgentName, v))
}

// AgentNameLTE applies the LTE predicate on the "agent_name" field.
func AgentNameLTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldAgentName, v))
}

// AgentNameContains applies the Contains predicate on the "agent_name" field.
func AgentNameContains(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContains(FieldAgentName, v))
}

// AgentNameHasPrefix applies the HasPrefix predicate on the "agent_name" field.
func AgentNameHasPrefix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasPrefix(FieldAgentName, v))
}

// AgentNameHasSuffix applies the HasSuffix predicate on the "agent_name" field.
func AgentNameHasSuffix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasSuffix(FieldAgentName, v))
}

// AgentNameEqualFold applies the EqualFold predicate on the "agent_name" field.
func AgentNameEqualFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEqualFold(FieldAgentName, v))
}

// AgentNameContainsFold applies the ContainsFold predicate on the "agent_name" field.
func AgentNameContainsFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContainsFold(FieldAgentName, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldUserID, v))
}

// UserNameEQ applies the EQ predicate on the "user_name" field.
func UserNameEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldUserName, v))
}

// UserNameNEQ applies the NEQ predicate on the "user_name" field.
func UserNameNEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldUserName, v))
}

// UserNameIn applies the In predicate on the "user_name" field.
func UserNameIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldUserName, vs...))
}

// UserNameNotIn applies the NotIn predicate on the "user_name" field.
func UserNameNotIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldUserName, vs...))
}

// UserNameGT applies the GT predicate on the "user_name" field.
func UserNameGT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldUserName, v))
}

// UserNameGTE applies the GTE predicate on the "user_name" field.
func UserNameGTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldUserName, v))
}

// UserNameLT applies the LT predicate on the "user_name" field.
func UserNameLT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldUserName, v))
}

// UserNameLTE applies the LTE predicate on the "user_name" field.
func UserNameLTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldUserName, v))
}

// UserNameContains applies the Contains predicate on the "user_name" field.
func UserNameContains(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContains(FieldUserName, v))
}

// UserNameHasPrefix applies the HasPrefix predicate on the "user_name" field.
func UserNameHasPrefix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasPrefix(FieldUserName, v))
}

// UserNameHasSuffix applies the HasSuffix predicate on the "user_name" field.
func UserNameHasSuffix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasSuffix(FieldUserName, v))
}

// UserNameEqualFold applies the EqualFold predicate on the "user_name" field.
func UserNameEqualFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEqualFold(FieldUserName, v))
}

// UserNameContainsFold applies the ContainsFold predicate on the "user_name" field.
func UserNameContainsFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContainsFold(FieldUserName, v))
}

// QuestionEQ applies the EQ predicate on the "question" field.
func QuestionEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldQuestion, v))
}

// QuestionNEQ applies the NEQ predicate on the "question" field.
func QuestionNEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldQuestion, v))
}

// QuestionIn applies the In predicate on the "question" field.
func QuestionIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldQuestion, vs...))
}

// QuestionNotIn applies the NotIn predicate on the "question" field.
func QuestionNotIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldQuestion, vs...))
}

// QuestionGT applies the GT predicate on the "question" field.
func QuestionGT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldQuestion, v))
}

// QuestionGTE applies the GTE predicate on the "question" field.
func QuestionGTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldQuestion, v))
}

// QuestionLT applies the LT predicate on the "question" field.
func QuestionLT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldQuestion, v))
}

// QuestionLTE applies the LTE predicate on the "question" field.
func QuestionLTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldQuestion, v))
}

// QuestionContains applies the Contains predicate on the "question" field.
func QuestionContains(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContains(FieldQuestion, v))
}

// QuestionHasPrefix applies the HasPrefix predicate on the "question" field.
func QuestionHasPrefix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasPrefix(FieldQuestion, v))
}

// QuestionHasSuffix applies the HasSuffix predicate on the "question" field.
func QuestionHasSuffix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasSuffix(FieldQuestion, v))
}

// QuestionEqualFold applies the EqualFold predicate on the "question" field.
func QuestionEqualFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEqualFold(FieldQuestion, v))
}

// QuestionContainsFold applies the ContainsFold predicate on the "question" field.
func QuestionContainsFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContainsFold(FieldQuestion, v))
}

// AnswerEQ applies the EQ predicate on the "answer" field.
func AnswerEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldAnswer, v))
}

// AnswerNEQ applies the NEQ predicate on the "answer" field.
func AnswerNEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldAnswer, v))
}

// AnswerIn applies the In predicate on the "answer" field.
func AnswerIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldAnswer, vs...))
}

// AnswerNotIn applies the NotIn predicate on the "answer" field.
func AnswerNotIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldAnswer, vs...))
}

// AnswerGT applies the GT predicate on the "answer" field.
func AnswerGT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldAnswer, v))
}

// AnswerGTE applies the GTE predicate on the "answer" field.
func AnswerGTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldAnswer, v))
}

// AnswerLT applies the LT predicate on the "answer" field.
func AnswerLT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldAnswer, v))
}

// AnswerLTE applies the LTE predicate on the "answer" field.
func AnswerLTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldAnswer, v))
}

// AnswerContains applies the Contains predicate on the "answer" field.
func AnswerContains(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContains(FieldAnswer, v))
}

// AnswerHasPrefix applies the HasPrefix predicate on the "answer" field.
func AnswerHasPrefix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasPrefix(FieldAnswer, v))
}

// AnswerHasSuffix applies the HasSuffix predicate on the "answer" field.
func AnswerHasSuffix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasSuffix(FieldAnswer, v))
}

// AnswerEqualFold applies the EqualFold predicate on the "answer" field.
func AnswerEqualFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEqualFold(FieldAnswer, v))
}

// AnswerContainsFold applies the ContainsFold predicate on the "answer" field.
func AnswerContainsFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContainsFold(FieldAnswer, v))
}

// PromptTokensEQ applies the EQ predicate on the "prompt_tokens" field.
func PromptTokensEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldPromptTokens, v))
}

// PromptTokensNEQ applies the NEQ predicate on the "prompt_tokens" field.
func PromptTokensNEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldPromptTokens, v))
}

// PromptTokensIn applies the In predicate on the "prompt_tokens" field.
func PromptTokensIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldPromptTokens, vs...))
}

// PromptTokensNotIn applies the NotIn predicate on the "prompt_tokens" field.
func PromptTokensNotIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldPromptTokens, vs...))
}

// PromptTokensGT applies the GT predicate on the "prompt_tokens" field.
func PromptTokensGT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldPromptTokens, v))
}

// PromptTokensGTE applies the GTE predicate on the "prompt_tokens" field.
func PromptTokensGTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldPromptTokens, v))
}

// PromptTokensLT applies the LT predicate on the "prompt_tokens" field.
func PromptTokensLT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldPromptTokens, v))
}

// PromptTokensLTE applies the LTE predicate on the "prompt_tokens" field.
func PromptTokensLTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldPromptTokens, v))
}

// CompletionTokensEQ applies the EQ predicate on the "completion_tokens" field.
func CompletionTokensEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldCompletionTokens, v))
}

// CompletionTokensNEQ applies the NEQ predicate on the "completion_tokens" field.
func CompletionTokensNEQ(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldCompletionTokens, v))
}

// CompletionTokensIn applies the In predicate on the "completion_tokens" field.
func CompletionTokensIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldCompletionTokens, vs...))
}

// CompletionTokensNotIn applies the NotIn predicate on the "completion_tokens" field.
func CompletionTokensNotIn(vs ...int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldCompletionTokens, vs...))
}

// CompletionTokensGT applies the GT predicate on the "completion_tokens" field.
func CompletionTokensGT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldCompletionTokens, v))
}

// CompletionTokensGTE applies the GTE predicate on the "completion_tokens" field.
func CompletionTokensGTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldCompletionTokens, v))
}

// CompletionTokensLT applies the LT predicate on the "completion_tokens" field.
func CompletionTokensLT(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldCompletionTokens, v))
}

// CompletionTokensLTE applies the LTE predicate on the "completion_tokens" field.
func CompletionTokensLTE(v int64) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldCompletionTokens, v))
}

// RequestStatusEQ applies the EQ predicate on the "request_status" field.
func RequestStatusEQ(v int8) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldRequestStatus, v))
}

// RequestStatusNEQ applies the NEQ predicate on the "request_status" field.
func RequestStatusNEQ(v int8) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldRequestStatus, v))
}

// RequestStatusIn applies the In predicate on the "request_status" field.
func RequestStatusIn(vs ...int8) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldRequestStatus, vs...))
}

// RequestStatusNotIn applies the NotIn predicate on the "request_status" field.
func RequestStatusNotIn(vs ...int8) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldRequestStatus, vs...))
}

// RequestStatusGT applies the GT predicate on the "request_status" field.
func RequestStatusGT(v int8) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldRequestStatus, v))
}

// RequestStatusGTE applies the GTE predicate on the "request_status" field.
func RequestStatusGTE(v int8) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldRequestStatus, v))
}

// RequestStatusLT applies the LT predicate on the "request_status" field.
func RequestStatusLT(v int8) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldRequestStatus, v))
}

// RequestStatusLTE applies the LTE predicate on the "request_status" field.
func RequestStatusLTE(v int8) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldRequestStatus, v))
}

// ErrorCodeEQ applies the EQ predicate on the "error_code" field.
func ErrorCodeEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEQ(FieldErrorCode, v))
}

// ErrorCodeNEQ applies the NEQ predicate on the "error_code" field.
func ErrorCodeNEQ(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNEQ(FieldErrorCode, v))
}

// ErrorCodeIn applies the In predicate on the "error_code" field.
func ErrorCodeIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldIn(FieldErrorCode, vs...))
}

// ErrorCodeNotIn applies the NotIn predicate on the "error_code" field.
func ErrorCodeNotIn(vs ...string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldNotIn(FieldErrorCode, vs...))
}

// ErrorCodeGT applies the GT predicate on the "error_code" field.
func ErrorCodeGT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGT(FieldErrorCode, v))
}

// ErrorCodeGTE applies the GTE predicate on the "error_code" field.
func ErrorCodeGTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldGTE(FieldErrorCode, v))
}

// ErrorCodeLT applies the LT predicate on the "error_code" field.
func ErrorCodeLT(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLT(FieldErrorCode, v))
}

// ErrorCodeLTE applies the LTE predicate on the "error_code" field.
func ErrorCodeLTE(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldLTE(FieldErrorCode, v))
}

// ErrorCodeContains applies the Contains predicate on the "error_code" field.
func ErrorCodeContains(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContains(FieldErrorCode, v))
}

// ErrorCodeHasPrefix applies the HasPrefix predicate on the "error_code" field.
func ErrorCodeHasPrefix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasPrefix(FieldErrorCode, v))
}

// ErrorCodeHasSuffix applies the HasSuffix predicate on the "error_code" field.
func ErrorCodeHasSuffix(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldHasSuffix(FieldErrorCode, v))
}

// ErrorCodeEqualFold applies the EqualFold predicate on the "error_code" field.
func ErrorCodeEqualFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldEqualFold(FieldErrorCode, v))
}

// ErrorCodeContainsFold applies the ContainsFold predicate on the "error_code" field.
func ErrorCodeContainsFold(v string) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.FieldContainsFold(FieldErrorCode, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AiModelUsage) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AiModelUsage) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AiModelUsage) predicate.AiModelUsage {
	return predicate.AiModelUsage(sql.NotPredicates(p))
}

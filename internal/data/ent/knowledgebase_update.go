// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// KnowledgeBaseUpdate is the builder for updating KnowledgeBase entities.
type KnowledgeBaseUpdate struct {
	config
	hooks     []Hook
	mutation  *KnowledgeBaseMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the KnowledgeBaseUpdate builder.
func (kbu *KnowledgeBaseUpdate) Where(ps ...predicate.KnowledgeBase) *KnowledgeBaseUpdate {
	kbu.mutation.Where(ps...)
	return kbu
}

// SetTenantID sets the "tenant_id" field.
func (kbu *KnowledgeBaseUpdate) SetTenantID(i int64) *KnowledgeBaseUpdate {
	kbu.mutation.ResetTenantID()
	kbu.mutation.SetTenantID(i)
	return kbu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (kbu *KnowledgeBaseUpdate) SetNillableTenantID(i *int64) *KnowledgeBaseUpdate {
	if i != nil {
		kbu.SetTenantID(*i)
	}
	return kbu
}

// AddTenantID adds i to the "tenant_id" field.
func (kbu *KnowledgeBaseUpdate) AddTenantID(i int64) *KnowledgeBaseUpdate {
	kbu.mutation.AddTenantID(i)
	return kbu
}

// SetUpdatedAt sets the "updated_at" field.
func (kbu *KnowledgeBaseUpdate) SetUpdatedAt(t time.Time) *KnowledgeBaseUpdate {
	kbu.mutation.SetUpdatedAt(t)
	return kbu
}

// SetDeletedAt sets the "deleted_at" field.
func (kbu *KnowledgeBaseUpdate) SetDeletedAt(t time.Time) *KnowledgeBaseUpdate {
	kbu.mutation.SetDeletedAt(t)
	return kbu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (kbu *KnowledgeBaseUpdate) SetNillableDeletedAt(t *time.Time) *KnowledgeBaseUpdate {
	if t != nil {
		kbu.SetDeletedAt(*t)
	}
	return kbu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (kbu *KnowledgeBaseUpdate) ClearDeletedAt() *KnowledgeBaseUpdate {
	kbu.mutation.ClearDeletedAt()
	return kbu
}

// SetName sets the "name" field.
func (kbu *KnowledgeBaseUpdate) SetName(s string) *KnowledgeBaseUpdate {
	kbu.mutation.SetName(s)
	return kbu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (kbu *KnowledgeBaseUpdate) SetNillableName(s *string) *KnowledgeBaseUpdate {
	if s != nil {
		kbu.SetName(*s)
	}
	return kbu
}

// SetPublic sets the "public" field.
func (kbu *KnowledgeBaseUpdate) SetPublic(b bool) *KnowledgeBaseUpdate {
	kbu.mutation.SetPublic(b)
	return kbu
}

// SetNillablePublic sets the "public" field if the given value is not nil.
func (kbu *KnowledgeBaseUpdate) SetNillablePublic(b *bool) *KnowledgeBaseUpdate {
	if b != nil {
		kbu.SetPublic(*b)
	}
	return kbu
}

// SetDataType sets the "data_type" field.
func (kbu *KnowledgeBaseUpdate) SetDataType(i int32) *KnowledgeBaseUpdate {
	kbu.mutation.ResetDataType()
	kbu.mutation.SetDataType(i)
	return kbu
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (kbu *KnowledgeBaseUpdate) SetNillableDataType(i *int32) *KnowledgeBaseUpdate {
	if i != nil {
		kbu.SetDataType(*i)
	}
	return kbu
}

// AddDataType adds i to the "data_type" field.
func (kbu *KnowledgeBaseUpdate) AddDataType(i int32) *KnowledgeBaseUpdate {
	kbu.mutation.AddDataType(i)
	return kbu
}

// SetUserID sets the "user_id" field.
func (kbu *KnowledgeBaseUpdate) SetUserID(i int64) *KnowledgeBaseUpdate {
	kbu.mutation.ResetUserID()
	kbu.mutation.SetUserID(i)
	return kbu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (kbu *KnowledgeBaseUpdate) SetNillableUserID(i *int64) *KnowledgeBaseUpdate {
	if i != nil {
		kbu.SetUserID(*i)
	}
	return kbu
}

// AddUserID adds i to the "user_id" field.
func (kbu *KnowledgeBaseUpdate) AddUserID(i int64) *KnowledgeBaseUpdate {
	kbu.mutation.AddUserID(i)
	return kbu
}

// SetManagerUserIds sets the "manager_user_ids" field.
func (kbu *KnowledgeBaseUpdate) SetManagerUserIds(pq *pq.Int64Array) *KnowledgeBaseUpdate {
	kbu.mutation.SetManagerUserIds(pq)
	return kbu
}

// SetEditableUserIds sets the "editable_user_ids" field.
func (kbu *KnowledgeBaseUpdate) SetEditableUserIds(pq *pq.Int64Array) *KnowledgeBaseUpdate {
	kbu.mutation.SetEditableUserIds(pq)
	return kbu
}

// Mutation returns the KnowledgeBaseMutation object of the builder.
func (kbu *KnowledgeBaseUpdate) Mutation() *KnowledgeBaseMutation {
	return kbu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (kbu *KnowledgeBaseUpdate) Save(ctx context.Context) (int, error) {
	if err := kbu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, kbu.sqlSave, kbu.mutation, kbu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (kbu *KnowledgeBaseUpdate) SaveX(ctx context.Context) int {
	affected, err := kbu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (kbu *KnowledgeBaseUpdate) Exec(ctx context.Context) error {
	_, err := kbu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (kbu *KnowledgeBaseUpdate) ExecX(ctx context.Context) {
	if err := kbu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (kbu *KnowledgeBaseUpdate) defaults() error {
	if _, ok := kbu.mutation.UpdatedAt(); !ok {
		if knowledgebase.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized knowledgebase.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := knowledgebase.UpdateDefaultUpdatedAt()
		kbu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (kbu *KnowledgeBaseUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *KnowledgeBaseUpdate {
	kbu.modifiers = append(kbu.modifiers, modifiers...)
	return kbu
}

func (kbu *KnowledgeBaseUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(knowledgebase.Table, knowledgebase.Columns, sqlgraph.NewFieldSpec(knowledgebase.FieldID, field.TypeInt64))
	if ps := kbu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := kbu.mutation.TenantID(); ok {
		_spec.SetField(knowledgebase.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := kbu.mutation.AddedTenantID(); ok {
		_spec.AddField(knowledgebase.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := kbu.mutation.UpdatedAt(); ok {
		_spec.SetField(knowledgebase.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := kbu.mutation.DeletedAt(); ok {
		_spec.SetField(knowledgebase.FieldDeletedAt, field.TypeTime, value)
	}
	if kbu.mutation.DeletedAtCleared() {
		_spec.ClearField(knowledgebase.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := kbu.mutation.Name(); ok {
		_spec.SetField(knowledgebase.FieldName, field.TypeString, value)
	}
	if value, ok := kbu.mutation.Public(); ok {
		_spec.SetField(knowledgebase.FieldPublic, field.TypeBool, value)
	}
	if value, ok := kbu.mutation.DataType(); ok {
		_spec.SetField(knowledgebase.FieldDataType, field.TypeInt32, value)
	}
	if value, ok := kbu.mutation.AddedDataType(); ok {
		_spec.AddField(knowledgebase.FieldDataType, field.TypeInt32, value)
	}
	if value, ok := kbu.mutation.UserID(); ok {
		_spec.SetField(knowledgebase.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := kbu.mutation.AddedUserID(); ok {
		_spec.AddField(knowledgebase.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := kbu.mutation.ManagerUserIds(); ok {
		_spec.SetField(knowledgebase.FieldManagerUserIds, field.TypeOther, value)
	}
	if value, ok := kbu.mutation.EditableUserIds(); ok {
		_spec.SetField(knowledgebase.FieldEditableUserIds, field.TypeOther, value)
	}
	_spec.AddModifiers(kbu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, kbu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{knowledgebase.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	kbu.mutation.done = true
	return n, nil
}

// KnowledgeBaseUpdateOne is the builder for updating a single KnowledgeBase entity.
type KnowledgeBaseUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *KnowledgeBaseMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetTenantID sets the "tenant_id" field.
func (kbuo *KnowledgeBaseUpdateOne) SetTenantID(i int64) *KnowledgeBaseUpdateOne {
	kbuo.mutation.ResetTenantID()
	kbuo.mutation.SetTenantID(i)
	return kbuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (kbuo *KnowledgeBaseUpdateOne) SetNillableTenantID(i *int64) *KnowledgeBaseUpdateOne {
	if i != nil {
		kbuo.SetTenantID(*i)
	}
	return kbuo
}

// AddTenantID adds i to the "tenant_id" field.
func (kbuo *KnowledgeBaseUpdateOne) AddTenantID(i int64) *KnowledgeBaseUpdateOne {
	kbuo.mutation.AddTenantID(i)
	return kbuo
}

// SetUpdatedAt sets the "updated_at" field.
func (kbuo *KnowledgeBaseUpdateOne) SetUpdatedAt(t time.Time) *KnowledgeBaseUpdateOne {
	kbuo.mutation.SetUpdatedAt(t)
	return kbuo
}

// SetDeletedAt sets the "deleted_at" field.
func (kbuo *KnowledgeBaseUpdateOne) SetDeletedAt(t time.Time) *KnowledgeBaseUpdateOne {
	kbuo.mutation.SetDeletedAt(t)
	return kbuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (kbuo *KnowledgeBaseUpdateOne) SetNillableDeletedAt(t *time.Time) *KnowledgeBaseUpdateOne {
	if t != nil {
		kbuo.SetDeletedAt(*t)
	}
	return kbuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (kbuo *KnowledgeBaseUpdateOne) ClearDeletedAt() *KnowledgeBaseUpdateOne {
	kbuo.mutation.ClearDeletedAt()
	return kbuo
}

// SetName sets the "name" field.
func (kbuo *KnowledgeBaseUpdateOne) SetName(s string) *KnowledgeBaseUpdateOne {
	kbuo.mutation.SetName(s)
	return kbuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (kbuo *KnowledgeBaseUpdateOne) SetNillableName(s *string) *KnowledgeBaseUpdateOne {
	if s != nil {
		kbuo.SetName(*s)
	}
	return kbuo
}

// SetPublic sets the "public" field.
func (kbuo *KnowledgeBaseUpdateOne) SetPublic(b bool) *KnowledgeBaseUpdateOne {
	kbuo.mutation.SetPublic(b)
	return kbuo
}

// SetNillablePublic sets the "public" field if the given value is not nil.
func (kbuo *KnowledgeBaseUpdateOne) SetNillablePublic(b *bool) *KnowledgeBaseUpdateOne {
	if b != nil {
		kbuo.SetPublic(*b)
	}
	return kbuo
}

// SetDataType sets the "data_type" field.
func (kbuo *KnowledgeBaseUpdateOne) SetDataType(i int32) *KnowledgeBaseUpdateOne {
	kbuo.mutation.ResetDataType()
	kbuo.mutation.SetDataType(i)
	return kbuo
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (kbuo *KnowledgeBaseUpdateOne) SetNillableDataType(i *int32) *KnowledgeBaseUpdateOne {
	if i != nil {
		kbuo.SetDataType(*i)
	}
	return kbuo
}

// AddDataType adds i to the "data_type" field.
func (kbuo *KnowledgeBaseUpdateOne) AddDataType(i int32) *KnowledgeBaseUpdateOne {
	kbuo.mutation.AddDataType(i)
	return kbuo
}

// SetUserID sets the "user_id" field.
func (kbuo *KnowledgeBaseUpdateOne) SetUserID(i int64) *KnowledgeBaseUpdateOne {
	kbuo.mutation.ResetUserID()
	kbuo.mutation.SetUserID(i)
	return kbuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (kbuo *KnowledgeBaseUpdateOne) SetNillableUserID(i *int64) *KnowledgeBaseUpdateOne {
	if i != nil {
		kbuo.SetUserID(*i)
	}
	return kbuo
}

// AddUserID adds i to the "user_id" field.
func (kbuo *KnowledgeBaseUpdateOne) AddUserID(i int64) *KnowledgeBaseUpdateOne {
	kbuo.mutation.AddUserID(i)
	return kbuo
}

// SetManagerUserIds sets the "manager_user_ids" field.
func (kbuo *KnowledgeBaseUpdateOne) SetManagerUserIds(pq *pq.Int64Array) *KnowledgeBaseUpdateOne {
	kbuo.mutation.SetManagerUserIds(pq)
	return kbuo
}

// SetEditableUserIds sets the "editable_user_ids" field.
func (kbuo *KnowledgeBaseUpdateOne) SetEditableUserIds(pq *pq.Int64Array) *KnowledgeBaseUpdateOne {
	kbuo.mutation.SetEditableUserIds(pq)
	return kbuo
}

// Mutation returns the KnowledgeBaseMutation object of the builder.
func (kbuo *KnowledgeBaseUpdateOne) Mutation() *KnowledgeBaseMutation {
	return kbuo.mutation
}

// Where appends a list predicates to the KnowledgeBaseUpdate builder.
func (kbuo *KnowledgeBaseUpdateOne) Where(ps ...predicate.KnowledgeBase) *KnowledgeBaseUpdateOne {
	kbuo.mutation.Where(ps...)
	return kbuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (kbuo *KnowledgeBaseUpdateOne) Select(field string, fields ...string) *KnowledgeBaseUpdateOne {
	kbuo.fields = append([]string{field}, fields...)
	return kbuo
}

// Save executes the query and returns the updated KnowledgeBase entity.
func (kbuo *KnowledgeBaseUpdateOne) Save(ctx context.Context) (*KnowledgeBase, error) {
	if err := kbuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, kbuo.sqlSave, kbuo.mutation, kbuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (kbuo *KnowledgeBaseUpdateOne) SaveX(ctx context.Context) *KnowledgeBase {
	node, err := kbuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (kbuo *KnowledgeBaseUpdateOne) Exec(ctx context.Context) error {
	_, err := kbuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (kbuo *KnowledgeBaseUpdateOne) ExecX(ctx context.Context) {
	if err := kbuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (kbuo *KnowledgeBaseUpdateOne) defaults() error {
	if _, ok := kbuo.mutation.UpdatedAt(); !ok {
		if knowledgebase.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized knowledgebase.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := knowledgebase.UpdateDefaultUpdatedAt()
		kbuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (kbuo *KnowledgeBaseUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *KnowledgeBaseUpdateOne {
	kbuo.modifiers = append(kbuo.modifiers, modifiers...)
	return kbuo
}

func (kbuo *KnowledgeBaseUpdateOne) sqlSave(ctx context.Context) (_node *KnowledgeBase, err error) {
	_spec := sqlgraph.NewUpdateSpec(knowledgebase.Table, knowledgebase.Columns, sqlgraph.NewFieldSpec(knowledgebase.FieldID, field.TypeInt64))
	id, ok := kbuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "KnowledgeBase.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := kbuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, knowledgebase.FieldID)
		for _, f := range fields {
			if !knowledgebase.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != knowledgebase.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := kbuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := kbuo.mutation.TenantID(); ok {
		_spec.SetField(knowledgebase.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := kbuo.mutation.AddedTenantID(); ok {
		_spec.AddField(knowledgebase.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := kbuo.mutation.UpdatedAt(); ok {
		_spec.SetField(knowledgebase.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := kbuo.mutation.DeletedAt(); ok {
		_spec.SetField(knowledgebase.FieldDeletedAt, field.TypeTime, value)
	}
	if kbuo.mutation.DeletedAtCleared() {
		_spec.ClearField(knowledgebase.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := kbuo.mutation.Name(); ok {
		_spec.SetField(knowledgebase.FieldName, field.TypeString, value)
	}
	if value, ok := kbuo.mutation.Public(); ok {
		_spec.SetField(knowledgebase.FieldPublic, field.TypeBool, value)
	}
	if value, ok := kbuo.mutation.DataType(); ok {
		_spec.SetField(knowledgebase.FieldDataType, field.TypeInt32, value)
	}
	if value, ok := kbuo.mutation.AddedDataType(); ok {
		_spec.AddField(knowledgebase.FieldDataType, field.TypeInt32, value)
	}
	if value, ok := kbuo.mutation.UserID(); ok {
		_spec.SetField(knowledgebase.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := kbuo.mutation.AddedUserID(); ok {
		_spec.AddField(knowledgebase.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := kbuo.mutation.ManagerUserIds(); ok {
		_spec.SetField(knowledgebase.FieldManagerUserIds, field.TypeOther, value)
	}
	if value, ok := kbuo.mutation.EditableUserIds(); ok {
		_spec.SetField(knowledgebase.FieldEditableUserIds, field.TypeOther, value)
	}
	_spec.AddModifiers(kbuo.modifiers...)
	_node = &KnowledgeBase{config: kbuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, kbuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{knowledgebase.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	kbuo.mutation.done = true
	return _node, nil
}

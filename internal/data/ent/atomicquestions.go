// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/atomicquestions"
)

// AtomicQuestions is the model entity for the AtomicQuestions schema.
type AtomicQuestions struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// entity_tag
	EntityTag string `json:"entity_tag,omitempty"`
	// pre_entity_tag
	PreEntityTag string `json:"pre_entity_tag,omitempty"`
	// 文件关系id
	FileRelationID int64 `json:"file_relation_id,omitempty"`
	// chunk_index
	ChunkIndex int64 `json:"chunk_index,omitempty"`
	// chunk_size
	ChunkSize int64 `json:"chunk_size,omitempty"`
	// index
	Index int64 `json:"index,omitempty"`
	// 原子问题数组
	Question *pq.StringArray `json:"question,omitempty"`
	// 是否处理
	IsHandle     bool `json:"is_handle,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AtomicQuestions) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case atomicquestions.FieldQuestion:
			values[i] = new(pq.StringArray)
		case atomicquestions.FieldIsHandle:
			values[i] = new(sql.NullBool)
		case atomicquestions.FieldID, atomicquestions.FieldFileRelationID, atomicquestions.FieldChunkIndex, atomicquestions.FieldChunkSize, atomicquestions.FieldIndex:
			values[i] = new(sql.NullInt64)
		case atomicquestions.FieldEntityTag, atomicquestions.FieldPreEntityTag:
			values[i] = new(sql.NullString)
		case atomicquestions.FieldCreatedAt, atomicquestions.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AtomicQuestions fields.
func (aq *AtomicQuestions) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case atomicquestions.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			aq.ID = int64(value.Int64)
		case atomicquestions.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				aq.CreatedAt = value.Time
			}
		case atomicquestions.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				aq.UpdatedAt = value.Time
			}
		case atomicquestions.FieldEntityTag:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field entity_tag", values[i])
			} else if value.Valid {
				aq.EntityTag = value.String
			}
		case atomicquestions.FieldPreEntityTag:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field pre_entity_tag", values[i])
			} else if value.Valid {
				aq.PreEntityTag = value.String
			}
		case atomicquestions.FieldFileRelationID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field file_relation_id", values[i])
			} else if value.Valid {
				aq.FileRelationID = value.Int64
			}
		case atomicquestions.FieldChunkIndex:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field chunk_index", values[i])
			} else if value.Valid {
				aq.ChunkIndex = value.Int64
			}
		case atomicquestions.FieldChunkSize:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field chunk_size", values[i])
			} else if value.Valid {
				aq.ChunkSize = value.Int64
			}
		case atomicquestions.FieldIndex:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field index", values[i])
			} else if value.Valid {
				aq.Index = value.Int64
			}
		case atomicquestions.FieldQuestion:
			if value, ok := values[i].(*pq.StringArray); !ok {
				return fmt.Errorf("unexpected type %T for field question", values[i])
			} else if value != nil {
				aq.Question = value
			}
		case atomicquestions.FieldIsHandle:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_handle", values[i])
			} else if value.Valid {
				aq.IsHandle = value.Bool
			}
		default:
			aq.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AtomicQuestions.
// This includes values selected through modifiers, order, etc.
func (aq *AtomicQuestions) Value(name string) (ent.Value, error) {
	return aq.selectValues.Get(name)
}

// Update returns a builder for updating this AtomicQuestions.
// Note that you need to call AtomicQuestions.Unwrap() before calling this method if this AtomicQuestions
// was returned from a transaction, and the transaction was committed or rolled back.
func (aq *AtomicQuestions) Update() *AtomicQuestionsUpdateOne {
	return NewAtomicQuestionsClient(aq.config).UpdateOne(aq)
}

// Unwrap unwraps the AtomicQuestions entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (aq *AtomicQuestions) Unwrap() *AtomicQuestions {
	_tx, ok := aq.config.driver.(*txDriver)
	if !ok {
		panic("ent: AtomicQuestions is not a transactional entity")
	}
	aq.config.driver = _tx.drv
	return aq
}

// String implements the fmt.Stringer.
func (aq *AtomicQuestions) String() string {
	var builder strings.Builder
	builder.WriteString("AtomicQuestions(")
	builder.WriteString(fmt.Sprintf("id=%v, ", aq.ID))
	builder.WriteString("created_at=")
	builder.WriteString(aq.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(aq.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("entity_tag=")
	builder.WriteString(aq.EntityTag)
	builder.WriteString(", ")
	builder.WriteString("pre_entity_tag=")
	builder.WriteString(aq.PreEntityTag)
	builder.WriteString(", ")
	builder.WriteString("file_relation_id=")
	builder.WriteString(fmt.Sprintf("%v", aq.FileRelationID))
	builder.WriteString(", ")
	builder.WriteString("chunk_index=")
	builder.WriteString(fmt.Sprintf("%v", aq.ChunkIndex))
	builder.WriteString(", ")
	builder.WriteString("chunk_size=")
	builder.WriteString(fmt.Sprintf("%v", aq.ChunkSize))
	builder.WriteString(", ")
	builder.WriteString("index=")
	builder.WriteString(fmt.Sprintf("%v", aq.Index))
	builder.WriteString(", ")
	builder.WriteString("question=")
	builder.WriteString(fmt.Sprintf("%v", aq.Question))
	builder.WriteString(", ")
	builder.WriteString("is_handle=")
	builder.WriteString(fmt.Sprintf("%v", aq.IsHandle))
	builder.WriteByte(')')
	return builder.String()
}

// AtomicQuestionsSlice is a parsable slice of AtomicQuestions.
type AtomicQuestionsSlice []*AtomicQuestions

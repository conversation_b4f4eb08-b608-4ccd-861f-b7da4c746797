// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritylog"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiAgentSecurityLogDelete is the builder for deleting a AiAgentSecurityLog entity.
type AiAgentSecurityLogDelete struct {
	config
	hooks    []Hook
	mutation *AiAgentSecurityLogMutation
}

// Where appends a list predicates to the AiAgentSecurityLogDelete builder.
func (aasld *AiAgentSecurityLogDelete) Where(ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogDelete {
	aasld.mutation.Where(ps...)
	return aasld
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (aasld *AiAgentSecurityLogDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, aasld.sqlExec, aasld.mutation, aasld.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (aasld *AiAgentSecurityLogDelete) ExecX(ctx context.Context) int {
	n, err := aasld.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (aasld *AiAgentSecurityLogDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(aiagentsecuritylog.Table, sqlgraph.NewFieldSpec(aiagentsecuritylog.FieldID, field.TypeInt64))
	if ps := aasld.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, aasld.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	aasld.mutation.done = true
	return affected, err
}

// AiAgentSecurityLogDeleteOne is the builder for deleting a single AiAgentSecurityLog entity.
type AiAgentSecurityLogDeleteOne struct {
	aasld *AiAgentSecurityLogDelete
}

// Where appends a list predicates to the AiAgentSecurityLogDelete builder.
func (aasldo *AiAgentSecurityLogDeleteOne) Where(ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogDeleteOne {
	aasldo.aasld.mutation.Where(ps...)
	return aasldo
}

// Exec executes the deletion query.
func (aasldo *AiAgentSecurityLogDeleteOne) Exec(ctx context.Context) error {
	n, err := aasldo.aasld.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{aiagentsecuritylog.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (aasldo *AiAgentSecurityLogDeleteOne) ExecX(ctx context.Context) {
	if err := aasldo.Exec(ctx); err != nil {
		panic(err)
	}
}

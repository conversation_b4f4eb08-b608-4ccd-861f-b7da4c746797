// Code generated by ent, DO NOT EDIT.

package intercept

import (
	"context"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritylog"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritypolicy"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichatitem"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodeldetail"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/atomicquestions"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/classificationfiles"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/defaultagentavatar"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/externalmodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebasefile"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/useragentorder"
)

// The Query interface represents an operation that queries a graph.
// By using this interface, users can write generic code that manipulates
// query builders of different types.
type Query interface {
	// Type returns the string representation of the query type.
	Type() string
	// Limit the number of records to be returned by this query.
	Limit(int)
	// Offset to start from.
	Offset(int)
	// Unique configures the query builder to filter duplicate records.
	Unique(bool)
	// Order specifies how the records should be ordered.
	Order(...func(*sql.Selector))
	// WhereP appends storage-level predicates to the query builder. Using this method, users
	// can use type-assertion to append predicates that do not depend on any generated package.
	WhereP(...func(*sql.Selector))
}

// The Func type is an adapter that allows ordinary functions to be used as interceptors.
// Unlike traversal functions, interceptors are skipped during graph traversals. Note that the
// implementation of Func is different from the one defined in entgo.io/ent.InterceptFunc.
type Func func(context.Context, Query) error

// Intercept calls f(ctx, q) and then applied the next Querier.
func (f Func) Intercept(next ent.Querier) ent.Querier {
	return ent.QuerierFunc(func(ctx context.Context, q ent.Query) (ent.Value, error) {
		query, err := NewQuery(q)
		if err != nil {
			return nil, err
		}
		if err := f(ctx, query); err != nil {
			return nil, err
		}
		return next.Query(ctx, q)
	})
}

// The TraverseFunc type is an adapter to allow the use of ordinary function as Traverser.
// If f is a function with the appropriate signature, TraverseFunc(f) is a Traverser that calls f.
type TraverseFunc func(context.Context, Query) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFunc) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFunc) Traverse(ctx context.Context, q ent.Query) error {
	query, err := NewQuery(q)
	if err != nil {
		return err
	}
	return f(ctx, query)
}

// The AiAgentFunc type is an adapter to allow the use of ordinary function as a Querier.
type AiAgentFunc func(context.Context, *ent.AiAgentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AiAgentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AiAgentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AiAgentQuery", q)
}

// The TraverseAiAgent type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAiAgent func(context.Context, *ent.AiAgentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAiAgent) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAiAgent) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AiAgentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AiAgentQuery", q)
}

// The AiAgentSecurityLogFunc type is an adapter to allow the use of ordinary function as a Querier.
type AiAgentSecurityLogFunc func(context.Context, *ent.AiAgentSecurityLogQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AiAgentSecurityLogFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AiAgentSecurityLogQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AiAgentSecurityLogQuery", q)
}

// The TraverseAiAgentSecurityLog type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAiAgentSecurityLog func(context.Context, *ent.AiAgentSecurityLogQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAiAgentSecurityLog) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAiAgentSecurityLog) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AiAgentSecurityLogQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AiAgentSecurityLogQuery", q)
}

// The AiAgentSecurityPolicyFunc type is an adapter to allow the use of ordinary function as a Querier.
type AiAgentSecurityPolicyFunc func(context.Context, *ent.AiAgentSecurityPolicyQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AiAgentSecurityPolicyFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AiAgentSecurityPolicyQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AiAgentSecurityPolicyQuery", q)
}

// The TraverseAiAgentSecurityPolicy type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAiAgentSecurityPolicy func(context.Context, *ent.AiAgentSecurityPolicyQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAiAgentSecurityPolicy) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAiAgentSecurityPolicy) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AiAgentSecurityPolicyQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AiAgentSecurityPolicyQuery", q)
}

// The AiChatFunc type is an adapter to allow the use of ordinary function as a Querier.
type AiChatFunc func(context.Context, *ent.AiChatQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AiChatFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AiChatQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AiChatQuery", q)
}

// The TraverseAiChat type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAiChat func(context.Context, *ent.AiChatQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAiChat) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAiChat) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AiChatQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AiChatQuery", q)
}

// The AiChatItemFunc type is an adapter to allow the use of ordinary function as a Querier.
type AiChatItemFunc func(context.Context, *ent.AiChatItemQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AiChatItemFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AiChatItemQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AiChatItemQuery", q)
}

// The TraverseAiChatItem type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAiChatItem func(context.Context, *ent.AiChatItemQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAiChatItem) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAiChatItem) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AiChatItemQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AiChatItemQuery", q)
}

// The AiModelFunc type is an adapter to allow the use of ordinary function as a Querier.
type AiModelFunc func(context.Context, *ent.AiModelQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AiModelFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AiModelQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AiModelQuery", q)
}

// The TraverseAiModel type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAiModel func(context.Context, *ent.AiModelQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAiModel) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAiModel) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AiModelQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AiModelQuery", q)
}

// The AiModelDetailFunc type is an adapter to allow the use of ordinary function as a Querier.
type AiModelDetailFunc func(context.Context, *ent.AiModelDetailQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AiModelDetailFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AiModelDetailQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AiModelDetailQuery", q)
}

// The TraverseAiModelDetail type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAiModelDetail func(context.Context, *ent.AiModelDetailQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAiModelDetail) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAiModelDetail) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AiModelDetailQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AiModelDetailQuery", q)
}

// The AiModelUsageFunc type is an adapter to allow the use of ordinary function as a Querier.
type AiModelUsageFunc func(context.Context, *ent.AiModelUsageQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AiModelUsageFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AiModelUsageQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AiModelUsageQuery", q)
}

// The TraverseAiModelUsage type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAiModelUsage func(context.Context, *ent.AiModelUsageQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAiModelUsage) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAiModelUsage) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AiModelUsageQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AiModelUsageQuery", q)
}

// The AtomicQuestionsFunc type is an adapter to allow the use of ordinary function as a Querier.
type AtomicQuestionsFunc func(context.Context, *ent.AtomicQuestionsQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AtomicQuestionsFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AtomicQuestionsQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AtomicQuestionsQuery", q)
}

// The TraverseAtomicQuestions type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAtomicQuestions func(context.Context, *ent.AtomicQuestionsQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAtomicQuestions) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAtomicQuestions) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AtomicQuestionsQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AtomicQuestionsQuery", q)
}

// The ClassificationFilesFunc type is an adapter to allow the use of ordinary function as a Querier.
type ClassificationFilesFunc func(context.Context, *ent.ClassificationFilesQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ClassificationFilesFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ClassificationFilesQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ClassificationFilesQuery", q)
}

// The TraverseClassificationFiles type is an adapter to allow the use of ordinary function as Traverser.
type TraverseClassificationFiles func(context.Context, *ent.ClassificationFilesQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseClassificationFiles) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseClassificationFiles) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ClassificationFilesQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ClassificationFilesQuery", q)
}

// The DefaultAgentAvatarFunc type is an adapter to allow the use of ordinary function as a Querier.
type DefaultAgentAvatarFunc func(context.Context, *ent.DefaultAgentAvatarQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f DefaultAgentAvatarFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.DefaultAgentAvatarQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.DefaultAgentAvatarQuery", q)
}

// The TraverseDefaultAgentAvatar type is an adapter to allow the use of ordinary function as Traverser.
type TraverseDefaultAgentAvatar func(context.Context, *ent.DefaultAgentAvatarQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseDefaultAgentAvatar) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseDefaultAgentAvatar) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.DefaultAgentAvatarQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.DefaultAgentAvatarQuery", q)
}

// The ExternalModelUsageFunc type is an adapter to allow the use of ordinary function as a Querier.
type ExternalModelUsageFunc func(context.Context, *ent.ExternalModelUsageQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ExternalModelUsageFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ExternalModelUsageQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ExternalModelUsageQuery", q)
}

// The TraverseExternalModelUsage type is an adapter to allow the use of ordinary function as Traverser.
type TraverseExternalModelUsage func(context.Context, *ent.ExternalModelUsageQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseExternalModelUsage) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseExternalModelUsage) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ExternalModelUsageQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ExternalModelUsageQuery", q)
}

// The KnowledgeBaseFunc type is an adapter to allow the use of ordinary function as a Querier.
type KnowledgeBaseFunc func(context.Context, *ent.KnowledgeBaseQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f KnowledgeBaseFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.KnowledgeBaseQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.KnowledgeBaseQuery", q)
}

// The TraverseKnowledgeBase type is an adapter to allow the use of ordinary function as Traverser.
type TraverseKnowledgeBase func(context.Context, *ent.KnowledgeBaseQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseKnowledgeBase) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseKnowledgeBase) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.KnowledgeBaseQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.KnowledgeBaseQuery", q)
}

// The KnowledgeBaseFileFunc type is an adapter to allow the use of ordinary function as a Querier.
type KnowledgeBaseFileFunc func(context.Context, *ent.KnowledgeBaseFileQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f KnowledgeBaseFileFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.KnowledgeBaseFileQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.KnowledgeBaseFileQuery", q)
}

// The TraverseKnowledgeBaseFile type is an adapter to allow the use of ordinary function as Traverser.
type TraverseKnowledgeBaseFile func(context.Context, *ent.KnowledgeBaseFileQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseKnowledgeBaseFile) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseKnowledgeBaseFile) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.KnowledgeBaseFileQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.KnowledgeBaseFileQuery", q)
}

// The UserAgentOrderFunc type is an adapter to allow the use of ordinary function as a Querier.
type UserAgentOrderFunc func(context.Context, *ent.UserAgentOrderQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f UserAgentOrderFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.UserAgentOrderQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.UserAgentOrderQuery", q)
}

// The TraverseUserAgentOrder type is an adapter to allow the use of ordinary function as Traverser.
type TraverseUserAgentOrder func(context.Context, *ent.UserAgentOrderQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseUserAgentOrder) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseUserAgentOrder) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserAgentOrderQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.UserAgentOrderQuery", q)
}

// NewQuery returns the generic Query interface for the given typed query.
func NewQuery(q ent.Query) (Query, error) {
	switch q := q.(type) {
	case *ent.AiAgentQuery:
		return &query[*ent.AiAgentQuery, predicate.AiAgent, aiagent.OrderOption]{typ: ent.TypeAiAgent, tq: q}, nil
	case *ent.AiAgentSecurityLogQuery:
		return &query[*ent.AiAgentSecurityLogQuery, predicate.AiAgentSecurityLog, aiagentsecuritylog.OrderOption]{typ: ent.TypeAiAgentSecurityLog, tq: q}, nil
	case *ent.AiAgentSecurityPolicyQuery:
		return &query[*ent.AiAgentSecurityPolicyQuery, predicate.AiAgentSecurityPolicy, aiagentsecuritypolicy.OrderOption]{typ: ent.TypeAiAgentSecurityPolicy, tq: q}, nil
	case *ent.AiChatQuery:
		return &query[*ent.AiChatQuery, predicate.AiChat, aichat.OrderOption]{typ: ent.TypeAiChat, tq: q}, nil
	case *ent.AiChatItemQuery:
		return &query[*ent.AiChatItemQuery, predicate.AiChatItem, aichatitem.OrderOption]{typ: ent.TypeAiChatItem, tq: q}, nil
	case *ent.AiModelQuery:
		return &query[*ent.AiModelQuery, predicate.AiModel, aimodel.OrderOption]{typ: ent.TypeAiModel, tq: q}, nil
	case *ent.AiModelDetailQuery:
		return &query[*ent.AiModelDetailQuery, predicate.AiModelDetail, aimodeldetail.OrderOption]{typ: ent.TypeAiModelDetail, tq: q}, nil
	case *ent.AiModelUsageQuery:
		return &query[*ent.AiModelUsageQuery, predicate.AiModelUsage, aimodelusage.OrderOption]{typ: ent.TypeAiModelUsage, tq: q}, nil
	case *ent.AtomicQuestionsQuery:
		return &query[*ent.AtomicQuestionsQuery, predicate.AtomicQuestions, atomicquestions.OrderOption]{typ: ent.TypeAtomicQuestions, tq: q}, nil
	case *ent.ClassificationFilesQuery:
		return &query[*ent.ClassificationFilesQuery, predicate.ClassificationFiles, classificationfiles.OrderOption]{typ: ent.TypeClassificationFiles, tq: q}, nil
	case *ent.DefaultAgentAvatarQuery:
		return &query[*ent.DefaultAgentAvatarQuery, predicate.DefaultAgentAvatar, defaultagentavatar.OrderOption]{typ: ent.TypeDefaultAgentAvatar, tq: q}, nil
	case *ent.ExternalModelUsageQuery:
		return &query[*ent.ExternalModelUsageQuery, predicate.ExternalModelUsage, externalmodelusage.OrderOption]{typ: ent.TypeExternalModelUsage, tq: q}, nil
	case *ent.KnowledgeBaseQuery:
		return &query[*ent.KnowledgeBaseQuery, predicate.KnowledgeBase, knowledgebase.OrderOption]{typ: ent.TypeKnowledgeBase, tq: q}, nil
	case *ent.KnowledgeBaseFileQuery:
		return &query[*ent.KnowledgeBaseFileQuery, predicate.KnowledgeBaseFile, knowledgebasefile.OrderOption]{typ: ent.TypeKnowledgeBaseFile, tq: q}, nil
	case *ent.UserAgentOrderQuery:
		return &query[*ent.UserAgentOrderQuery, predicate.UserAgentOrder, useragentorder.OrderOption]{typ: ent.TypeUserAgentOrder, tq: q}, nil
	default:
		return nil, fmt.Errorf("unknown query type %T", q)
	}
}

type query[T any, P ~func(*sql.Selector), R ~func(*sql.Selector)] struct {
	typ string
	tq  interface {
		Limit(int) T
		Offset(int) T
		Unique(bool) T
		Order(...R) T
		Where(...P) T
	}
}

func (q query[T, P, R]) Type() string {
	return q.typ
}

func (q query[T, P, R]) Limit(limit int) {
	q.tq.Limit(limit)
}

func (q query[T, P, R]) Offset(offset int) {
	q.tq.Offset(offset)
}

func (q query[T, P, R]) Unique(unique bool) {
	q.tq.Unique(unique)
}

func (q query[T, P, R]) Order(orders ...func(*sql.Selector)) {
	rs := make([]R, len(orders))
	for i := range orders {
		rs[i] = orders[i]
	}
	q.tq.Order(rs...)
}

func (q query[T, P, R]) WhereP(ps ...func(*sql.Selector)) {
	p := make([]P, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	q.tq.Where(p...)
}

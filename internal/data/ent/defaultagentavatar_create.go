// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/defaultagentavatar"
)

// DefaultAgentAvatarCreate is the builder for creating a DefaultAgentAvatar entity.
type DefaultAgentAvatarCreate struct {
	config
	mutation *DefaultAgentAvatarMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetTenantID sets the "tenant_id" field.
func (daac *DefaultAgentAvatarCreate) SetTenantID(i int64) *DefaultAgentAvatarCreate {
	daac.mutation.SetTenantID(i)
	return daac
}

// SetCreatedAt sets the "created_at" field.
func (daac *DefaultAgentAvatarCreate) SetCreatedAt(t time.Time) *DefaultAgentAvatarCreate {
	daac.mutation.SetCreatedAt(t)
	return daac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (daac *DefaultAgentAvatarCreate) SetNillableCreatedAt(t *time.Time) *DefaultAgentAvatarCreate {
	if t != nil {
		daac.SetCreatedAt(*t)
	}
	return daac
}

// SetUpdatedAt sets the "updated_at" field.
func (daac *DefaultAgentAvatarCreate) SetUpdatedAt(t time.Time) *DefaultAgentAvatarCreate {
	daac.mutation.SetUpdatedAt(t)
	return daac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (daac *DefaultAgentAvatarCreate) SetNillableUpdatedAt(t *time.Time) *DefaultAgentAvatarCreate {
	if t != nil {
		daac.SetUpdatedAt(*t)
	}
	return daac
}

// SetDeletedAt sets the "deleted_at" field.
func (daac *DefaultAgentAvatarCreate) SetDeletedAt(t time.Time) *DefaultAgentAvatarCreate {
	daac.mutation.SetDeletedAt(t)
	return daac
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (daac *DefaultAgentAvatarCreate) SetNillableDeletedAt(t *time.Time) *DefaultAgentAvatarCreate {
	if t != nil {
		daac.SetDeletedAt(*t)
	}
	return daac
}

// SetAvatar sets the "avatar" field.
func (daac *DefaultAgentAvatarCreate) SetAvatar(s string) *DefaultAgentAvatarCreate {
	daac.mutation.SetAvatar(s)
	return daac
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (daac *DefaultAgentAvatarCreate) SetClickedAvatar(s string) *DefaultAgentAvatarCreate {
	daac.mutation.SetClickedAvatar(s)
	return daac
}

// SetAvatarType sets the "avatar_type" field.
func (daac *DefaultAgentAvatarCreate) SetAvatarType(i int8) *DefaultAgentAvatarCreate {
	daac.mutation.SetAvatarType(i)
	return daac
}

// SetID sets the "id" field.
func (daac *DefaultAgentAvatarCreate) SetID(i int64) *DefaultAgentAvatarCreate {
	daac.mutation.SetID(i)
	return daac
}

// Mutation returns the DefaultAgentAvatarMutation object of the builder.
func (daac *DefaultAgentAvatarCreate) Mutation() *DefaultAgentAvatarMutation {
	return daac.mutation
}

// Save creates the DefaultAgentAvatar in the database.
func (daac *DefaultAgentAvatarCreate) Save(ctx context.Context) (*DefaultAgentAvatar, error) {
	if err := daac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, daac.sqlSave, daac.mutation, daac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (daac *DefaultAgentAvatarCreate) SaveX(ctx context.Context) *DefaultAgentAvatar {
	v, err := daac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (daac *DefaultAgentAvatarCreate) Exec(ctx context.Context) error {
	_, err := daac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (daac *DefaultAgentAvatarCreate) ExecX(ctx context.Context) {
	if err := daac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (daac *DefaultAgentAvatarCreate) defaults() error {
	if _, ok := daac.mutation.CreatedAt(); !ok {
		if defaultagentavatar.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized defaultagentavatar.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := defaultagentavatar.DefaultCreatedAt()
		daac.mutation.SetCreatedAt(v)
	}
	if _, ok := daac.mutation.UpdatedAt(); !ok {
		if defaultagentavatar.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized defaultagentavatar.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := defaultagentavatar.DefaultUpdatedAt()
		daac.mutation.SetUpdatedAt(v)
	}
	if _, ok := daac.mutation.DeletedAt(); !ok {
		v := defaultagentavatar.DefaultDeletedAt
		daac.mutation.SetDeletedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (daac *DefaultAgentAvatarCreate) check() error {
	if _, ok := daac.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "DefaultAgentAvatar.tenant_id"`)}
	}
	if _, ok := daac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "DefaultAgentAvatar.created_at"`)}
	}
	if _, ok := daac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "DefaultAgentAvatar.updated_at"`)}
	}
	if _, ok := daac.mutation.Avatar(); !ok {
		return &ValidationError{Name: "avatar", err: errors.New(`ent: missing required field "DefaultAgentAvatar.avatar"`)}
	}
	if _, ok := daac.mutation.ClickedAvatar(); !ok {
		return &ValidationError{Name: "clicked_avatar", err: errors.New(`ent: missing required field "DefaultAgentAvatar.clicked_avatar"`)}
	}
	if _, ok := daac.mutation.AvatarType(); !ok {
		return &ValidationError{Name: "avatar_type", err: errors.New(`ent: missing required field "DefaultAgentAvatar.avatar_type"`)}
	}
	return nil
}

func (daac *DefaultAgentAvatarCreate) sqlSave(ctx context.Context) (*DefaultAgentAvatar, error) {
	if err := daac.check(); err != nil {
		return nil, err
	}
	_node, _spec := daac.createSpec()
	if err := sqlgraph.CreateNode(ctx, daac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	daac.mutation.id = &_node.ID
	daac.mutation.done = true
	return _node, nil
}

func (daac *DefaultAgentAvatarCreate) createSpec() (*DefaultAgentAvatar, *sqlgraph.CreateSpec) {
	var (
		_node = &DefaultAgentAvatar{config: daac.config}
		_spec = sqlgraph.NewCreateSpec(defaultagentavatar.Table, sqlgraph.NewFieldSpec(defaultagentavatar.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = daac.conflict
	if id, ok := daac.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := daac.mutation.TenantID(); ok {
		_spec.SetField(defaultagentavatar.FieldTenantID, field.TypeInt64, value)
		_node.TenantID = value
	}
	if value, ok := daac.mutation.CreatedAt(); ok {
		_spec.SetField(defaultagentavatar.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := daac.mutation.UpdatedAt(); ok {
		_spec.SetField(defaultagentavatar.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := daac.mutation.DeletedAt(); ok {
		_spec.SetField(defaultagentavatar.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := daac.mutation.Avatar(); ok {
		_spec.SetField(defaultagentavatar.FieldAvatar, field.TypeString, value)
		_node.Avatar = value
	}
	if value, ok := daac.mutation.ClickedAvatar(); ok {
		_spec.SetField(defaultagentavatar.FieldClickedAvatar, field.TypeString, value)
		_node.ClickedAvatar = value
	}
	if value, ok := daac.mutation.AvatarType(); ok {
		_spec.SetField(defaultagentavatar.FieldAvatarType, field.TypeInt8, value)
		_node.AvatarType = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DefaultAgentAvatar.Create().
//		SetTenantID(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DefaultAgentAvatarUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (daac *DefaultAgentAvatarCreate) OnConflict(opts ...sql.ConflictOption) *DefaultAgentAvatarUpsertOne {
	daac.conflict = opts
	return &DefaultAgentAvatarUpsertOne{
		create: daac,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DefaultAgentAvatar.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (daac *DefaultAgentAvatarCreate) OnConflictColumns(columns ...string) *DefaultAgentAvatarUpsertOne {
	daac.conflict = append(daac.conflict, sql.ConflictColumns(columns...))
	return &DefaultAgentAvatarUpsertOne{
		create: daac,
	}
}

type (
	// DefaultAgentAvatarUpsertOne is the builder for "upsert"-ing
	//  one DefaultAgentAvatar node.
	DefaultAgentAvatarUpsertOne struct {
		create *DefaultAgentAvatarCreate
	}

	// DefaultAgentAvatarUpsert is the "OnConflict" setter.
	DefaultAgentAvatarUpsert struct {
		*sql.UpdateSet
	}
)

// SetTenantID sets the "tenant_id" field.
func (u *DefaultAgentAvatarUpsert) SetTenantID(v int64) *DefaultAgentAvatarUpsert {
	u.Set(defaultagentavatar.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsert) UpdateTenantID() *DefaultAgentAvatarUpsert {
	u.SetExcluded(defaultagentavatar.FieldTenantID)
	return u
}

// AddTenantID adds v to the "tenant_id" field.
func (u *DefaultAgentAvatarUpsert) AddTenantID(v int64) *DefaultAgentAvatarUpsert {
	u.Add(defaultagentavatar.FieldTenantID, v)
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *DefaultAgentAvatarUpsert) SetUpdatedAt(v time.Time) *DefaultAgentAvatarUpsert {
	u.Set(defaultagentavatar.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsert) UpdateUpdatedAt() *DefaultAgentAvatarUpsert {
	u.SetExcluded(defaultagentavatar.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *DefaultAgentAvatarUpsert) SetDeletedAt(v time.Time) *DefaultAgentAvatarUpsert {
	u.Set(defaultagentavatar.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsert) UpdateDeletedAt() *DefaultAgentAvatarUpsert {
	u.SetExcluded(defaultagentavatar.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *DefaultAgentAvatarUpsert) ClearDeletedAt() *DefaultAgentAvatarUpsert {
	u.SetNull(defaultagentavatar.FieldDeletedAt)
	return u
}

// SetAvatar sets the "avatar" field.
func (u *DefaultAgentAvatarUpsert) SetAvatar(v string) *DefaultAgentAvatarUpsert {
	u.Set(defaultagentavatar.FieldAvatar, v)
	return u
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsert) UpdateAvatar() *DefaultAgentAvatarUpsert {
	u.SetExcluded(defaultagentavatar.FieldAvatar)
	return u
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (u *DefaultAgentAvatarUpsert) SetClickedAvatar(v string) *DefaultAgentAvatarUpsert {
	u.Set(defaultagentavatar.FieldClickedAvatar, v)
	return u
}

// UpdateClickedAvatar sets the "clicked_avatar" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsert) UpdateClickedAvatar() *DefaultAgentAvatarUpsert {
	u.SetExcluded(defaultagentavatar.FieldClickedAvatar)
	return u
}

// SetAvatarType sets the "avatar_type" field.
func (u *DefaultAgentAvatarUpsert) SetAvatarType(v int8) *DefaultAgentAvatarUpsert {
	u.Set(defaultagentavatar.FieldAvatarType, v)
	return u
}

// UpdateAvatarType sets the "avatar_type" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsert) UpdateAvatarType() *DefaultAgentAvatarUpsert {
	u.SetExcluded(defaultagentavatar.FieldAvatarType)
	return u
}

// AddAvatarType adds v to the "avatar_type" field.
func (u *DefaultAgentAvatarUpsert) AddAvatarType(v int8) *DefaultAgentAvatarUpsert {
	u.Add(defaultagentavatar.FieldAvatarType, v)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.DefaultAgentAvatar.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(defaultagentavatar.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DefaultAgentAvatarUpsertOne) UpdateNewValues() *DefaultAgentAvatarUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(defaultagentavatar.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(defaultagentavatar.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DefaultAgentAvatar.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *DefaultAgentAvatarUpsertOne) Ignore() *DefaultAgentAvatarUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DefaultAgentAvatarUpsertOne) DoNothing() *DefaultAgentAvatarUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DefaultAgentAvatarCreate.OnConflict
// documentation for more info.
func (u *DefaultAgentAvatarUpsertOne) Update(set func(*DefaultAgentAvatarUpsert)) *DefaultAgentAvatarUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DefaultAgentAvatarUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *DefaultAgentAvatarUpsertOne) SetTenantID(v int64) *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *DefaultAgentAvatarUpsertOne) AddTenantID(v int64) *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertOne) UpdateTenantID() *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *DefaultAgentAvatarUpsertOne) SetUpdatedAt(v time.Time) *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertOne) UpdateUpdatedAt() *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *DefaultAgentAvatarUpsertOne) SetDeletedAt(v time.Time) *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertOne) UpdateDeletedAt() *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *DefaultAgentAvatarUpsertOne) ClearDeletedAt() *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.ClearDeletedAt()
	})
}

// SetAvatar sets the "avatar" field.
func (u *DefaultAgentAvatarUpsertOne) SetAvatar(v string) *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetAvatar(v)
	})
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertOne) UpdateAvatar() *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateAvatar()
	})
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (u *DefaultAgentAvatarUpsertOne) SetClickedAvatar(v string) *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetClickedAvatar(v)
	})
}

// UpdateClickedAvatar sets the "clicked_avatar" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertOne) UpdateClickedAvatar() *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateClickedAvatar()
	})
}

// SetAvatarType sets the "avatar_type" field.
func (u *DefaultAgentAvatarUpsertOne) SetAvatarType(v int8) *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetAvatarType(v)
	})
}

// AddAvatarType adds v to the "avatar_type" field.
func (u *DefaultAgentAvatarUpsertOne) AddAvatarType(v int8) *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.AddAvatarType(v)
	})
}

// UpdateAvatarType sets the "avatar_type" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertOne) UpdateAvatarType() *DefaultAgentAvatarUpsertOne {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateAvatarType()
	})
}

// Exec executes the query.
func (u *DefaultAgentAvatarUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DefaultAgentAvatarCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DefaultAgentAvatarUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *DefaultAgentAvatarUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *DefaultAgentAvatarUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// DefaultAgentAvatarCreateBulk is the builder for creating many DefaultAgentAvatar entities in bulk.
type DefaultAgentAvatarCreateBulk struct {
	config
	err      error
	builders []*DefaultAgentAvatarCreate
	conflict []sql.ConflictOption
}

// Save creates the DefaultAgentAvatar entities in the database.
func (daacb *DefaultAgentAvatarCreateBulk) Save(ctx context.Context) ([]*DefaultAgentAvatar, error) {
	if daacb.err != nil {
		return nil, daacb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(daacb.builders))
	nodes := make([]*DefaultAgentAvatar, len(daacb.builders))
	mutators := make([]Mutator, len(daacb.builders))
	for i := range daacb.builders {
		func(i int, root context.Context) {
			builder := daacb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*DefaultAgentAvatarMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, daacb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = daacb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, daacb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, daacb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (daacb *DefaultAgentAvatarCreateBulk) SaveX(ctx context.Context) []*DefaultAgentAvatar {
	v, err := daacb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (daacb *DefaultAgentAvatarCreateBulk) Exec(ctx context.Context) error {
	_, err := daacb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (daacb *DefaultAgentAvatarCreateBulk) ExecX(ctx context.Context) {
	if err := daacb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.DefaultAgentAvatar.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.DefaultAgentAvatarUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (daacb *DefaultAgentAvatarCreateBulk) OnConflict(opts ...sql.ConflictOption) *DefaultAgentAvatarUpsertBulk {
	daacb.conflict = opts
	return &DefaultAgentAvatarUpsertBulk{
		create: daacb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.DefaultAgentAvatar.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (daacb *DefaultAgentAvatarCreateBulk) OnConflictColumns(columns ...string) *DefaultAgentAvatarUpsertBulk {
	daacb.conflict = append(daacb.conflict, sql.ConflictColumns(columns...))
	return &DefaultAgentAvatarUpsertBulk{
		create: daacb,
	}
}

// DefaultAgentAvatarUpsertBulk is the builder for "upsert"-ing
// a bulk of DefaultAgentAvatar nodes.
type DefaultAgentAvatarUpsertBulk struct {
	create *DefaultAgentAvatarCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.DefaultAgentAvatar.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(defaultagentavatar.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *DefaultAgentAvatarUpsertBulk) UpdateNewValues() *DefaultAgentAvatarUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(defaultagentavatar.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(defaultagentavatar.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.DefaultAgentAvatar.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *DefaultAgentAvatarUpsertBulk) Ignore() *DefaultAgentAvatarUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *DefaultAgentAvatarUpsertBulk) DoNothing() *DefaultAgentAvatarUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the DefaultAgentAvatarCreateBulk.OnConflict
// documentation for more info.
func (u *DefaultAgentAvatarUpsertBulk) Update(set func(*DefaultAgentAvatarUpsert)) *DefaultAgentAvatarUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&DefaultAgentAvatarUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *DefaultAgentAvatarUpsertBulk) SetTenantID(v int64) *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *DefaultAgentAvatarUpsertBulk) AddTenantID(v int64) *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertBulk) UpdateTenantID() *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *DefaultAgentAvatarUpsertBulk) SetUpdatedAt(v time.Time) *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertBulk) UpdateUpdatedAt() *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *DefaultAgentAvatarUpsertBulk) SetDeletedAt(v time.Time) *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertBulk) UpdateDeletedAt() *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *DefaultAgentAvatarUpsertBulk) ClearDeletedAt() *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.ClearDeletedAt()
	})
}

// SetAvatar sets the "avatar" field.
func (u *DefaultAgentAvatarUpsertBulk) SetAvatar(v string) *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetAvatar(v)
	})
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertBulk) UpdateAvatar() *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateAvatar()
	})
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (u *DefaultAgentAvatarUpsertBulk) SetClickedAvatar(v string) *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetClickedAvatar(v)
	})
}

// UpdateClickedAvatar sets the "clicked_avatar" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertBulk) UpdateClickedAvatar() *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateClickedAvatar()
	})
}

// SetAvatarType sets the "avatar_type" field.
func (u *DefaultAgentAvatarUpsertBulk) SetAvatarType(v int8) *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.SetAvatarType(v)
	})
}

// AddAvatarType adds v to the "avatar_type" field.
func (u *DefaultAgentAvatarUpsertBulk) AddAvatarType(v int8) *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.AddAvatarType(v)
	})
}

// UpdateAvatarType sets the "avatar_type" field to the value that was provided on create.
func (u *DefaultAgentAvatarUpsertBulk) UpdateAvatarType() *DefaultAgentAvatarUpsertBulk {
	return u.Update(func(s *DefaultAgentAvatarUpsert) {
		s.UpdateAvatarType()
	})
}

// Exec executes the query.
func (u *DefaultAgentAvatarUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the DefaultAgentAvatarCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for DefaultAgentAvatarCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *DefaultAgentAvatarUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
)

// AiModel is the model entity for the AiModel schema.
type AiModel struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 模型名称
	ModelName string `json:"model_name,omitempty"`
	// 模型
	Model int64 `json:"model,omitempty"`
	// api_key
	APIKey       string `json:"api_key,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AiModel) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case aimodel.FieldID, aimodel.FieldModel:
			values[i] = new(sql.NullInt64)
		case aimodel.FieldModelName, aimodel.FieldAPIKey:
			values[i] = new(sql.NullString)
		case aimodel.FieldCreatedAt, aimodel.FieldUpdatedAt, aimodel.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AiModel fields.
func (am *AiModel) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case aimodel.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			am.ID = int64(value.Int64)
		case aimodel.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				am.CreatedAt = value.Time
			}
		case aimodel.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				am.UpdatedAt = value.Time
			}
		case aimodel.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				am.DeletedAt = value.Time
			}
		case aimodel.FieldModelName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field model_name", values[i])
			} else if value.Valid {
				am.ModelName = value.String
			}
		case aimodel.FieldModel:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field model", values[i])
			} else if value.Valid {
				am.Model = value.Int64
			}
		case aimodel.FieldAPIKey:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field api_key", values[i])
			} else if value.Valid {
				am.APIKey = value.String
			}
		default:
			am.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AiModel.
// This includes values selected through modifiers, order, etc.
func (am *AiModel) Value(name string) (ent.Value, error) {
	return am.selectValues.Get(name)
}

// Update returns a builder for updating this AiModel.
// Note that you need to call AiModel.Unwrap() before calling this method if this AiModel
// was returned from a transaction, and the transaction was committed or rolled back.
func (am *AiModel) Update() *AiModelUpdateOne {
	return NewAiModelClient(am.config).UpdateOne(am)
}

// Unwrap unwraps the AiModel entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (am *AiModel) Unwrap() *AiModel {
	_tx, ok := am.config.driver.(*txDriver)
	if !ok {
		panic("ent: AiModel is not a transactional entity")
	}
	am.config.driver = _tx.drv
	return am
}

// String implements the fmt.Stringer.
func (am *AiModel) String() string {
	var builder strings.Builder
	builder.WriteString("AiModel(")
	builder.WriteString(fmt.Sprintf("id=%v, ", am.ID))
	builder.WriteString("created_at=")
	builder.WriteString(am.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(am.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(am.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("model_name=")
	builder.WriteString(am.ModelName)
	builder.WriteString(", ")
	builder.WriteString("model=")
	builder.WriteString(fmt.Sprintf("%v", am.Model))
	builder.WriteString(", ")
	builder.WriteString("api_key=")
	builder.WriteString(am.APIKey)
	builder.WriteByte(')')
	return builder.String()
}

// AiModels is a parsable slice of AiModel.
type AiModels []*AiModel

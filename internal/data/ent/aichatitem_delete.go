// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichatitem"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiChatItemDelete is the builder for deleting a AiChatItem entity.
type AiChatItemDelete struct {
	config
	hooks    []Hook
	mutation *AiChatItemMutation
}

// Where appends a list predicates to the AiChatItemDelete builder.
func (acid *AiChatItemDelete) Where(ps ...predicate.AiChatItem) *AiChatItemDelete {
	acid.mutation.Where(ps...)
	return acid
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (acid *AiChatItemDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, acid.sqlExec, acid.mutation, acid.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (acid *AiChatItemDelete) ExecX(ctx context.Context) int {
	n, err := acid.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (acid *AiChatItemDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(aichatitem.Table, sqlgraph.NewFieldSpec(aichatitem.FieldID, field.TypeInt64))
	if ps := acid.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, acid.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	acid.mutation.done = true
	return affected, err
}

// AiChatItemDeleteOne is the builder for deleting a single AiChatItem entity.
type AiChatItemDeleteOne struct {
	acid *AiChatItemDelete
}

// Where appends a list predicates to the AiChatItemDelete builder.
func (acido *AiChatItemDeleteOne) Where(ps ...predicate.AiChatItem) *AiChatItemDeleteOne {
	acido.acid.mutation.Where(ps...)
	return acido
}

// Exec executes the deletion query.
func (acido *AiChatItemDeleteOne) Exec(ctx context.Context) error {
	n, err := acido.acid.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{aichatitem.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (acido *AiChatItemDeleteOne) ExecX(ctx context.Context) {
	if err := acido.Exec(ctx); err != nil {
		panic(err)
	}
}

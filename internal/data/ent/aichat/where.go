// Code generated by ent, DO NOT EDIT.

package aichat

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldDeletedAt, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldName, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldUserID, v))
}

// ChatType applies equality check predicate on the "chat_type" field. It's identical to ChatTypeEQ.
func ChatType(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldChatType, v))
}

// AgentID applies equality check predicate on the "agent_id" field. It's identical to AgentIDEQ.
func AgentID(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldAgentID, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldLTE(FieldTenantID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.AiChat {
	return predicate.AiChat(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.AiChat {
	return predicate.AiChat(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.AiChat {
	return predicate.AiChat(sql.FieldNotNull(FieldDeletedAt))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.AiChat {
	return predicate.AiChat(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.AiChat {
	return predicate.AiChat(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.AiChat {
	return predicate.AiChat(sql.FieldContainsFold(FieldName, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldLTE(FieldUserID, v))
}

// ChatTypeEQ applies the EQ predicate on the "chat_type" field.
func ChatTypeEQ(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldChatType, v))
}

// ChatTypeNEQ applies the NEQ predicate on the "chat_type" field.
func ChatTypeNEQ(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNEQ(FieldChatType, v))
}

// ChatTypeIn applies the In predicate on the "chat_type" field.
func ChatTypeIn(vs ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldIn(FieldChatType, vs...))
}

// ChatTypeNotIn applies the NotIn predicate on the "chat_type" field.
func ChatTypeNotIn(vs ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNotIn(FieldChatType, vs...))
}

// ChatTypeGT applies the GT predicate on the "chat_type" field.
func ChatTypeGT(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldGT(FieldChatType, v))
}

// ChatTypeGTE applies the GTE predicate on the "chat_type" field.
func ChatTypeGTE(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldGTE(FieldChatType, v))
}

// ChatTypeLT applies the LT predicate on the "chat_type" field.
func ChatTypeLT(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldLT(FieldChatType, v))
}

// ChatTypeLTE applies the LTE predicate on the "chat_type" field.
func ChatTypeLTE(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldLTE(FieldChatType, v))
}

// AgentIDEQ applies the EQ predicate on the "agent_id" field.
func AgentIDEQ(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldEQ(FieldAgentID, v))
}

// AgentIDNEQ applies the NEQ predicate on the "agent_id" field.
func AgentIDNEQ(v int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNEQ(FieldAgentID, v))
}

// AgentIDIn applies the In predicate on the "agent_id" field.
func AgentIDIn(vs ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldIn(FieldAgentID, vs...))
}

// AgentIDNotIn applies the NotIn predicate on the "agent_id" field.
func AgentIDNotIn(vs ...int64) predicate.AiChat {
	return predicate.AiChat(sql.FieldNotIn(FieldAgentID, vs...))
}

// AgentIDIsNil applies the IsNil predicate on the "agent_id" field.
func AgentIDIsNil() predicate.AiChat {
	return predicate.AiChat(sql.FieldIsNull(FieldAgentID))
}

// AgentIDNotNil applies the NotNil predicate on the "agent_id" field.
func AgentIDNotNil() predicate.AiChat {
	return predicate.AiChat(sql.FieldNotNull(FieldAgentID))
}

// HasAiAgent applies the HasEdge predicate on the "ai_agent" edge.
func HasAiAgent() predicate.AiChat {
	return predicate.AiChat(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, AiAgentTable, AiAgentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAiAgentWith applies the HasEdge predicate on the "ai_agent" edge with a given conditions (other predicates).
func HasAiAgentWith(preds ...predicate.AiAgent) predicate.AiChat {
	return predicate.AiChat(func(s *sql.Selector) {
		step := newAiAgentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AiChat) predicate.AiChat {
	return predicate.AiChat(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AiChat) predicate.AiChat {
	return predicate.AiChat(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AiChat) predicate.AiChat {
	return predicate.AiChat(sql.NotPredicates(p))
}

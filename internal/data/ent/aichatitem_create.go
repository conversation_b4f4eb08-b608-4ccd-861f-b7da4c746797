// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichatitem"
)

// AiChatItemCreate is the builder for creating a AiChatItem entity.
type AiChatItemCreate struct {
	config
	mutation *AiChatItemMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetTenantID sets the "tenant_id" field.
func (acic *AiChatItemCreate) SetTenantID(i int64) *AiChatItemCreate {
	acic.mutation.SetTenantID(i)
	return acic
}

// SetCreatedAt sets the "created_at" field.
func (acic *AiChatItemCreate) SetCreatedAt(t time.Time) *AiChatItemCreate {
	acic.mutation.SetCreatedAt(t)
	return acic
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableCreatedAt(t *time.Time) *AiChatItemCreate {
	if t != nil {
		acic.SetCreatedAt(*t)
	}
	return acic
}

// SetUpdatedAt sets the "updated_at" field.
func (acic *AiChatItemCreate) SetUpdatedAt(t time.Time) *AiChatItemCreate {
	acic.mutation.SetUpdatedAt(t)
	return acic
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableUpdatedAt(t *time.Time) *AiChatItemCreate {
	if t != nil {
		acic.SetUpdatedAt(*t)
	}
	return acic
}

// SetDeletedAt sets the "deleted_at" field.
func (acic *AiChatItemCreate) SetDeletedAt(t time.Time) *AiChatItemCreate {
	acic.mutation.SetDeletedAt(t)
	return acic
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableDeletedAt(t *time.Time) *AiChatItemCreate {
	if t != nil {
		acic.SetDeletedAt(*t)
	}
	return acic
}

// SetChatID sets the "chat_id" field.
func (acic *AiChatItemCreate) SetChatID(i int64) *AiChatItemCreate {
	acic.mutation.SetChatID(i)
	return acic
}

// SetObjectID sets the "object_id" field.
func (acic *AiChatItemCreate) SetObjectID(i int64) *AiChatItemCreate {
	acic.mutation.SetObjectID(i)
	return acic
}

// SetObjectType sets the "object_type" field.
func (acic *AiChatItemCreate) SetObjectType(i int64) *AiChatItemCreate {
	acic.mutation.SetObjectType(i)
	return acic
}

// SetMessage sets the "message" field.
func (acic *AiChatItemCreate) SetMessage(s string) *AiChatItemCreate {
	acic.mutation.SetMessage(s)
	return acic
}

// SetRefFiles sets the "ref_files" field.
func (acic *AiChatItemCreate) SetRefFiles(s string) *AiChatItemCreate {
	acic.mutation.SetRefFiles(s)
	return acic
}

// SetAgreeStatus sets the "agree_status" field.
func (acic *AiChatItemCreate) SetAgreeStatus(i int8) *AiChatItemCreate {
	acic.mutation.SetAgreeStatus(i)
	return acic
}

// SetNillableAgreeStatus sets the "agree_status" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableAgreeStatus(i *int8) *AiChatItemCreate {
	if i != nil {
		acic.SetAgreeStatus(*i)
	}
	return acic
}

// SetRoundID sets the "round_id" field.
func (acic *AiChatItemCreate) SetRoundID(i int64) *AiChatItemCreate {
	acic.mutation.SetRoundID(i)
	return acic
}

// SetNillableRoundID sets the "round_id" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableRoundID(i *int64) *AiChatItemCreate {
	if i != nil {
		acic.SetRoundID(*i)
	}
	return acic
}

// SetPcName sets the "pc_name" field.
func (acic *AiChatItemCreate) SetPcName(s string) *AiChatItemCreate {
	acic.mutation.SetPcName(s)
	return acic
}

// SetNillablePcName sets the "pc_name" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillablePcName(s *string) *AiChatItemCreate {
	if s != nil {
		acic.SetPcName(*s)
	}
	return acic
}

// SetReason sets the "reason" field.
func (acic *AiChatItemCreate) SetReason(s string) *AiChatItemCreate {
	acic.mutation.SetReason(s)
	return acic
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableReason(s *string) *AiChatItemCreate {
	if s != nil {
		acic.SetReason(*s)
	}
	return acic
}

// SetPrimaryClassification sets the "primary_classification" field.
func (acic *AiChatItemCreate) SetPrimaryClassification(s string) *AiChatItemCreate {
	acic.mutation.SetPrimaryClassification(s)
	return acic
}

// SetNillablePrimaryClassification sets the "primary_classification" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillablePrimaryClassification(s *string) *AiChatItemCreate {
	if s != nil {
		acic.SetPrimaryClassification(*s)
	}
	return acic
}

// SetSecondaryClassification sets the "secondary_classification" field.
func (acic *AiChatItemCreate) SetSecondaryClassification(s string) *AiChatItemCreate {
	acic.mutation.SetSecondaryClassification(s)
	return acic
}

// SetNillableSecondaryClassification sets the "secondary_classification" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableSecondaryClassification(s *string) *AiChatItemCreate {
	if s != nil {
		acic.SetSecondaryClassification(*s)
	}
	return acic
}

// SetMineTypes sets the "mine_types" field.
func (acic *AiChatItemCreate) SetMineTypes(pa *pq.StringArray) *AiChatItemCreate {
	acic.mutation.SetMineTypes(pa)
	return acic
}

// SetSuggestQuestions sets the "suggest_questions" field.
func (acic *AiChatItemCreate) SetSuggestQuestions(pa *pq.StringArray) *AiChatItemCreate {
	acic.mutation.SetSuggestQuestions(pa)
	return acic
}

// SetHitAction sets the "hit_action" field.
func (acic *AiChatItemCreate) SetHitAction(i int64) *AiChatItemCreate {
	acic.mutation.SetHitAction(i)
	return acic
}

// SetNillableHitAction sets the "hit_action" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableHitAction(i *int64) *AiChatItemCreate {
	if i != nil {
		acic.SetHitAction(*i)
	}
	return acic
}

// SetHitResponse sets the "hit_response" field.
func (acic *AiChatItemCreate) SetHitResponse(s string) *AiChatItemCreate {
	acic.mutation.SetHitResponse(s)
	return acic
}

// SetNillableHitResponse sets the "hit_response" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableHitResponse(s *string) *AiChatItemCreate {
	if s != nil {
		acic.SetHitResponse(*s)
	}
	return acic
}

// SetHitContinueSend sets the "hit_continue_send" field.
func (acic *AiChatItemCreate) SetHitContinueSend(b bool) *AiChatItemCreate {
	acic.mutation.SetHitContinueSend(b)
	return acic
}

// SetNillableHitContinueSend sets the "hit_continue_send" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableHitContinueSend(b *bool) *AiChatItemCreate {
	if b != nil {
		acic.SetHitContinueSend(*b)
	}
	return acic
}

// SetIsInternetSearch sets the "is_internet_search" field.
func (acic *AiChatItemCreate) SetIsInternetSearch(b bool) *AiChatItemCreate {
	acic.mutation.SetIsInternetSearch(b)
	return acic
}

// SetNillableIsInternetSearch sets the "is_internet_search" field if the given value is not nil.
func (acic *AiChatItemCreate) SetNillableIsInternetSearch(b *bool) *AiChatItemCreate {
	if b != nil {
		acic.SetIsInternetSearch(*b)
	}
	return acic
}

// SetID sets the "id" field.
func (acic *AiChatItemCreate) SetID(i int64) *AiChatItemCreate {
	acic.mutation.SetID(i)
	return acic
}

// Mutation returns the AiChatItemMutation object of the builder.
func (acic *AiChatItemCreate) Mutation() *AiChatItemMutation {
	return acic.mutation
}

// Save creates the AiChatItem in the database.
func (acic *AiChatItemCreate) Save(ctx context.Context) (*AiChatItem, error) {
	if err := acic.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, acic.sqlSave, acic.mutation, acic.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (acic *AiChatItemCreate) SaveX(ctx context.Context) *AiChatItem {
	v, err := acic.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acic *AiChatItemCreate) Exec(ctx context.Context) error {
	_, err := acic.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acic *AiChatItemCreate) ExecX(ctx context.Context) {
	if err := acic.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (acic *AiChatItemCreate) defaults() error {
	if _, ok := acic.mutation.CreatedAt(); !ok {
		if aichatitem.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized aichatitem.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := aichatitem.DefaultCreatedAt()
		acic.mutation.SetCreatedAt(v)
	}
	if _, ok := acic.mutation.UpdatedAt(); !ok {
		if aichatitem.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aichatitem.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aichatitem.DefaultUpdatedAt()
		acic.mutation.SetUpdatedAt(v)
	}
	if _, ok := acic.mutation.DeletedAt(); !ok {
		v := aichatitem.DefaultDeletedAt
		acic.mutation.SetDeletedAt(v)
	}
	if _, ok := acic.mutation.AgreeStatus(); !ok {
		v := aichatitem.DefaultAgreeStatus
		acic.mutation.SetAgreeStatus(v)
	}
	if _, ok := acic.mutation.RoundID(); !ok {
		v := aichatitem.DefaultRoundID
		acic.mutation.SetRoundID(v)
	}
	if _, ok := acic.mutation.PcName(); !ok {
		v := aichatitem.DefaultPcName
		acic.mutation.SetPcName(v)
	}
	if _, ok := acic.mutation.Reason(); !ok {
		v := aichatitem.DefaultReason
		acic.mutation.SetReason(v)
	}
	if _, ok := acic.mutation.PrimaryClassification(); !ok {
		v := aichatitem.DefaultPrimaryClassification
		acic.mutation.SetPrimaryClassification(v)
	}
	if _, ok := acic.mutation.SecondaryClassification(); !ok {
		v := aichatitem.DefaultSecondaryClassification
		acic.mutation.SetSecondaryClassification(v)
	}
	if _, ok := acic.mutation.MineTypes(); !ok {
		v := aichatitem.DefaultMineTypes
		acic.mutation.SetMineTypes(v)
	}
	if _, ok := acic.mutation.SuggestQuestions(); !ok {
		v := aichatitem.DefaultSuggestQuestions
		acic.mutation.SetSuggestQuestions(v)
	}
	if _, ok := acic.mutation.HitAction(); !ok {
		v := aichatitem.DefaultHitAction
		acic.mutation.SetHitAction(v)
	}
	if _, ok := acic.mutation.HitResponse(); !ok {
		v := aichatitem.DefaultHitResponse
		acic.mutation.SetHitResponse(v)
	}
	if _, ok := acic.mutation.HitContinueSend(); !ok {
		v := aichatitem.DefaultHitContinueSend
		acic.mutation.SetHitContinueSend(v)
	}
	if _, ok := acic.mutation.IsInternetSearch(); !ok {
		v := aichatitem.DefaultIsInternetSearch
		acic.mutation.SetIsInternetSearch(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (acic *AiChatItemCreate) check() error {
	if _, ok := acic.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "AiChatItem.tenant_id"`)}
	}
	if _, ok := acic.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AiChatItem.created_at"`)}
	}
	if _, ok := acic.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AiChatItem.updated_at"`)}
	}
	if _, ok := acic.mutation.ChatID(); !ok {
		return &ValidationError{Name: "chat_id", err: errors.New(`ent: missing required field "AiChatItem.chat_id"`)}
	}
	if _, ok := acic.mutation.ObjectID(); !ok {
		return &ValidationError{Name: "object_id", err: errors.New(`ent: missing required field "AiChatItem.object_id"`)}
	}
	if _, ok := acic.mutation.ObjectType(); !ok {
		return &ValidationError{Name: "object_type", err: errors.New(`ent: missing required field "AiChatItem.object_type"`)}
	}
	if _, ok := acic.mutation.Message(); !ok {
		return &ValidationError{Name: "message", err: errors.New(`ent: missing required field "AiChatItem.message"`)}
	}
	if _, ok := acic.mutation.RefFiles(); !ok {
		return &ValidationError{Name: "ref_files", err: errors.New(`ent: missing required field "AiChatItem.ref_files"`)}
	}
	if _, ok := acic.mutation.AgreeStatus(); !ok {
		return &ValidationError{Name: "agree_status", err: errors.New(`ent: missing required field "AiChatItem.agree_status"`)}
	}
	if _, ok := acic.mutation.RoundID(); !ok {
		return &ValidationError{Name: "round_id", err: errors.New(`ent: missing required field "AiChatItem.round_id"`)}
	}
	if _, ok := acic.mutation.PcName(); !ok {
		return &ValidationError{Name: "pc_name", err: errors.New(`ent: missing required field "AiChatItem.pc_name"`)}
	}
	if _, ok := acic.mutation.Reason(); !ok {
		return &ValidationError{Name: "reason", err: errors.New(`ent: missing required field "AiChatItem.reason"`)}
	}
	if _, ok := acic.mutation.PrimaryClassification(); !ok {
		return &ValidationError{Name: "primary_classification", err: errors.New(`ent: missing required field "AiChatItem.primary_classification"`)}
	}
	if _, ok := acic.mutation.SecondaryClassification(); !ok {
		return &ValidationError{Name: "secondary_classification", err: errors.New(`ent: missing required field "AiChatItem.secondary_classification"`)}
	}
	if _, ok := acic.mutation.MineTypes(); !ok {
		return &ValidationError{Name: "mine_types", err: errors.New(`ent: missing required field "AiChatItem.mine_types"`)}
	}
	if _, ok := acic.mutation.SuggestQuestions(); !ok {
		return &ValidationError{Name: "suggest_questions", err: errors.New(`ent: missing required field "AiChatItem.suggest_questions"`)}
	}
	if _, ok := acic.mutation.HitAction(); !ok {
		return &ValidationError{Name: "hit_action", err: errors.New(`ent: missing required field "AiChatItem.hit_action"`)}
	}
	if _, ok := acic.mutation.HitResponse(); !ok {
		return &ValidationError{Name: "hit_response", err: errors.New(`ent: missing required field "AiChatItem.hit_response"`)}
	}
	if _, ok := acic.mutation.HitContinueSend(); !ok {
		return &ValidationError{Name: "hit_continue_send", err: errors.New(`ent: missing required field "AiChatItem.hit_continue_send"`)}
	}
	if _, ok := acic.mutation.IsInternetSearch(); !ok {
		return &ValidationError{Name: "is_internet_search", err: errors.New(`ent: missing required field "AiChatItem.is_internet_search"`)}
	}
	return nil
}

func (acic *AiChatItemCreate) sqlSave(ctx context.Context) (*AiChatItem, error) {
	if err := acic.check(); err != nil {
		return nil, err
	}
	_node, _spec := acic.createSpec()
	if err := sqlgraph.CreateNode(ctx, acic.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	acic.mutation.id = &_node.ID
	acic.mutation.done = true
	return _node, nil
}

func (acic *AiChatItemCreate) createSpec() (*AiChatItem, *sqlgraph.CreateSpec) {
	var (
		_node = &AiChatItem{config: acic.config}
		_spec = sqlgraph.NewCreateSpec(aichatitem.Table, sqlgraph.NewFieldSpec(aichatitem.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = acic.conflict
	if id, ok := acic.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := acic.mutation.TenantID(); ok {
		_spec.SetField(aichatitem.FieldTenantID, field.TypeInt64, value)
		_node.TenantID = value
	}
	if value, ok := acic.mutation.CreatedAt(); ok {
		_spec.SetField(aichatitem.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := acic.mutation.UpdatedAt(); ok {
		_spec.SetField(aichatitem.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := acic.mutation.DeletedAt(); ok {
		_spec.SetField(aichatitem.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := acic.mutation.ChatID(); ok {
		_spec.SetField(aichatitem.FieldChatID, field.TypeInt64, value)
		_node.ChatID = value
	}
	if value, ok := acic.mutation.ObjectID(); ok {
		_spec.SetField(aichatitem.FieldObjectID, field.TypeInt64, value)
		_node.ObjectID = value
	}
	if value, ok := acic.mutation.ObjectType(); ok {
		_spec.SetField(aichatitem.FieldObjectType, field.TypeInt64, value)
		_node.ObjectType = value
	}
	if value, ok := acic.mutation.Message(); ok {
		_spec.SetField(aichatitem.FieldMessage, field.TypeString, value)
		_node.Message = value
	}
	if value, ok := acic.mutation.RefFiles(); ok {
		_spec.SetField(aichatitem.FieldRefFiles, field.TypeString, value)
		_node.RefFiles = value
	}
	if value, ok := acic.mutation.AgreeStatus(); ok {
		_spec.SetField(aichatitem.FieldAgreeStatus, field.TypeInt8, value)
		_node.AgreeStatus = value
	}
	if value, ok := acic.mutation.RoundID(); ok {
		_spec.SetField(aichatitem.FieldRoundID, field.TypeInt64, value)
		_node.RoundID = value
	}
	if value, ok := acic.mutation.PcName(); ok {
		_spec.SetField(aichatitem.FieldPcName, field.TypeString, value)
		_node.PcName = value
	}
	if value, ok := acic.mutation.Reason(); ok {
		_spec.SetField(aichatitem.FieldReason, field.TypeString, value)
		_node.Reason = value
	}
	if value, ok := acic.mutation.PrimaryClassification(); ok {
		_spec.SetField(aichatitem.FieldPrimaryClassification, field.TypeString, value)
		_node.PrimaryClassification = value
	}
	if value, ok := acic.mutation.SecondaryClassification(); ok {
		_spec.SetField(aichatitem.FieldSecondaryClassification, field.TypeString, value)
		_node.SecondaryClassification = value
	}
	if value, ok := acic.mutation.MineTypes(); ok {
		_spec.SetField(aichatitem.FieldMineTypes, field.TypeOther, value)
		_node.MineTypes = value
	}
	if value, ok := acic.mutation.SuggestQuestions(); ok {
		_spec.SetField(aichatitem.FieldSuggestQuestions, field.TypeOther, value)
		_node.SuggestQuestions = value
	}
	if value, ok := acic.mutation.HitAction(); ok {
		_spec.SetField(aichatitem.FieldHitAction, field.TypeInt64, value)
		_node.HitAction = value
	}
	if value, ok := acic.mutation.HitResponse(); ok {
		_spec.SetField(aichatitem.FieldHitResponse, field.TypeString, value)
		_node.HitResponse = value
	}
	if value, ok := acic.mutation.HitContinueSend(); ok {
		_spec.SetField(aichatitem.FieldHitContinueSend, field.TypeBool, value)
		_node.HitContinueSend = value
	}
	if value, ok := acic.mutation.IsInternetSearch(); ok {
		_spec.SetField(aichatitem.FieldIsInternetSearch, field.TypeBool, value)
		_node.IsInternetSearch = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiChatItem.Create().
//		SetTenantID(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiChatItemUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (acic *AiChatItemCreate) OnConflict(opts ...sql.ConflictOption) *AiChatItemUpsertOne {
	acic.conflict = opts
	return &AiChatItemUpsertOne{
		create: acic,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiChatItem.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (acic *AiChatItemCreate) OnConflictColumns(columns ...string) *AiChatItemUpsertOne {
	acic.conflict = append(acic.conflict, sql.ConflictColumns(columns...))
	return &AiChatItemUpsertOne{
		create: acic,
	}
}

type (
	// AiChatItemUpsertOne is the builder for "upsert"-ing
	//  one AiChatItem node.
	AiChatItemUpsertOne struct {
		create *AiChatItemCreate
	}

	// AiChatItemUpsert is the "OnConflict" setter.
	AiChatItemUpsert struct {
		*sql.UpdateSet
	}
)

// SetTenantID sets the "tenant_id" field.
func (u *AiChatItemUpsert) SetTenantID(v int64) *AiChatItemUpsert {
	u.Set(aichatitem.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateTenantID() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldTenantID)
	return u
}

// AddTenantID adds v to the "tenant_id" field.
func (u *AiChatItemUpsert) AddTenantID(v int64) *AiChatItemUpsert {
	u.Add(aichatitem.FieldTenantID, v)
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiChatItemUpsert) SetUpdatedAt(v time.Time) *AiChatItemUpsert {
	u.Set(aichatitem.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateUpdatedAt() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiChatItemUpsert) SetDeletedAt(v time.Time) *AiChatItemUpsert {
	u.Set(aichatitem.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateDeletedAt() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiChatItemUpsert) ClearDeletedAt() *AiChatItemUpsert {
	u.SetNull(aichatitem.FieldDeletedAt)
	return u
}

// SetChatID sets the "chat_id" field.
func (u *AiChatItemUpsert) SetChatID(v int64) *AiChatItemUpsert {
	u.Set(aichatitem.FieldChatID, v)
	return u
}

// UpdateChatID sets the "chat_id" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateChatID() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldChatID)
	return u
}

// AddChatID adds v to the "chat_id" field.
func (u *AiChatItemUpsert) AddChatID(v int64) *AiChatItemUpsert {
	u.Add(aichatitem.FieldChatID, v)
	return u
}

// SetObjectID sets the "object_id" field.
func (u *AiChatItemUpsert) SetObjectID(v int64) *AiChatItemUpsert {
	u.Set(aichatitem.FieldObjectID, v)
	return u
}

// UpdateObjectID sets the "object_id" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateObjectID() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldObjectID)
	return u
}

// AddObjectID adds v to the "object_id" field.
func (u *AiChatItemUpsert) AddObjectID(v int64) *AiChatItemUpsert {
	u.Add(aichatitem.FieldObjectID, v)
	return u
}

// SetObjectType sets the "object_type" field.
func (u *AiChatItemUpsert) SetObjectType(v int64) *AiChatItemUpsert {
	u.Set(aichatitem.FieldObjectType, v)
	return u
}

// UpdateObjectType sets the "object_type" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateObjectType() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldObjectType)
	return u
}

// AddObjectType adds v to the "object_type" field.
func (u *AiChatItemUpsert) AddObjectType(v int64) *AiChatItemUpsert {
	u.Add(aichatitem.FieldObjectType, v)
	return u
}

// SetMessage sets the "message" field.
func (u *AiChatItemUpsert) SetMessage(v string) *AiChatItemUpsert {
	u.Set(aichatitem.FieldMessage, v)
	return u
}

// UpdateMessage sets the "message" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateMessage() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldMessage)
	return u
}

// SetRefFiles sets the "ref_files" field.
func (u *AiChatItemUpsert) SetRefFiles(v string) *AiChatItemUpsert {
	u.Set(aichatitem.FieldRefFiles, v)
	return u
}

// UpdateRefFiles sets the "ref_files" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateRefFiles() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldRefFiles)
	return u
}

// SetAgreeStatus sets the "agree_status" field.
func (u *AiChatItemUpsert) SetAgreeStatus(v int8) *AiChatItemUpsert {
	u.Set(aichatitem.FieldAgreeStatus, v)
	return u
}

// UpdateAgreeStatus sets the "agree_status" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateAgreeStatus() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldAgreeStatus)
	return u
}

// AddAgreeStatus adds v to the "agree_status" field.
func (u *AiChatItemUpsert) AddAgreeStatus(v int8) *AiChatItemUpsert {
	u.Add(aichatitem.FieldAgreeStatus, v)
	return u
}

// SetRoundID sets the "round_id" field.
func (u *AiChatItemUpsert) SetRoundID(v int64) *AiChatItemUpsert {
	u.Set(aichatitem.FieldRoundID, v)
	return u
}

// UpdateRoundID sets the "round_id" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateRoundID() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldRoundID)
	return u
}

// AddRoundID adds v to the "round_id" field.
func (u *AiChatItemUpsert) AddRoundID(v int64) *AiChatItemUpsert {
	u.Add(aichatitem.FieldRoundID, v)
	return u
}

// SetPcName sets the "pc_name" field.
func (u *AiChatItemUpsert) SetPcName(v string) *AiChatItemUpsert {
	u.Set(aichatitem.FieldPcName, v)
	return u
}

// UpdatePcName sets the "pc_name" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdatePcName() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldPcName)
	return u
}

// SetReason sets the "reason" field.
func (u *AiChatItemUpsert) SetReason(v string) *AiChatItemUpsert {
	u.Set(aichatitem.FieldReason, v)
	return u
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateReason() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldReason)
	return u
}

// SetPrimaryClassification sets the "primary_classification" field.
func (u *AiChatItemUpsert) SetPrimaryClassification(v string) *AiChatItemUpsert {
	u.Set(aichatitem.FieldPrimaryClassification, v)
	return u
}

// UpdatePrimaryClassification sets the "primary_classification" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdatePrimaryClassification() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldPrimaryClassification)
	return u
}

// SetSecondaryClassification sets the "secondary_classification" field.
func (u *AiChatItemUpsert) SetSecondaryClassification(v string) *AiChatItemUpsert {
	u.Set(aichatitem.FieldSecondaryClassification, v)
	return u
}

// UpdateSecondaryClassification sets the "secondary_classification" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateSecondaryClassification() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldSecondaryClassification)
	return u
}

// SetMineTypes sets the "mine_types" field.
func (u *AiChatItemUpsert) SetMineTypes(v *pq.StringArray) *AiChatItemUpsert {
	u.Set(aichatitem.FieldMineTypes, v)
	return u
}

// UpdateMineTypes sets the "mine_types" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateMineTypes() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldMineTypes)
	return u
}

// SetSuggestQuestions sets the "suggest_questions" field.
func (u *AiChatItemUpsert) SetSuggestQuestions(v *pq.StringArray) *AiChatItemUpsert {
	u.Set(aichatitem.FieldSuggestQuestions, v)
	return u
}

// UpdateSuggestQuestions sets the "suggest_questions" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateSuggestQuestions() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldSuggestQuestions)
	return u
}

// SetHitAction sets the "hit_action" field.
func (u *AiChatItemUpsert) SetHitAction(v int64) *AiChatItemUpsert {
	u.Set(aichatitem.FieldHitAction, v)
	return u
}

// UpdateHitAction sets the "hit_action" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateHitAction() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldHitAction)
	return u
}

// AddHitAction adds v to the "hit_action" field.
func (u *AiChatItemUpsert) AddHitAction(v int64) *AiChatItemUpsert {
	u.Add(aichatitem.FieldHitAction, v)
	return u
}

// SetHitResponse sets the "hit_response" field.
func (u *AiChatItemUpsert) SetHitResponse(v string) *AiChatItemUpsert {
	u.Set(aichatitem.FieldHitResponse, v)
	return u
}

// UpdateHitResponse sets the "hit_response" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateHitResponse() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldHitResponse)
	return u
}

// SetHitContinueSend sets the "hit_continue_send" field.
func (u *AiChatItemUpsert) SetHitContinueSend(v bool) *AiChatItemUpsert {
	u.Set(aichatitem.FieldHitContinueSend, v)
	return u
}

// UpdateHitContinueSend sets the "hit_continue_send" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateHitContinueSend() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldHitContinueSend)
	return u
}

// SetIsInternetSearch sets the "is_internet_search" field.
func (u *AiChatItemUpsert) SetIsInternetSearch(v bool) *AiChatItemUpsert {
	u.Set(aichatitem.FieldIsInternetSearch, v)
	return u
}

// UpdateIsInternetSearch sets the "is_internet_search" field to the value that was provided on create.
func (u *AiChatItemUpsert) UpdateIsInternetSearch() *AiChatItemUpsert {
	u.SetExcluded(aichatitem.FieldIsInternetSearch)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AiChatItem.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aichatitem.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiChatItemUpsertOne) UpdateNewValues() *AiChatItemUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(aichatitem.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(aichatitem.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiChatItem.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AiChatItemUpsertOne) Ignore() *AiChatItemUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiChatItemUpsertOne) DoNothing() *AiChatItemUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiChatItemCreate.OnConflict
// documentation for more info.
func (u *AiChatItemUpsertOne) Update(set func(*AiChatItemUpsert)) *AiChatItemUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiChatItemUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *AiChatItemUpsertOne) SetTenantID(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *AiChatItemUpsertOne) AddTenantID(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateTenantID() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiChatItemUpsertOne) SetUpdatedAt(v time.Time) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateUpdatedAt() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiChatItemUpsertOne) SetDeletedAt(v time.Time) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateDeletedAt() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiChatItemUpsertOne) ClearDeletedAt() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.ClearDeletedAt()
	})
}

// SetChatID sets the "chat_id" field.
func (u *AiChatItemUpsertOne) SetChatID(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetChatID(v)
	})
}

// AddChatID adds v to the "chat_id" field.
func (u *AiChatItemUpsertOne) AddChatID(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddChatID(v)
	})
}

// UpdateChatID sets the "chat_id" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateChatID() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateChatID()
	})
}

// SetObjectID sets the "object_id" field.
func (u *AiChatItemUpsertOne) SetObjectID(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetObjectID(v)
	})
}

// AddObjectID adds v to the "object_id" field.
func (u *AiChatItemUpsertOne) AddObjectID(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddObjectID(v)
	})
}

// UpdateObjectID sets the "object_id" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateObjectID() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateObjectID()
	})
}

// SetObjectType sets the "object_type" field.
func (u *AiChatItemUpsertOne) SetObjectType(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetObjectType(v)
	})
}

// AddObjectType adds v to the "object_type" field.
func (u *AiChatItemUpsertOne) AddObjectType(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddObjectType(v)
	})
}

// UpdateObjectType sets the "object_type" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateObjectType() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateObjectType()
	})
}

// SetMessage sets the "message" field.
func (u *AiChatItemUpsertOne) SetMessage(v string) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetMessage(v)
	})
}

// UpdateMessage sets the "message" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateMessage() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateMessage()
	})
}

// SetRefFiles sets the "ref_files" field.
func (u *AiChatItemUpsertOne) SetRefFiles(v string) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetRefFiles(v)
	})
}

// UpdateRefFiles sets the "ref_files" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateRefFiles() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateRefFiles()
	})
}

// SetAgreeStatus sets the "agree_status" field.
func (u *AiChatItemUpsertOne) SetAgreeStatus(v int8) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetAgreeStatus(v)
	})
}

// AddAgreeStatus adds v to the "agree_status" field.
func (u *AiChatItemUpsertOne) AddAgreeStatus(v int8) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddAgreeStatus(v)
	})
}

// UpdateAgreeStatus sets the "agree_status" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateAgreeStatus() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateAgreeStatus()
	})
}

// SetRoundID sets the "round_id" field.
func (u *AiChatItemUpsertOne) SetRoundID(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetRoundID(v)
	})
}

// AddRoundID adds v to the "round_id" field.
func (u *AiChatItemUpsertOne) AddRoundID(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddRoundID(v)
	})
}

// UpdateRoundID sets the "round_id" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateRoundID() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateRoundID()
	})
}

// SetPcName sets the "pc_name" field.
func (u *AiChatItemUpsertOne) SetPcName(v string) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetPcName(v)
	})
}

// UpdatePcName sets the "pc_name" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdatePcName() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdatePcName()
	})
}

// SetReason sets the "reason" field.
func (u *AiChatItemUpsertOne) SetReason(v string) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetReason(v)
	})
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateReason() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateReason()
	})
}

// SetPrimaryClassification sets the "primary_classification" field.
func (u *AiChatItemUpsertOne) SetPrimaryClassification(v string) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetPrimaryClassification(v)
	})
}

// UpdatePrimaryClassification sets the "primary_classification" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdatePrimaryClassification() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdatePrimaryClassification()
	})
}

// SetSecondaryClassification sets the "secondary_classification" field.
func (u *AiChatItemUpsertOne) SetSecondaryClassification(v string) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetSecondaryClassification(v)
	})
}

// UpdateSecondaryClassification sets the "secondary_classification" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateSecondaryClassification() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateSecondaryClassification()
	})
}

// SetMineTypes sets the "mine_types" field.
func (u *AiChatItemUpsertOne) SetMineTypes(v *pq.StringArray) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetMineTypes(v)
	})
}

// UpdateMineTypes sets the "mine_types" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateMineTypes() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateMineTypes()
	})
}

// SetSuggestQuestions sets the "suggest_questions" field.
func (u *AiChatItemUpsertOne) SetSuggestQuestions(v *pq.StringArray) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetSuggestQuestions(v)
	})
}

// UpdateSuggestQuestions sets the "suggest_questions" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateSuggestQuestions() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateSuggestQuestions()
	})
}

// SetHitAction sets the "hit_action" field.
func (u *AiChatItemUpsertOne) SetHitAction(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetHitAction(v)
	})
}

// AddHitAction adds v to the "hit_action" field.
func (u *AiChatItemUpsertOne) AddHitAction(v int64) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddHitAction(v)
	})
}

// UpdateHitAction sets the "hit_action" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateHitAction() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateHitAction()
	})
}

// SetHitResponse sets the "hit_response" field.
func (u *AiChatItemUpsertOne) SetHitResponse(v string) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetHitResponse(v)
	})
}

// UpdateHitResponse sets the "hit_response" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateHitResponse() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateHitResponse()
	})
}

// SetHitContinueSend sets the "hit_continue_send" field.
func (u *AiChatItemUpsertOne) SetHitContinueSend(v bool) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetHitContinueSend(v)
	})
}

// UpdateHitContinueSend sets the "hit_continue_send" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateHitContinueSend() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateHitContinueSend()
	})
}

// SetIsInternetSearch sets the "is_internet_search" field.
func (u *AiChatItemUpsertOne) SetIsInternetSearch(v bool) *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetIsInternetSearch(v)
	})
}

// UpdateIsInternetSearch sets the "is_internet_search" field to the value that was provided on create.
func (u *AiChatItemUpsertOne) UpdateIsInternetSearch() *AiChatItemUpsertOne {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateIsInternetSearch()
	})
}

// Exec executes the query.
func (u *AiChatItemUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiChatItemCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiChatItemUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AiChatItemUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AiChatItemUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AiChatItemCreateBulk is the builder for creating many AiChatItem entities in bulk.
type AiChatItemCreateBulk struct {
	config
	err      error
	builders []*AiChatItemCreate
	conflict []sql.ConflictOption
}

// Save creates the AiChatItem entities in the database.
func (acicb *AiChatItemCreateBulk) Save(ctx context.Context) ([]*AiChatItem, error) {
	if acicb.err != nil {
		return nil, acicb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(acicb.builders))
	nodes := make([]*AiChatItem, len(acicb.builders))
	mutators := make([]Mutator, len(acicb.builders))
	for i := range acicb.builders {
		func(i int, root context.Context) {
			builder := acicb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AiChatItemMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, acicb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = acicb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, acicb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, acicb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (acicb *AiChatItemCreateBulk) SaveX(ctx context.Context) []*AiChatItem {
	v, err := acicb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acicb *AiChatItemCreateBulk) Exec(ctx context.Context) error {
	_, err := acicb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acicb *AiChatItemCreateBulk) ExecX(ctx context.Context) {
	if err := acicb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiChatItem.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiChatItemUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (acicb *AiChatItemCreateBulk) OnConflict(opts ...sql.ConflictOption) *AiChatItemUpsertBulk {
	acicb.conflict = opts
	return &AiChatItemUpsertBulk{
		create: acicb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiChatItem.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (acicb *AiChatItemCreateBulk) OnConflictColumns(columns ...string) *AiChatItemUpsertBulk {
	acicb.conflict = append(acicb.conflict, sql.ConflictColumns(columns...))
	return &AiChatItemUpsertBulk{
		create: acicb,
	}
}

// AiChatItemUpsertBulk is the builder for "upsert"-ing
// a bulk of AiChatItem nodes.
type AiChatItemUpsertBulk struct {
	create *AiChatItemCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AiChatItem.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aichatitem.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiChatItemUpsertBulk) UpdateNewValues() *AiChatItemUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(aichatitem.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(aichatitem.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiChatItem.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AiChatItemUpsertBulk) Ignore() *AiChatItemUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiChatItemUpsertBulk) DoNothing() *AiChatItemUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiChatItemCreateBulk.OnConflict
// documentation for more info.
func (u *AiChatItemUpsertBulk) Update(set func(*AiChatItemUpsert)) *AiChatItemUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiChatItemUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *AiChatItemUpsertBulk) SetTenantID(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *AiChatItemUpsertBulk) AddTenantID(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateTenantID() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiChatItemUpsertBulk) SetUpdatedAt(v time.Time) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateUpdatedAt() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiChatItemUpsertBulk) SetDeletedAt(v time.Time) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateDeletedAt() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiChatItemUpsertBulk) ClearDeletedAt() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.ClearDeletedAt()
	})
}

// SetChatID sets the "chat_id" field.
func (u *AiChatItemUpsertBulk) SetChatID(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetChatID(v)
	})
}

// AddChatID adds v to the "chat_id" field.
func (u *AiChatItemUpsertBulk) AddChatID(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddChatID(v)
	})
}

// UpdateChatID sets the "chat_id" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateChatID() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateChatID()
	})
}

// SetObjectID sets the "object_id" field.
func (u *AiChatItemUpsertBulk) SetObjectID(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetObjectID(v)
	})
}

// AddObjectID adds v to the "object_id" field.
func (u *AiChatItemUpsertBulk) AddObjectID(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddObjectID(v)
	})
}

// UpdateObjectID sets the "object_id" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateObjectID() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateObjectID()
	})
}

// SetObjectType sets the "object_type" field.
func (u *AiChatItemUpsertBulk) SetObjectType(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetObjectType(v)
	})
}

// AddObjectType adds v to the "object_type" field.
func (u *AiChatItemUpsertBulk) AddObjectType(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddObjectType(v)
	})
}

// UpdateObjectType sets the "object_type" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateObjectType() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateObjectType()
	})
}

// SetMessage sets the "message" field.
func (u *AiChatItemUpsertBulk) SetMessage(v string) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetMessage(v)
	})
}

// UpdateMessage sets the "message" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateMessage() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateMessage()
	})
}

// SetRefFiles sets the "ref_files" field.
func (u *AiChatItemUpsertBulk) SetRefFiles(v string) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetRefFiles(v)
	})
}

// UpdateRefFiles sets the "ref_files" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateRefFiles() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateRefFiles()
	})
}

// SetAgreeStatus sets the "agree_status" field.
func (u *AiChatItemUpsertBulk) SetAgreeStatus(v int8) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetAgreeStatus(v)
	})
}

// AddAgreeStatus adds v to the "agree_status" field.
func (u *AiChatItemUpsertBulk) AddAgreeStatus(v int8) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddAgreeStatus(v)
	})
}

// UpdateAgreeStatus sets the "agree_status" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateAgreeStatus() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateAgreeStatus()
	})
}

// SetRoundID sets the "round_id" field.
func (u *AiChatItemUpsertBulk) SetRoundID(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetRoundID(v)
	})
}

// AddRoundID adds v to the "round_id" field.
func (u *AiChatItemUpsertBulk) AddRoundID(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddRoundID(v)
	})
}

// UpdateRoundID sets the "round_id" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateRoundID() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateRoundID()
	})
}

// SetPcName sets the "pc_name" field.
func (u *AiChatItemUpsertBulk) SetPcName(v string) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetPcName(v)
	})
}

// UpdatePcName sets the "pc_name" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdatePcName() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdatePcName()
	})
}

// SetReason sets the "reason" field.
func (u *AiChatItemUpsertBulk) SetReason(v string) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetReason(v)
	})
}

// UpdateReason sets the "reason" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateReason() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateReason()
	})
}

// SetPrimaryClassification sets the "primary_classification" field.
func (u *AiChatItemUpsertBulk) SetPrimaryClassification(v string) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetPrimaryClassification(v)
	})
}

// UpdatePrimaryClassification sets the "primary_classification" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdatePrimaryClassification() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdatePrimaryClassification()
	})
}

// SetSecondaryClassification sets the "secondary_classification" field.
func (u *AiChatItemUpsertBulk) SetSecondaryClassification(v string) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetSecondaryClassification(v)
	})
}

// UpdateSecondaryClassification sets the "secondary_classification" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateSecondaryClassification() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateSecondaryClassification()
	})
}

// SetMineTypes sets the "mine_types" field.
func (u *AiChatItemUpsertBulk) SetMineTypes(v *pq.StringArray) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetMineTypes(v)
	})
}

// UpdateMineTypes sets the "mine_types" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateMineTypes() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateMineTypes()
	})
}

// SetSuggestQuestions sets the "suggest_questions" field.
func (u *AiChatItemUpsertBulk) SetSuggestQuestions(v *pq.StringArray) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetSuggestQuestions(v)
	})
}

// UpdateSuggestQuestions sets the "suggest_questions" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateSuggestQuestions() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateSuggestQuestions()
	})
}

// SetHitAction sets the "hit_action" field.
func (u *AiChatItemUpsertBulk) SetHitAction(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetHitAction(v)
	})
}

// AddHitAction adds v to the "hit_action" field.
func (u *AiChatItemUpsertBulk) AddHitAction(v int64) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.AddHitAction(v)
	})
}

// UpdateHitAction sets the "hit_action" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateHitAction() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateHitAction()
	})
}

// SetHitResponse sets the "hit_response" field.
func (u *AiChatItemUpsertBulk) SetHitResponse(v string) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetHitResponse(v)
	})
}

// UpdateHitResponse sets the "hit_response" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateHitResponse() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateHitResponse()
	})
}

// SetHitContinueSend sets the "hit_continue_send" field.
func (u *AiChatItemUpsertBulk) SetHitContinueSend(v bool) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetHitContinueSend(v)
	})
}

// UpdateHitContinueSend sets the "hit_continue_send" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateHitContinueSend() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateHitContinueSend()
	})
}

// SetIsInternetSearch sets the "is_internet_search" field.
func (u *AiChatItemUpsertBulk) SetIsInternetSearch(v bool) *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.SetIsInternetSearch(v)
	})
}

// UpdateIsInternetSearch sets the "is_internet_search" field to the value that was provided on create.
func (u *AiChatItemUpsertBulk) UpdateIsInternetSearch() *AiChatItemUpsertBulk {
	return u.Update(func(s *AiChatItemUpsert) {
		s.UpdateIsInternetSearch()
	})
}

// Exec executes the query.
func (u *AiChatItemUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AiChatItemCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiChatItemCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiChatItemUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

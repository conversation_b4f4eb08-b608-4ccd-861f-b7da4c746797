// Code generated by ent, DO NOT EDIT.

package aimodel

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldDeletedAt, v))
}

// ModelName applies equality check predicate on the "model_name" field. It's identical to ModelNameEQ.
func ModelName(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldModelName, v))
}

// Model applies equality check predicate on the "model" field. It's identical to ModelEQ.
func Model(v int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldModel, v))
}

// APIKey applies equality check predicate on the "api_key" field. It's identical to APIKeyEQ.
func APIKey(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldAPIKey, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.AiModel {
	return predicate.AiModel(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.AiModel {
	return predicate.AiModel(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.AiModel {
	return predicate.AiModel(sql.FieldNotNull(FieldDeletedAt))
}

// ModelNameEQ applies the EQ predicate on the "model_name" field.
func ModelNameEQ(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldModelName, v))
}

// ModelNameNEQ applies the NEQ predicate on the "model_name" field.
func ModelNameNEQ(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldNEQ(FieldModelName, v))
}

// ModelNameIn applies the In predicate on the "model_name" field.
func ModelNameIn(vs ...string) predicate.AiModel {
	return predicate.AiModel(sql.FieldIn(FieldModelName, vs...))
}

// ModelNameNotIn applies the NotIn predicate on the "model_name" field.
func ModelNameNotIn(vs ...string) predicate.AiModel {
	return predicate.AiModel(sql.FieldNotIn(FieldModelName, vs...))
}

// ModelNameGT applies the GT predicate on the "model_name" field.
func ModelNameGT(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldGT(FieldModelName, v))
}

// ModelNameGTE applies the GTE predicate on the "model_name" field.
func ModelNameGTE(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldGTE(FieldModelName, v))
}

// ModelNameLT applies the LT predicate on the "model_name" field.
func ModelNameLT(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldLT(FieldModelName, v))
}

// ModelNameLTE applies the LTE predicate on the "model_name" field.
func ModelNameLTE(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldLTE(FieldModelName, v))
}

// ModelNameContains applies the Contains predicate on the "model_name" field.
func ModelNameContains(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldContains(FieldModelName, v))
}

// ModelNameHasPrefix applies the HasPrefix predicate on the "model_name" field.
func ModelNameHasPrefix(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldHasPrefix(FieldModelName, v))
}

// ModelNameHasSuffix applies the HasSuffix predicate on the "model_name" field.
func ModelNameHasSuffix(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldHasSuffix(FieldModelName, v))
}

// ModelNameEqualFold applies the EqualFold predicate on the "model_name" field.
func ModelNameEqualFold(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldEqualFold(FieldModelName, v))
}

// ModelNameContainsFold applies the ContainsFold predicate on the "model_name" field.
func ModelNameContainsFold(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldContainsFold(FieldModelName, v))
}

// ModelEQ applies the EQ predicate on the "model" field.
func ModelEQ(v int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldModel, v))
}

// ModelNEQ applies the NEQ predicate on the "model" field.
func ModelNEQ(v int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldNEQ(FieldModel, v))
}

// ModelIn applies the In predicate on the "model" field.
func ModelIn(vs ...int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldIn(FieldModel, vs...))
}

// ModelNotIn applies the NotIn predicate on the "model" field.
func ModelNotIn(vs ...int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldNotIn(FieldModel, vs...))
}

// ModelGT applies the GT predicate on the "model" field.
func ModelGT(v int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldGT(FieldModel, v))
}

// ModelGTE applies the GTE predicate on the "model" field.
func ModelGTE(v int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldGTE(FieldModel, v))
}

// ModelLT applies the LT predicate on the "model" field.
func ModelLT(v int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldLT(FieldModel, v))
}

// ModelLTE applies the LTE predicate on the "model" field.
func ModelLTE(v int64) predicate.AiModel {
	return predicate.AiModel(sql.FieldLTE(FieldModel, v))
}

// APIKeyEQ applies the EQ predicate on the "api_key" field.
func APIKeyEQ(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldEQ(FieldAPIKey, v))
}

// APIKeyNEQ applies the NEQ predicate on the "api_key" field.
func APIKeyNEQ(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldNEQ(FieldAPIKey, v))
}

// APIKeyIn applies the In predicate on the "api_key" field.
func APIKeyIn(vs ...string) predicate.AiModel {
	return predicate.AiModel(sql.FieldIn(FieldAPIKey, vs...))
}

// APIKeyNotIn applies the NotIn predicate on the "api_key" field.
func APIKeyNotIn(vs ...string) predicate.AiModel {
	return predicate.AiModel(sql.FieldNotIn(FieldAPIKey, vs...))
}

// APIKeyGT applies the GT predicate on the "api_key" field.
func APIKeyGT(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldGT(FieldAPIKey, v))
}

// APIKeyGTE applies the GTE predicate on the "api_key" field.
func APIKeyGTE(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldGTE(FieldAPIKey, v))
}

// APIKeyLT applies the LT predicate on the "api_key" field.
func APIKeyLT(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldLT(FieldAPIKey, v))
}

// APIKeyLTE applies the LTE predicate on the "api_key" field.
func APIKeyLTE(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldLTE(FieldAPIKey, v))
}

// APIKeyContains applies the Contains predicate on the "api_key" field.
func APIKeyContains(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldContains(FieldAPIKey, v))
}

// APIKeyHasPrefix applies the HasPrefix predicate on the "api_key" field.
func APIKeyHasPrefix(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldHasPrefix(FieldAPIKey, v))
}

// APIKeyHasSuffix applies the HasSuffix predicate on the "api_key" field.
func APIKeyHasSuffix(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldHasSuffix(FieldAPIKey, v))
}

// APIKeyEqualFold applies the EqualFold predicate on the "api_key" field.
func APIKeyEqualFold(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldEqualFold(FieldAPIKey, v))
}

// APIKeyContainsFold applies the ContainsFold predicate on the "api_key" field.
func APIKeyContainsFold(v string) predicate.AiModel {
	return predicate.AiModel(sql.FieldContainsFold(FieldAPIKey, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AiModel) predicate.AiModel {
	return predicate.AiModel(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AiModel) predicate.AiModel {
	return predicate.AiModel(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AiModel) predicate.AiModel {
	return predicate.AiModel(sql.NotPredicates(p))
}

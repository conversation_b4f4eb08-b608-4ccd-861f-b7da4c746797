// Code generated by ent, DO NOT EDIT.

package aiagentsecuritylog

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldDeletedAt, v))
}

// RiskLevel applies equality check predicate on the "risk_level" field. It's identical to RiskLevelEQ.
func RiskLevel(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldRiskLevel, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldUserID, v))
}

// UserName applies equality check predicate on the "user_name" field. It's identical to UserNameEQ.
func UserName(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldUserName, v))
}

// DeptID applies equality check predicate on the "dept_id" field. It's identical to DeptIDEQ.
func DeptID(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldDeptID, v))
}

// DeptName applies equality check predicate on the "dept_name" field. It's identical to DeptNameEQ.
func DeptName(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldDeptName, v))
}

// PcName applies equality check predicate on the "pc_name" field. It's identical to PcNameEQ.
func PcName(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldPcName, v))
}

// AgentID applies equality check predicate on the "agent_id" field. It's identical to AgentIDEQ.
func AgentID(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldAgentID, v))
}

// AgentName applies equality check predicate on the "agent_name" field. It's identical to AgentNameEQ.
func AgentName(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldAgentName, v))
}

// AgentDescription applies equality check predicate on the "agent_description" field. It's identical to AgentDescriptionEQ.
func AgentDescription(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldAgentDescription, v))
}

// HitAction applies equality check predicate on the "hit_action" field. It's identical to HitActionEQ.
func HitAction(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldHitAction, v))
}

// Question applies equality check predicate on the "question" field. It's identical to QuestionEQ.
func Question(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldQuestion, v))
}

// ActionCategory applies equality check predicate on the "action_category" field. It's identical to ActionCategoryEQ.
func ActionCategory(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldActionCategory, v))
}

// UploadedFiles applies equality check predicate on the "uploaded_files" field. It's identical to UploadedFilesEQ.
func UploadedFiles(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldUploadedFiles, v))
}

// HitPolicies applies equality check predicate on the "hit_policies" field. It's identical to HitPoliciesEQ.
func HitPolicies(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldHitPolicies, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotNull(FieldDeletedAt))
}

// RiskLevelEQ applies the EQ predicate on the "risk_level" field.
func RiskLevelEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldRiskLevel, v))
}

// RiskLevelNEQ applies the NEQ predicate on the "risk_level" field.
func RiskLevelNEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldRiskLevel, v))
}

// RiskLevelIn applies the In predicate on the "risk_level" field.
func RiskLevelIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldRiskLevel, vs...))
}

// RiskLevelNotIn applies the NotIn predicate on the "risk_level" field.
func RiskLevelNotIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldRiskLevel, vs...))
}

// RiskLevelGT applies the GT predicate on the "risk_level" field.
func RiskLevelGT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldRiskLevel, v))
}

// RiskLevelGTE applies the GTE predicate on the "risk_level" field.
func RiskLevelGTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldRiskLevel, v))
}

// RiskLevelLT applies the LT predicate on the "risk_level" field.
func RiskLevelLT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldRiskLevel, v))
}

// RiskLevelLTE applies the LTE predicate on the "risk_level" field.
func RiskLevelLTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldRiskLevel, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldUserID, v))
}

// UserNameEQ applies the EQ predicate on the "user_name" field.
func UserNameEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldUserName, v))
}

// UserNameNEQ applies the NEQ predicate on the "user_name" field.
func UserNameNEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldUserName, v))
}

// UserNameIn applies the In predicate on the "user_name" field.
func UserNameIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldUserName, vs...))
}

// UserNameNotIn applies the NotIn predicate on the "user_name" field.
func UserNameNotIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldUserName, vs...))
}

// UserNameGT applies the GT predicate on the "user_name" field.
func UserNameGT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldUserName, v))
}

// UserNameGTE applies the GTE predicate on the "user_name" field.
func UserNameGTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldUserName, v))
}

// UserNameLT applies the LT predicate on the "user_name" field.
func UserNameLT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldUserName, v))
}

// UserNameLTE applies the LTE predicate on the "user_name" field.
func UserNameLTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldUserName, v))
}

// UserNameContains applies the Contains predicate on the "user_name" field.
func UserNameContains(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContains(FieldUserName, v))
}

// UserNameHasPrefix applies the HasPrefix predicate on the "user_name" field.
func UserNameHasPrefix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasPrefix(FieldUserName, v))
}

// UserNameHasSuffix applies the HasSuffix predicate on the "user_name" field.
func UserNameHasSuffix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasSuffix(FieldUserName, v))
}

// UserNameEqualFold applies the EqualFold predicate on the "user_name" field.
func UserNameEqualFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEqualFold(FieldUserName, v))
}

// UserNameContainsFold applies the ContainsFold predicate on the "user_name" field.
func UserNameContainsFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContainsFold(FieldUserName, v))
}

// DeptIDEQ applies the EQ predicate on the "dept_id" field.
func DeptIDEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldDeptID, v))
}

// DeptIDNEQ applies the NEQ predicate on the "dept_id" field.
func DeptIDNEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldDeptID, v))
}

// DeptIDIn applies the In predicate on the "dept_id" field.
func DeptIDIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldDeptID, vs...))
}

// DeptIDNotIn applies the NotIn predicate on the "dept_id" field.
func DeptIDNotIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldDeptID, vs...))
}

// DeptIDGT applies the GT predicate on the "dept_id" field.
func DeptIDGT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldDeptID, v))
}

// DeptIDGTE applies the GTE predicate on the "dept_id" field.
func DeptIDGTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldDeptID, v))
}

// DeptIDLT applies the LT predicate on the "dept_id" field.
func DeptIDLT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldDeptID, v))
}

// DeptIDLTE applies the LTE predicate on the "dept_id" field.
func DeptIDLTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldDeptID, v))
}

// DeptNameEQ applies the EQ predicate on the "dept_name" field.
func DeptNameEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldDeptName, v))
}

// DeptNameNEQ applies the NEQ predicate on the "dept_name" field.
func DeptNameNEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldDeptName, v))
}

// DeptNameIn applies the In predicate on the "dept_name" field.
func DeptNameIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldDeptName, vs...))
}

// DeptNameNotIn applies the NotIn predicate on the "dept_name" field.
func DeptNameNotIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldDeptName, vs...))
}

// DeptNameGT applies the GT predicate on the "dept_name" field.
func DeptNameGT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldDeptName, v))
}

// DeptNameGTE applies the GTE predicate on the "dept_name" field.
func DeptNameGTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldDeptName, v))
}

// DeptNameLT applies the LT predicate on the "dept_name" field.
func DeptNameLT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldDeptName, v))
}

// DeptNameLTE applies the LTE predicate on the "dept_name" field.
func DeptNameLTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldDeptName, v))
}

// DeptNameContains applies the Contains predicate on the "dept_name" field.
func DeptNameContains(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContains(FieldDeptName, v))
}

// DeptNameHasPrefix applies the HasPrefix predicate on the "dept_name" field.
func DeptNameHasPrefix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasPrefix(FieldDeptName, v))
}

// DeptNameHasSuffix applies the HasSuffix predicate on the "dept_name" field.
func DeptNameHasSuffix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasSuffix(FieldDeptName, v))
}

// DeptNameEqualFold applies the EqualFold predicate on the "dept_name" field.
func DeptNameEqualFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEqualFold(FieldDeptName, v))
}

// DeptNameContainsFold applies the ContainsFold predicate on the "dept_name" field.
func DeptNameContainsFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContainsFold(FieldDeptName, v))
}

// PcNameEQ applies the EQ predicate on the "pc_name" field.
func PcNameEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldPcName, v))
}

// PcNameNEQ applies the NEQ predicate on the "pc_name" field.
func PcNameNEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldPcName, v))
}

// PcNameIn applies the In predicate on the "pc_name" field.
func PcNameIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldPcName, vs...))
}

// PcNameNotIn applies the NotIn predicate on the "pc_name" field.
func PcNameNotIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldPcName, vs...))
}

// PcNameGT applies the GT predicate on the "pc_name" field.
func PcNameGT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldPcName, v))
}

// PcNameGTE applies the GTE predicate on the "pc_name" field.
func PcNameGTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldPcName, v))
}

// PcNameLT applies the LT predicate on the "pc_name" field.
func PcNameLT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldPcName, v))
}

// PcNameLTE applies the LTE predicate on the "pc_name" field.
func PcNameLTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldPcName, v))
}

// PcNameContains applies the Contains predicate on the "pc_name" field.
func PcNameContains(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContains(FieldPcName, v))
}

// PcNameHasPrefix applies the HasPrefix predicate on the "pc_name" field.
func PcNameHasPrefix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasPrefix(FieldPcName, v))
}

// PcNameHasSuffix applies the HasSuffix predicate on the "pc_name" field.
func PcNameHasSuffix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasSuffix(FieldPcName, v))
}

// PcNameEqualFold applies the EqualFold predicate on the "pc_name" field.
func PcNameEqualFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEqualFold(FieldPcName, v))
}

// PcNameContainsFold applies the ContainsFold predicate on the "pc_name" field.
func PcNameContainsFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContainsFold(FieldPcName, v))
}

// AgentIDEQ applies the EQ predicate on the "agent_id" field.
func AgentIDEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldAgentID, v))
}

// AgentIDNEQ applies the NEQ predicate on the "agent_id" field.
func AgentIDNEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldAgentID, v))
}

// AgentIDIn applies the In predicate on the "agent_id" field.
func AgentIDIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldAgentID, vs...))
}

// AgentIDNotIn applies the NotIn predicate on the "agent_id" field.
func AgentIDNotIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldAgentID, vs...))
}

// AgentIDGT applies the GT predicate on the "agent_id" field.
func AgentIDGT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldAgentID, v))
}

// AgentIDGTE applies the GTE predicate on the "agent_id" field.
func AgentIDGTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldAgentID, v))
}

// AgentIDLT applies the LT predicate on the "agent_id" field.
func AgentIDLT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldAgentID, v))
}

// AgentIDLTE applies the LTE predicate on the "agent_id" field.
func AgentIDLTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldAgentID, v))
}

// AgentNameEQ applies the EQ predicate on the "agent_name" field.
func AgentNameEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldAgentName, v))
}

// AgentNameNEQ applies the NEQ predicate on the "agent_name" field.
func AgentNameNEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldAgentName, v))
}

// AgentNameIn applies the In predicate on the "agent_name" field.
func AgentNameIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldAgentName, vs...))
}

// AgentNameNotIn applies the NotIn predicate on the "agent_name" field.
func AgentNameNotIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldAgentName, vs...))
}

// AgentNameGT applies the GT predicate on the "agent_name" field.
func AgentNameGT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldAgentName, v))
}

// AgentNameGTE applies the GTE predicate on the "agent_name" field.
func AgentNameGTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldAgentName, v))
}

// AgentNameLT applies the LT predicate on the "agent_name" field.
func AgentNameLT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldAgentName, v))
}

// AgentNameLTE applies the LTE predicate on the "agent_name" field.
func AgentNameLTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldAgentName, v))
}

// AgentNameContains applies the Contains predicate on the "agent_name" field.
func AgentNameContains(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContains(FieldAgentName, v))
}

// AgentNameHasPrefix applies the HasPrefix predicate on the "agent_name" field.
func AgentNameHasPrefix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasPrefix(FieldAgentName, v))
}

// AgentNameHasSuffix applies the HasSuffix predicate on the "agent_name" field.
func AgentNameHasSuffix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasSuffix(FieldAgentName, v))
}

// AgentNameEqualFold applies the EqualFold predicate on the "agent_name" field.
func AgentNameEqualFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEqualFold(FieldAgentName, v))
}

// AgentNameContainsFold applies the ContainsFold predicate on the "agent_name" field.
func AgentNameContainsFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContainsFold(FieldAgentName, v))
}

// AgentDescriptionEQ applies the EQ predicate on the "agent_description" field.
func AgentDescriptionEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldAgentDescription, v))
}

// AgentDescriptionNEQ applies the NEQ predicate on the "agent_description" field.
func AgentDescriptionNEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldAgentDescription, v))
}

// AgentDescriptionIn applies the In predicate on the "agent_description" field.
func AgentDescriptionIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldAgentDescription, vs...))
}

// AgentDescriptionNotIn applies the NotIn predicate on the "agent_description" field.
func AgentDescriptionNotIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldAgentDescription, vs...))
}

// AgentDescriptionGT applies the GT predicate on the "agent_description" field.
func AgentDescriptionGT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldAgentDescription, v))
}

// AgentDescriptionGTE applies the GTE predicate on the "agent_description" field.
func AgentDescriptionGTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldAgentDescription, v))
}

// AgentDescriptionLT applies the LT predicate on the "agent_description" field.
func AgentDescriptionLT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldAgentDescription, v))
}

// AgentDescriptionLTE applies the LTE predicate on the "agent_description" field.
func AgentDescriptionLTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldAgentDescription, v))
}

// AgentDescriptionContains applies the Contains predicate on the "agent_description" field.
func AgentDescriptionContains(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContains(FieldAgentDescription, v))
}

// AgentDescriptionHasPrefix applies the HasPrefix predicate on the "agent_description" field.
func AgentDescriptionHasPrefix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasPrefix(FieldAgentDescription, v))
}

// AgentDescriptionHasSuffix applies the HasSuffix predicate on the "agent_description" field.
func AgentDescriptionHasSuffix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasSuffix(FieldAgentDescription, v))
}

// AgentDescriptionEqualFold applies the EqualFold predicate on the "agent_description" field.
func AgentDescriptionEqualFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEqualFold(FieldAgentDescription, v))
}

// AgentDescriptionContainsFold applies the ContainsFold predicate on the "agent_description" field.
func AgentDescriptionContainsFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContainsFold(FieldAgentDescription, v))
}

// HitActionEQ applies the EQ predicate on the "hit_action" field.
func HitActionEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldHitAction, v))
}

// HitActionNEQ applies the NEQ predicate on the "hit_action" field.
func HitActionNEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldHitAction, v))
}

// HitActionIn applies the In predicate on the "hit_action" field.
func HitActionIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldHitAction, vs...))
}

// HitActionNotIn applies the NotIn predicate on the "hit_action" field.
func HitActionNotIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldHitAction, vs...))
}

// HitActionGT applies the GT predicate on the "hit_action" field.
func HitActionGT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldHitAction, v))
}

// HitActionGTE applies the GTE predicate on the "hit_action" field.
func HitActionGTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldHitAction, v))
}

// HitActionLT applies the LT predicate on the "hit_action" field.
func HitActionLT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldHitAction, v))
}

// HitActionLTE applies the LTE predicate on the "hit_action" field.
func HitActionLTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldHitAction, v))
}

// QuestionEQ applies the EQ predicate on the "question" field.
func QuestionEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldQuestion, v))
}

// QuestionNEQ applies the NEQ predicate on the "question" field.
func QuestionNEQ(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldQuestion, v))
}

// QuestionIn applies the In predicate on the "question" field.
func QuestionIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldQuestion, vs...))
}

// QuestionNotIn applies the NotIn predicate on the "question" field.
func QuestionNotIn(vs ...string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldQuestion, vs...))
}

// QuestionGT applies the GT predicate on the "question" field.
func QuestionGT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldQuestion, v))
}

// QuestionGTE applies the GTE predicate on the "question" field.
func QuestionGTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldQuestion, v))
}

// QuestionLT applies the LT predicate on the "question" field.
func QuestionLT(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldQuestion, v))
}

// QuestionLTE applies the LTE predicate on the "question" field.
func QuestionLTE(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldQuestion, v))
}

// QuestionContains applies the Contains predicate on the "question" field.
func QuestionContains(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContains(FieldQuestion, v))
}

// QuestionHasPrefix applies the HasPrefix predicate on the "question" field.
func QuestionHasPrefix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasPrefix(FieldQuestion, v))
}

// QuestionHasSuffix applies the HasSuffix predicate on the "question" field.
func QuestionHasSuffix(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldHasSuffix(FieldQuestion, v))
}

// QuestionEqualFold applies the EqualFold predicate on the "question" field.
func QuestionEqualFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEqualFold(FieldQuestion, v))
}

// QuestionContainsFold applies the ContainsFold predicate on the "question" field.
func QuestionContainsFold(v string) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldContainsFold(FieldQuestion, v))
}

// ActionCategoryEQ applies the EQ predicate on the "action_category" field.
func ActionCategoryEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldActionCategory, v))
}

// ActionCategoryNEQ applies the NEQ predicate on the "action_category" field.
func ActionCategoryNEQ(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldActionCategory, v))
}

// ActionCategoryIn applies the In predicate on the "action_category" field.
func ActionCategoryIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldActionCategory, vs...))
}

// ActionCategoryNotIn applies the NotIn predicate on the "action_category" field.
func ActionCategoryNotIn(vs ...int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldActionCategory, vs...))
}

// ActionCategoryGT applies the GT predicate on the "action_category" field.
func ActionCategoryGT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldActionCategory, v))
}

// ActionCategoryGTE applies the GTE predicate on the "action_category" field.
func ActionCategoryGTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldActionCategory, v))
}

// ActionCategoryLT applies the LT predicate on the "action_category" field.
func ActionCategoryLT(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldActionCategory, v))
}

// ActionCategoryLTE applies the LTE predicate on the "action_category" field.
func ActionCategoryLTE(v int64) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldActionCategory, v))
}

// UploadedFilesEQ applies the EQ predicate on the "uploaded_files" field.
func UploadedFilesEQ(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldUploadedFiles, v))
}

// UploadedFilesNEQ applies the NEQ predicate on the "uploaded_files" field.
func UploadedFilesNEQ(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldUploadedFiles, v))
}

// UploadedFilesIn applies the In predicate on the "uploaded_files" field.
func UploadedFilesIn(vs ...*pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldUploadedFiles, vs...))
}

// UploadedFilesNotIn applies the NotIn predicate on the "uploaded_files" field.
func UploadedFilesNotIn(vs ...*pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldUploadedFiles, vs...))
}

// UploadedFilesGT applies the GT predicate on the "uploaded_files" field.
func UploadedFilesGT(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldUploadedFiles, v))
}

// UploadedFilesGTE applies the GTE predicate on the "uploaded_files" field.
func UploadedFilesGTE(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldUploadedFiles, v))
}

// UploadedFilesLT applies the LT predicate on the "uploaded_files" field.
func UploadedFilesLT(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldUploadedFiles, v))
}

// UploadedFilesLTE applies the LTE predicate on the "uploaded_files" field.
func UploadedFilesLTE(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldUploadedFiles, v))
}

// HitPoliciesEQ applies the EQ predicate on the "hit_policies" field.
func HitPoliciesEQ(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldEQ(FieldHitPolicies, v))
}

// HitPoliciesNEQ applies the NEQ predicate on the "hit_policies" field.
func HitPoliciesNEQ(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNEQ(FieldHitPolicies, v))
}

// HitPoliciesIn applies the In predicate on the "hit_policies" field.
func HitPoliciesIn(vs ...*pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldIn(FieldHitPolicies, vs...))
}

// HitPoliciesNotIn applies the NotIn predicate on the "hit_policies" field.
func HitPoliciesNotIn(vs ...*pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldNotIn(FieldHitPolicies, vs...))
}

// HitPoliciesGT applies the GT predicate on the "hit_policies" field.
func HitPoliciesGT(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGT(FieldHitPolicies, v))
}

// HitPoliciesGTE applies the GTE predicate on the "hit_policies" field.
func HitPoliciesGTE(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldGTE(FieldHitPolicies, v))
}

// HitPoliciesLT applies the LT predicate on the "hit_policies" field.
func HitPoliciesLT(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLT(FieldHitPolicies, v))
}

// HitPoliciesLTE applies the LTE predicate on the "hit_policies" field.
func HitPoliciesLTE(v *pq.StringArray) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.FieldLTE(FieldHitPolicies, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AiAgentSecurityLog) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AiAgentSecurityLog) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AiAgentSecurityLog) predicate.AiAgentSecurityLog {
	return predicate.AiAgentSecurityLog(sql.NotPredicates(p))
}

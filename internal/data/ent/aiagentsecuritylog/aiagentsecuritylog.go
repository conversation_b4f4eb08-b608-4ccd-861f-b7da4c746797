// Code generated by ent, DO NOT EDIT.

package aiagentsecuritylog

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

const (
	// Label holds the string label denoting the aiagentsecuritylog type in the database.
	Label = "ai_agent_security_log"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldRiskLevel holds the string denoting the risk_level field in the database.
	FieldRiskLevel = "risk_level"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldUserName holds the string denoting the user_name field in the database.
	FieldUserName = "user_name"
	// FieldDeptID holds the string denoting the dept_id field in the database.
	FieldDeptID = "dept_id"
	// FieldDeptName holds the string denoting the dept_name field in the database.
	FieldDeptName = "dept_name"
	// FieldPcName holds the string denoting the pc_name field in the database.
	FieldPcName = "pc_name"
	// FieldAgentID holds the string denoting the agent_id field in the database.
	FieldAgentID = "agent_id"
	// FieldAgentName holds the string denoting the agent_name field in the database.
	FieldAgentName = "agent_name"
	// FieldAgentDescription holds the string denoting the agent_description field in the database.
	FieldAgentDescription = "agent_description"
	// FieldHitAction holds the string denoting the hit_action field in the database.
	FieldHitAction = "hit_action"
	// FieldQuestion holds the string denoting the question field in the database.
	FieldQuestion = "question"
	// FieldActionCategory holds the string denoting the action_category field in the database.
	FieldActionCategory = "action_category"
	// FieldUploadedFiles holds the string denoting the uploaded_files field in the database.
	FieldUploadedFiles = "uploaded_files"
	// FieldHitPolicies holds the string denoting the hit_policies field in the database.
	FieldHitPolicies = "hit_policies"
	// Table holds the table name of the aiagentsecuritylog in the database.
	Table = "ai_agent_security_log"
)

// Columns holds all SQL columns for aiagentsecuritylog fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldRiskLevel,
	FieldUserID,
	FieldUserName,
	FieldDeptID,
	FieldDeptName,
	FieldPcName,
	FieldAgentID,
	FieldAgentName,
	FieldAgentDescription,
	FieldHitAction,
	FieldQuestion,
	FieldActionCategory,
	FieldUploadedFiles,
	FieldHitPolicies,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultDeletedAt holds the default value on creation for the "deleted_at" field.
	DefaultDeletedAt time.Time
	// DefaultHitAction holds the default value on creation for the "hit_action" field.
	DefaultHitAction int64
	// DefaultActionCategory holds the default value on creation for the "action_category" field.
	DefaultActionCategory int64
	// DefaultUploadedFiles holds the default value on creation for the "uploaded_files" field.
	DefaultUploadedFiles *pq.StringArray
	// DefaultHitPolicies holds the default value on creation for the "hit_policies" field.
	DefaultHitPolicies *pq.StringArray
)

// OrderOption defines the ordering options for the AiAgentSecurityLog queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByRiskLevel orders the results by the risk_level field.
func ByRiskLevel(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRiskLevel, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByUserName orders the results by the user_name field.
func ByUserName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserName, opts...).ToFunc()
}

// ByDeptID orders the results by the dept_id field.
func ByDeptID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeptID, opts...).ToFunc()
}

// ByDeptName orders the results by the dept_name field.
func ByDeptName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeptName, opts...).ToFunc()
}

// ByPcName orders the results by the pc_name field.
func ByPcName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPcName, opts...).ToFunc()
}

// ByAgentID orders the results by the agent_id field.
func ByAgentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAgentID, opts...).ToFunc()
}

// ByAgentName orders the results by the agent_name field.
func ByAgentName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAgentName, opts...).ToFunc()
}

// ByAgentDescription orders the results by the agent_description field.
func ByAgentDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAgentDescription, opts...).ToFunc()
}

// ByHitAction orders the results by the hit_action field.
func ByHitAction(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHitAction, opts...).ToFunc()
}

// ByQuestion orders the results by the question field.
func ByQuestion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldQuestion, opts...).ToFunc()
}

// ByActionCategory orders the results by the action_category field.
func ByActionCategory(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldActionCategory, opts...).ToFunc()
}

// ByUploadedFiles orders the results by the uploaded_files field.
func ByUploadedFiles(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUploadedFiles, opts...).ToFunc()
}

// ByHitPolicies orders the results by the hit_policies field.
func ByHitPolicies(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHitPolicies, opts...).ToFunc()
}

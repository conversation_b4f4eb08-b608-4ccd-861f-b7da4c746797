// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AiAgentColumns holds the columns for the "ai_agent" table.
	AiAgentColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "name", Type: field.TypeString},
		{Name: "description", Type: field.TypeString},
		{Name: "avatar", Type: field.TypeString},
		{Name: "clicked_avatar", Type: field.TypeString},
		{Name: "welcome_msg", Type: field.TypeString},
		{Name: "fallback_msg", Type: field.TypeString},
		{Name: "owner_id", Type: field.TypeInt64},
		{Name: "visibility_type", Type: field.TypeInt8},
		{Name: "manageable_to_user", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "bigint[]"}},
		{Name: "visible_to_user", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "bigint[]"}},
		{Name: "visible_to_dept", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "bigint[]"}},
		{Name: "knowledge_base_ids", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "bigint[]"}},
		{Name: "schema", Type: field.TypeString},
		{Name: "is_public", Type: field.TypeBool},
		{Name: "is_enabled", Type: field.TypeBool},
		{Name: "is_ref_files", Type: field.TypeBool},
		{Name: "model_type", Type: field.TypeInt64, Default: 1},
		{Name: "model_id", Type: field.TypeInt64, Default: 1},
		{Name: "use_count", Type: field.TypeInt64, Default: 0},
		{Name: "knowledge_base_type", Type: field.TypeInt64, Default: 0},
		{Name: "internet_search", Type: field.TypeBool, Default: false},
		{Name: "agent_type", Type: field.TypeInt64, Default: 1},
		{Name: "thinking", Type: field.TypeBool, Default: false},
		{Name: "thinking_model_id", Type: field.TypeInt64, Default: 0},
		{Name: "role_setting", Type: field.TypeString},
		{Name: "upload_file", Type: field.TypeBool, Default: true},
		{Name: "semantic_cache", Type: field.TypeBool, Default: true},
	}
	// AiAgentTable holds the schema information for the "ai_agent" table.
	AiAgentTable = &schema.Table{
		Name:       "ai_agent",
		Columns:    AiAgentColumns,
		PrimaryKey: []*schema.Column{AiAgentColumns[0]},
	}
	// AiAgentSecurityLogColumns holds the columns for the "ai_agent_security_log" table.
	AiAgentSecurityLogColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "risk_level", Type: field.TypeInt64},
		{Name: "user_id", Type: field.TypeInt64},
		{Name: "user_name", Type: field.TypeString},
		{Name: "dept_id", Type: field.TypeInt64},
		{Name: "dept_name", Type: field.TypeString},
		{Name: "pc_name", Type: field.TypeString},
		{Name: "agent_id", Type: field.TypeInt64},
		{Name: "agent_name", Type: field.TypeString},
		{Name: "agent_description", Type: field.TypeString},
		{Name: "hit_action", Type: field.TypeInt64, Default: 1},
		{Name: "question", Type: field.TypeString},
		{Name: "action_category", Type: field.TypeInt64, Default: 1},
		{Name: "uploaded_files", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "text[]"}},
		{Name: "hit_policies", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "text[]"}},
	}
	// AiAgentSecurityLogTable holds the schema information for the "ai_agent_security_log" table.
	AiAgentSecurityLogTable = &schema.Table{
		Name:       "ai_agent_security_log",
		Columns:    AiAgentSecurityLogColumns,
		PrimaryKey: []*schema.Column{AiAgentSecurityLogColumns[0]},
	}
	// AiAgentSecurityPolicyColumns holds the columns for the "ai_agent_security_policy" table.
	AiAgentSecurityPolicyColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "name", Type: field.TypeString},
		{Name: "agent_id", Type: field.TypeInt64},
		{Name: "policy_category", Type: field.TypeInt64},
		{Name: "risk_level", Type: field.TypeInt64},
		{Name: "enabled", Type: field.TypeBool, Default: true},
		{Name: "policies", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "text[]"}},
		{Name: "hit_action", Type: field.TypeInt64, Default: 1},
		{Name: "hit_response", Type: field.TypeString, Default: ""},
		{Name: "updated_by", Type: field.TypeInt64},
	}
	// AiAgentSecurityPolicyTable holds the schema information for the "ai_agent_security_policy" table.
	AiAgentSecurityPolicyTable = &schema.Table{
		Name:       "ai_agent_security_policy",
		Columns:    AiAgentSecurityPolicyColumns,
		PrimaryKey: []*schema.Column{AiAgentSecurityPolicyColumns[0]},
	}
	// AiChatColumns holds the columns for the "ai_chat" table.
	AiChatColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "tenant_id", Type: field.TypeInt64},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "name", Type: field.TypeString},
		{Name: "user_id", Type: field.TypeInt64},
		{Name: "chat_type", Type: field.TypeInt64},
		{Name: "agent_id", Type: field.TypeInt64, Nullable: true},
	}
	// AiChatTable holds the schema information for the "ai_chat" table.
	AiChatTable = &schema.Table{
		Name:       "ai_chat",
		Columns:    AiChatColumns,
		PrimaryKey: []*schema.Column{AiChatColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "ai_chat_ai_agent_ai_chat",
				Columns:    []*schema.Column{AiChatColumns[8]},
				RefColumns: []*schema.Column{AiAgentColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// AiChatItemColumns holds the columns for the "ai_chat_item" table.
	AiChatItemColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "tenant_id", Type: field.TypeInt64},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "chat_id", Type: field.TypeInt64},
		{Name: "object_id", Type: field.TypeInt64},
		{Name: "object_type", Type: field.TypeInt64},
		{Name: "message", Type: field.TypeString},
		{Name: "ref_files", Type: field.TypeString},
		{Name: "agree_status", Type: field.TypeInt8, Default: 0},
		{Name: "round_id", Type: field.TypeInt64, Default: 0},
		{Name: "pc_name", Type: field.TypeString, Default: ""},
		{Name: "reason", Type: field.TypeString, Default: ""},
		{Name: "primary_classification", Type: field.TypeString, Default: ""},
		{Name: "secondary_classification", Type: field.TypeString, Default: ""},
		{Name: "mine_types", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "varchar[]"}},
		{Name: "suggest_questions", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "varchar[]"}},
		{Name: "hit_action", Type: field.TypeInt64, Default: 0},
		{Name: "hit_response", Type: field.TypeString, Default: ""},
		{Name: "hit_continue_send", Type: field.TypeBool, Default: false},
		{Name: "is_internet_search", Type: field.TypeBool, Default: false},
	}
	// AiChatItemTable holds the schema information for the "ai_chat_item" table.
	AiChatItemTable = &schema.Table{
		Name:       "ai_chat_item",
		Columns:    AiChatItemColumns,
		PrimaryKey: []*schema.Column{AiChatItemColumns[0]},
	}
	// AiModelColumns holds the columns for the "ai_model" table.
	AiModelColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "model_name", Type: field.TypeString},
		{Name: "model", Type: field.TypeInt64},
		{Name: "api_key", Type: field.TypeString},
	}
	// AiModelTable holds the schema information for the "ai_model" table.
	AiModelTable = &schema.Table{
		Name:       "ai_model",
		Columns:    AiModelColumns,
		PrimaryKey: []*schema.Column{AiModelColumns[0]},
	}
	// AiModelDetailColumns holds the columns for the "ai_model_detail" table.
	AiModelDetailColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "model_name", Type: field.TypeString},
		{Name: "name", Type: field.TypeString},
		{Name: "url", Type: field.TypeString},
		{Name: "avatar", Type: field.TypeString},
		{Name: "can_internet_search", Type: field.TypeBool},
		{Name: "balance_search_url", Type: field.TypeString},
		{Name: "thinking_enable_status", Type: field.TypeInt64, Default: 0},
		{Name: "background_url", Type: field.TypeString},
	}
	// AiModelDetailTable holds the schema information for the "ai_model_detail" table.
	AiModelDetailTable = &schema.Table{
		Name:       "ai_model_detail",
		Columns:    AiModelDetailColumns,
		PrimaryKey: []*schema.Column{AiModelDetailColumns[0]},
	}
	// AiModelUsageColumns holds the columns for the "ai_model_usage" table.
	AiModelUsageColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "model_detail_id", Type: field.TypeInt64},
		{Name: "model_name", Type: field.TypeString},
		{Name: "model_gateway_name", Type: field.TypeString},
		{Name: "agent_id", Type: field.TypeInt64},
		{Name: "agent_name", Type: field.TypeString},
		{Name: "user_id", Type: field.TypeInt64},
		{Name: "user_name", Type: field.TypeString},
		{Name: "question", Type: field.TypeString},
		{Name: "answer", Type: field.TypeString},
		{Name: "prompt_tokens", Type: field.TypeInt64},
		{Name: "completion_tokens", Type: field.TypeInt64},
		{Name: "request_status", Type: field.TypeInt8, Default: 0},
		{Name: "error_code", Type: field.TypeString, Default: ""},
	}
	// AiModelUsageTable holds the schema information for the "ai_model_usage" table.
	AiModelUsageTable = &schema.Table{
		Name:       "ai_model_usage",
		Columns:    AiModelUsageColumns,
		PrimaryKey: []*schema.Column{AiModelUsageColumns[0]},
	}
	// AtomicQuestionsColumns holds the columns for the "atomic_questions" table.
	AtomicQuestionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "entity_tag", Type: field.TypeString},
		{Name: "pre_entity_tag", Type: field.TypeString},
		{Name: "file_relation_id", Type: field.TypeInt64},
		{Name: "chunk_index", Type: field.TypeInt64},
		{Name: "chunk_size", Type: field.TypeInt64},
		{Name: "index", Type: field.TypeInt64},
		{Name: "question", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "text[]"}},
		{Name: "is_handle", Type: field.TypeBool, Default: false},
	}
	// AtomicQuestionsTable holds the schema information for the "atomic_questions" table.
	AtomicQuestionsTable = &schema.Table{
		Name:       "atomic_questions",
		Columns:    AtomicQuestionsColumns,
		PrimaryKey: []*schema.Column{AtomicQuestionsColumns[0]},
	}
	// ClassificationFilesColumns holds the columns for the "classification_files" table.
	ClassificationFilesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "name", Type: field.TypeString, Default: ""},
		{Name: "file_relation_id", Type: field.TypeInt64, Default: 0},
		{Name: "pre_entity_tag", Type: field.TypeString, Default: ""},
		{Name: "entity_tag", Type: field.TypeString, Default: ""},
		{Name: "filename", Type: field.TypeString, Default: ""},
		{Name: "mime_type", Type: field.TypeString, Default: ""},
		{Name: "user_id", Type: field.TypeInt64, Default: 0},
		{Name: "user_name", Type: field.TypeString, Default: ""},
		{Name: "dept_ids", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "bigint[]"}},
		{Name: "dept_name", Type: field.TypeString, Default: ""},
		{Name: "path", Type: field.TypeString, Default: ""},
		{Name: "security_level", Type: field.TypeInt32, Default: 0},
		{Name: "node_level", Type: field.TypeInt32, Default: 0},
		{Name: "check_status", Type: field.TypeInt32, Default: 0},
		{Name: "tree_type", Type: field.TypeInt32, Default: 0},
	}
	// ClassificationFilesTable holds the schema information for the "classification_files" table.
	ClassificationFilesTable = &schema.Table{
		Name:       "classification_files",
		Columns:    ClassificationFilesColumns,
		PrimaryKey: []*schema.Column{ClassificationFilesColumns[0]},
	}
	// DefaultAgentAvatarColumns holds the columns for the "default_agent_avatar" table.
	DefaultAgentAvatarColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "tenant_id", Type: field.TypeInt64},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "avatar", Type: field.TypeString},
		{Name: "clicked_avatar", Type: field.TypeString},
		{Name: "avatar_type", Type: field.TypeInt8},
	}
	// DefaultAgentAvatarTable holds the schema information for the "default_agent_avatar" table.
	DefaultAgentAvatarTable = &schema.Table{
		Name:       "default_agent_avatar",
		Columns:    DefaultAgentAvatarColumns,
		PrimaryKey: []*schema.Column{DefaultAgentAvatarColumns[0]},
	}
	// ExternalModelUsageColumns holds the columns for the "external_model_usage" table.
	ExternalModelUsageColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "model_name", Type: field.TypeString, Default: ""},
		{Name: "question", Type: field.TypeString, Default: ""},
		{Name: "question_tag", Type: field.TypeString, Default: ""},
		{Name: "files", Type: field.TypeString, Default: ""},
		{Name: "mime_types", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "text[]"}},
		{Name: "user_id", Type: field.TypeInt64, Default: 0},
		{Name: "user_name", Type: field.TypeString, Default: ""},
		{Name: "dept_id", Type: field.TypeInt64, Default: 0},
		{Name: "dept_name", Type: field.TypeString, Default: ""},
		{Name: "pc_name", Type: field.TypeString, Default: ""},
		{Name: "happened_at", Type: field.TypeTime},
	}
	// ExternalModelUsageTable holds the schema information for the "external_model_usage" table.
	ExternalModelUsageTable = &schema.Table{
		Name:       "external_model_usage",
		Columns:    ExternalModelUsageColumns,
		PrimaryKey: []*schema.Column{ExternalModelUsageColumns[0]},
	}
	// KnowledgeBaseColumns holds the columns for the "knowledge_base" table.
	KnowledgeBaseColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "tenant_id", Type: field.TypeInt64},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "name", Type: field.TypeString, Default: ""},
		{Name: "public", Type: field.TypeBool, Default: false},
		{Name: "data_type", Type: field.TypeInt32, Default: 0},
		{Name: "user_id", Type: field.TypeInt64, Default: 0},
		{Name: "manager_user_ids", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "bigint[]"}},
		{Name: "editable_user_ids", Type: field.TypeOther, SchemaType: map[string]string{"postgres": "bigint[]"}},
	}
	// KnowledgeBaseTable holds the schema information for the "knowledge_base" table.
	KnowledgeBaseTable = &schema.Table{
		Name:       "knowledge_base",
		Columns:    KnowledgeBaseColumns,
		PrimaryKey: []*schema.Column{KnowledgeBaseColumns[0]},
	}
	// KnowledgeBaseFileColumns holds the columns for the "knowledge_base_file" table.
	KnowledgeBaseFileColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "tenant_id", Type: field.TypeInt64},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "knowledge_base_id", Type: field.TypeInt64, Default: 0},
		{Name: "data_type", Type: field.TypeInt32, Default: 0},
		{Name: "file_relation_id", Type: field.TypeInt64, Default: 0},
		{Name: "metadata", Type: field.TypeString, Default: ""},
		{Name: "status", Type: field.TypeInt32, Default: 0},
		{Name: "failed_reason", Type: field.TypeString, Default: ""},
	}
	// KnowledgeBaseFileTable holds the schema information for the "knowledge_base_file" table.
	KnowledgeBaseFileTable = &schema.Table{
		Name:       "knowledge_base_file",
		Columns:    KnowledgeBaseFileColumns,
		PrimaryKey: []*schema.Column{KnowledgeBaseFileColumns[0]},
	}
	// UserAgentOrderColumns holds the columns for the "user_agent_order" table.
	UserAgentOrderColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt64, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true},
		{Name: "user_id", Type: field.TypeInt64, Default: 0},
		{Name: "agent_id", Type: field.TypeInt64, Default: 0},
		{Name: "order_index", Type: field.TypeInt64, Default: 0},
	}
	// UserAgentOrderTable holds the schema information for the "user_agent_order" table.
	UserAgentOrderTable = &schema.Table{
		Name:       "user_agent_order",
		Columns:    UserAgentOrderColumns,
		PrimaryKey: []*schema.Column{UserAgentOrderColumns[0]},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AiAgentTable,
		AiAgentSecurityLogTable,
		AiAgentSecurityPolicyTable,
		AiChatTable,
		AiChatItemTable,
		AiModelTable,
		AiModelDetailTable,
		AiModelUsageTable,
		AtomicQuestionsTable,
		ClassificationFilesTable,
		DefaultAgentAvatarTable,
		ExternalModelUsageTable,
		KnowledgeBaseTable,
		KnowledgeBaseFileTable,
		UserAgentOrderTable,
	}
)

func init() {
	AiAgentTable.Annotation = &entsql.Annotation{
		Table: "ai_agent",
	}
	AiAgentSecurityLogTable.Annotation = &entsql.Annotation{
		Table: "ai_agent_security_log",
	}
	AiAgentSecurityPolicyTable.Annotation = &entsql.Annotation{
		Table: "ai_agent_security_policy",
	}
	AiChatTable.ForeignKeys[0].RefTable = AiAgentTable
	AiChatTable.Annotation = &entsql.Annotation{
		Table: "ai_chat",
	}
	AiChatItemTable.Annotation = &entsql.Annotation{
		Table: "ai_chat_item",
	}
	AiModelTable.Annotation = &entsql.Annotation{
		Table: "ai_model",
	}
	AiModelDetailTable.Annotation = &entsql.Annotation{
		Table: "ai_model_detail",
	}
	AiModelUsageTable.Annotation = &entsql.Annotation{
		Table: "ai_model_usage",
	}
	AtomicQuestionsTable.Annotation = &entsql.Annotation{
		Table: "atomic_questions",
	}
	ClassificationFilesTable.Annotation = &entsql.Annotation{
		Table: "classification_files",
	}
	DefaultAgentAvatarTable.Annotation = &entsql.Annotation{
		Table: "default_agent_avatar",
	}
	ExternalModelUsageTable.Annotation = &entsql.Annotation{
		Table: "external_model_usage",
	}
	KnowledgeBaseTable.Annotation = &entsql.Annotation{
		Table: "knowledge_base",
	}
	KnowledgeBaseFileTable.Annotation = &entsql.Annotation{
		Table: "knowledge_base_file",
	}
	UserAgentOrderTable.Annotation = &entsql.Annotation{
		Table: "user_agent_order",
	}
}

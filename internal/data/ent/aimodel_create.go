// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
)

// AiModelCreate is the builder for creating a AiModel entity.
type AiModelCreate struct {
	config
	mutation *AiModelMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (amc *AiModelCreate) SetCreatedAt(t time.Time) *AiModelCreate {
	amc.mutation.SetCreatedAt(t)
	return amc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (amc *AiModelCreate) SetNillableCreatedAt(t *time.Time) *AiModelCreate {
	if t != nil {
		amc.SetCreatedAt(*t)
	}
	return amc
}

// SetUpdatedAt sets the "updated_at" field.
func (amc *AiModelCreate) SetUpdatedAt(t time.Time) *AiModelCreate {
	amc.mutation.SetUpdatedAt(t)
	return amc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (amc *AiModelCreate) SetNillableUpdatedAt(t *time.Time) *AiModelCreate {
	if t != nil {
		amc.SetUpdatedAt(*t)
	}
	return amc
}

// SetDeletedAt sets the "deleted_at" field.
func (amc *AiModelCreate) SetDeletedAt(t time.Time) *AiModelCreate {
	amc.mutation.SetDeletedAt(t)
	return amc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (amc *AiModelCreate) SetNillableDeletedAt(t *time.Time) *AiModelCreate {
	if t != nil {
		amc.SetDeletedAt(*t)
	}
	return amc
}

// SetModelName sets the "model_name" field.
func (amc *AiModelCreate) SetModelName(s string) *AiModelCreate {
	amc.mutation.SetModelName(s)
	return amc
}

// SetModel sets the "model" field.
func (amc *AiModelCreate) SetModel(i int64) *AiModelCreate {
	amc.mutation.SetModel(i)
	return amc
}

// SetAPIKey sets the "api_key" field.
func (amc *AiModelCreate) SetAPIKey(s string) *AiModelCreate {
	amc.mutation.SetAPIKey(s)
	return amc
}

// SetID sets the "id" field.
func (amc *AiModelCreate) SetID(i int64) *AiModelCreate {
	amc.mutation.SetID(i)
	return amc
}

// Mutation returns the AiModelMutation object of the builder.
func (amc *AiModelCreate) Mutation() *AiModelMutation {
	return amc.mutation
}

// Save creates the AiModel in the database.
func (amc *AiModelCreate) Save(ctx context.Context) (*AiModel, error) {
	if err := amc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, amc.sqlSave, amc.mutation, amc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (amc *AiModelCreate) SaveX(ctx context.Context) *AiModel {
	v, err := amc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (amc *AiModelCreate) Exec(ctx context.Context) error {
	_, err := amc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amc *AiModelCreate) ExecX(ctx context.Context) {
	if err := amc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (amc *AiModelCreate) defaults() error {
	if _, ok := amc.mutation.CreatedAt(); !ok {
		if aimodel.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodel.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := aimodel.DefaultCreatedAt()
		amc.mutation.SetCreatedAt(v)
	}
	if _, ok := amc.mutation.UpdatedAt(); !ok {
		if aimodel.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodel.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aimodel.DefaultUpdatedAt()
		amc.mutation.SetUpdatedAt(v)
	}
	if _, ok := amc.mutation.DeletedAt(); !ok {
		v := aimodel.DefaultDeletedAt
		amc.mutation.SetDeletedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (amc *AiModelCreate) check() error {
	if _, ok := amc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AiModel.created_at"`)}
	}
	if _, ok := amc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AiModel.updated_at"`)}
	}
	if _, ok := amc.mutation.ModelName(); !ok {
		return &ValidationError{Name: "model_name", err: errors.New(`ent: missing required field "AiModel.model_name"`)}
	}
	if _, ok := amc.mutation.Model(); !ok {
		return &ValidationError{Name: "model", err: errors.New(`ent: missing required field "AiModel.model"`)}
	}
	if _, ok := amc.mutation.APIKey(); !ok {
		return &ValidationError{Name: "api_key", err: errors.New(`ent: missing required field "AiModel.api_key"`)}
	}
	return nil
}

func (amc *AiModelCreate) sqlSave(ctx context.Context) (*AiModel, error) {
	if err := amc.check(); err != nil {
		return nil, err
	}
	_node, _spec := amc.createSpec()
	if err := sqlgraph.CreateNode(ctx, amc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	amc.mutation.id = &_node.ID
	amc.mutation.done = true
	return _node, nil
}

func (amc *AiModelCreate) createSpec() (*AiModel, *sqlgraph.CreateSpec) {
	var (
		_node = &AiModel{config: amc.config}
		_spec = sqlgraph.NewCreateSpec(aimodel.Table, sqlgraph.NewFieldSpec(aimodel.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = amc.conflict
	if id, ok := amc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := amc.mutation.CreatedAt(); ok {
		_spec.SetField(aimodel.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := amc.mutation.UpdatedAt(); ok {
		_spec.SetField(aimodel.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := amc.mutation.DeletedAt(); ok {
		_spec.SetField(aimodel.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := amc.mutation.ModelName(); ok {
		_spec.SetField(aimodel.FieldModelName, field.TypeString, value)
		_node.ModelName = value
	}
	if value, ok := amc.mutation.Model(); ok {
		_spec.SetField(aimodel.FieldModel, field.TypeInt64, value)
		_node.Model = value
	}
	if value, ok := amc.mutation.APIKey(); ok {
		_spec.SetField(aimodel.FieldAPIKey, field.TypeString, value)
		_node.APIKey = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiModel.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiModelUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (amc *AiModelCreate) OnConflict(opts ...sql.ConflictOption) *AiModelUpsertOne {
	amc.conflict = opts
	return &AiModelUpsertOne{
		create: amc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiModel.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (amc *AiModelCreate) OnConflictColumns(columns ...string) *AiModelUpsertOne {
	amc.conflict = append(amc.conflict, sql.ConflictColumns(columns...))
	return &AiModelUpsertOne{
		create: amc,
	}
}

type (
	// AiModelUpsertOne is the builder for "upsert"-ing
	//  one AiModel node.
	AiModelUpsertOne struct {
		create *AiModelCreate
	}

	// AiModelUpsert is the "OnConflict" setter.
	AiModelUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *AiModelUpsert) SetUpdatedAt(v time.Time) *AiModelUpsert {
	u.Set(aimodel.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiModelUpsert) UpdateUpdatedAt() *AiModelUpsert {
	u.SetExcluded(aimodel.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiModelUpsert) SetDeletedAt(v time.Time) *AiModelUpsert {
	u.Set(aimodel.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiModelUpsert) UpdateDeletedAt() *AiModelUpsert {
	u.SetExcluded(aimodel.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiModelUpsert) ClearDeletedAt() *AiModelUpsert {
	u.SetNull(aimodel.FieldDeletedAt)
	return u
}

// SetModelName sets the "model_name" field.
func (u *AiModelUpsert) SetModelName(v string) *AiModelUpsert {
	u.Set(aimodel.FieldModelName, v)
	return u
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *AiModelUpsert) UpdateModelName() *AiModelUpsert {
	u.SetExcluded(aimodel.FieldModelName)
	return u
}

// SetModel sets the "model" field.
func (u *AiModelUpsert) SetModel(v int64) *AiModelUpsert {
	u.Set(aimodel.FieldModel, v)
	return u
}

// UpdateModel sets the "model" field to the value that was provided on create.
func (u *AiModelUpsert) UpdateModel() *AiModelUpsert {
	u.SetExcluded(aimodel.FieldModel)
	return u
}

// AddModel adds v to the "model" field.
func (u *AiModelUpsert) AddModel(v int64) *AiModelUpsert {
	u.Add(aimodel.FieldModel, v)
	return u
}

// SetAPIKey sets the "api_key" field.
func (u *AiModelUpsert) SetAPIKey(v string) *AiModelUpsert {
	u.Set(aimodel.FieldAPIKey, v)
	return u
}

// UpdateAPIKey sets the "api_key" field to the value that was provided on create.
func (u *AiModelUpsert) UpdateAPIKey() *AiModelUpsert {
	u.SetExcluded(aimodel.FieldAPIKey)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AiModel.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aimodel.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiModelUpsertOne) UpdateNewValues() *AiModelUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(aimodel.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(aimodel.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiModel.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AiModelUpsertOne) Ignore() *AiModelUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiModelUpsertOne) DoNothing() *AiModelUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiModelCreate.OnConflict
// documentation for more info.
func (u *AiModelUpsertOne) Update(set func(*AiModelUpsert)) *AiModelUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiModelUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiModelUpsertOne) SetUpdatedAt(v time.Time) *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiModelUpsertOne) UpdateUpdatedAt() *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiModelUpsertOne) SetDeletedAt(v time.Time) *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiModelUpsertOne) UpdateDeletedAt() *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiModelUpsertOne) ClearDeletedAt() *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.ClearDeletedAt()
	})
}

// SetModelName sets the "model_name" field.
func (u *AiModelUpsertOne) SetModelName(v string) *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.SetModelName(v)
	})
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *AiModelUpsertOne) UpdateModelName() *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateModelName()
	})
}

// SetModel sets the "model" field.
func (u *AiModelUpsertOne) SetModel(v int64) *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.SetModel(v)
	})
}

// AddModel adds v to the "model" field.
func (u *AiModelUpsertOne) AddModel(v int64) *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.AddModel(v)
	})
}

// UpdateModel sets the "model" field to the value that was provided on create.
func (u *AiModelUpsertOne) UpdateModel() *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateModel()
	})
}

// SetAPIKey sets the "api_key" field.
func (u *AiModelUpsertOne) SetAPIKey(v string) *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.SetAPIKey(v)
	})
}

// UpdateAPIKey sets the "api_key" field to the value that was provided on create.
func (u *AiModelUpsertOne) UpdateAPIKey() *AiModelUpsertOne {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateAPIKey()
	})
}

// Exec executes the query.
func (u *AiModelUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiModelCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiModelUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AiModelUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AiModelUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AiModelCreateBulk is the builder for creating many AiModel entities in bulk.
type AiModelCreateBulk struct {
	config
	err      error
	builders []*AiModelCreate
	conflict []sql.ConflictOption
}

// Save creates the AiModel entities in the database.
func (amcb *AiModelCreateBulk) Save(ctx context.Context) ([]*AiModel, error) {
	if amcb.err != nil {
		return nil, amcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(amcb.builders))
	nodes := make([]*AiModel, len(amcb.builders))
	mutators := make([]Mutator, len(amcb.builders))
	for i := range amcb.builders {
		func(i int, root context.Context) {
			builder := amcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AiModelMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, amcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = amcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, amcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, amcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (amcb *AiModelCreateBulk) SaveX(ctx context.Context) []*AiModel {
	v, err := amcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (amcb *AiModelCreateBulk) Exec(ctx context.Context) error {
	_, err := amcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amcb *AiModelCreateBulk) ExecX(ctx context.Context) {
	if err := amcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiModel.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiModelUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (amcb *AiModelCreateBulk) OnConflict(opts ...sql.ConflictOption) *AiModelUpsertBulk {
	amcb.conflict = opts
	return &AiModelUpsertBulk{
		create: amcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiModel.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (amcb *AiModelCreateBulk) OnConflictColumns(columns ...string) *AiModelUpsertBulk {
	amcb.conflict = append(amcb.conflict, sql.ConflictColumns(columns...))
	return &AiModelUpsertBulk{
		create: amcb,
	}
}

// AiModelUpsertBulk is the builder for "upsert"-ing
// a bulk of AiModel nodes.
type AiModelUpsertBulk struct {
	create *AiModelCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AiModel.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aimodel.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiModelUpsertBulk) UpdateNewValues() *AiModelUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(aimodel.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(aimodel.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiModel.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AiModelUpsertBulk) Ignore() *AiModelUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiModelUpsertBulk) DoNothing() *AiModelUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiModelCreateBulk.OnConflict
// documentation for more info.
func (u *AiModelUpsertBulk) Update(set func(*AiModelUpsert)) *AiModelUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiModelUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiModelUpsertBulk) SetUpdatedAt(v time.Time) *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiModelUpsertBulk) UpdateUpdatedAt() *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiModelUpsertBulk) SetDeletedAt(v time.Time) *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiModelUpsertBulk) UpdateDeletedAt() *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiModelUpsertBulk) ClearDeletedAt() *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.ClearDeletedAt()
	})
}

// SetModelName sets the "model_name" field.
func (u *AiModelUpsertBulk) SetModelName(v string) *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.SetModelName(v)
	})
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *AiModelUpsertBulk) UpdateModelName() *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateModelName()
	})
}

// SetModel sets the "model" field.
func (u *AiModelUpsertBulk) SetModel(v int64) *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.SetModel(v)
	})
}

// AddModel adds v to the "model" field.
func (u *AiModelUpsertBulk) AddModel(v int64) *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.AddModel(v)
	})
}

// UpdateModel sets the "model" field to the value that was provided on create.
func (u *AiModelUpsertBulk) UpdateModel() *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateModel()
	})
}

// SetAPIKey sets the "api_key" field.
func (u *AiModelUpsertBulk) SetAPIKey(v string) *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.SetAPIKey(v)
	})
}

// UpdateAPIKey sets the "api_key" field to the value that was provided on create.
func (u *AiModelUpsertBulk) UpdateAPIKey() *AiModelUpsertBulk {
	return u.Update(func(s *AiModelUpsert) {
		s.UpdateAPIKey()
	})
}

// Exec executes the query.
func (u *AiModelUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AiModelCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiModelCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiModelUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

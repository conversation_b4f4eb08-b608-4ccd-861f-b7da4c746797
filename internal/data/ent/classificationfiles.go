// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/classificationfiles"
)

// ClassificationFiles is the model entity for the ClassificationFiles schema.
type ClassificationFiles struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// Name holds the value of the "name" field.
	Name string `json:"name,omitempty"`
	// FileRelationID holds the value of the "file_relation_id" field.
	FileRelationID int64 `json:"file_relation_id,omitempty"`
	// PreEntityTag holds the value of the "pre_entity_tag" field.
	PreEntityTag string `json:"pre_entity_tag,omitempty"`
	// EntityTag holds the value of the "entity_tag" field.
	EntityTag string `json:"entity_tag,omitempty"`
	// Filename holds the value of the "filename" field.
	Filename string `json:"filename,omitempty"`
	// MimeType holds the value of the "mime_type" field.
	MimeType string `json:"mime_type,omitempty"`
	// UserID holds the value of the "user_id" field.
	UserID int64 `json:"user_id,omitempty"`
	// UserName holds the value of the "user_name" field.
	UserName string `json:"user_name,omitempty"`
	// DeptIds holds the value of the "dept_ids" field.
	DeptIds *pq.Int64Array `json:"dept_ids,omitempty"`
	// DeptName holds the value of the "dept_name" field.
	DeptName string `json:"dept_name,omitempty"`
	// Path holds the value of the "path" field.
	Path string `json:"path,omitempty"`
	// SecurityLevel holds the value of the "security_level" field.
	SecurityLevel int32 `json:"security_level,omitempty"`
	// NodeLevel holds the value of the "node_level" field.
	NodeLevel int32 `json:"node_level,omitempty"`
	// CheckStatus holds the value of the "check_status" field.
	CheckStatus int32 `json:"check_status,omitempty"`
	// TreeType holds the value of the "tree_type" field.
	TreeType     int32 `json:"tree_type,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*ClassificationFiles) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case classificationfiles.FieldDeptIds:
			values[i] = new(pq.Int64Array)
		case classificationfiles.FieldID, classificationfiles.FieldFileRelationID, classificationfiles.FieldUserID, classificationfiles.FieldSecurityLevel, classificationfiles.FieldNodeLevel, classificationfiles.FieldCheckStatus, classificationfiles.FieldTreeType:
			values[i] = new(sql.NullInt64)
		case classificationfiles.FieldName, classificationfiles.FieldPreEntityTag, classificationfiles.FieldEntityTag, classificationfiles.FieldFilename, classificationfiles.FieldMimeType, classificationfiles.FieldUserName, classificationfiles.FieldDeptName, classificationfiles.FieldPath:
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the ClassificationFiles fields.
func (cf *ClassificationFiles) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case classificationfiles.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			cf.ID = int64(value.Int64)
		case classificationfiles.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				cf.Name = value.String
			}
		case classificationfiles.FieldFileRelationID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field file_relation_id", values[i])
			} else if value.Valid {
				cf.FileRelationID = value.Int64
			}
		case classificationfiles.FieldPreEntityTag:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field pre_entity_tag", values[i])
			} else if value.Valid {
				cf.PreEntityTag = value.String
			}
		case classificationfiles.FieldEntityTag:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field entity_tag", values[i])
			} else if value.Valid {
				cf.EntityTag = value.String
			}
		case classificationfiles.FieldFilename:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field filename", values[i])
			} else if value.Valid {
				cf.Filename = value.String
			}
		case classificationfiles.FieldMimeType:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field mime_type", values[i])
			} else if value.Valid {
				cf.MimeType = value.String
			}
		case classificationfiles.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				cf.UserID = value.Int64
			}
		case classificationfiles.FieldUserName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_name", values[i])
			} else if value.Valid {
				cf.UserName = value.String
			}
		case classificationfiles.FieldDeptIds:
			if value, ok := values[i].(*pq.Int64Array); !ok {
				return fmt.Errorf("unexpected type %T for field dept_ids", values[i])
			} else if value != nil {
				cf.DeptIds = value
			}
		case classificationfiles.FieldDeptName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field dept_name", values[i])
			} else if value.Valid {
				cf.DeptName = value.String
			}
		case classificationfiles.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				cf.Path = value.String
			}
		case classificationfiles.FieldSecurityLevel:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field security_level", values[i])
			} else if value.Valid {
				cf.SecurityLevel = int32(value.Int64)
			}
		case classificationfiles.FieldNodeLevel:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field node_level", values[i])
			} else if value.Valid {
				cf.NodeLevel = int32(value.Int64)
			}
		case classificationfiles.FieldCheckStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field check_status", values[i])
			} else if value.Valid {
				cf.CheckStatus = int32(value.Int64)
			}
		case classificationfiles.FieldTreeType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tree_type", values[i])
			} else if value.Valid {
				cf.TreeType = int32(value.Int64)
			}
		default:
			cf.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the ClassificationFiles.
// This includes values selected through modifiers, order, etc.
func (cf *ClassificationFiles) Value(name string) (ent.Value, error) {
	return cf.selectValues.Get(name)
}

// Update returns a builder for updating this ClassificationFiles.
// Note that you need to call ClassificationFiles.Unwrap() before calling this method if this ClassificationFiles
// was returned from a transaction, and the transaction was committed or rolled back.
func (cf *ClassificationFiles) Update() *ClassificationFilesUpdateOne {
	return NewClassificationFilesClient(cf.config).UpdateOne(cf)
}

// Unwrap unwraps the ClassificationFiles entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (cf *ClassificationFiles) Unwrap() *ClassificationFiles {
	_tx, ok := cf.config.driver.(*txDriver)
	if !ok {
		panic("ent: ClassificationFiles is not a transactional entity")
	}
	cf.config.driver = _tx.drv
	return cf
}

// String implements the fmt.Stringer.
func (cf *ClassificationFiles) String() string {
	var builder strings.Builder
	builder.WriteString("ClassificationFiles(")
	builder.WriteString(fmt.Sprintf("id=%v, ", cf.ID))
	builder.WriteString("name=")
	builder.WriteString(cf.Name)
	builder.WriteString(", ")
	builder.WriteString("file_relation_id=")
	builder.WriteString(fmt.Sprintf("%v", cf.FileRelationID))
	builder.WriteString(", ")
	builder.WriteString("pre_entity_tag=")
	builder.WriteString(cf.PreEntityTag)
	builder.WriteString(", ")
	builder.WriteString("entity_tag=")
	builder.WriteString(cf.EntityTag)
	builder.WriteString(", ")
	builder.WriteString("filename=")
	builder.WriteString(cf.Filename)
	builder.WriteString(", ")
	builder.WriteString("mime_type=")
	builder.WriteString(cf.MimeType)
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", cf.UserID))
	builder.WriteString(", ")
	builder.WriteString("user_name=")
	builder.WriteString(cf.UserName)
	builder.WriteString(", ")
	builder.WriteString("dept_ids=")
	builder.WriteString(fmt.Sprintf("%v", cf.DeptIds))
	builder.WriteString(", ")
	builder.WriteString("dept_name=")
	builder.WriteString(cf.DeptName)
	builder.WriteString(", ")
	builder.WriteString("path=")
	builder.WriteString(cf.Path)
	builder.WriteString(", ")
	builder.WriteString("security_level=")
	builder.WriteString(fmt.Sprintf("%v", cf.SecurityLevel))
	builder.WriteString(", ")
	builder.WriteString("node_level=")
	builder.WriteString(fmt.Sprintf("%v", cf.NodeLevel))
	builder.WriteString(", ")
	builder.WriteString("check_status=")
	builder.WriteString(fmt.Sprintf("%v", cf.CheckStatus))
	builder.WriteString(", ")
	builder.WriteString("tree_type=")
	builder.WriteString(fmt.Sprintf("%v", cf.TreeType))
	builder.WriteByte(')')
	return builder.String()
}

// ClassificationFilesSlice is a parsable slice of ClassificationFiles.
type ClassificationFilesSlice []*ClassificationFiles

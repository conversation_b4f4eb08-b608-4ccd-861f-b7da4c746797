// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/migrate"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritylog"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritypolicy"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichatitem"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodeldetail"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/atomicquestions"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/classificationfiles"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/defaultagentavatar"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/externalmodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebasefile"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/useragentorder"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// AiAgent is the client for interacting with the AiAgent builders.
	AiAgent *AiAgentClient
	// AiAgentSecurityLog is the client for interacting with the AiAgentSecurityLog builders.
	AiAgentSecurityLog *AiAgentSecurityLogClient
	// AiAgentSecurityPolicy is the client for interacting with the AiAgentSecurityPolicy builders.
	AiAgentSecurityPolicy *AiAgentSecurityPolicyClient
	// AiChat is the client for interacting with the AiChat builders.
	AiChat *AiChatClient
	// AiChatItem is the client for interacting with the AiChatItem builders.
	AiChatItem *AiChatItemClient
	// AiModel is the client for interacting with the AiModel builders.
	AiModel *AiModelClient
	// AiModelDetail is the client for interacting with the AiModelDetail builders.
	AiModelDetail *AiModelDetailClient
	// AiModelUsage is the client for interacting with the AiModelUsage builders.
	AiModelUsage *AiModelUsageClient
	// AtomicQuestions is the client for interacting with the AtomicQuestions builders.
	AtomicQuestions *AtomicQuestionsClient
	// ClassificationFiles is the client for interacting with the ClassificationFiles builders.
	ClassificationFiles *ClassificationFilesClient
	// DefaultAgentAvatar is the client for interacting with the DefaultAgentAvatar builders.
	DefaultAgentAvatar *DefaultAgentAvatarClient
	// ExternalModelUsage is the client for interacting with the ExternalModelUsage builders.
	ExternalModelUsage *ExternalModelUsageClient
	// KnowledgeBase is the client for interacting with the KnowledgeBase builders.
	KnowledgeBase *KnowledgeBaseClient
	// KnowledgeBaseFile is the client for interacting with the KnowledgeBaseFile builders.
	KnowledgeBaseFile *KnowledgeBaseFileClient
	// UserAgentOrder is the client for interacting with the UserAgentOrder builders.
	UserAgentOrder *UserAgentOrderClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.AiAgent = NewAiAgentClient(c.config)
	c.AiAgentSecurityLog = NewAiAgentSecurityLogClient(c.config)
	c.AiAgentSecurityPolicy = NewAiAgentSecurityPolicyClient(c.config)
	c.AiChat = NewAiChatClient(c.config)
	c.AiChatItem = NewAiChatItemClient(c.config)
	c.AiModel = NewAiModelClient(c.config)
	c.AiModelDetail = NewAiModelDetailClient(c.config)
	c.AiModelUsage = NewAiModelUsageClient(c.config)
	c.AtomicQuestions = NewAtomicQuestionsClient(c.config)
	c.ClassificationFiles = NewClassificationFilesClient(c.config)
	c.DefaultAgentAvatar = NewDefaultAgentAvatarClient(c.config)
	c.ExternalModelUsage = NewExternalModelUsageClient(c.config)
	c.KnowledgeBase = NewKnowledgeBaseClient(c.config)
	c.KnowledgeBaseFile = NewKnowledgeBaseFileClient(c.config)
	c.UserAgentOrder = NewUserAgentOrderClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                   ctx,
		config:                cfg,
		AiAgent:               NewAiAgentClient(cfg),
		AiAgentSecurityLog:    NewAiAgentSecurityLogClient(cfg),
		AiAgentSecurityPolicy: NewAiAgentSecurityPolicyClient(cfg),
		AiChat:                NewAiChatClient(cfg),
		AiChatItem:            NewAiChatItemClient(cfg),
		AiModel:               NewAiModelClient(cfg),
		AiModelDetail:         NewAiModelDetailClient(cfg),
		AiModelUsage:          NewAiModelUsageClient(cfg),
		AtomicQuestions:       NewAtomicQuestionsClient(cfg),
		ClassificationFiles:   NewClassificationFilesClient(cfg),
		DefaultAgentAvatar:    NewDefaultAgentAvatarClient(cfg),
		ExternalModelUsage:    NewExternalModelUsageClient(cfg),
		KnowledgeBase:         NewKnowledgeBaseClient(cfg),
		KnowledgeBaseFile:     NewKnowledgeBaseFileClient(cfg),
		UserAgentOrder:        NewUserAgentOrderClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                   ctx,
		config:                cfg,
		AiAgent:               NewAiAgentClient(cfg),
		AiAgentSecurityLog:    NewAiAgentSecurityLogClient(cfg),
		AiAgentSecurityPolicy: NewAiAgentSecurityPolicyClient(cfg),
		AiChat:                NewAiChatClient(cfg),
		AiChatItem:            NewAiChatItemClient(cfg),
		AiModel:               NewAiModelClient(cfg),
		AiModelDetail:         NewAiModelDetailClient(cfg),
		AiModelUsage:          NewAiModelUsageClient(cfg),
		AtomicQuestions:       NewAtomicQuestionsClient(cfg),
		ClassificationFiles:   NewClassificationFilesClient(cfg),
		DefaultAgentAvatar:    NewDefaultAgentAvatarClient(cfg),
		ExternalModelUsage:    NewExternalModelUsageClient(cfg),
		KnowledgeBase:         NewKnowledgeBaseClient(cfg),
		KnowledgeBaseFile:     NewKnowledgeBaseFileClient(cfg),
		UserAgentOrder:        NewUserAgentOrderClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		AiAgent.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.AiAgent, c.AiAgentSecurityLog, c.AiAgentSecurityPolicy, c.AiChat,
		c.AiChatItem, c.AiModel, c.AiModelDetail, c.AiModelUsage, c.AtomicQuestions,
		c.ClassificationFiles, c.DefaultAgentAvatar, c.ExternalModelUsage,
		c.KnowledgeBase, c.KnowledgeBaseFile, c.UserAgentOrder,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.AiAgent, c.AiAgentSecurityLog, c.AiAgentSecurityPolicy, c.AiChat,
		c.AiChatItem, c.AiModel, c.AiModelDetail, c.AiModelUsage, c.AtomicQuestions,
		c.ClassificationFiles, c.DefaultAgentAvatar, c.ExternalModelUsage,
		c.KnowledgeBase, c.KnowledgeBaseFile, c.UserAgentOrder,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AiAgentMutation:
		return c.AiAgent.mutate(ctx, m)
	case *AiAgentSecurityLogMutation:
		return c.AiAgentSecurityLog.mutate(ctx, m)
	case *AiAgentSecurityPolicyMutation:
		return c.AiAgentSecurityPolicy.mutate(ctx, m)
	case *AiChatMutation:
		return c.AiChat.mutate(ctx, m)
	case *AiChatItemMutation:
		return c.AiChatItem.mutate(ctx, m)
	case *AiModelMutation:
		return c.AiModel.mutate(ctx, m)
	case *AiModelDetailMutation:
		return c.AiModelDetail.mutate(ctx, m)
	case *AiModelUsageMutation:
		return c.AiModelUsage.mutate(ctx, m)
	case *AtomicQuestionsMutation:
		return c.AtomicQuestions.mutate(ctx, m)
	case *ClassificationFilesMutation:
		return c.ClassificationFiles.mutate(ctx, m)
	case *DefaultAgentAvatarMutation:
		return c.DefaultAgentAvatar.mutate(ctx, m)
	case *ExternalModelUsageMutation:
		return c.ExternalModelUsage.mutate(ctx, m)
	case *KnowledgeBaseMutation:
		return c.KnowledgeBase.mutate(ctx, m)
	case *KnowledgeBaseFileMutation:
		return c.KnowledgeBaseFile.mutate(ctx, m)
	case *UserAgentOrderMutation:
		return c.UserAgentOrder.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// AiAgentClient is a client for the AiAgent schema.
type AiAgentClient struct {
	config
}

// NewAiAgentClient returns a client for the AiAgent from the given config.
func NewAiAgentClient(c config) *AiAgentClient {
	return &AiAgentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `aiagent.Hooks(f(g(h())))`.
func (c *AiAgentClient) Use(hooks ...Hook) {
	c.hooks.AiAgent = append(c.hooks.AiAgent, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `aiagent.Intercept(f(g(h())))`.
func (c *AiAgentClient) Intercept(interceptors ...Interceptor) {
	c.inters.AiAgent = append(c.inters.AiAgent, interceptors...)
}

// Create returns a builder for creating a AiAgent entity.
func (c *AiAgentClient) Create() *AiAgentCreate {
	mutation := newAiAgentMutation(c.config, OpCreate)
	return &AiAgentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AiAgent entities.
func (c *AiAgentClient) CreateBulk(builders ...*AiAgentCreate) *AiAgentCreateBulk {
	return &AiAgentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AiAgentClient) MapCreateBulk(slice any, setFunc func(*AiAgentCreate, int)) *AiAgentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AiAgentCreateBulk{err: fmt.Errorf("calling to AiAgentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AiAgentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AiAgentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AiAgent.
func (c *AiAgentClient) Update() *AiAgentUpdate {
	mutation := newAiAgentMutation(c.config, OpUpdate)
	return &AiAgentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AiAgentClient) UpdateOne(aa *AiAgent) *AiAgentUpdateOne {
	mutation := newAiAgentMutation(c.config, OpUpdateOne, withAiAgent(aa))
	return &AiAgentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AiAgentClient) UpdateOneID(id int64) *AiAgentUpdateOne {
	mutation := newAiAgentMutation(c.config, OpUpdateOne, withAiAgentID(id))
	return &AiAgentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AiAgent.
func (c *AiAgentClient) Delete() *AiAgentDelete {
	mutation := newAiAgentMutation(c.config, OpDelete)
	return &AiAgentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AiAgentClient) DeleteOne(aa *AiAgent) *AiAgentDeleteOne {
	return c.DeleteOneID(aa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AiAgentClient) DeleteOneID(id int64) *AiAgentDeleteOne {
	builder := c.Delete().Where(aiagent.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AiAgentDeleteOne{builder}
}

// Query returns a query builder for AiAgent.
func (c *AiAgentClient) Query() *AiAgentQuery {
	return &AiAgentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAiAgent},
		inters: c.Interceptors(),
	}
}

// Get returns a AiAgent entity by its id.
func (c *AiAgentClient) Get(ctx context.Context, id int64) (*AiAgent, error) {
	return c.Query().Where(aiagent.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AiAgentClient) GetX(ctx context.Context, id int64) *AiAgent {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryAiChat queries the ai_chat edge of a AiAgent.
func (c *AiAgentClient) QueryAiChat(aa *AiAgent) *AiChatQuery {
	query := (&AiChatClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := aa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(aiagent.Table, aiagent.FieldID, id),
			sqlgraph.To(aichat.Table, aichat.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, aiagent.AiChatTable, aiagent.AiChatColumn),
		)
		fromV = sqlgraph.Neighbors(aa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *AiAgentClient) Hooks() []Hook {
	hooks := c.hooks.AiAgent
	return append(hooks[:len(hooks):len(hooks)], aiagent.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AiAgentClient) Interceptors() []Interceptor {
	inters := c.inters.AiAgent
	return append(inters[:len(inters):len(inters)], aiagent.Interceptors[:]...)
}

func (c *AiAgentClient) mutate(ctx context.Context, m *AiAgentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AiAgentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AiAgentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AiAgentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AiAgentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AiAgent mutation op: %q", m.Op())
	}
}

// AiAgentSecurityLogClient is a client for the AiAgentSecurityLog schema.
type AiAgentSecurityLogClient struct {
	config
}

// NewAiAgentSecurityLogClient returns a client for the AiAgentSecurityLog from the given config.
func NewAiAgentSecurityLogClient(c config) *AiAgentSecurityLogClient {
	return &AiAgentSecurityLogClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `aiagentsecuritylog.Hooks(f(g(h())))`.
func (c *AiAgentSecurityLogClient) Use(hooks ...Hook) {
	c.hooks.AiAgentSecurityLog = append(c.hooks.AiAgentSecurityLog, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `aiagentsecuritylog.Intercept(f(g(h())))`.
func (c *AiAgentSecurityLogClient) Intercept(interceptors ...Interceptor) {
	c.inters.AiAgentSecurityLog = append(c.inters.AiAgentSecurityLog, interceptors...)
}

// Create returns a builder for creating a AiAgentSecurityLog entity.
func (c *AiAgentSecurityLogClient) Create() *AiAgentSecurityLogCreate {
	mutation := newAiAgentSecurityLogMutation(c.config, OpCreate)
	return &AiAgentSecurityLogCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AiAgentSecurityLog entities.
func (c *AiAgentSecurityLogClient) CreateBulk(builders ...*AiAgentSecurityLogCreate) *AiAgentSecurityLogCreateBulk {
	return &AiAgentSecurityLogCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AiAgentSecurityLogClient) MapCreateBulk(slice any, setFunc func(*AiAgentSecurityLogCreate, int)) *AiAgentSecurityLogCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AiAgentSecurityLogCreateBulk{err: fmt.Errorf("calling to AiAgentSecurityLogClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AiAgentSecurityLogCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AiAgentSecurityLogCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AiAgentSecurityLog.
func (c *AiAgentSecurityLogClient) Update() *AiAgentSecurityLogUpdate {
	mutation := newAiAgentSecurityLogMutation(c.config, OpUpdate)
	return &AiAgentSecurityLogUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AiAgentSecurityLogClient) UpdateOne(aasl *AiAgentSecurityLog) *AiAgentSecurityLogUpdateOne {
	mutation := newAiAgentSecurityLogMutation(c.config, OpUpdateOne, withAiAgentSecurityLog(aasl))
	return &AiAgentSecurityLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AiAgentSecurityLogClient) UpdateOneID(id int64) *AiAgentSecurityLogUpdateOne {
	mutation := newAiAgentSecurityLogMutation(c.config, OpUpdateOne, withAiAgentSecurityLogID(id))
	return &AiAgentSecurityLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AiAgentSecurityLog.
func (c *AiAgentSecurityLogClient) Delete() *AiAgentSecurityLogDelete {
	mutation := newAiAgentSecurityLogMutation(c.config, OpDelete)
	return &AiAgentSecurityLogDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AiAgentSecurityLogClient) DeleteOne(aasl *AiAgentSecurityLog) *AiAgentSecurityLogDeleteOne {
	return c.DeleteOneID(aasl.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AiAgentSecurityLogClient) DeleteOneID(id int64) *AiAgentSecurityLogDeleteOne {
	builder := c.Delete().Where(aiagentsecuritylog.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AiAgentSecurityLogDeleteOne{builder}
}

// Query returns a query builder for AiAgentSecurityLog.
func (c *AiAgentSecurityLogClient) Query() *AiAgentSecurityLogQuery {
	return &AiAgentSecurityLogQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAiAgentSecurityLog},
		inters: c.Interceptors(),
	}
}

// Get returns a AiAgentSecurityLog entity by its id.
func (c *AiAgentSecurityLogClient) Get(ctx context.Context, id int64) (*AiAgentSecurityLog, error) {
	return c.Query().Where(aiagentsecuritylog.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AiAgentSecurityLogClient) GetX(ctx context.Context, id int64) *AiAgentSecurityLog {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AiAgentSecurityLogClient) Hooks() []Hook {
	hooks := c.hooks.AiAgentSecurityLog
	return append(hooks[:len(hooks):len(hooks)], aiagentsecuritylog.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AiAgentSecurityLogClient) Interceptors() []Interceptor {
	inters := c.inters.AiAgentSecurityLog
	return append(inters[:len(inters):len(inters)], aiagentsecuritylog.Interceptors[:]...)
}

func (c *AiAgentSecurityLogClient) mutate(ctx context.Context, m *AiAgentSecurityLogMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AiAgentSecurityLogCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AiAgentSecurityLogUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AiAgentSecurityLogUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AiAgentSecurityLogDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AiAgentSecurityLog mutation op: %q", m.Op())
	}
}

// AiAgentSecurityPolicyClient is a client for the AiAgentSecurityPolicy schema.
type AiAgentSecurityPolicyClient struct {
	config
}

// NewAiAgentSecurityPolicyClient returns a client for the AiAgentSecurityPolicy from the given config.
func NewAiAgentSecurityPolicyClient(c config) *AiAgentSecurityPolicyClient {
	return &AiAgentSecurityPolicyClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `aiagentsecuritypolicy.Hooks(f(g(h())))`.
func (c *AiAgentSecurityPolicyClient) Use(hooks ...Hook) {
	c.hooks.AiAgentSecurityPolicy = append(c.hooks.AiAgentSecurityPolicy, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `aiagentsecuritypolicy.Intercept(f(g(h())))`.
func (c *AiAgentSecurityPolicyClient) Intercept(interceptors ...Interceptor) {
	c.inters.AiAgentSecurityPolicy = append(c.inters.AiAgentSecurityPolicy, interceptors...)
}

// Create returns a builder for creating a AiAgentSecurityPolicy entity.
func (c *AiAgentSecurityPolicyClient) Create() *AiAgentSecurityPolicyCreate {
	mutation := newAiAgentSecurityPolicyMutation(c.config, OpCreate)
	return &AiAgentSecurityPolicyCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AiAgentSecurityPolicy entities.
func (c *AiAgentSecurityPolicyClient) CreateBulk(builders ...*AiAgentSecurityPolicyCreate) *AiAgentSecurityPolicyCreateBulk {
	return &AiAgentSecurityPolicyCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AiAgentSecurityPolicyClient) MapCreateBulk(slice any, setFunc func(*AiAgentSecurityPolicyCreate, int)) *AiAgentSecurityPolicyCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AiAgentSecurityPolicyCreateBulk{err: fmt.Errorf("calling to AiAgentSecurityPolicyClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AiAgentSecurityPolicyCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AiAgentSecurityPolicyCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AiAgentSecurityPolicy.
func (c *AiAgentSecurityPolicyClient) Update() *AiAgentSecurityPolicyUpdate {
	mutation := newAiAgentSecurityPolicyMutation(c.config, OpUpdate)
	return &AiAgentSecurityPolicyUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AiAgentSecurityPolicyClient) UpdateOne(aasp *AiAgentSecurityPolicy) *AiAgentSecurityPolicyUpdateOne {
	mutation := newAiAgentSecurityPolicyMutation(c.config, OpUpdateOne, withAiAgentSecurityPolicy(aasp))
	return &AiAgentSecurityPolicyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AiAgentSecurityPolicyClient) UpdateOneID(id int64) *AiAgentSecurityPolicyUpdateOne {
	mutation := newAiAgentSecurityPolicyMutation(c.config, OpUpdateOne, withAiAgentSecurityPolicyID(id))
	return &AiAgentSecurityPolicyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AiAgentSecurityPolicy.
func (c *AiAgentSecurityPolicyClient) Delete() *AiAgentSecurityPolicyDelete {
	mutation := newAiAgentSecurityPolicyMutation(c.config, OpDelete)
	return &AiAgentSecurityPolicyDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AiAgentSecurityPolicyClient) DeleteOne(aasp *AiAgentSecurityPolicy) *AiAgentSecurityPolicyDeleteOne {
	return c.DeleteOneID(aasp.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AiAgentSecurityPolicyClient) DeleteOneID(id int64) *AiAgentSecurityPolicyDeleteOne {
	builder := c.Delete().Where(aiagentsecuritypolicy.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AiAgentSecurityPolicyDeleteOne{builder}
}

// Query returns a query builder for AiAgentSecurityPolicy.
func (c *AiAgentSecurityPolicyClient) Query() *AiAgentSecurityPolicyQuery {
	return &AiAgentSecurityPolicyQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAiAgentSecurityPolicy},
		inters: c.Interceptors(),
	}
}

// Get returns a AiAgentSecurityPolicy entity by its id.
func (c *AiAgentSecurityPolicyClient) Get(ctx context.Context, id int64) (*AiAgentSecurityPolicy, error) {
	return c.Query().Where(aiagentsecuritypolicy.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AiAgentSecurityPolicyClient) GetX(ctx context.Context, id int64) *AiAgentSecurityPolicy {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AiAgentSecurityPolicyClient) Hooks() []Hook {
	hooks := c.hooks.AiAgentSecurityPolicy
	return append(hooks[:len(hooks):len(hooks)], aiagentsecuritypolicy.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AiAgentSecurityPolicyClient) Interceptors() []Interceptor {
	inters := c.inters.AiAgentSecurityPolicy
	return append(inters[:len(inters):len(inters)], aiagentsecuritypolicy.Interceptors[:]...)
}

func (c *AiAgentSecurityPolicyClient) mutate(ctx context.Context, m *AiAgentSecurityPolicyMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AiAgentSecurityPolicyCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AiAgentSecurityPolicyUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AiAgentSecurityPolicyUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AiAgentSecurityPolicyDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AiAgentSecurityPolicy mutation op: %q", m.Op())
	}
}

// AiChatClient is a client for the AiChat schema.
type AiChatClient struct {
	config
}

// NewAiChatClient returns a client for the AiChat from the given config.
func NewAiChatClient(c config) *AiChatClient {
	return &AiChatClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `aichat.Hooks(f(g(h())))`.
func (c *AiChatClient) Use(hooks ...Hook) {
	c.hooks.AiChat = append(c.hooks.AiChat, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `aichat.Intercept(f(g(h())))`.
func (c *AiChatClient) Intercept(interceptors ...Interceptor) {
	c.inters.AiChat = append(c.inters.AiChat, interceptors...)
}

// Create returns a builder for creating a AiChat entity.
func (c *AiChatClient) Create() *AiChatCreate {
	mutation := newAiChatMutation(c.config, OpCreate)
	return &AiChatCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AiChat entities.
func (c *AiChatClient) CreateBulk(builders ...*AiChatCreate) *AiChatCreateBulk {
	return &AiChatCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AiChatClient) MapCreateBulk(slice any, setFunc func(*AiChatCreate, int)) *AiChatCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AiChatCreateBulk{err: fmt.Errorf("calling to AiChatClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AiChatCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AiChatCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AiChat.
func (c *AiChatClient) Update() *AiChatUpdate {
	mutation := newAiChatMutation(c.config, OpUpdate)
	return &AiChatUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AiChatClient) UpdateOne(ac *AiChat) *AiChatUpdateOne {
	mutation := newAiChatMutation(c.config, OpUpdateOne, withAiChat(ac))
	return &AiChatUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AiChatClient) UpdateOneID(id int64) *AiChatUpdateOne {
	mutation := newAiChatMutation(c.config, OpUpdateOne, withAiChatID(id))
	return &AiChatUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AiChat.
func (c *AiChatClient) Delete() *AiChatDelete {
	mutation := newAiChatMutation(c.config, OpDelete)
	return &AiChatDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AiChatClient) DeleteOne(ac *AiChat) *AiChatDeleteOne {
	return c.DeleteOneID(ac.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AiChatClient) DeleteOneID(id int64) *AiChatDeleteOne {
	builder := c.Delete().Where(aichat.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AiChatDeleteOne{builder}
}

// Query returns a query builder for AiChat.
func (c *AiChatClient) Query() *AiChatQuery {
	return &AiChatQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAiChat},
		inters: c.Interceptors(),
	}
}

// Get returns a AiChat entity by its id.
func (c *AiChatClient) Get(ctx context.Context, id int64) (*AiChat, error) {
	return c.Query().Where(aichat.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AiChatClient) GetX(ctx context.Context, id int64) *AiChat {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryAiAgent queries the ai_agent edge of a AiChat.
func (c *AiChatClient) QueryAiAgent(ac *AiChat) *AiAgentQuery {
	query := (&AiAgentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := ac.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(aichat.Table, aichat.FieldID, id),
			sqlgraph.To(aiagent.Table, aiagent.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, aichat.AiAgentTable, aichat.AiAgentColumn),
		)
		fromV = sqlgraph.Neighbors(ac.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *AiChatClient) Hooks() []Hook {
	hooks := c.hooks.AiChat
	return append(hooks[:len(hooks):len(hooks)], aichat.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AiChatClient) Interceptors() []Interceptor {
	inters := c.inters.AiChat
	return append(inters[:len(inters):len(inters)], aichat.Interceptors[:]...)
}

func (c *AiChatClient) mutate(ctx context.Context, m *AiChatMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AiChatCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AiChatUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AiChatUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AiChatDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AiChat mutation op: %q", m.Op())
	}
}

// AiChatItemClient is a client for the AiChatItem schema.
type AiChatItemClient struct {
	config
}

// NewAiChatItemClient returns a client for the AiChatItem from the given config.
func NewAiChatItemClient(c config) *AiChatItemClient {
	return &AiChatItemClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `aichatitem.Hooks(f(g(h())))`.
func (c *AiChatItemClient) Use(hooks ...Hook) {
	c.hooks.AiChatItem = append(c.hooks.AiChatItem, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `aichatitem.Intercept(f(g(h())))`.
func (c *AiChatItemClient) Intercept(interceptors ...Interceptor) {
	c.inters.AiChatItem = append(c.inters.AiChatItem, interceptors...)
}

// Create returns a builder for creating a AiChatItem entity.
func (c *AiChatItemClient) Create() *AiChatItemCreate {
	mutation := newAiChatItemMutation(c.config, OpCreate)
	return &AiChatItemCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AiChatItem entities.
func (c *AiChatItemClient) CreateBulk(builders ...*AiChatItemCreate) *AiChatItemCreateBulk {
	return &AiChatItemCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AiChatItemClient) MapCreateBulk(slice any, setFunc func(*AiChatItemCreate, int)) *AiChatItemCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AiChatItemCreateBulk{err: fmt.Errorf("calling to AiChatItemClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AiChatItemCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AiChatItemCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AiChatItem.
func (c *AiChatItemClient) Update() *AiChatItemUpdate {
	mutation := newAiChatItemMutation(c.config, OpUpdate)
	return &AiChatItemUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AiChatItemClient) UpdateOne(aci *AiChatItem) *AiChatItemUpdateOne {
	mutation := newAiChatItemMutation(c.config, OpUpdateOne, withAiChatItem(aci))
	return &AiChatItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AiChatItemClient) UpdateOneID(id int64) *AiChatItemUpdateOne {
	mutation := newAiChatItemMutation(c.config, OpUpdateOne, withAiChatItemID(id))
	return &AiChatItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AiChatItem.
func (c *AiChatItemClient) Delete() *AiChatItemDelete {
	mutation := newAiChatItemMutation(c.config, OpDelete)
	return &AiChatItemDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AiChatItemClient) DeleteOne(aci *AiChatItem) *AiChatItemDeleteOne {
	return c.DeleteOneID(aci.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AiChatItemClient) DeleteOneID(id int64) *AiChatItemDeleteOne {
	builder := c.Delete().Where(aichatitem.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AiChatItemDeleteOne{builder}
}

// Query returns a query builder for AiChatItem.
func (c *AiChatItemClient) Query() *AiChatItemQuery {
	return &AiChatItemQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAiChatItem},
		inters: c.Interceptors(),
	}
}

// Get returns a AiChatItem entity by its id.
func (c *AiChatItemClient) Get(ctx context.Context, id int64) (*AiChatItem, error) {
	return c.Query().Where(aichatitem.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AiChatItemClient) GetX(ctx context.Context, id int64) *AiChatItem {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AiChatItemClient) Hooks() []Hook {
	hooks := c.hooks.AiChatItem
	return append(hooks[:len(hooks):len(hooks)], aichatitem.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AiChatItemClient) Interceptors() []Interceptor {
	inters := c.inters.AiChatItem
	return append(inters[:len(inters):len(inters)], aichatitem.Interceptors[:]...)
}

func (c *AiChatItemClient) mutate(ctx context.Context, m *AiChatItemMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AiChatItemCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AiChatItemUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AiChatItemUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AiChatItemDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AiChatItem mutation op: %q", m.Op())
	}
}

// AiModelClient is a client for the AiModel schema.
type AiModelClient struct {
	config
}

// NewAiModelClient returns a client for the AiModel from the given config.
func NewAiModelClient(c config) *AiModelClient {
	return &AiModelClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `aimodel.Hooks(f(g(h())))`.
func (c *AiModelClient) Use(hooks ...Hook) {
	c.hooks.AiModel = append(c.hooks.AiModel, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `aimodel.Intercept(f(g(h())))`.
func (c *AiModelClient) Intercept(interceptors ...Interceptor) {
	c.inters.AiModel = append(c.inters.AiModel, interceptors...)
}

// Create returns a builder for creating a AiModel entity.
func (c *AiModelClient) Create() *AiModelCreate {
	mutation := newAiModelMutation(c.config, OpCreate)
	return &AiModelCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AiModel entities.
func (c *AiModelClient) CreateBulk(builders ...*AiModelCreate) *AiModelCreateBulk {
	return &AiModelCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AiModelClient) MapCreateBulk(slice any, setFunc func(*AiModelCreate, int)) *AiModelCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AiModelCreateBulk{err: fmt.Errorf("calling to AiModelClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AiModelCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AiModelCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AiModel.
func (c *AiModelClient) Update() *AiModelUpdate {
	mutation := newAiModelMutation(c.config, OpUpdate)
	return &AiModelUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AiModelClient) UpdateOne(am *AiModel) *AiModelUpdateOne {
	mutation := newAiModelMutation(c.config, OpUpdateOne, withAiModel(am))
	return &AiModelUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AiModelClient) UpdateOneID(id int64) *AiModelUpdateOne {
	mutation := newAiModelMutation(c.config, OpUpdateOne, withAiModelID(id))
	return &AiModelUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AiModel.
func (c *AiModelClient) Delete() *AiModelDelete {
	mutation := newAiModelMutation(c.config, OpDelete)
	return &AiModelDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AiModelClient) DeleteOne(am *AiModel) *AiModelDeleteOne {
	return c.DeleteOneID(am.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AiModelClient) DeleteOneID(id int64) *AiModelDeleteOne {
	builder := c.Delete().Where(aimodel.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AiModelDeleteOne{builder}
}

// Query returns a query builder for AiModel.
func (c *AiModelClient) Query() *AiModelQuery {
	return &AiModelQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAiModel},
		inters: c.Interceptors(),
	}
}

// Get returns a AiModel entity by its id.
func (c *AiModelClient) Get(ctx context.Context, id int64) (*AiModel, error) {
	return c.Query().Where(aimodel.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AiModelClient) GetX(ctx context.Context, id int64) *AiModel {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AiModelClient) Hooks() []Hook {
	hooks := c.hooks.AiModel
	return append(hooks[:len(hooks):len(hooks)], aimodel.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AiModelClient) Interceptors() []Interceptor {
	inters := c.inters.AiModel
	return append(inters[:len(inters):len(inters)], aimodel.Interceptors[:]...)
}

func (c *AiModelClient) mutate(ctx context.Context, m *AiModelMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AiModelCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AiModelUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AiModelUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AiModelDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AiModel mutation op: %q", m.Op())
	}
}

// AiModelDetailClient is a client for the AiModelDetail schema.
type AiModelDetailClient struct {
	config
}

// NewAiModelDetailClient returns a client for the AiModelDetail from the given config.
func NewAiModelDetailClient(c config) *AiModelDetailClient {
	return &AiModelDetailClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `aimodeldetail.Hooks(f(g(h())))`.
func (c *AiModelDetailClient) Use(hooks ...Hook) {
	c.hooks.AiModelDetail = append(c.hooks.AiModelDetail, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `aimodeldetail.Intercept(f(g(h())))`.
func (c *AiModelDetailClient) Intercept(interceptors ...Interceptor) {
	c.inters.AiModelDetail = append(c.inters.AiModelDetail, interceptors...)
}

// Create returns a builder for creating a AiModelDetail entity.
func (c *AiModelDetailClient) Create() *AiModelDetailCreate {
	mutation := newAiModelDetailMutation(c.config, OpCreate)
	return &AiModelDetailCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AiModelDetail entities.
func (c *AiModelDetailClient) CreateBulk(builders ...*AiModelDetailCreate) *AiModelDetailCreateBulk {
	return &AiModelDetailCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AiModelDetailClient) MapCreateBulk(slice any, setFunc func(*AiModelDetailCreate, int)) *AiModelDetailCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AiModelDetailCreateBulk{err: fmt.Errorf("calling to AiModelDetailClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AiModelDetailCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AiModelDetailCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AiModelDetail.
func (c *AiModelDetailClient) Update() *AiModelDetailUpdate {
	mutation := newAiModelDetailMutation(c.config, OpUpdate)
	return &AiModelDetailUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AiModelDetailClient) UpdateOne(amd *AiModelDetail) *AiModelDetailUpdateOne {
	mutation := newAiModelDetailMutation(c.config, OpUpdateOne, withAiModelDetail(amd))
	return &AiModelDetailUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AiModelDetailClient) UpdateOneID(id int64) *AiModelDetailUpdateOne {
	mutation := newAiModelDetailMutation(c.config, OpUpdateOne, withAiModelDetailID(id))
	return &AiModelDetailUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AiModelDetail.
func (c *AiModelDetailClient) Delete() *AiModelDetailDelete {
	mutation := newAiModelDetailMutation(c.config, OpDelete)
	return &AiModelDetailDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AiModelDetailClient) DeleteOne(amd *AiModelDetail) *AiModelDetailDeleteOne {
	return c.DeleteOneID(amd.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AiModelDetailClient) DeleteOneID(id int64) *AiModelDetailDeleteOne {
	builder := c.Delete().Where(aimodeldetail.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AiModelDetailDeleteOne{builder}
}

// Query returns a query builder for AiModelDetail.
func (c *AiModelDetailClient) Query() *AiModelDetailQuery {
	return &AiModelDetailQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAiModelDetail},
		inters: c.Interceptors(),
	}
}

// Get returns a AiModelDetail entity by its id.
func (c *AiModelDetailClient) Get(ctx context.Context, id int64) (*AiModelDetail, error) {
	return c.Query().Where(aimodeldetail.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AiModelDetailClient) GetX(ctx context.Context, id int64) *AiModelDetail {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AiModelDetailClient) Hooks() []Hook {
	hooks := c.hooks.AiModelDetail
	return append(hooks[:len(hooks):len(hooks)], aimodeldetail.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AiModelDetailClient) Interceptors() []Interceptor {
	inters := c.inters.AiModelDetail
	return append(inters[:len(inters):len(inters)], aimodeldetail.Interceptors[:]...)
}

func (c *AiModelDetailClient) mutate(ctx context.Context, m *AiModelDetailMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AiModelDetailCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AiModelDetailUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AiModelDetailUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AiModelDetailDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AiModelDetail mutation op: %q", m.Op())
	}
}

// AiModelUsageClient is a client for the AiModelUsage schema.
type AiModelUsageClient struct {
	config
}

// NewAiModelUsageClient returns a client for the AiModelUsage from the given config.
func NewAiModelUsageClient(c config) *AiModelUsageClient {
	return &AiModelUsageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `aimodelusage.Hooks(f(g(h())))`.
func (c *AiModelUsageClient) Use(hooks ...Hook) {
	c.hooks.AiModelUsage = append(c.hooks.AiModelUsage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `aimodelusage.Intercept(f(g(h())))`.
func (c *AiModelUsageClient) Intercept(interceptors ...Interceptor) {
	c.inters.AiModelUsage = append(c.inters.AiModelUsage, interceptors...)
}

// Create returns a builder for creating a AiModelUsage entity.
func (c *AiModelUsageClient) Create() *AiModelUsageCreate {
	mutation := newAiModelUsageMutation(c.config, OpCreate)
	return &AiModelUsageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AiModelUsage entities.
func (c *AiModelUsageClient) CreateBulk(builders ...*AiModelUsageCreate) *AiModelUsageCreateBulk {
	return &AiModelUsageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AiModelUsageClient) MapCreateBulk(slice any, setFunc func(*AiModelUsageCreate, int)) *AiModelUsageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AiModelUsageCreateBulk{err: fmt.Errorf("calling to AiModelUsageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AiModelUsageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AiModelUsageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AiModelUsage.
func (c *AiModelUsageClient) Update() *AiModelUsageUpdate {
	mutation := newAiModelUsageMutation(c.config, OpUpdate)
	return &AiModelUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AiModelUsageClient) UpdateOne(amu *AiModelUsage) *AiModelUsageUpdateOne {
	mutation := newAiModelUsageMutation(c.config, OpUpdateOne, withAiModelUsage(amu))
	return &AiModelUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AiModelUsageClient) UpdateOneID(id int64) *AiModelUsageUpdateOne {
	mutation := newAiModelUsageMutation(c.config, OpUpdateOne, withAiModelUsageID(id))
	return &AiModelUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AiModelUsage.
func (c *AiModelUsageClient) Delete() *AiModelUsageDelete {
	mutation := newAiModelUsageMutation(c.config, OpDelete)
	return &AiModelUsageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AiModelUsageClient) DeleteOne(amu *AiModelUsage) *AiModelUsageDeleteOne {
	return c.DeleteOneID(amu.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AiModelUsageClient) DeleteOneID(id int64) *AiModelUsageDeleteOne {
	builder := c.Delete().Where(aimodelusage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AiModelUsageDeleteOne{builder}
}

// Query returns a query builder for AiModelUsage.
func (c *AiModelUsageClient) Query() *AiModelUsageQuery {
	return &AiModelUsageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAiModelUsage},
		inters: c.Interceptors(),
	}
}

// Get returns a AiModelUsage entity by its id.
func (c *AiModelUsageClient) Get(ctx context.Context, id int64) (*AiModelUsage, error) {
	return c.Query().Where(aimodelusage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AiModelUsageClient) GetX(ctx context.Context, id int64) *AiModelUsage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AiModelUsageClient) Hooks() []Hook {
	hooks := c.hooks.AiModelUsage
	return append(hooks[:len(hooks):len(hooks)], aimodelusage.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *AiModelUsageClient) Interceptors() []Interceptor {
	inters := c.inters.AiModelUsage
	return append(inters[:len(inters):len(inters)], aimodelusage.Interceptors[:]...)
}

func (c *AiModelUsageClient) mutate(ctx context.Context, m *AiModelUsageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AiModelUsageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AiModelUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AiModelUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AiModelUsageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AiModelUsage mutation op: %q", m.Op())
	}
}

// AtomicQuestionsClient is a client for the AtomicQuestions schema.
type AtomicQuestionsClient struct {
	config
}

// NewAtomicQuestionsClient returns a client for the AtomicQuestions from the given config.
func NewAtomicQuestionsClient(c config) *AtomicQuestionsClient {
	return &AtomicQuestionsClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `atomicquestions.Hooks(f(g(h())))`.
func (c *AtomicQuestionsClient) Use(hooks ...Hook) {
	c.hooks.AtomicQuestions = append(c.hooks.AtomicQuestions, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `atomicquestions.Intercept(f(g(h())))`.
func (c *AtomicQuestionsClient) Intercept(interceptors ...Interceptor) {
	c.inters.AtomicQuestions = append(c.inters.AtomicQuestions, interceptors...)
}

// Create returns a builder for creating a AtomicQuestions entity.
func (c *AtomicQuestionsClient) Create() *AtomicQuestionsCreate {
	mutation := newAtomicQuestionsMutation(c.config, OpCreate)
	return &AtomicQuestionsCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AtomicQuestions entities.
func (c *AtomicQuestionsClient) CreateBulk(builders ...*AtomicQuestionsCreate) *AtomicQuestionsCreateBulk {
	return &AtomicQuestionsCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AtomicQuestionsClient) MapCreateBulk(slice any, setFunc func(*AtomicQuestionsCreate, int)) *AtomicQuestionsCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AtomicQuestionsCreateBulk{err: fmt.Errorf("calling to AtomicQuestionsClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AtomicQuestionsCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AtomicQuestionsCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AtomicQuestions.
func (c *AtomicQuestionsClient) Update() *AtomicQuestionsUpdate {
	mutation := newAtomicQuestionsMutation(c.config, OpUpdate)
	return &AtomicQuestionsUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AtomicQuestionsClient) UpdateOne(aq *AtomicQuestions) *AtomicQuestionsUpdateOne {
	mutation := newAtomicQuestionsMutation(c.config, OpUpdateOne, withAtomicQuestions(aq))
	return &AtomicQuestionsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AtomicQuestionsClient) UpdateOneID(id int64) *AtomicQuestionsUpdateOne {
	mutation := newAtomicQuestionsMutation(c.config, OpUpdateOne, withAtomicQuestionsID(id))
	return &AtomicQuestionsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AtomicQuestions.
func (c *AtomicQuestionsClient) Delete() *AtomicQuestionsDelete {
	mutation := newAtomicQuestionsMutation(c.config, OpDelete)
	return &AtomicQuestionsDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AtomicQuestionsClient) DeleteOne(aq *AtomicQuestions) *AtomicQuestionsDeleteOne {
	return c.DeleteOneID(aq.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AtomicQuestionsClient) DeleteOneID(id int64) *AtomicQuestionsDeleteOne {
	builder := c.Delete().Where(atomicquestions.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AtomicQuestionsDeleteOne{builder}
}

// Query returns a query builder for AtomicQuestions.
func (c *AtomicQuestionsClient) Query() *AtomicQuestionsQuery {
	return &AtomicQuestionsQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAtomicQuestions},
		inters: c.Interceptors(),
	}
}

// Get returns a AtomicQuestions entity by its id.
func (c *AtomicQuestionsClient) Get(ctx context.Context, id int64) (*AtomicQuestions, error) {
	return c.Query().Where(atomicquestions.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AtomicQuestionsClient) GetX(ctx context.Context, id int64) *AtomicQuestions {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AtomicQuestionsClient) Hooks() []Hook {
	return c.hooks.AtomicQuestions
}

// Interceptors returns the client interceptors.
func (c *AtomicQuestionsClient) Interceptors() []Interceptor {
	return c.inters.AtomicQuestions
}

func (c *AtomicQuestionsClient) mutate(ctx context.Context, m *AtomicQuestionsMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AtomicQuestionsCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AtomicQuestionsUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AtomicQuestionsUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AtomicQuestionsDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AtomicQuestions mutation op: %q", m.Op())
	}
}

// ClassificationFilesClient is a client for the ClassificationFiles schema.
type ClassificationFilesClient struct {
	config
}

// NewClassificationFilesClient returns a client for the ClassificationFiles from the given config.
func NewClassificationFilesClient(c config) *ClassificationFilesClient {
	return &ClassificationFilesClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `classificationfiles.Hooks(f(g(h())))`.
func (c *ClassificationFilesClient) Use(hooks ...Hook) {
	c.hooks.ClassificationFiles = append(c.hooks.ClassificationFiles, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `classificationfiles.Intercept(f(g(h())))`.
func (c *ClassificationFilesClient) Intercept(interceptors ...Interceptor) {
	c.inters.ClassificationFiles = append(c.inters.ClassificationFiles, interceptors...)
}

// Create returns a builder for creating a ClassificationFiles entity.
func (c *ClassificationFilesClient) Create() *ClassificationFilesCreate {
	mutation := newClassificationFilesMutation(c.config, OpCreate)
	return &ClassificationFilesCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ClassificationFiles entities.
func (c *ClassificationFilesClient) CreateBulk(builders ...*ClassificationFilesCreate) *ClassificationFilesCreateBulk {
	return &ClassificationFilesCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ClassificationFilesClient) MapCreateBulk(slice any, setFunc func(*ClassificationFilesCreate, int)) *ClassificationFilesCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ClassificationFilesCreateBulk{err: fmt.Errorf("calling to ClassificationFilesClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ClassificationFilesCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ClassificationFilesCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ClassificationFiles.
func (c *ClassificationFilesClient) Update() *ClassificationFilesUpdate {
	mutation := newClassificationFilesMutation(c.config, OpUpdate)
	return &ClassificationFilesUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ClassificationFilesClient) UpdateOne(cf *ClassificationFiles) *ClassificationFilesUpdateOne {
	mutation := newClassificationFilesMutation(c.config, OpUpdateOne, withClassificationFiles(cf))
	return &ClassificationFilesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ClassificationFilesClient) UpdateOneID(id int64) *ClassificationFilesUpdateOne {
	mutation := newClassificationFilesMutation(c.config, OpUpdateOne, withClassificationFilesID(id))
	return &ClassificationFilesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ClassificationFiles.
func (c *ClassificationFilesClient) Delete() *ClassificationFilesDelete {
	mutation := newClassificationFilesMutation(c.config, OpDelete)
	return &ClassificationFilesDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ClassificationFilesClient) DeleteOne(cf *ClassificationFiles) *ClassificationFilesDeleteOne {
	return c.DeleteOneID(cf.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ClassificationFilesClient) DeleteOneID(id int64) *ClassificationFilesDeleteOne {
	builder := c.Delete().Where(classificationfiles.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ClassificationFilesDeleteOne{builder}
}

// Query returns a query builder for ClassificationFiles.
func (c *ClassificationFilesClient) Query() *ClassificationFilesQuery {
	return &ClassificationFilesQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeClassificationFiles},
		inters: c.Interceptors(),
	}
}

// Get returns a ClassificationFiles entity by its id.
func (c *ClassificationFilesClient) Get(ctx context.Context, id int64) (*ClassificationFiles, error) {
	return c.Query().Where(classificationfiles.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ClassificationFilesClient) GetX(ctx context.Context, id int64) *ClassificationFiles {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ClassificationFilesClient) Hooks() []Hook {
	return c.hooks.ClassificationFiles
}

// Interceptors returns the client interceptors.
func (c *ClassificationFilesClient) Interceptors() []Interceptor {
	return c.inters.ClassificationFiles
}

func (c *ClassificationFilesClient) mutate(ctx context.Context, m *ClassificationFilesMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ClassificationFilesCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ClassificationFilesUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ClassificationFilesUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ClassificationFilesDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown ClassificationFiles mutation op: %q", m.Op())
	}
}

// DefaultAgentAvatarClient is a client for the DefaultAgentAvatar schema.
type DefaultAgentAvatarClient struct {
	config
}

// NewDefaultAgentAvatarClient returns a client for the DefaultAgentAvatar from the given config.
func NewDefaultAgentAvatarClient(c config) *DefaultAgentAvatarClient {
	return &DefaultAgentAvatarClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `defaultagentavatar.Hooks(f(g(h())))`.
func (c *DefaultAgentAvatarClient) Use(hooks ...Hook) {
	c.hooks.DefaultAgentAvatar = append(c.hooks.DefaultAgentAvatar, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `defaultagentavatar.Intercept(f(g(h())))`.
func (c *DefaultAgentAvatarClient) Intercept(interceptors ...Interceptor) {
	c.inters.DefaultAgentAvatar = append(c.inters.DefaultAgentAvatar, interceptors...)
}

// Create returns a builder for creating a DefaultAgentAvatar entity.
func (c *DefaultAgentAvatarClient) Create() *DefaultAgentAvatarCreate {
	mutation := newDefaultAgentAvatarMutation(c.config, OpCreate)
	return &DefaultAgentAvatarCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of DefaultAgentAvatar entities.
func (c *DefaultAgentAvatarClient) CreateBulk(builders ...*DefaultAgentAvatarCreate) *DefaultAgentAvatarCreateBulk {
	return &DefaultAgentAvatarCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *DefaultAgentAvatarClient) MapCreateBulk(slice any, setFunc func(*DefaultAgentAvatarCreate, int)) *DefaultAgentAvatarCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &DefaultAgentAvatarCreateBulk{err: fmt.Errorf("calling to DefaultAgentAvatarClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*DefaultAgentAvatarCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &DefaultAgentAvatarCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for DefaultAgentAvatar.
func (c *DefaultAgentAvatarClient) Update() *DefaultAgentAvatarUpdate {
	mutation := newDefaultAgentAvatarMutation(c.config, OpUpdate)
	return &DefaultAgentAvatarUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *DefaultAgentAvatarClient) UpdateOne(daa *DefaultAgentAvatar) *DefaultAgentAvatarUpdateOne {
	mutation := newDefaultAgentAvatarMutation(c.config, OpUpdateOne, withDefaultAgentAvatar(daa))
	return &DefaultAgentAvatarUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *DefaultAgentAvatarClient) UpdateOneID(id int64) *DefaultAgentAvatarUpdateOne {
	mutation := newDefaultAgentAvatarMutation(c.config, OpUpdateOne, withDefaultAgentAvatarID(id))
	return &DefaultAgentAvatarUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for DefaultAgentAvatar.
func (c *DefaultAgentAvatarClient) Delete() *DefaultAgentAvatarDelete {
	mutation := newDefaultAgentAvatarMutation(c.config, OpDelete)
	return &DefaultAgentAvatarDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *DefaultAgentAvatarClient) DeleteOne(daa *DefaultAgentAvatar) *DefaultAgentAvatarDeleteOne {
	return c.DeleteOneID(daa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *DefaultAgentAvatarClient) DeleteOneID(id int64) *DefaultAgentAvatarDeleteOne {
	builder := c.Delete().Where(defaultagentavatar.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &DefaultAgentAvatarDeleteOne{builder}
}

// Query returns a query builder for DefaultAgentAvatar.
func (c *DefaultAgentAvatarClient) Query() *DefaultAgentAvatarQuery {
	return &DefaultAgentAvatarQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeDefaultAgentAvatar},
		inters: c.Interceptors(),
	}
}

// Get returns a DefaultAgentAvatar entity by its id.
func (c *DefaultAgentAvatarClient) Get(ctx context.Context, id int64) (*DefaultAgentAvatar, error) {
	return c.Query().Where(defaultagentavatar.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *DefaultAgentAvatarClient) GetX(ctx context.Context, id int64) *DefaultAgentAvatar {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *DefaultAgentAvatarClient) Hooks() []Hook {
	hooks := c.hooks.DefaultAgentAvatar
	return append(hooks[:len(hooks):len(hooks)], defaultagentavatar.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *DefaultAgentAvatarClient) Interceptors() []Interceptor {
	inters := c.inters.DefaultAgentAvatar
	return append(inters[:len(inters):len(inters)], defaultagentavatar.Interceptors[:]...)
}

func (c *DefaultAgentAvatarClient) mutate(ctx context.Context, m *DefaultAgentAvatarMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&DefaultAgentAvatarCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&DefaultAgentAvatarUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&DefaultAgentAvatarUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&DefaultAgentAvatarDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown DefaultAgentAvatar mutation op: %q", m.Op())
	}
}

// ExternalModelUsageClient is a client for the ExternalModelUsage schema.
type ExternalModelUsageClient struct {
	config
}

// NewExternalModelUsageClient returns a client for the ExternalModelUsage from the given config.
func NewExternalModelUsageClient(c config) *ExternalModelUsageClient {
	return &ExternalModelUsageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `externalmodelusage.Hooks(f(g(h())))`.
func (c *ExternalModelUsageClient) Use(hooks ...Hook) {
	c.hooks.ExternalModelUsage = append(c.hooks.ExternalModelUsage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `externalmodelusage.Intercept(f(g(h())))`.
func (c *ExternalModelUsageClient) Intercept(interceptors ...Interceptor) {
	c.inters.ExternalModelUsage = append(c.inters.ExternalModelUsage, interceptors...)
}

// Create returns a builder for creating a ExternalModelUsage entity.
func (c *ExternalModelUsageClient) Create() *ExternalModelUsageCreate {
	mutation := newExternalModelUsageMutation(c.config, OpCreate)
	return &ExternalModelUsageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ExternalModelUsage entities.
func (c *ExternalModelUsageClient) CreateBulk(builders ...*ExternalModelUsageCreate) *ExternalModelUsageCreateBulk {
	return &ExternalModelUsageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ExternalModelUsageClient) MapCreateBulk(slice any, setFunc func(*ExternalModelUsageCreate, int)) *ExternalModelUsageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ExternalModelUsageCreateBulk{err: fmt.Errorf("calling to ExternalModelUsageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ExternalModelUsageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ExternalModelUsageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ExternalModelUsage.
func (c *ExternalModelUsageClient) Update() *ExternalModelUsageUpdate {
	mutation := newExternalModelUsageMutation(c.config, OpUpdate)
	return &ExternalModelUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ExternalModelUsageClient) UpdateOne(emu *ExternalModelUsage) *ExternalModelUsageUpdateOne {
	mutation := newExternalModelUsageMutation(c.config, OpUpdateOne, withExternalModelUsage(emu))
	return &ExternalModelUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ExternalModelUsageClient) UpdateOneID(id int64) *ExternalModelUsageUpdateOne {
	mutation := newExternalModelUsageMutation(c.config, OpUpdateOne, withExternalModelUsageID(id))
	return &ExternalModelUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ExternalModelUsage.
func (c *ExternalModelUsageClient) Delete() *ExternalModelUsageDelete {
	mutation := newExternalModelUsageMutation(c.config, OpDelete)
	return &ExternalModelUsageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ExternalModelUsageClient) DeleteOne(emu *ExternalModelUsage) *ExternalModelUsageDeleteOne {
	return c.DeleteOneID(emu.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ExternalModelUsageClient) DeleteOneID(id int64) *ExternalModelUsageDeleteOne {
	builder := c.Delete().Where(externalmodelusage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ExternalModelUsageDeleteOne{builder}
}

// Query returns a query builder for ExternalModelUsage.
func (c *ExternalModelUsageClient) Query() *ExternalModelUsageQuery {
	return &ExternalModelUsageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeExternalModelUsage},
		inters: c.Interceptors(),
	}
}

// Get returns a ExternalModelUsage entity by its id.
func (c *ExternalModelUsageClient) Get(ctx context.Context, id int64) (*ExternalModelUsage, error) {
	return c.Query().Where(externalmodelusage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ExternalModelUsageClient) GetX(ctx context.Context, id int64) *ExternalModelUsage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ExternalModelUsageClient) Hooks() []Hook {
	hooks := c.hooks.ExternalModelUsage
	return append(hooks[:len(hooks):len(hooks)], externalmodelusage.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ExternalModelUsageClient) Interceptors() []Interceptor {
	inters := c.inters.ExternalModelUsage
	return append(inters[:len(inters):len(inters)], externalmodelusage.Interceptors[:]...)
}

func (c *ExternalModelUsageClient) mutate(ctx context.Context, m *ExternalModelUsageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ExternalModelUsageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ExternalModelUsageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ExternalModelUsageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ExternalModelUsageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown ExternalModelUsage mutation op: %q", m.Op())
	}
}

// KnowledgeBaseClient is a client for the KnowledgeBase schema.
type KnowledgeBaseClient struct {
	config
}

// NewKnowledgeBaseClient returns a client for the KnowledgeBase from the given config.
func NewKnowledgeBaseClient(c config) *KnowledgeBaseClient {
	return &KnowledgeBaseClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `knowledgebase.Hooks(f(g(h())))`.
func (c *KnowledgeBaseClient) Use(hooks ...Hook) {
	c.hooks.KnowledgeBase = append(c.hooks.KnowledgeBase, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `knowledgebase.Intercept(f(g(h())))`.
func (c *KnowledgeBaseClient) Intercept(interceptors ...Interceptor) {
	c.inters.KnowledgeBase = append(c.inters.KnowledgeBase, interceptors...)
}

// Create returns a builder for creating a KnowledgeBase entity.
func (c *KnowledgeBaseClient) Create() *KnowledgeBaseCreate {
	mutation := newKnowledgeBaseMutation(c.config, OpCreate)
	return &KnowledgeBaseCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of KnowledgeBase entities.
func (c *KnowledgeBaseClient) CreateBulk(builders ...*KnowledgeBaseCreate) *KnowledgeBaseCreateBulk {
	return &KnowledgeBaseCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *KnowledgeBaseClient) MapCreateBulk(slice any, setFunc func(*KnowledgeBaseCreate, int)) *KnowledgeBaseCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &KnowledgeBaseCreateBulk{err: fmt.Errorf("calling to KnowledgeBaseClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*KnowledgeBaseCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &KnowledgeBaseCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for KnowledgeBase.
func (c *KnowledgeBaseClient) Update() *KnowledgeBaseUpdate {
	mutation := newKnowledgeBaseMutation(c.config, OpUpdate)
	return &KnowledgeBaseUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *KnowledgeBaseClient) UpdateOne(kb *KnowledgeBase) *KnowledgeBaseUpdateOne {
	mutation := newKnowledgeBaseMutation(c.config, OpUpdateOne, withKnowledgeBase(kb))
	return &KnowledgeBaseUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *KnowledgeBaseClient) UpdateOneID(id int64) *KnowledgeBaseUpdateOne {
	mutation := newKnowledgeBaseMutation(c.config, OpUpdateOne, withKnowledgeBaseID(id))
	return &KnowledgeBaseUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for KnowledgeBase.
func (c *KnowledgeBaseClient) Delete() *KnowledgeBaseDelete {
	mutation := newKnowledgeBaseMutation(c.config, OpDelete)
	return &KnowledgeBaseDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *KnowledgeBaseClient) DeleteOne(kb *KnowledgeBase) *KnowledgeBaseDeleteOne {
	return c.DeleteOneID(kb.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *KnowledgeBaseClient) DeleteOneID(id int64) *KnowledgeBaseDeleteOne {
	builder := c.Delete().Where(knowledgebase.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &KnowledgeBaseDeleteOne{builder}
}

// Query returns a query builder for KnowledgeBase.
func (c *KnowledgeBaseClient) Query() *KnowledgeBaseQuery {
	return &KnowledgeBaseQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeKnowledgeBase},
		inters: c.Interceptors(),
	}
}

// Get returns a KnowledgeBase entity by its id.
func (c *KnowledgeBaseClient) Get(ctx context.Context, id int64) (*KnowledgeBase, error) {
	return c.Query().Where(knowledgebase.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *KnowledgeBaseClient) GetX(ctx context.Context, id int64) *KnowledgeBase {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *KnowledgeBaseClient) Hooks() []Hook {
	hooks := c.hooks.KnowledgeBase
	return append(hooks[:len(hooks):len(hooks)], knowledgebase.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *KnowledgeBaseClient) Interceptors() []Interceptor {
	inters := c.inters.KnowledgeBase
	return append(inters[:len(inters):len(inters)], knowledgebase.Interceptors[:]...)
}

func (c *KnowledgeBaseClient) mutate(ctx context.Context, m *KnowledgeBaseMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&KnowledgeBaseCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&KnowledgeBaseUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&KnowledgeBaseUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&KnowledgeBaseDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown KnowledgeBase mutation op: %q", m.Op())
	}
}

// KnowledgeBaseFileClient is a client for the KnowledgeBaseFile schema.
type KnowledgeBaseFileClient struct {
	config
}

// NewKnowledgeBaseFileClient returns a client for the KnowledgeBaseFile from the given config.
func NewKnowledgeBaseFileClient(c config) *KnowledgeBaseFileClient {
	return &KnowledgeBaseFileClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `knowledgebasefile.Hooks(f(g(h())))`.
func (c *KnowledgeBaseFileClient) Use(hooks ...Hook) {
	c.hooks.KnowledgeBaseFile = append(c.hooks.KnowledgeBaseFile, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `knowledgebasefile.Intercept(f(g(h())))`.
func (c *KnowledgeBaseFileClient) Intercept(interceptors ...Interceptor) {
	c.inters.KnowledgeBaseFile = append(c.inters.KnowledgeBaseFile, interceptors...)
}

// Create returns a builder for creating a KnowledgeBaseFile entity.
func (c *KnowledgeBaseFileClient) Create() *KnowledgeBaseFileCreate {
	mutation := newKnowledgeBaseFileMutation(c.config, OpCreate)
	return &KnowledgeBaseFileCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of KnowledgeBaseFile entities.
func (c *KnowledgeBaseFileClient) CreateBulk(builders ...*KnowledgeBaseFileCreate) *KnowledgeBaseFileCreateBulk {
	return &KnowledgeBaseFileCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *KnowledgeBaseFileClient) MapCreateBulk(slice any, setFunc func(*KnowledgeBaseFileCreate, int)) *KnowledgeBaseFileCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &KnowledgeBaseFileCreateBulk{err: fmt.Errorf("calling to KnowledgeBaseFileClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*KnowledgeBaseFileCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &KnowledgeBaseFileCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for KnowledgeBaseFile.
func (c *KnowledgeBaseFileClient) Update() *KnowledgeBaseFileUpdate {
	mutation := newKnowledgeBaseFileMutation(c.config, OpUpdate)
	return &KnowledgeBaseFileUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *KnowledgeBaseFileClient) UpdateOne(kbf *KnowledgeBaseFile) *KnowledgeBaseFileUpdateOne {
	mutation := newKnowledgeBaseFileMutation(c.config, OpUpdateOne, withKnowledgeBaseFile(kbf))
	return &KnowledgeBaseFileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *KnowledgeBaseFileClient) UpdateOneID(id int64) *KnowledgeBaseFileUpdateOne {
	mutation := newKnowledgeBaseFileMutation(c.config, OpUpdateOne, withKnowledgeBaseFileID(id))
	return &KnowledgeBaseFileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for KnowledgeBaseFile.
func (c *KnowledgeBaseFileClient) Delete() *KnowledgeBaseFileDelete {
	mutation := newKnowledgeBaseFileMutation(c.config, OpDelete)
	return &KnowledgeBaseFileDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *KnowledgeBaseFileClient) DeleteOne(kbf *KnowledgeBaseFile) *KnowledgeBaseFileDeleteOne {
	return c.DeleteOneID(kbf.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *KnowledgeBaseFileClient) DeleteOneID(id int64) *KnowledgeBaseFileDeleteOne {
	builder := c.Delete().Where(knowledgebasefile.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &KnowledgeBaseFileDeleteOne{builder}
}

// Query returns a query builder for KnowledgeBaseFile.
func (c *KnowledgeBaseFileClient) Query() *KnowledgeBaseFileQuery {
	return &KnowledgeBaseFileQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeKnowledgeBaseFile},
		inters: c.Interceptors(),
	}
}

// Get returns a KnowledgeBaseFile entity by its id.
func (c *KnowledgeBaseFileClient) Get(ctx context.Context, id int64) (*KnowledgeBaseFile, error) {
	return c.Query().Where(knowledgebasefile.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *KnowledgeBaseFileClient) GetX(ctx context.Context, id int64) *KnowledgeBaseFile {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *KnowledgeBaseFileClient) Hooks() []Hook {
	hooks := c.hooks.KnowledgeBaseFile
	return append(hooks[:len(hooks):len(hooks)], knowledgebasefile.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *KnowledgeBaseFileClient) Interceptors() []Interceptor {
	inters := c.inters.KnowledgeBaseFile
	return append(inters[:len(inters):len(inters)], knowledgebasefile.Interceptors[:]...)
}

func (c *KnowledgeBaseFileClient) mutate(ctx context.Context, m *KnowledgeBaseFileMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&KnowledgeBaseFileCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&KnowledgeBaseFileUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&KnowledgeBaseFileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&KnowledgeBaseFileDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown KnowledgeBaseFile mutation op: %q", m.Op())
	}
}

// UserAgentOrderClient is a client for the UserAgentOrder schema.
type UserAgentOrderClient struct {
	config
}

// NewUserAgentOrderClient returns a client for the UserAgentOrder from the given config.
func NewUserAgentOrderClient(c config) *UserAgentOrderClient {
	return &UserAgentOrderClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `useragentorder.Hooks(f(g(h())))`.
func (c *UserAgentOrderClient) Use(hooks ...Hook) {
	c.hooks.UserAgentOrder = append(c.hooks.UserAgentOrder, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `useragentorder.Intercept(f(g(h())))`.
func (c *UserAgentOrderClient) Intercept(interceptors ...Interceptor) {
	c.inters.UserAgentOrder = append(c.inters.UserAgentOrder, interceptors...)
}

// Create returns a builder for creating a UserAgentOrder entity.
func (c *UserAgentOrderClient) Create() *UserAgentOrderCreate {
	mutation := newUserAgentOrderMutation(c.config, OpCreate)
	return &UserAgentOrderCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of UserAgentOrder entities.
func (c *UserAgentOrderClient) CreateBulk(builders ...*UserAgentOrderCreate) *UserAgentOrderCreateBulk {
	return &UserAgentOrderCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserAgentOrderClient) MapCreateBulk(slice any, setFunc func(*UserAgentOrderCreate, int)) *UserAgentOrderCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserAgentOrderCreateBulk{err: fmt.Errorf("calling to UserAgentOrderClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserAgentOrderCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserAgentOrderCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for UserAgentOrder.
func (c *UserAgentOrderClient) Update() *UserAgentOrderUpdate {
	mutation := newUserAgentOrderMutation(c.config, OpUpdate)
	return &UserAgentOrderUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserAgentOrderClient) UpdateOne(uao *UserAgentOrder) *UserAgentOrderUpdateOne {
	mutation := newUserAgentOrderMutation(c.config, OpUpdateOne, withUserAgentOrder(uao))
	return &UserAgentOrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserAgentOrderClient) UpdateOneID(id int64) *UserAgentOrderUpdateOne {
	mutation := newUserAgentOrderMutation(c.config, OpUpdateOne, withUserAgentOrderID(id))
	return &UserAgentOrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for UserAgentOrder.
func (c *UserAgentOrderClient) Delete() *UserAgentOrderDelete {
	mutation := newUserAgentOrderMutation(c.config, OpDelete)
	return &UserAgentOrderDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserAgentOrderClient) DeleteOne(uao *UserAgentOrder) *UserAgentOrderDeleteOne {
	return c.DeleteOneID(uao.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserAgentOrderClient) DeleteOneID(id int64) *UserAgentOrderDeleteOne {
	builder := c.Delete().Where(useragentorder.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserAgentOrderDeleteOne{builder}
}

// Query returns a query builder for UserAgentOrder.
func (c *UserAgentOrderClient) Query() *UserAgentOrderQuery {
	return &UserAgentOrderQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUserAgentOrder},
		inters: c.Interceptors(),
	}
}

// Get returns a UserAgentOrder entity by its id.
func (c *UserAgentOrderClient) Get(ctx context.Context, id int64) (*UserAgentOrder, error) {
	return c.Query().Where(useragentorder.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserAgentOrderClient) GetX(ctx context.Context, id int64) *UserAgentOrder {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *UserAgentOrderClient) Hooks() []Hook {
	hooks := c.hooks.UserAgentOrder
	return append(hooks[:len(hooks):len(hooks)], useragentorder.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *UserAgentOrderClient) Interceptors() []Interceptor {
	inters := c.inters.UserAgentOrder
	return append(inters[:len(inters):len(inters)], useragentorder.Interceptors[:]...)
}

func (c *UserAgentOrderClient) mutate(ctx context.Context, m *UserAgentOrderMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserAgentOrderCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserAgentOrderUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserAgentOrderUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserAgentOrderDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown UserAgentOrder mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		AiAgent, AiAgentSecurityLog, AiAgentSecurityPolicy, AiChat, AiChatItem, AiModel,
		AiModelDetail, AiModelUsage, AtomicQuestions, ClassificationFiles,
		DefaultAgentAvatar, ExternalModelUsage, KnowledgeBase, KnowledgeBaseFile,
		UserAgentOrder []ent.Hook
	}
	inters struct {
		AiAgent, AiAgentSecurityLog, AiAgentSecurityPolicy, AiChat, AiChatItem, AiModel,
		AiModelDetail, AiModelUsage, AtomicQuestions, ClassificationFiles,
		DefaultAgentAvatar, ExternalModelUsage, KnowledgeBase, KnowledgeBaseFile,
		UserAgentOrder []ent.Interceptor
	}
)

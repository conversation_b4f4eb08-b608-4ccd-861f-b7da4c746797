// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiChatUpdate is the builder for updating AiChat entities.
type AiChatUpdate struct {
	config
	hooks     []Hook
	mutation  *AiChatMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AiChatUpdate builder.
func (acu *AiChatUpdate) Where(ps ...predicate.AiChat) *AiChatUpdate {
	acu.mutation.Where(ps...)
	return acu
}

// SetTenantID sets the "tenant_id" field.
func (acu *AiChatUpdate) SetTenantID(i int64) *AiChatUpdate {
	acu.mutation.ResetTenantID()
	acu.mutation.SetTenantID(i)
	return acu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (acu *AiChatUpdate) SetNillableTenantID(i *int64) *AiChatUpdate {
	if i != nil {
		acu.SetTenantID(*i)
	}
	return acu
}

// AddTenantID adds i to the "tenant_id" field.
func (acu *AiChatUpdate) AddTenantID(i int64) *AiChatUpdate {
	acu.mutation.AddTenantID(i)
	return acu
}

// SetUpdatedAt sets the "updated_at" field.
func (acu *AiChatUpdate) SetUpdatedAt(t time.Time) *AiChatUpdate {
	acu.mutation.SetUpdatedAt(t)
	return acu
}

// SetDeletedAt sets the "deleted_at" field.
func (acu *AiChatUpdate) SetDeletedAt(t time.Time) *AiChatUpdate {
	acu.mutation.SetDeletedAt(t)
	return acu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (acu *AiChatUpdate) SetNillableDeletedAt(t *time.Time) *AiChatUpdate {
	if t != nil {
		acu.SetDeletedAt(*t)
	}
	return acu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (acu *AiChatUpdate) ClearDeletedAt() *AiChatUpdate {
	acu.mutation.ClearDeletedAt()
	return acu
}

// SetName sets the "name" field.
func (acu *AiChatUpdate) SetName(s string) *AiChatUpdate {
	acu.mutation.SetName(s)
	return acu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (acu *AiChatUpdate) SetNillableName(s *string) *AiChatUpdate {
	if s != nil {
		acu.SetName(*s)
	}
	return acu
}

// SetUserID sets the "user_id" field.
func (acu *AiChatUpdate) SetUserID(i int64) *AiChatUpdate {
	acu.mutation.ResetUserID()
	acu.mutation.SetUserID(i)
	return acu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (acu *AiChatUpdate) SetNillableUserID(i *int64) *AiChatUpdate {
	if i != nil {
		acu.SetUserID(*i)
	}
	return acu
}

// AddUserID adds i to the "user_id" field.
func (acu *AiChatUpdate) AddUserID(i int64) *AiChatUpdate {
	acu.mutation.AddUserID(i)
	return acu
}

// SetChatType sets the "chat_type" field.
func (acu *AiChatUpdate) SetChatType(i int64) *AiChatUpdate {
	acu.mutation.ResetChatType()
	acu.mutation.SetChatType(i)
	return acu
}

// SetNillableChatType sets the "chat_type" field if the given value is not nil.
func (acu *AiChatUpdate) SetNillableChatType(i *int64) *AiChatUpdate {
	if i != nil {
		acu.SetChatType(*i)
	}
	return acu
}

// AddChatType adds i to the "chat_type" field.
func (acu *AiChatUpdate) AddChatType(i int64) *AiChatUpdate {
	acu.mutation.AddChatType(i)
	return acu
}

// SetAgentID sets the "agent_id" field.
func (acu *AiChatUpdate) SetAgentID(i int64) *AiChatUpdate {
	acu.mutation.SetAgentID(i)
	return acu
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (acu *AiChatUpdate) SetNillableAgentID(i *int64) *AiChatUpdate {
	if i != nil {
		acu.SetAgentID(*i)
	}
	return acu
}

// ClearAgentID clears the value of the "agent_id" field.
func (acu *AiChatUpdate) ClearAgentID() *AiChatUpdate {
	acu.mutation.ClearAgentID()
	return acu
}

// SetAiAgentID sets the "ai_agent" edge to the AiAgent entity by ID.
func (acu *AiChatUpdate) SetAiAgentID(id int64) *AiChatUpdate {
	acu.mutation.SetAiAgentID(id)
	return acu
}

// SetNillableAiAgentID sets the "ai_agent" edge to the AiAgent entity by ID if the given value is not nil.
func (acu *AiChatUpdate) SetNillableAiAgentID(id *int64) *AiChatUpdate {
	if id != nil {
		acu = acu.SetAiAgentID(*id)
	}
	return acu
}

// SetAiAgent sets the "ai_agent" edge to the AiAgent entity.
func (acu *AiChatUpdate) SetAiAgent(a *AiAgent) *AiChatUpdate {
	return acu.SetAiAgentID(a.ID)
}

// Mutation returns the AiChatMutation object of the builder.
func (acu *AiChatUpdate) Mutation() *AiChatMutation {
	return acu.mutation
}

// ClearAiAgent clears the "ai_agent" edge to the AiAgent entity.
func (acu *AiChatUpdate) ClearAiAgent() *AiChatUpdate {
	acu.mutation.ClearAiAgent()
	return acu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (acu *AiChatUpdate) Save(ctx context.Context) (int, error) {
	if err := acu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, acu.sqlSave, acu.mutation, acu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (acu *AiChatUpdate) SaveX(ctx context.Context) int {
	affected, err := acu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (acu *AiChatUpdate) Exec(ctx context.Context) error {
	_, err := acu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acu *AiChatUpdate) ExecX(ctx context.Context) {
	if err := acu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (acu *AiChatUpdate) defaults() error {
	if _, ok := acu.mutation.UpdatedAt(); !ok {
		if aichat.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aichat.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aichat.UpdateDefaultUpdatedAt()
		acu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (acu *AiChatUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiChatUpdate {
	acu.modifiers = append(acu.modifiers, modifiers...)
	return acu
}

func (acu *AiChatUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(aichat.Table, aichat.Columns, sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64))
	if ps := acu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := acu.mutation.TenantID(); ok {
		_spec.SetField(aichat.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := acu.mutation.AddedTenantID(); ok {
		_spec.AddField(aichat.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := acu.mutation.UpdatedAt(); ok {
		_spec.SetField(aichat.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := acu.mutation.DeletedAt(); ok {
		_spec.SetField(aichat.FieldDeletedAt, field.TypeTime, value)
	}
	if acu.mutation.DeletedAtCleared() {
		_spec.ClearField(aichat.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := acu.mutation.Name(); ok {
		_spec.SetField(aichat.FieldName, field.TypeString, value)
	}
	if value, ok := acu.mutation.UserID(); ok {
		_spec.SetField(aichat.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := acu.mutation.AddedUserID(); ok {
		_spec.AddField(aichat.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := acu.mutation.ChatType(); ok {
		_spec.SetField(aichat.FieldChatType, field.TypeInt64, value)
	}
	if value, ok := acu.mutation.AddedChatType(); ok {
		_spec.AddField(aichat.FieldChatType, field.TypeInt64, value)
	}
	if acu.mutation.AiAgentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   aichat.AiAgentTable,
			Columns: []string{aichat.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt64),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := acu.mutation.AiAgentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   aichat.AiAgentTable,
			Columns: []string{aichat.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(acu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, acu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aichat.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	acu.mutation.done = true
	return n, nil
}

// AiChatUpdateOne is the builder for updating a single AiChat entity.
type AiChatUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AiChatMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetTenantID sets the "tenant_id" field.
func (acuo *AiChatUpdateOne) SetTenantID(i int64) *AiChatUpdateOne {
	acuo.mutation.ResetTenantID()
	acuo.mutation.SetTenantID(i)
	return acuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (acuo *AiChatUpdateOne) SetNillableTenantID(i *int64) *AiChatUpdateOne {
	if i != nil {
		acuo.SetTenantID(*i)
	}
	return acuo
}

// AddTenantID adds i to the "tenant_id" field.
func (acuo *AiChatUpdateOne) AddTenantID(i int64) *AiChatUpdateOne {
	acuo.mutation.AddTenantID(i)
	return acuo
}

// SetUpdatedAt sets the "updated_at" field.
func (acuo *AiChatUpdateOne) SetUpdatedAt(t time.Time) *AiChatUpdateOne {
	acuo.mutation.SetUpdatedAt(t)
	return acuo
}

// SetDeletedAt sets the "deleted_at" field.
func (acuo *AiChatUpdateOne) SetDeletedAt(t time.Time) *AiChatUpdateOne {
	acuo.mutation.SetDeletedAt(t)
	return acuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (acuo *AiChatUpdateOne) SetNillableDeletedAt(t *time.Time) *AiChatUpdateOne {
	if t != nil {
		acuo.SetDeletedAt(*t)
	}
	return acuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (acuo *AiChatUpdateOne) ClearDeletedAt() *AiChatUpdateOne {
	acuo.mutation.ClearDeletedAt()
	return acuo
}

// SetName sets the "name" field.
func (acuo *AiChatUpdateOne) SetName(s string) *AiChatUpdateOne {
	acuo.mutation.SetName(s)
	return acuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (acuo *AiChatUpdateOne) SetNillableName(s *string) *AiChatUpdateOne {
	if s != nil {
		acuo.SetName(*s)
	}
	return acuo
}

// SetUserID sets the "user_id" field.
func (acuo *AiChatUpdateOne) SetUserID(i int64) *AiChatUpdateOne {
	acuo.mutation.ResetUserID()
	acuo.mutation.SetUserID(i)
	return acuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (acuo *AiChatUpdateOne) SetNillableUserID(i *int64) *AiChatUpdateOne {
	if i != nil {
		acuo.SetUserID(*i)
	}
	return acuo
}

// AddUserID adds i to the "user_id" field.
func (acuo *AiChatUpdateOne) AddUserID(i int64) *AiChatUpdateOne {
	acuo.mutation.AddUserID(i)
	return acuo
}

// SetChatType sets the "chat_type" field.
func (acuo *AiChatUpdateOne) SetChatType(i int64) *AiChatUpdateOne {
	acuo.mutation.ResetChatType()
	acuo.mutation.SetChatType(i)
	return acuo
}

// SetNillableChatType sets the "chat_type" field if the given value is not nil.
func (acuo *AiChatUpdateOne) SetNillableChatType(i *int64) *AiChatUpdateOne {
	if i != nil {
		acuo.SetChatType(*i)
	}
	return acuo
}

// AddChatType adds i to the "chat_type" field.
func (acuo *AiChatUpdateOne) AddChatType(i int64) *AiChatUpdateOne {
	acuo.mutation.AddChatType(i)
	return acuo
}

// SetAgentID sets the "agent_id" field.
func (acuo *AiChatUpdateOne) SetAgentID(i int64) *AiChatUpdateOne {
	acuo.mutation.SetAgentID(i)
	return acuo
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (acuo *AiChatUpdateOne) SetNillableAgentID(i *int64) *AiChatUpdateOne {
	if i != nil {
		acuo.SetAgentID(*i)
	}
	return acuo
}

// ClearAgentID clears the value of the "agent_id" field.
func (acuo *AiChatUpdateOne) ClearAgentID() *AiChatUpdateOne {
	acuo.mutation.ClearAgentID()
	return acuo
}

// SetAiAgentID sets the "ai_agent" edge to the AiAgent entity by ID.
func (acuo *AiChatUpdateOne) SetAiAgentID(id int64) *AiChatUpdateOne {
	acuo.mutation.SetAiAgentID(id)
	return acuo
}

// SetNillableAiAgentID sets the "ai_agent" edge to the AiAgent entity by ID if the given value is not nil.
func (acuo *AiChatUpdateOne) SetNillableAiAgentID(id *int64) *AiChatUpdateOne {
	if id != nil {
		acuo = acuo.SetAiAgentID(*id)
	}
	return acuo
}

// SetAiAgent sets the "ai_agent" edge to the AiAgent entity.
func (acuo *AiChatUpdateOne) SetAiAgent(a *AiAgent) *AiChatUpdateOne {
	return acuo.SetAiAgentID(a.ID)
}

// Mutation returns the AiChatMutation object of the builder.
func (acuo *AiChatUpdateOne) Mutation() *AiChatMutation {
	return acuo.mutation
}

// ClearAiAgent clears the "ai_agent" edge to the AiAgent entity.
func (acuo *AiChatUpdateOne) ClearAiAgent() *AiChatUpdateOne {
	acuo.mutation.ClearAiAgent()
	return acuo
}

// Where appends a list predicates to the AiChatUpdate builder.
func (acuo *AiChatUpdateOne) Where(ps ...predicate.AiChat) *AiChatUpdateOne {
	acuo.mutation.Where(ps...)
	return acuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (acuo *AiChatUpdateOne) Select(field string, fields ...string) *AiChatUpdateOne {
	acuo.fields = append([]string{field}, fields...)
	return acuo
}

// Save executes the query and returns the updated AiChat entity.
func (acuo *AiChatUpdateOne) Save(ctx context.Context) (*AiChat, error) {
	if err := acuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, acuo.sqlSave, acuo.mutation, acuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (acuo *AiChatUpdateOne) SaveX(ctx context.Context) *AiChat {
	node, err := acuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (acuo *AiChatUpdateOne) Exec(ctx context.Context) error {
	_, err := acuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acuo *AiChatUpdateOne) ExecX(ctx context.Context) {
	if err := acuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (acuo *AiChatUpdateOne) defaults() error {
	if _, ok := acuo.mutation.UpdatedAt(); !ok {
		if aichat.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aichat.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aichat.UpdateDefaultUpdatedAt()
		acuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (acuo *AiChatUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiChatUpdateOne {
	acuo.modifiers = append(acuo.modifiers, modifiers...)
	return acuo
}

func (acuo *AiChatUpdateOne) sqlSave(ctx context.Context) (_node *AiChat, err error) {
	_spec := sqlgraph.NewUpdateSpec(aichat.Table, aichat.Columns, sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64))
	id, ok := acuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AiChat.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := acuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aichat.FieldID)
		for _, f := range fields {
			if !aichat.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != aichat.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := acuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := acuo.mutation.TenantID(); ok {
		_spec.SetField(aichat.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := acuo.mutation.AddedTenantID(); ok {
		_spec.AddField(aichat.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := acuo.mutation.UpdatedAt(); ok {
		_spec.SetField(aichat.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := acuo.mutation.DeletedAt(); ok {
		_spec.SetField(aichat.FieldDeletedAt, field.TypeTime, value)
	}
	if acuo.mutation.DeletedAtCleared() {
		_spec.ClearField(aichat.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := acuo.mutation.Name(); ok {
		_spec.SetField(aichat.FieldName, field.TypeString, value)
	}
	if value, ok := acuo.mutation.UserID(); ok {
		_spec.SetField(aichat.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := acuo.mutation.AddedUserID(); ok {
		_spec.AddField(aichat.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := acuo.mutation.ChatType(); ok {
		_spec.SetField(aichat.FieldChatType, field.TypeInt64, value)
	}
	if value, ok := acuo.mutation.AddedChatType(); ok {
		_spec.AddField(aichat.FieldChatType, field.TypeInt64, value)
	}
	if acuo.mutation.AiAgentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   aichat.AiAgentTable,
			Columns: []string{aichat.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt64),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := acuo.mutation.AiAgentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   aichat.AiAgentTable,
			Columns: []string{aichat.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(acuo.modifiers...)
	_node = &AiChat{config: acuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, acuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aichat.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	acuo.mutation.done = true
	return _node, nil
}

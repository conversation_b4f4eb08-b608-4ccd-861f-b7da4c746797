// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/useragentorder"
)

// UserAgentOrderUpdate is the builder for updating UserAgentOrder entities.
type UserAgentOrderUpdate struct {
	config
	hooks     []Hook
	mutation  *UserAgentOrderMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the UserAgentOrderUpdate builder.
func (uaou *UserAgentOrderUpdate) Where(ps ...predicate.UserAgentOrder) *UserAgentOrderUpdate {
	uaou.mutation.Where(ps...)
	return uaou
}

// SetUpdatedAt sets the "updated_at" field.
func (uaou *UserAgentOrderUpdate) SetUpdatedAt(t time.Time) *UserAgentOrderUpdate {
	uaou.mutation.SetUpdatedAt(t)
	return uaou
}

// SetDeletedAt sets the "deleted_at" field.
func (uaou *UserAgentOrderUpdate) SetDeletedAt(t time.Time) *UserAgentOrderUpdate {
	uaou.mutation.SetDeletedAt(t)
	return uaou
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uaou *UserAgentOrderUpdate) SetNillableDeletedAt(t *time.Time) *UserAgentOrderUpdate {
	if t != nil {
		uaou.SetDeletedAt(*t)
	}
	return uaou
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (uaou *UserAgentOrderUpdate) ClearDeletedAt() *UserAgentOrderUpdate {
	uaou.mutation.ClearDeletedAt()
	return uaou
}

// SetUserID sets the "user_id" field.
func (uaou *UserAgentOrderUpdate) SetUserID(i int64) *UserAgentOrderUpdate {
	uaou.mutation.ResetUserID()
	uaou.mutation.SetUserID(i)
	return uaou
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (uaou *UserAgentOrderUpdate) SetNillableUserID(i *int64) *UserAgentOrderUpdate {
	if i != nil {
		uaou.SetUserID(*i)
	}
	return uaou
}

// AddUserID adds i to the "user_id" field.
func (uaou *UserAgentOrderUpdate) AddUserID(i int64) *UserAgentOrderUpdate {
	uaou.mutation.AddUserID(i)
	return uaou
}

// SetAgentID sets the "agent_id" field.
func (uaou *UserAgentOrderUpdate) SetAgentID(i int64) *UserAgentOrderUpdate {
	uaou.mutation.ResetAgentID()
	uaou.mutation.SetAgentID(i)
	return uaou
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (uaou *UserAgentOrderUpdate) SetNillableAgentID(i *int64) *UserAgentOrderUpdate {
	if i != nil {
		uaou.SetAgentID(*i)
	}
	return uaou
}

// AddAgentID adds i to the "agent_id" field.
func (uaou *UserAgentOrderUpdate) AddAgentID(i int64) *UserAgentOrderUpdate {
	uaou.mutation.AddAgentID(i)
	return uaou
}

// SetOrderIndex sets the "order_index" field.
func (uaou *UserAgentOrderUpdate) SetOrderIndex(i int64) *UserAgentOrderUpdate {
	uaou.mutation.ResetOrderIndex()
	uaou.mutation.SetOrderIndex(i)
	return uaou
}

// SetNillableOrderIndex sets the "order_index" field if the given value is not nil.
func (uaou *UserAgentOrderUpdate) SetNillableOrderIndex(i *int64) *UserAgentOrderUpdate {
	if i != nil {
		uaou.SetOrderIndex(*i)
	}
	return uaou
}

// AddOrderIndex adds i to the "order_index" field.
func (uaou *UserAgentOrderUpdate) AddOrderIndex(i int64) *UserAgentOrderUpdate {
	uaou.mutation.AddOrderIndex(i)
	return uaou
}

// Mutation returns the UserAgentOrderMutation object of the builder.
func (uaou *UserAgentOrderUpdate) Mutation() *UserAgentOrderMutation {
	return uaou.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uaou *UserAgentOrderUpdate) Save(ctx context.Context) (int, error) {
	if err := uaou.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, uaou.sqlSave, uaou.mutation, uaou.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uaou *UserAgentOrderUpdate) SaveX(ctx context.Context) int {
	affected, err := uaou.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uaou *UserAgentOrderUpdate) Exec(ctx context.Context) error {
	_, err := uaou.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uaou *UserAgentOrderUpdate) ExecX(ctx context.Context) {
	if err := uaou.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uaou *UserAgentOrderUpdate) defaults() error {
	if _, ok := uaou.mutation.UpdatedAt(); !ok {
		if useragentorder.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized useragentorder.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := useragentorder.UpdateDefaultUpdatedAt()
		uaou.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (uaou *UserAgentOrderUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *UserAgentOrderUpdate {
	uaou.modifiers = append(uaou.modifiers, modifiers...)
	return uaou
}

func (uaou *UserAgentOrderUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(useragentorder.Table, useragentorder.Columns, sqlgraph.NewFieldSpec(useragentorder.FieldID, field.TypeInt64))
	if ps := uaou.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uaou.mutation.UpdatedAt(); ok {
		_spec.SetField(useragentorder.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uaou.mutation.DeletedAt(); ok {
		_spec.SetField(useragentorder.FieldDeletedAt, field.TypeTime, value)
	}
	if uaou.mutation.DeletedAtCleared() {
		_spec.ClearField(useragentorder.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := uaou.mutation.UserID(); ok {
		_spec.SetField(useragentorder.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := uaou.mutation.AddedUserID(); ok {
		_spec.AddField(useragentorder.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := uaou.mutation.AgentID(); ok {
		_spec.SetField(useragentorder.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := uaou.mutation.AddedAgentID(); ok {
		_spec.AddField(useragentorder.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := uaou.mutation.OrderIndex(); ok {
		_spec.SetField(useragentorder.FieldOrderIndex, field.TypeInt64, value)
	}
	if value, ok := uaou.mutation.AddedOrderIndex(); ok {
		_spec.AddField(useragentorder.FieldOrderIndex, field.TypeInt64, value)
	}
	_spec.AddModifiers(uaou.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, uaou.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{useragentorder.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uaou.mutation.done = true
	return n, nil
}

// UserAgentOrderUpdateOne is the builder for updating a single UserAgentOrder entity.
type UserAgentOrderUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *UserAgentOrderMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (uaouo *UserAgentOrderUpdateOne) SetUpdatedAt(t time.Time) *UserAgentOrderUpdateOne {
	uaouo.mutation.SetUpdatedAt(t)
	return uaouo
}

// SetDeletedAt sets the "deleted_at" field.
func (uaouo *UserAgentOrderUpdateOne) SetDeletedAt(t time.Time) *UserAgentOrderUpdateOne {
	uaouo.mutation.SetDeletedAt(t)
	return uaouo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uaouo *UserAgentOrderUpdateOne) SetNillableDeletedAt(t *time.Time) *UserAgentOrderUpdateOne {
	if t != nil {
		uaouo.SetDeletedAt(*t)
	}
	return uaouo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (uaouo *UserAgentOrderUpdateOne) ClearDeletedAt() *UserAgentOrderUpdateOne {
	uaouo.mutation.ClearDeletedAt()
	return uaouo
}

// SetUserID sets the "user_id" field.
func (uaouo *UserAgentOrderUpdateOne) SetUserID(i int64) *UserAgentOrderUpdateOne {
	uaouo.mutation.ResetUserID()
	uaouo.mutation.SetUserID(i)
	return uaouo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (uaouo *UserAgentOrderUpdateOne) SetNillableUserID(i *int64) *UserAgentOrderUpdateOne {
	if i != nil {
		uaouo.SetUserID(*i)
	}
	return uaouo
}

// AddUserID adds i to the "user_id" field.
func (uaouo *UserAgentOrderUpdateOne) AddUserID(i int64) *UserAgentOrderUpdateOne {
	uaouo.mutation.AddUserID(i)
	return uaouo
}

// SetAgentID sets the "agent_id" field.
func (uaouo *UserAgentOrderUpdateOne) SetAgentID(i int64) *UserAgentOrderUpdateOne {
	uaouo.mutation.ResetAgentID()
	uaouo.mutation.SetAgentID(i)
	return uaouo
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (uaouo *UserAgentOrderUpdateOne) SetNillableAgentID(i *int64) *UserAgentOrderUpdateOne {
	if i != nil {
		uaouo.SetAgentID(*i)
	}
	return uaouo
}

// AddAgentID adds i to the "agent_id" field.
func (uaouo *UserAgentOrderUpdateOne) AddAgentID(i int64) *UserAgentOrderUpdateOne {
	uaouo.mutation.AddAgentID(i)
	return uaouo
}

// SetOrderIndex sets the "order_index" field.
func (uaouo *UserAgentOrderUpdateOne) SetOrderIndex(i int64) *UserAgentOrderUpdateOne {
	uaouo.mutation.ResetOrderIndex()
	uaouo.mutation.SetOrderIndex(i)
	return uaouo
}

// SetNillableOrderIndex sets the "order_index" field if the given value is not nil.
func (uaouo *UserAgentOrderUpdateOne) SetNillableOrderIndex(i *int64) *UserAgentOrderUpdateOne {
	if i != nil {
		uaouo.SetOrderIndex(*i)
	}
	return uaouo
}

// AddOrderIndex adds i to the "order_index" field.
func (uaouo *UserAgentOrderUpdateOne) AddOrderIndex(i int64) *UserAgentOrderUpdateOne {
	uaouo.mutation.AddOrderIndex(i)
	return uaouo
}

// Mutation returns the UserAgentOrderMutation object of the builder.
func (uaouo *UserAgentOrderUpdateOne) Mutation() *UserAgentOrderMutation {
	return uaouo.mutation
}

// Where appends a list predicates to the UserAgentOrderUpdate builder.
func (uaouo *UserAgentOrderUpdateOne) Where(ps ...predicate.UserAgentOrder) *UserAgentOrderUpdateOne {
	uaouo.mutation.Where(ps...)
	return uaouo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uaouo *UserAgentOrderUpdateOne) Select(field string, fields ...string) *UserAgentOrderUpdateOne {
	uaouo.fields = append([]string{field}, fields...)
	return uaouo
}

// Save executes the query and returns the updated UserAgentOrder entity.
func (uaouo *UserAgentOrderUpdateOne) Save(ctx context.Context) (*UserAgentOrder, error) {
	if err := uaouo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, uaouo.sqlSave, uaouo.mutation, uaouo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uaouo *UserAgentOrderUpdateOne) SaveX(ctx context.Context) *UserAgentOrder {
	node, err := uaouo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uaouo *UserAgentOrderUpdateOne) Exec(ctx context.Context) error {
	_, err := uaouo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uaouo *UserAgentOrderUpdateOne) ExecX(ctx context.Context) {
	if err := uaouo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uaouo *UserAgentOrderUpdateOne) defaults() error {
	if _, ok := uaouo.mutation.UpdatedAt(); !ok {
		if useragentorder.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized useragentorder.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := useragentorder.UpdateDefaultUpdatedAt()
		uaouo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (uaouo *UserAgentOrderUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *UserAgentOrderUpdateOne {
	uaouo.modifiers = append(uaouo.modifiers, modifiers...)
	return uaouo
}

func (uaouo *UserAgentOrderUpdateOne) sqlSave(ctx context.Context) (_node *UserAgentOrder, err error) {
	_spec := sqlgraph.NewUpdateSpec(useragentorder.Table, useragentorder.Columns, sqlgraph.NewFieldSpec(useragentorder.FieldID, field.TypeInt64))
	id, ok := uaouo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "UserAgentOrder.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uaouo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, useragentorder.FieldID)
		for _, f := range fields {
			if !useragentorder.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != useragentorder.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uaouo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uaouo.mutation.UpdatedAt(); ok {
		_spec.SetField(useragentorder.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uaouo.mutation.DeletedAt(); ok {
		_spec.SetField(useragentorder.FieldDeletedAt, field.TypeTime, value)
	}
	if uaouo.mutation.DeletedAtCleared() {
		_spec.ClearField(useragentorder.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := uaouo.mutation.UserID(); ok {
		_spec.SetField(useragentorder.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := uaouo.mutation.AddedUserID(); ok {
		_spec.AddField(useragentorder.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := uaouo.mutation.AgentID(); ok {
		_spec.SetField(useragentorder.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := uaouo.mutation.AddedAgentID(); ok {
		_spec.AddField(useragentorder.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := uaouo.mutation.OrderIndex(); ok {
		_spec.SetField(useragentorder.FieldOrderIndex, field.TypeInt64, value)
	}
	if value, ok := uaouo.mutation.AddedOrderIndex(); ok {
		_spec.AddField(useragentorder.FieldOrderIndex, field.TypeInt64, value)
	}
	_spec.AddModifiers(uaouo.modifiers...)
	_node = &UserAgentOrder{config: uaouo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uaouo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{useragentorder.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uaouo.mutation.done = true
	return _node, nil
}

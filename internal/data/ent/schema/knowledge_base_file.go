package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

type KnowledgeBaseFile struct {
	ent.Schema
}

func (KnowledgeBaseFile) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.Int64("knowledge_base_id").Default(0).Comment("知识库id"),
		field.Int32("data_type").Default(0).Comment("数据类型 1文档 2表格"),
		field.Int64("file_relation_id").Default(0).Comment("文件id"),
		field.String("metadata").Default("").Comment("元数据 excel_schema"),
		field.Int32("status").Default(0).Comment("状态 0等待 1处理中 2成功 3失败"),
		field.String("failed_reason").Default("").Comment("失败原因"),
	}
}

func (KnowledgeBaseFile) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TenantIDMixin{},
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

func (KnowledgeBaseFile) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "knowledge_base_file"},
	}
}

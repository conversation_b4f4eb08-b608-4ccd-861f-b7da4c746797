package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// AiChat holds the schema definition for the AiChat entity.
type AiChat struct {
	ent.Schema
}

// Fields of the AiChat.
func (AiChat) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("name").Comment("聊天名称"),
		// field.Int64("file_relation_id").Comment("文件id"),
		field.Int64("user_id").Comment("用户id"),
		field.Int64("chat_type").Comment("聊天类型 1.rag"),
		field.Int64("agent_id").Comment("agent id").Optional(),
	}
}

func (AiChat) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TenantIDMixin{},
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

// Edges of the AiChat.
func (AiChat) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("ai_agent", AiAgent.Type).Ref("ai_chat").Unique().Field("agent_id"),
	}
}

func (AiChat) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "ai_chat"},
	}
}

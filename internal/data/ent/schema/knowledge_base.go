package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

type KnowledgeBase struct {
	ent.Schema
}

func (KnowledgeBase) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("name").Default("").Comment("名称"),
		field.Bool("public").Default(false).Comment("是否公开"),
		field.Int32("data_type").Default(0).Comment("数据类型 1文档 2表格"),
		field.Int64("user_id").Default(0).Comment("用户id"),
		field.Other("manager_user_ids", &pq.Int64Array{}).SchemaType(map[string]string{dialect.Postgres: "bigint[]"}).Default(&pq.Int64Array{}).Comment("管理者用户ids"),
		field.Other("editable_user_ids", &pq.Int64Array{}).SchemaType(map[string]string{dialect.Postgres: "bigint[]"}).Default(&pq.Int64Array{}).Comment("可编辑用户ids"),
	}
}

func (KnowledgeBase) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TenantIDMixin{},
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

func (KnowledgeBase) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "knowledge_base"},
	}
}

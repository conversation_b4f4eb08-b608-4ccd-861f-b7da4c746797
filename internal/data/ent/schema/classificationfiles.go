package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
)

type ClassificationFiles struct {
	ent.Schema
}

func (ClassificationFiles) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("name").Default("").Comment(""),
		field.Int64("file_relation_id").Default(0).Comment(""),
		field.String("pre_entity_tag").Default("").Comment(""),
		field.String("entity_tag").Default("").Comment(""),
		field.String("filename").Default("").Comment(""),
		field.String("mime_type").Default("").Comment(""),
		field.Int64("user_id").Default(0).Comment(""),
		field.String("user_name").Default("").Comment(""),
		field.Other("dept_ids", &pq.Int64Array{}).SchemaType(map[string]string{dialect.Postgres: "bigint[]"}).Default(&pq.Int64Array{}).Comment(""),
		field.String("dept_name").Default("").Comment(""),
		field.String("path").Default("").Comment(""),
		field.Int32("security_level").Default(0).Comment(""),
		field.Int32("node_level").Default(0).Comment(""),
		field.Int32("check_status").Default(0).Comment(""),
		field.Int32("tree_type").Default(0).Comment(""),
	}
}

func (ClassificationFiles) Mixin() []ent.Mixin {
	return []ent.Mixin{}
}

func (ClassificationFiles) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "classification_files"},
	}
}

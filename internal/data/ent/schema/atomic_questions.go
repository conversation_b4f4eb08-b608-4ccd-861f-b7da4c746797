package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
)

type AtomicQuestions struct {
	ent.Schema
}

func (AtomicQuestions) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("entity_tag").Comment("entity_tag"),
		field.String("pre_entity_tag").Comment("pre_entity_tag"),
		field.Int64("file_relation_id").Comment("文件关系id"),
		field.Int64("chunk_index").Comment("chunk_index"),
		field.Int64("chunk_size").Comment("chunk_size"),
		field.Int64("index").Comment("index"),
		field.Other("question", &pq.StringArray{}).SchemaType(map[string]string{dialect.Postgres: "text[]"}).Default(&pq.StringArray{}).Comment("原子问题数组"),
		field.Bool("is_handle").Default(false).Comment("是否处理"),
	}
}

func (AtomicQuestions) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TimeMixin{},
	}
}

func (AtomicQuestions) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "atomic_questions"},
	}
}

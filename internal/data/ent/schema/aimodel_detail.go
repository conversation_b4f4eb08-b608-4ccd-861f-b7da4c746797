package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// AiModel holds the schema definition for the AiModel entity.
type AiModelDetail struct {
	ent.Schema
}

// Fields of the AiModel.
func (AiModelDetail) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("model_name").Comment("模型名称"),
		field.String("name").Comment("模型"),
		field.String("url").Comment("url"),
		field.String("avatar").Comment("头像"),
		field.Bool("can_internet_search").Comment("是否可以互联网搜索"),
		field.String("balance_search_url").Comment("余额查询URL"),
		field.Int64("thinking_enable_status").Comment("0:禁用思考 1:开启思考 2:可以动态开启关闭思考").Default(0),
		field.String("background_url").Comment("后台URL"),
	}
}

func (AiModelDetail) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

// Edges of the AiModel.
func (AiModelDetail) Edges() []ent.Edge {
	return nil
}

func (AiModelDetail) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "ai_model_detail"},
	}
}

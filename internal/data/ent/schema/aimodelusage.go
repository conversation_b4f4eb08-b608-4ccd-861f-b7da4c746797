package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// AiModelUsage holds the schema definition for the AiModelUsage entity.
type AiModelUsage struct {
	ent.Schema
}

// Fields of the AiModelUsage.
func (AiModelUsage) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.Int64("model_detail_id").Comment("模型详情ID"),
		field.String("model_name").Comment("模型名称"),
		field.String("model_gateway_name").Comment("模型网关名称"),
		field.Int64("agent_id").Comment("智能体ID"),
		field.String("agent_name").Comment("智能体名称"),
		field.Int64("user_id").Comment("用户ID"),
		field.String("user_name").Comment("用户名称"),
		field.String("question").Comment("用户提问"),
		field.String("answer").Comment("模型回答"),
		field.Int64("prompt_tokens").Comment("提示词token数量"),
		field.Int64("completion_tokens").Comment("完成token数量"),
		field.Int8("request_status").Comment("0: 未知, 1: 成功, 2: 失败").Default(0),
		field.String("error_code").Comment("错误码").Default(""),
	}
}

// Edges of the AiModelUsage.
func (AiModelUsage) Edges() []ent.Edge {
	return nil
}

func (AiModelUsage) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

func (AiModelUsage) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "ai_model_usage"},
	}
}

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// AiAgent holds the schema definition for the AiAgent entity.
type AiAgent struct {
	ent.Schema
}

// Fields of the AiAgent.
func (AiAgent) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("name").Comment("智能体名称"),
		field.String("description").Comment("描述"),
		field.String("avatar").Comment("头像"),
		field.String("clicked_avatar").Comment("点击头像"),
		field.String("welcome_msg").Comment("欢迎语"),
		field.String("fallback_msg").Comment("兜底回复"),
		field.Int64("owner_id").Comment("创建者ID"),
		field.Int8("visibility_type").Comment("可见性对象类型"),
		field.Other("manageable_to_user", &pq.Int64Array{}).SchemaType(map[string]string{dialect.Postgres: "bigint[]"}).Default(&pq.Int64Array{}).Comment("可管理用户id列表"),
		field.Other("visible_to_user", &pq.Int64Array{}).SchemaType(map[string]string{dialect.Postgres: "bigint[]"}).Default(&pq.Int64Array{}).Comment("可见用户id列表"),
		field.Other("visible_to_dept", &pq.Int64Array{}).SchemaType(map[string]string{dialect.Postgres: "bigint[]"}).Default(&pq.Int64Array{}).Comment("可见部门id列表"),
		field.Other("knowledge_base_ids", &pq.Int64Array{}).SchemaType(map[string]string{dialect.Postgres: "bigint[]"}).Default(&pq.Int64Array{}).Comment("知识库id列表"),
		field.String("schema").Comment("智能体编排schema"),
		field.Bool("is_public").Comment("是否公开"),
		field.Bool("is_enabled").Comment("是否启用"),
		field.Bool("is_ref_files").Comment("是否显示引用文件"),
		field.Int64("model_type").Comment("模型类型， 1：内部模型，2：外部模型").Default(1),
		field.Int64("model_id").Comment("模型ID").Default(1),
		field.Int64("use_count").Comment("使用次数").Default(0),
		field.Int64("knowledge_base_type").Comment("知识库类型，0：指定知识库 1：个人权限内的文件").Default(0),
		field.Bool("internet_search").Comment("是否开启互联网搜索").Default(false),
		field.Int64("agent_type").Comment("智能体类型，1：基础问答 2：检索深度问答 3：合同审核").Default(1),
		field.Bool("thinking").Comment("是否开启思考").Default(false),
		field.Int64("thinking_model_id").Comment("思考模型ID").Default(0),
		field.String("role_setting").Comment("角色设定"),
		field.Bool("upload_file").Comment("上传文件").Default(true),
		field.Bool("semantic_cache").Comment("语义缓存").Default(true),
	}
}

func (AiAgent) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

// Edges of the AiAgent.
func (AiAgent) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("ai_chat", AiChat.Type),
	}
}

func (AiAgent) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "ai_agent"},
	}
}

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// AiAgentSecurityPolicy holds the schema definition for the AiAgentSecurityPolicy entity.
type AiAgentSecurityPolicy struct {
	ent.Schema
}

// Fields of the AiAgentSecurityPolicy.
func (AiAgentSecurityPolicy) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("name").Comment("策略名称"),
		field.Int64("agent_id").Comment("agent id"),
		field.Int64("policy_category").Comment("策略分类 1.敏感信息匹配"),
		field.Int64("risk_level").Comment("风险等级 1.低 2.中 3.高"),
		field.Bool("enabled").Comment("是否启用").Default(true),
		field.Other("policies", &pq.StringArray{}).SchemaType(map[string]string{dialect.Postgres: "text[]"}).Default(&pq.StringArray{}).Comment("策略内容"),
		field.Int64("hit_action").Comment("命中策略后的操作 1.阻断 2.警告").Default(1),
		field.String("hit_response").Comment("命中策略后的响应内容").Default(""),
		field.Int64("updated_by").Comment("修改人id"),
	}
}

func (AiAgentSecurityPolicy) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

// Edges of the AiAgentSecurityPolicy.
func (AiAgentSecurityPolicy) Edges() []ent.Edge {
	return nil
}

func (AiAgentSecurityPolicy) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "ai_agent_security_policy"},
	}
}

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// AiChatItem holds the schema definition for the AiChatItem entity.
type AiChatItem struct {
	ent.Schema
}

// Fields of the AiChatItem.
func (AiChatItem) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.Int64("chat_id").Comment("chat id"),
		field.Int64("object_id").Comment("消息对象id"),
		field.Int64("object_type").Comment("消息对象类型 0.系统 1.用户"),
		field.String("message").Comment("消息内容"),
		field.String("ref_files").Comment("引用文件"),
		field.Int8("agree_status").Comment("点赞状态 0.默认 1.点赞 2.批评").Default(0),
		field.Int64("round_id").Comment("用于多轮对话").Default(0),
		field.String("pc_name").Comment("主机名称").Default(""),
		field.String("reason").Comment("深度思考内容").Default(""),
		field.String("primary_classification").Comment("提问词一级分类").Default(""),
		field.String("secondary_classification").Comment("提问词二级分类").Default(""),
		field.Other("mine_types", &pq.StringArray{}).SchemaType(map[string]string{dialect.Postgres: "varchar[]"}).Default(&pq.StringArray{}).Comment("引用文件类型列表"),
		field.Other("suggest_questions", &pq.StringArray{}).SchemaType(map[string]string{dialect.Postgres: "varchar[]"}).Default(&pq.StringArray{}).Comment("建议问题"),
		field.Int64("hit_action").Comment("1.阻断 2.警告").Default(0),
		field.String("hit_response").Comment("阻断或警告的响应内容").Default(""),
		field.Bool("hit_continue_send").Comment("是否继续发送").Default(false),
		field.Bool("is_internet_search").Comment("是否互联网搜索").Default(false),
	}
}

func (AiChatItem) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TenantIDMixin{},
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

// Edges of the AiChatItem.
func (AiChatItem) Edges() []ent.Edge {
	return nil
}

func (AiChatItem) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "ai_chat_item"},
	}
}

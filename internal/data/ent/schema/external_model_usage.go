package schema

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

type ExternalModelUsage struct {
	ent.Schema
}

func (ExternalModelUsage) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("model_name").Default("").Comment("模型名称"),
		field.String("question").Default("").Comment("问题"),
		field.String("question_tag").Default("").Comment("问题分类"),
		field.String("files").Default("").Comment("上传的文件"),
		field.Other("mime_types", &pq.StringArray{}).SchemaType(map[string]string{dialect.Postgres: "text[]"}).Default(&pq.StringArray{}).Comment("文件类型"),
		field.Int64("user_id").Default(0).Comment("用户ID"),
		field.String("user_name").Default("").Comment("用户名称"),
		field.Int64("dept_id").Default(0).Comment("部门ID"),
		field.String("dept_name").Default("").Comment("部门名称"),
		field.String("pc_name").Default("").Comment("主机名称"),
		field.Time("happened_at").Default(time.Time{}).Comment("发生时间"),
	}
}

func (ExternalModelUsage) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

func (ExternalModelUsage) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "external_model_usage"},
	}
}

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// AiModel holds the schema definition for the AiModel entity.
type AiModel struct {
	ent.Schema
}

// Fields of the AiModel.
func (AiModel) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("model_name").Comment("模型名称"),
		field.Int64("model").Comment("模型"),
		field.String("api_key").Comment("api_key"),
	}
}

func (AiModel) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

// Edges of the AiModel.
func (AiModel) Edges() []ent.Edge {
	return nil
}

func (AiModel) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "ai_model"},
	}
}

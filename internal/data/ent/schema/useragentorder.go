package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// UserAgentOrder holds the schema definition for the UserAgentOrder entity.
type UserAgentOrder struct {
	ent.Schema
}

// Fields of the UserAgentOrder.
func (UserAgentOrder) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.Int64("user_id").Comment("用户id").Default(0),
		field.Int64("agent_id").Comment("智能体id").Default(0),
		field.Int64("order_index").Comment("排序索引").Default(0),
	}
}

// Edges of the UserAgentOrder.
func (UserAgentOrder) Edges() []ent.Edge {
	return nil
}

func (UserAgentOrder) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

func (UserAgentOrder) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "user_agent_order"},
	}
}

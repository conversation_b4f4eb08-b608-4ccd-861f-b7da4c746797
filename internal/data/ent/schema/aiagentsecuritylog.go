package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// AiAgentSecurityLog holds the schema definition for the AiAgentSecurityLog entity.
type AiAgentSecurityLog struct {
	ent.Schema
}

// Fields of the AiAgentSecurityLog.
func (AiAgentSecurityLog) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.Int64("risk_level").Comment("风险等级 1.低 2.中 3.高"),
		field.Int64("user_id").Comment("用户ID"),
		field.String("user_name").Comment("用户名"),
		field.Int64("dept_id").Comment("部门ID"),
		field.String("dept_name").Comment("部门名称"),
		field.String("pc_name").Comment("PC名称"),
		field.Int64("agent_id").Comment("Agent ID"),
		field.String("agent_name").Comment("Agent名称"),
		field.String("agent_description").Comment("Agent描述"),
		field.Int64("hit_action").Comment("命中策略后的操作 1.阻断 2.警告").Default(1),
		field.String("question").Comment("用户提问"),
		field.Int64("action_category").Comment("操作分类 1.智能问答").Default(1),
		field.Other("uploaded_files", &pq.StringArray{}).SchemaType(map[string]string{dialect.Postgres: "text[]"}).Default(&pq.StringArray{}).Comment("上传文件信息"),
		field.Other("hit_policies", &pq.StringArray{}).SchemaType(map[string]string{dialect.Postgres: "text[]"}).Default(&pq.StringArray{}).Comment("命中策略内容"),
	}
}

// Edges of the AiAgentSecurityLog.
func (AiAgentSecurityLog) Edges() []ent.Edge {
	return nil
}

func (AiAgentSecurityLog) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

func (AiAgentSecurityLog) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "ai_agent_security_log"},
	}
}

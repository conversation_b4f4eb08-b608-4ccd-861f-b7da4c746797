package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/BackendTeam/pkg/entx"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
)

// DefaultAgentAvatar holds the schema definition for the DefaultAgentAvatar entity.
type DefaultAgentAvatar struct {
	ent.Schema
}

// Fields of the DefaultAgentAvatar.
func (DefaultAgentAvatar) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("id").Comment("主键"),
		field.String("avatar").Comment("头像key"),
		field.String("clicked_avatar").Comment("点击头像key"),
		field.Int8("avatar_type").Comment("头像类型 1.部门应用图标  2.数字员工图标"),
	}
}

// Edges of the DefaultAgentAvatar.
func (DefaultAgentAvatar) Edges() []ent.Edge {
	return nil
}

func (DefaultAgentAvatar) Mixin() []ent.Mixin {
	return []ent.Mixin{
		entx.TenantIDMixin{},
		entx.TimeMixin{},
		mixin.SoftDelete{},
	}
}

func (DefaultAgentAvatar) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "default_agent_avatar"},
	}
}

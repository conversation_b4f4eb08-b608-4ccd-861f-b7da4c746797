// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"

	"entgo.io/ent/dialect/sql"
)

// Database is the client that holds all ent builders.
type Database struct {
	client *Client
}

// NewDatabase creates a new database configured with the given options.
func NewDatabase(opts ...Option) *Database {
	return &Database{client: NewClient(opts...)}
}

// InTx runs the given function f within a transaction.
func (db *Database) InTx(ctx context.Context, f func(context.Context) error) error {
	tx := TxFromContext(ctx)
	if tx != nil {
		return f(ctx)
	}

	tx, err := db.client.Tx(ctx)
	if err != nil {
		return fmt.Errorf("starting transaction: %w", err)
	}

	if err = f(NewTxContext(ctx, tx)); err != nil {
		if err2 := tx.Rollback(); err2 != nil {
			return fmt.Errorf("rolling back transaction: %v (original error: %w)", err2, err)
		}
		return err
	}

	if err := tx.Commit(); err != nil {
		return fmt.Errorf("committing transaction: %w", err)
	}
	return nil
}

func (db *Database) loadClient(ctx context.Context) *Client {
	tx := TxFromContext(ctx)
	if tx != nil {
		return tx.Client()
	}
	return db.client
}

// Exec executes a query that doesn't return rows. For example, in SQL, INSERT or UPDATE.
func (db *Database) Exec(ctx context.Context, query string, args ...interface{}) (*sql.Result, error) {
	var res sql.Result
	err := db.loadClient(ctx).driver.Exec(ctx, query, args, &res)
	if err != nil {
		return nil, err
	}
	return &res, nil
}

// Query executes a query that returns rows, typically a SELECT in SQL.
func (db *Database) Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	var rows sql.Rows
	err := db.loadClient(ctx).driver.Query(ctx, query, args, &rows)
	if err != nil {
		return nil, err
	}
	return &rows, nil
}

// AiAgent is the client for interacting with the AiAgent builders.
func (db *Database) AiAgent(ctx context.Context) *AiAgentClient {
	return db.loadClient(ctx).AiAgent
}

// AiAgentSecurityLog is the client for interacting with the AiAgentSecurityLog builders.
func (db *Database) AiAgentSecurityLog(ctx context.Context) *AiAgentSecurityLogClient {
	return db.loadClient(ctx).AiAgentSecurityLog
}

// AiAgentSecurityPolicy is the client for interacting with the AiAgentSecurityPolicy builders.
func (db *Database) AiAgentSecurityPolicy(ctx context.Context) *AiAgentSecurityPolicyClient {
	return db.loadClient(ctx).AiAgentSecurityPolicy
}

// AiChat is the client for interacting with the AiChat builders.
func (db *Database) AiChat(ctx context.Context) *AiChatClient {
	return db.loadClient(ctx).AiChat
}

// AiChatItem is the client for interacting with the AiChatItem builders.
func (db *Database) AiChatItem(ctx context.Context) *AiChatItemClient {
	return db.loadClient(ctx).AiChatItem
}

// AiModel is the client for interacting with the AiModel builders.
func (db *Database) AiModel(ctx context.Context) *AiModelClient {
	return db.loadClient(ctx).AiModel
}

// AiModelDetail is the client for interacting with the AiModelDetail builders.
func (db *Database) AiModelDetail(ctx context.Context) *AiModelDetailClient {
	return db.loadClient(ctx).AiModelDetail
}

// AiModelUsage is the client for interacting with the AiModelUsage builders.
func (db *Database) AiModelUsage(ctx context.Context) *AiModelUsageClient {
	return db.loadClient(ctx).AiModelUsage
}

// AtomicQuestions is the client for interacting with the AtomicQuestions builders.
func (db *Database) AtomicQuestions(ctx context.Context) *AtomicQuestionsClient {
	return db.loadClient(ctx).AtomicQuestions
}

// ClassificationFiles is the client for interacting with the ClassificationFiles builders.
func (db *Database) ClassificationFiles(ctx context.Context) *ClassificationFilesClient {
	return db.loadClient(ctx).ClassificationFiles
}

// DefaultAgentAvatar is the client for interacting with the DefaultAgentAvatar builders.
func (db *Database) DefaultAgentAvatar(ctx context.Context) *DefaultAgentAvatarClient {
	return db.loadClient(ctx).DefaultAgentAvatar
}

// ExternalModelUsage is the client for interacting with the ExternalModelUsage builders.
func (db *Database) ExternalModelUsage(ctx context.Context) *ExternalModelUsageClient {
	return db.loadClient(ctx).ExternalModelUsage
}

// KnowledgeBase is the client for interacting with the KnowledgeBase builders.
func (db *Database) KnowledgeBase(ctx context.Context) *KnowledgeBaseClient {
	return db.loadClient(ctx).KnowledgeBase
}

// KnowledgeBaseFile is the client for interacting with the KnowledgeBaseFile builders.
func (db *Database) KnowledgeBaseFile(ctx context.Context) *KnowledgeBaseFileClient {
	return db.loadClient(ctx).KnowledgeBaseFile
}

// UserAgentOrder is the client for interacting with the UserAgentOrder builders.
func (db *Database) UserAgentOrder(ctx context.Context) *UserAgentOrderClient {
	return db.loadClient(ctx).UserAgentOrder
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/useragentorder"
)

// UserAgentOrderCreate is the builder for creating a UserAgentOrder entity.
type UserAgentOrderCreate struct {
	config
	mutation *UserAgentOrderMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (uaoc *UserAgentOrderCreate) SetCreatedAt(t time.Time) *UserAgentOrderCreate {
	uaoc.mutation.SetCreatedAt(t)
	return uaoc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (uaoc *UserAgentOrderCreate) SetNillableCreatedAt(t *time.Time) *UserAgentOrderCreate {
	if t != nil {
		uaoc.SetCreatedAt(*t)
	}
	return uaoc
}

// SetUpdatedAt sets the "updated_at" field.
func (uaoc *UserAgentOrderCreate) SetUpdatedAt(t time.Time) *UserAgentOrderCreate {
	uaoc.mutation.SetUpdatedAt(t)
	return uaoc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (uaoc *UserAgentOrderCreate) SetNillableUpdatedAt(t *time.Time) *UserAgentOrderCreate {
	if t != nil {
		uaoc.SetUpdatedAt(*t)
	}
	return uaoc
}

// SetDeletedAt sets the "deleted_at" field.
func (uaoc *UserAgentOrderCreate) SetDeletedAt(t time.Time) *UserAgentOrderCreate {
	uaoc.mutation.SetDeletedAt(t)
	return uaoc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uaoc *UserAgentOrderCreate) SetNillableDeletedAt(t *time.Time) *UserAgentOrderCreate {
	if t != nil {
		uaoc.SetDeletedAt(*t)
	}
	return uaoc
}

// SetUserID sets the "user_id" field.
func (uaoc *UserAgentOrderCreate) SetUserID(i int64) *UserAgentOrderCreate {
	uaoc.mutation.SetUserID(i)
	return uaoc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (uaoc *UserAgentOrderCreate) SetNillableUserID(i *int64) *UserAgentOrderCreate {
	if i != nil {
		uaoc.SetUserID(*i)
	}
	return uaoc
}

// SetAgentID sets the "agent_id" field.
func (uaoc *UserAgentOrderCreate) SetAgentID(i int64) *UserAgentOrderCreate {
	uaoc.mutation.SetAgentID(i)
	return uaoc
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (uaoc *UserAgentOrderCreate) SetNillableAgentID(i *int64) *UserAgentOrderCreate {
	if i != nil {
		uaoc.SetAgentID(*i)
	}
	return uaoc
}

// SetOrderIndex sets the "order_index" field.
func (uaoc *UserAgentOrderCreate) SetOrderIndex(i int64) *UserAgentOrderCreate {
	uaoc.mutation.SetOrderIndex(i)
	return uaoc
}

// SetNillableOrderIndex sets the "order_index" field if the given value is not nil.
func (uaoc *UserAgentOrderCreate) SetNillableOrderIndex(i *int64) *UserAgentOrderCreate {
	if i != nil {
		uaoc.SetOrderIndex(*i)
	}
	return uaoc
}

// SetID sets the "id" field.
func (uaoc *UserAgentOrderCreate) SetID(i int64) *UserAgentOrderCreate {
	uaoc.mutation.SetID(i)
	return uaoc
}

// Mutation returns the UserAgentOrderMutation object of the builder.
func (uaoc *UserAgentOrderCreate) Mutation() *UserAgentOrderMutation {
	return uaoc.mutation
}

// Save creates the UserAgentOrder in the database.
func (uaoc *UserAgentOrderCreate) Save(ctx context.Context) (*UserAgentOrder, error) {
	if err := uaoc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, uaoc.sqlSave, uaoc.mutation, uaoc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (uaoc *UserAgentOrderCreate) SaveX(ctx context.Context) *UserAgentOrder {
	v, err := uaoc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uaoc *UserAgentOrderCreate) Exec(ctx context.Context) error {
	_, err := uaoc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uaoc *UserAgentOrderCreate) ExecX(ctx context.Context) {
	if err := uaoc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uaoc *UserAgentOrderCreate) defaults() error {
	if _, ok := uaoc.mutation.CreatedAt(); !ok {
		if useragentorder.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized useragentorder.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := useragentorder.DefaultCreatedAt()
		uaoc.mutation.SetCreatedAt(v)
	}
	if _, ok := uaoc.mutation.UpdatedAt(); !ok {
		if useragentorder.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized useragentorder.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := useragentorder.DefaultUpdatedAt()
		uaoc.mutation.SetUpdatedAt(v)
	}
	if _, ok := uaoc.mutation.DeletedAt(); !ok {
		v := useragentorder.DefaultDeletedAt
		uaoc.mutation.SetDeletedAt(v)
	}
	if _, ok := uaoc.mutation.UserID(); !ok {
		v := useragentorder.DefaultUserID
		uaoc.mutation.SetUserID(v)
	}
	if _, ok := uaoc.mutation.AgentID(); !ok {
		v := useragentorder.DefaultAgentID
		uaoc.mutation.SetAgentID(v)
	}
	if _, ok := uaoc.mutation.OrderIndex(); !ok {
		v := useragentorder.DefaultOrderIndex
		uaoc.mutation.SetOrderIndex(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (uaoc *UserAgentOrderCreate) check() error {
	if _, ok := uaoc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "UserAgentOrder.created_at"`)}
	}
	if _, ok := uaoc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "UserAgentOrder.updated_at"`)}
	}
	if _, ok := uaoc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "UserAgentOrder.user_id"`)}
	}
	if _, ok := uaoc.mutation.AgentID(); !ok {
		return &ValidationError{Name: "agent_id", err: errors.New(`ent: missing required field "UserAgentOrder.agent_id"`)}
	}
	if _, ok := uaoc.mutation.OrderIndex(); !ok {
		return &ValidationError{Name: "order_index", err: errors.New(`ent: missing required field "UserAgentOrder.order_index"`)}
	}
	return nil
}

func (uaoc *UserAgentOrderCreate) sqlSave(ctx context.Context) (*UserAgentOrder, error) {
	if err := uaoc.check(); err != nil {
		return nil, err
	}
	_node, _spec := uaoc.createSpec()
	if err := sqlgraph.CreateNode(ctx, uaoc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	uaoc.mutation.id = &_node.ID
	uaoc.mutation.done = true
	return _node, nil
}

func (uaoc *UserAgentOrderCreate) createSpec() (*UserAgentOrder, *sqlgraph.CreateSpec) {
	var (
		_node = &UserAgentOrder{config: uaoc.config}
		_spec = sqlgraph.NewCreateSpec(useragentorder.Table, sqlgraph.NewFieldSpec(useragentorder.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = uaoc.conflict
	if id, ok := uaoc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := uaoc.mutation.CreatedAt(); ok {
		_spec.SetField(useragentorder.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := uaoc.mutation.UpdatedAt(); ok {
		_spec.SetField(useragentorder.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := uaoc.mutation.DeletedAt(); ok {
		_spec.SetField(useragentorder.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := uaoc.mutation.UserID(); ok {
		_spec.SetField(useragentorder.FieldUserID, field.TypeInt64, value)
		_node.UserID = value
	}
	if value, ok := uaoc.mutation.AgentID(); ok {
		_spec.SetField(useragentorder.FieldAgentID, field.TypeInt64, value)
		_node.AgentID = value
	}
	if value, ok := uaoc.mutation.OrderIndex(); ok {
		_spec.SetField(useragentorder.FieldOrderIndex, field.TypeInt64, value)
		_node.OrderIndex = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.UserAgentOrder.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.UserAgentOrderUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (uaoc *UserAgentOrderCreate) OnConflict(opts ...sql.ConflictOption) *UserAgentOrderUpsertOne {
	uaoc.conflict = opts
	return &UserAgentOrderUpsertOne{
		create: uaoc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.UserAgentOrder.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (uaoc *UserAgentOrderCreate) OnConflictColumns(columns ...string) *UserAgentOrderUpsertOne {
	uaoc.conflict = append(uaoc.conflict, sql.ConflictColumns(columns...))
	return &UserAgentOrderUpsertOne{
		create: uaoc,
	}
}

type (
	// UserAgentOrderUpsertOne is the builder for "upsert"-ing
	//  one UserAgentOrder node.
	UserAgentOrderUpsertOne struct {
		create *UserAgentOrderCreate
	}

	// UserAgentOrderUpsert is the "OnConflict" setter.
	UserAgentOrderUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *UserAgentOrderUpsert) SetUpdatedAt(v time.Time) *UserAgentOrderUpsert {
	u.Set(useragentorder.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *UserAgentOrderUpsert) UpdateUpdatedAt() *UserAgentOrderUpsert {
	u.SetExcluded(useragentorder.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *UserAgentOrderUpsert) SetDeletedAt(v time.Time) *UserAgentOrderUpsert {
	u.Set(useragentorder.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *UserAgentOrderUpsert) UpdateDeletedAt() *UserAgentOrderUpsert {
	u.SetExcluded(useragentorder.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *UserAgentOrderUpsert) ClearDeletedAt() *UserAgentOrderUpsert {
	u.SetNull(useragentorder.FieldDeletedAt)
	return u
}

// SetUserID sets the "user_id" field.
func (u *UserAgentOrderUpsert) SetUserID(v int64) *UserAgentOrderUpsert {
	u.Set(useragentorder.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *UserAgentOrderUpsert) UpdateUserID() *UserAgentOrderUpsert {
	u.SetExcluded(useragentorder.FieldUserID)
	return u
}

// AddUserID adds v to the "user_id" field.
func (u *UserAgentOrderUpsert) AddUserID(v int64) *UserAgentOrderUpsert {
	u.Add(useragentorder.FieldUserID, v)
	return u
}

// SetAgentID sets the "agent_id" field.
func (u *UserAgentOrderUpsert) SetAgentID(v int64) *UserAgentOrderUpsert {
	u.Set(useragentorder.FieldAgentID, v)
	return u
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *UserAgentOrderUpsert) UpdateAgentID() *UserAgentOrderUpsert {
	u.SetExcluded(useragentorder.FieldAgentID)
	return u
}

// AddAgentID adds v to the "agent_id" field.
func (u *UserAgentOrderUpsert) AddAgentID(v int64) *UserAgentOrderUpsert {
	u.Add(useragentorder.FieldAgentID, v)
	return u
}

// SetOrderIndex sets the "order_index" field.
func (u *UserAgentOrderUpsert) SetOrderIndex(v int64) *UserAgentOrderUpsert {
	u.Set(useragentorder.FieldOrderIndex, v)
	return u
}

// UpdateOrderIndex sets the "order_index" field to the value that was provided on create.
func (u *UserAgentOrderUpsert) UpdateOrderIndex() *UserAgentOrderUpsert {
	u.SetExcluded(useragentorder.FieldOrderIndex)
	return u
}

// AddOrderIndex adds v to the "order_index" field.
func (u *UserAgentOrderUpsert) AddOrderIndex(v int64) *UserAgentOrderUpsert {
	u.Add(useragentorder.FieldOrderIndex, v)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.UserAgentOrder.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(useragentorder.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *UserAgentOrderUpsertOne) UpdateNewValues() *UserAgentOrderUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(useragentorder.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(useragentorder.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.UserAgentOrder.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *UserAgentOrderUpsertOne) Ignore() *UserAgentOrderUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *UserAgentOrderUpsertOne) DoNothing() *UserAgentOrderUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the UserAgentOrderCreate.OnConflict
// documentation for more info.
func (u *UserAgentOrderUpsertOne) Update(set func(*UserAgentOrderUpsert)) *UserAgentOrderUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&UserAgentOrderUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *UserAgentOrderUpsertOne) SetUpdatedAt(v time.Time) *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *UserAgentOrderUpsertOne) UpdateUpdatedAt() *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *UserAgentOrderUpsertOne) SetDeletedAt(v time.Time) *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *UserAgentOrderUpsertOne) UpdateDeletedAt() *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *UserAgentOrderUpsertOne) ClearDeletedAt() *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.ClearDeletedAt()
	})
}

// SetUserID sets the "user_id" field.
func (u *UserAgentOrderUpsertOne) SetUserID(v int64) *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *UserAgentOrderUpsertOne) AddUserID(v int64) *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *UserAgentOrderUpsertOne) UpdateUserID() *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateUserID()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *UserAgentOrderUpsertOne) SetAgentID(v int64) *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetAgentID(v)
	})
}

// AddAgentID adds v to the "agent_id" field.
func (u *UserAgentOrderUpsertOne) AddAgentID(v int64) *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.AddAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *UserAgentOrderUpsertOne) UpdateAgentID() *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateAgentID()
	})
}

// SetOrderIndex sets the "order_index" field.
func (u *UserAgentOrderUpsertOne) SetOrderIndex(v int64) *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetOrderIndex(v)
	})
}

// AddOrderIndex adds v to the "order_index" field.
func (u *UserAgentOrderUpsertOne) AddOrderIndex(v int64) *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.AddOrderIndex(v)
	})
}

// UpdateOrderIndex sets the "order_index" field to the value that was provided on create.
func (u *UserAgentOrderUpsertOne) UpdateOrderIndex() *UserAgentOrderUpsertOne {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateOrderIndex()
	})
}

// Exec executes the query.
func (u *UserAgentOrderUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for UserAgentOrderCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *UserAgentOrderUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *UserAgentOrderUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *UserAgentOrderUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// UserAgentOrderCreateBulk is the builder for creating many UserAgentOrder entities in bulk.
type UserAgentOrderCreateBulk struct {
	config
	err      error
	builders []*UserAgentOrderCreate
	conflict []sql.ConflictOption
}

// Save creates the UserAgentOrder entities in the database.
func (uaocb *UserAgentOrderCreateBulk) Save(ctx context.Context) ([]*UserAgentOrder, error) {
	if uaocb.err != nil {
		return nil, uaocb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(uaocb.builders))
	nodes := make([]*UserAgentOrder, len(uaocb.builders))
	mutators := make([]Mutator, len(uaocb.builders))
	for i := range uaocb.builders {
		func(i int, root context.Context) {
			builder := uaocb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UserAgentOrderMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, uaocb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = uaocb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, uaocb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, uaocb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (uaocb *UserAgentOrderCreateBulk) SaveX(ctx context.Context) []*UserAgentOrder {
	v, err := uaocb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uaocb *UserAgentOrderCreateBulk) Exec(ctx context.Context) error {
	_, err := uaocb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uaocb *UserAgentOrderCreateBulk) ExecX(ctx context.Context) {
	if err := uaocb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.UserAgentOrder.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.UserAgentOrderUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (uaocb *UserAgentOrderCreateBulk) OnConflict(opts ...sql.ConflictOption) *UserAgentOrderUpsertBulk {
	uaocb.conflict = opts
	return &UserAgentOrderUpsertBulk{
		create: uaocb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.UserAgentOrder.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (uaocb *UserAgentOrderCreateBulk) OnConflictColumns(columns ...string) *UserAgentOrderUpsertBulk {
	uaocb.conflict = append(uaocb.conflict, sql.ConflictColumns(columns...))
	return &UserAgentOrderUpsertBulk{
		create: uaocb,
	}
}

// UserAgentOrderUpsertBulk is the builder for "upsert"-ing
// a bulk of UserAgentOrder nodes.
type UserAgentOrderUpsertBulk struct {
	create *UserAgentOrderCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.UserAgentOrder.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(useragentorder.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *UserAgentOrderUpsertBulk) UpdateNewValues() *UserAgentOrderUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(useragentorder.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(useragentorder.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.UserAgentOrder.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *UserAgentOrderUpsertBulk) Ignore() *UserAgentOrderUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *UserAgentOrderUpsertBulk) DoNothing() *UserAgentOrderUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the UserAgentOrderCreateBulk.OnConflict
// documentation for more info.
func (u *UserAgentOrderUpsertBulk) Update(set func(*UserAgentOrderUpsert)) *UserAgentOrderUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&UserAgentOrderUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *UserAgentOrderUpsertBulk) SetUpdatedAt(v time.Time) *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *UserAgentOrderUpsertBulk) UpdateUpdatedAt() *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *UserAgentOrderUpsertBulk) SetDeletedAt(v time.Time) *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *UserAgentOrderUpsertBulk) UpdateDeletedAt() *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *UserAgentOrderUpsertBulk) ClearDeletedAt() *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.ClearDeletedAt()
	})
}

// SetUserID sets the "user_id" field.
func (u *UserAgentOrderUpsertBulk) SetUserID(v int64) *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *UserAgentOrderUpsertBulk) AddUserID(v int64) *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *UserAgentOrderUpsertBulk) UpdateUserID() *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateUserID()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *UserAgentOrderUpsertBulk) SetAgentID(v int64) *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetAgentID(v)
	})
}

// AddAgentID adds v to the "agent_id" field.
func (u *UserAgentOrderUpsertBulk) AddAgentID(v int64) *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.AddAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *UserAgentOrderUpsertBulk) UpdateAgentID() *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateAgentID()
	})
}

// SetOrderIndex sets the "order_index" field.
func (u *UserAgentOrderUpsertBulk) SetOrderIndex(v int64) *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.SetOrderIndex(v)
	})
}

// AddOrderIndex adds v to the "order_index" field.
func (u *UserAgentOrderUpsertBulk) AddOrderIndex(v int64) *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.AddOrderIndex(v)
	})
}

// UpdateOrderIndex sets the "order_index" field to the value that was provided on create.
func (u *UserAgentOrderUpsertBulk) UpdateOrderIndex() *UserAgentOrderUpsertBulk {
	return u.Update(func(s *UserAgentOrderUpsert) {
		s.UpdateOrderIndex()
	})
}

// Exec executes the query.
func (u *UserAgentOrderUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the UserAgentOrderCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for UserAgentOrderCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *UserAgentOrderUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

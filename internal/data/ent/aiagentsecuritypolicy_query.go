// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritypolicy"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiAgentSecurityPolicyQuery is the builder for querying AiAgentSecurityPolicy entities.
type AiAgentSecurityPolicyQuery struct {
	config
	ctx        *QueryContext
	order      []aiagentsecuritypolicy.OrderOption
	inters     []Interceptor
	predicates []predicate.AiAgentSecurityPolicy
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AiAgentSecurityPolicyQuery builder.
func (aaspq *AiAgentSecurityPolicyQuery) Where(ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyQuery {
	aaspq.predicates = append(aaspq.predicates, ps...)
	return aaspq
}

// Limit the number of records to be returned by this query.
func (aaspq *AiAgentSecurityPolicyQuery) Limit(limit int) *AiAgentSecurityPolicyQuery {
	aaspq.ctx.Limit = &limit
	return aaspq
}

// Offset to start from.
func (aaspq *AiAgentSecurityPolicyQuery) Offset(offset int) *AiAgentSecurityPolicyQuery {
	aaspq.ctx.Offset = &offset
	return aaspq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (aaspq *AiAgentSecurityPolicyQuery) Unique(unique bool) *AiAgentSecurityPolicyQuery {
	aaspq.ctx.Unique = &unique
	return aaspq
}

// Order specifies how the records should be ordered.
func (aaspq *AiAgentSecurityPolicyQuery) Order(o ...aiagentsecuritypolicy.OrderOption) *AiAgentSecurityPolicyQuery {
	aaspq.order = append(aaspq.order, o...)
	return aaspq
}

// First returns the first AiAgentSecurityPolicy entity from the query.
// Returns a *NotFoundError when no AiAgentSecurityPolicy was found.
func (aaspq *AiAgentSecurityPolicyQuery) First(ctx context.Context) (*AiAgentSecurityPolicy, error) {
	nodes, err := aaspq.Limit(1).All(setContextOp(ctx, aaspq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{aiagentsecuritypolicy.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (aaspq *AiAgentSecurityPolicyQuery) FirstX(ctx context.Context) *AiAgentSecurityPolicy {
	node, err := aaspq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AiAgentSecurityPolicy ID from the query.
// Returns a *NotFoundError when no AiAgentSecurityPolicy ID was found.
func (aaspq *AiAgentSecurityPolicyQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = aaspq.Limit(1).IDs(setContextOp(ctx, aaspq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{aiagentsecuritypolicy.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (aaspq *AiAgentSecurityPolicyQuery) FirstIDX(ctx context.Context) int64 {
	id, err := aaspq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AiAgentSecurityPolicy entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AiAgentSecurityPolicy entity is found.
// Returns a *NotFoundError when no AiAgentSecurityPolicy entities are found.
func (aaspq *AiAgentSecurityPolicyQuery) Only(ctx context.Context) (*AiAgentSecurityPolicy, error) {
	nodes, err := aaspq.Limit(2).All(setContextOp(ctx, aaspq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{aiagentsecuritypolicy.Label}
	default:
		return nil, &NotSingularError{aiagentsecuritypolicy.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (aaspq *AiAgentSecurityPolicyQuery) OnlyX(ctx context.Context) *AiAgentSecurityPolicy {
	node, err := aaspq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AiAgentSecurityPolicy ID in the query.
// Returns a *NotSingularError when more than one AiAgentSecurityPolicy ID is found.
// Returns a *NotFoundError when no entities are found.
func (aaspq *AiAgentSecurityPolicyQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = aaspq.Limit(2).IDs(setContextOp(ctx, aaspq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{aiagentsecuritypolicy.Label}
	default:
		err = &NotSingularError{aiagentsecuritypolicy.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (aaspq *AiAgentSecurityPolicyQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := aaspq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AiAgentSecurityPolicies.
func (aaspq *AiAgentSecurityPolicyQuery) All(ctx context.Context) ([]*AiAgentSecurityPolicy, error) {
	ctx = setContextOp(ctx, aaspq.ctx, "All")
	if err := aaspq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AiAgentSecurityPolicy, *AiAgentSecurityPolicyQuery]()
	return withInterceptors[[]*AiAgentSecurityPolicy](ctx, aaspq, qr, aaspq.inters)
}

// AllX is like All, but panics if an error occurs.
func (aaspq *AiAgentSecurityPolicyQuery) AllX(ctx context.Context) []*AiAgentSecurityPolicy {
	nodes, err := aaspq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AiAgentSecurityPolicy IDs.
func (aaspq *AiAgentSecurityPolicyQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if aaspq.ctx.Unique == nil && aaspq.path != nil {
		aaspq.Unique(true)
	}
	ctx = setContextOp(ctx, aaspq.ctx, "IDs")
	if err = aaspq.Select(aiagentsecuritypolicy.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (aaspq *AiAgentSecurityPolicyQuery) IDsX(ctx context.Context) []int64 {
	ids, err := aaspq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (aaspq *AiAgentSecurityPolicyQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, aaspq.ctx, "Count")
	if err := aaspq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, aaspq, querierCount[*AiAgentSecurityPolicyQuery](), aaspq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (aaspq *AiAgentSecurityPolicyQuery) CountX(ctx context.Context) int {
	count, err := aaspq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (aaspq *AiAgentSecurityPolicyQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, aaspq.ctx, "Exist")
	switch _, err := aaspq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (aaspq *AiAgentSecurityPolicyQuery) ExistX(ctx context.Context) bool {
	exist, err := aaspq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AiAgentSecurityPolicyQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (aaspq *AiAgentSecurityPolicyQuery) Clone() *AiAgentSecurityPolicyQuery {
	if aaspq == nil {
		return nil
	}
	return &AiAgentSecurityPolicyQuery{
		config:     aaspq.config,
		ctx:        aaspq.ctx.Clone(),
		order:      append([]aiagentsecuritypolicy.OrderOption{}, aaspq.order...),
		inters:     append([]Interceptor{}, aaspq.inters...),
		predicates: append([]predicate.AiAgentSecurityPolicy{}, aaspq.predicates...),
		// clone intermediate query.
		sql:  aaspq.sql.Clone(),
		path: aaspq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AiAgentSecurityPolicy.Query().
//		GroupBy(aiagentsecuritypolicy.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (aaspq *AiAgentSecurityPolicyQuery) GroupBy(field string, fields ...string) *AiAgentSecurityPolicyGroupBy {
	aaspq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AiAgentSecurityPolicyGroupBy{build: aaspq}
	grbuild.flds = &aaspq.ctx.Fields
	grbuild.label = aiagentsecuritypolicy.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.AiAgentSecurityPolicy.Query().
//		Select(aiagentsecuritypolicy.FieldCreatedAt).
//		Scan(ctx, &v)
func (aaspq *AiAgentSecurityPolicyQuery) Select(fields ...string) *AiAgentSecurityPolicySelect {
	aaspq.ctx.Fields = append(aaspq.ctx.Fields, fields...)
	sbuild := &AiAgentSecurityPolicySelect{AiAgentSecurityPolicyQuery: aaspq}
	sbuild.label = aiagentsecuritypolicy.Label
	sbuild.flds, sbuild.scan = &aaspq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AiAgentSecurityPolicySelect configured with the given aggregations.
func (aaspq *AiAgentSecurityPolicyQuery) Aggregate(fns ...AggregateFunc) *AiAgentSecurityPolicySelect {
	return aaspq.Select().Aggregate(fns...)
}

func (aaspq *AiAgentSecurityPolicyQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range aaspq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, aaspq); err != nil {
				return err
			}
		}
	}
	for _, f := range aaspq.ctx.Fields {
		if !aiagentsecuritypolicy.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if aaspq.path != nil {
		prev, err := aaspq.path(ctx)
		if err != nil {
			return err
		}
		aaspq.sql = prev
	}
	return nil
}

func (aaspq *AiAgentSecurityPolicyQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AiAgentSecurityPolicy, error) {
	var (
		nodes = []*AiAgentSecurityPolicy{}
		_spec = aaspq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AiAgentSecurityPolicy).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AiAgentSecurityPolicy{config: aaspq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(aaspq.modifiers) > 0 {
		_spec.Modifiers = aaspq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, aaspq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (aaspq *AiAgentSecurityPolicyQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := aaspq.querySpec()
	if len(aaspq.modifiers) > 0 {
		_spec.Modifiers = aaspq.modifiers
	}
	_spec.Node.Columns = aaspq.ctx.Fields
	if len(aaspq.ctx.Fields) > 0 {
		_spec.Unique = aaspq.ctx.Unique != nil && *aaspq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, aaspq.driver, _spec)
}

func (aaspq *AiAgentSecurityPolicyQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(aiagentsecuritypolicy.Table, aiagentsecuritypolicy.Columns, sqlgraph.NewFieldSpec(aiagentsecuritypolicy.FieldID, field.TypeInt64))
	_spec.From = aaspq.sql
	if unique := aaspq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if aaspq.path != nil {
		_spec.Unique = true
	}
	if fields := aaspq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aiagentsecuritypolicy.FieldID)
		for i := range fields {
			if fields[i] != aiagentsecuritypolicy.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := aaspq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := aaspq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := aaspq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := aaspq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (aaspq *AiAgentSecurityPolicyQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(aaspq.driver.Dialect())
	t1 := builder.Table(aiagentsecuritypolicy.Table)
	columns := aaspq.ctx.Fields
	if len(columns) == 0 {
		columns = aiagentsecuritypolicy.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if aaspq.sql != nil {
		selector = aaspq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if aaspq.ctx.Unique != nil && *aaspq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range aaspq.modifiers {
		m(selector)
	}
	for _, p := range aaspq.predicates {
		p(selector)
	}
	for _, p := range aaspq.order {
		p(selector)
	}
	if offset := aaspq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := aaspq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (aaspq *AiAgentSecurityPolicyQuery) ForUpdate(opts ...sql.LockOption) *AiAgentSecurityPolicyQuery {
	if aaspq.driver.Dialect() == dialect.Postgres {
		aaspq.Unique(false)
	}
	aaspq.modifiers = append(aaspq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return aaspq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (aaspq *AiAgentSecurityPolicyQuery) ForShare(opts ...sql.LockOption) *AiAgentSecurityPolicyQuery {
	if aaspq.driver.Dialect() == dialect.Postgres {
		aaspq.Unique(false)
	}
	aaspq.modifiers = append(aaspq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return aaspq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (aaspq *AiAgentSecurityPolicyQuery) Modify(modifiers ...func(s *sql.Selector)) *AiAgentSecurityPolicySelect {
	aaspq.modifiers = append(aaspq.modifiers, modifiers...)
	return aaspq.Select()
}

// AiAgentSecurityPolicyGroupBy is the group-by builder for AiAgentSecurityPolicy entities.
type AiAgentSecurityPolicyGroupBy struct {
	selector
	build *AiAgentSecurityPolicyQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (aaspgb *AiAgentSecurityPolicyGroupBy) Aggregate(fns ...AggregateFunc) *AiAgentSecurityPolicyGroupBy {
	aaspgb.fns = append(aaspgb.fns, fns...)
	return aaspgb
}

// Scan applies the selector query and scans the result into the given value.
func (aaspgb *AiAgentSecurityPolicyGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aaspgb.build.ctx, "GroupBy")
	if err := aaspgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiAgentSecurityPolicyQuery, *AiAgentSecurityPolicyGroupBy](ctx, aaspgb.build, aaspgb, aaspgb.build.inters, v)
}

func (aaspgb *AiAgentSecurityPolicyGroupBy) sqlScan(ctx context.Context, root *AiAgentSecurityPolicyQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(aaspgb.fns))
	for _, fn := range aaspgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*aaspgb.flds)+len(aaspgb.fns))
		for _, f := range *aaspgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*aaspgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aaspgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AiAgentSecurityPolicySelect is the builder for selecting fields of AiAgentSecurityPolicy entities.
type AiAgentSecurityPolicySelect struct {
	*AiAgentSecurityPolicyQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (aasps *AiAgentSecurityPolicySelect) Aggregate(fns ...AggregateFunc) *AiAgentSecurityPolicySelect {
	aasps.fns = append(aasps.fns, fns...)
	return aasps
}

// Scan applies the selector query and scans the result into the given value.
func (aasps *AiAgentSecurityPolicySelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aasps.ctx, "Select")
	if err := aasps.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiAgentSecurityPolicyQuery, *AiAgentSecurityPolicySelect](ctx, aasps.AiAgentSecurityPolicyQuery, aasps, aasps.inters, v)
}

func (aasps *AiAgentSecurityPolicySelect) sqlScan(ctx context.Context, root *AiAgentSecurityPolicyQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(aasps.fns))
	for _, fn := range aasps.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*aasps.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aasps.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (aasps *AiAgentSecurityPolicySelect) Modify(modifiers ...func(s *sql.Selector)) *AiAgentSecurityPolicySelect {
	aasps.modifiers = append(aasps.modifiers, modifiers...)
	return aasps
}

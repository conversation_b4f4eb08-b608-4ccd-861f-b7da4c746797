// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/useragentorder"
)

// UserAgentOrder is the model entity for the UserAgentOrder schema.
type UserAgentOrder struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 用户id
	UserID int64 `json:"user_id,omitempty"`
	// 智能体id
	AgentID int64 `json:"agent_id,omitempty"`
	// 排序索引
	OrderIndex   int64 `json:"order_index,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*UserAgentOrder) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case useragentorder.FieldID, useragentorder.FieldUserID, useragentorder.FieldAgentID, useragentorder.FieldOrderIndex:
			values[i] = new(sql.NullInt64)
		case useragentorder.FieldCreatedAt, useragentorder.FieldUpdatedAt, useragentorder.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the UserAgentOrder fields.
func (uao *UserAgentOrder) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case useragentorder.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			uao.ID = int64(value.Int64)
		case useragentorder.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				uao.CreatedAt = value.Time
			}
		case useragentorder.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				uao.UpdatedAt = value.Time
			}
		case useragentorder.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				uao.DeletedAt = value.Time
			}
		case useragentorder.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				uao.UserID = value.Int64
			}
		case useragentorder.FieldAgentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field agent_id", values[i])
			} else if value.Valid {
				uao.AgentID = value.Int64
			}
		case useragentorder.FieldOrderIndex:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field order_index", values[i])
			} else if value.Valid {
				uao.OrderIndex = value.Int64
			}
		default:
			uao.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the UserAgentOrder.
// This includes values selected through modifiers, order, etc.
func (uao *UserAgentOrder) Value(name string) (ent.Value, error) {
	return uao.selectValues.Get(name)
}

// Update returns a builder for updating this UserAgentOrder.
// Note that you need to call UserAgentOrder.Unwrap() before calling this method if this UserAgentOrder
// was returned from a transaction, and the transaction was committed or rolled back.
func (uao *UserAgentOrder) Update() *UserAgentOrderUpdateOne {
	return NewUserAgentOrderClient(uao.config).UpdateOne(uao)
}

// Unwrap unwraps the UserAgentOrder entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (uao *UserAgentOrder) Unwrap() *UserAgentOrder {
	_tx, ok := uao.config.driver.(*txDriver)
	if !ok {
		panic("ent: UserAgentOrder is not a transactional entity")
	}
	uao.config.driver = _tx.drv
	return uao
}

// String implements the fmt.Stringer.
func (uao *UserAgentOrder) String() string {
	var builder strings.Builder
	builder.WriteString("UserAgentOrder(")
	builder.WriteString(fmt.Sprintf("id=%v, ", uao.ID))
	builder.WriteString("created_at=")
	builder.WriteString(uao.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(uao.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(uao.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", uao.UserID))
	builder.WriteString(", ")
	builder.WriteString("agent_id=")
	builder.WriteString(fmt.Sprintf("%v", uao.AgentID))
	builder.WriteString(", ")
	builder.WriteString("order_index=")
	builder.WriteString(fmt.Sprintf("%v", uao.OrderIndex))
	builder.WriteByte(')')
	return builder.String()
}

// UserAgentOrders is a parsable slice of UserAgentOrder.
type UserAgentOrders []*UserAgentOrder

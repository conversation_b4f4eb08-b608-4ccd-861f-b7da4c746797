// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiModelDelete is the builder for deleting a AiModel entity.
type AiModelDelete struct {
	config
	hooks    []Hook
	mutation *AiModelMutation
}

// Where appends a list predicates to the AiModelDelete builder.
func (amd *AiModelDelete) Where(ps ...predicate.AiModel) *AiModelDelete {
	amd.mutation.Where(ps...)
	return amd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (amd *AiModelDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, amd.sqlExec, amd.mutation, amd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (amd *AiModelDelete) ExecX(ctx context.Context) int {
	n, err := amd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (amd *AiModelDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(aimodel.Table, sqlgraph.NewFieldSpec(aimodel.FieldID, field.TypeInt64))
	if ps := amd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, amd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	amd.mutation.done = true
	return affected, err
}

// AiModelDeleteOne is the builder for deleting a single AiModel entity.
type AiModelDeleteOne struct {
	amd *AiModelDelete
}

// Where appends a list predicates to the AiModelDelete builder.
func (amdo *AiModelDeleteOne) Where(ps ...predicate.AiModel) *AiModelDeleteOne {
	amdo.amd.mutation.Where(ps...)
	return amdo
}

// Exec executes the deletion query.
func (amdo *AiModelDeleteOne) Exec(ctx context.Context) error {
	n, err := amdo.amd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{aimodel.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (amdo *AiModelDeleteOne) ExecX(ctx context.Context) {
	if err := amdo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodeldetail"
)

// AiModelDetail is the model entity for the AiModelDetail schema.
type AiModelDetail struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 模型名称
	ModelName string `json:"model_name,omitempty"`
	// 模型
	Name string `json:"name,omitempty"`
	// url
	URL string `json:"url,omitempty"`
	// 头像
	Avatar string `json:"avatar,omitempty"`
	// 是否可以互联网搜索
	CanInternetSearch bool `json:"can_internet_search,omitempty"`
	// 余额查询URL
	BalanceSearchURL string `json:"balance_search_url,omitempty"`
	// 0:禁用思考 1:开启思考 2:可以动态开启关闭思考
	ThinkingEnableStatus int64 `json:"thinking_enable_status,omitempty"`
	// 后台URL
	BackgroundURL string `json:"background_url,omitempty"`
	selectValues  sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AiModelDetail) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case aimodeldetail.FieldCanInternetSearch:
			values[i] = new(sql.NullBool)
		case aimodeldetail.FieldID, aimodeldetail.FieldThinkingEnableStatus:
			values[i] = new(sql.NullInt64)
		case aimodeldetail.FieldModelName, aimodeldetail.FieldName, aimodeldetail.FieldURL, aimodeldetail.FieldAvatar, aimodeldetail.FieldBalanceSearchURL, aimodeldetail.FieldBackgroundURL:
			values[i] = new(sql.NullString)
		case aimodeldetail.FieldCreatedAt, aimodeldetail.FieldUpdatedAt, aimodeldetail.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AiModelDetail fields.
func (amd *AiModelDetail) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case aimodeldetail.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			amd.ID = int64(value.Int64)
		case aimodeldetail.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				amd.CreatedAt = value.Time
			}
		case aimodeldetail.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				amd.UpdatedAt = value.Time
			}
		case aimodeldetail.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				amd.DeletedAt = value.Time
			}
		case aimodeldetail.FieldModelName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field model_name", values[i])
			} else if value.Valid {
				amd.ModelName = value.String
			}
		case aimodeldetail.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				amd.Name = value.String
			}
		case aimodeldetail.FieldURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field url", values[i])
			} else if value.Valid {
				amd.URL = value.String
			}
		case aimodeldetail.FieldAvatar:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field avatar", values[i])
			} else if value.Valid {
				amd.Avatar = value.String
			}
		case aimodeldetail.FieldCanInternetSearch:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field can_internet_search", values[i])
			} else if value.Valid {
				amd.CanInternetSearch = value.Bool
			}
		case aimodeldetail.FieldBalanceSearchURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field balance_search_url", values[i])
			} else if value.Valid {
				amd.BalanceSearchURL = value.String
			}
		case aimodeldetail.FieldThinkingEnableStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field thinking_enable_status", values[i])
			} else if value.Valid {
				amd.ThinkingEnableStatus = value.Int64
			}
		case aimodeldetail.FieldBackgroundURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field background_url", values[i])
			} else if value.Valid {
				amd.BackgroundURL = value.String
			}
		default:
			amd.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AiModelDetail.
// This includes values selected through modifiers, order, etc.
func (amd *AiModelDetail) Value(name string) (ent.Value, error) {
	return amd.selectValues.Get(name)
}

// Update returns a builder for updating this AiModelDetail.
// Note that you need to call AiModelDetail.Unwrap() before calling this method if this AiModelDetail
// was returned from a transaction, and the transaction was committed or rolled back.
func (amd *AiModelDetail) Update() *AiModelDetailUpdateOne {
	return NewAiModelDetailClient(amd.config).UpdateOne(amd)
}

// Unwrap unwraps the AiModelDetail entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (amd *AiModelDetail) Unwrap() *AiModelDetail {
	_tx, ok := amd.config.driver.(*txDriver)
	if !ok {
		panic("ent: AiModelDetail is not a transactional entity")
	}
	amd.config.driver = _tx.drv
	return amd
}

// String implements the fmt.Stringer.
func (amd *AiModelDetail) String() string {
	var builder strings.Builder
	builder.WriteString("AiModelDetail(")
	builder.WriteString(fmt.Sprintf("id=%v, ", amd.ID))
	builder.WriteString("created_at=")
	builder.WriteString(amd.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(amd.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(amd.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("model_name=")
	builder.WriteString(amd.ModelName)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(amd.Name)
	builder.WriteString(", ")
	builder.WriteString("url=")
	builder.WriteString(amd.URL)
	builder.WriteString(", ")
	builder.WriteString("avatar=")
	builder.WriteString(amd.Avatar)
	builder.WriteString(", ")
	builder.WriteString("can_internet_search=")
	builder.WriteString(fmt.Sprintf("%v", amd.CanInternetSearch))
	builder.WriteString(", ")
	builder.WriteString("balance_search_url=")
	builder.WriteString(amd.BalanceSearchURL)
	builder.WriteString(", ")
	builder.WriteString("thinking_enable_status=")
	builder.WriteString(fmt.Sprintf("%v", amd.ThinkingEnableStatus))
	builder.WriteString(", ")
	builder.WriteString("background_url=")
	builder.WriteString(amd.BackgroundURL)
	builder.WriteByte(')')
	return builder.String()
}

// AiModelDetails is a parsable slice of AiModelDetail.
type AiModelDetails []*AiModelDetail

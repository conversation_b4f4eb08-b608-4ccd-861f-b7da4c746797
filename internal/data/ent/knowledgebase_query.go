// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// KnowledgeBaseQuery is the builder for querying KnowledgeBase entities.
type KnowledgeBaseQuery struct {
	config
	ctx        *QueryContext
	order      []knowledgebase.OrderOption
	inters     []Interceptor
	predicates []predicate.KnowledgeBase
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the KnowledgeBaseQuery builder.
func (kbq *KnowledgeBaseQuery) Where(ps ...predicate.KnowledgeBase) *KnowledgeBaseQuery {
	kbq.predicates = append(kbq.predicates, ps...)
	return kbq
}

// Limit the number of records to be returned by this query.
func (kbq *KnowledgeBaseQuery) Limit(limit int) *KnowledgeBaseQuery {
	kbq.ctx.Limit = &limit
	return kbq
}

// Offset to start from.
func (kbq *KnowledgeBaseQuery) Offset(offset int) *KnowledgeBaseQuery {
	kbq.ctx.Offset = &offset
	return kbq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (kbq *KnowledgeBaseQuery) Unique(unique bool) *KnowledgeBaseQuery {
	kbq.ctx.Unique = &unique
	return kbq
}

// Order specifies how the records should be ordered.
func (kbq *KnowledgeBaseQuery) Order(o ...knowledgebase.OrderOption) *KnowledgeBaseQuery {
	kbq.order = append(kbq.order, o...)
	return kbq
}

// First returns the first KnowledgeBase entity from the query.
// Returns a *NotFoundError when no KnowledgeBase was found.
func (kbq *KnowledgeBaseQuery) First(ctx context.Context) (*KnowledgeBase, error) {
	nodes, err := kbq.Limit(1).All(setContextOp(ctx, kbq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{knowledgebase.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (kbq *KnowledgeBaseQuery) FirstX(ctx context.Context) *KnowledgeBase {
	node, err := kbq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first KnowledgeBase ID from the query.
// Returns a *NotFoundError when no KnowledgeBase ID was found.
func (kbq *KnowledgeBaseQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = kbq.Limit(1).IDs(setContextOp(ctx, kbq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{knowledgebase.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (kbq *KnowledgeBaseQuery) FirstIDX(ctx context.Context) int64 {
	id, err := kbq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single KnowledgeBase entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one KnowledgeBase entity is found.
// Returns a *NotFoundError when no KnowledgeBase entities are found.
func (kbq *KnowledgeBaseQuery) Only(ctx context.Context) (*KnowledgeBase, error) {
	nodes, err := kbq.Limit(2).All(setContextOp(ctx, kbq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{knowledgebase.Label}
	default:
		return nil, &NotSingularError{knowledgebase.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (kbq *KnowledgeBaseQuery) OnlyX(ctx context.Context) *KnowledgeBase {
	node, err := kbq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only KnowledgeBase ID in the query.
// Returns a *NotSingularError when more than one KnowledgeBase ID is found.
// Returns a *NotFoundError when no entities are found.
func (kbq *KnowledgeBaseQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = kbq.Limit(2).IDs(setContextOp(ctx, kbq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{knowledgebase.Label}
	default:
		err = &NotSingularError{knowledgebase.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (kbq *KnowledgeBaseQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := kbq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of KnowledgeBases.
func (kbq *KnowledgeBaseQuery) All(ctx context.Context) ([]*KnowledgeBase, error) {
	ctx = setContextOp(ctx, kbq.ctx, "All")
	if err := kbq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*KnowledgeBase, *KnowledgeBaseQuery]()
	return withInterceptors[[]*KnowledgeBase](ctx, kbq, qr, kbq.inters)
}

// AllX is like All, but panics if an error occurs.
func (kbq *KnowledgeBaseQuery) AllX(ctx context.Context) []*KnowledgeBase {
	nodes, err := kbq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of KnowledgeBase IDs.
func (kbq *KnowledgeBaseQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if kbq.ctx.Unique == nil && kbq.path != nil {
		kbq.Unique(true)
	}
	ctx = setContextOp(ctx, kbq.ctx, "IDs")
	if err = kbq.Select(knowledgebase.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (kbq *KnowledgeBaseQuery) IDsX(ctx context.Context) []int64 {
	ids, err := kbq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (kbq *KnowledgeBaseQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, kbq.ctx, "Count")
	if err := kbq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, kbq, querierCount[*KnowledgeBaseQuery](), kbq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (kbq *KnowledgeBaseQuery) CountX(ctx context.Context) int {
	count, err := kbq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (kbq *KnowledgeBaseQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, kbq.ctx, "Exist")
	switch _, err := kbq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (kbq *KnowledgeBaseQuery) ExistX(ctx context.Context) bool {
	exist, err := kbq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the KnowledgeBaseQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (kbq *KnowledgeBaseQuery) Clone() *KnowledgeBaseQuery {
	if kbq == nil {
		return nil
	}
	return &KnowledgeBaseQuery{
		config:     kbq.config,
		ctx:        kbq.ctx.Clone(),
		order:      append([]knowledgebase.OrderOption{}, kbq.order...),
		inters:     append([]Interceptor{}, kbq.inters...),
		predicates: append([]predicate.KnowledgeBase{}, kbq.predicates...),
		// clone intermediate query.
		sql:  kbq.sql.Clone(),
		path: kbq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.KnowledgeBase.Query().
//		GroupBy(knowledgebase.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (kbq *KnowledgeBaseQuery) GroupBy(field string, fields ...string) *KnowledgeBaseGroupBy {
	kbq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &KnowledgeBaseGroupBy{build: kbq}
	grbuild.flds = &kbq.ctx.Fields
	grbuild.label = knowledgebase.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//	}
//
//	client.KnowledgeBase.Query().
//		Select(knowledgebase.FieldTenantID).
//		Scan(ctx, &v)
func (kbq *KnowledgeBaseQuery) Select(fields ...string) *KnowledgeBaseSelect {
	kbq.ctx.Fields = append(kbq.ctx.Fields, fields...)
	sbuild := &KnowledgeBaseSelect{KnowledgeBaseQuery: kbq}
	sbuild.label = knowledgebase.Label
	sbuild.flds, sbuild.scan = &kbq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a KnowledgeBaseSelect configured with the given aggregations.
func (kbq *KnowledgeBaseQuery) Aggregate(fns ...AggregateFunc) *KnowledgeBaseSelect {
	return kbq.Select().Aggregate(fns...)
}

func (kbq *KnowledgeBaseQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range kbq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, kbq); err != nil {
				return err
			}
		}
	}
	for _, f := range kbq.ctx.Fields {
		if !knowledgebase.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if kbq.path != nil {
		prev, err := kbq.path(ctx)
		if err != nil {
			return err
		}
		kbq.sql = prev
	}
	return nil
}

func (kbq *KnowledgeBaseQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*KnowledgeBase, error) {
	var (
		nodes = []*KnowledgeBase{}
		_spec = kbq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*KnowledgeBase).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &KnowledgeBase{config: kbq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(kbq.modifiers) > 0 {
		_spec.Modifiers = kbq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, kbq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (kbq *KnowledgeBaseQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := kbq.querySpec()
	if len(kbq.modifiers) > 0 {
		_spec.Modifiers = kbq.modifiers
	}
	_spec.Node.Columns = kbq.ctx.Fields
	if len(kbq.ctx.Fields) > 0 {
		_spec.Unique = kbq.ctx.Unique != nil && *kbq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, kbq.driver, _spec)
}

func (kbq *KnowledgeBaseQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(knowledgebase.Table, knowledgebase.Columns, sqlgraph.NewFieldSpec(knowledgebase.FieldID, field.TypeInt64))
	_spec.From = kbq.sql
	if unique := kbq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if kbq.path != nil {
		_spec.Unique = true
	}
	if fields := kbq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, knowledgebase.FieldID)
		for i := range fields {
			if fields[i] != knowledgebase.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := kbq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := kbq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := kbq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := kbq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (kbq *KnowledgeBaseQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(kbq.driver.Dialect())
	t1 := builder.Table(knowledgebase.Table)
	columns := kbq.ctx.Fields
	if len(columns) == 0 {
		columns = knowledgebase.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if kbq.sql != nil {
		selector = kbq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if kbq.ctx.Unique != nil && *kbq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range kbq.modifiers {
		m(selector)
	}
	for _, p := range kbq.predicates {
		p(selector)
	}
	for _, p := range kbq.order {
		p(selector)
	}
	if offset := kbq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := kbq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (kbq *KnowledgeBaseQuery) ForUpdate(opts ...sql.LockOption) *KnowledgeBaseQuery {
	if kbq.driver.Dialect() == dialect.Postgres {
		kbq.Unique(false)
	}
	kbq.modifiers = append(kbq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return kbq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (kbq *KnowledgeBaseQuery) ForShare(opts ...sql.LockOption) *KnowledgeBaseQuery {
	if kbq.driver.Dialect() == dialect.Postgres {
		kbq.Unique(false)
	}
	kbq.modifiers = append(kbq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return kbq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (kbq *KnowledgeBaseQuery) Modify(modifiers ...func(s *sql.Selector)) *KnowledgeBaseSelect {
	kbq.modifiers = append(kbq.modifiers, modifiers...)
	return kbq.Select()
}

// KnowledgeBaseGroupBy is the group-by builder for KnowledgeBase entities.
type KnowledgeBaseGroupBy struct {
	selector
	build *KnowledgeBaseQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (kbgb *KnowledgeBaseGroupBy) Aggregate(fns ...AggregateFunc) *KnowledgeBaseGroupBy {
	kbgb.fns = append(kbgb.fns, fns...)
	return kbgb
}

// Scan applies the selector query and scans the result into the given value.
func (kbgb *KnowledgeBaseGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, kbgb.build.ctx, "GroupBy")
	if err := kbgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*KnowledgeBaseQuery, *KnowledgeBaseGroupBy](ctx, kbgb.build, kbgb, kbgb.build.inters, v)
}

func (kbgb *KnowledgeBaseGroupBy) sqlScan(ctx context.Context, root *KnowledgeBaseQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(kbgb.fns))
	for _, fn := range kbgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*kbgb.flds)+len(kbgb.fns))
		for _, f := range *kbgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*kbgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := kbgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// KnowledgeBaseSelect is the builder for selecting fields of KnowledgeBase entities.
type KnowledgeBaseSelect struct {
	*KnowledgeBaseQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (kbs *KnowledgeBaseSelect) Aggregate(fns ...AggregateFunc) *KnowledgeBaseSelect {
	kbs.fns = append(kbs.fns, fns...)
	return kbs
}

// Scan applies the selector query and scans the result into the given value.
func (kbs *KnowledgeBaseSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, kbs.ctx, "Select")
	if err := kbs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*KnowledgeBaseQuery, *KnowledgeBaseSelect](ctx, kbs.KnowledgeBaseQuery, kbs, kbs.inters, v)
}

func (kbs *KnowledgeBaseSelect) sqlScan(ctx context.Context, root *KnowledgeBaseQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(kbs.fns))
	for _, fn := range kbs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*kbs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := kbs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (kbs *KnowledgeBaseSelect) Modify(modifiers ...func(s *sql.Selector)) *KnowledgeBaseSelect {
	kbs.modifiers = append(kbs.modifiers, modifiers...)
	return kbs
}

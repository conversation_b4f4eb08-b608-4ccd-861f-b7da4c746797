// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritypolicy"
)

// AiAgentSecurityPolicyCreate is the builder for creating a AiAgentSecurityPolicy entity.
type AiAgentSecurityPolicyCreate struct {
	config
	mutation *AiAgentSecurityPolicyMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetCreatedAt(t time.Time) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetCreatedAt(t)
	return aaspc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (aaspc *AiAgentSecurityPolicyCreate) SetNillableCreatedAt(t *time.Time) *AiAgentSecurityPolicyCreate {
	if t != nil {
		aaspc.SetCreatedAt(*t)
	}
	return aaspc
}

// SetUpdatedAt sets the "updated_at" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetUpdatedAt(t time.Time) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetUpdatedAt(t)
	return aaspc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (aaspc *AiAgentSecurityPolicyCreate) SetNillableUpdatedAt(t *time.Time) *AiAgentSecurityPolicyCreate {
	if t != nil {
		aaspc.SetUpdatedAt(*t)
	}
	return aaspc
}

// SetDeletedAt sets the "deleted_at" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetDeletedAt(t time.Time) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetDeletedAt(t)
	return aaspc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aaspc *AiAgentSecurityPolicyCreate) SetNillableDeletedAt(t *time.Time) *AiAgentSecurityPolicyCreate {
	if t != nil {
		aaspc.SetDeletedAt(*t)
	}
	return aaspc
}

// SetName sets the "name" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetName(s string) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetName(s)
	return aaspc
}

// SetAgentID sets the "agent_id" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetAgentID(i int64) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetAgentID(i)
	return aaspc
}

// SetPolicyCategory sets the "policy_category" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetPolicyCategory(i int64) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetPolicyCategory(i)
	return aaspc
}

// SetRiskLevel sets the "risk_level" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetRiskLevel(i int64) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetRiskLevel(i)
	return aaspc
}

// SetEnabled sets the "enabled" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetEnabled(b bool) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetEnabled(b)
	return aaspc
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (aaspc *AiAgentSecurityPolicyCreate) SetNillableEnabled(b *bool) *AiAgentSecurityPolicyCreate {
	if b != nil {
		aaspc.SetEnabled(*b)
	}
	return aaspc
}

// SetPolicies sets the "policies" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetPolicies(pa *pq.StringArray) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetPolicies(pa)
	return aaspc
}

// SetHitAction sets the "hit_action" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetHitAction(i int64) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetHitAction(i)
	return aaspc
}

// SetNillableHitAction sets the "hit_action" field if the given value is not nil.
func (aaspc *AiAgentSecurityPolicyCreate) SetNillableHitAction(i *int64) *AiAgentSecurityPolicyCreate {
	if i != nil {
		aaspc.SetHitAction(*i)
	}
	return aaspc
}

// SetHitResponse sets the "hit_response" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetHitResponse(s string) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetHitResponse(s)
	return aaspc
}

// SetNillableHitResponse sets the "hit_response" field if the given value is not nil.
func (aaspc *AiAgentSecurityPolicyCreate) SetNillableHitResponse(s *string) *AiAgentSecurityPolicyCreate {
	if s != nil {
		aaspc.SetHitResponse(*s)
	}
	return aaspc
}

// SetUpdatedBy sets the "updated_by" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetUpdatedBy(i int64) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetUpdatedBy(i)
	return aaspc
}

// SetID sets the "id" field.
func (aaspc *AiAgentSecurityPolicyCreate) SetID(i int64) *AiAgentSecurityPolicyCreate {
	aaspc.mutation.SetID(i)
	return aaspc
}

// Mutation returns the AiAgentSecurityPolicyMutation object of the builder.
func (aaspc *AiAgentSecurityPolicyCreate) Mutation() *AiAgentSecurityPolicyMutation {
	return aaspc.mutation
}

// Save creates the AiAgentSecurityPolicy in the database.
func (aaspc *AiAgentSecurityPolicyCreate) Save(ctx context.Context) (*AiAgentSecurityPolicy, error) {
	if err := aaspc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, aaspc.sqlSave, aaspc.mutation, aaspc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (aaspc *AiAgentSecurityPolicyCreate) SaveX(ctx context.Context) *AiAgentSecurityPolicy {
	v, err := aaspc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aaspc *AiAgentSecurityPolicyCreate) Exec(ctx context.Context) error {
	_, err := aaspc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aaspc *AiAgentSecurityPolicyCreate) ExecX(ctx context.Context) {
	if err := aaspc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aaspc *AiAgentSecurityPolicyCreate) defaults() error {
	if _, ok := aaspc.mutation.CreatedAt(); !ok {
		if aiagentsecuritypolicy.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagentsecuritypolicy.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := aiagentsecuritypolicy.DefaultCreatedAt()
		aaspc.mutation.SetCreatedAt(v)
	}
	if _, ok := aaspc.mutation.UpdatedAt(); !ok {
		if aiagentsecuritypolicy.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagentsecuritypolicy.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aiagentsecuritypolicy.DefaultUpdatedAt()
		aaspc.mutation.SetUpdatedAt(v)
	}
	if _, ok := aaspc.mutation.DeletedAt(); !ok {
		v := aiagentsecuritypolicy.DefaultDeletedAt
		aaspc.mutation.SetDeletedAt(v)
	}
	if _, ok := aaspc.mutation.Enabled(); !ok {
		v := aiagentsecuritypolicy.DefaultEnabled
		aaspc.mutation.SetEnabled(v)
	}
	if _, ok := aaspc.mutation.Policies(); !ok {
		v := aiagentsecuritypolicy.DefaultPolicies
		aaspc.mutation.SetPolicies(v)
	}
	if _, ok := aaspc.mutation.HitAction(); !ok {
		v := aiagentsecuritypolicy.DefaultHitAction
		aaspc.mutation.SetHitAction(v)
	}
	if _, ok := aaspc.mutation.HitResponse(); !ok {
		v := aiagentsecuritypolicy.DefaultHitResponse
		aaspc.mutation.SetHitResponse(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (aaspc *AiAgentSecurityPolicyCreate) check() error {
	if _, ok := aaspc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.created_at"`)}
	}
	if _, ok := aaspc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.updated_at"`)}
	}
	if _, ok := aaspc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.name"`)}
	}
	if _, ok := aaspc.mutation.AgentID(); !ok {
		return &ValidationError{Name: "agent_id", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.agent_id"`)}
	}
	if _, ok := aaspc.mutation.PolicyCategory(); !ok {
		return &ValidationError{Name: "policy_category", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.policy_category"`)}
	}
	if _, ok := aaspc.mutation.RiskLevel(); !ok {
		return &ValidationError{Name: "risk_level", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.risk_level"`)}
	}
	if _, ok := aaspc.mutation.Enabled(); !ok {
		return &ValidationError{Name: "enabled", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.enabled"`)}
	}
	if _, ok := aaspc.mutation.Policies(); !ok {
		return &ValidationError{Name: "policies", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.policies"`)}
	}
	if _, ok := aaspc.mutation.HitAction(); !ok {
		return &ValidationError{Name: "hit_action", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.hit_action"`)}
	}
	if _, ok := aaspc.mutation.HitResponse(); !ok {
		return &ValidationError{Name: "hit_response", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.hit_response"`)}
	}
	if _, ok := aaspc.mutation.UpdatedBy(); !ok {
		return &ValidationError{Name: "updated_by", err: errors.New(`ent: missing required field "AiAgentSecurityPolicy.updated_by"`)}
	}
	return nil
}

func (aaspc *AiAgentSecurityPolicyCreate) sqlSave(ctx context.Context) (*AiAgentSecurityPolicy, error) {
	if err := aaspc.check(); err != nil {
		return nil, err
	}
	_node, _spec := aaspc.createSpec()
	if err := sqlgraph.CreateNode(ctx, aaspc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	aaspc.mutation.id = &_node.ID
	aaspc.mutation.done = true
	return _node, nil
}

func (aaspc *AiAgentSecurityPolicyCreate) createSpec() (*AiAgentSecurityPolicy, *sqlgraph.CreateSpec) {
	var (
		_node = &AiAgentSecurityPolicy{config: aaspc.config}
		_spec = sqlgraph.NewCreateSpec(aiagentsecuritypolicy.Table, sqlgraph.NewFieldSpec(aiagentsecuritypolicy.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = aaspc.conflict
	if id, ok := aaspc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := aaspc.mutation.CreatedAt(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := aaspc.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := aaspc.mutation.DeletedAt(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := aaspc.mutation.Name(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := aaspc.mutation.AgentID(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldAgentID, field.TypeInt64, value)
		_node.AgentID = value
	}
	if value, ok := aaspc.mutation.PolicyCategory(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldPolicyCategory, field.TypeInt64, value)
		_node.PolicyCategory = value
	}
	if value, ok := aaspc.mutation.RiskLevel(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldRiskLevel, field.TypeInt64, value)
		_node.RiskLevel = value
	}
	if value, ok := aaspc.mutation.Enabled(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldEnabled, field.TypeBool, value)
		_node.Enabled = value
	}
	if value, ok := aaspc.mutation.Policies(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldPolicies, field.TypeOther, value)
		_node.Policies = value
	}
	if value, ok := aaspc.mutation.HitAction(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldHitAction, field.TypeInt64, value)
		_node.HitAction = value
	}
	if value, ok := aaspc.mutation.HitResponse(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldHitResponse, field.TypeString, value)
		_node.HitResponse = value
	}
	if value, ok := aaspc.mutation.UpdatedBy(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldUpdatedBy, field.TypeInt64, value)
		_node.UpdatedBy = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiAgentSecurityPolicy.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiAgentSecurityPolicyUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (aaspc *AiAgentSecurityPolicyCreate) OnConflict(opts ...sql.ConflictOption) *AiAgentSecurityPolicyUpsertOne {
	aaspc.conflict = opts
	return &AiAgentSecurityPolicyUpsertOne{
		create: aaspc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiAgentSecurityPolicy.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aaspc *AiAgentSecurityPolicyCreate) OnConflictColumns(columns ...string) *AiAgentSecurityPolicyUpsertOne {
	aaspc.conflict = append(aaspc.conflict, sql.ConflictColumns(columns...))
	return &AiAgentSecurityPolicyUpsertOne{
		create: aaspc,
	}
}

type (
	// AiAgentSecurityPolicyUpsertOne is the builder for "upsert"-ing
	//  one AiAgentSecurityPolicy node.
	AiAgentSecurityPolicyUpsertOne struct {
		create *AiAgentSecurityPolicyCreate
	}

	// AiAgentSecurityPolicyUpsert is the "OnConflict" setter.
	AiAgentSecurityPolicyUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *AiAgentSecurityPolicyUpsert) SetUpdatedAt(v time.Time) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdateUpdatedAt() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiAgentSecurityPolicyUpsert) SetDeletedAt(v time.Time) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdateDeletedAt() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiAgentSecurityPolicyUpsert) ClearDeletedAt() *AiAgentSecurityPolicyUpsert {
	u.SetNull(aiagentsecuritypolicy.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *AiAgentSecurityPolicyUpsert) SetName(v string) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdateName() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldName)
	return u
}

// SetAgentID sets the "agent_id" field.
func (u *AiAgentSecurityPolicyUpsert) SetAgentID(v int64) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldAgentID, v)
	return u
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdateAgentID() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldAgentID)
	return u
}

// AddAgentID adds v to the "agent_id" field.
func (u *AiAgentSecurityPolicyUpsert) AddAgentID(v int64) *AiAgentSecurityPolicyUpsert {
	u.Add(aiagentsecuritypolicy.FieldAgentID, v)
	return u
}

// SetPolicyCategory sets the "policy_category" field.
func (u *AiAgentSecurityPolicyUpsert) SetPolicyCategory(v int64) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldPolicyCategory, v)
	return u
}

// UpdatePolicyCategory sets the "policy_category" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdatePolicyCategory() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldPolicyCategory)
	return u
}

// AddPolicyCategory adds v to the "policy_category" field.
func (u *AiAgentSecurityPolicyUpsert) AddPolicyCategory(v int64) *AiAgentSecurityPolicyUpsert {
	u.Add(aiagentsecuritypolicy.FieldPolicyCategory, v)
	return u
}

// SetRiskLevel sets the "risk_level" field.
func (u *AiAgentSecurityPolicyUpsert) SetRiskLevel(v int64) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldRiskLevel, v)
	return u
}

// UpdateRiskLevel sets the "risk_level" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdateRiskLevel() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldRiskLevel)
	return u
}

// AddRiskLevel adds v to the "risk_level" field.
func (u *AiAgentSecurityPolicyUpsert) AddRiskLevel(v int64) *AiAgentSecurityPolicyUpsert {
	u.Add(aiagentsecuritypolicy.FieldRiskLevel, v)
	return u
}

// SetEnabled sets the "enabled" field.
func (u *AiAgentSecurityPolicyUpsert) SetEnabled(v bool) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldEnabled, v)
	return u
}

// UpdateEnabled sets the "enabled" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdateEnabled() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldEnabled)
	return u
}

// SetPolicies sets the "policies" field.
func (u *AiAgentSecurityPolicyUpsert) SetPolicies(v *pq.StringArray) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldPolicies, v)
	return u
}

// UpdatePolicies sets the "policies" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdatePolicies() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldPolicies)
	return u
}

// SetHitAction sets the "hit_action" field.
func (u *AiAgentSecurityPolicyUpsert) SetHitAction(v int64) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldHitAction, v)
	return u
}

// UpdateHitAction sets the "hit_action" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdateHitAction() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldHitAction)
	return u
}

// AddHitAction adds v to the "hit_action" field.
func (u *AiAgentSecurityPolicyUpsert) AddHitAction(v int64) *AiAgentSecurityPolicyUpsert {
	u.Add(aiagentsecuritypolicy.FieldHitAction, v)
	return u
}

// SetHitResponse sets the "hit_response" field.
func (u *AiAgentSecurityPolicyUpsert) SetHitResponse(v string) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldHitResponse, v)
	return u
}

// UpdateHitResponse sets the "hit_response" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdateHitResponse() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldHitResponse)
	return u
}

// SetUpdatedBy sets the "updated_by" field.
func (u *AiAgentSecurityPolicyUpsert) SetUpdatedBy(v int64) *AiAgentSecurityPolicyUpsert {
	u.Set(aiagentsecuritypolicy.FieldUpdatedBy, v)
	return u
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsert) UpdateUpdatedBy() *AiAgentSecurityPolicyUpsert {
	u.SetExcluded(aiagentsecuritypolicy.FieldUpdatedBy)
	return u
}

// AddUpdatedBy adds v to the "updated_by" field.
func (u *AiAgentSecurityPolicyUpsert) AddUpdatedBy(v int64) *AiAgentSecurityPolicyUpsert {
	u.Add(aiagentsecuritypolicy.FieldUpdatedBy, v)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AiAgentSecurityPolicy.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aiagentsecuritypolicy.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiAgentSecurityPolicyUpsertOne) UpdateNewValues() *AiAgentSecurityPolicyUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(aiagentsecuritypolicy.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(aiagentsecuritypolicy.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiAgentSecurityPolicy.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AiAgentSecurityPolicyUpsertOne) Ignore() *AiAgentSecurityPolicyUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiAgentSecurityPolicyUpsertOne) DoNothing() *AiAgentSecurityPolicyUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiAgentSecurityPolicyCreate.OnConflict
// documentation for more info.
func (u *AiAgentSecurityPolicyUpsertOne) Update(set func(*AiAgentSecurityPolicyUpsert)) *AiAgentSecurityPolicyUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiAgentSecurityPolicyUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetUpdatedAt(v time.Time) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdateUpdatedAt() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetDeletedAt(v time.Time) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdateDeletedAt() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiAgentSecurityPolicyUpsertOne) ClearDeletedAt() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetName(v string) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdateName() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateName()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetAgentID(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetAgentID(v)
	})
}

// AddAgentID adds v to the "agent_id" field.
func (u *AiAgentSecurityPolicyUpsertOne) AddAgentID(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdateAgentID() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateAgentID()
	})
}

// SetPolicyCategory sets the "policy_category" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetPolicyCategory(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetPolicyCategory(v)
	})
}

// AddPolicyCategory adds v to the "policy_category" field.
func (u *AiAgentSecurityPolicyUpsertOne) AddPolicyCategory(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddPolicyCategory(v)
	})
}

// UpdatePolicyCategory sets the "policy_category" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdatePolicyCategory() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdatePolicyCategory()
	})
}

// SetRiskLevel sets the "risk_level" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetRiskLevel(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetRiskLevel(v)
	})
}

// AddRiskLevel adds v to the "risk_level" field.
func (u *AiAgentSecurityPolicyUpsertOne) AddRiskLevel(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddRiskLevel(v)
	})
}

// UpdateRiskLevel sets the "risk_level" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdateRiskLevel() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateRiskLevel()
	})
}

// SetEnabled sets the "enabled" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetEnabled(v bool) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetEnabled(v)
	})
}

// UpdateEnabled sets the "enabled" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdateEnabled() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateEnabled()
	})
}

// SetPolicies sets the "policies" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetPolicies(v *pq.StringArray) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetPolicies(v)
	})
}

// UpdatePolicies sets the "policies" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdatePolicies() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdatePolicies()
	})
}

// SetHitAction sets the "hit_action" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetHitAction(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetHitAction(v)
	})
}

// AddHitAction adds v to the "hit_action" field.
func (u *AiAgentSecurityPolicyUpsertOne) AddHitAction(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddHitAction(v)
	})
}

// UpdateHitAction sets the "hit_action" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdateHitAction() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateHitAction()
	})
}

// SetHitResponse sets the "hit_response" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetHitResponse(v string) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetHitResponse(v)
	})
}

// UpdateHitResponse sets the "hit_response" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdateHitResponse() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateHitResponse()
	})
}

// SetUpdatedBy sets the "updated_by" field.
func (u *AiAgentSecurityPolicyUpsertOne) SetUpdatedBy(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetUpdatedBy(v)
	})
}

// AddUpdatedBy adds v to the "updated_by" field.
func (u *AiAgentSecurityPolicyUpsertOne) AddUpdatedBy(v int64) *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddUpdatedBy(v)
	})
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertOne) UpdateUpdatedBy() *AiAgentSecurityPolicyUpsertOne {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateUpdatedBy()
	})
}

// Exec executes the query.
func (u *AiAgentSecurityPolicyUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiAgentSecurityPolicyCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiAgentSecurityPolicyUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AiAgentSecurityPolicyUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AiAgentSecurityPolicyUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AiAgentSecurityPolicyCreateBulk is the builder for creating many AiAgentSecurityPolicy entities in bulk.
type AiAgentSecurityPolicyCreateBulk struct {
	config
	err      error
	builders []*AiAgentSecurityPolicyCreate
	conflict []sql.ConflictOption
}

// Save creates the AiAgentSecurityPolicy entities in the database.
func (aaspcb *AiAgentSecurityPolicyCreateBulk) Save(ctx context.Context) ([]*AiAgentSecurityPolicy, error) {
	if aaspcb.err != nil {
		return nil, aaspcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(aaspcb.builders))
	nodes := make([]*AiAgentSecurityPolicy, len(aaspcb.builders))
	mutators := make([]Mutator, len(aaspcb.builders))
	for i := range aaspcb.builders {
		func(i int, root context.Context) {
			builder := aaspcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AiAgentSecurityPolicyMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, aaspcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = aaspcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, aaspcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, aaspcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (aaspcb *AiAgentSecurityPolicyCreateBulk) SaveX(ctx context.Context) []*AiAgentSecurityPolicy {
	v, err := aaspcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aaspcb *AiAgentSecurityPolicyCreateBulk) Exec(ctx context.Context) error {
	_, err := aaspcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aaspcb *AiAgentSecurityPolicyCreateBulk) ExecX(ctx context.Context) {
	if err := aaspcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiAgentSecurityPolicy.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiAgentSecurityPolicyUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (aaspcb *AiAgentSecurityPolicyCreateBulk) OnConflict(opts ...sql.ConflictOption) *AiAgentSecurityPolicyUpsertBulk {
	aaspcb.conflict = opts
	return &AiAgentSecurityPolicyUpsertBulk{
		create: aaspcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiAgentSecurityPolicy.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aaspcb *AiAgentSecurityPolicyCreateBulk) OnConflictColumns(columns ...string) *AiAgentSecurityPolicyUpsertBulk {
	aaspcb.conflict = append(aaspcb.conflict, sql.ConflictColumns(columns...))
	return &AiAgentSecurityPolicyUpsertBulk{
		create: aaspcb,
	}
}

// AiAgentSecurityPolicyUpsertBulk is the builder for "upsert"-ing
// a bulk of AiAgentSecurityPolicy nodes.
type AiAgentSecurityPolicyUpsertBulk struct {
	create *AiAgentSecurityPolicyCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AiAgentSecurityPolicy.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aiagentsecuritypolicy.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateNewValues() *AiAgentSecurityPolicyUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(aiagentsecuritypolicy.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(aiagentsecuritypolicy.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiAgentSecurityPolicy.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AiAgentSecurityPolicyUpsertBulk) Ignore() *AiAgentSecurityPolicyUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiAgentSecurityPolicyUpsertBulk) DoNothing() *AiAgentSecurityPolicyUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiAgentSecurityPolicyCreateBulk.OnConflict
// documentation for more info.
func (u *AiAgentSecurityPolicyUpsertBulk) Update(set func(*AiAgentSecurityPolicyUpsert)) *AiAgentSecurityPolicyUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiAgentSecurityPolicyUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetUpdatedAt(v time.Time) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateUpdatedAt() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetDeletedAt(v time.Time) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateDeletedAt() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiAgentSecurityPolicyUpsertBulk) ClearDeletedAt() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetName(v string) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateName() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateName()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetAgentID(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetAgentID(v)
	})
}

// AddAgentID adds v to the "agent_id" field.
func (u *AiAgentSecurityPolicyUpsertBulk) AddAgentID(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateAgentID() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateAgentID()
	})
}

// SetPolicyCategory sets the "policy_category" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetPolicyCategory(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetPolicyCategory(v)
	})
}

// AddPolicyCategory adds v to the "policy_category" field.
func (u *AiAgentSecurityPolicyUpsertBulk) AddPolicyCategory(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddPolicyCategory(v)
	})
}

// UpdatePolicyCategory sets the "policy_category" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdatePolicyCategory() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdatePolicyCategory()
	})
}

// SetRiskLevel sets the "risk_level" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetRiskLevel(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetRiskLevel(v)
	})
}

// AddRiskLevel adds v to the "risk_level" field.
func (u *AiAgentSecurityPolicyUpsertBulk) AddRiskLevel(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddRiskLevel(v)
	})
}

// UpdateRiskLevel sets the "risk_level" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateRiskLevel() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateRiskLevel()
	})
}

// SetEnabled sets the "enabled" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetEnabled(v bool) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetEnabled(v)
	})
}

// UpdateEnabled sets the "enabled" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateEnabled() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateEnabled()
	})
}

// SetPolicies sets the "policies" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetPolicies(v *pq.StringArray) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetPolicies(v)
	})
}

// UpdatePolicies sets the "policies" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdatePolicies() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdatePolicies()
	})
}

// SetHitAction sets the "hit_action" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetHitAction(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetHitAction(v)
	})
}

// AddHitAction adds v to the "hit_action" field.
func (u *AiAgentSecurityPolicyUpsertBulk) AddHitAction(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddHitAction(v)
	})
}

// UpdateHitAction sets the "hit_action" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateHitAction() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateHitAction()
	})
}

// SetHitResponse sets the "hit_response" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetHitResponse(v string) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetHitResponse(v)
	})
}

// UpdateHitResponse sets the "hit_response" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateHitResponse() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateHitResponse()
	})
}

// SetUpdatedBy sets the "updated_by" field.
func (u *AiAgentSecurityPolicyUpsertBulk) SetUpdatedBy(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.SetUpdatedBy(v)
	})
}

// AddUpdatedBy adds v to the "updated_by" field.
func (u *AiAgentSecurityPolicyUpsertBulk) AddUpdatedBy(v int64) *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.AddUpdatedBy(v)
	})
}

// UpdateUpdatedBy sets the "updated_by" field to the value that was provided on create.
func (u *AiAgentSecurityPolicyUpsertBulk) UpdateUpdatedBy() *AiAgentSecurityPolicyUpsertBulk {
	return u.Update(func(s *AiAgentSecurityPolicyUpsert) {
		s.UpdateUpdatedBy()
	})
}

// Exec executes the query.
func (u *AiAgentSecurityPolicyUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AiAgentSecurityPolicyCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiAgentSecurityPolicyCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiAgentSecurityPolicyUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/atomicquestions"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AtomicQuestionsUpdate is the builder for updating AtomicQuestions entities.
type AtomicQuestionsUpdate struct {
	config
	hooks     []Hook
	mutation  *AtomicQuestionsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AtomicQuestionsUpdate builder.
func (aqu *AtomicQuestionsUpdate) Where(ps ...predicate.AtomicQuestions) *AtomicQuestionsUpdate {
	aqu.mutation.Where(ps...)
	return aqu
}

// SetUpdatedAt sets the "updated_at" field.
func (aqu *AtomicQuestionsUpdate) SetUpdatedAt(t time.Time) *AtomicQuestionsUpdate {
	aqu.mutation.SetUpdatedAt(t)
	return aqu
}

// SetEntityTag sets the "entity_tag" field.
func (aqu *AtomicQuestionsUpdate) SetEntityTag(s string) *AtomicQuestionsUpdate {
	aqu.mutation.SetEntityTag(s)
	return aqu
}

// SetNillableEntityTag sets the "entity_tag" field if the given value is not nil.
func (aqu *AtomicQuestionsUpdate) SetNillableEntityTag(s *string) *AtomicQuestionsUpdate {
	if s != nil {
		aqu.SetEntityTag(*s)
	}
	return aqu
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (aqu *AtomicQuestionsUpdate) SetPreEntityTag(s string) *AtomicQuestionsUpdate {
	aqu.mutation.SetPreEntityTag(s)
	return aqu
}

// SetNillablePreEntityTag sets the "pre_entity_tag" field if the given value is not nil.
func (aqu *AtomicQuestionsUpdate) SetNillablePreEntityTag(s *string) *AtomicQuestionsUpdate {
	if s != nil {
		aqu.SetPreEntityTag(*s)
	}
	return aqu
}

// SetFileRelationID sets the "file_relation_id" field.
func (aqu *AtomicQuestionsUpdate) SetFileRelationID(i int64) *AtomicQuestionsUpdate {
	aqu.mutation.ResetFileRelationID()
	aqu.mutation.SetFileRelationID(i)
	return aqu
}

// SetNillableFileRelationID sets the "file_relation_id" field if the given value is not nil.
func (aqu *AtomicQuestionsUpdate) SetNillableFileRelationID(i *int64) *AtomicQuestionsUpdate {
	if i != nil {
		aqu.SetFileRelationID(*i)
	}
	return aqu
}

// AddFileRelationID adds i to the "file_relation_id" field.
func (aqu *AtomicQuestionsUpdate) AddFileRelationID(i int64) *AtomicQuestionsUpdate {
	aqu.mutation.AddFileRelationID(i)
	return aqu
}

// SetChunkIndex sets the "chunk_index" field.
func (aqu *AtomicQuestionsUpdate) SetChunkIndex(i int64) *AtomicQuestionsUpdate {
	aqu.mutation.ResetChunkIndex()
	aqu.mutation.SetChunkIndex(i)
	return aqu
}

// SetNillableChunkIndex sets the "chunk_index" field if the given value is not nil.
func (aqu *AtomicQuestionsUpdate) SetNillableChunkIndex(i *int64) *AtomicQuestionsUpdate {
	if i != nil {
		aqu.SetChunkIndex(*i)
	}
	return aqu
}

// AddChunkIndex adds i to the "chunk_index" field.
func (aqu *AtomicQuestionsUpdate) AddChunkIndex(i int64) *AtomicQuestionsUpdate {
	aqu.mutation.AddChunkIndex(i)
	return aqu
}

// SetChunkSize sets the "chunk_size" field.
func (aqu *AtomicQuestionsUpdate) SetChunkSize(i int64) *AtomicQuestionsUpdate {
	aqu.mutation.ResetChunkSize()
	aqu.mutation.SetChunkSize(i)
	return aqu
}

// SetNillableChunkSize sets the "chunk_size" field if the given value is not nil.
func (aqu *AtomicQuestionsUpdate) SetNillableChunkSize(i *int64) *AtomicQuestionsUpdate {
	if i != nil {
		aqu.SetChunkSize(*i)
	}
	return aqu
}

// AddChunkSize adds i to the "chunk_size" field.
func (aqu *AtomicQuestionsUpdate) AddChunkSize(i int64) *AtomicQuestionsUpdate {
	aqu.mutation.AddChunkSize(i)
	return aqu
}

// SetIndex sets the "index" field.
func (aqu *AtomicQuestionsUpdate) SetIndex(i int64) *AtomicQuestionsUpdate {
	aqu.mutation.ResetIndex()
	aqu.mutation.SetIndex(i)
	return aqu
}

// SetNillableIndex sets the "index" field if the given value is not nil.
func (aqu *AtomicQuestionsUpdate) SetNillableIndex(i *int64) *AtomicQuestionsUpdate {
	if i != nil {
		aqu.SetIndex(*i)
	}
	return aqu
}

// AddIndex adds i to the "index" field.
func (aqu *AtomicQuestionsUpdate) AddIndex(i int64) *AtomicQuestionsUpdate {
	aqu.mutation.AddIndex(i)
	return aqu
}

// SetQuestion sets the "question" field.
func (aqu *AtomicQuestionsUpdate) SetQuestion(pa *pq.StringArray) *AtomicQuestionsUpdate {
	aqu.mutation.SetQuestion(pa)
	return aqu
}

// SetIsHandle sets the "is_handle" field.
func (aqu *AtomicQuestionsUpdate) SetIsHandle(b bool) *AtomicQuestionsUpdate {
	aqu.mutation.SetIsHandle(b)
	return aqu
}

// SetNillableIsHandle sets the "is_handle" field if the given value is not nil.
func (aqu *AtomicQuestionsUpdate) SetNillableIsHandle(b *bool) *AtomicQuestionsUpdate {
	if b != nil {
		aqu.SetIsHandle(*b)
	}
	return aqu
}

// Mutation returns the AtomicQuestionsMutation object of the builder.
func (aqu *AtomicQuestionsUpdate) Mutation() *AtomicQuestionsMutation {
	return aqu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (aqu *AtomicQuestionsUpdate) Save(ctx context.Context) (int, error) {
	aqu.defaults()
	return withHooks(ctx, aqu.sqlSave, aqu.mutation, aqu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aqu *AtomicQuestionsUpdate) SaveX(ctx context.Context) int {
	affected, err := aqu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (aqu *AtomicQuestionsUpdate) Exec(ctx context.Context) error {
	_, err := aqu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aqu *AtomicQuestionsUpdate) ExecX(ctx context.Context) {
	if err := aqu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aqu *AtomicQuestionsUpdate) defaults() {
	if _, ok := aqu.mutation.UpdatedAt(); !ok {
		v := atomicquestions.UpdateDefaultUpdatedAt()
		aqu.mutation.SetUpdatedAt(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aqu *AtomicQuestionsUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AtomicQuestionsUpdate {
	aqu.modifiers = append(aqu.modifiers, modifiers...)
	return aqu
}

func (aqu *AtomicQuestionsUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(atomicquestions.Table, atomicquestions.Columns, sqlgraph.NewFieldSpec(atomicquestions.FieldID, field.TypeInt64))
	if ps := aqu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aqu.mutation.UpdatedAt(); ok {
		_spec.SetField(atomicquestions.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aqu.mutation.EntityTag(); ok {
		_spec.SetField(atomicquestions.FieldEntityTag, field.TypeString, value)
	}
	if value, ok := aqu.mutation.PreEntityTag(); ok {
		_spec.SetField(atomicquestions.FieldPreEntityTag, field.TypeString, value)
	}
	if value, ok := aqu.mutation.FileRelationID(); ok {
		_spec.SetField(atomicquestions.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := aqu.mutation.AddedFileRelationID(); ok {
		_spec.AddField(atomicquestions.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := aqu.mutation.ChunkIndex(); ok {
		_spec.SetField(atomicquestions.FieldChunkIndex, field.TypeInt64, value)
	}
	if value, ok := aqu.mutation.AddedChunkIndex(); ok {
		_spec.AddField(atomicquestions.FieldChunkIndex, field.TypeInt64, value)
	}
	if value, ok := aqu.mutation.ChunkSize(); ok {
		_spec.SetField(atomicquestions.FieldChunkSize, field.TypeInt64, value)
	}
	if value, ok := aqu.mutation.AddedChunkSize(); ok {
		_spec.AddField(atomicquestions.FieldChunkSize, field.TypeInt64, value)
	}
	if value, ok := aqu.mutation.Index(); ok {
		_spec.SetField(atomicquestions.FieldIndex, field.TypeInt64, value)
	}
	if value, ok := aqu.mutation.AddedIndex(); ok {
		_spec.AddField(atomicquestions.FieldIndex, field.TypeInt64, value)
	}
	if value, ok := aqu.mutation.Question(); ok {
		_spec.SetField(atomicquestions.FieldQuestion, field.TypeOther, value)
	}
	if value, ok := aqu.mutation.IsHandle(); ok {
		_spec.SetField(atomicquestions.FieldIsHandle, field.TypeBool, value)
	}
	_spec.AddModifiers(aqu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, aqu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{atomicquestions.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	aqu.mutation.done = true
	return n, nil
}

// AtomicQuestionsUpdateOne is the builder for updating a single AtomicQuestions entity.
type AtomicQuestionsUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AtomicQuestionsMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (aquo *AtomicQuestionsUpdateOne) SetUpdatedAt(t time.Time) *AtomicQuestionsUpdateOne {
	aquo.mutation.SetUpdatedAt(t)
	return aquo
}

// SetEntityTag sets the "entity_tag" field.
func (aquo *AtomicQuestionsUpdateOne) SetEntityTag(s string) *AtomicQuestionsUpdateOne {
	aquo.mutation.SetEntityTag(s)
	return aquo
}

// SetNillableEntityTag sets the "entity_tag" field if the given value is not nil.
func (aquo *AtomicQuestionsUpdateOne) SetNillableEntityTag(s *string) *AtomicQuestionsUpdateOne {
	if s != nil {
		aquo.SetEntityTag(*s)
	}
	return aquo
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (aquo *AtomicQuestionsUpdateOne) SetPreEntityTag(s string) *AtomicQuestionsUpdateOne {
	aquo.mutation.SetPreEntityTag(s)
	return aquo
}

// SetNillablePreEntityTag sets the "pre_entity_tag" field if the given value is not nil.
func (aquo *AtomicQuestionsUpdateOne) SetNillablePreEntityTag(s *string) *AtomicQuestionsUpdateOne {
	if s != nil {
		aquo.SetPreEntityTag(*s)
	}
	return aquo
}

// SetFileRelationID sets the "file_relation_id" field.
func (aquo *AtomicQuestionsUpdateOne) SetFileRelationID(i int64) *AtomicQuestionsUpdateOne {
	aquo.mutation.ResetFileRelationID()
	aquo.mutation.SetFileRelationID(i)
	return aquo
}

// SetNillableFileRelationID sets the "file_relation_id" field if the given value is not nil.
func (aquo *AtomicQuestionsUpdateOne) SetNillableFileRelationID(i *int64) *AtomicQuestionsUpdateOne {
	if i != nil {
		aquo.SetFileRelationID(*i)
	}
	return aquo
}

// AddFileRelationID adds i to the "file_relation_id" field.
func (aquo *AtomicQuestionsUpdateOne) AddFileRelationID(i int64) *AtomicQuestionsUpdateOne {
	aquo.mutation.AddFileRelationID(i)
	return aquo
}

// SetChunkIndex sets the "chunk_index" field.
func (aquo *AtomicQuestionsUpdateOne) SetChunkIndex(i int64) *AtomicQuestionsUpdateOne {
	aquo.mutation.ResetChunkIndex()
	aquo.mutation.SetChunkIndex(i)
	return aquo
}

// SetNillableChunkIndex sets the "chunk_index" field if the given value is not nil.
func (aquo *AtomicQuestionsUpdateOne) SetNillableChunkIndex(i *int64) *AtomicQuestionsUpdateOne {
	if i != nil {
		aquo.SetChunkIndex(*i)
	}
	return aquo
}

// AddChunkIndex adds i to the "chunk_index" field.
func (aquo *AtomicQuestionsUpdateOne) AddChunkIndex(i int64) *AtomicQuestionsUpdateOne {
	aquo.mutation.AddChunkIndex(i)
	return aquo
}

// SetChunkSize sets the "chunk_size" field.
func (aquo *AtomicQuestionsUpdateOne) SetChunkSize(i int64) *AtomicQuestionsUpdateOne {
	aquo.mutation.ResetChunkSize()
	aquo.mutation.SetChunkSize(i)
	return aquo
}

// SetNillableChunkSize sets the "chunk_size" field if the given value is not nil.
func (aquo *AtomicQuestionsUpdateOne) SetNillableChunkSize(i *int64) *AtomicQuestionsUpdateOne {
	if i != nil {
		aquo.SetChunkSize(*i)
	}
	return aquo
}

// AddChunkSize adds i to the "chunk_size" field.
func (aquo *AtomicQuestionsUpdateOne) AddChunkSize(i int64) *AtomicQuestionsUpdateOne {
	aquo.mutation.AddChunkSize(i)
	return aquo
}

// SetIndex sets the "index" field.
func (aquo *AtomicQuestionsUpdateOne) SetIndex(i int64) *AtomicQuestionsUpdateOne {
	aquo.mutation.ResetIndex()
	aquo.mutation.SetIndex(i)
	return aquo
}

// SetNillableIndex sets the "index" field if the given value is not nil.
func (aquo *AtomicQuestionsUpdateOne) SetNillableIndex(i *int64) *AtomicQuestionsUpdateOne {
	if i != nil {
		aquo.SetIndex(*i)
	}
	return aquo
}

// AddIndex adds i to the "index" field.
func (aquo *AtomicQuestionsUpdateOne) AddIndex(i int64) *AtomicQuestionsUpdateOne {
	aquo.mutation.AddIndex(i)
	return aquo
}

// SetQuestion sets the "question" field.
func (aquo *AtomicQuestionsUpdateOne) SetQuestion(pa *pq.StringArray) *AtomicQuestionsUpdateOne {
	aquo.mutation.SetQuestion(pa)
	return aquo
}

// SetIsHandle sets the "is_handle" field.
func (aquo *AtomicQuestionsUpdateOne) SetIsHandle(b bool) *AtomicQuestionsUpdateOne {
	aquo.mutation.SetIsHandle(b)
	return aquo
}

// SetNillableIsHandle sets the "is_handle" field if the given value is not nil.
func (aquo *AtomicQuestionsUpdateOne) SetNillableIsHandle(b *bool) *AtomicQuestionsUpdateOne {
	if b != nil {
		aquo.SetIsHandle(*b)
	}
	return aquo
}

// Mutation returns the AtomicQuestionsMutation object of the builder.
func (aquo *AtomicQuestionsUpdateOne) Mutation() *AtomicQuestionsMutation {
	return aquo.mutation
}

// Where appends a list predicates to the AtomicQuestionsUpdate builder.
func (aquo *AtomicQuestionsUpdateOne) Where(ps ...predicate.AtomicQuestions) *AtomicQuestionsUpdateOne {
	aquo.mutation.Where(ps...)
	return aquo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (aquo *AtomicQuestionsUpdateOne) Select(field string, fields ...string) *AtomicQuestionsUpdateOne {
	aquo.fields = append([]string{field}, fields...)
	return aquo
}

// Save executes the query and returns the updated AtomicQuestions entity.
func (aquo *AtomicQuestionsUpdateOne) Save(ctx context.Context) (*AtomicQuestions, error) {
	aquo.defaults()
	return withHooks(ctx, aquo.sqlSave, aquo.mutation, aquo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aquo *AtomicQuestionsUpdateOne) SaveX(ctx context.Context) *AtomicQuestions {
	node, err := aquo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (aquo *AtomicQuestionsUpdateOne) Exec(ctx context.Context) error {
	_, err := aquo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aquo *AtomicQuestionsUpdateOne) ExecX(ctx context.Context) {
	if err := aquo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aquo *AtomicQuestionsUpdateOne) defaults() {
	if _, ok := aquo.mutation.UpdatedAt(); !ok {
		v := atomicquestions.UpdateDefaultUpdatedAt()
		aquo.mutation.SetUpdatedAt(v)
	}
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aquo *AtomicQuestionsUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AtomicQuestionsUpdateOne {
	aquo.modifiers = append(aquo.modifiers, modifiers...)
	return aquo
}

func (aquo *AtomicQuestionsUpdateOne) sqlSave(ctx context.Context) (_node *AtomicQuestions, err error) {
	_spec := sqlgraph.NewUpdateSpec(atomicquestions.Table, atomicquestions.Columns, sqlgraph.NewFieldSpec(atomicquestions.FieldID, field.TypeInt64))
	id, ok := aquo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AtomicQuestions.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := aquo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, atomicquestions.FieldID)
		for _, f := range fields {
			if !atomicquestions.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != atomicquestions.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := aquo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aquo.mutation.UpdatedAt(); ok {
		_spec.SetField(atomicquestions.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aquo.mutation.EntityTag(); ok {
		_spec.SetField(atomicquestions.FieldEntityTag, field.TypeString, value)
	}
	if value, ok := aquo.mutation.PreEntityTag(); ok {
		_spec.SetField(atomicquestions.FieldPreEntityTag, field.TypeString, value)
	}
	if value, ok := aquo.mutation.FileRelationID(); ok {
		_spec.SetField(atomicquestions.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := aquo.mutation.AddedFileRelationID(); ok {
		_spec.AddField(atomicquestions.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := aquo.mutation.ChunkIndex(); ok {
		_spec.SetField(atomicquestions.FieldChunkIndex, field.TypeInt64, value)
	}
	if value, ok := aquo.mutation.AddedChunkIndex(); ok {
		_spec.AddField(atomicquestions.FieldChunkIndex, field.TypeInt64, value)
	}
	if value, ok := aquo.mutation.ChunkSize(); ok {
		_spec.SetField(atomicquestions.FieldChunkSize, field.TypeInt64, value)
	}
	if value, ok := aquo.mutation.AddedChunkSize(); ok {
		_spec.AddField(atomicquestions.FieldChunkSize, field.TypeInt64, value)
	}
	if value, ok := aquo.mutation.Index(); ok {
		_spec.SetField(atomicquestions.FieldIndex, field.TypeInt64, value)
	}
	if value, ok := aquo.mutation.AddedIndex(); ok {
		_spec.AddField(atomicquestions.FieldIndex, field.TypeInt64, value)
	}
	if value, ok := aquo.mutation.Question(); ok {
		_spec.SetField(atomicquestions.FieldQuestion, field.TypeOther, value)
	}
	if value, ok := aquo.mutation.IsHandle(); ok {
		_spec.SetField(atomicquestions.FieldIsHandle, field.TypeBool, value)
	}
	_spec.AddModifiers(aquo.modifiers...)
	_node = &AtomicQuestions{config: aquo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, aquo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{atomicquestions.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	aquo.mutation.done = true
	return _node, nil
}

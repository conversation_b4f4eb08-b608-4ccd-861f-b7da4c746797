// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
)

// AiChat is the model entity for the AiChat schema.
type AiChat struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 租户ID
	TenantID int64 `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 聊天名称
	Name string `json:"name,omitempty"`
	// 用户id
	UserID int64 `json:"user_id,omitempty"`
	// 聊天类型 1.rag
	ChatType int64 `json:"chat_type,omitempty"`
	// agent id
	AgentID int64 `json:"agent_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the AiChatQuery when eager-loading is set.
	Edges        AiChatEdges `json:"edges"`
	selectValues sql.SelectValues
}

// AiChatEdges holds the relations/edges for other nodes in the graph.
type AiChatEdges struct {
	// AiAgent holds the value of the ai_agent edge.
	AiAgent *AiAgent `json:"ai_agent,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// AiAgentOrErr returns the AiAgent value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e AiChatEdges) AiAgentOrErr() (*AiAgent, error) {
	if e.loadedTypes[0] {
		if e.AiAgent == nil {
			// Edge was loaded but was not found.
			return nil, &NotFoundError{label: aiagent.Label}
		}
		return e.AiAgent, nil
	}
	return nil, &NotLoadedError{edge: "ai_agent"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AiChat) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case aichat.FieldID, aichat.FieldTenantID, aichat.FieldUserID, aichat.FieldChatType, aichat.FieldAgentID:
			values[i] = new(sql.NullInt64)
		case aichat.FieldName:
			values[i] = new(sql.NullString)
		case aichat.FieldCreatedAt, aichat.FieldUpdatedAt, aichat.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AiChat fields.
func (ac *AiChat) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case aichat.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			ac.ID = int64(value.Int64)
		case aichat.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				ac.TenantID = value.Int64
			}
		case aichat.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				ac.CreatedAt = value.Time
			}
		case aichat.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				ac.UpdatedAt = value.Time
			}
		case aichat.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				ac.DeletedAt = value.Time
			}
		case aichat.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				ac.Name = value.String
			}
		case aichat.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				ac.UserID = value.Int64
			}
		case aichat.FieldChatType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field chat_type", values[i])
			} else if value.Valid {
				ac.ChatType = value.Int64
			}
		case aichat.FieldAgentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field agent_id", values[i])
			} else if value.Valid {
				ac.AgentID = value.Int64
			}
		default:
			ac.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AiChat.
// This includes values selected through modifiers, order, etc.
func (ac *AiChat) Value(name string) (ent.Value, error) {
	return ac.selectValues.Get(name)
}

// QueryAiAgent queries the "ai_agent" edge of the AiChat entity.
func (ac *AiChat) QueryAiAgent() *AiAgentQuery {
	return NewAiChatClient(ac.config).QueryAiAgent(ac)
}

// Update returns a builder for updating this AiChat.
// Note that you need to call AiChat.Unwrap() before calling this method if this AiChat
// was returned from a transaction, and the transaction was committed or rolled back.
func (ac *AiChat) Update() *AiChatUpdateOne {
	return NewAiChatClient(ac.config).UpdateOne(ac)
}

// Unwrap unwraps the AiChat entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (ac *AiChat) Unwrap() *AiChat {
	_tx, ok := ac.config.driver.(*txDriver)
	if !ok {
		panic("ent: AiChat is not a transactional entity")
	}
	ac.config.driver = _tx.drv
	return ac
}

// String implements the fmt.Stringer.
func (ac *AiChat) String() string {
	var builder strings.Builder
	builder.WriteString("AiChat(")
	builder.WriteString(fmt.Sprintf("id=%v, ", ac.ID))
	builder.WriteString("tenant_id=")
	builder.WriteString(fmt.Sprintf("%v", ac.TenantID))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(ac.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(ac.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(ac.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(ac.Name)
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", ac.UserID))
	builder.WriteString(", ")
	builder.WriteString("chat_type=")
	builder.WriteString(fmt.Sprintf("%v", ac.ChatType))
	builder.WriteString(", ")
	builder.WriteString("agent_id=")
	builder.WriteString(fmt.Sprintf("%v", ac.AgentID))
	builder.WriteByte(')')
	return builder.String()
}

// AiChats is a parsable slice of AiChat.
type AiChats []*AiChat

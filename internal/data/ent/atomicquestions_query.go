// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/atomicquestions"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AtomicQuestionsQuery is the builder for querying AtomicQuestions entities.
type AtomicQuestionsQuery struct {
	config
	ctx        *QueryContext
	order      []atomicquestions.OrderOption
	inters     []Interceptor
	predicates []predicate.AtomicQuestions
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AtomicQuestionsQuery builder.
func (aqq *AtomicQuestionsQuery) Where(ps ...predicate.AtomicQuestions) *AtomicQuestionsQuery {
	aqq.predicates = append(aqq.predicates, ps...)
	return aqq
}

// Limit the number of records to be returned by this query.
func (aqq *AtomicQuestionsQuery) Limit(limit int) *AtomicQuestionsQuery {
	aqq.ctx.Limit = &limit
	return aqq
}

// Offset to start from.
func (aqq *AtomicQuestionsQuery) Offset(offset int) *AtomicQuestionsQuery {
	aqq.ctx.Offset = &offset
	return aqq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (aqq *AtomicQuestionsQuery) Unique(unique bool) *AtomicQuestionsQuery {
	aqq.ctx.Unique = &unique
	return aqq
}

// Order specifies how the records should be ordered.
func (aqq *AtomicQuestionsQuery) Order(o ...atomicquestions.OrderOption) *AtomicQuestionsQuery {
	aqq.order = append(aqq.order, o...)
	return aqq
}

// First returns the first AtomicQuestions entity from the query.
// Returns a *NotFoundError when no AtomicQuestions was found.
func (aqq *AtomicQuestionsQuery) First(ctx context.Context) (*AtomicQuestions, error) {
	nodes, err := aqq.Limit(1).All(setContextOp(ctx, aqq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{atomicquestions.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (aqq *AtomicQuestionsQuery) FirstX(ctx context.Context) *AtomicQuestions {
	node, err := aqq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AtomicQuestions ID from the query.
// Returns a *NotFoundError when no AtomicQuestions ID was found.
func (aqq *AtomicQuestionsQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = aqq.Limit(1).IDs(setContextOp(ctx, aqq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{atomicquestions.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (aqq *AtomicQuestionsQuery) FirstIDX(ctx context.Context) int64 {
	id, err := aqq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AtomicQuestions entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AtomicQuestions entity is found.
// Returns a *NotFoundError when no AtomicQuestions entities are found.
func (aqq *AtomicQuestionsQuery) Only(ctx context.Context) (*AtomicQuestions, error) {
	nodes, err := aqq.Limit(2).All(setContextOp(ctx, aqq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{atomicquestions.Label}
	default:
		return nil, &NotSingularError{atomicquestions.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (aqq *AtomicQuestionsQuery) OnlyX(ctx context.Context) *AtomicQuestions {
	node, err := aqq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AtomicQuestions ID in the query.
// Returns a *NotSingularError when more than one AtomicQuestions ID is found.
// Returns a *NotFoundError when no entities are found.
func (aqq *AtomicQuestionsQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = aqq.Limit(2).IDs(setContextOp(ctx, aqq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{atomicquestions.Label}
	default:
		err = &NotSingularError{atomicquestions.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (aqq *AtomicQuestionsQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := aqq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AtomicQuestionsSlice.
func (aqq *AtomicQuestionsQuery) All(ctx context.Context) ([]*AtomicQuestions, error) {
	ctx = setContextOp(ctx, aqq.ctx, "All")
	if err := aqq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AtomicQuestions, *AtomicQuestionsQuery]()
	return withInterceptors[[]*AtomicQuestions](ctx, aqq, qr, aqq.inters)
}

// AllX is like All, but panics if an error occurs.
func (aqq *AtomicQuestionsQuery) AllX(ctx context.Context) []*AtomicQuestions {
	nodes, err := aqq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AtomicQuestions IDs.
func (aqq *AtomicQuestionsQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if aqq.ctx.Unique == nil && aqq.path != nil {
		aqq.Unique(true)
	}
	ctx = setContextOp(ctx, aqq.ctx, "IDs")
	if err = aqq.Select(atomicquestions.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (aqq *AtomicQuestionsQuery) IDsX(ctx context.Context) []int64 {
	ids, err := aqq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (aqq *AtomicQuestionsQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, aqq.ctx, "Count")
	if err := aqq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, aqq, querierCount[*AtomicQuestionsQuery](), aqq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (aqq *AtomicQuestionsQuery) CountX(ctx context.Context) int {
	count, err := aqq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (aqq *AtomicQuestionsQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, aqq.ctx, "Exist")
	switch _, err := aqq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (aqq *AtomicQuestionsQuery) ExistX(ctx context.Context) bool {
	exist, err := aqq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AtomicQuestionsQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (aqq *AtomicQuestionsQuery) Clone() *AtomicQuestionsQuery {
	if aqq == nil {
		return nil
	}
	return &AtomicQuestionsQuery{
		config:     aqq.config,
		ctx:        aqq.ctx.Clone(),
		order:      append([]atomicquestions.OrderOption{}, aqq.order...),
		inters:     append([]Interceptor{}, aqq.inters...),
		predicates: append([]predicate.AtomicQuestions{}, aqq.predicates...),
		// clone intermediate query.
		sql:  aqq.sql.Clone(),
		path: aqq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AtomicQuestions.Query().
//		GroupBy(atomicquestions.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (aqq *AtomicQuestionsQuery) GroupBy(field string, fields ...string) *AtomicQuestionsGroupBy {
	aqq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AtomicQuestionsGroupBy{build: aqq}
	grbuild.flds = &aqq.ctx.Fields
	grbuild.label = atomicquestions.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.AtomicQuestions.Query().
//		Select(atomicquestions.FieldCreatedAt).
//		Scan(ctx, &v)
func (aqq *AtomicQuestionsQuery) Select(fields ...string) *AtomicQuestionsSelect {
	aqq.ctx.Fields = append(aqq.ctx.Fields, fields...)
	sbuild := &AtomicQuestionsSelect{AtomicQuestionsQuery: aqq}
	sbuild.label = atomicquestions.Label
	sbuild.flds, sbuild.scan = &aqq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AtomicQuestionsSelect configured with the given aggregations.
func (aqq *AtomicQuestionsQuery) Aggregate(fns ...AggregateFunc) *AtomicQuestionsSelect {
	return aqq.Select().Aggregate(fns...)
}

func (aqq *AtomicQuestionsQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range aqq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, aqq); err != nil {
				return err
			}
		}
	}
	for _, f := range aqq.ctx.Fields {
		if !atomicquestions.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if aqq.path != nil {
		prev, err := aqq.path(ctx)
		if err != nil {
			return err
		}
		aqq.sql = prev
	}
	return nil
}

func (aqq *AtomicQuestionsQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AtomicQuestions, error) {
	var (
		nodes = []*AtomicQuestions{}
		_spec = aqq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AtomicQuestions).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AtomicQuestions{config: aqq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(aqq.modifiers) > 0 {
		_spec.Modifiers = aqq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, aqq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (aqq *AtomicQuestionsQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := aqq.querySpec()
	if len(aqq.modifiers) > 0 {
		_spec.Modifiers = aqq.modifiers
	}
	_spec.Node.Columns = aqq.ctx.Fields
	if len(aqq.ctx.Fields) > 0 {
		_spec.Unique = aqq.ctx.Unique != nil && *aqq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, aqq.driver, _spec)
}

func (aqq *AtomicQuestionsQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(atomicquestions.Table, atomicquestions.Columns, sqlgraph.NewFieldSpec(atomicquestions.FieldID, field.TypeInt64))
	_spec.From = aqq.sql
	if unique := aqq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if aqq.path != nil {
		_spec.Unique = true
	}
	if fields := aqq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, atomicquestions.FieldID)
		for i := range fields {
			if fields[i] != atomicquestions.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := aqq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := aqq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := aqq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := aqq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (aqq *AtomicQuestionsQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(aqq.driver.Dialect())
	t1 := builder.Table(atomicquestions.Table)
	columns := aqq.ctx.Fields
	if len(columns) == 0 {
		columns = atomicquestions.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if aqq.sql != nil {
		selector = aqq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if aqq.ctx.Unique != nil && *aqq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range aqq.modifiers {
		m(selector)
	}
	for _, p := range aqq.predicates {
		p(selector)
	}
	for _, p := range aqq.order {
		p(selector)
	}
	if offset := aqq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := aqq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (aqq *AtomicQuestionsQuery) ForUpdate(opts ...sql.LockOption) *AtomicQuestionsQuery {
	if aqq.driver.Dialect() == dialect.Postgres {
		aqq.Unique(false)
	}
	aqq.modifiers = append(aqq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return aqq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (aqq *AtomicQuestionsQuery) ForShare(opts ...sql.LockOption) *AtomicQuestionsQuery {
	if aqq.driver.Dialect() == dialect.Postgres {
		aqq.Unique(false)
	}
	aqq.modifiers = append(aqq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return aqq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (aqq *AtomicQuestionsQuery) Modify(modifiers ...func(s *sql.Selector)) *AtomicQuestionsSelect {
	aqq.modifiers = append(aqq.modifiers, modifiers...)
	return aqq.Select()
}

// AtomicQuestionsGroupBy is the group-by builder for AtomicQuestions entities.
type AtomicQuestionsGroupBy struct {
	selector
	build *AtomicQuestionsQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (aqgb *AtomicQuestionsGroupBy) Aggregate(fns ...AggregateFunc) *AtomicQuestionsGroupBy {
	aqgb.fns = append(aqgb.fns, fns...)
	return aqgb
}

// Scan applies the selector query and scans the result into the given value.
func (aqgb *AtomicQuestionsGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aqgb.build.ctx, "GroupBy")
	if err := aqgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AtomicQuestionsQuery, *AtomicQuestionsGroupBy](ctx, aqgb.build, aqgb, aqgb.build.inters, v)
}

func (aqgb *AtomicQuestionsGroupBy) sqlScan(ctx context.Context, root *AtomicQuestionsQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(aqgb.fns))
	for _, fn := range aqgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*aqgb.flds)+len(aqgb.fns))
		for _, f := range *aqgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*aqgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aqgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AtomicQuestionsSelect is the builder for selecting fields of AtomicQuestions entities.
type AtomicQuestionsSelect struct {
	*AtomicQuestionsQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (aqs *AtomicQuestionsSelect) Aggregate(fns ...AggregateFunc) *AtomicQuestionsSelect {
	aqs.fns = append(aqs.fns, fns...)
	return aqs
}

// Scan applies the selector query and scans the result into the given value.
func (aqs *AtomicQuestionsSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aqs.ctx, "Select")
	if err := aqs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AtomicQuestionsQuery, *AtomicQuestionsSelect](ctx, aqs.AtomicQuestionsQuery, aqs, aqs.inters, v)
}

func (aqs *AtomicQuestionsSelect) sqlScan(ctx context.Context, root *AtomicQuestionsQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(aqs.fns))
	for _, fn := range aqs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*aqs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aqs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (aqs *AtomicQuestionsSelect) Modify(modifiers ...func(s *sql.Selector)) *AtomicQuestionsSelect {
	aqs.modifiers = append(aqs.modifiers, modifiers...)
	return aqs
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/useragentorder"
)

// UserAgentOrderDelete is the builder for deleting a UserAgentOrder entity.
type UserAgentOrderDelete struct {
	config
	hooks    []Hook
	mutation *UserAgentOrderMutation
}

// Where appends a list predicates to the UserAgentOrderDelete builder.
func (uaod *UserAgentOrderDelete) Where(ps ...predicate.UserAgentOrder) *UserAgentOrderDelete {
	uaod.mutation.Where(ps...)
	return uaod
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (uaod *UserAgentOrderDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, uaod.sqlExec, uaod.mutation, uaod.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (uaod *UserAgentOrderDelete) ExecX(ctx context.Context) int {
	n, err := uaod.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (uaod *UserAgentOrderDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(useragentorder.Table, sqlgraph.NewFieldSpec(useragentorder.FieldID, field.TypeInt64))
	if ps := uaod.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, uaod.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	uaod.mutation.done = true
	return affected, err
}

// UserAgentOrderDeleteOne is the builder for deleting a single UserAgentOrder entity.
type UserAgentOrderDeleteOne struct {
	uaod *UserAgentOrderDelete
}

// Where appends a list predicates to the UserAgentOrderDelete builder.
func (uaodo *UserAgentOrderDeleteOne) Where(ps ...predicate.UserAgentOrder) *UserAgentOrderDeleteOne {
	uaodo.uaod.mutation.Where(ps...)
	return uaodo
}

// Exec executes the deletion query.
func (uaodo *UserAgentOrderDeleteOne) Exec(ctx context.Context) error {
	n, err := uaodo.uaod.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{useragentorder.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (uaodo *UserAgentOrderDeleteOne) ExecX(ctx context.Context) {
	if err := uaodo.Exec(ctx); err != nil {
		panic(err)
	}
}

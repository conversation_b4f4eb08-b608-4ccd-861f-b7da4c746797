// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"gitlab.minum.cloud/BackendTeam/pkg/middleware/page"
)

// Paginate get page number and page size from context and return a query with offset and limit
func (aa *AiAgentQuery) Paginate(ctx context.Context) *AiAgentQuery {
	pageNum, pageSize := page.Extract(ctx)
	return aa.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (aasl *AiAgentSecurityLogQuery) Paginate(ctx context.Context) *AiAgentSecurityLogQuery {
	pageNum, pageSize := page.Extract(ctx)
	return aasl.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (aasp *AiAgentSecurityPolicyQuery) Paginate(ctx context.Context) *AiAgentSecurityPolicyQuery {
	pageNum, pageSize := page.Extract(ctx)
	return aasp.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (ac *AiChatQuery) Paginate(ctx context.Context) *AiChatQuery {
	pageNum, pageSize := page.Extract(ctx)
	return ac.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (aci *AiChatItemQuery) Paginate(ctx context.Context) *AiChatItemQuery {
	pageNum, pageSize := page.Extract(ctx)
	return aci.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (am *AiModelQuery) Paginate(ctx context.Context) *AiModelQuery {
	pageNum, pageSize := page.Extract(ctx)
	return am.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (amd *AiModelDetailQuery) Paginate(ctx context.Context) *AiModelDetailQuery {
	pageNum, pageSize := page.Extract(ctx)
	return amd.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (amu *AiModelUsageQuery) Paginate(ctx context.Context) *AiModelUsageQuery {
	pageNum, pageSize := page.Extract(ctx)
	return amu.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (aq *AtomicQuestionsQuery) Paginate(ctx context.Context) *AtomicQuestionsQuery {
	pageNum, pageSize := page.Extract(ctx)
	return aq.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (cf *ClassificationFilesQuery) Paginate(ctx context.Context) *ClassificationFilesQuery {
	pageNum, pageSize := page.Extract(ctx)
	return cf.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (daa *DefaultAgentAvatarQuery) Paginate(ctx context.Context) *DefaultAgentAvatarQuery {
	pageNum, pageSize := page.Extract(ctx)
	return daa.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (emu *ExternalModelUsageQuery) Paginate(ctx context.Context) *ExternalModelUsageQuery {
	pageNum, pageSize := page.Extract(ctx)
	return emu.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (kb *KnowledgeBaseQuery) Paginate(ctx context.Context) *KnowledgeBaseQuery {
	pageNum, pageSize := page.Extract(ctx)
	return kb.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (kbf *KnowledgeBaseFileQuery) Paginate(ctx context.Context) *KnowledgeBaseFileQuery {
	pageNum, pageSize := page.Extract(ctx)
	return kbf.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Paginate get page number and page size from context and return a query with offset and limit
func (uao *UserAgentOrderQuery) Paginate(ctx context.Context) *UserAgentOrderQuery {
	pageNum, pageSize := page.Extract(ctx)
	return uao.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodeldetail"
)

// AiModelDetailCreate is the builder for creating a AiModelDetail entity.
type AiModelDetailCreate struct {
	config
	mutation *AiModelDetailMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (amdc *AiModelDetailCreate) SetCreatedAt(t time.Time) *AiModelDetailCreate {
	amdc.mutation.SetCreatedAt(t)
	return amdc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (amdc *AiModelDetailCreate) SetNillableCreatedAt(t *time.Time) *AiModelDetailCreate {
	if t != nil {
		amdc.SetCreatedAt(*t)
	}
	return amdc
}

// SetUpdatedAt sets the "updated_at" field.
func (amdc *AiModelDetailCreate) SetUpdatedAt(t time.Time) *AiModelDetailCreate {
	amdc.mutation.SetUpdatedAt(t)
	return amdc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (amdc *AiModelDetailCreate) SetNillableUpdatedAt(t *time.Time) *AiModelDetailCreate {
	if t != nil {
		amdc.SetUpdatedAt(*t)
	}
	return amdc
}

// SetDeletedAt sets the "deleted_at" field.
func (amdc *AiModelDetailCreate) SetDeletedAt(t time.Time) *AiModelDetailCreate {
	amdc.mutation.SetDeletedAt(t)
	return amdc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (amdc *AiModelDetailCreate) SetNillableDeletedAt(t *time.Time) *AiModelDetailCreate {
	if t != nil {
		amdc.SetDeletedAt(*t)
	}
	return amdc
}

// SetModelName sets the "model_name" field.
func (amdc *AiModelDetailCreate) SetModelName(s string) *AiModelDetailCreate {
	amdc.mutation.SetModelName(s)
	return amdc
}

// SetName sets the "name" field.
func (amdc *AiModelDetailCreate) SetName(s string) *AiModelDetailCreate {
	amdc.mutation.SetName(s)
	return amdc
}

// SetURL sets the "url" field.
func (amdc *AiModelDetailCreate) SetURL(s string) *AiModelDetailCreate {
	amdc.mutation.SetURL(s)
	return amdc
}

// SetAvatar sets the "avatar" field.
func (amdc *AiModelDetailCreate) SetAvatar(s string) *AiModelDetailCreate {
	amdc.mutation.SetAvatar(s)
	return amdc
}

// SetCanInternetSearch sets the "can_internet_search" field.
func (amdc *AiModelDetailCreate) SetCanInternetSearch(b bool) *AiModelDetailCreate {
	amdc.mutation.SetCanInternetSearch(b)
	return amdc
}

// SetBalanceSearchURL sets the "balance_search_url" field.
func (amdc *AiModelDetailCreate) SetBalanceSearchURL(s string) *AiModelDetailCreate {
	amdc.mutation.SetBalanceSearchURL(s)
	return amdc
}

// SetThinkingEnableStatus sets the "thinking_enable_status" field.
func (amdc *AiModelDetailCreate) SetThinkingEnableStatus(i int64) *AiModelDetailCreate {
	amdc.mutation.SetThinkingEnableStatus(i)
	return amdc
}

// SetNillableThinkingEnableStatus sets the "thinking_enable_status" field if the given value is not nil.
func (amdc *AiModelDetailCreate) SetNillableThinkingEnableStatus(i *int64) *AiModelDetailCreate {
	if i != nil {
		amdc.SetThinkingEnableStatus(*i)
	}
	return amdc
}

// SetBackgroundURL sets the "background_url" field.
func (amdc *AiModelDetailCreate) SetBackgroundURL(s string) *AiModelDetailCreate {
	amdc.mutation.SetBackgroundURL(s)
	return amdc
}

// SetID sets the "id" field.
func (amdc *AiModelDetailCreate) SetID(i int64) *AiModelDetailCreate {
	amdc.mutation.SetID(i)
	return amdc
}

// Mutation returns the AiModelDetailMutation object of the builder.
func (amdc *AiModelDetailCreate) Mutation() *AiModelDetailMutation {
	return amdc.mutation
}

// Save creates the AiModelDetail in the database.
func (amdc *AiModelDetailCreate) Save(ctx context.Context) (*AiModelDetail, error) {
	if err := amdc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, amdc.sqlSave, amdc.mutation, amdc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (amdc *AiModelDetailCreate) SaveX(ctx context.Context) *AiModelDetail {
	v, err := amdc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (amdc *AiModelDetailCreate) Exec(ctx context.Context) error {
	_, err := amdc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amdc *AiModelDetailCreate) ExecX(ctx context.Context) {
	if err := amdc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (amdc *AiModelDetailCreate) defaults() error {
	if _, ok := amdc.mutation.CreatedAt(); !ok {
		if aimodeldetail.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodeldetail.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := aimodeldetail.DefaultCreatedAt()
		amdc.mutation.SetCreatedAt(v)
	}
	if _, ok := amdc.mutation.UpdatedAt(); !ok {
		if aimodeldetail.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodeldetail.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aimodeldetail.DefaultUpdatedAt()
		amdc.mutation.SetUpdatedAt(v)
	}
	if _, ok := amdc.mutation.DeletedAt(); !ok {
		v := aimodeldetail.DefaultDeletedAt
		amdc.mutation.SetDeletedAt(v)
	}
	if _, ok := amdc.mutation.ThinkingEnableStatus(); !ok {
		v := aimodeldetail.DefaultThinkingEnableStatus
		amdc.mutation.SetThinkingEnableStatus(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (amdc *AiModelDetailCreate) check() error {
	if _, ok := amdc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AiModelDetail.created_at"`)}
	}
	if _, ok := amdc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AiModelDetail.updated_at"`)}
	}
	if _, ok := amdc.mutation.ModelName(); !ok {
		return &ValidationError{Name: "model_name", err: errors.New(`ent: missing required field "AiModelDetail.model_name"`)}
	}
	if _, ok := amdc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "AiModelDetail.name"`)}
	}
	if _, ok := amdc.mutation.URL(); !ok {
		return &ValidationError{Name: "url", err: errors.New(`ent: missing required field "AiModelDetail.url"`)}
	}
	if _, ok := amdc.mutation.Avatar(); !ok {
		return &ValidationError{Name: "avatar", err: errors.New(`ent: missing required field "AiModelDetail.avatar"`)}
	}
	if _, ok := amdc.mutation.CanInternetSearch(); !ok {
		return &ValidationError{Name: "can_internet_search", err: errors.New(`ent: missing required field "AiModelDetail.can_internet_search"`)}
	}
	if _, ok := amdc.mutation.BalanceSearchURL(); !ok {
		return &ValidationError{Name: "balance_search_url", err: errors.New(`ent: missing required field "AiModelDetail.balance_search_url"`)}
	}
	if _, ok := amdc.mutation.ThinkingEnableStatus(); !ok {
		return &ValidationError{Name: "thinking_enable_status", err: errors.New(`ent: missing required field "AiModelDetail.thinking_enable_status"`)}
	}
	if _, ok := amdc.mutation.BackgroundURL(); !ok {
		return &ValidationError{Name: "background_url", err: errors.New(`ent: missing required field "AiModelDetail.background_url"`)}
	}
	return nil
}

func (amdc *AiModelDetailCreate) sqlSave(ctx context.Context) (*AiModelDetail, error) {
	if err := amdc.check(); err != nil {
		return nil, err
	}
	_node, _spec := amdc.createSpec()
	if err := sqlgraph.CreateNode(ctx, amdc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	amdc.mutation.id = &_node.ID
	amdc.mutation.done = true
	return _node, nil
}

func (amdc *AiModelDetailCreate) createSpec() (*AiModelDetail, *sqlgraph.CreateSpec) {
	var (
		_node = &AiModelDetail{config: amdc.config}
		_spec = sqlgraph.NewCreateSpec(aimodeldetail.Table, sqlgraph.NewFieldSpec(aimodeldetail.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = amdc.conflict
	if id, ok := amdc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := amdc.mutation.CreatedAt(); ok {
		_spec.SetField(aimodeldetail.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := amdc.mutation.UpdatedAt(); ok {
		_spec.SetField(aimodeldetail.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := amdc.mutation.DeletedAt(); ok {
		_spec.SetField(aimodeldetail.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := amdc.mutation.ModelName(); ok {
		_spec.SetField(aimodeldetail.FieldModelName, field.TypeString, value)
		_node.ModelName = value
	}
	if value, ok := amdc.mutation.Name(); ok {
		_spec.SetField(aimodeldetail.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := amdc.mutation.URL(); ok {
		_spec.SetField(aimodeldetail.FieldURL, field.TypeString, value)
		_node.URL = value
	}
	if value, ok := amdc.mutation.Avatar(); ok {
		_spec.SetField(aimodeldetail.FieldAvatar, field.TypeString, value)
		_node.Avatar = value
	}
	if value, ok := amdc.mutation.CanInternetSearch(); ok {
		_spec.SetField(aimodeldetail.FieldCanInternetSearch, field.TypeBool, value)
		_node.CanInternetSearch = value
	}
	if value, ok := amdc.mutation.BalanceSearchURL(); ok {
		_spec.SetField(aimodeldetail.FieldBalanceSearchURL, field.TypeString, value)
		_node.BalanceSearchURL = value
	}
	if value, ok := amdc.mutation.ThinkingEnableStatus(); ok {
		_spec.SetField(aimodeldetail.FieldThinkingEnableStatus, field.TypeInt64, value)
		_node.ThinkingEnableStatus = value
	}
	if value, ok := amdc.mutation.BackgroundURL(); ok {
		_spec.SetField(aimodeldetail.FieldBackgroundURL, field.TypeString, value)
		_node.BackgroundURL = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiModelDetail.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiModelDetailUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (amdc *AiModelDetailCreate) OnConflict(opts ...sql.ConflictOption) *AiModelDetailUpsertOne {
	amdc.conflict = opts
	return &AiModelDetailUpsertOne{
		create: amdc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiModelDetail.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (amdc *AiModelDetailCreate) OnConflictColumns(columns ...string) *AiModelDetailUpsertOne {
	amdc.conflict = append(amdc.conflict, sql.ConflictColumns(columns...))
	return &AiModelDetailUpsertOne{
		create: amdc,
	}
}

type (
	// AiModelDetailUpsertOne is the builder for "upsert"-ing
	//  one AiModelDetail node.
	AiModelDetailUpsertOne struct {
		create *AiModelDetailCreate
	}

	// AiModelDetailUpsert is the "OnConflict" setter.
	AiModelDetailUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *AiModelDetailUpsert) SetUpdatedAt(v time.Time) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateUpdatedAt() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiModelDetailUpsert) SetDeletedAt(v time.Time) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateDeletedAt() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiModelDetailUpsert) ClearDeletedAt() *AiModelDetailUpsert {
	u.SetNull(aimodeldetail.FieldDeletedAt)
	return u
}

// SetModelName sets the "model_name" field.
func (u *AiModelDetailUpsert) SetModelName(v string) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldModelName, v)
	return u
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateModelName() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldModelName)
	return u
}

// SetName sets the "name" field.
func (u *AiModelDetailUpsert) SetName(v string) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateName() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldName)
	return u
}

// SetURL sets the "url" field.
func (u *AiModelDetailUpsert) SetURL(v string) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldURL, v)
	return u
}

// UpdateURL sets the "url" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateURL() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldURL)
	return u
}

// SetAvatar sets the "avatar" field.
func (u *AiModelDetailUpsert) SetAvatar(v string) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldAvatar, v)
	return u
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateAvatar() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldAvatar)
	return u
}

// SetCanInternetSearch sets the "can_internet_search" field.
func (u *AiModelDetailUpsert) SetCanInternetSearch(v bool) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldCanInternetSearch, v)
	return u
}

// UpdateCanInternetSearch sets the "can_internet_search" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateCanInternetSearch() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldCanInternetSearch)
	return u
}

// SetBalanceSearchURL sets the "balance_search_url" field.
func (u *AiModelDetailUpsert) SetBalanceSearchURL(v string) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldBalanceSearchURL, v)
	return u
}

// UpdateBalanceSearchURL sets the "balance_search_url" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateBalanceSearchURL() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldBalanceSearchURL)
	return u
}

// SetThinkingEnableStatus sets the "thinking_enable_status" field.
func (u *AiModelDetailUpsert) SetThinkingEnableStatus(v int64) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldThinkingEnableStatus, v)
	return u
}

// UpdateThinkingEnableStatus sets the "thinking_enable_status" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateThinkingEnableStatus() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldThinkingEnableStatus)
	return u
}

// AddThinkingEnableStatus adds v to the "thinking_enable_status" field.
func (u *AiModelDetailUpsert) AddThinkingEnableStatus(v int64) *AiModelDetailUpsert {
	u.Add(aimodeldetail.FieldThinkingEnableStatus, v)
	return u
}

// SetBackgroundURL sets the "background_url" field.
func (u *AiModelDetailUpsert) SetBackgroundURL(v string) *AiModelDetailUpsert {
	u.Set(aimodeldetail.FieldBackgroundURL, v)
	return u
}

// UpdateBackgroundURL sets the "background_url" field to the value that was provided on create.
func (u *AiModelDetailUpsert) UpdateBackgroundURL() *AiModelDetailUpsert {
	u.SetExcluded(aimodeldetail.FieldBackgroundURL)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AiModelDetail.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aimodeldetail.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiModelDetailUpsertOne) UpdateNewValues() *AiModelDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(aimodeldetail.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(aimodeldetail.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiModelDetail.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AiModelDetailUpsertOne) Ignore() *AiModelDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiModelDetailUpsertOne) DoNothing() *AiModelDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiModelDetailCreate.OnConflict
// documentation for more info.
func (u *AiModelDetailUpsertOne) Update(set func(*AiModelDetailUpsert)) *AiModelDetailUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiModelDetailUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiModelDetailUpsertOne) SetUpdatedAt(v time.Time) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateUpdatedAt() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiModelDetailUpsertOne) SetDeletedAt(v time.Time) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateDeletedAt() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiModelDetailUpsertOne) ClearDeletedAt() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.ClearDeletedAt()
	})
}

// SetModelName sets the "model_name" field.
func (u *AiModelDetailUpsertOne) SetModelName(v string) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetModelName(v)
	})
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateModelName() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateModelName()
	})
}

// SetName sets the "name" field.
func (u *AiModelDetailUpsertOne) SetName(v string) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateName() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateName()
	})
}

// SetURL sets the "url" field.
func (u *AiModelDetailUpsertOne) SetURL(v string) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetURL(v)
	})
}

// UpdateURL sets the "url" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateURL() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateURL()
	})
}

// SetAvatar sets the "avatar" field.
func (u *AiModelDetailUpsertOne) SetAvatar(v string) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetAvatar(v)
	})
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateAvatar() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateAvatar()
	})
}

// SetCanInternetSearch sets the "can_internet_search" field.
func (u *AiModelDetailUpsertOne) SetCanInternetSearch(v bool) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetCanInternetSearch(v)
	})
}

// UpdateCanInternetSearch sets the "can_internet_search" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateCanInternetSearch() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateCanInternetSearch()
	})
}

// SetBalanceSearchURL sets the "balance_search_url" field.
func (u *AiModelDetailUpsertOne) SetBalanceSearchURL(v string) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetBalanceSearchURL(v)
	})
}

// UpdateBalanceSearchURL sets the "balance_search_url" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateBalanceSearchURL() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateBalanceSearchURL()
	})
}

// SetThinkingEnableStatus sets the "thinking_enable_status" field.
func (u *AiModelDetailUpsertOne) SetThinkingEnableStatus(v int64) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetThinkingEnableStatus(v)
	})
}

// AddThinkingEnableStatus adds v to the "thinking_enable_status" field.
func (u *AiModelDetailUpsertOne) AddThinkingEnableStatus(v int64) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.AddThinkingEnableStatus(v)
	})
}

// UpdateThinkingEnableStatus sets the "thinking_enable_status" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateThinkingEnableStatus() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateThinkingEnableStatus()
	})
}

// SetBackgroundURL sets the "background_url" field.
func (u *AiModelDetailUpsertOne) SetBackgroundURL(v string) *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetBackgroundURL(v)
	})
}

// UpdateBackgroundURL sets the "background_url" field to the value that was provided on create.
func (u *AiModelDetailUpsertOne) UpdateBackgroundURL() *AiModelDetailUpsertOne {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateBackgroundURL()
	})
}

// Exec executes the query.
func (u *AiModelDetailUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiModelDetailCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiModelDetailUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AiModelDetailUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AiModelDetailUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AiModelDetailCreateBulk is the builder for creating many AiModelDetail entities in bulk.
type AiModelDetailCreateBulk struct {
	config
	err      error
	builders []*AiModelDetailCreate
	conflict []sql.ConflictOption
}

// Save creates the AiModelDetail entities in the database.
func (amdcb *AiModelDetailCreateBulk) Save(ctx context.Context) ([]*AiModelDetail, error) {
	if amdcb.err != nil {
		return nil, amdcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(amdcb.builders))
	nodes := make([]*AiModelDetail, len(amdcb.builders))
	mutators := make([]Mutator, len(amdcb.builders))
	for i := range amdcb.builders {
		func(i int, root context.Context) {
			builder := amdcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AiModelDetailMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, amdcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = amdcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, amdcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, amdcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (amdcb *AiModelDetailCreateBulk) SaveX(ctx context.Context) []*AiModelDetail {
	v, err := amdcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (amdcb *AiModelDetailCreateBulk) Exec(ctx context.Context) error {
	_, err := amdcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amdcb *AiModelDetailCreateBulk) ExecX(ctx context.Context) {
	if err := amdcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiModelDetail.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiModelDetailUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (amdcb *AiModelDetailCreateBulk) OnConflict(opts ...sql.ConflictOption) *AiModelDetailUpsertBulk {
	amdcb.conflict = opts
	return &AiModelDetailUpsertBulk{
		create: amdcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiModelDetail.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (amdcb *AiModelDetailCreateBulk) OnConflictColumns(columns ...string) *AiModelDetailUpsertBulk {
	amdcb.conflict = append(amdcb.conflict, sql.ConflictColumns(columns...))
	return &AiModelDetailUpsertBulk{
		create: amdcb,
	}
}

// AiModelDetailUpsertBulk is the builder for "upsert"-ing
// a bulk of AiModelDetail nodes.
type AiModelDetailUpsertBulk struct {
	create *AiModelDetailCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AiModelDetail.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aimodeldetail.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiModelDetailUpsertBulk) UpdateNewValues() *AiModelDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(aimodeldetail.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(aimodeldetail.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiModelDetail.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AiModelDetailUpsertBulk) Ignore() *AiModelDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiModelDetailUpsertBulk) DoNothing() *AiModelDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiModelDetailCreateBulk.OnConflict
// documentation for more info.
func (u *AiModelDetailUpsertBulk) Update(set func(*AiModelDetailUpsert)) *AiModelDetailUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiModelDetailUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiModelDetailUpsertBulk) SetUpdatedAt(v time.Time) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateUpdatedAt() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiModelDetailUpsertBulk) SetDeletedAt(v time.Time) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateDeletedAt() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiModelDetailUpsertBulk) ClearDeletedAt() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.ClearDeletedAt()
	})
}

// SetModelName sets the "model_name" field.
func (u *AiModelDetailUpsertBulk) SetModelName(v string) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetModelName(v)
	})
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateModelName() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateModelName()
	})
}

// SetName sets the "name" field.
func (u *AiModelDetailUpsertBulk) SetName(v string) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateName() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateName()
	})
}

// SetURL sets the "url" field.
func (u *AiModelDetailUpsertBulk) SetURL(v string) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetURL(v)
	})
}

// UpdateURL sets the "url" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateURL() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateURL()
	})
}

// SetAvatar sets the "avatar" field.
func (u *AiModelDetailUpsertBulk) SetAvatar(v string) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetAvatar(v)
	})
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateAvatar() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateAvatar()
	})
}

// SetCanInternetSearch sets the "can_internet_search" field.
func (u *AiModelDetailUpsertBulk) SetCanInternetSearch(v bool) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetCanInternetSearch(v)
	})
}

// UpdateCanInternetSearch sets the "can_internet_search" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateCanInternetSearch() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateCanInternetSearch()
	})
}

// SetBalanceSearchURL sets the "balance_search_url" field.
func (u *AiModelDetailUpsertBulk) SetBalanceSearchURL(v string) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetBalanceSearchURL(v)
	})
}

// UpdateBalanceSearchURL sets the "balance_search_url" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateBalanceSearchURL() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateBalanceSearchURL()
	})
}

// SetThinkingEnableStatus sets the "thinking_enable_status" field.
func (u *AiModelDetailUpsertBulk) SetThinkingEnableStatus(v int64) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetThinkingEnableStatus(v)
	})
}

// AddThinkingEnableStatus adds v to the "thinking_enable_status" field.
func (u *AiModelDetailUpsertBulk) AddThinkingEnableStatus(v int64) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.AddThinkingEnableStatus(v)
	})
}

// UpdateThinkingEnableStatus sets the "thinking_enable_status" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateThinkingEnableStatus() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateThinkingEnableStatus()
	})
}

// SetBackgroundURL sets the "background_url" field.
func (u *AiModelDetailUpsertBulk) SetBackgroundURL(v string) *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.SetBackgroundURL(v)
	})
}

// UpdateBackgroundURL sets the "background_url" field to the value that was provided on create.
func (u *AiModelDetailUpsertBulk) UpdateBackgroundURL() *AiModelDetailUpsertBulk {
	return u.Update(func(s *AiModelDetailUpsert) {
		s.UpdateBackgroundURL()
	})
}

// Exec executes the query.
func (u *AiModelDetailUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AiModelDetailCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiModelDetailCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiModelDetailUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

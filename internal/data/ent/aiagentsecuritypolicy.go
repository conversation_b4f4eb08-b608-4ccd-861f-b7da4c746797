// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritypolicy"
)

// AiAgentSecurityPolicy is the model entity for the AiAgentSecurityPolicy schema.
type AiAgentSecurityPolicy struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 策略名称
	Name string `json:"name,omitempty"`
	// agent id
	AgentID int64 `json:"agent_id,omitempty"`
	// 策略分类 1.敏感信息匹配
	PolicyCategory int64 `json:"policy_category,omitempty"`
	// 风险等级 1.低 2.中 3.高
	RiskLevel int64 `json:"risk_level,omitempty"`
	// 是否启用
	Enabled bool `json:"enabled,omitempty"`
	// 策略内容
	Policies *pq.StringArray `json:"policies,omitempty"`
	// 命中策略后的操作 1.阻断 2.警告
	HitAction int64 `json:"hit_action,omitempty"`
	// 命中策略后的响应内容
	HitResponse string `json:"hit_response,omitempty"`
	// 修改人id
	UpdatedBy    int64 `json:"updated_by,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AiAgentSecurityPolicy) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case aiagentsecuritypolicy.FieldPolicies:
			values[i] = new(pq.StringArray)
		case aiagentsecuritypolicy.FieldEnabled:
			values[i] = new(sql.NullBool)
		case aiagentsecuritypolicy.FieldID, aiagentsecuritypolicy.FieldAgentID, aiagentsecuritypolicy.FieldPolicyCategory, aiagentsecuritypolicy.FieldRiskLevel, aiagentsecuritypolicy.FieldHitAction, aiagentsecuritypolicy.FieldUpdatedBy:
			values[i] = new(sql.NullInt64)
		case aiagentsecuritypolicy.FieldName, aiagentsecuritypolicy.FieldHitResponse:
			values[i] = new(sql.NullString)
		case aiagentsecuritypolicy.FieldCreatedAt, aiagentsecuritypolicy.FieldUpdatedAt, aiagentsecuritypolicy.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AiAgentSecurityPolicy fields.
func (aasp *AiAgentSecurityPolicy) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case aiagentsecuritypolicy.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			aasp.ID = int64(value.Int64)
		case aiagentsecuritypolicy.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				aasp.CreatedAt = value.Time
			}
		case aiagentsecuritypolicy.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				aasp.UpdatedAt = value.Time
			}
		case aiagentsecuritypolicy.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				aasp.DeletedAt = value.Time
			}
		case aiagentsecuritypolicy.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				aasp.Name = value.String
			}
		case aiagentsecuritypolicy.FieldAgentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field agent_id", values[i])
			} else if value.Valid {
				aasp.AgentID = value.Int64
			}
		case aiagentsecuritypolicy.FieldPolicyCategory:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field policy_category", values[i])
			} else if value.Valid {
				aasp.PolicyCategory = value.Int64
			}
		case aiagentsecuritypolicy.FieldRiskLevel:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field risk_level", values[i])
			} else if value.Valid {
				aasp.RiskLevel = value.Int64
			}
		case aiagentsecuritypolicy.FieldEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field enabled", values[i])
			} else if value.Valid {
				aasp.Enabled = value.Bool
			}
		case aiagentsecuritypolicy.FieldPolicies:
			if value, ok := values[i].(*pq.StringArray); !ok {
				return fmt.Errorf("unexpected type %T for field policies", values[i])
			} else if value != nil {
				aasp.Policies = value
			}
		case aiagentsecuritypolicy.FieldHitAction:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field hit_action", values[i])
			} else if value.Valid {
				aasp.HitAction = value.Int64
			}
		case aiagentsecuritypolicy.FieldHitResponse:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field hit_response", values[i])
			} else if value.Valid {
				aasp.HitResponse = value.String
			}
		case aiagentsecuritypolicy.FieldUpdatedBy:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field updated_by", values[i])
			} else if value.Valid {
				aasp.UpdatedBy = value.Int64
			}
		default:
			aasp.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AiAgentSecurityPolicy.
// This includes values selected through modifiers, order, etc.
func (aasp *AiAgentSecurityPolicy) Value(name string) (ent.Value, error) {
	return aasp.selectValues.Get(name)
}

// Update returns a builder for updating this AiAgentSecurityPolicy.
// Note that you need to call AiAgentSecurityPolicy.Unwrap() before calling this method if this AiAgentSecurityPolicy
// was returned from a transaction, and the transaction was committed or rolled back.
func (aasp *AiAgentSecurityPolicy) Update() *AiAgentSecurityPolicyUpdateOne {
	return NewAiAgentSecurityPolicyClient(aasp.config).UpdateOne(aasp)
}

// Unwrap unwraps the AiAgentSecurityPolicy entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (aasp *AiAgentSecurityPolicy) Unwrap() *AiAgentSecurityPolicy {
	_tx, ok := aasp.config.driver.(*txDriver)
	if !ok {
		panic("ent: AiAgentSecurityPolicy is not a transactional entity")
	}
	aasp.config.driver = _tx.drv
	return aasp
}

// String implements the fmt.Stringer.
func (aasp *AiAgentSecurityPolicy) String() string {
	var builder strings.Builder
	builder.WriteString("AiAgentSecurityPolicy(")
	builder.WriteString(fmt.Sprintf("id=%v, ", aasp.ID))
	builder.WriteString("created_at=")
	builder.WriteString(aasp.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(aasp.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(aasp.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(aasp.Name)
	builder.WriteString(", ")
	builder.WriteString("agent_id=")
	builder.WriteString(fmt.Sprintf("%v", aasp.AgentID))
	builder.WriteString(", ")
	builder.WriteString("policy_category=")
	builder.WriteString(fmt.Sprintf("%v", aasp.PolicyCategory))
	builder.WriteString(", ")
	builder.WriteString("risk_level=")
	builder.WriteString(fmt.Sprintf("%v", aasp.RiskLevel))
	builder.WriteString(", ")
	builder.WriteString("enabled=")
	builder.WriteString(fmt.Sprintf("%v", aasp.Enabled))
	builder.WriteString(", ")
	builder.WriteString("policies=")
	builder.WriteString(fmt.Sprintf("%v", aasp.Policies))
	builder.WriteString(", ")
	builder.WriteString("hit_action=")
	builder.WriteString(fmt.Sprintf("%v", aasp.HitAction))
	builder.WriteString(", ")
	builder.WriteString("hit_response=")
	builder.WriteString(aasp.HitResponse)
	builder.WriteString(", ")
	builder.WriteString("updated_by=")
	builder.WriteString(fmt.Sprintf("%v", aasp.UpdatedBy))
	builder.WriteByte(')')
	return builder.String()
}

// AiAgentSecurityPolicies is a parsable slice of AiAgentSecurityPolicy.
type AiAgentSecurityPolicies []*AiAgentSecurityPolicy

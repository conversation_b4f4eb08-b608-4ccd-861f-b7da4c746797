{{/* Tell Intellij/GoLand to enable the autocompletion based on the *gen.Graph type. */}}
{{/* gotype: entgo.io/ent/entc/gen.Graph */}}

{{ define "Paginate" }}

{{/* Add the base header for the generated file */}}
{{ $pkg := base $.Config.Package }}
{{ template "header" $ }}

import (
    "context"
    "gitlab.minum.cloud/BackendTeam/pkg/middleware/page"
)

{{/* Loop over all nodes and add the Greet method */}}
{{ range $n := $.Nodes }}
    {{ $receiver := $n.Receiver }}
    {{ $client := print $n.Name "Query" }}
    // Paginate get page number and page size from context and return a query with offset and limit
    func ({{ $receiver }} *{{ $client }}) Paginate(ctx context.Context) *{{$n.Name}}Query {
        pageNum, pageSize := page.Extract(ctx)
        return {{ $receiver }}.Offset(int(pageSize) * (int(pageNum) - 1)).Limit(int(pageSize))
    }
{{ end }}

{{ end }}
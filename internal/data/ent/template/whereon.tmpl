{{/* Tell Intellij/GoLand to enable the autocompletion based on the *gen.Graph type. */}}
{{/* gotype: entgo.io/ent/entc/gen.Graph */}}

{{ define "whereon" }}

    {{/* Add the base header for the generated file */}}
    {{ $pkg := base $.Config.Package }}
    {{ template "header" $ }}

    import (
    "context"
    "gitlab.minum.cloud/BackendTeam/pkg/middleware/page"
    )

    {{/* Loop over all nodes and add the Greet method */}}
    {{ range $n := $.Nodes }}
        {{ $receiver := $n.Receiver }}
        {{ $client := print $n.Name "Query" }}
        // WhereOn applies the given predicates on the query.
        func ({{ $receiver }} *{{ $client }}) WhereOn(condition bool, ps ...predicate.{{$n.Name}}) *{{$n.Name}}Query {
        if !condition {
        return {{ $receiver }}
        }
        return {{ $receiver }}.Where(ps...)
        }
    {{ end }}

    {{ range $n := $.Nodes }}
        {{ $receiver := $n.Receiver }}
        {{ $client := print $n.Name "Update" }}
        // WhereOn applies the given predicates on the update.
        func ({{ $receiver }} *{{ $client }}) WhereOn(condition bool, ps ...predicate.{{$n.Name}}) *{{$n.Name}}Update {
        if !condition {
        return {{ $receiver }}
        }
        return {{ $receiver }}.Where(ps...)
        }
    {{ end }}

    {{ range $n := $.Nodes }}
        {{ $receiver := $n.Receiver }}
        {{ $client := print $n.Name "UpdateOne" }}
        // WhereOn applies the given predicates on the updateOne.
        func ({{ $receiver }} *{{ $client }}) WhereOn(condition bool, ps ...predicate.{{$n.Name}}) *{{$n.Name}}UpdateOne {
        if !condition {
        return {{ $receiver }}
        }
        return {{ $receiver }}.Where(ps...)
        }
    {{ end }}

    {{ range $n := $.Nodes }}
        {{ $receiver := $n.Receiver }}
        {{ $client := print $n.Name "Delete" }}
        // WhereOn applies the given predicates on the delete.
        func ({{ $receiver }} *{{ $client }}) WhereOn(condition bool, ps ...predicate.{{$n.Name}}) *{{$n.Name}}Delete {
        if !condition {
        return {{ $receiver }}
        }
        return {{ $receiver }}.Where(ps...)
        }
    {{ end }}

    {{ range $n := $.Nodes }}
        {{ $receiver := $n.Receiver }}
        {{ $client := print $n.Name "DeleteOne" }}
        // WhereOn applies the given predicates on the deleteOne.
        func ({{ $receiver }} *{{ $client }}) WhereOn(condition bool, ps ...predicate.{{$n.Name}}) *{{$n.Name}}DeleteOne {
        if !condition {
        return {{ $receiver }}
        }
        return {{ $receiver }}.Where(ps...)
        }
    {{ end }}

{{ end }}
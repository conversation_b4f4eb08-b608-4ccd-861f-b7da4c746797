{{/* Tell Intellij/GoLand to enable the autocompletion based on the *gen.Graph type. */}}
{{/* gotype: entgo.io/ent/entc/gen.Graph */}}

{{ define "countz" }}

    {{/* Add the base header for the generated file */}}
    {{ $pkg := base $.Config.Package }}
    {{ template "header" $ }}

    import (
    "context"
    "entgo.io/ent/dialect/sql"
    )

    {{/* Loop over all nodes and add the Greet method */}}
    {{ range $n := $.Nodes }}
        {{ $receiver := $n.Receiver }}
        {{ $client := print $n.Name "Query" }}
        // Countz use count(*)
        func ({{ $receiver }} *{{ $client }}) Countz(ctx context.Context)(int, error) {
            return {{ $receiver }}.Modify(func(s *sql.Selector) {
                s.Select("count(*)")
            }).Int(ctx)
        }
    {{ end }}


{{ end }}
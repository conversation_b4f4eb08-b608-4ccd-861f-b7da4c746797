// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichatitem"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiChatItemUpdate is the builder for updating AiChatItem entities.
type AiChatItemUpdate struct {
	config
	hooks     []Hook
	mutation  *AiChatItemMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AiChatItemUpdate builder.
func (aciu *AiChatItemUpdate) Where(ps ...predicate.AiChatItem) *AiChatItemUpdate {
	aciu.mutation.Where(ps...)
	return aciu
}

// SetTenantID sets the "tenant_id" field.
func (aciu *AiChatItemUpdate) SetTenantID(i int64) *AiChatItemUpdate {
	aciu.mutation.ResetTenantID()
	aciu.mutation.SetTenantID(i)
	return aciu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableTenantID(i *int64) *AiChatItemUpdate {
	if i != nil {
		aciu.SetTenantID(*i)
	}
	return aciu
}

// AddTenantID adds i to the "tenant_id" field.
func (aciu *AiChatItemUpdate) AddTenantID(i int64) *AiChatItemUpdate {
	aciu.mutation.AddTenantID(i)
	return aciu
}

// SetUpdatedAt sets the "updated_at" field.
func (aciu *AiChatItemUpdate) SetUpdatedAt(t time.Time) *AiChatItemUpdate {
	aciu.mutation.SetUpdatedAt(t)
	return aciu
}

// SetDeletedAt sets the "deleted_at" field.
func (aciu *AiChatItemUpdate) SetDeletedAt(t time.Time) *AiChatItemUpdate {
	aciu.mutation.SetDeletedAt(t)
	return aciu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableDeletedAt(t *time.Time) *AiChatItemUpdate {
	if t != nil {
		aciu.SetDeletedAt(*t)
	}
	return aciu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (aciu *AiChatItemUpdate) ClearDeletedAt() *AiChatItemUpdate {
	aciu.mutation.ClearDeletedAt()
	return aciu
}

// SetChatID sets the "chat_id" field.
func (aciu *AiChatItemUpdate) SetChatID(i int64) *AiChatItemUpdate {
	aciu.mutation.ResetChatID()
	aciu.mutation.SetChatID(i)
	return aciu
}

// SetNillableChatID sets the "chat_id" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableChatID(i *int64) *AiChatItemUpdate {
	if i != nil {
		aciu.SetChatID(*i)
	}
	return aciu
}

// AddChatID adds i to the "chat_id" field.
func (aciu *AiChatItemUpdate) AddChatID(i int64) *AiChatItemUpdate {
	aciu.mutation.AddChatID(i)
	return aciu
}

// SetObjectID sets the "object_id" field.
func (aciu *AiChatItemUpdate) SetObjectID(i int64) *AiChatItemUpdate {
	aciu.mutation.ResetObjectID()
	aciu.mutation.SetObjectID(i)
	return aciu
}

// SetNillableObjectID sets the "object_id" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableObjectID(i *int64) *AiChatItemUpdate {
	if i != nil {
		aciu.SetObjectID(*i)
	}
	return aciu
}

// AddObjectID adds i to the "object_id" field.
func (aciu *AiChatItemUpdate) AddObjectID(i int64) *AiChatItemUpdate {
	aciu.mutation.AddObjectID(i)
	return aciu
}

// SetObjectType sets the "object_type" field.
func (aciu *AiChatItemUpdate) SetObjectType(i int64) *AiChatItemUpdate {
	aciu.mutation.ResetObjectType()
	aciu.mutation.SetObjectType(i)
	return aciu
}

// SetNillableObjectType sets the "object_type" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableObjectType(i *int64) *AiChatItemUpdate {
	if i != nil {
		aciu.SetObjectType(*i)
	}
	return aciu
}

// AddObjectType adds i to the "object_type" field.
func (aciu *AiChatItemUpdate) AddObjectType(i int64) *AiChatItemUpdate {
	aciu.mutation.AddObjectType(i)
	return aciu
}

// SetMessage sets the "message" field.
func (aciu *AiChatItemUpdate) SetMessage(s string) *AiChatItemUpdate {
	aciu.mutation.SetMessage(s)
	return aciu
}

// SetNillableMessage sets the "message" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableMessage(s *string) *AiChatItemUpdate {
	if s != nil {
		aciu.SetMessage(*s)
	}
	return aciu
}

// SetRefFiles sets the "ref_files" field.
func (aciu *AiChatItemUpdate) SetRefFiles(s string) *AiChatItemUpdate {
	aciu.mutation.SetRefFiles(s)
	return aciu
}

// SetNillableRefFiles sets the "ref_files" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableRefFiles(s *string) *AiChatItemUpdate {
	if s != nil {
		aciu.SetRefFiles(*s)
	}
	return aciu
}

// SetAgreeStatus sets the "agree_status" field.
func (aciu *AiChatItemUpdate) SetAgreeStatus(i int8) *AiChatItemUpdate {
	aciu.mutation.ResetAgreeStatus()
	aciu.mutation.SetAgreeStatus(i)
	return aciu
}

// SetNillableAgreeStatus sets the "agree_status" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableAgreeStatus(i *int8) *AiChatItemUpdate {
	if i != nil {
		aciu.SetAgreeStatus(*i)
	}
	return aciu
}

// AddAgreeStatus adds i to the "agree_status" field.
func (aciu *AiChatItemUpdate) AddAgreeStatus(i int8) *AiChatItemUpdate {
	aciu.mutation.AddAgreeStatus(i)
	return aciu
}

// SetRoundID sets the "round_id" field.
func (aciu *AiChatItemUpdate) SetRoundID(i int64) *AiChatItemUpdate {
	aciu.mutation.ResetRoundID()
	aciu.mutation.SetRoundID(i)
	return aciu
}

// SetNillableRoundID sets the "round_id" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableRoundID(i *int64) *AiChatItemUpdate {
	if i != nil {
		aciu.SetRoundID(*i)
	}
	return aciu
}

// AddRoundID adds i to the "round_id" field.
func (aciu *AiChatItemUpdate) AddRoundID(i int64) *AiChatItemUpdate {
	aciu.mutation.AddRoundID(i)
	return aciu
}

// SetPcName sets the "pc_name" field.
func (aciu *AiChatItemUpdate) SetPcName(s string) *AiChatItemUpdate {
	aciu.mutation.SetPcName(s)
	return aciu
}

// SetNillablePcName sets the "pc_name" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillablePcName(s *string) *AiChatItemUpdate {
	if s != nil {
		aciu.SetPcName(*s)
	}
	return aciu
}

// SetReason sets the "reason" field.
func (aciu *AiChatItemUpdate) SetReason(s string) *AiChatItemUpdate {
	aciu.mutation.SetReason(s)
	return aciu
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableReason(s *string) *AiChatItemUpdate {
	if s != nil {
		aciu.SetReason(*s)
	}
	return aciu
}

// SetPrimaryClassification sets the "primary_classification" field.
func (aciu *AiChatItemUpdate) SetPrimaryClassification(s string) *AiChatItemUpdate {
	aciu.mutation.SetPrimaryClassification(s)
	return aciu
}

// SetNillablePrimaryClassification sets the "primary_classification" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillablePrimaryClassification(s *string) *AiChatItemUpdate {
	if s != nil {
		aciu.SetPrimaryClassification(*s)
	}
	return aciu
}

// SetSecondaryClassification sets the "secondary_classification" field.
func (aciu *AiChatItemUpdate) SetSecondaryClassification(s string) *AiChatItemUpdate {
	aciu.mutation.SetSecondaryClassification(s)
	return aciu
}

// SetNillableSecondaryClassification sets the "secondary_classification" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableSecondaryClassification(s *string) *AiChatItemUpdate {
	if s != nil {
		aciu.SetSecondaryClassification(*s)
	}
	return aciu
}

// SetMineTypes sets the "mine_types" field.
func (aciu *AiChatItemUpdate) SetMineTypes(pa *pq.StringArray) *AiChatItemUpdate {
	aciu.mutation.SetMineTypes(pa)
	return aciu
}

// SetSuggestQuestions sets the "suggest_questions" field.
func (aciu *AiChatItemUpdate) SetSuggestQuestions(pa *pq.StringArray) *AiChatItemUpdate {
	aciu.mutation.SetSuggestQuestions(pa)
	return aciu
}

// SetHitAction sets the "hit_action" field.
func (aciu *AiChatItemUpdate) SetHitAction(i int64) *AiChatItemUpdate {
	aciu.mutation.ResetHitAction()
	aciu.mutation.SetHitAction(i)
	return aciu
}

// SetNillableHitAction sets the "hit_action" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableHitAction(i *int64) *AiChatItemUpdate {
	if i != nil {
		aciu.SetHitAction(*i)
	}
	return aciu
}

// AddHitAction adds i to the "hit_action" field.
func (aciu *AiChatItemUpdate) AddHitAction(i int64) *AiChatItemUpdate {
	aciu.mutation.AddHitAction(i)
	return aciu
}

// SetHitResponse sets the "hit_response" field.
func (aciu *AiChatItemUpdate) SetHitResponse(s string) *AiChatItemUpdate {
	aciu.mutation.SetHitResponse(s)
	return aciu
}

// SetNillableHitResponse sets the "hit_response" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableHitResponse(s *string) *AiChatItemUpdate {
	if s != nil {
		aciu.SetHitResponse(*s)
	}
	return aciu
}

// SetHitContinueSend sets the "hit_continue_send" field.
func (aciu *AiChatItemUpdate) SetHitContinueSend(b bool) *AiChatItemUpdate {
	aciu.mutation.SetHitContinueSend(b)
	return aciu
}

// SetNillableHitContinueSend sets the "hit_continue_send" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableHitContinueSend(b *bool) *AiChatItemUpdate {
	if b != nil {
		aciu.SetHitContinueSend(*b)
	}
	return aciu
}

// SetIsInternetSearch sets the "is_internet_search" field.
func (aciu *AiChatItemUpdate) SetIsInternetSearch(b bool) *AiChatItemUpdate {
	aciu.mutation.SetIsInternetSearch(b)
	return aciu
}

// SetNillableIsInternetSearch sets the "is_internet_search" field if the given value is not nil.
func (aciu *AiChatItemUpdate) SetNillableIsInternetSearch(b *bool) *AiChatItemUpdate {
	if b != nil {
		aciu.SetIsInternetSearch(*b)
	}
	return aciu
}

// Mutation returns the AiChatItemMutation object of the builder.
func (aciu *AiChatItemUpdate) Mutation() *AiChatItemMutation {
	return aciu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (aciu *AiChatItemUpdate) Save(ctx context.Context) (int, error) {
	if err := aciu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, aciu.sqlSave, aciu.mutation, aciu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aciu *AiChatItemUpdate) SaveX(ctx context.Context) int {
	affected, err := aciu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (aciu *AiChatItemUpdate) Exec(ctx context.Context) error {
	_, err := aciu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aciu *AiChatItemUpdate) ExecX(ctx context.Context) {
	if err := aciu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aciu *AiChatItemUpdate) defaults() error {
	if _, ok := aciu.mutation.UpdatedAt(); !ok {
		if aichatitem.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aichatitem.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aichatitem.UpdateDefaultUpdatedAt()
		aciu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aciu *AiChatItemUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiChatItemUpdate {
	aciu.modifiers = append(aciu.modifiers, modifiers...)
	return aciu
}

func (aciu *AiChatItemUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(aichatitem.Table, aichatitem.Columns, sqlgraph.NewFieldSpec(aichatitem.FieldID, field.TypeInt64))
	if ps := aciu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aciu.mutation.TenantID(); ok {
		_spec.SetField(aichatitem.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.AddedTenantID(); ok {
		_spec.AddField(aichatitem.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.UpdatedAt(); ok {
		_spec.SetField(aichatitem.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aciu.mutation.DeletedAt(); ok {
		_spec.SetField(aichatitem.FieldDeletedAt, field.TypeTime, value)
	}
	if aciu.mutation.DeletedAtCleared() {
		_spec.ClearField(aichatitem.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := aciu.mutation.ChatID(); ok {
		_spec.SetField(aichatitem.FieldChatID, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.AddedChatID(); ok {
		_spec.AddField(aichatitem.FieldChatID, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.ObjectID(); ok {
		_spec.SetField(aichatitem.FieldObjectID, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.AddedObjectID(); ok {
		_spec.AddField(aichatitem.FieldObjectID, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.ObjectType(); ok {
		_spec.SetField(aichatitem.FieldObjectType, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.AddedObjectType(); ok {
		_spec.AddField(aichatitem.FieldObjectType, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.Message(); ok {
		_spec.SetField(aichatitem.FieldMessage, field.TypeString, value)
	}
	if value, ok := aciu.mutation.RefFiles(); ok {
		_spec.SetField(aichatitem.FieldRefFiles, field.TypeString, value)
	}
	if value, ok := aciu.mutation.AgreeStatus(); ok {
		_spec.SetField(aichatitem.FieldAgreeStatus, field.TypeInt8, value)
	}
	if value, ok := aciu.mutation.AddedAgreeStatus(); ok {
		_spec.AddField(aichatitem.FieldAgreeStatus, field.TypeInt8, value)
	}
	if value, ok := aciu.mutation.RoundID(); ok {
		_spec.SetField(aichatitem.FieldRoundID, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.AddedRoundID(); ok {
		_spec.AddField(aichatitem.FieldRoundID, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.PcName(); ok {
		_spec.SetField(aichatitem.FieldPcName, field.TypeString, value)
	}
	if value, ok := aciu.mutation.Reason(); ok {
		_spec.SetField(aichatitem.FieldReason, field.TypeString, value)
	}
	if value, ok := aciu.mutation.PrimaryClassification(); ok {
		_spec.SetField(aichatitem.FieldPrimaryClassification, field.TypeString, value)
	}
	if value, ok := aciu.mutation.SecondaryClassification(); ok {
		_spec.SetField(aichatitem.FieldSecondaryClassification, field.TypeString, value)
	}
	if value, ok := aciu.mutation.MineTypes(); ok {
		_spec.SetField(aichatitem.FieldMineTypes, field.TypeOther, value)
	}
	if value, ok := aciu.mutation.SuggestQuestions(); ok {
		_spec.SetField(aichatitem.FieldSuggestQuestions, field.TypeOther, value)
	}
	if value, ok := aciu.mutation.HitAction(); ok {
		_spec.SetField(aichatitem.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.AddedHitAction(); ok {
		_spec.AddField(aichatitem.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aciu.mutation.HitResponse(); ok {
		_spec.SetField(aichatitem.FieldHitResponse, field.TypeString, value)
	}
	if value, ok := aciu.mutation.HitContinueSend(); ok {
		_spec.SetField(aichatitem.FieldHitContinueSend, field.TypeBool, value)
	}
	if value, ok := aciu.mutation.IsInternetSearch(); ok {
		_spec.SetField(aichatitem.FieldIsInternetSearch, field.TypeBool, value)
	}
	_spec.AddModifiers(aciu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, aciu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aichatitem.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	aciu.mutation.done = true
	return n, nil
}

// AiChatItemUpdateOne is the builder for updating a single AiChatItem entity.
type AiChatItemUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AiChatItemMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetTenantID sets the "tenant_id" field.
func (aciuo *AiChatItemUpdateOne) SetTenantID(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.ResetTenantID()
	aciuo.mutation.SetTenantID(i)
	return aciuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableTenantID(i *int64) *AiChatItemUpdateOne {
	if i != nil {
		aciuo.SetTenantID(*i)
	}
	return aciuo
}

// AddTenantID adds i to the "tenant_id" field.
func (aciuo *AiChatItemUpdateOne) AddTenantID(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.AddTenantID(i)
	return aciuo
}

// SetUpdatedAt sets the "updated_at" field.
func (aciuo *AiChatItemUpdateOne) SetUpdatedAt(t time.Time) *AiChatItemUpdateOne {
	aciuo.mutation.SetUpdatedAt(t)
	return aciuo
}

// SetDeletedAt sets the "deleted_at" field.
func (aciuo *AiChatItemUpdateOne) SetDeletedAt(t time.Time) *AiChatItemUpdateOne {
	aciuo.mutation.SetDeletedAt(t)
	return aciuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableDeletedAt(t *time.Time) *AiChatItemUpdateOne {
	if t != nil {
		aciuo.SetDeletedAt(*t)
	}
	return aciuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (aciuo *AiChatItemUpdateOne) ClearDeletedAt() *AiChatItemUpdateOne {
	aciuo.mutation.ClearDeletedAt()
	return aciuo
}

// SetChatID sets the "chat_id" field.
func (aciuo *AiChatItemUpdateOne) SetChatID(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.ResetChatID()
	aciuo.mutation.SetChatID(i)
	return aciuo
}

// SetNillableChatID sets the "chat_id" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableChatID(i *int64) *AiChatItemUpdateOne {
	if i != nil {
		aciuo.SetChatID(*i)
	}
	return aciuo
}

// AddChatID adds i to the "chat_id" field.
func (aciuo *AiChatItemUpdateOne) AddChatID(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.AddChatID(i)
	return aciuo
}

// SetObjectID sets the "object_id" field.
func (aciuo *AiChatItemUpdateOne) SetObjectID(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.ResetObjectID()
	aciuo.mutation.SetObjectID(i)
	return aciuo
}

// SetNillableObjectID sets the "object_id" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableObjectID(i *int64) *AiChatItemUpdateOne {
	if i != nil {
		aciuo.SetObjectID(*i)
	}
	return aciuo
}

// AddObjectID adds i to the "object_id" field.
func (aciuo *AiChatItemUpdateOne) AddObjectID(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.AddObjectID(i)
	return aciuo
}

// SetObjectType sets the "object_type" field.
func (aciuo *AiChatItemUpdateOne) SetObjectType(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.ResetObjectType()
	aciuo.mutation.SetObjectType(i)
	return aciuo
}

// SetNillableObjectType sets the "object_type" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableObjectType(i *int64) *AiChatItemUpdateOne {
	if i != nil {
		aciuo.SetObjectType(*i)
	}
	return aciuo
}

// AddObjectType adds i to the "object_type" field.
func (aciuo *AiChatItemUpdateOne) AddObjectType(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.AddObjectType(i)
	return aciuo
}

// SetMessage sets the "message" field.
func (aciuo *AiChatItemUpdateOne) SetMessage(s string) *AiChatItemUpdateOne {
	aciuo.mutation.SetMessage(s)
	return aciuo
}

// SetNillableMessage sets the "message" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableMessage(s *string) *AiChatItemUpdateOne {
	if s != nil {
		aciuo.SetMessage(*s)
	}
	return aciuo
}

// SetRefFiles sets the "ref_files" field.
func (aciuo *AiChatItemUpdateOne) SetRefFiles(s string) *AiChatItemUpdateOne {
	aciuo.mutation.SetRefFiles(s)
	return aciuo
}

// SetNillableRefFiles sets the "ref_files" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableRefFiles(s *string) *AiChatItemUpdateOne {
	if s != nil {
		aciuo.SetRefFiles(*s)
	}
	return aciuo
}

// SetAgreeStatus sets the "agree_status" field.
func (aciuo *AiChatItemUpdateOne) SetAgreeStatus(i int8) *AiChatItemUpdateOne {
	aciuo.mutation.ResetAgreeStatus()
	aciuo.mutation.SetAgreeStatus(i)
	return aciuo
}

// SetNillableAgreeStatus sets the "agree_status" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableAgreeStatus(i *int8) *AiChatItemUpdateOne {
	if i != nil {
		aciuo.SetAgreeStatus(*i)
	}
	return aciuo
}

// AddAgreeStatus adds i to the "agree_status" field.
func (aciuo *AiChatItemUpdateOne) AddAgreeStatus(i int8) *AiChatItemUpdateOne {
	aciuo.mutation.AddAgreeStatus(i)
	return aciuo
}

// SetRoundID sets the "round_id" field.
func (aciuo *AiChatItemUpdateOne) SetRoundID(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.ResetRoundID()
	aciuo.mutation.SetRoundID(i)
	return aciuo
}

// SetNillableRoundID sets the "round_id" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableRoundID(i *int64) *AiChatItemUpdateOne {
	if i != nil {
		aciuo.SetRoundID(*i)
	}
	return aciuo
}

// AddRoundID adds i to the "round_id" field.
func (aciuo *AiChatItemUpdateOne) AddRoundID(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.AddRoundID(i)
	return aciuo
}

// SetPcName sets the "pc_name" field.
func (aciuo *AiChatItemUpdateOne) SetPcName(s string) *AiChatItemUpdateOne {
	aciuo.mutation.SetPcName(s)
	return aciuo
}

// SetNillablePcName sets the "pc_name" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillablePcName(s *string) *AiChatItemUpdateOne {
	if s != nil {
		aciuo.SetPcName(*s)
	}
	return aciuo
}

// SetReason sets the "reason" field.
func (aciuo *AiChatItemUpdateOne) SetReason(s string) *AiChatItemUpdateOne {
	aciuo.mutation.SetReason(s)
	return aciuo
}

// SetNillableReason sets the "reason" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableReason(s *string) *AiChatItemUpdateOne {
	if s != nil {
		aciuo.SetReason(*s)
	}
	return aciuo
}

// SetPrimaryClassification sets the "primary_classification" field.
func (aciuo *AiChatItemUpdateOne) SetPrimaryClassification(s string) *AiChatItemUpdateOne {
	aciuo.mutation.SetPrimaryClassification(s)
	return aciuo
}

// SetNillablePrimaryClassification sets the "primary_classification" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillablePrimaryClassification(s *string) *AiChatItemUpdateOne {
	if s != nil {
		aciuo.SetPrimaryClassification(*s)
	}
	return aciuo
}

// SetSecondaryClassification sets the "secondary_classification" field.
func (aciuo *AiChatItemUpdateOne) SetSecondaryClassification(s string) *AiChatItemUpdateOne {
	aciuo.mutation.SetSecondaryClassification(s)
	return aciuo
}

// SetNillableSecondaryClassification sets the "secondary_classification" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableSecondaryClassification(s *string) *AiChatItemUpdateOne {
	if s != nil {
		aciuo.SetSecondaryClassification(*s)
	}
	return aciuo
}

// SetMineTypes sets the "mine_types" field.
func (aciuo *AiChatItemUpdateOne) SetMineTypes(pa *pq.StringArray) *AiChatItemUpdateOne {
	aciuo.mutation.SetMineTypes(pa)
	return aciuo
}

// SetSuggestQuestions sets the "suggest_questions" field.
func (aciuo *AiChatItemUpdateOne) SetSuggestQuestions(pa *pq.StringArray) *AiChatItemUpdateOne {
	aciuo.mutation.SetSuggestQuestions(pa)
	return aciuo
}

// SetHitAction sets the "hit_action" field.
func (aciuo *AiChatItemUpdateOne) SetHitAction(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.ResetHitAction()
	aciuo.mutation.SetHitAction(i)
	return aciuo
}

// SetNillableHitAction sets the "hit_action" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableHitAction(i *int64) *AiChatItemUpdateOne {
	if i != nil {
		aciuo.SetHitAction(*i)
	}
	return aciuo
}

// AddHitAction adds i to the "hit_action" field.
func (aciuo *AiChatItemUpdateOne) AddHitAction(i int64) *AiChatItemUpdateOne {
	aciuo.mutation.AddHitAction(i)
	return aciuo
}

// SetHitResponse sets the "hit_response" field.
func (aciuo *AiChatItemUpdateOne) SetHitResponse(s string) *AiChatItemUpdateOne {
	aciuo.mutation.SetHitResponse(s)
	return aciuo
}

// SetNillableHitResponse sets the "hit_response" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableHitResponse(s *string) *AiChatItemUpdateOne {
	if s != nil {
		aciuo.SetHitResponse(*s)
	}
	return aciuo
}

// SetHitContinueSend sets the "hit_continue_send" field.
func (aciuo *AiChatItemUpdateOne) SetHitContinueSend(b bool) *AiChatItemUpdateOne {
	aciuo.mutation.SetHitContinueSend(b)
	return aciuo
}

// SetNillableHitContinueSend sets the "hit_continue_send" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableHitContinueSend(b *bool) *AiChatItemUpdateOne {
	if b != nil {
		aciuo.SetHitContinueSend(*b)
	}
	return aciuo
}

// SetIsInternetSearch sets the "is_internet_search" field.
func (aciuo *AiChatItemUpdateOne) SetIsInternetSearch(b bool) *AiChatItemUpdateOne {
	aciuo.mutation.SetIsInternetSearch(b)
	return aciuo
}

// SetNillableIsInternetSearch sets the "is_internet_search" field if the given value is not nil.
func (aciuo *AiChatItemUpdateOne) SetNillableIsInternetSearch(b *bool) *AiChatItemUpdateOne {
	if b != nil {
		aciuo.SetIsInternetSearch(*b)
	}
	return aciuo
}

// Mutation returns the AiChatItemMutation object of the builder.
func (aciuo *AiChatItemUpdateOne) Mutation() *AiChatItemMutation {
	return aciuo.mutation
}

// Where appends a list predicates to the AiChatItemUpdate builder.
func (aciuo *AiChatItemUpdateOne) Where(ps ...predicate.AiChatItem) *AiChatItemUpdateOne {
	aciuo.mutation.Where(ps...)
	return aciuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (aciuo *AiChatItemUpdateOne) Select(field string, fields ...string) *AiChatItemUpdateOne {
	aciuo.fields = append([]string{field}, fields...)
	return aciuo
}

// Save executes the query and returns the updated AiChatItem entity.
func (aciuo *AiChatItemUpdateOne) Save(ctx context.Context) (*AiChatItem, error) {
	if err := aciuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, aciuo.sqlSave, aciuo.mutation, aciuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aciuo *AiChatItemUpdateOne) SaveX(ctx context.Context) *AiChatItem {
	node, err := aciuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (aciuo *AiChatItemUpdateOne) Exec(ctx context.Context) error {
	_, err := aciuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aciuo *AiChatItemUpdateOne) ExecX(ctx context.Context) {
	if err := aciuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aciuo *AiChatItemUpdateOne) defaults() error {
	if _, ok := aciuo.mutation.UpdatedAt(); !ok {
		if aichatitem.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aichatitem.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aichatitem.UpdateDefaultUpdatedAt()
		aciuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aciuo *AiChatItemUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiChatItemUpdateOne {
	aciuo.modifiers = append(aciuo.modifiers, modifiers...)
	return aciuo
}

func (aciuo *AiChatItemUpdateOne) sqlSave(ctx context.Context) (_node *AiChatItem, err error) {
	_spec := sqlgraph.NewUpdateSpec(aichatitem.Table, aichatitem.Columns, sqlgraph.NewFieldSpec(aichatitem.FieldID, field.TypeInt64))
	id, ok := aciuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AiChatItem.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := aciuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aichatitem.FieldID)
		for _, f := range fields {
			if !aichatitem.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != aichatitem.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := aciuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aciuo.mutation.TenantID(); ok {
		_spec.SetField(aichatitem.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.AddedTenantID(); ok {
		_spec.AddField(aichatitem.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.UpdatedAt(); ok {
		_spec.SetField(aichatitem.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aciuo.mutation.DeletedAt(); ok {
		_spec.SetField(aichatitem.FieldDeletedAt, field.TypeTime, value)
	}
	if aciuo.mutation.DeletedAtCleared() {
		_spec.ClearField(aichatitem.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := aciuo.mutation.ChatID(); ok {
		_spec.SetField(aichatitem.FieldChatID, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.AddedChatID(); ok {
		_spec.AddField(aichatitem.FieldChatID, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.ObjectID(); ok {
		_spec.SetField(aichatitem.FieldObjectID, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.AddedObjectID(); ok {
		_spec.AddField(aichatitem.FieldObjectID, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.ObjectType(); ok {
		_spec.SetField(aichatitem.FieldObjectType, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.AddedObjectType(); ok {
		_spec.AddField(aichatitem.FieldObjectType, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.Message(); ok {
		_spec.SetField(aichatitem.FieldMessage, field.TypeString, value)
	}
	if value, ok := aciuo.mutation.RefFiles(); ok {
		_spec.SetField(aichatitem.FieldRefFiles, field.TypeString, value)
	}
	if value, ok := aciuo.mutation.AgreeStatus(); ok {
		_spec.SetField(aichatitem.FieldAgreeStatus, field.TypeInt8, value)
	}
	if value, ok := aciuo.mutation.AddedAgreeStatus(); ok {
		_spec.AddField(aichatitem.FieldAgreeStatus, field.TypeInt8, value)
	}
	if value, ok := aciuo.mutation.RoundID(); ok {
		_spec.SetField(aichatitem.FieldRoundID, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.AddedRoundID(); ok {
		_spec.AddField(aichatitem.FieldRoundID, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.PcName(); ok {
		_spec.SetField(aichatitem.FieldPcName, field.TypeString, value)
	}
	if value, ok := aciuo.mutation.Reason(); ok {
		_spec.SetField(aichatitem.FieldReason, field.TypeString, value)
	}
	if value, ok := aciuo.mutation.PrimaryClassification(); ok {
		_spec.SetField(aichatitem.FieldPrimaryClassification, field.TypeString, value)
	}
	if value, ok := aciuo.mutation.SecondaryClassification(); ok {
		_spec.SetField(aichatitem.FieldSecondaryClassification, field.TypeString, value)
	}
	if value, ok := aciuo.mutation.MineTypes(); ok {
		_spec.SetField(aichatitem.FieldMineTypes, field.TypeOther, value)
	}
	if value, ok := aciuo.mutation.SuggestQuestions(); ok {
		_spec.SetField(aichatitem.FieldSuggestQuestions, field.TypeOther, value)
	}
	if value, ok := aciuo.mutation.HitAction(); ok {
		_spec.SetField(aichatitem.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.AddedHitAction(); ok {
		_spec.AddField(aichatitem.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aciuo.mutation.HitResponse(); ok {
		_spec.SetField(aichatitem.FieldHitResponse, field.TypeString, value)
	}
	if value, ok := aciuo.mutation.HitContinueSend(); ok {
		_spec.SetField(aichatitem.FieldHitContinueSend, field.TypeBool, value)
	}
	if value, ok := aciuo.mutation.IsInternetSearch(); ok {
		_spec.SetField(aichatitem.FieldIsInternetSearch, field.TypeBool, value)
	}
	_spec.AddModifiers(aciuo.modifiers...)
	_node = &AiChatItem{config: aciuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, aciuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aichatitem.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	aciuo.mutation.done = true
	return _node, nil
}

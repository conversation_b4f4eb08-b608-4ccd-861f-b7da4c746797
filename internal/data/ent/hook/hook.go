// Code generated by ent, DO NOT EDIT.

package hook

import (
	"context"
	"fmt"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

// The AiAgentFunc type is an adapter to allow the use of ordinary
// function as AiAgent mutator.
type AiAgentFunc func(context.Context, *ent.AiAgentMutation) (ent.Value, error)

// Mu<PERSON> calls f(ctx, m).
func (f AiAgentFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AiAgentMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AiAgentMutation", m)
}

// The AiAgentSecurityLogFunc type is an adapter to allow the use of ordinary
// function as AiAgentSecurityLog mutator.
type AiAgentSecurityLogFunc func(context.Context, *ent.AiAgentSecurityLogMutation) (ent.Value, error)

// Mu<PERSON> calls f(ctx, m).
func (f AiAgentSecurityLogFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AiAgentSecurityLogMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AiAgentSecurityLogMutation", m)
}

// The AiAgentSecurityPolicyFunc type is an adapter to allow the use of ordinary
// function as AiAgentSecurityPolicy mutator.
type AiAgentSecurityPolicyFunc func(context.Context, *ent.AiAgentSecurityPolicyMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AiAgentSecurityPolicyFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AiAgentSecurityPolicyMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AiAgentSecurityPolicyMutation", m)
}

// The AiChatFunc type is an adapter to allow the use of ordinary
// function as AiChat mutator.
type AiChatFunc func(context.Context, *ent.AiChatMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AiChatFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AiChatMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AiChatMutation", m)
}

// The AiChatItemFunc type is an adapter to allow the use of ordinary
// function as AiChatItem mutator.
type AiChatItemFunc func(context.Context, *ent.AiChatItemMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AiChatItemFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AiChatItemMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AiChatItemMutation", m)
}

// The AiModelFunc type is an adapter to allow the use of ordinary
// function as AiModel mutator.
type AiModelFunc func(context.Context, *ent.AiModelMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AiModelFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AiModelMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AiModelMutation", m)
}

// The AiModelDetailFunc type is an adapter to allow the use of ordinary
// function as AiModelDetail mutator.
type AiModelDetailFunc func(context.Context, *ent.AiModelDetailMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AiModelDetailFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AiModelDetailMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AiModelDetailMutation", m)
}

// The AiModelUsageFunc type is an adapter to allow the use of ordinary
// function as AiModelUsage mutator.
type AiModelUsageFunc func(context.Context, *ent.AiModelUsageMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AiModelUsageFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AiModelUsageMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AiModelUsageMutation", m)
}

// The AtomicQuestionsFunc type is an adapter to allow the use of ordinary
// function as AtomicQuestions mutator.
type AtomicQuestionsFunc func(context.Context, *ent.AtomicQuestionsMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f AtomicQuestionsFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.AtomicQuestionsMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.AtomicQuestionsMutation", m)
}

// The ClassificationFilesFunc type is an adapter to allow the use of ordinary
// function as ClassificationFiles mutator.
type ClassificationFilesFunc func(context.Context, *ent.ClassificationFilesMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f ClassificationFilesFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.ClassificationFilesMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.ClassificationFilesMutation", m)
}

// The DefaultAgentAvatarFunc type is an adapter to allow the use of ordinary
// function as DefaultAgentAvatar mutator.
type DefaultAgentAvatarFunc func(context.Context, *ent.DefaultAgentAvatarMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f DefaultAgentAvatarFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.DefaultAgentAvatarMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.DefaultAgentAvatarMutation", m)
}

// The ExternalModelUsageFunc type is an adapter to allow the use of ordinary
// function as ExternalModelUsage mutator.
type ExternalModelUsageFunc func(context.Context, *ent.ExternalModelUsageMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f ExternalModelUsageFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.ExternalModelUsageMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.ExternalModelUsageMutation", m)
}

// The KnowledgeBaseFunc type is an adapter to allow the use of ordinary
// function as KnowledgeBase mutator.
type KnowledgeBaseFunc func(context.Context, *ent.KnowledgeBaseMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f KnowledgeBaseFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.KnowledgeBaseMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.KnowledgeBaseMutation", m)
}

// The KnowledgeBaseFileFunc type is an adapter to allow the use of ordinary
// function as KnowledgeBaseFile mutator.
type KnowledgeBaseFileFunc func(context.Context, *ent.KnowledgeBaseFileMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f KnowledgeBaseFileFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.KnowledgeBaseFileMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.KnowledgeBaseFileMutation", m)
}

// The UserAgentOrderFunc type is an adapter to allow the use of ordinary
// function as UserAgentOrder mutator.
type UserAgentOrderFunc func(context.Context, *ent.UserAgentOrderMutation) (ent.Value, error)

// Mutate calls f(ctx, m).
func (f UserAgentOrderFunc) Mutate(ctx context.Context, m ent.Mutation) (ent.Value, error) {
	if mv, ok := m.(*ent.UserAgentOrderMutation); ok {
		return f(ctx, mv)
	}
	return nil, fmt.Errorf("unexpected mutation type %T. expect *ent.UserAgentOrderMutation", m)
}

// Condition is a hook condition function.
type Condition func(context.Context, ent.Mutation) bool

// And groups conditions with the AND operator.
func And(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		if !first(ctx, m) || !second(ctx, m) {
			return false
		}
		for _, cond := range rest {
			if !cond(ctx, m) {
				return false
			}
		}
		return true
	}
}

// Or groups conditions with the OR operator.
func Or(first, second Condition, rest ...Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		if first(ctx, m) || second(ctx, m) {
			return true
		}
		for _, cond := range rest {
			if cond(ctx, m) {
				return true
			}
		}
		return false
	}
}

// Not negates a given condition.
func Not(cond Condition) Condition {
	return func(ctx context.Context, m ent.Mutation) bool {
		return !cond(ctx, m)
	}
}

// HasOp is a condition testing mutation operation.
func HasOp(op ent.Op) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		return m.Op().Is(op)
	}
}

// HasAddedFields is a condition validating `.AddedField` on fields.
func HasAddedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if _, exists := m.AddedField(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.AddedField(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasClearedFields is a condition validating `.FieldCleared` on fields.
func HasClearedFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if exists := m.FieldCleared(field); !exists {
			return false
		}
		for _, field := range fields {
			if exists := m.FieldCleared(field); !exists {
				return false
			}
		}
		return true
	}
}

// HasFields is a condition validating `.Field` on fields.
func HasFields(field string, fields ...string) Condition {
	return func(_ context.Context, m ent.Mutation) bool {
		if _, exists := m.Field(field); !exists {
			return false
		}
		for _, field := range fields {
			if _, exists := m.Field(field); !exists {
				return false
			}
		}
		return true
	}
}

// If executes the given hook under condition.
//
//	hook.If(ComputeAverage, And(HasFields(...), HasAddedFields(...)))
func If(hk ent.Hook, cond Condition) ent.Hook {
	return func(next ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {
			if cond(ctx, m) {
				return hk(next).Mutate(ctx, m)
			}
			return next.Mutate(ctx, m)
		})
	}
}

// On executes the given hook only for the given operation.
//
//	hook.On(Log, ent.Delete|ent.Create)
func On(hk ent.Hook, op ent.Op) ent.Hook {
	return If(hk, HasOp(op))
}

// Unless skips the given hook only for the given operation.
//
//	hook.Unless(Log, ent.Update|ent.UpdateOne)
func Unless(hk ent.Hook, op ent.Op) ent.Hook {
	return If(hk, Not(HasOp(op)))
}

// FixedError is a hook returning a fixed error.
func FixedError(err error) ent.Hook {
	return func(ent.Mutator) ent.Mutator {
		return ent.MutateFunc(func(context.Context, ent.Mutation) (ent.Value, error) {
			return nil, err
		})
	}
}

// Reject returns a hook that rejects all operations that match op.
//
//	func (T) Hooks() []ent.Hook {
//		return []ent.Hook{
//			Reject(ent.Delete|ent.Update),
//		}
//	}
func Reject(op ent.Op) ent.Hook {
	hk := FixedError(fmt.Errorf("%s operation is not allowed", op))
	return On(hk, op)
}

// Chain acts as a list of hooks and is effectively immutable.
// Once created, it will always hold the same set of hooks in the same order.
type Chain struct {
	hooks []ent.Hook
}

// NewChain creates a new chain of hooks.
func NewChain(hooks ...ent.Hook) Chain {
	return Chain{append([]ent.Hook(nil), hooks...)}
}

// Hook chains the list of hooks and returns the final hook.
func (c Chain) Hook() ent.Hook {
	return func(mutator ent.Mutator) ent.Mutator {
		for i := len(c.hooks) - 1; i >= 0; i-- {
			mutator = c.hooks[i](mutator)
		}
		return mutator
	}
}

// Append extends a chain, adding the specified hook
// as the last ones in the mutation flow.
func (c Chain) Append(hooks ...ent.Hook) Chain {
	newHooks := make([]ent.Hook, 0, len(c.hooks)+len(hooks))
	newHooks = append(newHooks, c.hooks...)
	newHooks = append(newHooks, hooks...)
	return Chain{newHooks}
}

// Extend extends a chain, adding the specified chain
// as the last ones in the mutation flow.
func (c Chain) Extend(chain Chain) Chain {
	return c.Append(chain.hooks...)
}

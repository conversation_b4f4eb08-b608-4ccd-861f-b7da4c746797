// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebasefile"
)

// KnowledgeBaseFileCreate is the builder for creating a KnowledgeBaseFile entity.
type KnowledgeBaseFileCreate struct {
	config
	mutation *KnowledgeBaseFileMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetTenantID sets the "tenant_id" field.
func (kbfc *KnowledgeBaseFileCreate) SetTenantID(i int64) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetTenantID(i)
	return kbfc
}

// SetCreatedAt sets the "created_at" field.
func (kbfc *KnowledgeBaseFileCreate) SetCreatedAt(t time.Time) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetCreatedAt(t)
	return kbfc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (kbfc *KnowledgeBaseFileCreate) SetNillableCreatedAt(t *time.Time) *KnowledgeBaseFileCreate {
	if t != nil {
		kbfc.SetCreatedAt(*t)
	}
	return kbfc
}

// SetUpdatedAt sets the "updated_at" field.
func (kbfc *KnowledgeBaseFileCreate) SetUpdatedAt(t time.Time) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetUpdatedAt(t)
	return kbfc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (kbfc *KnowledgeBaseFileCreate) SetNillableUpdatedAt(t *time.Time) *KnowledgeBaseFileCreate {
	if t != nil {
		kbfc.SetUpdatedAt(*t)
	}
	return kbfc
}

// SetDeletedAt sets the "deleted_at" field.
func (kbfc *KnowledgeBaseFileCreate) SetDeletedAt(t time.Time) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetDeletedAt(t)
	return kbfc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (kbfc *KnowledgeBaseFileCreate) SetNillableDeletedAt(t *time.Time) *KnowledgeBaseFileCreate {
	if t != nil {
		kbfc.SetDeletedAt(*t)
	}
	return kbfc
}

// SetKnowledgeBaseID sets the "knowledge_base_id" field.
func (kbfc *KnowledgeBaseFileCreate) SetKnowledgeBaseID(i int64) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetKnowledgeBaseID(i)
	return kbfc
}

// SetNillableKnowledgeBaseID sets the "knowledge_base_id" field if the given value is not nil.
func (kbfc *KnowledgeBaseFileCreate) SetNillableKnowledgeBaseID(i *int64) *KnowledgeBaseFileCreate {
	if i != nil {
		kbfc.SetKnowledgeBaseID(*i)
	}
	return kbfc
}

// SetDataType sets the "data_type" field.
func (kbfc *KnowledgeBaseFileCreate) SetDataType(i int32) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetDataType(i)
	return kbfc
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (kbfc *KnowledgeBaseFileCreate) SetNillableDataType(i *int32) *KnowledgeBaseFileCreate {
	if i != nil {
		kbfc.SetDataType(*i)
	}
	return kbfc
}

// SetFileRelationID sets the "file_relation_id" field.
func (kbfc *KnowledgeBaseFileCreate) SetFileRelationID(i int64) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetFileRelationID(i)
	return kbfc
}

// SetNillableFileRelationID sets the "file_relation_id" field if the given value is not nil.
func (kbfc *KnowledgeBaseFileCreate) SetNillableFileRelationID(i *int64) *KnowledgeBaseFileCreate {
	if i != nil {
		kbfc.SetFileRelationID(*i)
	}
	return kbfc
}

// SetMetadata sets the "metadata" field.
func (kbfc *KnowledgeBaseFileCreate) SetMetadata(s string) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetMetadata(s)
	return kbfc
}

// SetNillableMetadata sets the "metadata" field if the given value is not nil.
func (kbfc *KnowledgeBaseFileCreate) SetNillableMetadata(s *string) *KnowledgeBaseFileCreate {
	if s != nil {
		kbfc.SetMetadata(*s)
	}
	return kbfc
}

// SetStatus sets the "status" field.
func (kbfc *KnowledgeBaseFileCreate) SetStatus(i int32) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetStatus(i)
	return kbfc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (kbfc *KnowledgeBaseFileCreate) SetNillableStatus(i *int32) *KnowledgeBaseFileCreate {
	if i != nil {
		kbfc.SetStatus(*i)
	}
	return kbfc
}

// SetFailedReason sets the "failed_reason" field.
func (kbfc *KnowledgeBaseFileCreate) SetFailedReason(s string) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetFailedReason(s)
	return kbfc
}

// SetNillableFailedReason sets the "failed_reason" field if the given value is not nil.
func (kbfc *KnowledgeBaseFileCreate) SetNillableFailedReason(s *string) *KnowledgeBaseFileCreate {
	if s != nil {
		kbfc.SetFailedReason(*s)
	}
	return kbfc
}

// SetID sets the "id" field.
func (kbfc *KnowledgeBaseFileCreate) SetID(i int64) *KnowledgeBaseFileCreate {
	kbfc.mutation.SetID(i)
	return kbfc
}

// Mutation returns the KnowledgeBaseFileMutation object of the builder.
func (kbfc *KnowledgeBaseFileCreate) Mutation() *KnowledgeBaseFileMutation {
	return kbfc.mutation
}

// Save creates the KnowledgeBaseFile in the database.
func (kbfc *KnowledgeBaseFileCreate) Save(ctx context.Context) (*KnowledgeBaseFile, error) {
	if err := kbfc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, kbfc.sqlSave, kbfc.mutation, kbfc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (kbfc *KnowledgeBaseFileCreate) SaveX(ctx context.Context) *KnowledgeBaseFile {
	v, err := kbfc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (kbfc *KnowledgeBaseFileCreate) Exec(ctx context.Context) error {
	_, err := kbfc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (kbfc *KnowledgeBaseFileCreate) ExecX(ctx context.Context) {
	if err := kbfc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (kbfc *KnowledgeBaseFileCreate) defaults() error {
	if _, ok := kbfc.mutation.CreatedAt(); !ok {
		if knowledgebasefile.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized knowledgebasefile.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := knowledgebasefile.DefaultCreatedAt()
		kbfc.mutation.SetCreatedAt(v)
	}
	if _, ok := kbfc.mutation.UpdatedAt(); !ok {
		if knowledgebasefile.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized knowledgebasefile.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := knowledgebasefile.DefaultUpdatedAt()
		kbfc.mutation.SetUpdatedAt(v)
	}
	if _, ok := kbfc.mutation.DeletedAt(); !ok {
		v := knowledgebasefile.DefaultDeletedAt
		kbfc.mutation.SetDeletedAt(v)
	}
	if _, ok := kbfc.mutation.KnowledgeBaseID(); !ok {
		v := knowledgebasefile.DefaultKnowledgeBaseID
		kbfc.mutation.SetKnowledgeBaseID(v)
	}
	if _, ok := kbfc.mutation.DataType(); !ok {
		v := knowledgebasefile.DefaultDataType
		kbfc.mutation.SetDataType(v)
	}
	if _, ok := kbfc.mutation.FileRelationID(); !ok {
		v := knowledgebasefile.DefaultFileRelationID
		kbfc.mutation.SetFileRelationID(v)
	}
	if _, ok := kbfc.mutation.Metadata(); !ok {
		v := knowledgebasefile.DefaultMetadata
		kbfc.mutation.SetMetadata(v)
	}
	if _, ok := kbfc.mutation.Status(); !ok {
		v := knowledgebasefile.DefaultStatus
		kbfc.mutation.SetStatus(v)
	}
	if _, ok := kbfc.mutation.FailedReason(); !ok {
		v := knowledgebasefile.DefaultFailedReason
		kbfc.mutation.SetFailedReason(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (kbfc *KnowledgeBaseFileCreate) check() error {
	if _, ok := kbfc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "KnowledgeBaseFile.tenant_id"`)}
	}
	if _, ok := kbfc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "KnowledgeBaseFile.created_at"`)}
	}
	if _, ok := kbfc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "KnowledgeBaseFile.updated_at"`)}
	}
	if _, ok := kbfc.mutation.KnowledgeBaseID(); !ok {
		return &ValidationError{Name: "knowledge_base_id", err: errors.New(`ent: missing required field "KnowledgeBaseFile.knowledge_base_id"`)}
	}
	if _, ok := kbfc.mutation.DataType(); !ok {
		return &ValidationError{Name: "data_type", err: errors.New(`ent: missing required field "KnowledgeBaseFile.data_type"`)}
	}
	if _, ok := kbfc.mutation.FileRelationID(); !ok {
		return &ValidationError{Name: "file_relation_id", err: errors.New(`ent: missing required field "KnowledgeBaseFile.file_relation_id"`)}
	}
	if _, ok := kbfc.mutation.Metadata(); !ok {
		return &ValidationError{Name: "metadata", err: errors.New(`ent: missing required field "KnowledgeBaseFile.metadata"`)}
	}
	if _, ok := kbfc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "KnowledgeBaseFile.status"`)}
	}
	if _, ok := kbfc.mutation.FailedReason(); !ok {
		return &ValidationError{Name: "failed_reason", err: errors.New(`ent: missing required field "KnowledgeBaseFile.failed_reason"`)}
	}
	return nil
}

func (kbfc *KnowledgeBaseFileCreate) sqlSave(ctx context.Context) (*KnowledgeBaseFile, error) {
	if err := kbfc.check(); err != nil {
		return nil, err
	}
	_node, _spec := kbfc.createSpec()
	if err := sqlgraph.CreateNode(ctx, kbfc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	kbfc.mutation.id = &_node.ID
	kbfc.mutation.done = true
	return _node, nil
}

func (kbfc *KnowledgeBaseFileCreate) createSpec() (*KnowledgeBaseFile, *sqlgraph.CreateSpec) {
	var (
		_node = &KnowledgeBaseFile{config: kbfc.config}
		_spec = sqlgraph.NewCreateSpec(knowledgebasefile.Table, sqlgraph.NewFieldSpec(knowledgebasefile.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = kbfc.conflict
	if id, ok := kbfc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := kbfc.mutation.TenantID(); ok {
		_spec.SetField(knowledgebasefile.FieldTenantID, field.TypeInt64, value)
		_node.TenantID = value
	}
	if value, ok := kbfc.mutation.CreatedAt(); ok {
		_spec.SetField(knowledgebasefile.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := kbfc.mutation.UpdatedAt(); ok {
		_spec.SetField(knowledgebasefile.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := kbfc.mutation.DeletedAt(); ok {
		_spec.SetField(knowledgebasefile.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := kbfc.mutation.KnowledgeBaseID(); ok {
		_spec.SetField(knowledgebasefile.FieldKnowledgeBaseID, field.TypeInt64, value)
		_node.KnowledgeBaseID = value
	}
	if value, ok := kbfc.mutation.DataType(); ok {
		_spec.SetField(knowledgebasefile.FieldDataType, field.TypeInt32, value)
		_node.DataType = value
	}
	if value, ok := kbfc.mutation.FileRelationID(); ok {
		_spec.SetField(knowledgebasefile.FieldFileRelationID, field.TypeInt64, value)
		_node.FileRelationID = value
	}
	if value, ok := kbfc.mutation.Metadata(); ok {
		_spec.SetField(knowledgebasefile.FieldMetadata, field.TypeString, value)
		_node.Metadata = value
	}
	if value, ok := kbfc.mutation.Status(); ok {
		_spec.SetField(knowledgebasefile.FieldStatus, field.TypeInt32, value)
		_node.Status = value
	}
	if value, ok := kbfc.mutation.FailedReason(); ok {
		_spec.SetField(knowledgebasefile.FieldFailedReason, field.TypeString, value)
		_node.FailedReason = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.KnowledgeBaseFile.Create().
//		SetTenantID(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.KnowledgeBaseFileUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (kbfc *KnowledgeBaseFileCreate) OnConflict(opts ...sql.ConflictOption) *KnowledgeBaseFileUpsertOne {
	kbfc.conflict = opts
	return &KnowledgeBaseFileUpsertOne{
		create: kbfc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.KnowledgeBaseFile.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (kbfc *KnowledgeBaseFileCreate) OnConflictColumns(columns ...string) *KnowledgeBaseFileUpsertOne {
	kbfc.conflict = append(kbfc.conflict, sql.ConflictColumns(columns...))
	return &KnowledgeBaseFileUpsertOne{
		create: kbfc,
	}
}

type (
	// KnowledgeBaseFileUpsertOne is the builder for "upsert"-ing
	//  one KnowledgeBaseFile node.
	KnowledgeBaseFileUpsertOne struct {
		create *KnowledgeBaseFileCreate
	}

	// KnowledgeBaseFileUpsert is the "OnConflict" setter.
	KnowledgeBaseFileUpsert struct {
		*sql.UpdateSet
	}
)

// SetTenantID sets the "tenant_id" field.
func (u *KnowledgeBaseFileUpsert) SetTenantID(v int64) *KnowledgeBaseFileUpsert {
	u.Set(knowledgebasefile.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsert) UpdateTenantID() *KnowledgeBaseFileUpsert {
	u.SetExcluded(knowledgebasefile.FieldTenantID)
	return u
}

// AddTenantID adds v to the "tenant_id" field.
func (u *KnowledgeBaseFileUpsert) AddTenantID(v int64) *KnowledgeBaseFileUpsert {
	u.Add(knowledgebasefile.FieldTenantID, v)
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *KnowledgeBaseFileUpsert) SetUpdatedAt(v time.Time) *KnowledgeBaseFileUpsert {
	u.Set(knowledgebasefile.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsert) UpdateUpdatedAt() *KnowledgeBaseFileUpsert {
	u.SetExcluded(knowledgebasefile.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *KnowledgeBaseFileUpsert) SetDeletedAt(v time.Time) *KnowledgeBaseFileUpsert {
	u.Set(knowledgebasefile.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsert) UpdateDeletedAt() *KnowledgeBaseFileUpsert {
	u.SetExcluded(knowledgebasefile.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *KnowledgeBaseFileUpsert) ClearDeletedAt() *KnowledgeBaseFileUpsert {
	u.SetNull(knowledgebasefile.FieldDeletedAt)
	return u
}

// SetKnowledgeBaseID sets the "knowledge_base_id" field.
func (u *KnowledgeBaseFileUpsert) SetKnowledgeBaseID(v int64) *KnowledgeBaseFileUpsert {
	u.Set(knowledgebasefile.FieldKnowledgeBaseID, v)
	return u
}

// UpdateKnowledgeBaseID sets the "knowledge_base_id" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsert) UpdateKnowledgeBaseID() *KnowledgeBaseFileUpsert {
	u.SetExcluded(knowledgebasefile.FieldKnowledgeBaseID)
	return u
}

// AddKnowledgeBaseID adds v to the "knowledge_base_id" field.
func (u *KnowledgeBaseFileUpsert) AddKnowledgeBaseID(v int64) *KnowledgeBaseFileUpsert {
	u.Add(knowledgebasefile.FieldKnowledgeBaseID, v)
	return u
}

// SetDataType sets the "data_type" field.
func (u *KnowledgeBaseFileUpsert) SetDataType(v int32) *KnowledgeBaseFileUpsert {
	u.Set(knowledgebasefile.FieldDataType, v)
	return u
}

// UpdateDataType sets the "data_type" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsert) UpdateDataType() *KnowledgeBaseFileUpsert {
	u.SetExcluded(knowledgebasefile.FieldDataType)
	return u
}

// AddDataType adds v to the "data_type" field.
func (u *KnowledgeBaseFileUpsert) AddDataType(v int32) *KnowledgeBaseFileUpsert {
	u.Add(knowledgebasefile.FieldDataType, v)
	return u
}

// SetFileRelationID sets the "file_relation_id" field.
func (u *KnowledgeBaseFileUpsert) SetFileRelationID(v int64) *KnowledgeBaseFileUpsert {
	u.Set(knowledgebasefile.FieldFileRelationID, v)
	return u
}

// UpdateFileRelationID sets the "file_relation_id" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsert) UpdateFileRelationID() *KnowledgeBaseFileUpsert {
	u.SetExcluded(knowledgebasefile.FieldFileRelationID)
	return u
}

// AddFileRelationID adds v to the "file_relation_id" field.
func (u *KnowledgeBaseFileUpsert) AddFileRelationID(v int64) *KnowledgeBaseFileUpsert {
	u.Add(knowledgebasefile.FieldFileRelationID, v)
	return u
}

// SetMetadata sets the "metadata" field.
func (u *KnowledgeBaseFileUpsert) SetMetadata(v string) *KnowledgeBaseFileUpsert {
	u.Set(knowledgebasefile.FieldMetadata, v)
	return u
}

// UpdateMetadata sets the "metadata" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsert) UpdateMetadata() *KnowledgeBaseFileUpsert {
	u.SetExcluded(knowledgebasefile.FieldMetadata)
	return u
}

// SetStatus sets the "status" field.
func (u *KnowledgeBaseFileUpsert) SetStatus(v int32) *KnowledgeBaseFileUpsert {
	u.Set(knowledgebasefile.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsert) UpdateStatus() *KnowledgeBaseFileUpsert {
	u.SetExcluded(knowledgebasefile.FieldStatus)
	return u
}

// AddStatus adds v to the "status" field.
func (u *KnowledgeBaseFileUpsert) AddStatus(v int32) *KnowledgeBaseFileUpsert {
	u.Add(knowledgebasefile.FieldStatus, v)
	return u
}

// SetFailedReason sets the "failed_reason" field.
func (u *KnowledgeBaseFileUpsert) SetFailedReason(v string) *KnowledgeBaseFileUpsert {
	u.Set(knowledgebasefile.FieldFailedReason, v)
	return u
}

// UpdateFailedReason sets the "failed_reason" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsert) UpdateFailedReason() *KnowledgeBaseFileUpsert {
	u.SetExcluded(knowledgebasefile.FieldFailedReason)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.KnowledgeBaseFile.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(knowledgebasefile.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *KnowledgeBaseFileUpsertOne) UpdateNewValues() *KnowledgeBaseFileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(knowledgebasefile.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(knowledgebasefile.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.KnowledgeBaseFile.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *KnowledgeBaseFileUpsertOne) Ignore() *KnowledgeBaseFileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *KnowledgeBaseFileUpsertOne) DoNothing() *KnowledgeBaseFileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the KnowledgeBaseFileCreate.OnConflict
// documentation for more info.
func (u *KnowledgeBaseFileUpsertOne) Update(set func(*KnowledgeBaseFileUpsert)) *KnowledgeBaseFileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&KnowledgeBaseFileUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *KnowledgeBaseFileUpsertOne) SetTenantID(v int64) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *KnowledgeBaseFileUpsertOne) AddTenantID(v int64) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertOne) UpdateTenantID() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *KnowledgeBaseFileUpsertOne) SetUpdatedAt(v time.Time) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertOne) UpdateUpdatedAt() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *KnowledgeBaseFileUpsertOne) SetDeletedAt(v time.Time) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertOne) UpdateDeletedAt() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *KnowledgeBaseFileUpsertOne) ClearDeletedAt() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.ClearDeletedAt()
	})
}

// SetKnowledgeBaseID sets the "knowledge_base_id" field.
func (u *KnowledgeBaseFileUpsertOne) SetKnowledgeBaseID(v int64) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetKnowledgeBaseID(v)
	})
}

// AddKnowledgeBaseID adds v to the "knowledge_base_id" field.
func (u *KnowledgeBaseFileUpsertOne) AddKnowledgeBaseID(v int64) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddKnowledgeBaseID(v)
	})
}

// UpdateKnowledgeBaseID sets the "knowledge_base_id" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertOne) UpdateKnowledgeBaseID() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateKnowledgeBaseID()
	})
}

// SetDataType sets the "data_type" field.
func (u *KnowledgeBaseFileUpsertOne) SetDataType(v int32) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetDataType(v)
	})
}

// AddDataType adds v to the "data_type" field.
func (u *KnowledgeBaseFileUpsertOne) AddDataType(v int32) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddDataType(v)
	})
}

// UpdateDataType sets the "data_type" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertOne) UpdateDataType() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateDataType()
	})
}

// SetFileRelationID sets the "file_relation_id" field.
func (u *KnowledgeBaseFileUpsertOne) SetFileRelationID(v int64) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetFileRelationID(v)
	})
}

// AddFileRelationID adds v to the "file_relation_id" field.
func (u *KnowledgeBaseFileUpsertOne) AddFileRelationID(v int64) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddFileRelationID(v)
	})
}

// UpdateFileRelationID sets the "file_relation_id" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertOne) UpdateFileRelationID() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateFileRelationID()
	})
}

// SetMetadata sets the "metadata" field.
func (u *KnowledgeBaseFileUpsertOne) SetMetadata(v string) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetMetadata(v)
	})
}

// UpdateMetadata sets the "metadata" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertOne) UpdateMetadata() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateMetadata()
	})
}

// SetStatus sets the "status" field.
func (u *KnowledgeBaseFileUpsertOne) SetStatus(v int32) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetStatus(v)
	})
}

// AddStatus adds v to the "status" field.
func (u *KnowledgeBaseFileUpsertOne) AddStatus(v int32) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertOne) UpdateStatus() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateStatus()
	})
}

// SetFailedReason sets the "failed_reason" field.
func (u *KnowledgeBaseFileUpsertOne) SetFailedReason(v string) *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetFailedReason(v)
	})
}

// UpdateFailedReason sets the "failed_reason" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertOne) UpdateFailedReason() *KnowledgeBaseFileUpsertOne {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateFailedReason()
	})
}

// Exec executes the query.
func (u *KnowledgeBaseFileUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for KnowledgeBaseFileCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *KnowledgeBaseFileUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *KnowledgeBaseFileUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *KnowledgeBaseFileUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// KnowledgeBaseFileCreateBulk is the builder for creating many KnowledgeBaseFile entities in bulk.
type KnowledgeBaseFileCreateBulk struct {
	config
	err      error
	builders []*KnowledgeBaseFileCreate
	conflict []sql.ConflictOption
}

// Save creates the KnowledgeBaseFile entities in the database.
func (kbfcb *KnowledgeBaseFileCreateBulk) Save(ctx context.Context) ([]*KnowledgeBaseFile, error) {
	if kbfcb.err != nil {
		return nil, kbfcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(kbfcb.builders))
	nodes := make([]*KnowledgeBaseFile, len(kbfcb.builders))
	mutators := make([]Mutator, len(kbfcb.builders))
	for i := range kbfcb.builders {
		func(i int, root context.Context) {
			builder := kbfcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*KnowledgeBaseFileMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, kbfcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = kbfcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, kbfcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, kbfcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (kbfcb *KnowledgeBaseFileCreateBulk) SaveX(ctx context.Context) []*KnowledgeBaseFile {
	v, err := kbfcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (kbfcb *KnowledgeBaseFileCreateBulk) Exec(ctx context.Context) error {
	_, err := kbfcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (kbfcb *KnowledgeBaseFileCreateBulk) ExecX(ctx context.Context) {
	if err := kbfcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.KnowledgeBaseFile.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.KnowledgeBaseFileUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (kbfcb *KnowledgeBaseFileCreateBulk) OnConflict(opts ...sql.ConflictOption) *KnowledgeBaseFileUpsertBulk {
	kbfcb.conflict = opts
	return &KnowledgeBaseFileUpsertBulk{
		create: kbfcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.KnowledgeBaseFile.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (kbfcb *KnowledgeBaseFileCreateBulk) OnConflictColumns(columns ...string) *KnowledgeBaseFileUpsertBulk {
	kbfcb.conflict = append(kbfcb.conflict, sql.ConflictColumns(columns...))
	return &KnowledgeBaseFileUpsertBulk{
		create: kbfcb,
	}
}

// KnowledgeBaseFileUpsertBulk is the builder for "upsert"-ing
// a bulk of KnowledgeBaseFile nodes.
type KnowledgeBaseFileUpsertBulk struct {
	create *KnowledgeBaseFileCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.KnowledgeBaseFile.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(knowledgebasefile.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *KnowledgeBaseFileUpsertBulk) UpdateNewValues() *KnowledgeBaseFileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(knowledgebasefile.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(knowledgebasefile.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.KnowledgeBaseFile.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *KnowledgeBaseFileUpsertBulk) Ignore() *KnowledgeBaseFileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *KnowledgeBaseFileUpsertBulk) DoNothing() *KnowledgeBaseFileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the KnowledgeBaseFileCreateBulk.OnConflict
// documentation for more info.
func (u *KnowledgeBaseFileUpsertBulk) Update(set func(*KnowledgeBaseFileUpsert)) *KnowledgeBaseFileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&KnowledgeBaseFileUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *KnowledgeBaseFileUpsertBulk) SetTenantID(v int64) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *KnowledgeBaseFileUpsertBulk) AddTenantID(v int64) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertBulk) UpdateTenantID() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *KnowledgeBaseFileUpsertBulk) SetUpdatedAt(v time.Time) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertBulk) UpdateUpdatedAt() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *KnowledgeBaseFileUpsertBulk) SetDeletedAt(v time.Time) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertBulk) UpdateDeletedAt() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *KnowledgeBaseFileUpsertBulk) ClearDeletedAt() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.ClearDeletedAt()
	})
}

// SetKnowledgeBaseID sets the "knowledge_base_id" field.
func (u *KnowledgeBaseFileUpsertBulk) SetKnowledgeBaseID(v int64) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetKnowledgeBaseID(v)
	})
}

// AddKnowledgeBaseID adds v to the "knowledge_base_id" field.
func (u *KnowledgeBaseFileUpsertBulk) AddKnowledgeBaseID(v int64) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddKnowledgeBaseID(v)
	})
}

// UpdateKnowledgeBaseID sets the "knowledge_base_id" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertBulk) UpdateKnowledgeBaseID() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateKnowledgeBaseID()
	})
}

// SetDataType sets the "data_type" field.
func (u *KnowledgeBaseFileUpsertBulk) SetDataType(v int32) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetDataType(v)
	})
}

// AddDataType adds v to the "data_type" field.
func (u *KnowledgeBaseFileUpsertBulk) AddDataType(v int32) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddDataType(v)
	})
}

// UpdateDataType sets the "data_type" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertBulk) UpdateDataType() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateDataType()
	})
}

// SetFileRelationID sets the "file_relation_id" field.
func (u *KnowledgeBaseFileUpsertBulk) SetFileRelationID(v int64) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetFileRelationID(v)
	})
}

// AddFileRelationID adds v to the "file_relation_id" field.
func (u *KnowledgeBaseFileUpsertBulk) AddFileRelationID(v int64) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddFileRelationID(v)
	})
}

// UpdateFileRelationID sets the "file_relation_id" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertBulk) UpdateFileRelationID() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateFileRelationID()
	})
}

// SetMetadata sets the "metadata" field.
func (u *KnowledgeBaseFileUpsertBulk) SetMetadata(v string) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetMetadata(v)
	})
}

// UpdateMetadata sets the "metadata" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertBulk) UpdateMetadata() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateMetadata()
	})
}

// SetStatus sets the "status" field.
func (u *KnowledgeBaseFileUpsertBulk) SetStatus(v int32) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetStatus(v)
	})
}

// AddStatus adds v to the "status" field.
func (u *KnowledgeBaseFileUpsertBulk) AddStatus(v int32) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.AddStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertBulk) UpdateStatus() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateStatus()
	})
}

// SetFailedReason sets the "failed_reason" field.
func (u *KnowledgeBaseFileUpsertBulk) SetFailedReason(v string) *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.SetFailedReason(v)
	})
}

// UpdateFailedReason sets the "failed_reason" field to the value that was provided on create.
func (u *KnowledgeBaseFileUpsertBulk) UpdateFailedReason() *KnowledgeBaseFileUpsertBulk {
	return u.Update(func(s *KnowledgeBaseFileUpsert) {
		s.UpdateFailedReason()
	})
}

// Exec executes the query.
func (u *KnowledgeBaseFileUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the KnowledgeBaseFileCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for KnowledgeBaseFileCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *KnowledgeBaseFileUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

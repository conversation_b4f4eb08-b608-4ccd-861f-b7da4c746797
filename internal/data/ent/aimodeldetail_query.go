// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodeldetail"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiModelDetailQuery is the builder for querying AiModelDetail entities.
type AiModelDetailQuery struct {
	config
	ctx        *QueryContext
	order      []aimodeldetail.OrderOption
	inters     []Interceptor
	predicates []predicate.AiModelDetail
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AiModelDetailQuery builder.
func (amdq *AiModelDetailQuery) Where(ps ...predicate.AiModelDetail) *AiModelDetailQuery {
	amdq.predicates = append(amdq.predicates, ps...)
	return amdq
}

// Limit the number of records to be returned by this query.
func (amdq *AiModelDetailQuery) Limit(limit int) *AiModelDetailQuery {
	amdq.ctx.Limit = &limit
	return amdq
}

// Offset to start from.
func (amdq *AiModelDetailQuery) Offset(offset int) *AiModelDetailQuery {
	amdq.ctx.Offset = &offset
	return amdq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (amdq *AiModelDetailQuery) Unique(unique bool) *AiModelDetailQuery {
	amdq.ctx.Unique = &unique
	return amdq
}

// Order specifies how the records should be ordered.
func (amdq *AiModelDetailQuery) Order(o ...aimodeldetail.OrderOption) *AiModelDetailQuery {
	amdq.order = append(amdq.order, o...)
	return amdq
}

// First returns the first AiModelDetail entity from the query.
// Returns a *NotFoundError when no AiModelDetail was found.
func (amdq *AiModelDetailQuery) First(ctx context.Context) (*AiModelDetail, error) {
	nodes, err := amdq.Limit(1).All(setContextOp(ctx, amdq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{aimodeldetail.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (amdq *AiModelDetailQuery) FirstX(ctx context.Context) *AiModelDetail {
	node, err := amdq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AiModelDetail ID from the query.
// Returns a *NotFoundError when no AiModelDetail ID was found.
func (amdq *AiModelDetailQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = amdq.Limit(1).IDs(setContextOp(ctx, amdq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{aimodeldetail.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (amdq *AiModelDetailQuery) FirstIDX(ctx context.Context) int64 {
	id, err := amdq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AiModelDetail entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AiModelDetail entity is found.
// Returns a *NotFoundError when no AiModelDetail entities are found.
func (amdq *AiModelDetailQuery) Only(ctx context.Context) (*AiModelDetail, error) {
	nodes, err := amdq.Limit(2).All(setContextOp(ctx, amdq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{aimodeldetail.Label}
	default:
		return nil, &NotSingularError{aimodeldetail.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (amdq *AiModelDetailQuery) OnlyX(ctx context.Context) *AiModelDetail {
	node, err := amdq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AiModelDetail ID in the query.
// Returns a *NotSingularError when more than one AiModelDetail ID is found.
// Returns a *NotFoundError when no entities are found.
func (amdq *AiModelDetailQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = amdq.Limit(2).IDs(setContextOp(ctx, amdq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{aimodeldetail.Label}
	default:
		err = &NotSingularError{aimodeldetail.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (amdq *AiModelDetailQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := amdq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AiModelDetails.
func (amdq *AiModelDetailQuery) All(ctx context.Context) ([]*AiModelDetail, error) {
	ctx = setContextOp(ctx, amdq.ctx, "All")
	if err := amdq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AiModelDetail, *AiModelDetailQuery]()
	return withInterceptors[[]*AiModelDetail](ctx, amdq, qr, amdq.inters)
}

// AllX is like All, but panics if an error occurs.
func (amdq *AiModelDetailQuery) AllX(ctx context.Context) []*AiModelDetail {
	nodes, err := amdq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AiModelDetail IDs.
func (amdq *AiModelDetailQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if amdq.ctx.Unique == nil && amdq.path != nil {
		amdq.Unique(true)
	}
	ctx = setContextOp(ctx, amdq.ctx, "IDs")
	if err = amdq.Select(aimodeldetail.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (amdq *AiModelDetailQuery) IDsX(ctx context.Context) []int64 {
	ids, err := amdq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (amdq *AiModelDetailQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, amdq.ctx, "Count")
	if err := amdq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, amdq, querierCount[*AiModelDetailQuery](), amdq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (amdq *AiModelDetailQuery) CountX(ctx context.Context) int {
	count, err := amdq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (amdq *AiModelDetailQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, amdq.ctx, "Exist")
	switch _, err := amdq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (amdq *AiModelDetailQuery) ExistX(ctx context.Context) bool {
	exist, err := amdq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AiModelDetailQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (amdq *AiModelDetailQuery) Clone() *AiModelDetailQuery {
	if amdq == nil {
		return nil
	}
	return &AiModelDetailQuery{
		config:     amdq.config,
		ctx:        amdq.ctx.Clone(),
		order:      append([]aimodeldetail.OrderOption{}, amdq.order...),
		inters:     append([]Interceptor{}, amdq.inters...),
		predicates: append([]predicate.AiModelDetail{}, amdq.predicates...),
		// clone intermediate query.
		sql:  amdq.sql.Clone(),
		path: amdq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AiModelDetail.Query().
//		GroupBy(aimodeldetail.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (amdq *AiModelDetailQuery) GroupBy(field string, fields ...string) *AiModelDetailGroupBy {
	amdq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AiModelDetailGroupBy{build: amdq}
	grbuild.flds = &amdq.ctx.Fields
	grbuild.label = aimodeldetail.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.AiModelDetail.Query().
//		Select(aimodeldetail.FieldCreatedAt).
//		Scan(ctx, &v)
func (amdq *AiModelDetailQuery) Select(fields ...string) *AiModelDetailSelect {
	amdq.ctx.Fields = append(amdq.ctx.Fields, fields...)
	sbuild := &AiModelDetailSelect{AiModelDetailQuery: amdq}
	sbuild.label = aimodeldetail.Label
	sbuild.flds, sbuild.scan = &amdq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AiModelDetailSelect configured with the given aggregations.
func (amdq *AiModelDetailQuery) Aggregate(fns ...AggregateFunc) *AiModelDetailSelect {
	return amdq.Select().Aggregate(fns...)
}

func (amdq *AiModelDetailQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range amdq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, amdq); err != nil {
				return err
			}
		}
	}
	for _, f := range amdq.ctx.Fields {
		if !aimodeldetail.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if amdq.path != nil {
		prev, err := amdq.path(ctx)
		if err != nil {
			return err
		}
		amdq.sql = prev
	}
	return nil
}

func (amdq *AiModelDetailQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AiModelDetail, error) {
	var (
		nodes = []*AiModelDetail{}
		_spec = amdq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AiModelDetail).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AiModelDetail{config: amdq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(amdq.modifiers) > 0 {
		_spec.Modifiers = amdq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, amdq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (amdq *AiModelDetailQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := amdq.querySpec()
	if len(amdq.modifiers) > 0 {
		_spec.Modifiers = amdq.modifiers
	}
	_spec.Node.Columns = amdq.ctx.Fields
	if len(amdq.ctx.Fields) > 0 {
		_spec.Unique = amdq.ctx.Unique != nil && *amdq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, amdq.driver, _spec)
}

func (amdq *AiModelDetailQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(aimodeldetail.Table, aimodeldetail.Columns, sqlgraph.NewFieldSpec(aimodeldetail.FieldID, field.TypeInt64))
	_spec.From = amdq.sql
	if unique := amdq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if amdq.path != nil {
		_spec.Unique = true
	}
	if fields := amdq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aimodeldetail.FieldID)
		for i := range fields {
			if fields[i] != aimodeldetail.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := amdq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := amdq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := amdq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := amdq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (amdq *AiModelDetailQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(amdq.driver.Dialect())
	t1 := builder.Table(aimodeldetail.Table)
	columns := amdq.ctx.Fields
	if len(columns) == 0 {
		columns = aimodeldetail.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if amdq.sql != nil {
		selector = amdq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if amdq.ctx.Unique != nil && *amdq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range amdq.modifiers {
		m(selector)
	}
	for _, p := range amdq.predicates {
		p(selector)
	}
	for _, p := range amdq.order {
		p(selector)
	}
	if offset := amdq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := amdq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (amdq *AiModelDetailQuery) ForUpdate(opts ...sql.LockOption) *AiModelDetailQuery {
	if amdq.driver.Dialect() == dialect.Postgres {
		amdq.Unique(false)
	}
	amdq.modifiers = append(amdq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return amdq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (amdq *AiModelDetailQuery) ForShare(opts ...sql.LockOption) *AiModelDetailQuery {
	if amdq.driver.Dialect() == dialect.Postgres {
		amdq.Unique(false)
	}
	amdq.modifiers = append(amdq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return amdq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (amdq *AiModelDetailQuery) Modify(modifiers ...func(s *sql.Selector)) *AiModelDetailSelect {
	amdq.modifiers = append(amdq.modifiers, modifiers...)
	return amdq.Select()
}

// AiModelDetailGroupBy is the group-by builder for AiModelDetail entities.
type AiModelDetailGroupBy struct {
	selector
	build *AiModelDetailQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (amdgb *AiModelDetailGroupBy) Aggregate(fns ...AggregateFunc) *AiModelDetailGroupBy {
	amdgb.fns = append(amdgb.fns, fns...)
	return amdgb
}

// Scan applies the selector query and scans the result into the given value.
func (amdgb *AiModelDetailGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, amdgb.build.ctx, "GroupBy")
	if err := amdgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiModelDetailQuery, *AiModelDetailGroupBy](ctx, amdgb.build, amdgb, amdgb.build.inters, v)
}

func (amdgb *AiModelDetailGroupBy) sqlScan(ctx context.Context, root *AiModelDetailQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(amdgb.fns))
	for _, fn := range amdgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*amdgb.flds)+len(amdgb.fns))
		for _, f := range *amdgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*amdgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := amdgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AiModelDetailSelect is the builder for selecting fields of AiModelDetail entities.
type AiModelDetailSelect struct {
	*AiModelDetailQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (amds *AiModelDetailSelect) Aggregate(fns ...AggregateFunc) *AiModelDetailSelect {
	amds.fns = append(amds.fns, fns...)
	return amds
}

// Scan applies the selector query and scans the result into the given value.
func (amds *AiModelDetailSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, amds.ctx, "Select")
	if err := amds.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiModelDetailQuery, *AiModelDetailSelect](ctx, amds.AiModelDetailQuery, amds, amds.inters, v)
}

func (amds *AiModelDetailSelect) sqlScan(ctx context.Context, root *AiModelDetailQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(amds.fns))
	for _, fn := range amds.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*amds.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := amds.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (amds *AiModelDetailSelect) Modify(modifiers ...func(s *sql.Selector)) *AiModelDetailSelect {
	amds.modifiers = append(amds.modifiers, modifiers...)
	return amds
}

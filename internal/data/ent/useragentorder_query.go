// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/useragentorder"
)

// UserAgentOrderQuery is the builder for querying UserAgentOrder entities.
type UserAgentOrderQuery struct {
	config
	ctx        *QueryContext
	order      []useragentorder.OrderOption
	inters     []Interceptor
	predicates []predicate.UserAgentOrder
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the UserAgentOrderQuery builder.
func (uaoq *UserAgentOrderQuery) Where(ps ...predicate.UserAgentOrder) *UserAgentOrderQuery {
	uaoq.predicates = append(uaoq.predicates, ps...)
	return uaoq
}

// Limit the number of records to be returned by this query.
func (uaoq *UserAgentOrderQuery) Limit(limit int) *UserAgentOrderQuery {
	uaoq.ctx.Limit = &limit
	return uaoq
}

// Offset to start from.
func (uaoq *UserAgentOrderQuery) Offset(offset int) *UserAgentOrderQuery {
	uaoq.ctx.Offset = &offset
	return uaoq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (uaoq *UserAgentOrderQuery) Unique(unique bool) *UserAgentOrderQuery {
	uaoq.ctx.Unique = &unique
	return uaoq
}

// Order specifies how the records should be ordered.
func (uaoq *UserAgentOrderQuery) Order(o ...useragentorder.OrderOption) *UserAgentOrderQuery {
	uaoq.order = append(uaoq.order, o...)
	return uaoq
}

// First returns the first UserAgentOrder entity from the query.
// Returns a *NotFoundError when no UserAgentOrder was found.
func (uaoq *UserAgentOrderQuery) First(ctx context.Context) (*UserAgentOrder, error) {
	nodes, err := uaoq.Limit(1).All(setContextOp(ctx, uaoq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{useragentorder.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (uaoq *UserAgentOrderQuery) FirstX(ctx context.Context) *UserAgentOrder {
	node, err := uaoq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first UserAgentOrder ID from the query.
// Returns a *NotFoundError when no UserAgentOrder ID was found.
func (uaoq *UserAgentOrderQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = uaoq.Limit(1).IDs(setContextOp(ctx, uaoq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{useragentorder.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (uaoq *UserAgentOrderQuery) FirstIDX(ctx context.Context) int64 {
	id, err := uaoq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single UserAgentOrder entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one UserAgentOrder entity is found.
// Returns a *NotFoundError when no UserAgentOrder entities are found.
func (uaoq *UserAgentOrderQuery) Only(ctx context.Context) (*UserAgentOrder, error) {
	nodes, err := uaoq.Limit(2).All(setContextOp(ctx, uaoq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{useragentorder.Label}
	default:
		return nil, &NotSingularError{useragentorder.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (uaoq *UserAgentOrderQuery) OnlyX(ctx context.Context) *UserAgentOrder {
	node, err := uaoq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only UserAgentOrder ID in the query.
// Returns a *NotSingularError when more than one UserAgentOrder ID is found.
// Returns a *NotFoundError when no entities are found.
func (uaoq *UserAgentOrderQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = uaoq.Limit(2).IDs(setContextOp(ctx, uaoq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{useragentorder.Label}
	default:
		err = &NotSingularError{useragentorder.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (uaoq *UserAgentOrderQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := uaoq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of UserAgentOrders.
func (uaoq *UserAgentOrderQuery) All(ctx context.Context) ([]*UserAgentOrder, error) {
	ctx = setContextOp(ctx, uaoq.ctx, "All")
	if err := uaoq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*UserAgentOrder, *UserAgentOrderQuery]()
	return withInterceptors[[]*UserAgentOrder](ctx, uaoq, qr, uaoq.inters)
}

// AllX is like All, but panics if an error occurs.
func (uaoq *UserAgentOrderQuery) AllX(ctx context.Context) []*UserAgentOrder {
	nodes, err := uaoq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of UserAgentOrder IDs.
func (uaoq *UserAgentOrderQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if uaoq.ctx.Unique == nil && uaoq.path != nil {
		uaoq.Unique(true)
	}
	ctx = setContextOp(ctx, uaoq.ctx, "IDs")
	if err = uaoq.Select(useragentorder.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (uaoq *UserAgentOrderQuery) IDsX(ctx context.Context) []int64 {
	ids, err := uaoq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (uaoq *UserAgentOrderQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, uaoq.ctx, "Count")
	if err := uaoq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, uaoq, querierCount[*UserAgentOrderQuery](), uaoq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (uaoq *UserAgentOrderQuery) CountX(ctx context.Context) int {
	count, err := uaoq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (uaoq *UserAgentOrderQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, uaoq.ctx, "Exist")
	switch _, err := uaoq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (uaoq *UserAgentOrderQuery) ExistX(ctx context.Context) bool {
	exist, err := uaoq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the UserAgentOrderQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (uaoq *UserAgentOrderQuery) Clone() *UserAgentOrderQuery {
	if uaoq == nil {
		return nil
	}
	return &UserAgentOrderQuery{
		config:     uaoq.config,
		ctx:        uaoq.ctx.Clone(),
		order:      append([]useragentorder.OrderOption{}, uaoq.order...),
		inters:     append([]Interceptor{}, uaoq.inters...),
		predicates: append([]predicate.UserAgentOrder{}, uaoq.predicates...),
		// clone intermediate query.
		sql:  uaoq.sql.Clone(),
		path: uaoq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.UserAgentOrder.Query().
//		GroupBy(useragentorder.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (uaoq *UserAgentOrderQuery) GroupBy(field string, fields ...string) *UserAgentOrderGroupBy {
	uaoq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &UserAgentOrderGroupBy{build: uaoq}
	grbuild.flds = &uaoq.ctx.Fields
	grbuild.label = useragentorder.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.UserAgentOrder.Query().
//		Select(useragentorder.FieldCreatedAt).
//		Scan(ctx, &v)
func (uaoq *UserAgentOrderQuery) Select(fields ...string) *UserAgentOrderSelect {
	uaoq.ctx.Fields = append(uaoq.ctx.Fields, fields...)
	sbuild := &UserAgentOrderSelect{UserAgentOrderQuery: uaoq}
	sbuild.label = useragentorder.Label
	sbuild.flds, sbuild.scan = &uaoq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a UserAgentOrderSelect configured with the given aggregations.
func (uaoq *UserAgentOrderQuery) Aggregate(fns ...AggregateFunc) *UserAgentOrderSelect {
	return uaoq.Select().Aggregate(fns...)
}

func (uaoq *UserAgentOrderQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range uaoq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, uaoq); err != nil {
				return err
			}
		}
	}
	for _, f := range uaoq.ctx.Fields {
		if !useragentorder.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if uaoq.path != nil {
		prev, err := uaoq.path(ctx)
		if err != nil {
			return err
		}
		uaoq.sql = prev
	}
	return nil
}

func (uaoq *UserAgentOrderQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*UserAgentOrder, error) {
	var (
		nodes = []*UserAgentOrder{}
		_spec = uaoq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*UserAgentOrder).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &UserAgentOrder{config: uaoq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(uaoq.modifiers) > 0 {
		_spec.Modifiers = uaoq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, uaoq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (uaoq *UserAgentOrderQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := uaoq.querySpec()
	if len(uaoq.modifiers) > 0 {
		_spec.Modifiers = uaoq.modifiers
	}
	_spec.Node.Columns = uaoq.ctx.Fields
	if len(uaoq.ctx.Fields) > 0 {
		_spec.Unique = uaoq.ctx.Unique != nil && *uaoq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, uaoq.driver, _spec)
}

func (uaoq *UserAgentOrderQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(useragentorder.Table, useragentorder.Columns, sqlgraph.NewFieldSpec(useragentorder.FieldID, field.TypeInt64))
	_spec.From = uaoq.sql
	if unique := uaoq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if uaoq.path != nil {
		_spec.Unique = true
	}
	if fields := uaoq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, useragentorder.FieldID)
		for i := range fields {
			if fields[i] != useragentorder.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := uaoq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := uaoq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := uaoq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := uaoq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (uaoq *UserAgentOrderQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(uaoq.driver.Dialect())
	t1 := builder.Table(useragentorder.Table)
	columns := uaoq.ctx.Fields
	if len(columns) == 0 {
		columns = useragentorder.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if uaoq.sql != nil {
		selector = uaoq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if uaoq.ctx.Unique != nil && *uaoq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range uaoq.modifiers {
		m(selector)
	}
	for _, p := range uaoq.predicates {
		p(selector)
	}
	for _, p := range uaoq.order {
		p(selector)
	}
	if offset := uaoq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := uaoq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (uaoq *UserAgentOrderQuery) ForUpdate(opts ...sql.LockOption) *UserAgentOrderQuery {
	if uaoq.driver.Dialect() == dialect.Postgres {
		uaoq.Unique(false)
	}
	uaoq.modifiers = append(uaoq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return uaoq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (uaoq *UserAgentOrderQuery) ForShare(opts ...sql.LockOption) *UserAgentOrderQuery {
	if uaoq.driver.Dialect() == dialect.Postgres {
		uaoq.Unique(false)
	}
	uaoq.modifiers = append(uaoq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return uaoq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (uaoq *UserAgentOrderQuery) Modify(modifiers ...func(s *sql.Selector)) *UserAgentOrderSelect {
	uaoq.modifiers = append(uaoq.modifiers, modifiers...)
	return uaoq.Select()
}

// UserAgentOrderGroupBy is the group-by builder for UserAgentOrder entities.
type UserAgentOrderGroupBy struct {
	selector
	build *UserAgentOrderQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (uaogb *UserAgentOrderGroupBy) Aggregate(fns ...AggregateFunc) *UserAgentOrderGroupBy {
	uaogb.fns = append(uaogb.fns, fns...)
	return uaogb
}

// Scan applies the selector query and scans the result into the given value.
func (uaogb *UserAgentOrderGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, uaogb.build.ctx, "GroupBy")
	if err := uaogb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserAgentOrderQuery, *UserAgentOrderGroupBy](ctx, uaogb.build, uaogb, uaogb.build.inters, v)
}

func (uaogb *UserAgentOrderGroupBy) sqlScan(ctx context.Context, root *UserAgentOrderQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(uaogb.fns))
	for _, fn := range uaogb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*uaogb.flds)+len(uaogb.fns))
		for _, f := range *uaogb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*uaogb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := uaogb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// UserAgentOrderSelect is the builder for selecting fields of UserAgentOrder entities.
type UserAgentOrderSelect struct {
	*UserAgentOrderQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (uaos *UserAgentOrderSelect) Aggregate(fns ...AggregateFunc) *UserAgentOrderSelect {
	uaos.fns = append(uaos.fns, fns...)
	return uaos
}

// Scan applies the selector query and scans the result into the given value.
func (uaos *UserAgentOrderSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, uaos.ctx, "Select")
	if err := uaos.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserAgentOrderQuery, *UserAgentOrderSelect](ctx, uaos.UserAgentOrderQuery, uaos, uaos.inters, v)
}

func (uaos *UserAgentOrderSelect) sqlScan(ctx context.Context, root *UserAgentOrderQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(uaos.fns))
	for _, fn := range uaos.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*uaos.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := uaos.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (uaos *UserAgentOrderSelect) Modify(modifiers ...func(s *sql.Selector)) *UserAgentOrderSelect {
	uaos.modifiers = append(uaos.modifiers, modifiers...)
	return uaos
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/externalmodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ExternalModelUsageUpdate is the builder for updating ExternalModelUsage entities.
type ExternalModelUsageUpdate struct {
	config
	hooks     []Hook
	mutation  *ExternalModelUsageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the ExternalModelUsageUpdate builder.
func (emuu *ExternalModelUsageUpdate) Where(ps ...predicate.ExternalModelUsage) *ExternalModelUsageUpdate {
	emuu.mutation.Where(ps...)
	return emuu
}

// SetUpdatedAt sets the "updated_at" field.
func (emuu *ExternalModelUsageUpdate) SetUpdatedAt(t time.Time) *ExternalModelUsageUpdate {
	emuu.mutation.SetUpdatedAt(t)
	return emuu
}

// SetDeletedAt sets the "deleted_at" field.
func (emuu *ExternalModelUsageUpdate) SetDeletedAt(t time.Time) *ExternalModelUsageUpdate {
	emuu.mutation.SetDeletedAt(t)
	return emuu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableDeletedAt(t *time.Time) *ExternalModelUsageUpdate {
	if t != nil {
		emuu.SetDeletedAt(*t)
	}
	return emuu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (emuu *ExternalModelUsageUpdate) ClearDeletedAt() *ExternalModelUsageUpdate {
	emuu.mutation.ClearDeletedAt()
	return emuu
}

// SetModelName sets the "model_name" field.
func (emuu *ExternalModelUsageUpdate) SetModelName(s string) *ExternalModelUsageUpdate {
	emuu.mutation.SetModelName(s)
	return emuu
}

// SetNillableModelName sets the "model_name" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableModelName(s *string) *ExternalModelUsageUpdate {
	if s != nil {
		emuu.SetModelName(*s)
	}
	return emuu
}

// SetQuestion sets the "question" field.
func (emuu *ExternalModelUsageUpdate) SetQuestion(s string) *ExternalModelUsageUpdate {
	emuu.mutation.SetQuestion(s)
	return emuu
}

// SetNillableQuestion sets the "question" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableQuestion(s *string) *ExternalModelUsageUpdate {
	if s != nil {
		emuu.SetQuestion(*s)
	}
	return emuu
}

// SetQuestionTag sets the "question_tag" field.
func (emuu *ExternalModelUsageUpdate) SetQuestionTag(s string) *ExternalModelUsageUpdate {
	emuu.mutation.SetQuestionTag(s)
	return emuu
}

// SetNillableQuestionTag sets the "question_tag" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableQuestionTag(s *string) *ExternalModelUsageUpdate {
	if s != nil {
		emuu.SetQuestionTag(*s)
	}
	return emuu
}

// SetFiles sets the "files" field.
func (emuu *ExternalModelUsageUpdate) SetFiles(s string) *ExternalModelUsageUpdate {
	emuu.mutation.SetFiles(s)
	return emuu
}

// SetNillableFiles sets the "files" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableFiles(s *string) *ExternalModelUsageUpdate {
	if s != nil {
		emuu.SetFiles(*s)
	}
	return emuu
}

// SetMimeTypes sets the "mime_types" field.
func (emuu *ExternalModelUsageUpdate) SetMimeTypes(pa *pq.StringArray) *ExternalModelUsageUpdate {
	emuu.mutation.SetMimeTypes(pa)
	return emuu
}

// SetUserID sets the "user_id" field.
func (emuu *ExternalModelUsageUpdate) SetUserID(i int64) *ExternalModelUsageUpdate {
	emuu.mutation.ResetUserID()
	emuu.mutation.SetUserID(i)
	return emuu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableUserID(i *int64) *ExternalModelUsageUpdate {
	if i != nil {
		emuu.SetUserID(*i)
	}
	return emuu
}

// AddUserID adds i to the "user_id" field.
func (emuu *ExternalModelUsageUpdate) AddUserID(i int64) *ExternalModelUsageUpdate {
	emuu.mutation.AddUserID(i)
	return emuu
}

// SetUserName sets the "user_name" field.
func (emuu *ExternalModelUsageUpdate) SetUserName(s string) *ExternalModelUsageUpdate {
	emuu.mutation.SetUserName(s)
	return emuu
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableUserName(s *string) *ExternalModelUsageUpdate {
	if s != nil {
		emuu.SetUserName(*s)
	}
	return emuu
}

// SetDeptID sets the "dept_id" field.
func (emuu *ExternalModelUsageUpdate) SetDeptID(i int64) *ExternalModelUsageUpdate {
	emuu.mutation.ResetDeptID()
	emuu.mutation.SetDeptID(i)
	return emuu
}

// SetNillableDeptID sets the "dept_id" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableDeptID(i *int64) *ExternalModelUsageUpdate {
	if i != nil {
		emuu.SetDeptID(*i)
	}
	return emuu
}

// AddDeptID adds i to the "dept_id" field.
func (emuu *ExternalModelUsageUpdate) AddDeptID(i int64) *ExternalModelUsageUpdate {
	emuu.mutation.AddDeptID(i)
	return emuu
}

// SetDeptName sets the "dept_name" field.
func (emuu *ExternalModelUsageUpdate) SetDeptName(s string) *ExternalModelUsageUpdate {
	emuu.mutation.SetDeptName(s)
	return emuu
}

// SetNillableDeptName sets the "dept_name" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableDeptName(s *string) *ExternalModelUsageUpdate {
	if s != nil {
		emuu.SetDeptName(*s)
	}
	return emuu
}

// SetPcName sets the "pc_name" field.
func (emuu *ExternalModelUsageUpdate) SetPcName(s string) *ExternalModelUsageUpdate {
	emuu.mutation.SetPcName(s)
	return emuu
}

// SetNillablePcName sets the "pc_name" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillablePcName(s *string) *ExternalModelUsageUpdate {
	if s != nil {
		emuu.SetPcName(*s)
	}
	return emuu
}

// SetHappenedAt sets the "happened_at" field.
func (emuu *ExternalModelUsageUpdate) SetHappenedAt(t time.Time) *ExternalModelUsageUpdate {
	emuu.mutation.SetHappenedAt(t)
	return emuu
}

// SetNillableHappenedAt sets the "happened_at" field if the given value is not nil.
func (emuu *ExternalModelUsageUpdate) SetNillableHappenedAt(t *time.Time) *ExternalModelUsageUpdate {
	if t != nil {
		emuu.SetHappenedAt(*t)
	}
	return emuu
}

// Mutation returns the ExternalModelUsageMutation object of the builder.
func (emuu *ExternalModelUsageUpdate) Mutation() *ExternalModelUsageMutation {
	return emuu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (emuu *ExternalModelUsageUpdate) Save(ctx context.Context) (int, error) {
	if err := emuu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, emuu.sqlSave, emuu.mutation, emuu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (emuu *ExternalModelUsageUpdate) SaveX(ctx context.Context) int {
	affected, err := emuu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (emuu *ExternalModelUsageUpdate) Exec(ctx context.Context) error {
	_, err := emuu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (emuu *ExternalModelUsageUpdate) ExecX(ctx context.Context) {
	if err := emuu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (emuu *ExternalModelUsageUpdate) defaults() error {
	if _, ok := emuu.mutation.UpdatedAt(); !ok {
		if externalmodelusage.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized externalmodelusage.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := externalmodelusage.UpdateDefaultUpdatedAt()
		emuu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (emuu *ExternalModelUsageUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ExternalModelUsageUpdate {
	emuu.modifiers = append(emuu.modifiers, modifiers...)
	return emuu
}

func (emuu *ExternalModelUsageUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(externalmodelusage.Table, externalmodelusage.Columns, sqlgraph.NewFieldSpec(externalmodelusage.FieldID, field.TypeInt64))
	if ps := emuu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := emuu.mutation.UpdatedAt(); ok {
		_spec.SetField(externalmodelusage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := emuu.mutation.DeletedAt(); ok {
		_spec.SetField(externalmodelusage.FieldDeletedAt, field.TypeTime, value)
	}
	if emuu.mutation.DeletedAtCleared() {
		_spec.ClearField(externalmodelusage.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := emuu.mutation.ModelName(); ok {
		_spec.SetField(externalmodelusage.FieldModelName, field.TypeString, value)
	}
	if value, ok := emuu.mutation.Question(); ok {
		_spec.SetField(externalmodelusage.FieldQuestion, field.TypeString, value)
	}
	if value, ok := emuu.mutation.QuestionTag(); ok {
		_spec.SetField(externalmodelusage.FieldQuestionTag, field.TypeString, value)
	}
	if value, ok := emuu.mutation.Files(); ok {
		_spec.SetField(externalmodelusage.FieldFiles, field.TypeString, value)
	}
	if value, ok := emuu.mutation.MimeTypes(); ok {
		_spec.SetField(externalmodelusage.FieldMimeTypes, field.TypeOther, value)
	}
	if value, ok := emuu.mutation.UserID(); ok {
		_spec.SetField(externalmodelusage.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := emuu.mutation.AddedUserID(); ok {
		_spec.AddField(externalmodelusage.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := emuu.mutation.UserName(); ok {
		_spec.SetField(externalmodelusage.FieldUserName, field.TypeString, value)
	}
	if value, ok := emuu.mutation.DeptID(); ok {
		_spec.SetField(externalmodelusage.FieldDeptID, field.TypeInt64, value)
	}
	if value, ok := emuu.mutation.AddedDeptID(); ok {
		_spec.AddField(externalmodelusage.FieldDeptID, field.TypeInt64, value)
	}
	if value, ok := emuu.mutation.DeptName(); ok {
		_spec.SetField(externalmodelusage.FieldDeptName, field.TypeString, value)
	}
	if value, ok := emuu.mutation.PcName(); ok {
		_spec.SetField(externalmodelusage.FieldPcName, field.TypeString, value)
	}
	if value, ok := emuu.mutation.HappenedAt(); ok {
		_spec.SetField(externalmodelusage.FieldHappenedAt, field.TypeTime, value)
	}
	_spec.AddModifiers(emuu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, emuu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{externalmodelusage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	emuu.mutation.done = true
	return n, nil
}

// ExternalModelUsageUpdateOne is the builder for updating a single ExternalModelUsage entity.
type ExternalModelUsageUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *ExternalModelUsageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (emuuo *ExternalModelUsageUpdateOne) SetUpdatedAt(t time.Time) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetUpdatedAt(t)
	return emuuo
}

// SetDeletedAt sets the "deleted_at" field.
func (emuuo *ExternalModelUsageUpdateOne) SetDeletedAt(t time.Time) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetDeletedAt(t)
	return emuuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableDeletedAt(t *time.Time) *ExternalModelUsageUpdateOne {
	if t != nil {
		emuuo.SetDeletedAt(*t)
	}
	return emuuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (emuuo *ExternalModelUsageUpdateOne) ClearDeletedAt() *ExternalModelUsageUpdateOne {
	emuuo.mutation.ClearDeletedAt()
	return emuuo
}

// SetModelName sets the "model_name" field.
func (emuuo *ExternalModelUsageUpdateOne) SetModelName(s string) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetModelName(s)
	return emuuo
}

// SetNillableModelName sets the "model_name" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableModelName(s *string) *ExternalModelUsageUpdateOne {
	if s != nil {
		emuuo.SetModelName(*s)
	}
	return emuuo
}

// SetQuestion sets the "question" field.
func (emuuo *ExternalModelUsageUpdateOne) SetQuestion(s string) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetQuestion(s)
	return emuuo
}

// SetNillableQuestion sets the "question" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableQuestion(s *string) *ExternalModelUsageUpdateOne {
	if s != nil {
		emuuo.SetQuestion(*s)
	}
	return emuuo
}

// SetQuestionTag sets the "question_tag" field.
func (emuuo *ExternalModelUsageUpdateOne) SetQuestionTag(s string) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetQuestionTag(s)
	return emuuo
}

// SetNillableQuestionTag sets the "question_tag" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableQuestionTag(s *string) *ExternalModelUsageUpdateOne {
	if s != nil {
		emuuo.SetQuestionTag(*s)
	}
	return emuuo
}

// SetFiles sets the "files" field.
func (emuuo *ExternalModelUsageUpdateOne) SetFiles(s string) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetFiles(s)
	return emuuo
}

// SetNillableFiles sets the "files" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableFiles(s *string) *ExternalModelUsageUpdateOne {
	if s != nil {
		emuuo.SetFiles(*s)
	}
	return emuuo
}

// SetMimeTypes sets the "mime_types" field.
func (emuuo *ExternalModelUsageUpdateOne) SetMimeTypes(pa *pq.StringArray) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetMimeTypes(pa)
	return emuuo
}

// SetUserID sets the "user_id" field.
func (emuuo *ExternalModelUsageUpdateOne) SetUserID(i int64) *ExternalModelUsageUpdateOne {
	emuuo.mutation.ResetUserID()
	emuuo.mutation.SetUserID(i)
	return emuuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableUserID(i *int64) *ExternalModelUsageUpdateOne {
	if i != nil {
		emuuo.SetUserID(*i)
	}
	return emuuo
}

// AddUserID adds i to the "user_id" field.
func (emuuo *ExternalModelUsageUpdateOne) AddUserID(i int64) *ExternalModelUsageUpdateOne {
	emuuo.mutation.AddUserID(i)
	return emuuo
}

// SetUserName sets the "user_name" field.
func (emuuo *ExternalModelUsageUpdateOne) SetUserName(s string) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetUserName(s)
	return emuuo
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableUserName(s *string) *ExternalModelUsageUpdateOne {
	if s != nil {
		emuuo.SetUserName(*s)
	}
	return emuuo
}

// SetDeptID sets the "dept_id" field.
func (emuuo *ExternalModelUsageUpdateOne) SetDeptID(i int64) *ExternalModelUsageUpdateOne {
	emuuo.mutation.ResetDeptID()
	emuuo.mutation.SetDeptID(i)
	return emuuo
}

// SetNillableDeptID sets the "dept_id" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableDeptID(i *int64) *ExternalModelUsageUpdateOne {
	if i != nil {
		emuuo.SetDeptID(*i)
	}
	return emuuo
}

// AddDeptID adds i to the "dept_id" field.
func (emuuo *ExternalModelUsageUpdateOne) AddDeptID(i int64) *ExternalModelUsageUpdateOne {
	emuuo.mutation.AddDeptID(i)
	return emuuo
}

// SetDeptName sets the "dept_name" field.
func (emuuo *ExternalModelUsageUpdateOne) SetDeptName(s string) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetDeptName(s)
	return emuuo
}

// SetNillableDeptName sets the "dept_name" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableDeptName(s *string) *ExternalModelUsageUpdateOne {
	if s != nil {
		emuuo.SetDeptName(*s)
	}
	return emuuo
}

// SetPcName sets the "pc_name" field.
func (emuuo *ExternalModelUsageUpdateOne) SetPcName(s string) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetPcName(s)
	return emuuo
}

// SetNillablePcName sets the "pc_name" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillablePcName(s *string) *ExternalModelUsageUpdateOne {
	if s != nil {
		emuuo.SetPcName(*s)
	}
	return emuuo
}

// SetHappenedAt sets the "happened_at" field.
func (emuuo *ExternalModelUsageUpdateOne) SetHappenedAt(t time.Time) *ExternalModelUsageUpdateOne {
	emuuo.mutation.SetHappenedAt(t)
	return emuuo
}

// SetNillableHappenedAt sets the "happened_at" field if the given value is not nil.
func (emuuo *ExternalModelUsageUpdateOne) SetNillableHappenedAt(t *time.Time) *ExternalModelUsageUpdateOne {
	if t != nil {
		emuuo.SetHappenedAt(*t)
	}
	return emuuo
}

// Mutation returns the ExternalModelUsageMutation object of the builder.
func (emuuo *ExternalModelUsageUpdateOne) Mutation() *ExternalModelUsageMutation {
	return emuuo.mutation
}

// Where appends a list predicates to the ExternalModelUsageUpdate builder.
func (emuuo *ExternalModelUsageUpdateOne) Where(ps ...predicate.ExternalModelUsage) *ExternalModelUsageUpdateOne {
	emuuo.mutation.Where(ps...)
	return emuuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (emuuo *ExternalModelUsageUpdateOne) Select(field string, fields ...string) *ExternalModelUsageUpdateOne {
	emuuo.fields = append([]string{field}, fields...)
	return emuuo
}

// Save executes the query and returns the updated ExternalModelUsage entity.
func (emuuo *ExternalModelUsageUpdateOne) Save(ctx context.Context) (*ExternalModelUsage, error) {
	if err := emuuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, emuuo.sqlSave, emuuo.mutation, emuuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (emuuo *ExternalModelUsageUpdateOne) SaveX(ctx context.Context) *ExternalModelUsage {
	node, err := emuuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (emuuo *ExternalModelUsageUpdateOne) Exec(ctx context.Context) error {
	_, err := emuuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (emuuo *ExternalModelUsageUpdateOne) ExecX(ctx context.Context) {
	if err := emuuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (emuuo *ExternalModelUsageUpdateOne) defaults() error {
	if _, ok := emuuo.mutation.UpdatedAt(); !ok {
		if externalmodelusage.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized externalmodelusage.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := externalmodelusage.UpdateDefaultUpdatedAt()
		emuuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (emuuo *ExternalModelUsageUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *ExternalModelUsageUpdateOne {
	emuuo.modifiers = append(emuuo.modifiers, modifiers...)
	return emuuo
}

func (emuuo *ExternalModelUsageUpdateOne) sqlSave(ctx context.Context) (_node *ExternalModelUsage, err error) {
	_spec := sqlgraph.NewUpdateSpec(externalmodelusage.Table, externalmodelusage.Columns, sqlgraph.NewFieldSpec(externalmodelusage.FieldID, field.TypeInt64))
	id, ok := emuuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "ExternalModelUsage.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := emuuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, externalmodelusage.FieldID)
		for _, f := range fields {
			if !externalmodelusage.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != externalmodelusage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := emuuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := emuuo.mutation.UpdatedAt(); ok {
		_spec.SetField(externalmodelusage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := emuuo.mutation.DeletedAt(); ok {
		_spec.SetField(externalmodelusage.FieldDeletedAt, field.TypeTime, value)
	}
	if emuuo.mutation.DeletedAtCleared() {
		_spec.ClearField(externalmodelusage.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := emuuo.mutation.ModelName(); ok {
		_spec.SetField(externalmodelusage.FieldModelName, field.TypeString, value)
	}
	if value, ok := emuuo.mutation.Question(); ok {
		_spec.SetField(externalmodelusage.FieldQuestion, field.TypeString, value)
	}
	if value, ok := emuuo.mutation.QuestionTag(); ok {
		_spec.SetField(externalmodelusage.FieldQuestionTag, field.TypeString, value)
	}
	if value, ok := emuuo.mutation.Files(); ok {
		_spec.SetField(externalmodelusage.FieldFiles, field.TypeString, value)
	}
	if value, ok := emuuo.mutation.MimeTypes(); ok {
		_spec.SetField(externalmodelusage.FieldMimeTypes, field.TypeOther, value)
	}
	if value, ok := emuuo.mutation.UserID(); ok {
		_spec.SetField(externalmodelusage.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := emuuo.mutation.AddedUserID(); ok {
		_spec.AddField(externalmodelusage.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := emuuo.mutation.UserName(); ok {
		_spec.SetField(externalmodelusage.FieldUserName, field.TypeString, value)
	}
	if value, ok := emuuo.mutation.DeptID(); ok {
		_spec.SetField(externalmodelusage.FieldDeptID, field.TypeInt64, value)
	}
	if value, ok := emuuo.mutation.AddedDeptID(); ok {
		_spec.AddField(externalmodelusage.FieldDeptID, field.TypeInt64, value)
	}
	if value, ok := emuuo.mutation.DeptName(); ok {
		_spec.SetField(externalmodelusage.FieldDeptName, field.TypeString, value)
	}
	if value, ok := emuuo.mutation.PcName(); ok {
		_spec.SetField(externalmodelusage.FieldPcName, field.TypeString, value)
	}
	if value, ok := emuuo.mutation.HappenedAt(); ok {
		_spec.SetField(externalmodelusage.FieldHappenedAt, field.TypeTime, value)
	}
	_spec.AddModifiers(emuuo.modifiers...)
	_node = &ExternalModelUsage{config: emuuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, emuuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{externalmodelusage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	emuuo.mutation.done = true
	return _node, nil
}

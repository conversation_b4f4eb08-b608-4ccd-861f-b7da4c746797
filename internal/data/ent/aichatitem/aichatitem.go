// Code generated by ent, DO NOT EDIT.

package aichatitem

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

const (
	// Label holds the string label denoting the aichatitem type in the database.
	Label = "ai_chat_item"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldChatID holds the string denoting the chat_id field in the database.
	FieldChatID = "chat_id"
	// FieldObjectID holds the string denoting the object_id field in the database.
	FieldObjectID = "object_id"
	// FieldObjectType holds the string denoting the object_type field in the database.
	FieldObjectType = "object_type"
	// FieldMessage holds the string denoting the message field in the database.
	FieldMessage = "message"
	// FieldRefFiles holds the string denoting the ref_files field in the database.
	FieldRefFiles = "ref_files"
	// FieldAgreeStatus holds the string denoting the agree_status field in the database.
	FieldAgreeStatus = "agree_status"
	// FieldRoundID holds the string denoting the round_id field in the database.
	FieldRoundID = "round_id"
	// FieldPcName holds the string denoting the pc_name field in the database.
	FieldPcName = "pc_name"
	// FieldReason holds the string denoting the reason field in the database.
	FieldReason = "reason"
	// FieldPrimaryClassification holds the string denoting the primary_classification field in the database.
	FieldPrimaryClassification = "primary_classification"
	// FieldSecondaryClassification holds the string denoting the secondary_classification field in the database.
	FieldSecondaryClassification = "secondary_classification"
	// FieldMineTypes holds the string denoting the mine_types field in the database.
	FieldMineTypes = "mine_types"
	// FieldSuggestQuestions holds the string denoting the suggest_questions field in the database.
	FieldSuggestQuestions = "suggest_questions"
	// FieldHitAction holds the string denoting the hit_action field in the database.
	FieldHitAction = "hit_action"
	// FieldHitResponse holds the string denoting the hit_response field in the database.
	FieldHitResponse = "hit_response"
	// FieldHitContinueSend holds the string denoting the hit_continue_send field in the database.
	FieldHitContinueSend = "hit_continue_send"
	// FieldIsInternetSearch holds the string denoting the is_internet_search field in the database.
	FieldIsInternetSearch = "is_internet_search"
	// Table holds the table name of the aichatitem in the database.
	Table = "ai_chat_item"
)

// Columns holds all SQL columns for aichatitem fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldChatID,
	FieldObjectID,
	FieldObjectType,
	FieldMessage,
	FieldRefFiles,
	FieldAgreeStatus,
	FieldRoundID,
	FieldPcName,
	FieldReason,
	FieldPrimaryClassification,
	FieldSecondaryClassification,
	FieldMineTypes,
	FieldSuggestQuestions,
	FieldHitAction,
	FieldHitResponse,
	FieldHitContinueSend,
	FieldIsInternetSearch,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultDeletedAt holds the default value on creation for the "deleted_at" field.
	DefaultDeletedAt time.Time
	// DefaultAgreeStatus holds the default value on creation for the "agree_status" field.
	DefaultAgreeStatus int8
	// DefaultRoundID holds the default value on creation for the "round_id" field.
	DefaultRoundID int64
	// DefaultPcName holds the default value on creation for the "pc_name" field.
	DefaultPcName string
	// DefaultReason holds the default value on creation for the "reason" field.
	DefaultReason string
	// DefaultPrimaryClassification holds the default value on creation for the "primary_classification" field.
	DefaultPrimaryClassification string
	// DefaultSecondaryClassification holds the default value on creation for the "secondary_classification" field.
	DefaultSecondaryClassification string
	// DefaultMineTypes holds the default value on creation for the "mine_types" field.
	DefaultMineTypes *pq.StringArray
	// DefaultSuggestQuestions holds the default value on creation for the "suggest_questions" field.
	DefaultSuggestQuestions *pq.StringArray
	// DefaultHitAction holds the default value on creation for the "hit_action" field.
	DefaultHitAction int64
	// DefaultHitResponse holds the default value on creation for the "hit_response" field.
	DefaultHitResponse string
	// DefaultHitContinueSend holds the default value on creation for the "hit_continue_send" field.
	DefaultHitContinueSend bool
	// DefaultIsInternetSearch holds the default value on creation for the "is_internet_search" field.
	DefaultIsInternetSearch bool
)

// OrderOption defines the ordering options for the AiChatItem queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByChatID orders the results by the chat_id field.
func ByChatID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldChatID, opts...).ToFunc()
}

// ByObjectID orders the results by the object_id field.
func ByObjectID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldObjectID, opts...).ToFunc()
}

// ByObjectType orders the results by the object_type field.
func ByObjectType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldObjectType, opts...).ToFunc()
}

// ByMessage orders the results by the message field.
func ByMessage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMessage, opts...).ToFunc()
}

// ByRefFiles orders the results by the ref_files field.
func ByRefFiles(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRefFiles, opts...).ToFunc()
}

// ByAgreeStatus orders the results by the agree_status field.
func ByAgreeStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAgreeStatus, opts...).ToFunc()
}

// ByRoundID orders the results by the round_id field.
func ByRoundID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRoundID, opts...).ToFunc()
}

// ByPcName orders the results by the pc_name field.
func ByPcName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPcName, opts...).ToFunc()
}

// ByReason orders the results by the reason field.
func ByReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldReason, opts...).ToFunc()
}

// ByPrimaryClassification orders the results by the primary_classification field.
func ByPrimaryClassification(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPrimaryClassification, opts...).ToFunc()
}

// BySecondaryClassification orders the results by the secondary_classification field.
func BySecondaryClassification(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSecondaryClassification, opts...).ToFunc()
}

// ByMineTypes orders the results by the mine_types field.
func ByMineTypes(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMineTypes, opts...).ToFunc()
}

// BySuggestQuestions orders the results by the suggest_questions field.
func BySuggestQuestions(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSuggestQuestions, opts...).ToFunc()
}

// ByHitAction orders the results by the hit_action field.
func ByHitAction(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHitAction, opts...).ToFunc()
}

// ByHitResponse orders the results by the hit_response field.
func ByHitResponse(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHitResponse, opts...).ToFunc()
}

// ByHitContinueSend orders the results by the hit_continue_send field.
func ByHitContinueSend(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHitContinueSend, opts...).ToFunc()
}

// ByIsInternetSearch orders the results by the is_internet_search field.
func ByIsInternetSearch(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsInternetSearch, opts...).ToFunc()
}

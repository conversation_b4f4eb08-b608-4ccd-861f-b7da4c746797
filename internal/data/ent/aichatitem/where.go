// Code generated by ent, DO NOT EDIT.

package aichatitem

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldDeletedAt, v))
}

// ChatID applies equality check predicate on the "chat_id" field. It's identical to ChatIDEQ.
func ChatID(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldChatID, v))
}

// ObjectID applies equality check predicate on the "object_id" field. It's identical to ObjectIDEQ.
func ObjectID(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldObjectID, v))
}

// ObjectType applies equality check predicate on the "object_type" field. It's identical to ObjectTypeEQ.
func ObjectType(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldObjectType, v))
}

// Message applies equality check predicate on the "message" field. It's identical to MessageEQ.
func Message(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldMessage, v))
}

// RefFiles applies equality check predicate on the "ref_files" field. It's identical to RefFilesEQ.
func RefFiles(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldRefFiles, v))
}

// AgreeStatus applies equality check predicate on the "agree_status" field. It's identical to AgreeStatusEQ.
func AgreeStatus(v int8) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldAgreeStatus, v))
}

// RoundID applies equality check predicate on the "round_id" field. It's identical to RoundIDEQ.
func RoundID(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldRoundID, v))
}

// PcName applies equality check predicate on the "pc_name" field. It's identical to PcNameEQ.
func PcName(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldPcName, v))
}

// Reason applies equality check predicate on the "reason" field. It's identical to ReasonEQ.
func Reason(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldReason, v))
}

// PrimaryClassification applies equality check predicate on the "primary_classification" field. It's identical to PrimaryClassificationEQ.
func PrimaryClassification(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldPrimaryClassification, v))
}

// SecondaryClassification applies equality check predicate on the "secondary_classification" field. It's identical to SecondaryClassificationEQ.
func SecondaryClassification(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldSecondaryClassification, v))
}

// MineTypes applies equality check predicate on the "mine_types" field. It's identical to MineTypesEQ.
func MineTypes(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldMineTypes, v))
}

// SuggestQuestions applies equality check predicate on the "suggest_questions" field. It's identical to SuggestQuestionsEQ.
func SuggestQuestions(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldSuggestQuestions, v))
}

// HitAction applies equality check predicate on the "hit_action" field. It's identical to HitActionEQ.
func HitAction(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldHitAction, v))
}

// HitResponse applies equality check predicate on the "hit_response" field. It's identical to HitResponseEQ.
func HitResponse(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldHitResponse, v))
}

// HitContinueSend applies equality check predicate on the "hit_continue_send" field. It's identical to HitContinueSendEQ.
func HitContinueSend(v bool) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldHitContinueSend, v))
}

// IsInternetSearch applies equality check predicate on the "is_internet_search" field. It's identical to IsInternetSearchEQ.
func IsInternetSearch(v bool) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldIsInternetSearch, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldTenantID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotNull(FieldDeletedAt))
}

// ChatIDEQ applies the EQ predicate on the "chat_id" field.
func ChatIDEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldChatID, v))
}

// ChatIDNEQ applies the NEQ predicate on the "chat_id" field.
func ChatIDNEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldChatID, v))
}

// ChatIDIn applies the In predicate on the "chat_id" field.
func ChatIDIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldChatID, vs...))
}

// ChatIDNotIn applies the NotIn predicate on the "chat_id" field.
func ChatIDNotIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldChatID, vs...))
}

// ChatIDGT applies the GT predicate on the "chat_id" field.
func ChatIDGT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldChatID, v))
}

// ChatIDGTE applies the GTE predicate on the "chat_id" field.
func ChatIDGTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldChatID, v))
}

// ChatIDLT applies the LT predicate on the "chat_id" field.
func ChatIDLT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldChatID, v))
}

// ChatIDLTE applies the LTE predicate on the "chat_id" field.
func ChatIDLTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldChatID, v))
}

// ObjectIDEQ applies the EQ predicate on the "object_id" field.
func ObjectIDEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldObjectID, v))
}

// ObjectIDNEQ applies the NEQ predicate on the "object_id" field.
func ObjectIDNEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldObjectID, v))
}

// ObjectIDIn applies the In predicate on the "object_id" field.
func ObjectIDIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldObjectID, vs...))
}

// ObjectIDNotIn applies the NotIn predicate on the "object_id" field.
func ObjectIDNotIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldObjectID, vs...))
}

// ObjectIDGT applies the GT predicate on the "object_id" field.
func ObjectIDGT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldObjectID, v))
}

// ObjectIDGTE applies the GTE predicate on the "object_id" field.
func ObjectIDGTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldObjectID, v))
}

// ObjectIDLT applies the LT predicate on the "object_id" field.
func ObjectIDLT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldObjectID, v))
}

// ObjectIDLTE applies the LTE predicate on the "object_id" field.
func ObjectIDLTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldObjectID, v))
}

// ObjectTypeEQ applies the EQ predicate on the "object_type" field.
func ObjectTypeEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldObjectType, v))
}

// ObjectTypeNEQ applies the NEQ predicate on the "object_type" field.
func ObjectTypeNEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldObjectType, v))
}

// ObjectTypeIn applies the In predicate on the "object_type" field.
func ObjectTypeIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldObjectType, vs...))
}

// ObjectTypeNotIn applies the NotIn predicate on the "object_type" field.
func ObjectTypeNotIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldObjectType, vs...))
}

// ObjectTypeGT applies the GT predicate on the "object_type" field.
func ObjectTypeGT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldObjectType, v))
}

// ObjectTypeGTE applies the GTE predicate on the "object_type" field.
func ObjectTypeGTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldObjectType, v))
}

// ObjectTypeLT applies the LT predicate on the "object_type" field.
func ObjectTypeLT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldObjectType, v))
}

// ObjectTypeLTE applies the LTE predicate on the "object_type" field.
func ObjectTypeLTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldObjectType, v))
}

// MessageEQ applies the EQ predicate on the "message" field.
func MessageEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldMessage, v))
}

// MessageNEQ applies the NEQ predicate on the "message" field.
func MessageNEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldMessage, v))
}

// MessageIn applies the In predicate on the "message" field.
func MessageIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldMessage, vs...))
}

// MessageNotIn applies the NotIn predicate on the "message" field.
func MessageNotIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldMessage, vs...))
}

// MessageGT applies the GT predicate on the "message" field.
func MessageGT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldMessage, v))
}

// MessageGTE applies the GTE predicate on the "message" field.
func MessageGTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldMessage, v))
}

// MessageLT applies the LT predicate on the "message" field.
func MessageLT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldMessage, v))
}

// MessageLTE applies the LTE predicate on the "message" field.
func MessageLTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldMessage, v))
}

// MessageContains applies the Contains predicate on the "message" field.
func MessageContains(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContains(FieldMessage, v))
}

// MessageHasPrefix applies the HasPrefix predicate on the "message" field.
func MessageHasPrefix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasPrefix(FieldMessage, v))
}

// MessageHasSuffix applies the HasSuffix predicate on the "message" field.
func MessageHasSuffix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasSuffix(FieldMessage, v))
}

// MessageEqualFold applies the EqualFold predicate on the "message" field.
func MessageEqualFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEqualFold(FieldMessage, v))
}

// MessageContainsFold applies the ContainsFold predicate on the "message" field.
func MessageContainsFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContainsFold(FieldMessage, v))
}

// RefFilesEQ applies the EQ predicate on the "ref_files" field.
func RefFilesEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldRefFiles, v))
}

// RefFilesNEQ applies the NEQ predicate on the "ref_files" field.
func RefFilesNEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldRefFiles, v))
}

// RefFilesIn applies the In predicate on the "ref_files" field.
func RefFilesIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldRefFiles, vs...))
}

// RefFilesNotIn applies the NotIn predicate on the "ref_files" field.
func RefFilesNotIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldRefFiles, vs...))
}

// RefFilesGT applies the GT predicate on the "ref_files" field.
func RefFilesGT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldRefFiles, v))
}

// RefFilesGTE applies the GTE predicate on the "ref_files" field.
func RefFilesGTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldRefFiles, v))
}

// RefFilesLT applies the LT predicate on the "ref_files" field.
func RefFilesLT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldRefFiles, v))
}

// RefFilesLTE applies the LTE predicate on the "ref_files" field.
func RefFilesLTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldRefFiles, v))
}

// RefFilesContains applies the Contains predicate on the "ref_files" field.
func RefFilesContains(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContains(FieldRefFiles, v))
}

// RefFilesHasPrefix applies the HasPrefix predicate on the "ref_files" field.
func RefFilesHasPrefix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasPrefix(FieldRefFiles, v))
}

// RefFilesHasSuffix applies the HasSuffix predicate on the "ref_files" field.
func RefFilesHasSuffix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasSuffix(FieldRefFiles, v))
}

// RefFilesEqualFold applies the EqualFold predicate on the "ref_files" field.
func RefFilesEqualFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEqualFold(FieldRefFiles, v))
}

// RefFilesContainsFold applies the ContainsFold predicate on the "ref_files" field.
func RefFilesContainsFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContainsFold(FieldRefFiles, v))
}

// AgreeStatusEQ applies the EQ predicate on the "agree_status" field.
func AgreeStatusEQ(v int8) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldAgreeStatus, v))
}

// AgreeStatusNEQ applies the NEQ predicate on the "agree_status" field.
func AgreeStatusNEQ(v int8) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldAgreeStatus, v))
}

// AgreeStatusIn applies the In predicate on the "agree_status" field.
func AgreeStatusIn(vs ...int8) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldAgreeStatus, vs...))
}

// AgreeStatusNotIn applies the NotIn predicate on the "agree_status" field.
func AgreeStatusNotIn(vs ...int8) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldAgreeStatus, vs...))
}

// AgreeStatusGT applies the GT predicate on the "agree_status" field.
func AgreeStatusGT(v int8) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldAgreeStatus, v))
}

// AgreeStatusGTE applies the GTE predicate on the "agree_status" field.
func AgreeStatusGTE(v int8) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldAgreeStatus, v))
}

// AgreeStatusLT applies the LT predicate on the "agree_status" field.
func AgreeStatusLT(v int8) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldAgreeStatus, v))
}

// AgreeStatusLTE applies the LTE predicate on the "agree_status" field.
func AgreeStatusLTE(v int8) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldAgreeStatus, v))
}

// RoundIDEQ applies the EQ predicate on the "round_id" field.
func RoundIDEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldRoundID, v))
}

// RoundIDNEQ applies the NEQ predicate on the "round_id" field.
func RoundIDNEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldRoundID, v))
}

// RoundIDIn applies the In predicate on the "round_id" field.
func RoundIDIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldRoundID, vs...))
}

// RoundIDNotIn applies the NotIn predicate on the "round_id" field.
func RoundIDNotIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldRoundID, vs...))
}

// RoundIDGT applies the GT predicate on the "round_id" field.
func RoundIDGT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldRoundID, v))
}

// RoundIDGTE applies the GTE predicate on the "round_id" field.
func RoundIDGTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldRoundID, v))
}

// RoundIDLT applies the LT predicate on the "round_id" field.
func RoundIDLT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldRoundID, v))
}

// RoundIDLTE applies the LTE predicate on the "round_id" field.
func RoundIDLTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldRoundID, v))
}

// PcNameEQ applies the EQ predicate on the "pc_name" field.
func PcNameEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldPcName, v))
}

// PcNameNEQ applies the NEQ predicate on the "pc_name" field.
func PcNameNEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldPcName, v))
}

// PcNameIn applies the In predicate on the "pc_name" field.
func PcNameIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldPcName, vs...))
}

// PcNameNotIn applies the NotIn predicate on the "pc_name" field.
func PcNameNotIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldPcName, vs...))
}

// PcNameGT applies the GT predicate on the "pc_name" field.
func PcNameGT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldPcName, v))
}

// PcNameGTE applies the GTE predicate on the "pc_name" field.
func PcNameGTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldPcName, v))
}

// PcNameLT applies the LT predicate on the "pc_name" field.
func PcNameLT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldPcName, v))
}

// PcNameLTE applies the LTE predicate on the "pc_name" field.
func PcNameLTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldPcName, v))
}

// PcNameContains applies the Contains predicate on the "pc_name" field.
func PcNameContains(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContains(FieldPcName, v))
}

// PcNameHasPrefix applies the HasPrefix predicate on the "pc_name" field.
func PcNameHasPrefix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasPrefix(FieldPcName, v))
}

// PcNameHasSuffix applies the HasSuffix predicate on the "pc_name" field.
func PcNameHasSuffix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasSuffix(FieldPcName, v))
}

// PcNameEqualFold applies the EqualFold predicate on the "pc_name" field.
func PcNameEqualFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEqualFold(FieldPcName, v))
}

// PcNameContainsFold applies the ContainsFold predicate on the "pc_name" field.
func PcNameContainsFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContainsFold(FieldPcName, v))
}

// ReasonEQ applies the EQ predicate on the "reason" field.
func ReasonEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldReason, v))
}

// ReasonNEQ applies the NEQ predicate on the "reason" field.
func ReasonNEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldReason, v))
}

// ReasonIn applies the In predicate on the "reason" field.
func ReasonIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldReason, vs...))
}

// ReasonNotIn applies the NotIn predicate on the "reason" field.
func ReasonNotIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldReason, vs...))
}

// ReasonGT applies the GT predicate on the "reason" field.
func ReasonGT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldReason, v))
}

// ReasonGTE applies the GTE predicate on the "reason" field.
func ReasonGTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldReason, v))
}

// ReasonLT applies the LT predicate on the "reason" field.
func ReasonLT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldReason, v))
}

// ReasonLTE applies the LTE predicate on the "reason" field.
func ReasonLTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldReason, v))
}

// ReasonContains applies the Contains predicate on the "reason" field.
func ReasonContains(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContains(FieldReason, v))
}

// ReasonHasPrefix applies the HasPrefix predicate on the "reason" field.
func ReasonHasPrefix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasPrefix(FieldReason, v))
}

// ReasonHasSuffix applies the HasSuffix predicate on the "reason" field.
func ReasonHasSuffix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasSuffix(FieldReason, v))
}

// ReasonEqualFold applies the EqualFold predicate on the "reason" field.
func ReasonEqualFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEqualFold(FieldReason, v))
}

// ReasonContainsFold applies the ContainsFold predicate on the "reason" field.
func ReasonContainsFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContainsFold(FieldReason, v))
}

// PrimaryClassificationEQ applies the EQ predicate on the "primary_classification" field.
func PrimaryClassificationEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldPrimaryClassification, v))
}

// PrimaryClassificationNEQ applies the NEQ predicate on the "primary_classification" field.
func PrimaryClassificationNEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldPrimaryClassification, v))
}

// PrimaryClassificationIn applies the In predicate on the "primary_classification" field.
func PrimaryClassificationIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldPrimaryClassification, vs...))
}

// PrimaryClassificationNotIn applies the NotIn predicate on the "primary_classification" field.
func PrimaryClassificationNotIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldPrimaryClassification, vs...))
}

// PrimaryClassificationGT applies the GT predicate on the "primary_classification" field.
func PrimaryClassificationGT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldPrimaryClassification, v))
}

// PrimaryClassificationGTE applies the GTE predicate on the "primary_classification" field.
func PrimaryClassificationGTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldPrimaryClassification, v))
}

// PrimaryClassificationLT applies the LT predicate on the "primary_classification" field.
func PrimaryClassificationLT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldPrimaryClassification, v))
}

// PrimaryClassificationLTE applies the LTE predicate on the "primary_classification" field.
func PrimaryClassificationLTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldPrimaryClassification, v))
}

// PrimaryClassificationContains applies the Contains predicate on the "primary_classification" field.
func PrimaryClassificationContains(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContains(FieldPrimaryClassification, v))
}

// PrimaryClassificationHasPrefix applies the HasPrefix predicate on the "primary_classification" field.
func PrimaryClassificationHasPrefix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasPrefix(FieldPrimaryClassification, v))
}

// PrimaryClassificationHasSuffix applies the HasSuffix predicate on the "primary_classification" field.
func PrimaryClassificationHasSuffix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasSuffix(FieldPrimaryClassification, v))
}

// PrimaryClassificationEqualFold applies the EqualFold predicate on the "primary_classification" field.
func PrimaryClassificationEqualFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEqualFold(FieldPrimaryClassification, v))
}

// PrimaryClassificationContainsFold applies the ContainsFold predicate on the "primary_classification" field.
func PrimaryClassificationContainsFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContainsFold(FieldPrimaryClassification, v))
}

// SecondaryClassificationEQ applies the EQ predicate on the "secondary_classification" field.
func SecondaryClassificationEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldSecondaryClassification, v))
}

// SecondaryClassificationNEQ applies the NEQ predicate on the "secondary_classification" field.
func SecondaryClassificationNEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldSecondaryClassification, v))
}

// SecondaryClassificationIn applies the In predicate on the "secondary_classification" field.
func SecondaryClassificationIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldSecondaryClassification, vs...))
}

// SecondaryClassificationNotIn applies the NotIn predicate on the "secondary_classification" field.
func SecondaryClassificationNotIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldSecondaryClassification, vs...))
}

// SecondaryClassificationGT applies the GT predicate on the "secondary_classification" field.
func SecondaryClassificationGT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldSecondaryClassification, v))
}

// SecondaryClassificationGTE applies the GTE predicate on the "secondary_classification" field.
func SecondaryClassificationGTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldSecondaryClassification, v))
}

// SecondaryClassificationLT applies the LT predicate on the "secondary_classification" field.
func SecondaryClassificationLT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldSecondaryClassification, v))
}

// SecondaryClassificationLTE applies the LTE predicate on the "secondary_classification" field.
func SecondaryClassificationLTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldSecondaryClassification, v))
}

// SecondaryClassificationContains applies the Contains predicate on the "secondary_classification" field.
func SecondaryClassificationContains(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContains(FieldSecondaryClassification, v))
}

// SecondaryClassificationHasPrefix applies the HasPrefix predicate on the "secondary_classification" field.
func SecondaryClassificationHasPrefix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasPrefix(FieldSecondaryClassification, v))
}

// SecondaryClassificationHasSuffix applies the HasSuffix predicate on the "secondary_classification" field.
func SecondaryClassificationHasSuffix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasSuffix(FieldSecondaryClassification, v))
}

// SecondaryClassificationEqualFold applies the EqualFold predicate on the "secondary_classification" field.
func SecondaryClassificationEqualFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEqualFold(FieldSecondaryClassification, v))
}

// SecondaryClassificationContainsFold applies the ContainsFold predicate on the "secondary_classification" field.
func SecondaryClassificationContainsFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContainsFold(FieldSecondaryClassification, v))
}

// MineTypesEQ applies the EQ predicate on the "mine_types" field.
func MineTypesEQ(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldMineTypes, v))
}

// MineTypesNEQ applies the NEQ predicate on the "mine_types" field.
func MineTypesNEQ(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldMineTypes, v))
}

// MineTypesIn applies the In predicate on the "mine_types" field.
func MineTypesIn(vs ...*pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldMineTypes, vs...))
}

// MineTypesNotIn applies the NotIn predicate on the "mine_types" field.
func MineTypesNotIn(vs ...*pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldMineTypes, vs...))
}

// MineTypesGT applies the GT predicate on the "mine_types" field.
func MineTypesGT(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldMineTypes, v))
}

// MineTypesGTE applies the GTE predicate on the "mine_types" field.
func MineTypesGTE(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldMineTypes, v))
}

// MineTypesLT applies the LT predicate on the "mine_types" field.
func MineTypesLT(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldMineTypes, v))
}

// MineTypesLTE applies the LTE predicate on the "mine_types" field.
func MineTypesLTE(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldMineTypes, v))
}

// SuggestQuestionsEQ applies the EQ predicate on the "suggest_questions" field.
func SuggestQuestionsEQ(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldSuggestQuestions, v))
}

// SuggestQuestionsNEQ applies the NEQ predicate on the "suggest_questions" field.
func SuggestQuestionsNEQ(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldSuggestQuestions, v))
}

// SuggestQuestionsIn applies the In predicate on the "suggest_questions" field.
func SuggestQuestionsIn(vs ...*pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldSuggestQuestions, vs...))
}

// SuggestQuestionsNotIn applies the NotIn predicate on the "suggest_questions" field.
func SuggestQuestionsNotIn(vs ...*pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldSuggestQuestions, vs...))
}

// SuggestQuestionsGT applies the GT predicate on the "suggest_questions" field.
func SuggestQuestionsGT(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldSuggestQuestions, v))
}

// SuggestQuestionsGTE applies the GTE predicate on the "suggest_questions" field.
func SuggestQuestionsGTE(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldSuggestQuestions, v))
}

// SuggestQuestionsLT applies the LT predicate on the "suggest_questions" field.
func SuggestQuestionsLT(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldSuggestQuestions, v))
}

// SuggestQuestionsLTE applies the LTE predicate on the "suggest_questions" field.
func SuggestQuestionsLTE(v *pq.StringArray) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldSuggestQuestions, v))
}

// HitActionEQ applies the EQ predicate on the "hit_action" field.
func HitActionEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldHitAction, v))
}

// HitActionNEQ applies the NEQ predicate on the "hit_action" field.
func HitActionNEQ(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldHitAction, v))
}

// HitActionIn applies the In predicate on the "hit_action" field.
func HitActionIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldHitAction, vs...))
}

// HitActionNotIn applies the NotIn predicate on the "hit_action" field.
func HitActionNotIn(vs ...int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldHitAction, vs...))
}

// HitActionGT applies the GT predicate on the "hit_action" field.
func HitActionGT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldHitAction, v))
}

// HitActionGTE applies the GTE predicate on the "hit_action" field.
func HitActionGTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldHitAction, v))
}

// HitActionLT applies the LT predicate on the "hit_action" field.
func HitActionLT(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldHitAction, v))
}

// HitActionLTE applies the LTE predicate on the "hit_action" field.
func HitActionLTE(v int64) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldHitAction, v))
}

// HitResponseEQ applies the EQ predicate on the "hit_response" field.
func HitResponseEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldHitResponse, v))
}

// HitResponseNEQ applies the NEQ predicate on the "hit_response" field.
func HitResponseNEQ(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldHitResponse, v))
}

// HitResponseIn applies the In predicate on the "hit_response" field.
func HitResponseIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldIn(FieldHitResponse, vs...))
}

// HitResponseNotIn applies the NotIn predicate on the "hit_response" field.
func HitResponseNotIn(vs ...string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNotIn(FieldHitResponse, vs...))
}

// HitResponseGT applies the GT predicate on the "hit_response" field.
func HitResponseGT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGT(FieldHitResponse, v))
}

// HitResponseGTE applies the GTE predicate on the "hit_response" field.
func HitResponseGTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldGTE(FieldHitResponse, v))
}

// HitResponseLT applies the LT predicate on the "hit_response" field.
func HitResponseLT(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLT(FieldHitResponse, v))
}

// HitResponseLTE applies the LTE predicate on the "hit_response" field.
func HitResponseLTE(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldLTE(FieldHitResponse, v))
}

// HitResponseContains applies the Contains predicate on the "hit_response" field.
func HitResponseContains(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContains(FieldHitResponse, v))
}

// HitResponseHasPrefix applies the HasPrefix predicate on the "hit_response" field.
func HitResponseHasPrefix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasPrefix(FieldHitResponse, v))
}

// HitResponseHasSuffix applies the HasSuffix predicate on the "hit_response" field.
func HitResponseHasSuffix(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldHasSuffix(FieldHitResponse, v))
}

// HitResponseEqualFold applies the EqualFold predicate on the "hit_response" field.
func HitResponseEqualFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEqualFold(FieldHitResponse, v))
}

// HitResponseContainsFold applies the ContainsFold predicate on the "hit_response" field.
func HitResponseContainsFold(v string) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldContainsFold(FieldHitResponse, v))
}

// HitContinueSendEQ applies the EQ predicate on the "hit_continue_send" field.
func HitContinueSendEQ(v bool) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldHitContinueSend, v))
}

// HitContinueSendNEQ applies the NEQ predicate on the "hit_continue_send" field.
func HitContinueSendNEQ(v bool) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldHitContinueSend, v))
}

// IsInternetSearchEQ applies the EQ predicate on the "is_internet_search" field.
func IsInternetSearchEQ(v bool) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldEQ(FieldIsInternetSearch, v))
}

// IsInternetSearchNEQ applies the NEQ predicate on the "is_internet_search" field.
func IsInternetSearchNEQ(v bool) predicate.AiChatItem {
	return predicate.AiChatItem(sql.FieldNEQ(FieldIsInternetSearch, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AiChatItem) predicate.AiChatItem {
	return predicate.AiChatItem(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AiChatItem) predicate.AiChatItem {
	return predicate.AiChatItem(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AiChatItem) predicate.AiChatItem {
	return predicate.AiChatItem(sql.NotPredicates(p))
}

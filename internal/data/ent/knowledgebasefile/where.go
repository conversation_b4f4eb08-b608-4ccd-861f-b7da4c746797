// Code generated by ent, DO NOT EDIT.

package knowledgebasefile

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldDeletedAt, v))
}

// KnowledgeBaseID applies equality check predicate on the "knowledge_base_id" field. It's identical to KnowledgeBaseIDEQ.
func KnowledgeBaseID(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldKnowledgeBaseID, v))
}

// DataType applies equality check predicate on the "data_type" field. It's identical to DataTypeEQ.
func DataType(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldDataType, v))
}

// FileRelationID applies equality check predicate on the "file_relation_id" field. It's identical to FileRelationIDEQ.
func FileRelationID(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldFileRelationID, v))
}

// Metadata applies equality check predicate on the "metadata" field. It's identical to MetadataEQ.
func Metadata(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldMetadata, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldStatus, v))
}

// FailedReason applies equality check predicate on the "failed_reason" field. It's identical to FailedReasonEQ.
func FailedReason(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldFailedReason, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldTenantID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotNull(FieldDeletedAt))
}

// KnowledgeBaseIDEQ applies the EQ predicate on the "knowledge_base_id" field.
func KnowledgeBaseIDEQ(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldKnowledgeBaseID, v))
}

// KnowledgeBaseIDNEQ applies the NEQ predicate on the "knowledge_base_id" field.
func KnowledgeBaseIDNEQ(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldKnowledgeBaseID, v))
}

// KnowledgeBaseIDIn applies the In predicate on the "knowledge_base_id" field.
func KnowledgeBaseIDIn(vs ...int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldKnowledgeBaseID, vs...))
}

// KnowledgeBaseIDNotIn applies the NotIn predicate on the "knowledge_base_id" field.
func KnowledgeBaseIDNotIn(vs ...int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldKnowledgeBaseID, vs...))
}

// KnowledgeBaseIDGT applies the GT predicate on the "knowledge_base_id" field.
func KnowledgeBaseIDGT(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldKnowledgeBaseID, v))
}

// KnowledgeBaseIDGTE applies the GTE predicate on the "knowledge_base_id" field.
func KnowledgeBaseIDGTE(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldKnowledgeBaseID, v))
}

// KnowledgeBaseIDLT applies the LT predicate on the "knowledge_base_id" field.
func KnowledgeBaseIDLT(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldKnowledgeBaseID, v))
}

// KnowledgeBaseIDLTE applies the LTE predicate on the "knowledge_base_id" field.
func KnowledgeBaseIDLTE(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldKnowledgeBaseID, v))
}

// DataTypeEQ applies the EQ predicate on the "data_type" field.
func DataTypeEQ(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldDataType, v))
}

// DataTypeNEQ applies the NEQ predicate on the "data_type" field.
func DataTypeNEQ(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldDataType, v))
}

// DataTypeIn applies the In predicate on the "data_type" field.
func DataTypeIn(vs ...int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldDataType, vs...))
}

// DataTypeNotIn applies the NotIn predicate on the "data_type" field.
func DataTypeNotIn(vs ...int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldDataType, vs...))
}

// DataTypeGT applies the GT predicate on the "data_type" field.
func DataTypeGT(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldDataType, v))
}

// DataTypeGTE applies the GTE predicate on the "data_type" field.
func DataTypeGTE(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldDataType, v))
}

// DataTypeLT applies the LT predicate on the "data_type" field.
func DataTypeLT(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldDataType, v))
}

// DataTypeLTE applies the LTE predicate on the "data_type" field.
func DataTypeLTE(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldDataType, v))
}

// FileRelationIDEQ applies the EQ predicate on the "file_relation_id" field.
func FileRelationIDEQ(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldFileRelationID, v))
}

// FileRelationIDNEQ applies the NEQ predicate on the "file_relation_id" field.
func FileRelationIDNEQ(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldFileRelationID, v))
}

// FileRelationIDIn applies the In predicate on the "file_relation_id" field.
func FileRelationIDIn(vs ...int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldFileRelationID, vs...))
}

// FileRelationIDNotIn applies the NotIn predicate on the "file_relation_id" field.
func FileRelationIDNotIn(vs ...int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldFileRelationID, vs...))
}

// FileRelationIDGT applies the GT predicate on the "file_relation_id" field.
func FileRelationIDGT(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldFileRelationID, v))
}

// FileRelationIDGTE applies the GTE predicate on the "file_relation_id" field.
func FileRelationIDGTE(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldFileRelationID, v))
}

// FileRelationIDLT applies the LT predicate on the "file_relation_id" field.
func FileRelationIDLT(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldFileRelationID, v))
}

// FileRelationIDLTE applies the LTE predicate on the "file_relation_id" field.
func FileRelationIDLTE(v int64) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldFileRelationID, v))
}

// MetadataEQ applies the EQ predicate on the "metadata" field.
func MetadataEQ(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldMetadata, v))
}

// MetadataNEQ applies the NEQ predicate on the "metadata" field.
func MetadataNEQ(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldMetadata, v))
}

// MetadataIn applies the In predicate on the "metadata" field.
func MetadataIn(vs ...string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldMetadata, vs...))
}

// MetadataNotIn applies the NotIn predicate on the "metadata" field.
func MetadataNotIn(vs ...string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldMetadata, vs...))
}

// MetadataGT applies the GT predicate on the "metadata" field.
func MetadataGT(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldMetadata, v))
}

// MetadataGTE applies the GTE predicate on the "metadata" field.
func MetadataGTE(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldMetadata, v))
}

// MetadataLT applies the LT predicate on the "metadata" field.
func MetadataLT(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldMetadata, v))
}

// MetadataLTE applies the LTE predicate on the "metadata" field.
func MetadataLTE(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldMetadata, v))
}

// MetadataContains applies the Contains predicate on the "metadata" field.
func MetadataContains(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldContains(FieldMetadata, v))
}

// MetadataHasPrefix applies the HasPrefix predicate on the "metadata" field.
func MetadataHasPrefix(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldHasPrefix(FieldMetadata, v))
}

// MetadataHasSuffix applies the HasSuffix predicate on the "metadata" field.
func MetadataHasSuffix(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldHasSuffix(FieldMetadata, v))
}

// MetadataEqualFold applies the EqualFold predicate on the "metadata" field.
func MetadataEqualFold(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEqualFold(FieldMetadata, v))
}

// MetadataContainsFold applies the ContainsFold predicate on the "metadata" field.
func MetadataContainsFold(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldContainsFold(FieldMetadata, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int32) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldStatus, v))
}

// FailedReasonEQ applies the EQ predicate on the "failed_reason" field.
func FailedReasonEQ(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEQ(FieldFailedReason, v))
}

// FailedReasonNEQ applies the NEQ predicate on the "failed_reason" field.
func FailedReasonNEQ(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNEQ(FieldFailedReason, v))
}

// FailedReasonIn applies the In predicate on the "failed_reason" field.
func FailedReasonIn(vs ...string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldIn(FieldFailedReason, vs...))
}

// FailedReasonNotIn applies the NotIn predicate on the "failed_reason" field.
func FailedReasonNotIn(vs ...string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldNotIn(FieldFailedReason, vs...))
}

// FailedReasonGT applies the GT predicate on the "failed_reason" field.
func FailedReasonGT(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGT(FieldFailedReason, v))
}

// FailedReasonGTE applies the GTE predicate on the "failed_reason" field.
func FailedReasonGTE(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldGTE(FieldFailedReason, v))
}

// FailedReasonLT applies the LT predicate on the "failed_reason" field.
func FailedReasonLT(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLT(FieldFailedReason, v))
}

// FailedReasonLTE applies the LTE predicate on the "failed_reason" field.
func FailedReasonLTE(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldLTE(FieldFailedReason, v))
}

// FailedReasonContains applies the Contains predicate on the "failed_reason" field.
func FailedReasonContains(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldContains(FieldFailedReason, v))
}

// FailedReasonHasPrefix applies the HasPrefix predicate on the "failed_reason" field.
func FailedReasonHasPrefix(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldHasPrefix(FieldFailedReason, v))
}

// FailedReasonHasSuffix applies the HasSuffix predicate on the "failed_reason" field.
func FailedReasonHasSuffix(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldHasSuffix(FieldFailedReason, v))
}

// FailedReasonEqualFold applies the EqualFold predicate on the "failed_reason" field.
func FailedReasonEqualFold(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldEqualFold(FieldFailedReason, v))
}

// FailedReasonContainsFold applies the ContainsFold predicate on the "failed_reason" field.
func FailedReasonContainsFold(v string) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.FieldContainsFold(FieldFailedReason, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.KnowledgeBaseFile) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.KnowledgeBaseFile) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.KnowledgeBaseFile) predicate.KnowledgeBaseFile {
	return predicate.KnowledgeBaseFile(sql.NotPredicates(p))
}

// Code generated by ent, DO NOT EDIT.

package knowledgebasefile

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the knowledgebasefile type in the database.
	Label = "knowledge_base_file"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldKnowledgeBaseID holds the string denoting the knowledge_base_id field in the database.
	FieldKnowledgeBaseID = "knowledge_base_id"
	// FieldDataType holds the string denoting the data_type field in the database.
	FieldDataType = "data_type"
	// FieldFileRelationID holds the string denoting the file_relation_id field in the database.
	FieldFileRelationID = "file_relation_id"
	// FieldMetadata holds the string denoting the metadata field in the database.
	FieldMetadata = "metadata"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldFailedReason holds the string denoting the failed_reason field in the database.
	FieldFailedReason = "failed_reason"
	// Table holds the table name of the knowledgebasefile in the database.
	Table = "knowledge_base_file"
)

// Columns holds all SQL columns for knowledgebasefile fields.
var Columns = []string{
	FieldID,
	FieldTenantID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldKnowledgeBaseID,
	FieldDataType,
	FieldFileRelationID,
	FieldMetadata,
	FieldStatus,
	FieldFailedReason,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultDeletedAt holds the default value on creation for the "deleted_at" field.
	DefaultDeletedAt time.Time
	// DefaultKnowledgeBaseID holds the default value on creation for the "knowledge_base_id" field.
	DefaultKnowledgeBaseID int64
	// DefaultDataType holds the default value on creation for the "data_type" field.
	DefaultDataType int32
	// DefaultFileRelationID holds the default value on creation for the "file_relation_id" field.
	DefaultFileRelationID int64
	// DefaultMetadata holds the default value on creation for the "metadata" field.
	DefaultMetadata string
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int32
	// DefaultFailedReason holds the default value on creation for the "failed_reason" field.
	DefaultFailedReason string
)

// OrderOption defines the ordering options for the KnowledgeBaseFile queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByKnowledgeBaseID orders the results by the knowledge_base_id field.
func ByKnowledgeBaseID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKnowledgeBaseID, opts...).ToFunc()
}

// ByDataType orders the results by the data_type field.
func ByDataType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDataType, opts...).ToFunc()
}

// ByFileRelationID orders the results by the file_relation_id field.
func ByFileRelationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileRelationID, opts...).ToFunc()
}

// ByMetadata orders the results by the metadata field.
func ByMetadata(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMetadata, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByFailedReason orders the results by the failed_reason field.
func ByFailedReason(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFailedReason, opts...).ToFunc()
}

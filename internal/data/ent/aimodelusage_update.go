// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiModelUsageUpdate is the builder for updating AiModelUsage entities.
type AiModelUsageUpdate struct {
	config
	hooks     []Hook
	mutation  *AiModelUsageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AiModelUsageUpdate builder.
func (amuu *AiModelUsageUpdate) Where(ps ...predicate.AiModelUsage) *AiModelUsageUpdate {
	amuu.mutation.Where(ps...)
	return amuu
}

// SetUpdatedAt sets the "updated_at" field.
func (amuu *AiModelUsageUpdate) SetUpdatedAt(t time.Time) *AiModelUsageUpdate {
	amuu.mutation.SetUpdatedAt(t)
	return amuu
}

// SetDeletedAt sets the "deleted_at" field.
func (amuu *AiModelUsageUpdate) SetDeletedAt(t time.Time) *AiModelUsageUpdate {
	amuu.mutation.SetDeletedAt(t)
	return amuu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableDeletedAt(t *time.Time) *AiModelUsageUpdate {
	if t != nil {
		amuu.SetDeletedAt(*t)
	}
	return amuu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (amuu *AiModelUsageUpdate) ClearDeletedAt() *AiModelUsageUpdate {
	amuu.mutation.ClearDeletedAt()
	return amuu
}

// SetModelDetailID sets the "model_detail_id" field.
func (amuu *AiModelUsageUpdate) SetModelDetailID(i int64) *AiModelUsageUpdate {
	amuu.mutation.ResetModelDetailID()
	amuu.mutation.SetModelDetailID(i)
	return amuu
}

// SetNillableModelDetailID sets the "model_detail_id" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableModelDetailID(i *int64) *AiModelUsageUpdate {
	if i != nil {
		amuu.SetModelDetailID(*i)
	}
	return amuu
}

// AddModelDetailID adds i to the "model_detail_id" field.
func (amuu *AiModelUsageUpdate) AddModelDetailID(i int64) *AiModelUsageUpdate {
	amuu.mutation.AddModelDetailID(i)
	return amuu
}

// SetModelName sets the "model_name" field.
func (amuu *AiModelUsageUpdate) SetModelName(s string) *AiModelUsageUpdate {
	amuu.mutation.SetModelName(s)
	return amuu
}

// SetNillableModelName sets the "model_name" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableModelName(s *string) *AiModelUsageUpdate {
	if s != nil {
		amuu.SetModelName(*s)
	}
	return amuu
}

// SetModelGatewayName sets the "model_gateway_name" field.
func (amuu *AiModelUsageUpdate) SetModelGatewayName(s string) *AiModelUsageUpdate {
	amuu.mutation.SetModelGatewayName(s)
	return amuu
}

// SetNillableModelGatewayName sets the "model_gateway_name" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableModelGatewayName(s *string) *AiModelUsageUpdate {
	if s != nil {
		amuu.SetModelGatewayName(*s)
	}
	return amuu
}

// SetAgentID sets the "agent_id" field.
func (amuu *AiModelUsageUpdate) SetAgentID(i int64) *AiModelUsageUpdate {
	amuu.mutation.ResetAgentID()
	amuu.mutation.SetAgentID(i)
	return amuu
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableAgentID(i *int64) *AiModelUsageUpdate {
	if i != nil {
		amuu.SetAgentID(*i)
	}
	return amuu
}

// AddAgentID adds i to the "agent_id" field.
func (amuu *AiModelUsageUpdate) AddAgentID(i int64) *AiModelUsageUpdate {
	amuu.mutation.AddAgentID(i)
	return amuu
}

// SetAgentName sets the "agent_name" field.
func (amuu *AiModelUsageUpdate) SetAgentName(s string) *AiModelUsageUpdate {
	amuu.mutation.SetAgentName(s)
	return amuu
}

// SetNillableAgentName sets the "agent_name" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableAgentName(s *string) *AiModelUsageUpdate {
	if s != nil {
		amuu.SetAgentName(*s)
	}
	return amuu
}

// SetUserID sets the "user_id" field.
func (amuu *AiModelUsageUpdate) SetUserID(i int64) *AiModelUsageUpdate {
	amuu.mutation.ResetUserID()
	amuu.mutation.SetUserID(i)
	return amuu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableUserID(i *int64) *AiModelUsageUpdate {
	if i != nil {
		amuu.SetUserID(*i)
	}
	return amuu
}

// AddUserID adds i to the "user_id" field.
func (amuu *AiModelUsageUpdate) AddUserID(i int64) *AiModelUsageUpdate {
	amuu.mutation.AddUserID(i)
	return amuu
}

// SetUserName sets the "user_name" field.
func (amuu *AiModelUsageUpdate) SetUserName(s string) *AiModelUsageUpdate {
	amuu.mutation.SetUserName(s)
	return amuu
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableUserName(s *string) *AiModelUsageUpdate {
	if s != nil {
		amuu.SetUserName(*s)
	}
	return amuu
}

// SetQuestion sets the "question" field.
func (amuu *AiModelUsageUpdate) SetQuestion(s string) *AiModelUsageUpdate {
	amuu.mutation.SetQuestion(s)
	return amuu
}

// SetNillableQuestion sets the "question" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableQuestion(s *string) *AiModelUsageUpdate {
	if s != nil {
		amuu.SetQuestion(*s)
	}
	return amuu
}

// SetAnswer sets the "answer" field.
func (amuu *AiModelUsageUpdate) SetAnswer(s string) *AiModelUsageUpdate {
	amuu.mutation.SetAnswer(s)
	return amuu
}

// SetNillableAnswer sets the "answer" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableAnswer(s *string) *AiModelUsageUpdate {
	if s != nil {
		amuu.SetAnswer(*s)
	}
	return amuu
}

// SetPromptTokens sets the "prompt_tokens" field.
func (amuu *AiModelUsageUpdate) SetPromptTokens(i int64) *AiModelUsageUpdate {
	amuu.mutation.ResetPromptTokens()
	amuu.mutation.SetPromptTokens(i)
	return amuu
}

// SetNillablePromptTokens sets the "prompt_tokens" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillablePromptTokens(i *int64) *AiModelUsageUpdate {
	if i != nil {
		amuu.SetPromptTokens(*i)
	}
	return amuu
}

// AddPromptTokens adds i to the "prompt_tokens" field.
func (amuu *AiModelUsageUpdate) AddPromptTokens(i int64) *AiModelUsageUpdate {
	amuu.mutation.AddPromptTokens(i)
	return amuu
}

// SetCompletionTokens sets the "completion_tokens" field.
func (amuu *AiModelUsageUpdate) SetCompletionTokens(i int64) *AiModelUsageUpdate {
	amuu.mutation.ResetCompletionTokens()
	amuu.mutation.SetCompletionTokens(i)
	return amuu
}

// SetNillableCompletionTokens sets the "completion_tokens" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableCompletionTokens(i *int64) *AiModelUsageUpdate {
	if i != nil {
		amuu.SetCompletionTokens(*i)
	}
	return amuu
}

// AddCompletionTokens adds i to the "completion_tokens" field.
func (amuu *AiModelUsageUpdate) AddCompletionTokens(i int64) *AiModelUsageUpdate {
	amuu.mutation.AddCompletionTokens(i)
	return amuu
}

// SetRequestStatus sets the "request_status" field.
func (amuu *AiModelUsageUpdate) SetRequestStatus(i int8) *AiModelUsageUpdate {
	amuu.mutation.ResetRequestStatus()
	amuu.mutation.SetRequestStatus(i)
	return amuu
}

// SetNillableRequestStatus sets the "request_status" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableRequestStatus(i *int8) *AiModelUsageUpdate {
	if i != nil {
		amuu.SetRequestStatus(*i)
	}
	return amuu
}

// AddRequestStatus adds i to the "request_status" field.
func (amuu *AiModelUsageUpdate) AddRequestStatus(i int8) *AiModelUsageUpdate {
	amuu.mutation.AddRequestStatus(i)
	return amuu
}

// SetErrorCode sets the "error_code" field.
func (amuu *AiModelUsageUpdate) SetErrorCode(s string) *AiModelUsageUpdate {
	amuu.mutation.SetErrorCode(s)
	return amuu
}

// SetNillableErrorCode sets the "error_code" field if the given value is not nil.
func (amuu *AiModelUsageUpdate) SetNillableErrorCode(s *string) *AiModelUsageUpdate {
	if s != nil {
		amuu.SetErrorCode(*s)
	}
	return amuu
}

// Mutation returns the AiModelUsageMutation object of the builder.
func (amuu *AiModelUsageUpdate) Mutation() *AiModelUsageMutation {
	return amuu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (amuu *AiModelUsageUpdate) Save(ctx context.Context) (int, error) {
	if err := amuu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, amuu.sqlSave, amuu.mutation, amuu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (amuu *AiModelUsageUpdate) SaveX(ctx context.Context) int {
	affected, err := amuu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (amuu *AiModelUsageUpdate) Exec(ctx context.Context) error {
	_, err := amuu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amuu *AiModelUsageUpdate) ExecX(ctx context.Context) {
	if err := amuu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (amuu *AiModelUsageUpdate) defaults() error {
	if _, ok := amuu.mutation.UpdatedAt(); !ok {
		if aimodelusage.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodelusage.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aimodelusage.UpdateDefaultUpdatedAt()
		amuu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (amuu *AiModelUsageUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiModelUsageUpdate {
	amuu.modifiers = append(amuu.modifiers, modifiers...)
	return amuu
}

func (amuu *AiModelUsageUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(aimodelusage.Table, aimodelusage.Columns, sqlgraph.NewFieldSpec(aimodelusage.FieldID, field.TypeInt64))
	if ps := amuu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := amuu.mutation.UpdatedAt(); ok {
		_spec.SetField(aimodelusage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := amuu.mutation.DeletedAt(); ok {
		_spec.SetField(aimodelusage.FieldDeletedAt, field.TypeTime, value)
	}
	if amuu.mutation.DeletedAtCleared() {
		_spec.ClearField(aimodelusage.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := amuu.mutation.ModelDetailID(); ok {
		_spec.SetField(aimodelusage.FieldModelDetailID, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.AddedModelDetailID(); ok {
		_spec.AddField(aimodelusage.FieldModelDetailID, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.ModelName(); ok {
		_spec.SetField(aimodelusage.FieldModelName, field.TypeString, value)
	}
	if value, ok := amuu.mutation.ModelGatewayName(); ok {
		_spec.SetField(aimodelusage.FieldModelGatewayName, field.TypeString, value)
	}
	if value, ok := amuu.mutation.AgentID(); ok {
		_spec.SetField(aimodelusage.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.AddedAgentID(); ok {
		_spec.AddField(aimodelusage.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.AgentName(); ok {
		_spec.SetField(aimodelusage.FieldAgentName, field.TypeString, value)
	}
	if value, ok := amuu.mutation.UserID(); ok {
		_spec.SetField(aimodelusage.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.AddedUserID(); ok {
		_spec.AddField(aimodelusage.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.UserName(); ok {
		_spec.SetField(aimodelusage.FieldUserName, field.TypeString, value)
	}
	if value, ok := amuu.mutation.Question(); ok {
		_spec.SetField(aimodelusage.FieldQuestion, field.TypeString, value)
	}
	if value, ok := amuu.mutation.Answer(); ok {
		_spec.SetField(aimodelusage.FieldAnswer, field.TypeString, value)
	}
	if value, ok := amuu.mutation.PromptTokens(); ok {
		_spec.SetField(aimodelusage.FieldPromptTokens, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.AddedPromptTokens(); ok {
		_spec.AddField(aimodelusage.FieldPromptTokens, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.CompletionTokens(); ok {
		_spec.SetField(aimodelusage.FieldCompletionTokens, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.AddedCompletionTokens(); ok {
		_spec.AddField(aimodelusage.FieldCompletionTokens, field.TypeInt64, value)
	}
	if value, ok := amuu.mutation.RequestStatus(); ok {
		_spec.SetField(aimodelusage.FieldRequestStatus, field.TypeInt8, value)
	}
	if value, ok := amuu.mutation.AddedRequestStatus(); ok {
		_spec.AddField(aimodelusage.FieldRequestStatus, field.TypeInt8, value)
	}
	if value, ok := amuu.mutation.ErrorCode(); ok {
		_spec.SetField(aimodelusage.FieldErrorCode, field.TypeString, value)
	}
	_spec.AddModifiers(amuu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, amuu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aimodelusage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	amuu.mutation.done = true
	return n, nil
}

// AiModelUsageUpdateOne is the builder for updating a single AiModelUsage entity.
type AiModelUsageUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AiModelUsageMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (amuuo *AiModelUsageUpdateOne) SetUpdatedAt(t time.Time) *AiModelUsageUpdateOne {
	amuuo.mutation.SetUpdatedAt(t)
	return amuuo
}

// SetDeletedAt sets the "deleted_at" field.
func (amuuo *AiModelUsageUpdateOne) SetDeletedAt(t time.Time) *AiModelUsageUpdateOne {
	amuuo.mutation.SetDeletedAt(t)
	return amuuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableDeletedAt(t *time.Time) *AiModelUsageUpdateOne {
	if t != nil {
		amuuo.SetDeletedAt(*t)
	}
	return amuuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (amuuo *AiModelUsageUpdateOne) ClearDeletedAt() *AiModelUsageUpdateOne {
	amuuo.mutation.ClearDeletedAt()
	return amuuo
}

// SetModelDetailID sets the "model_detail_id" field.
func (amuuo *AiModelUsageUpdateOne) SetModelDetailID(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.ResetModelDetailID()
	amuuo.mutation.SetModelDetailID(i)
	return amuuo
}

// SetNillableModelDetailID sets the "model_detail_id" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableModelDetailID(i *int64) *AiModelUsageUpdateOne {
	if i != nil {
		amuuo.SetModelDetailID(*i)
	}
	return amuuo
}

// AddModelDetailID adds i to the "model_detail_id" field.
func (amuuo *AiModelUsageUpdateOne) AddModelDetailID(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.AddModelDetailID(i)
	return amuuo
}

// SetModelName sets the "model_name" field.
func (amuuo *AiModelUsageUpdateOne) SetModelName(s string) *AiModelUsageUpdateOne {
	amuuo.mutation.SetModelName(s)
	return amuuo
}

// SetNillableModelName sets the "model_name" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableModelName(s *string) *AiModelUsageUpdateOne {
	if s != nil {
		amuuo.SetModelName(*s)
	}
	return amuuo
}

// SetModelGatewayName sets the "model_gateway_name" field.
func (amuuo *AiModelUsageUpdateOne) SetModelGatewayName(s string) *AiModelUsageUpdateOne {
	amuuo.mutation.SetModelGatewayName(s)
	return amuuo
}

// SetNillableModelGatewayName sets the "model_gateway_name" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableModelGatewayName(s *string) *AiModelUsageUpdateOne {
	if s != nil {
		amuuo.SetModelGatewayName(*s)
	}
	return amuuo
}

// SetAgentID sets the "agent_id" field.
func (amuuo *AiModelUsageUpdateOne) SetAgentID(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.ResetAgentID()
	amuuo.mutation.SetAgentID(i)
	return amuuo
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableAgentID(i *int64) *AiModelUsageUpdateOne {
	if i != nil {
		amuuo.SetAgentID(*i)
	}
	return amuuo
}

// AddAgentID adds i to the "agent_id" field.
func (amuuo *AiModelUsageUpdateOne) AddAgentID(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.AddAgentID(i)
	return amuuo
}

// SetAgentName sets the "agent_name" field.
func (amuuo *AiModelUsageUpdateOne) SetAgentName(s string) *AiModelUsageUpdateOne {
	amuuo.mutation.SetAgentName(s)
	return amuuo
}

// SetNillableAgentName sets the "agent_name" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableAgentName(s *string) *AiModelUsageUpdateOne {
	if s != nil {
		amuuo.SetAgentName(*s)
	}
	return amuuo
}

// SetUserID sets the "user_id" field.
func (amuuo *AiModelUsageUpdateOne) SetUserID(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.ResetUserID()
	amuuo.mutation.SetUserID(i)
	return amuuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableUserID(i *int64) *AiModelUsageUpdateOne {
	if i != nil {
		amuuo.SetUserID(*i)
	}
	return amuuo
}

// AddUserID adds i to the "user_id" field.
func (amuuo *AiModelUsageUpdateOne) AddUserID(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.AddUserID(i)
	return amuuo
}

// SetUserName sets the "user_name" field.
func (amuuo *AiModelUsageUpdateOne) SetUserName(s string) *AiModelUsageUpdateOne {
	amuuo.mutation.SetUserName(s)
	return amuuo
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableUserName(s *string) *AiModelUsageUpdateOne {
	if s != nil {
		amuuo.SetUserName(*s)
	}
	return amuuo
}

// SetQuestion sets the "question" field.
func (amuuo *AiModelUsageUpdateOne) SetQuestion(s string) *AiModelUsageUpdateOne {
	amuuo.mutation.SetQuestion(s)
	return amuuo
}

// SetNillableQuestion sets the "question" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableQuestion(s *string) *AiModelUsageUpdateOne {
	if s != nil {
		amuuo.SetQuestion(*s)
	}
	return amuuo
}

// SetAnswer sets the "answer" field.
func (amuuo *AiModelUsageUpdateOne) SetAnswer(s string) *AiModelUsageUpdateOne {
	amuuo.mutation.SetAnswer(s)
	return amuuo
}

// SetNillableAnswer sets the "answer" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableAnswer(s *string) *AiModelUsageUpdateOne {
	if s != nil {
		amuuo.SetAnswer(*s)
	}
	return amuuo
}

// SetPromptTokens sets the "prompt_tokens" field.
func (amuuo *AiModelUsageUpdateOne) SetPromptTokens(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.ResetPromptTokens()
	amuuo.mutation.SetPromptTokens(i)
	return amuuo
}

// SetNillablePromptTokens sets the "prompt_tokens" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillablePromptTokens(i *int64) *AiModelUsageUpdateOne {
	if i != nil {
		amuuo.SetPromptTokens(*i)
	}
	return amuuo
}

// AddPromptTokens adds i to the "prompt_tokens" field.
func (amuuo *AiModelUsageUpdateOne) AddPromptTokens(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.AddPromptTokens(i)
	return amuuo
}

// SetCompletionTokens sets the "completion_tokens" field.
func (amuuo *AiModelUsageUpdateOne) SetCompletionTokens(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.ResetCompletionTokens()
	amuuo.mutation.SetCompletionTokens(i)
	return amuuo
}

// SetNillableCompletionTokens sets the "completion_tokens" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableCompletionTokens(i *int64) *AiModelUsageUpdateOne {
	if i != nil {
		amuuo.SetCompletionTokens(*i)
	}
	return amuuo
}

// AddCompletionTokens adds i to the "completion_tokens" field.
func (amuuo *AiModelUsageUpdateOne) AddCompletionTokens(i int64) *AiModelUsageUpdateOne {
	amuuo.mutation.AddCompletionTokens(i)
	return amuuo
}

// SetRequestStatus sets the "request_status" field.
func (amuuo *AiModelUsageUpdateOne) SetRequestStatus(i int8) *AiModelUsageUpdateOne {
	amuuo.mutation.ResetRequestStatus()
	amuuo.mutation.SetRequestStatus(i)
	return amuuo
}

// SetNillableRequestStatus sets the "request_status" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableRequestStatus(i *int8) *AiModelUsageUpdateOne {
	if i != nil {
		amuuo.SetRequestStatus(*i)
	}
	return amuuo
}

// AddRequestStatus adds i to the "request_status" field.
func (amuuo *AiModelUsageUpdateOne) AddRequestStatus(i int8) *AiModelUsageUpdateOne {
	amuuo.mutation.AddRequestStatus(i)
	return amuuo
}

// SetErrorCode sets the "error_code" field.
func (amuuo *AiModelUsageUpdateOne) SetErrorCode(s string) *AiModelUsageUpdateOne {
	amuuo.mutation.SetErrorCode(s)
	return amuuo
}

// SetNillableErrorCode sets the "error_code" field if the given value is not nil.
func (amuuo *AiModelUsageUpdateOne) SetNillableErrorCode(s *string) *AiModelUsageUpdateOne {
	if s != nil {
		amuuo.SetErrorCode(*s)
	}
	return amuuo
}

// Mutation returns the AiModelUsageMutation object of the builder.
func (amuuo *AiModelUsageUpdateOne) Mutation() *AiModelUsageMutation {
	return amuuo.mutation
}

// Where appends a list predicates to the AiModelUsageUpdate builder.
func (amuuo *AiModelUsageUpdateOne) Where(ps ...predicate.AiModelUsage) *AiModelUsageUpdateOne {
	amuuo.mutation.Where(ps...)
	return amuuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (amuuo *AiModelUsageUpdateOne) Select(field string, fields ...string) *AiModelUsageUpdateOne {
	amuuo.fields = append([]string{field}, fields...)
	return amuuo
}

// Save executes the query and returns the updated AiModelUsage entity.
func (amuuo *AiModelUsageUpdateOne) Save(ctx context.Context) (*AiModelUsage, error) {
	if err := amuuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, amuuo.sqlSave, amuuo.mutation, amuuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (amuuo *AiModelUsageUpdateOne) SaveX(ctx context.Context) *AiModelUsage {
	node, err := amuuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (amuuo *AiModelUsageUpdateOne) Exec(ctx context.Context) error {
	_, err := amuuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amuuo *AiModelUsageUpdateOne) ExecX(ctx context.Context) {
	if err := amuuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (amuuo *AiModelUsageUpdateOne) defaults() error {
	if _, ok := amuuo.mutation.UpdatedAt(); !ok {
		if aimodelusage.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodelusage.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aimodelusage.UpdateDefaultUpdatedAt()
		amuuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (amuuo *AiModelUsageUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiModelUsageUpdateOne {
	amuuo.modifiers = append(amuuo.modifiers, modifiers...)
	return amuuo
}

func (amuuo *AiModelUsageUpdateOne) sqlSave(ctx context.Context) (_node *AiModelUsage, err error) {
	_spec := sqlgraph.NewUpdateSpec(aimodelusage.Table, aimodelusage.Columns, sqlgraph.NewFieldSpec(aimodelusage.FieldID, field.TypeInt64))
	id, ok := amuuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AiModelUsage.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := amuuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aimodelusage.FieldID)
		for _, f := range fields {
			if !aimodelusage.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != aimodelusage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := amuuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := amuuo.mutation.UpdatedAt(); ok {
		_spec.SetField(aimodelusage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := amuuo.mutation.DeletedAt(); ok {
		_spec.SetField(aimodelusage.FieldDeletedAt, field.TypeTime, value)
	}
	if amuuo.mutation.DeletedAtCleared() {
		_spec.ClearField(aimodelusage.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := amuuo.mutation.ModelDetailID(); ok {
		_spec.SetField(aimodelusage.FieldModelDetailID, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.AddedModelDetailID(); ok {
		_spec.AddField(aimodelusage.FieldModelDetailID, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.ModelName(); ok {
		_spec.SetField(aimodelusage.FieldModelName, field.TypeString, value)
	}
	if value, ok := amuuo.mutation.ModelGatewayName(); ok {
		_spec.SetField(aimodelusage.FieldModelGatewayName, field.TypeString, value)
	}
	if value, ok := amuuo.mutation.AgentID(); ok {
		_spec.SetField(aimodelusage.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.AddedAgentID(); ok {
		_spec.AddField(aimodelusage.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.AgentName(); ok {
		_spec.SetField(aimodelusage.FieldAgentName, field.TypeString, value)
	}
	if value, ok := amuuo.mutation.UserID(); ok {
		_spec.SetField(aimodelusage.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.AddedUserID(); ok {
		_spec.AddField(aimodelusage.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.UserName(); ok {
		_spec.SetField(aimodelusage.FieldUserName, field.TypeString, value)
	}
	if value, ok := amuuo.mutation.Question(); ok {
		_spec.SetField(aimodelusage.FieldQuestion, field.TypeString, value)
	}
	if value, ok := amuuo.mutation.Answer(); ok {
		_spec.SetField(aimodelusage.FieldAnswer, field.TypeString, value)
	}
	if value, ok := amuuo.mutation.PromptTokens(); ok {
		_spec.SetField(aimodelusage.FieldPromptTokens, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.AddedPromptTokens(); ok {
		_spec.AddField(aimodelusage.FieldPromptTokens, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.CompletionTokens(); ok {
		_spec.SetField(aimodelusage.FieldCompletionTokens, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.AddedCompletionTokens(); ok {
		_spec.AddField(aimodelusage.FieldCompletionTokens, field.TypeInt64, value)
	}
	if value, ok := amuuo.mutation.RequestStatus(); ok {
		_spec.SetField(aimodelusage.FieldRequestStatus, field.TypeInt8, value)
	}
	if value, ok := amuuo.mutation.AddedRequestStatus(); ok {
		_spec.AddField(aimodelusage.FieldRequestStatus, field.TypeInt8, value)
	}
	if value, ok := amuuo.mutation.ErrorCode(); ok {
		_spec.SetField(aimodelusage.FieldErrorCode, field.TypeString, value)
	}
	_spec.AddModifiers(amuuo.modifiers...)
	_node = &AiModelUsage{config: amuuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, amuuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aimodelusage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	amuuo.mutation.done = true
	return _node, nil
}

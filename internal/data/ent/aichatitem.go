// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichatitem"
)

// AiChatItem is the model entity for the AiChatItem schema.
type AiChatItem struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 租户ID
	TenantID int64 `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// chat id
	ChatID int64 `json:"chat_id,omitempty"`
	// 消息对象id
	ObjectID int64 `json:"object_id,omitempty"`
	// 消息对象类型 0.系统 1.用户
	ObjectType int64 `json:"object_type,omitempty"`
	// 消息内容
	Message string `json:"message,omitempty"`
	// 引用文件
	RefFiles string `json:"ref_files,omitempty"`
	// 点赞状态 0.默认 1.点赞 2.批评
	AgreeStatus int8 `json:"agree_status,omitempty"`
	// 用于多轮对话
	RoundID int64 `json:"round_id,omitempty"`
	// 主机名称
	PcName string `json:"pc_name,omitempty"`
	// 深度思考内容
	Reason string `json:"reason,omitempty"`
	// 提问词一级分类
	PrimaryClassification string `json:"primary_classification,omitempty"`
	// 提问词二级分类
	SecondaryClassification string `json:"secondary_classification,omitempty"`
	// 引用文件类型列表
	MineTypes *pq.StringArray `json:"mine_types,omitempty"`
	// 建议问题
	SuggestQuestions *pq.StringArray `json:"suggest_questions,omitempty"`
	// 1.阻断 2.警告
	HitAction int64 `json:"hit_action,omitempty"`
	// 阻断或警告的响应内容
	HitResponse string `json:"hit_response,omitempty"`
	// 是否继续发送
	HitContinueSend bool `json:"hit_continue_send,omitempty"`
	// 是否互联网搜索
	IsInternetSearch bool `json:"is_internet_search,omitempty"`
	selectValues     sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AiChatItem) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case aichatitem.FieldMineTypes, aichatitem.FieldSuggestQuestions:
			values[i] = new(pq.StringArray)
		case aichatitem.FieldHitContinueSend, aichatitem.FieldIsInternetSearch:
			values[i] = new(sql.NullBool)
		case aichatitem.FieldID, aichatitem.FieldTenantID, aichatitem.FieldChatID, aichatitem.FieldObjectID, aichatitem.FieldObjectType, aichatitem.FieldAgreeStatus, aichatitem.FieldRoundID, aichatitem.FieldHitAction:
			values[i] = new(sql.NullInt64)
		case aichatitem.FieldMessage, aichatitem.FieldRefFiles, aichatitem.FieldPcName, aichatitem.FieldReason, aichatitem.FieldPrimaryClassification, aichatitem.FieldSecondaryClassification, aichatitem.FieldHitResponse:
			values[i] = new(sql.NullString)
		case aichatitem.FieldCreatedAt, aichatitem.FieldUpdatedAt, aichatitem.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AiChatItem fields.
func (aci *AiChatItem) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case aichatitem.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			aci.ID = int64(value.Int64)
		case aichatitem.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				aci.TenantID = value.Int64
			}
		case aichatitem.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				aci.CreatedAt = value.Time
			}
		case aichatitem.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				aci.UpdatedAt = value.Time
			}
		case aichatitem.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				aci.DeletedAt = value.Time
			}
		case aichatitem.FieldChatID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field chat_id", values[i])
			} else if value.Valid {
				aci.ChatID = value.Int64
			}
		case aichatitem.FieldObjectID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field object_id", values[i])
			} else if value.Valid {
				aci.ObjectID = value.Int64
			}
		case aichatitem.FieldObjectType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field object_type", values[i])
			} else if value.Valid {
				aci.ObjectType = value.Int64
			}
		case aichatitem.FieldMessage:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field message", values[i])
			} else if value.Valid {
				aci.Message = value.String
			}
		case aichatitem.FieldRefFiles:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ref_files", values[i])
			} else if value.Valid {
				aci.RefFiles = value.String
			}
		case aichatitem.FieldAgreeStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field agree_status", values[i])
			} else if value.Valid {
				aci.AgreeStatus = int8(value.Int64)
			}
		case aichatitem.FieldRoundID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field round_id", values[i])
			} else if value.Valid {
				aci.RoundID = value.Int64
			}
		case aichatitem.FieldPcName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field pc_name", values[i])
			} else if value.Valid {
				aci.PcName = value.String
			}
		case aichatitem.FieldReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field reason", values[i])
			} else if value.Valid {
				aci.Reason = value.String
			}
		case aichatitem.FieldPrimaryClassification:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field primary_classification", values[i])
			} else if value.Valid {
				aci.PrimaryClassification = value.String
			}
		case aichatitem.FieldSecondaryClassification:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field secondary_classification", values[i])
			} else if value.Valid {
				aci.SecondaryClassification = value.String
			}
		case aichatitem.FieldMineTypes:
			if value, ok := values[i].(*pq.StringArray); !ok {
				return fmt.Errorf("unexpected type %T for field mine_types", values[i])
			} else if value != nil {
				aci.MineTypes = value
			}
		case aichatitem.FieldSuggestQuestions:
			if value, ok := values[i].(*pq.StringArray); !ok {
				return fmt.Errorf("unexpected type %T for field suggest_questions", values[i])
			} else if value != nil {
				aci.SuggestQuestions = value
			}
		case aichatitem.FieldHitAction:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field hit_action", values[i])
			} else if value.Valid {
				aci.HitAction = value.Int64
			}
		case aichatitem.FieldHitResponse:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field hit_response", values[i])
			} else if value.Valid {
				aci.HitResponse = value.String
			}
		case aichatitem.FieldHitContinueSend:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field hit_continue_send", values[i])
			} else if value.Valid {
				aci.HitContinueSend = value.Bool
			}
		case aichatitem.FieldIsInternetSearch:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_internet_search", values[i])
			} else if value.Valid {
				aci.IsInternetSearch = value.Bool
			}
		default:
			aci.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AiChatItem.
// This includes values selected through modifiers, order, etc.
func (aci *AiChatItem) Value(name string) (ent.Value, error) {
	return aci.selectValues.Get(name)
}

// Update returns a builder for updating this AiChatItem.
// Note that you need to call AiChatItem.Unwrap() before calling this method if this AiChatItem
// was returned from a transaction, and the transaction was committed or rolled back.
func (aci *AiChatItem) Update() *AiChatItemUpdateOne {
	return NewAiChatItemClient(aci.config).UpdateOne(aci)
}

// Unwrap unwraps the AiChatItem entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (aci *AiChatItem) Unwrap() *AiChatItem {
	_tx, ok := aci.config.driver.(*txDriver)
	if !ok {
		panic("ent: AiChatItem is not a transactional entity")
	}
	aci.config.driver = _tx.drv
	return aci
}

// String implements the fmt.Stringer.
func (aci *AiChatItem) String() string {
	var builder strings.Builder
	builder.WriteString("AiChatItem(")
	builder.WriteString(fmt.Sprintf("id=%v, ", aci.ID))
	builder.WriteString("tenant_id=")
	builder.WriteString(fmt.Sprintf("%v", aci.TenantID))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(aci.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(aci.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(aci.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("chat_id=")
	builder.WriteString(fmt.Sprintf("%v", aci.ChatID))
	builder.WriteString(", ")
	builder.WriteString("object_id=")
	builder.WriteString(fmt.Sprintf("%v", aci.ObjectID))
	builder.WriteString(", ")
	builder.WriteString("object_type=")
	builder.WriteString(fmt.Sprintf("%v", aci.ObjectType))
	builder.WriteString(", ")
	builder.WriteString("message=")
	builder.WriteString(aci.Message)
	builder.WriteString(", ")
	builder.WriteString("ref_files=")
	builder.WriteString(aci.RefFiles)
	builder.WriteString(", ")
	builder.WriteString("agree_status=")
	builder.WriteString(fmt.Sprintf("%v", aci.AgreeStatus))
	builder.WriteString(", ")
	builder.WriteString("round_id=")
	builder.WriteString(fmt.Sprintf("%v", aci.RoundID))
	builder.WriteString(", ")
	builder.WriteString("pc_name=")
	builder.WriteString(aci.PcName)
	builder.WriteString(", ")
	builder.WriteString("reason=")
	builder.WriteString(aci.Reason)
	builder.WriteString(", ")
	builder.WriteString("primary_classification=")
	builder.WriteString(aci.PrimaryClassification)
	builder.WriteString(", ")
	builder.WriteString("secondary_classification=")
	builder.WriteString(aci.SecondaryClassification)
	builder.WriteString(", ")
	builder.WriteString("mine_types=")
	builder.WriteString(fmt.Sprintf("%v", aci.MineTypes))
	builder.WriteString(", ")
	builder.WriteString("suggest_questions=")
	builder.WriteString(fmt.Sprintf("%v", aci.SuggestQuestions))
	builder.WriteString(", ")
	builder.WriteString("hit_action=")
	builder.WriteString(fmt.Sprintf("%v", aci.HitAction))
	builder.WriteString(", ")
	builder.WriteString("hit_response=")
	builder.WriteString(aci.HitResponse)
	builder.WriteString(", ")
	builder.WriteString("hit_continue_send=")
	builder.WriteString(fmt.Sprintf("%v", aci.HitContinueSend))
	builder.WriteString(", ")
	builder.WriteString("is_internet_search=")
	builder.WriteString(fmt.Sprintf("%v", aci.IsInternetSearch))
	builder.WriteByte(')')
	return builder.String()
}

// AiChatItems is a parsable slice of AiChatItem.
type AiChatItems []*AiChatItem

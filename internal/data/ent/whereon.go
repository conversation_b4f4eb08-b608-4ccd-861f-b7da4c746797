// Code generated by ent, DO NOT EDIT.

package ent

import "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"

// WhereOn applies the given predicates on the query.
func (aa *AiAgentQuery) WhereOn(condition bool, ps ...predicate.AiAgent) *AiAgentQuery {
	if !condition {
		return aa
	}
	return aa.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (aasl *AiAgentSecurityLogQuery) WhereOn(condition bool, ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogQuery {
	if !condition {
		return aasl
	}
	return aasl.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (aasp *AiAgentSecurityPolicyQuery) WhereOn(condition bool, ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyQuery {
	if !condition {
		return aasp
	}
	return aasp.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (ac *AiChatQuery) WhereOn(condition bool, ps ...predicate.AiChat) *AiChatQuery {
	if !condition {
		return ac
	}
	return ac.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (aci *AiChatItemQuery) WhereOn(condition bool, ps ...predicate.AiChatItem) *AiChatItemQuery {
	if !condition {
		return aci
	}
	return aci.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (am *AiModelQuery) WhereOn(condition bool, ps ...predicate.AiModel) *AiModelQuery {
	if !condition {
		return am
	}
	return am.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (amd *AiModelDetailQuery) WhereOn(condition bool, ps ...predicate.AiModelDetail) *AiModelDetailQuery {
	if !condition {
		return amd
	}
	return amd.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (amu *AiModelUsageQuery) WhereOn(condition bool, ps ...predicate.AiModelUsage) *AiModelUsageQuery {
	if !condition {
		return amu
	}
	return amu.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (aq *AtomicQuestionsQuery) WhereOn(condition bool, ps ...predicate.AtomicQuestions) *AtomicQuestionsQuery {
	if !condition {
		return aq
	}
	return aq.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (cf *ClassificationFilesQuery) WhereOn(condition bool, ps ...predicate.ClassificationFiles) *ClassificationFilesQuery {
	if !condition {
		return cf
	}
	return cf.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (daa *DefaultAgentAvatarQuery) WhereOn(condition bool, ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarQuery {
	if !condition {
		return daa
	}
	return daa.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (emu *ExternalModelUsageQuery) WhereOn(condition bool, ps ...predicate.ExternalModelUsage) *ExternalModelUsageQuery {
	if !condition {
		return emu
	}
	return emu.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (kb *KnowledgeBaseQuery) WhereOn(condition bool, ps ...predicate.KnowledgeBase) *KnowledgeBaseQuery {
	if !condition {
		return kb
	}
	return kb.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (kbf *KnowledgeBaseFileQuery) WhereOn(condition bool, ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileQuery {
	if !condition {
		return kbf
	}
	return kbf.Where(ps...)
}

// WhereOn applies the given predicates on the query.
func (uao *UserAgentOrderQuery) WhereOn(condition bool, ps ...predicate.UserAgentOrder) *UserAgentOrderQuery {
	if !condition {
		return uao
	}
	return uao.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (aa *AiAgentUpdate) WhereOn(condition bool, ps ...predicate.AiAgent) *AiAgentUpdate {
	if !condition {
		return aa
	}
	return aa.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (aasl *AiAgentSecurityLogUpdate) WhereOn(condition bool, ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogUpdate {
	if !condition {
		return aasl
	}
	return aasl.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (aasp *AiAgentSecurityPolicyUpdate) WhereOn(condition bool, ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyUpdate {
	if !condition {
		return aasp
	}
	return aasp.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (ac *AiChatUpdate) WhereOn(condition bool, ps ...predicate.AiChat) *AiChatUpdate {
	if !condition {
		return ac
	}
	return ac.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (aci *AiChatItemUpdate) WhereOn(condition bool, ps ...predicate.AiChatItem) *AiChatItemUpdate {
	if !condition {
		return aci
	}
	return aci.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (am *AiModelUpdate) WhereOn(condition bool, ps ...predicate.AiModel) *AiModelUpdate {
	if !condition {
		return am
	}
	return am.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (amd *AiModelDetailUpdate) WhereOn(condition bool, ps ...predicate.AiModelDetail) *AiModelDetailUpdate {
	if !condition {
		return amd
	}
	return amd.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (amu *AiModelUsageUpdate) WhereOn(condition bool, ps ...predicate.AiModelUsage) *AiModelUsageUpdate {
	if !condition {
		return amu
	}
	return amu.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (aq *AtomicQuestionsUpdate) WhereOn(condition bool, ps ...predicate.AtomicQuestions) *AtomicQuestionsUpdate {
	if !condition {
		return aq
	}
	return aq.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (cf *ClassificationFilesUpdate) WhereOn(condition bool, ps ...predicate.ClassificationFiles) *ClassificationFilesUpdate {
	if !condition {
		return cf
	}
	return cf.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (daa *DefaultAgentAvatarUpdate) WhereOn(condition bool, ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarUpdate {
	if !condition {
		return daa
	}
	return daa.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (emu *ExternalModelUsageUpdate) WhereOn(condition bool, ps ...predicate.ExternalModelUsage) *ExternalModelUsageUpdate {
	if !condition {
		return emu
	}
	return emu.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (kb *KnowledgeBaseUpdate) WhereOn(condition bool, ps ...predicate.KnowledgeBase) *KnowledgeBaseUpdate {
	if !condition {
		return kb
	}
	return kb.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (kbf *KnowledgeBaseFileUpdate) WhereOn(condition bool, ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileUpdate {
	if !condition {
		return kbf
	}
	return kbf.Where(ps...)
}

// WhereOn applies the given predicates on the update.
func (uao *UserAgentOrderUpdate) WhereOn(condition bool, ps ...predicate.UserAgentOrder) *UserAgentOrderUpdate {
	if !condition {
		return uao
	}
	return uao.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (aa *AiAgentUpdateOne) WhereOn(condition bool, ps ...predicate.AiAgent) *AiAgentUpdateOne {
	if !condition {
		return aa
	}
	return aa.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (aasl *AiAgentSecurityLogUpdateOne) WhereOn(condition bool, ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogUpdateOne {
	if !condition {
		return aasl
	}
	return aasl.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (aasp *AiAgentSecurityPolicyUpdateOne) WhereOn(condition bool, ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyUpdateOne {
	if !condition {
		return aasp
	}
	return aasp.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (ac *AiChatUpdateOne) WhereOn(condition bool, ps ...predicate.AiChat) *AiChatUpdateOne {
	if !condition {
		return ac
	}
	return ac.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (aci *AiChatItemUpdateOne) WhereOn(condition bool, ps ...predicate.AiChatItem) *AiChatItemUpdateOne {
	if !condition {
		return aci
	}
	return aci.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (am *AiModelUpdateOne) WhereOn(condition bool, ps ...predicate.AiModel) *AiModelUpdateOne {
	if !condition {
		return am
	}
	return am.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (amd *AiModelDetailUpdateOne) WhereOn(condition bool, ps ...predicate.AiModelDetail) *AiModelDetailUpdateOne {
	if !condition {
		return amd
	}
	return amd.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (amu *AiModelUsageUpdateOne) WhereOn(condition bool, ps ...predicate.AiModelUsage) *AiModelUsageUpdateOne {
	if !condition {
		return amu
	}
	return amu.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (aq *AtomicQuestionsUpdateOne) WhereOn(condition bool, ps ...predicate.AtomicQuestions) *AtomicQuestionsUpdateOne {
	if !condition {
		return aq
	}
	return aq.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (cf *ClassificationFilesUpdateOne) WhereOn(condition bool, ps ...predicate.ClassificationFiles) *ClassificationFilesUpdateOne {
	if !condition {
		return cf
	}
	return cf.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (daa *DefaultAgentAvatarUpdateOne) WhereOn(condition bool, ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarUpdateOne {
	if !condition {
		return daa
	}
	return daa.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (emu *ExternalModelUsageUpdateOne) WhereOn(condition bool, ps ...predicate.ExternalModelUsage) *ExternalModelUsageUpdateOne {
	if !condition {
		return emu
	}
	return emu.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (kb *KnowledgeBaseUpdateOne) WhereOn(condition bool, ps ...predicate.KnowledgeBase) *KnowledgeBaseUpdateOne {
	if !condition {
		return kb
	}
	return kb.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (kbf *KnowledgeBaseFileUpdateOne) WhereOn(condition bool, ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileUpdateOne {
	if !condition {
		return kbf
	}
	return kbf.Where(ps...)
}

// WhereOn applies the given predicates on the updateOne.
func (uao *UserAgentOrderUpdateOne) WhereOn(condition bool, ps ...predicate.UserAgentOrder) *UserAgentOrderUpdateOne {
	if !condition {
		return uao
	}
	return uao.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (aa *AiAgentDelete) WhereOn(condition bool, ps ...predicate.AiAgent) *AiAgentDelete {
	if !condition {
		return aa
	}
	return aa.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (aasl *AiAgentSecurityLogDelete) WhereOn(condition bool, ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogDelete {
	if !condition {
		return aasl
	}
	return aasl.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (aasp *AiAgentSecurityPolicyDelete) WhereOn(condition bool, ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyDelete {
	if !condition {
		return aasp
	}
	return aasp.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (ac *AiChatDelete) WhereOn(condition bool, ps ...predicate.AiChat) *AiChatDelete {
	if !condition {
		return ac
	}
	return ac.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (aci *AiChatItemDelete) WhereOn(condition bool, ps ...predicate.AiChatItem) *AiChatItemDelete {
	if !condition {
		return aci
	}
	return aci.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (am *AiModelDelete) WhereOn(condition bool, ps ...predicate.AiModel) *AiModelDelete {
	if !condition {
		return am
	}
	return am.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (amd *AiModelDetailDelete) WhereOn(condition bool, ps ...predicate.AiModelDetail) *AiModelDetailDelete {
	if !condition {
		return amd
	}
	return amd.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (amu *AiModelUsageDelete) WhereOn(condition bool, ps ...predicate.AiModelUsage) *AiModelUsageDelete {
	if !condition {
		return amu
	}
	return amu.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (aq *AtomicQuestionsDelete) WhereOn(condition bool, ps ...predicate.AtomicQuestions) *AtomicQuestionsDelete {
	if !condition {
		return aq
	}
	return aq.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (cf *ClassificationFilesDelete) WhereOn(condition bool, ps ...predicate.ClassificationFiles) *ClassificationFilesDelete {
	if !condition {
		return cf
	}
	return cf.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (daa *DefaultAgentAvatarDelete) WhereOn(condition bool, ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarDelete {
	if !condition {
		return daa
	}
	return daa.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (emu *ExternalModelUsageDelete) WhereOn(condition bool, ps ...predicate.ExternalModelUsage) *ExternalModelUsageDelete {
	if !condition {
		return emu
	}
	return emu.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (kb *KnowledgeBaseDelete) WhereOn(condition bool, ps ...predicate.KnowledgeBase) *KnowledgeBaseDelete {
	if !condition {
		return kb
	}
	return kb.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (kbf *KnowledgeBaseFileDelete) WhereOn(condition bool, ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileDelete {
	if !condition {
		return kbf
	}
	return kbf.Where(ps...)
}

// WhereOn applies the given predicates on the delete.
func (uao *UserAgentOrderDelete) WhereOn(condition bool, ps ...predicate.UserAgentOrder) *UserAgentOrderDelete {
	if !condition {
		return uao
	}
	return uao.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (aa *AiAgentDeleteOne) WhereOn(condition bool, ps ...predicate.AiAgent) *AiAgentDeleteOne {
	if !condition {
		return aa
	}
	return aa.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (aasl *AiAgentSecurityLogDeleteOne) WhereOn(condition bool, ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogDeleteOne {
	if !condition {
		return aasl
	}
	return aasl.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (aasp *AiAgentSecurityPolicyDeleteOne) WhereOn(condition bool, ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyDeleteOne {
	if !condition {
		return aasp
	}
	return aasp.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (ac *AiChatDeleteOne) WhereOn(condition bool, ps ...predicate.AiChat) *AiChatDeleteOne {
	if !condition {
		return ac
	}
	return ac.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (aci *AiChatItemDeleteOne) WhereOn(condition bool, ps ...predicate.AiChatItem) *AiChatItemDeleteOne {
	if !condition {
		return aci
	}
	return aci.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (am *AiModelDeleteOne) WhereOn(condition bool, ps ...predicate.AiModel) *AiModelDeleteOne {
	if !condition {
		return am
	}
	return am.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (amd *AiModelDetailDeleteOne) WhereOn(condition bool, ps ...predicate.AiModelDetail) *AiModelDetailDeleteOne {
	if !condition {
		return amd
	}
	return amd.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (amu *AiModelUsageDeleteOne) WhereOn(condition bool, ps ...predicate.AiModelUsage) *AiModelUsageDeleteOne {
	if !condition {
		return amu
	}
	return amu.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (aq *AtomicQuestionsDeleteOne) WhereOn(condition bool, ps ...predicate.AtomicQuestions) *AtomicQuestionsDeleteOne {
	if !condition {
		return aq
	}
	return aq.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (cf *ClassificationFilesDeleteOne) WhereOn(condition bool, ps ...predicate.ClassificationFiles) *ClassificationFilesDeleteOne {
	if !condition {
		return cf
	}
	return cf.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (daa *DefaultAgentAvatarDeleteOne) WhereOn(condition bool, ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarDeleteOne {
	if !condition {
		return daa
	}
	return daa.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (emu *ExternalModelUsageDeleteOne) WhereOn(condition bool, ps ...predicate.ExternalModelUsage) *ExternalModelUsageDeleteOne {
	if !condition {
		return emu
	}
	return emu.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (kb *KnowledgeBaseDeleteOne) WhereOn(condition bool, ps ...predicate.KnowledgeBase) *KnowledgeBaseDeleteOne {
	if !condition {
		return kb
	}
	return kb.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (kbf *KnowledgeBaseFileDeleteOne) WhereOn(condition bool, ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileDeleteOne {
	if !condition {
		return kbf
	}
	return kbf.Where(ps...)
}

// WhereOn applies the given predicates on the deleteOne.
func (uao *UserAgentOrderDeleteOne) WhereOn(condition bool, ps ...predicate.UserAgentOrder) *UserAgentOrderDeleteOne {
	if !condition {
		return uao
	}
	return uao.Where(ps...)
}

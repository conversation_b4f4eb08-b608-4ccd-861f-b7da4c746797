// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebasefile"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// KnowledgeBaseFileUpdate is the builder for updating KnowledgeBaseFile entities.
type KnowledgeBaseFileUpdate struct {
	config
	hooks     []Hook
	mutation  *KnowledgeBaseFileMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the KnowledgeBaseFileUpdate builder.
func (kbfu *KnowledgeBaseFileUpdate) Where(ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileUpdate {
	kbfu.mutation.Where(ps...)
	return kbfu
}

// SetTenantID sets the "tenant_id" field.
func (kbfu *KnowledgeBaseFileUpdate) SetTenantID(i int64) *KnowledgeBaseFileUpdate {
	kbfu.mutation.ResetTenantID()
	kbfu.mutation.SetTenantID(i)
	return kbfu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (kbfu *KnowledgeBaseFileUpdate) SetNillableTenantID(i *int64) *KnowledgeBaseFileUpdate {
	if i != nil {
		kbfu.SetTenantID(*i)
	}
	return kbfu
}

// AddTenantID adds i to the "tenant_id" field.
func (kbfu *KnowledgeBaseFileUpdate) AddTenantID(i int64) *KnowledgeBaseFileUpdate {
	kbfu.mutation.AddTenantID(i)
	return kbfu
}

// SetUpdatedAt sets the "updated_at" field.
func (kbfu *KnowledgeBaseFileUpdate) SetUpdatedAt(t time.Time) *KnowledgeBaseFileUpdate {
	kbfu.mutation.SetUpdatedAt(t)
	return kbfu
}

// SetDeletedAt sets the "deleted_at" field.
func (kbfu *KnowledgeBaseFileUpdate) SetDeletedAt(t time.Time) *KnowledgeBaseFileUpdate {
	kbfu.mutation.SetDeletedAt(t)
	return kbfu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (kbfu *KnowledgeBaseFileUpdate) SetNillableDeletedAt(t *time.Time) *KnowledgeBaseFileUpdate {
	if t != nil {
		kbfu.SetDeletedAt(*t)
	}
	return kbfu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (kbfu *KnowledgeBaseFileUpdate) ClearDeletedAt() *KnowledgeBaseFileUpdate {
	kbfu.mutation.ClearDeletedAt()
	return kbfu
}

// SetKnowledgeBaseID sets the "knowledge_base_id" field.
func (kbfu *KnowledgeBaseFileUpdate) SetKnowledgeBaseID(i int64) *KnowledgeBaseFileUpdate {
	kbfu.mutation.ResetKnowledgeBaseID()
	kbfu.mutation.SetKnowledgeBaseID(i)
	return kbfu
}

// SetNillableKnowledgeBaseID sets the "knowledge_base_id" field if the given value is not nil.
func (kbfu *KnowledgeBaseFileUpdate) SetNillableKnowledgeBaseID(i *int64) *KnowledgeBaseFileUpdate {
	if i != nil {
		kbfu.SetKnowledgeBaseID(*i)
	}
	return kbfu
}

// AddKnowledgeBaseID adds i to the "knowledge_base_id" field.
func (kbfu *KnowledgeBaseFileUpdate) AddKnowledgeBaseID(i int64) *KnowledgeBaseFileUpdate {
	kbfu.mutation.AddKnowledgeBaseID(i)
	return kbfu
}

// SetDataType sets the "data_type" field.
func (kbfu *KnowledgeBaseFileUpdate) SetDataType(i int32) *KnowledgeBaseFileUpdate {
	kbfu.mutation.ResetDataType()
	kbfu.mutation.SetDataType(i)
	return kbfu
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (kbfu *KnowledgeBaseFileUpdate) SetNillableDataType(i *int32) *KnowledgeBaseFileUpdate {
	if i != nil {
		kbfu.SetDataType(*i)
	}
	return kbfu
}

// AddDataType adds i to the "data_type" field.
func (kbfu *KnowledgeBaseFileUpdate) AddDataType(i int32) *KnowledgeBaseFileUpdate {
	kbfu.mutation.AddDataType(i)
	return kbfu
}

// SetFileRelationID sets the "file_relation_id" field.
func (kbfu *KnowledgeBaseFileUpdate) SetFileRelationID(i int64) *KnowledgeBaseFileUpdate {
	kbfu.mutation.ResetFileRelationID()
	kbfu.mutation.SetFileRelationID(i)
	return kbfu
}

// SetNillableFileRelationID sets the "file_relation_id" field if the given value is not nil.
func (kbfu *KnowledgeBaseFileUpdate) SetNillableFileRelationID(i *int64) *KnowledgeBaseFileUpdate {
	if i != nil {
		kbfu.SetFileRelationID(*i)
	}
	return kbfu
}

// AddFileRelationID adds i to the "file_relation_id" field.
func (kbfu *KnowledgeBaseFileUpdate) AddFileRelationID(i int64) *KnowledgeBaseFileUpdate {
	kbfu.mutation.AddFileRelationID(i)
	return kbfu
}

// SetMetadata sets the "metadata" field.
func (kbfu *KnowledgeBaseFileUpdate) SetMetadata(s string) *KnowledgeBaseFileUpdate {
	kbfu.mutation.SetMetadata(s)
	return kbfu
}

// SetNillableMetadata sets the "metadata" field if the given value is not nil.
func (kbfu *KnowledgeBaseFileUpdate) SetNillableMetadata(s *string) *KnowledgeBaseFileUpdate {
	if s != nil {
		kbfu.SetMetadata(*s)
	}
	return kbfu
}

// SetStatus sets the "status" field.
func (kbfu *KnowledgeBaseFileUpdate) SetStatus(i int32) *KnowledgeBaseFileUpdate {
	kbfu.mutation.ResetStatus()
	kbfu.mutation.SetStatus(i)
	return kbfu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (kbfu *KnowledgeBaseFileUpdate) SetNillableStatus(i *int32) *KnowledgeBaseFileUpdate {
	if i != nil {
		kbfu.SetStatus(*i)
	}
	return kbfu
}

// AddStatus adds i to the "status" field.
func (kbfu *KnowledgeBaseFileUpdate) AddStatus(i int32) *KnowledgeBaseFileUpdate {
	kbfu.mutation.AddStatus(i)
	return kbfu
}

// SetFailedReason sets the "failed_reason" field.
func (kbfu *KnowledgeBaseFileUpdate) SetFailedReason(s string) *KnowledgeBaseFileUpdate {
	kbfu.mutation.SetFailedReason(s)
	return kbfu
}

// SetNillableFailedReason sets the "failed_reason" field if the given value is not nil.
func (kbfu *KnowledgeBaseFileUpdate) SetNillableFailedReason(s *string) *KnowledgeBaseFileUpdate {
	if s != nil {
		kbfu.SetFailedReason(*s)
	}
	return kbfu
}

// Mutation returns the KnowledgeBaseFileMutation object of the builder.
func (kbfu *KnowledgeBaseFileUpdate) Mutation() *KnowledgeBaseFileMutation {
	return kbfu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (kbfu *KnowledgeBaseFileUpdate) Save(ctx context.Context) (int, error) {
	if err := kbfu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, kbfu.sqlSave, kbfu.mutation, kbfu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (kbfu *KnowledgeBaseFileUpdate) SaveX(ctx context.Context) int {
	affected, err := kbfu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (kbfu *KnowledgeBaseFileUpdate) Exec(ctx context.Context) error {
	_, err := kbfu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (kbfu *KnowledgeBaseFileUpdate) ExecX(ctx context.Context) {
	if err := kbfu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (kbfu *KnowledgeBaseFileUpdate) defaults() error {
	if _, ok := kbfu.mutation.UpdatedAt(); !ok {
		if knowledgebasefile.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized knowledgebasefile.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := knowledgebasefile.UpdateDefaultUpdatedAt()
		kbfu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (kbfu *KnowledgeBaseFileUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *KnowledgeBaseFileUpdate {
	kbfu.modifiers = append(kbfu.modifiers, modifiers...)
	return kbfu
}

func (kbfu *KnowledgeBaseFileUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(knowledgebasefile.Table, knowledgebasefile.Columns, sqlgraph.NewFieldSpec(knowledgebasefile.FieldID, field.TypeInt64))
	if ps := kbfu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := kbfu.mutation.TenantID(); ok {
		_spec.SetField(knowledgebasefile.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := kbfu.mutation.AddedTenantID(); ok {
		_spec.AddField(knowledgebasefile.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := kbfu.mutation.UpdatedAt(); ok {
		_spec.SetField(knowledgebasefile.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := kbfu.mutation.DeletedAt(); ok {
		_spec.SetField(knowledgebasefile.FieldDeletedAt, field.TypeTime, value)
	}
	if kbfu.mutation.DeletedAtCleared() {
		_spec.ClearField(knowledgebasefile.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := kbfu.mutation.KnowledgeBaseID(); ok {
		_spec.SetField(knowledgebasefile.FieldKnowledgeBaseID, field.TypeInt64, value)
	}
	if value, ok := kbfu.mutation.AddedKnowledgeBaseID(); ok {
		_spec.AddField(knowledgebasefile.FieldKnowledgeBaseID, field.TypeInt64, value)
	}
	if value, ok := kbfu.mutation.DataType(); ok {
		_spec.SetField(knowledgebasefile.FieldDataType, field.TypeInt32, value)
	}
	if value, ok := kbfu.mutation.AddedDataType(); ok {
		_spec.AddField(knowledgebasefile.FieldDataType, field.TypeInt32, value)
	}
	if value, ok := kbfu.mutation.FileRelationID(); ok {
		_spec.SetField(knowledgebasefile.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := kbfu.mutation.AddedFileRelationID(); ok {
		_spec.AddField(knowledgebasefile.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := kbfu.mutation.Metadata(); ok {
		_spec.SetField(knowledgebasefile.FieldMetadata, field.TypeString, value)
	}
	if value, ok := kbfu.mutation.Status(); ok {
		_spec.SetField(knowledgebasefile.FieldStatus, field.TypeInt32, value)
	}
	if value, ok := kbfu.mutation.AddedStatus(); ok {
		_spec.AddField(knowledgebasefile.FieldStatus, field.TypeInt32, value)
	}
	if value, ok := kbfu.mutation.FailedReason(); ok {
		_spec.SetField(knowledgebasefile.FieldFailedReason, field.TypeString, value)
	}
	_spec.AddModifiers(kbfu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, kbfu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{knowledgebasefile.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	kbfu.mutation.done = true
	return n, nil
}

// KnowledgeBaseFileUpdateOne is the builder for updating a single KnowledgeBaseFile entity.
type KnowledgeBaseFileUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *KnowledgeBaseFileMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetTenantID sets the "tenant_id" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetTenantID(i int64) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.ResetTenantID()
	kbfuo.mutation.SetTenantID(i)
	return kbfuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetNillableTenantID(i *int64) *KnowledgeBaseFileUpdateOne {
	if i != nil {
		kbfuo.SetTenantID(*i)
	}
	return kbfuo
}

// AddTenantID adds i to the "tenant_id" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) AddTenantID(i int64) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.AddTenantID(i)
	return kbfuo
}

// SetUpdatedAt sets the "updated_at" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetUpdatedAt(t time.Time) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.SetUpdatedAt(t)
	return kbfuo
}

// SetDeletedAt sets the "deleted_at" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetDeletedAt(t time.Time) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.SetDeletedAt(t)
	return kbfuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetNillableDeletedAt(t *time.Time) *KnowledgeBaseFileUpdateOne {
	if t != nil {
		kbfuo.SetDeletedAt(*t)
	}
	return kbfuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) ClearDeletedAt() *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.ClearDeletedAt()
	return kbfuo
}

// SetKnowledgeBaseID sets the "knowledge_base_id" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetKnowledgeBaseID(i int64) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.ResetKnowledgeBaseID()
	kbfuo.mutation.SetKnowledgeBaseID(i)
	return kbfuo
}

// SetNillableKnowledgeBaseID sets the "knowledge_base_id" field if the given value is not nil.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetNillableKnowledgeBaseID(i *int64) *KnowledgeBaseFileUpdateOne {
	if i != nil {
		kbfuo.SetKnowledgeBaseID(*i)
	}
	return kbfuo
}

// AddKnowledgeBaseID adds i to the "knowledge_base_id" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) AddKnowledgeBaseID(i int64) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.AddKnowledgeBaseID(i)
	return kbfuo
}

// SetDataType sets the "data_type" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetDataType(i int32) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.ResetDataType()
	kbfuo.mutation.SetDataType(i)
	return kbfuo
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetNillableDataType(i *int32) *KnowledgeBaseFileUpdateOne {
	if i != nil {
		kbfuo.SetDataType(*i)
	}
	return kbfuo
}

// AddDataType adds i to the "data_type" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) AddDataType(i int32) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.AddDataType(i)
	return kbfuo
}

// SetFileRelationID sets the "file_relation_id" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetFileRelationID(i int64) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.ResetFileRelationID()
	kbfuo.mutation.SetFileRelationID(i)
	return kbfuo
}

// SetNillableFileRelationID sets the "file_relation_id" field if the given value is not nil.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetNillableFileRelationID(i *int64) *KnowledgeBaseFileUpdateOne {
	if i != nil {
		kbfuo.SetFileRelationID(*i)
	}
	return kbfuo
}

// AddFileRelationID adds i to the "file_relation_id" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) AddFileRelationID(i int64) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.AddFileRelationID(i)
	return kbfuo
}

// SetMetadata sets the "metadata" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetMetadata(s string) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.SetMetadata(s)
	return kbfuo
}

// SetNillableMetadata sets the "metadata" field if the given value is not nil.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetNillableMetadata(s *string) *KnowledgeBaseFileUpdateOne {
	if s != nil {
		kbfuo.SetMetadata(*s)
	}
	return kbfuo
}

// SetStatus sets the "status" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetStatus(i int32) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.ResetStatus()
	kbfuo.mutation.SetStatus(i)
	return kbfuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetNillableStatus(i *int32) *KnowledgeBaseFileUpdateOne {
	if i != nil {
		kbfuo.SetStatus(*i)
	}
	return kbfuo
}

// AddStatus adds i to the "status" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) AddStatus(i int32) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.AddStatus(i)
	return kbfuo
}

// SetFailedReason sets the "failed_reason" field.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetFailedReason(s string) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.SetFailedReason(s)
	return kbfuo
}

// SetNillableFailedReason sets the "failed_reason" field if the given value is not nil.
func (kbfuo *KnowledgeBaseFileUpdateOne) SetNillableFailedReason(s *string) *KnowledgeBaseFileUpdateOne {
	if s != nil {
		kbfuo.SetFailedReason(*s)
	}
	return kbfuo
}

// Mutation returns the KnowledgeBaseFileMutation object of the builder.
func (kbfuo *KnowledgeBaseFileUpdateOne) Mutation() *KnowledgeBaseFileMutation {
	return kbfuo.mutation
}

// Where appends a list predicates to the KnowledgeBaseFileUpdate builder.
func (kbfuo *KnowledgeBaseFileUpdateOne) Where(ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileUpdateOne {
	kbfuo.mutation.Where(ps...)
	return kbfuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (kbfuo *KnowledgeBaseFileUpdateOne) Select(field string, fields ...string) *KnowledgeBaseFileUpdateOne {
	kbfuo.fields = append([]string{field}, fields...)
	return kbfuo
}

// Save executes the query and returns the updated KnowledgeBaseFile entity.
func (kbfuo *KnowledgeBaseFileUpdateOne) Save(ctx context.Context) (*KnowledgeBaseFile, error) {
	if err := kbfuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, kbfuo.sqlSave, kbfuo.mutation, kbfuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (kbfuo *KnowledgeBaseFileUpdateOne) SaveX(ctx context.Context) *KnowledgeBaseFile {
	node, err := kbfuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (kbfuo *KnowledgeBaseFileUpdateOne) Exec(ctx context.Context) error {
	_, err := kbfuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (kbfuo *KnowledgeBaseFileUpdateOne) ExecX(ctx context.Context) {
	if err := kbfuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (kbfuo *KnowledgeBaseFileUpdateOne) defaults() error {
	if _, ok := kbfuo.mutation.UpdatedAt(); !ok {
		if knowledgebasefile.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized knowledgebasefile.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := knowledgebasefile.UpdateDefaultUpdatedAt()
		kbfuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (kbfuo *KnowledgeBaseFileUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *KnowledgeBaseFileUpdateOne {
	kbfuo.modifiers = append(kbfuo.modifiers, modifiers...)
	return kbfuo
}

func (kbfuo *KnowledgeBaseFileUpdateOne) sqlSave(ctx context.Context) (_node *KnowledgeBaseFile, err error) {
	_spec := sqlgraph.NewUpdateSpec(knowledgebasefile.Table, knowledgebasefile.Columns, sqlgraph.NewFieldSpec(knowledgebasefile.FieldID, field.TypeInt64))
	id, ok := kbfuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "KnowledgeBaseFile.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := kbfuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, knowledgebasefile.FieldID)
		for _, f := range fields {
			if !knowledgebasefile.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != knowledgebasefile.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := kbfuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := kbfuo.mutation.TenantID(); ok {
		_spec.SetField(knowledgebasefile.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := kbfuo.mutation.AddedTenantID(); ok {
		_spec.AddField(knowledgebasefile.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := kbfuo.mutation.UpdatedAt(); ok {
		_spec.SetField(knowledgebasefile.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := kbfuo.mutation.DeletedAt(); ok {
		_spec.SetField(knowledgebasefile.FieldDeletedAt, field.TypeTime, value)
	}
	if kbfuo.mutation.DeletedAtCleared() {
		_spec.ClearField(knowledgebasefile.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := kbfuo.mutation.KnowledgeBaseID(); ok {
		_spec.SetField(knowledgebasefile.FieldKnowledgeBaseID, field.TypeInt64, value)
	}
	if value, ok := kbfuo.mutation.AddedKnowledgeBaseID(); ok {
		_spec.AddField(knowledgebasefile.FieldKnowledgeBaseID, field.TypeInt64, value)
	}
	if value, ok := kbfuo.mutation.DataType(); ok {
		_spec.SetField(knowledgebasefile.FieldDataType, field.TypeInt32, value)
	}
	if value, ok := kbfuo.mutation.AddedDataType(); ok {
		_spec.AddField(knowledgebasefile.FieldDataType, field.TypeInt32, value)
	}
	if value, ok := kbfuo.mutation.FileRelationID(); ok {
		_spec.SetField(knowledgebasefile.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := kbfuo.mutation.AddedFileRelationID(); ok {
		_spec.AddField(knowledgebasefile.FieldFileRelationID, field.TypeInt64, value)
	}
	if value, ok := kbfuo.mutation.Metadata(); ok {
		_spec.SetField(knowledgebasefile.FieldMetadata, field.TypeString, value)
	}
	if value, ok := kbfuo.mutation.Status(); ok {
		_spec.SetField(knowledgebasefile.FieldStatus, field.TypeInt32, value)
	}
	if value, ok := kbfuo.mutation.AddedStatus(); ok {
		_spec.AddField(knowledgebasefile.FieldStatus, field.TypeInt32, value)
	}
	if value, ok := kbfuo.mutation.FailedReason(); ok {
		_spec.SetField(knowledgebasefile.FieldFailedReason, field.TypeString, value)
	}
	_spec.AddModifiers(kbfuo.modifiers...)
	_node = &KnowledgeBaseFile{config: kbfuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, kbfuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{knowledgebasefile.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	kbfuo.mutation.done = true
	return _node, nil
}

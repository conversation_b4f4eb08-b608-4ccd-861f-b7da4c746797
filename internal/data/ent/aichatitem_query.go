// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichatitem"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiChatItemQuery is the builder for querying AiChatItem entities.
type AiChatItemQuery struct {
	config
	ctx        *QueryContext
	order      []aichatitem.OrderOption
	inters     []Interceptor
	predicates []predicate.AiChatItem
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AiChatItemQuery builder.
func (aciq *AiChatItemQuery) Where(ps ...predicate.AiChatItem) *AiChatItemQuery {
	aciq.predicates = append(aciq.predicates, ps...)
	return aciq
}

// Limit the number of records to be returned by this query.
func (aciq *AiChatItemQuery) Limit(limit int) *AiChatItemQuery {
	aciq.ctx.Limit = &limit
	return aciq
}

// Offset to start from.
func (aciq *AiChatItemQuery) Offset(offset int) *AiChatItemQuery {
	aciq.ctx.Offset = &offset
	return aciq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (aciq *AiChatItemQuery) Unique(unique bool) *AiChatItemQuery {
	aciq.ctx.Unique = &unique
	return aciq
}

// Order specifies how the records should be ordered.
func (aciq *AiChatItemQuery) Order(o ...aichatitem.OrderOption) *AiChatItemQuery {
	aciq.order = append(aciq.order, o...)
	return aciq
}

// First returns the first AiChatItem entity from the query.
// Returns a *NotFoundError when no AiChatItem was found.
func (aciq *AiChatItemQuery) First(ctx context.Context) (*AiChatItem, error) {
	nodes, err := aciq.Limit(1).All(setContextOp(ctx, aciq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{aichatitem.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (aciq *AiChatItemQuery) FirstX(ctx context.Context) *AiChatItem {
	node, err := aciq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AiChatItem ID from the query.
// Returns a *NotFoundError when no AiChatItem ID was found.
func (aciq *AiChatItemQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = aciq.Limit(1).IDs(setContextOp(ctx, aciq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{aichatitem.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (aciq *AiChatItemQuery) FirstIDX(ctx context.Context) int64 {
	id, err := aciq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AiChatItem entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AiChatItem entity is found.
// Returns a *NotFoundError when no AiChatItem entities are found.
func (aciq *AiChatItemQuery) Only(ctx context.Context) (*AiChatItem, error) {
	nodes, err := aciq.Limit(2).All(setContextOp(ctx, aciq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{aichatitem.Label}
	default:
		return nil, &NotSingularError{aichatitem.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (aciq *AiChatItemQuery) OnlyX(ctx context.Context) *AiChatItem {
	node, err := aciq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AiChatItem ID in the query.
// Returns a *NotSingularError when more than one AiChatItem ID is found.
// Returns a *NotFoundError when no entities are found.
func (aciq *AiChatItemQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = aciq.Limit(2).IDs(setContextOp(ctx, aciq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{aichatitem.Label}
	default:
		err = &NotSingularError{aichatitem.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (aciq *AiChatItemQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := aciq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AiChatItems.
func (aciq *AiChatItemQuery) All(ctx context.Context) ([]*AiChatItem, error) {
	ctx = setContextOp(ctx, aciq.ctx, "All")
	if err := aciq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AiChatItem, *AiChatItemQuery]()
	return withInterceptors[[]*AiChatItem](ctx, aciq, qr, aciq.inters)
}

// AllX is like All, but panics if an error occurs.
func (aciq *AiChatItemQuery) AllX(ctx context.Context) []*AiChatItem {
	nodes, err := aciq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AiChatItem IDs.
func (aciq *AiChatItemQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if aciq.ctx.Unique == nil && aciq.path != nil {
		aciq.Unique(true)
	}
	ctx = setContextOp(ctx, aciq.ctx, "IDs")
	if err = aciq.Select(aichatitem.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (aciq *AiChatItemQuery) IDsX(ctx context.Context) []int64 {
	ids, err := aciq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (aciq *AiChatItemQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, aciq.ctx, "Count")
	if err := aciq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, aciq, querierCount[*AiChatItemQuery](), aciq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (aciq *AiChatItemQuery) CountX(ctx context.Context) int {
	count, err := aciq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (aciq *AiChatItemQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, aciq.ctx, "Exist")
	switch _, err := aciq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (aciq *AiChatItemQuery) ExistX(ctx context.Context) bool {
	exist, err := aciq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AiChatItemQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (aciq *AiChatItemQuery) Clone() *AiChatItemQuery {
	if aciq == nil {
		return nil
	}
	return &AiChatItemQuery{
		config:     aciq.config,
		ctx:        aciq.ctx.Clone(),
		order:      append([]aichatitem.OrderOption{}, aciq.order...),
		inters:     append([]Interceptor{}, aciq.inters...),
		predicates: append([]predicate.AiChatItem{}, aciq.predicates...),
		// clone intermediate query.
		sql:  aciq.sql.Clone(),
		path: aciq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AiChatItem.Query().
//		GroupBy(aichatitem.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (aciq *AiChatItemQuery) GroupBy(field string, fields ...string) *AiChatItemGroupBy {
	aciq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AiChatItemGroupBy{build: aciq}
	grbuild.flds = &aciq.ctx.Fields
	grbuild.label = aichatitem.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//	}
//
//	client.AiChatItem.Query().
//		Select(aichatitem.FieldTenantID).
//		Scan(ctx, &v)
func (aciq *AiChatItemQuery) Select(fields ...string) *AiChatItemSelect {
	aciq.ctx.Fields = append(aciq.ctx.Fields, fields...)
	sbuild := &AiChatItemSelect{AiChatItemQuery: aciq}
	sbuild.label = aichatitem.Label
	sbuild.flds, sbuild.scan = &aciq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AiChatItemSelect configured with the given aggregations.
func (aciq *AiChatItemQuery) Aggregate(fns ...AggregateFunc) *AiChatItemSelect {
	return aciq.Select().Aggregate(fns...)
}

func (aciq *AiChatItemQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range aciq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, aciq); err != nil {
				return err
			}
		}
	}
	for _, f := range aciq.ctx.Fields {
		if !aichatitem.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if aciq.path != nil {
		prev, err := aciq.path(ctx)
		if err != nil {
			return err
		}
		aciq.sql = prev
	}
	return nil
}

func (aciq *AiChatItemQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AiChatItem, error) {
	var (
		nodes = []*AiChatItem{}
		_spec = aciq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AiChatItem).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AiChatItem{config: aciq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(aciq.modifiers) > 0 {
		_spec.Modifiers = aciq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, aciq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (aciq *AiChatItemQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := aciq.querySpec()
	if len(aciq.modifiers) > 0 {
		_spec.Modifiers = aciq.modifiers
	}
	_spec.Node.Columns = aciq.ctx.Fields
	if len(aciq.ctx.Fields) > 0 {
		_spec.Unique = aciq.ctx.Unique != nil && *aciq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, aciq.driver, _spec)
}

func (aciq *AiChatItemQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(aichatitem.Table, aichatitem.Columns, sqlgraph.NewFieldSpec(aichatitem.FieldID, field.TypeInt64))
	_spec.From = aciq.sql
	if unique := aciq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if aciq.path != nil {
		_spec.Unique = true
	}
	if fields := aciq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aichatitem.FieldID)
		for i := range fields {
			if fields[i] != aichatitem.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := aciq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := aciq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := aciq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := aciq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (aciq *AiChatItemQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(aciq.driver.Dialect())
	t1 := builder.Table(aichatitem.Table)
	columns := aciq.ctx.Fields
	if len(columns) == 0 {
		columns = aichatitem.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if aciq.sql != nil {
		selector = aciq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if aciq.ctx.Unique != nil && *aciq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range aciq.modifiers {
		m(selector)
	}
	for _, p := range aciq.predicates {
		p(selector)
	}
	for _, p := range aciq.order {
		p(selector)
	}
	if offset := aciq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := aciq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (aciq *AiChatItemQuery) ForUpdate(opts ...sql.LockOption) *AiChatItemQuery {
	if aciq.driver.Dialect() == dialect.Postgres {
		aciq.Unique(false)
	}
	aciq.modifiers = append(aciq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return aciq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (aciq *AiChatItemQuery) ForShare(opts ...sql.LockOption) *AiChatItemQuery {
	if aciq.driver.Dialect() == dialect.Postgres {
		aciq.Unique(false)
	}
	aciq.modifiers = append(aciq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return aciq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (aciq *AiChatItemQuery) Modify(modifiers ...func(s *sql.Selector)) *AiChatItemSelect {
	aciq.modifiers = append(aciq.modifiers, modifiers...)
	return aciq.Select()
}

// AiChatItemGroupBy is the group-by builder for AiChatItem entities.
type AiChatItemGroupBy struct {
	selector
	build *AiChatItemQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (acigb *AiChatItemGroupBy) Aggregate(fns ...AggregateFunc) *AiChatItemGroupBy {
	acigb.fns = append(acigb.fns, fns...)
	return acigb
}

// Scan applies the selector query and scans the result into the given value.
func (acigb *AiChatItemGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, acigb.build.ctx, "GroupBy")
	if err := acigb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiChatItemQuery, *AiChatItemGroupBy](ctx, acigb.build, acigb, acigb.build.inters, v)
}

func (acigb *AiChatItemGroupBy) sqlScan(ctx context.Context, root *AiChatItemQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(acigb.fns))
	for _, fn := range acigb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*acigb.flds)+len(acigb.fns))
		for _, f := range *acigb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*acigb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := acigb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AiChatItemSelect is the builder for selecting fields of AiChatItem entities.
type AiChatItemSelect struct {
	*AiChatItemQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (acis *AiChatItemSelect) Aggregate(fns ...AggregateFunc) *AiChatItemSelect {
	acis.fns = append(acis.fns, fns...)
	return acis
}

// Scan applies the selector query and scans the result into the given value.
func (acis *AiChatItemSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, acis.ctx, "Select")
	if err := acis.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiChatItemQuery, *AiChatItemSelect](ctx, acis.AiChatItemQuery, acis, acis.inters, v)
}

func (acis *AiChatItemSelect) sqlScan(ctx context.Context, root *AiChatItemQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(acis.fns))
	for _, fn := range acis.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*acis.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := acis.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (acis *AiChatItemSelect) Modify(modifiers ...func(s *sql.Selector)) *AiChatItemSelect {
	acis.modifiers = append(acis.modifiers, modifiers...)
	return acis
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
)

// AiAgent is the model entity for the AiAgent schema.
type AiAgent struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 智能体名称
	Name string `json:"name,omitempty"`
	// 描述
	Description string `json:"description,omitempty"`
	// 头像
	Avatar string `json:"avatar,omitempty"`
	// 点击头像
	ClickedAvatar string `json:"clicked_avatar,omitempty"`
	// 欢迎语
	WelcomeMsg string `json:"welcome_msg,omitempty"`
	// 兜底回复
	FallbackMsg string `json:"fallback_msg,omitempty"`
	// 创建者ID
	OwnerID int64 `json:"owner_id,omitempty"`
	// 可见性对象类型
	VisibilityType int8 `json:"visibility_type,omitempty"`
	// 可管理用户id列表
	ManageableToUser *pq.Int64Array `json:"manageable_to_user,omitempty"`
	// 可见用户id列表
	VisibleToUser *pq.Int64Array `json:"visible_to_user,omitempty"`
	// 可见部门id列表
	VisibleToDept *pq.Int64Array `json:"visible_to_dept,omitempty"`
	// 知识库id列表
	KnowledgeBaseIds *pq.Int64Array `json:"knowledge_base_ids,omitempty"`
	// 智能体编排schema
	Schema string `json:"schema,omitempty"`
	// 是否公开
	IsPublic bool `json:"is_public,omitempty"`
	// 是否启用
	IsEnabled bool `json:"is_enabled,omitempty"`
	// 是否显示引用文件
	IsRefFiles bool `json:"is_ref_files,omitempty"`
	// 模型类型， 1：内部模型，2：外部模型
	ModelType int64 `json:"model_type,omitempty"`
	// 模型ID
	ModelID int64 `json:"model_id,omitempty"`
	// 使用次数
	UseCount int64 `json:"use_count,omitempty"`
	// 知识库类型，0：指定知识库 1：个人权限内的文件
	KnowledgeBaseType int64 `json:"knowledge_base_type,omitempty"`
	// 是否开启互联网搜索
	InternetSearch bool `json:"internet_search,omitempty"`
	// 智能体类型，1：基础问答 2：检索深度问答 3：合同审核
	AgentType int64 `json:"agent_type,omitempty"`
	// 是否开启思考
	Thinking bool `json:"thinking,omitempty"`
	// 思考模型ID
	ThinkingModelID int64 `json:"thinking_model_id,omitempty"`
	// 角色设定
	RoleSetting string `json:"role_setting,omitempty"`
	// 上传文件
	UploadFile bool `json:"upload_file,omitempty"`
	// 语义缓存
	SemanticCache bool `json:"semantic_cache,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the AiAgentQuery when eager-loading is set.
	Edges        AiAgentEdges `json:"edges"`
	selectValues sql.SelectValues
}

// AiAgentEdges holds the relations/edges for other nodes in the graph.
type AiAgentEdges struct {
	// AiChat holds the value of the ai_chat edge.
	AiChat []*AiChat `json:"ai_chat,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// AiChatOrErr returns the AiChat value or an error if the edge
// was not loaded in eager-loading.
func (e AiAgentEdges) AiChatOrErr() ([]*AiChat, error) {
	if e.loadedTypes[0] {
		return e.AiChat, nil
	}
	return nil, &NotLoadedError{edge: "ai_chat"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AiAgent) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case aiagent.FieldManageableToUser, aiagent.FieldVisibleToUser, aiagent.FieldVisibleToDept, aiagent.FieldKnowledgeBaseIds:
			values[i] = new(pq.Int64Array)
		case aiagent.FieldIsPublic, aiagent.FieldIsEnabled, aiagent.FieldIsRefFiles, aiagent.FieldInternetSearch, aiagent.FieldThinking, aiagent.FieldUploadFile, aiagent.FieldSemanticCache:
			values[i] = new(sql.NullBool)
		case aiagent.FieldID, aiagent.FieldOwnerID, aiagent.FieldVisibilityType, aiagent.FieldModelType, aiagent.FieldModelID, aiagent.FieldUseCount, aiagent.FieldKnowledgeBaseType, aiagent.FieldAgentType, aiagent.FieldThinkingModelID:
			values[i] = new(sql.NullInt64)
		case aiagent.FieldName, aiagent.FieldDescription, aiagent.FieldAvatar, aiagent.FieldClickedAvatar, aiagent.FieldWelcomeMsg, aiagent.FieldFallbackMsg, aiagent.FieldSchema, aiagent.FieldRoleSetting:
			values[i] = new(sql.NullString)
		case aiagent.FieldCreatedAt, aiagent.FieldUpdatedAt, aiagent.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AiAgent fields.
func (aa *AiAgent) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case aiagent.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			aa.ID = int64(value.Int64)
		case aiagent.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				aa.CreatedAt = value.Time
			}
		case aiagent.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				aa.UpdatedAt = value.Time
			}
		case aiagent.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				aa.DeletedAt = value.Time
			}
		case aiagent.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				aa.Name = value.String
			}
		case aiagent.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				aa.Description = value.String
			}
		case aiagent.FieldAvatar:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field avatar", values[i])
			} else if value.Valid {
				aa.Avatar = value.String
			}
		case aiagent.FieldClickedAvatar:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field clicked_avatar", values[i])
			} else if value.Valid {
				aa.ClickedAvatar = value.String
			}
		case aiagent.FieldWelcomeMsg:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field welcome_msg", values[i])
			} else if value.Valid {
				aa.WelcomeMsg = value.String
			}
		case aiagent.FieldFallbackMsg:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field fallback_msg", values[i])
			} else if value.Valid {
				aa.FallbackMsg = value.String
			}
		case aiagent.FieldOwnerID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field owner_id", values[i])
			} else if value.Valid {
				aa.OwnerID = value.Int64
			}
		case aiagent.FieldVisibilityType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field visibility_type", values[i])
			} else if value.Valid {
				aa.VisibilityType = int8(value.Int64)
			}
		case aiagent.FieldManageableToUser:
			if value, ok := values[i].(*pq.Int64Array); !ok {
				return fmt.Errorf("unexpected type %T for field manageable_to_user", values[i])
			} else if value != nil {
				aa.ManageableToUser = value
			}
		case aiagent.FieldVisibleToUser:
			if value, ok := values[i].(*pq.Int64Array); !ok {
				return fmt.Errorf("unexpected type %T for field visible_to_user", values[i])
			} else if value != nil {
				aa.VisibleToUser = value
			}
		case aiagent.FieldVisibleToDept:
			if value, ok := values[i].(*pq.Int64Array); !ok {
				return fmt.Errorf("unexpected type %T for field visible_to_dept", values[i])
			} else if value != nil {
				aa.VisibleToDept = value
			}
		case aiagent.FieldKnowledgeBaseIds:
			if value, ok := values[i].(*pq.Int64Array); !ok {
				return fmt.Errorf("unexpected type %T for field knowledge_base_ids", values[i])
			} else if value != nil {
				aa.KnowledgeBaseIds = value
			}
		case aiagent.FieldSchema:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field schema", values[i])
			} else if value.Valid {
				aa.Schema = value.String
			}
		case aiagent.FieldIsPublic:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_public", values[i])
			} else if value.Valid {
				aa.IsPublic = value.Bool
			}
		case aiagent.FieldIsEnabled:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_enabled", values[i])
			} else if value.Valid {
				aa.IsEnabled = value.Bool
			}
		case aiagent.FieldIsRefFiles:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_ref_files", values[i])
			} else if value.Valid {
				aa.IsRefFiles = value.Bool
			}
		case aiagent.FieldModelType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field model_type", values[i])
			} else if value.Valid {
				aa.ModelType = value.Int64
			}
		case aiagent.FieldModelID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field model_id", values[i])
			} else if value.Valid {
				aa.ModelID = value.Int64
			}
		case aiagent.FieldUseCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field use_count", values[i])
			} else if value.Valid {
				aa.UseCount = value.Int64
			}
		case aiagent.FieldKnowledgeBaseType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field knowledge_base_type", values[i])
			} else if value.Valid {
				aa.KnowledgeBaseType = value.Int64
			}
		case aiagent.FieldInternetSearch:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field internet_search", values[i])
			} else if value.Valid {
				aa.InternetSearch = value.Bool
			}
		case aiagent.FieldAgentType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field agent_type", values[i])
			} else if value.Valid {
				aa.AgentType = value.Int64
			}
		case aiagent.FieldThinking:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field thinking", values[i])
			} else if value.Valid {
				aa.Thinking = value.Bool
			}
		case aiagent.FieldThinkingModelID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field thinking_model_id", values[i])
			} else if value.Valid {
				aa.ThinkingModelID = value.Int64
			}
		case aiagent.FieldRoleSetting:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field role_setting", values[i])
			} else if value.Valid {
				aa.RoleSetting = value.String
			}
		case aiagent.FieldUploadFile:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field upload_file", values[i])
			} else if value.Valid {
				aa.UploadFile = value.Bool
			}
		case aiagent.FieldSemanticCache:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field semantic_cache", values[i])
			} else if value.Valid {
				aa.SemanticCache = value.Bool
			}
		default:
			aa.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AiAgent.
// This includes values selected through modifiers, order, etc.
func (aa *AiAgent) Value(name string) (ent.Value, error) {
	return aa.selectValues.Get(name)
}

// QueryAiChat queries the "ai_chat" edge of the AiAgent entity.
func (aa *AiAgent) QueryAiChat() *AiChatQuery {
	return NewAiAgentClient(aa.config).QueryAiChat(aa)
}

// Update returns a builder for updating this AiAgent.
// Note that you need to call AiAgent.Unwrap() before calling this method if this AiAgent
// was returned from a transaction, and the transaction was committed or rolled back.
func (aa *AiAgent) Update() *AiAgentUpdateOne {
	return NewAiAgentClient(aa.config).UpdateOne(aa)
}

// Unwrap unwraps the AiAgent entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (aa *AiAgent) Unwrap() *AiAgent {
	_tx, ok := aa.config.driver.(*txDriver)
	if !ok {
		panic("ent: AiAgent is not a transactional entity")
	}
	aa.config.driver = _tx.drv
	return aa
}

// String implements the fmt.Stringer.
func (aa *AiAgent) String() string {
	var builder strings.Builder
	builder.WriteString("AiAgent(")
	builder.WriteString(fmt.Sprintf("id=%v, ", aa.ID))
	builder.WriteString("created_at=")
	builder.WriteString(aa.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(aa.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(aa.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(aa.Name)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(aa.Description)
	builder.WriteString(", ")
	builder.WriteString("avatar=")
	builder.WriteString(aa.Avatar)
	builder.WriteString(", ")
	builder.WriteString("clicked_avatar=")
	builder.WriteString(aa.ClickedAvatar)
	builder.WriteString(", ")
	builder.WriteString("welcome_msg=")
	builder.WriteString(aa.WelcomeMsg)
	builder.WriteString(", ")
	builder.WriteString("fallback_msg=")
	builder.WriteString(aa.FallbackMsg)
	builder.WriteString(", ")
	builder.WriteString("owner_id=")
	builder.WriteString(fmt.Sprintf("%v", aa.OwnerID))
	builder.WriteString(", ")
	builder.WriteString("visibility_type=")
	builder.WriteString(fmt.Sprintf("%v", aa.VisibilityType))
	builder.WriteString(", ")
	builder.WriteString("manageable_to_user=")
	builder.WriteString(fmt.Sprintf("%v", aa.ManageableToUser))
	builder.WriteString(", ")
	builder.WriteString("visible_to_user=")
	builder.WriteString(fmt.Sprintf("%v", aa.VisibleToUser))
	builder.WriteString(", ")
	builder.WriteString("visible_to_dept=")
	builder.WriteString(fmt.Sprintf("%v", aa.VisibleToDept))
	builder.WriteString(", ")
	builder.WriteString("knowledge_base_ids=")
	builder.WriteString(fmt.Sprintf("%v", aa.KnowledgeBaseIds))
	builder.WriteString(", ")
	builder.WriteString("schema=")
	builder.WriteString(aa.Schema)
	builder.WriteString(", ")
	builder.WriteString("is_public=")
	builder.WriteString(fmt.Sprintf("%v", aa.IsPublic))
	builder.WriteString(", ")
	builder.WriteString("is_enabled=")
	builder.WriteString(fmt.Sprintf("%v", aa.IsEnabled))
	builder.WriteString(", ")
	builder.WriteString("is_ref_files=")
	builder.WriteString(fmt.Sprintf("%v", aa.IsRefFiles))
	builder.WriteString(", ")
	builder.WriteString("model_type=")
	builder.WriteString(fmt.Sprintf("%v", aa.ModelType))
	builder.WriteString(", ")
	builder.WriteString("model_id=")
	builder.WriteString(fmt.Sprintf("%v", aa.ModelID))
	builder.WriteString(", ")
	builder.WriteString("use_count=")
	builder.WriteString(fmt.Sprintf("%v", aa.UseCount))
	builder.WriteString(", ")
	builder.WriteString("knowledge_base_type=")
	builder.WriteString(fmt.Sprintf("%v", aa.KnowledgeBaseType))
	builder.WriteString(", ")
	builder.WriteString("internet_search=")
	builder.WriteString(fmt.Sprintf("%v", aa.InternetSearch))
	builder.WriteString(", ")
	builder.WriteString("agent_type=")
	builder.WriteString(fmt.Sprintf("%v", aa.AgentType))
	builder.WriteString(", ")
	builder.WriteString("thinking=")
	builder.WriteString(fmt.Sprintf("%v", aa.Thinking))
	builder.WriteString(", ")
	builder.WriteString("thinking_model_id=")
	builder.WriteString(fmt.Sprintf("%v", aa.ThinkingModelID))
	builder.WriteString(", ")
	builder.WriteString("role_setting=")
	builder.WriteString(aa.RoleSetting)
	builder.WriteString(", ")
	builder.WriteString("upload_file=")
	builder.WriteString(fmt.Sprintf("%v", aa.UploadFile))
	builder.WriteString(", ")
	builder.WriteString("semantic_cache=")
	builder.WriteString(fmt.Sprintf("%v", aa.SemanticCache))
	builder.WriteByte(')')
	return builder.String()
}

// AiAgents is a parsable slice of AiAgent.
type AiAgents []*AiAgent

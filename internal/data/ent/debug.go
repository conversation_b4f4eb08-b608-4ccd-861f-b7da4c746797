// Code generated by ent, DO NOT EDIT.

package ent

import "entgo.io/ent/dialect"

func (c *AiAgentClient) Debug() *AiAgentClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &AiAgentClient{config: cfg}
}

func (c *AiAgentSecurityLogClient) Debug() *AiAgentSecurityLogClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &AiAgentSecurityLogClient{config: cfg}
}

func (c *AiAgentSecurityPolicyClient) Debug() *AiAgentSecurityPolicyClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &AiAgentSecurityPolicyClient{config: cfg}
}

func (c *AiChatClient) Debug() *AiChatClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &AiChatClient{config: cfg}
}

func (c *AiChatItemClient) Debug() *AiChatItemClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &AiChatItemClient{config: cfg}
}

func (c *AiModelClient) Debug() *AiModelClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &AiModelClient{config: cfg}
}

func (c *AiModelDetailClient) Debug() *AiModelDetailClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &AiModelDetailClient{config: cfg}
}

func (c *AiModelUsageClient) Debug() *AiModelUsageClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &AiModelUsageClient{config: cfg}
}

func (c *AtomicQuestionsClient) Debug() *AtomicQuestionsClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &AtomicQuestionsClient{config: cfg}
}

func (c *ClassificationFilesClient) Debug() *ClassificationFilesClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &ClassificationFilesClient{config: cfg}
}

func (c *DefaultAgentAvatarClient) Debug() *DefaultAgentAvatarClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &DefaultAgentAvatarClient{config: cfg}
}

func (c *ExternalModelUsageClient) Debug() *ExternalModelUsageClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &ExternalModelUsageClient{config: cfg}
}

func (c *KnowledgeBaseClient) Debug() *KnowledgeBaseClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &KnowledgeBaseClient{config: cfg}
}

func (c *KnowledgeBaseFileClient) Debug() *KnowledgeBaseFileClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &KnowledgeBaseFileClient{config: cfg}
}

func (c *UserAgentOrderClient) Debug() *UserAgentOrderClient {
	if c.debug {
		return c
	}
	cfg := config{driver: dialect.Debug(c.driver, c.log), log: c.log, debug: true, hooks: c.hooks}
	return &UserAgentOrderClient{config: cfg}
}

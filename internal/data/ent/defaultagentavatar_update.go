// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/defaultagentavatar"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// DefaultAgentAvatarUpdate is the builder for updating DefaultAgentAvatar entities.
type DefaultAgentAvatarUpdate struct {
	config
	hooks     []Hook
	mutation  *DefaultAgentAvatarMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the DefaultAgentAvatarUpdate builder.
func (daau *DefaultAgentAvatarUpdate) Where(ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarUpdate {
	daau.mutation.Where(ps...)
	return daau
}

// SetTenantID sets the "tenant_id" field.
func (daau *DefaultAgentAvatarUpdate) SetTenantID(i int64) *DefaultAgentAvatarUpdate {
	daau.mutation.ResetTenantID()
	daau.mutation.SetTenantID(i)
	return daau
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (daau *DefaultAgentAvatarUpdate) SetNillableTenantID(i *int64) *DefaultAgentAvatarUpdate {
	if i != nil {
		daau.SetTenantID(*i)
	}
	return daau
}

// AddTenantID adds i to the "tenant_id" field.
func (daau *DefaultAgentAvatarUpdate) AddTenantID(i int64) *DefaultAgentAvatarUpdate {
	daau.mutation.AddTenantID(i)
	return daau
}

// SetUpdatedAt sets the "updated_at" field.
func (daau *DefaultAgentAvatarUpdate) SetUpdatedAt(t time.Time) *DefaultAgentAvatarUpdate {
	daau.mutation.SetUpdatedAt(t)
	return daau
}

// SetDeletedAt sets the "deleted_at" field.
func (daau *DefaultAgentAvatarUpdate) SetDeletedAt(t time.Time) *DefaultAgentAvatarUpdate {
	daau.mutation.SetDeletedAt(t)
	return daau
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (daau *DefaultAgentAvatarUpdate) SetNillableDeletedAt(t *time.Time) *DefaultAgentAvatarUpdate {
	if t != nil {
		daau.SetDeletedAt(*t)
	}
	return daau
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (daau *DefaultAgentAvatarUpdate) ClearDeletedAt() *DefaultAgentAvatarUpdate {
	daau.mutation.ClearDeletedAt()
	return daau
}

// SetAvatar sets the "avatar" field.
func (daau *DefaultAgentAvatarUpdate) SetAvatar(s string) *DefaultAgentAvatarUpdate {
	daau.mutation.SetAvatar(s)
	return daau
}

// SetNillableAvatar sets the "avatar" field if the given value is not nil.
func (daau *DefaultAgentAvatarUpdate) SetNillableAvatar(s *string) *DefaultAgentAvatarUpdate {
	if s != nil {
		daau.SetAvatar(*s)
	}
	return daau
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (daau *DefaultAgentAvatarUpdate) SetClickedAvatar(s string) *DefaultAgentAvatarUpdate {
	daau.mutation.SetClickedAvatar(s)
	return daau
}

// SetNillableClickedAvatar sets the "clicked_avatar" field if the given value is not nil.
func (daau *DefaultAgentAvatarUpdate) SetNillableClickedAvatar(s *string) *DefaultAgentAvatarUpdate {
	if s != nil {
		daau.SetClickedAvatar(*s)
	}
	return daau
}

// SetAvatarType sets the "avatar_type" field.
func (daau *DefaultAgentAvatarUpdate) SetAvatarType(i int8) *DefaultAgentAvatarUpdate {
	daau.mutation.ResetAvatarType()
	daau.mutation.SetAvatarType(i)
	return daau
}

// SetNillableAvatarType sets the "avatar_type" field if the given value is not nil.
func (daau *DefaultAgentAvatarUpdate) SetNillableAvatarType(i *int8) *DefaultAgentAvatarUpdate {
	if i != nil {
		daau.SetAvatarType(*i)
	}
	return daau
}

// AddAvatarType adds i to the "avatar_type" field.
func (daau *DefaultAgentAvatarUpdate) AddAvatarType(i int8) *DefaultAgentAvatarUpdate {
	daau.mutation.AddAvatarType(i)
	return daau
}

// Mutation returns the DefaultAgentAvatarMutation object of the builder.
func (daau *DefaultAgentAvatarUpdate) Mutation() *DefaultAgentAvatarMutation {
	return daau.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (daau *DefaultAgentAvatarUpdate) Save(ctx context.Context) (int, error) {
	if err := daau.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, daau.sqlSave, daau.mutation, daau.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (daau *DefaultAgentAvatarUpdate) SaveX(ctx context.Context) int {
	affected, err := daau.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (daau *DefaultAgentAvatarUpdate) Exec(ctx context.Context) error {
	_, err := daau.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (daau *DefaultAgentAvatarUpdate) ExecX(ctx context.Context) {
	if err := daau.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (daau *DefaultAgentAvatarUpdate) defaults() error {
	if _, ok := daau.mutation.UpdatedAt(); !ok {
		if defaultagentavatar.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized defaultagentavatar.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := defaultagentavatar.UpdateDefaultUpdatedAt()
		daau.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (daau *DefaultAgentAvatarUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DefaultAgentAvatarUpdate {
	daau.modifiers = append(daau.modifiers, modifiers...)
	return daau
}

func (daau *DefaultAgentAvatarUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(defaultagentavatar.Table, defaultagentavatar.Columns, sqlgraph.NewFieldSpec(defaultagentavatar.FieldID, field.TypeInt64))
	if ps := daau.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := daau.mutation.TenantID(); ok {
		_spec.SetField(defaultagentavatar.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := daau.mutation.AddedTenantID(); ok {
		_spec.AddField(defaultagentavatar.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := daau.mutation.UpdatedAt(); ok {
		_spec.SetField(defaultagentavatar.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := daau.mutation.DeletedAt(); ok {
		_spec.SetField(defaultagentavatar.FieldDeletedAt, field.TypeTime, value)
	}
	if daau.mutation.DeletedAtCleared() {
		_spec.ClearField(defaultagentavatar.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := daau.mutation.Avatar(); ok {
		_spec.SetField(defaultagentavatar.FieldAvatar, field.TypeString, value)
	}
	if value, ok := daau.mutation.ClickedAvatar(); ok {
		_spec.SetField(defaultagentavatar.FieldClickedAvatar, field.TypeString, value)
	}
	if value, ok := daau.mutation.AvatarType(); ok {
		_spec.SetField(defaultagentavatar.FieldAvatarType, field.TypeInt8, value)
	}
	if value, ok := daau.mutation.AddedAvatarType(); ok {
		_spec.AddField(defaultagentavatar.FieldAvatarType, field.TypeInt8, value)
	}
	_spec.AddModifiers(daau.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, daau.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{defaultagentavatar.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	daau.mutation.done = true
	return n, nil
}

// DefaultAgentAvatarUpdateOne is the builder for updating a single DefaultAgentAvatar entity.
type DefaultAgentAvatarUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *DefaultAgentAvatarMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetTenantID sets the "tenant_id" field.
func (daauo *DefaultAgentAvatarUpdateOne) SetTenantID(i int64) *DefaultAgentAvatarUpdateOne {
	daauo.mutation.ResetTenantID()
	daauo.mutation.SetTenantID(i)
	return daauo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (daauo *DefaultAgentAvatarUpdateOne) SetNillableTenantID(i *int64) *DefaultAgentAvatarUpdateOne {
	if i != nil {
		daauo.SetTenantID(*i)
	}
	return daauo
}

// AddTenantID adds i to the "tenant_id" field.
func (daauo *DefaultAgentAvatarUpdateOne) AddTenantID(i int64) *DefaultAgentAvatarUpdateOne {
	daauo.mutation.AddTenantID(i)
	return daauo
}

// SetUpdatedAt sets the "updated_at" field.
func (daauo *DefaultAgentAvatarUpdateOne) SetUpdatedAt(t time.Time) *DefaultAgentAvatarUpdateOne {
	daauo.mutation.SetUpdatedAt(t)
	return daauo
}

// SetDeletedAt sets the "deleted_at" field.
func (daauo *DefaultAgentAvatarUpdateOne) SetDeletedAt(t time.Time) *DefaultAgentAvatarUpdateOne {
	daauo.mutation.SetDeletedAt(t)
	return daauo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (daauo *DefaultAgentAvatarUpdateOne) SetNillableDeletedAt(t *time.Time) *DefaultAgentAvatarUpdateOne {
	if t != nil {
		daauo.SetDeletedAt(*t)
	}
	return daauo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (daauo *DefaultAgentAvatarUpdateOne) ClearDeletedAt() *DefaultAgentAvatarUpdateOne {
	daauo.mutation.ClearDeletedAt()
	return daauo
}

// SetAvatar sets the "avatar" field.
func (daauo *DefaultAgentAvatarUpdateOne) SetAvatar(s string) *DefaultAgentAvatarUpdateOne {
	daauo.mutation.SetAvatar(s)
	return daauo
}

// SetNillableAvatar sets the "avatar" field if the given value is not nil.
func (daauo *DefaultAgentAvatarUpdateOne) SetNillableAvatar(s *string) *DefaultAgentAvatarUpdateOne {
	if s != nil {
		daauo.SetAvatar(*s)
	}
	return daauo
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (daauo *DefaultAgentAvatarUpdateOne) SetClickedAvatar(s string) *DefaultAgentAvatarUpdateOne {
	daauo.mutation.SetClickedAvatar(s)
	return daauo
}

// SetNillableClickedAvatar sets the "clicked_avatar" field if the given value is not nil.
func (daauo *DefaultAgentAvatarUpdateOne) SetNillableClickedAvatar(s *string) *DefaultAgentAvatarUpdateOne {
	if s != nil {
		daauo.SetClickedAvatar(*s)
	}
	return daauo
}

// SetAvatarType sets the "avatar_type" field.
func (daauo *DefaultAgentAvatarUpdateOne) SetAvatarType(i int8) *DefaultAgentAvatarUpdateOne {
	daauo.mutation.ResetAvatarType()
	daauo.mutation.SetAvatarType(i)
	return daauo
}

// SetNillableAvatarType sets the "avatar_type" field if the given value is not nil.
func (daauo *DefaultAgentAvatarUpdateOne) SetNillableAvatarType(i *int8) *DefaultAgentAvatarUpdateOne {
	if i != nil {
		daauo.SetAvatarType(*i)
	}
	return daauo
}

// AddAvatarType adds i to the "avatar_type" field.
func (daauo *DefaultAgentAvatarUpdateOne) AddAvatarType(i int8) *DefaultAgentAvatarUpdateOne {
	daauo.mutation.AddAvatarType(i)
	return daauo
}

// Mutation returns the DefaultAgentAvatarMutation object of the builder.
func (daauo *DefaultAgentAvatarUpdateOne) Mutation() *DefaultAgentAvatarMutation {
	return daauo.mutation
}

// Where appends a list predicates to the DefaultAgentAvatarUpdate builder.
func (daauo *DefaultAgentAvatarUpdateOne) Where(ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarUpdateOne {
	daauo.mutation.Where(ps...)
	return daauo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (daauo *DefaultAgentAvatarUpdateOne) Select(field string, fields ...string) *DefaultAgentAvatarUpdateOne {
	daauo.fields = append([]string{field}, fields...)
	return daauo
}

// Save executes the query and returns the updated DefaultAgentAvatar entity.
func (daauo *DefaultAgentAvatarUpdateOne) Save(ctx context.Context) (*DefaultAgentAvatar, error) {
	if err := daauo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, daauo.sqlSave, daauo.mutation, daauo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (daauo *DefaultAgentAvatarUpdateOne) SaveX(ctx context.Context) *DefaultAgentAvatar {
	node, err := daauo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (daauo *DefaultAgentAvatarUpdateOne) Exec(ctx context.Context) error {
	_, err := daauo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (daauo *DefaultAgentAvatarUpdateOne) ExecX(ctx context.Context) {
	if err := daauo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (daauo *DefaultAgentAvatarUpdateOne) defaults() error {
	if _, ok := daauo.mutation.UpdatedAt(); !ok {
		if defaultagentavatar.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized defaultagentavatar.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := defaultagentavatar.UpdateDefaultUpdatedAt()
		daauo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (daauo *DefaultAgentAvatarUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *DefaultAgentAvatarUpdateOne {
	daauo.modifiers = append(daauo.modifiers, modifiers...)
	return daauo
}

func (daauo *DefaultAgentAvatarUpdateOne) sqlSave(ctx context.Context) (_node *DefaultAgentAvatar, err error) {
	_spec := sqlgraph.NewUpdateSpec(defaultagentavatar.Table, defaultagentavatar.Columns, sqlgraph.NewFieldSpec(defaultagentavatar.FieldID, field.TypeInt64))
	id, ok := daauo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "DefaultAgentAvatar.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := daauo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, defaultagentavatar.FieldID)
		for _, f := range fields {
			if !defaultagentavatar.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != defaultagentavatar.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := daauo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := daauo.mutation.TenantID(); ok {
		_spec.SetField(defaultagentavatar.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := daauo.mutation.AddedTenantID(); ok {
		_spec.AddField(defaultagentavatar.FieldTenantID, field.TypeInt64, value)
	}
	if value, ok := daauo.mutation.UpdatedAt(); ok {
		_spec.SetField(defaultagentavatar.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := daauo.mutation.DeletedAt(); ok {
		_spec.SetField(defaultagentavatar.FieldDeletedAt, field.TypeTime, value)
	}
	if daauo.mutation.DeletedAtCleared() {
		_spec.ClearField(defaultagentavatar.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := daauo.mutation.Avatar(); ok {
		_spec.SetField(defaultagentavatar.FieldAvatar, field.TypeString, value)
	}
	if value, ok := daauo.mutation.ClickedAvatar(); ok {
		_spec.SetField(defaultagentavatar.FieldClickedAvatar, field.TypeString, value)
	}
	if value, ok := daauo.mutation.AvatarType(); ok {
		_spec.SetField(defaultagentavatar.FieldAvatarType, field.TypeInt8, value)
	}
	if value, ok := daauo.mutation.AddedAvatarType(); ok {
		_spec.AddField(defaultagentavatar.FieldAvatarType, field.TypeInt8, value)
	}
	_spec.AddModifiers(daauo.modifiers...)
	_node = &DefaultAgentAvatar{config: daauo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, daauo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{defaultagentavatar.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	daauo.mutation.done = true
	return _node, nil
}

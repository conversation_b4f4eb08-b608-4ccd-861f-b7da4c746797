// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/externalmodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ExternalModelUsageQuery is the builder for querying ExternalModelUsage entities.
type ExternalModelUsageQuery struct {
	config
	ctx        *QueryContext
	order      []externalmodelusage.OrderOption
	inters     []Interceptor
	predicates []predicate.ExternalModelUsage
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ExternalModelUsageQuery builder.
func (emuq *ExternalModelUsageQuery) Where(ps ...predicate.ExternalModelUsage) *ExternalModelUsageQuery {
	emuq.predicates = append(emuq.predicates, ps...)
	return emuq
}

// Limit the number of records to be returned by this query.
func (emuq *ExternalModelUsageQuery) Limit(limit int) *ExternalModelUsageQuery {
	emuq.ctx.Limit = &limit
	return emuq
}

// Offset to start from.
func (emuq *ExternalModelUsageQuery) Offset(offset int) *ExternalModelUsageQuery {
	emuq.ctx.Offset = &offset
	return emuq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (emuq *ExternalModelUsageQuery) Unique(unique bool) *ExternalModelUsageQuery {
	emuq.ctx.Unique = &unique
	return emuq
}

// Order specifies how the records should be ordered.
func (emuq *ExternalModelUsageQuery) Order(o ...externalmodelusage.OrderOption) *ExternalModelUsageQuery {
	emuq.order = append(emuq.order, o...)
	return emuq
}

// First returns the first ExternalModelUsage entity from the query.
// Returns a *NotFoundError when no ExternalModelUsage was found.
func (emuq *ExternalModelUsageQuery) First(ctx context.Context) (*ExternalModelUsage, error) {
	nodes, err := emuq.Limit(1).All(setContextOp(ctx, emuq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{externalmodelusage.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (emuq *ExternalModelUsageQuery) FirstX(ctx context.Context) *ExternalModelUsage {
	node, err := emuq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first ExternalModelUsage ID from the query.
// Returns a *NotFoundError when no ExternalModelUsage ID was found.
func (emuq *ExternalModelUsageQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = emuq.Limit(1).IDs(setContextOp(ctx, emuq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{externalmodelusage.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (emuq *ExternalModelUsageQuery) FirstIDX(ctx context.Context) int64 {
	id, err := emuq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single ExternalModelUsage entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one ExternalModelUsage entity is found.
// Returns a *NotFoundError when no ExternalModelUsage entities are found.
func (emuq *ExternalModelUsageQuery) Only(ctx context.Context) (*ExternalModelUsage, error) {
	nodes, err := emuq.Limit(2).All(setContextOp(ctx, emuq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{externalmodelusage.Label}
	default:
		return nil, &NotSingularError{externalmodelusage.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (emuq *ExternalModelUsageQuery) OnlyX(ctx context.Context) *ExternalModelUsage {
	node, err := emuq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only ExternalModelUsage ID in the query.
// Returns a *NotSingularError when more than one ExternalModelUsage ID is found.
// Returns a *NotFoundError when no entities are found.
func (emuq *ExternalModelUsageQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = emuq.Limit(2).IDs(setContextOp(ctx, emuq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{externalmodelusage.Label}
	default:
		err = &NotSingularError{externalmodelusage.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (emuq *ExternalModelUsageQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := emuq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of ExternalModelUsages.
func (emuq *ExternalModelUsageQuery) All(ctx context.Context) ([]*ExternalModelUsage, error) {
	ctx = setContextOp(ctx, emuq.ctx, "All")
	if err := emuq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*ExternalModelUsage, *ExternalModelUsageQuery]()
	return withInterceptors[[]*ExternalModelUsage](ctx, emuq, qr, emuq.inters)
}

// AllX is like All, but panics if an error occurs.
func (emuq *ExternalModelUsageQuery) AllX(ctx context.Context) []*ExternalModelUsage {
	nodes, err := emuq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of ExternalModelUsage IDs.
func (emuq *ExternalModelUsageQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if emuq.ctx.Unique == nil && emuq.path != nil {
		emuq.Unique(true)
	}
	ctx = setContextOp(ctx, emuq.ctx, "IDs")
	if err = emuq.Select(externalmodelusage.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (emuq *ExternalModelUsageQuery) IDsX(ctx context.Context) []int64 {
	ids, err := emuq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (emuq *ExternalModelUsageQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, emuq.ctx, "Count")
	if err := emuq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, emuq, querierCount[*ExternalModelUsageQuery](), emuq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (emuq *ExternalModelUsageQuery) CountX(ctx context.Context) int {
	count, err := emuq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (emuq *ExternalModelUsageQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, emuq.ctx, "Exist")
	switch _, err := emuq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (emuq *ExternalModelUsageQuery) ExistX(ctx context.Context) bool {
	exist, err := emuq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ExternalModelUsageQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (emuq *ExternalModelUsageQuery) Clone() *ExternalModelUsageQuery {
	if emuq == nil {
		return nil
	}
	return &ExternalModelUsageQuery{
		config:     emuq.config,
		ctx:        emuq.ctx.Clone(),
		order:      append([]externalmodelusage.OrderOption{}, emuq.order...),
		inters:     append([]Interceptor{}, emuq.inters...),
		predicates: append([]predicate.ExternalModelUsage{}, emuq.predicates...),
		// clone intermediate query.
		sql:  emuq.sql.Clone(),
		path: emuq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.ExternalModelUsage.Query().
//		GroupBy(externalmodelusage.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (emuq *ExternalModelUsageQuery) GroupBy(field string, fields ...string) *ExternalModelUsageGroupBy {
	emuq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ExternalModelUsageGroupBy{build: emuq}
	grbuild.flds = &emuq.ctx.Fields
	grbuild.label = externalmodelusage.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.ExternalModelUsage.Query().
//		Select(externalmodelusage.FieldCreatedAt).
//		Scan(ctx, &v)
func (emuq *ExternalModelUsageQuery) Select(fields ...string) *ExternalModelUsageSelect {
	emuq.ctx.Fields = append(emuq.ctx.Fields, fields...)
	sbuild := &ExternalModelUsageSelect{ExternalModelUsageQuery: emuq}
	sbuild.label = externalmodelusage.Label
	sbuild.flds, sbuild.scan = &emuq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ExternalModelUsageSelect configured with the given aggregations.
func (emuq *ExternalModelUsageQuery) Aggregate(fns ...AggregateFunc) *ExternalModelUsageSelect {
	return emuq.Select().Aggregate(fns...)
}

func (emuq *ExternalModelUsageQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range emuq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, emuq); err != nil {
				return err
			}
		}
	}
	for _, f := range emuq.ctx.Fields {
		if !externalmodelusage.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if emuq.path != nil {
		prev, err := emuq.path(ctx)
		if err != nil {
			return err
		}
		emuq.sql = prev
	}
	return nil
}

func (emuq *ExternalModelUsageQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*ExternalModelUsage, error) {
	var (
		nodes = []*ExternalModelUsage{}
		_spec = emuq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*ExternalModelUsage).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &ExternalModelUsage{config: emuq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(emuq.modifiers) > 0 {
		_spec.Modifiers = emuq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, emuq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (emuq *ExternalModelUsageQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := emuq.querySpec()
	if len(emuq.modifiers) > 0 {
		_spec.Modifiers = emuq.modifiers
	}
	_spec.Node.Columns = emuq.ctx.Fields
	if len(emuq.ctx.Fields) > 0 {
		_spec.Unique = emuq.ctx.Unique != nil && *emuq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, emuq.driver, _spec)
}

func (emuq *ExternalModelUsageQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(externalmodelusage.Table, externalmodelusage.Columns, sqlgraph.NewFieldSpec(externalmodelusage.FieldID, field.TypeInt64))
	_spec.From = emuq.sql
	if unique := emuq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if emuq.path != nil {
		_spec.Unique = true
	}
	if fields := emuq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, externalmodelusage.FieldID)
		for i := range fields {
			if fields[i] != externalmodelusage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := emuq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := emuq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := emuq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := emuq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (emuq *ExternalModelUsageQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(emuq.driver.Dialect())
	t1 := builder.Table(externalmodelusage.Table)
	columns := emuq.ctx.Fields
	if len(columns) == 0 {
		columns = externalmodelusage.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if emuq.sql != nil {
		selector = emuq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if emuq.ctx.Unique != nil && *emuq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range emuq.modifiers {
		m(selector)
	}
	for _, p := range emuq.predicates {
		p(selector)
	}
	for _, p := range emuq.order {
		p(selector)
	}
	if offset := emuq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := emuq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (emuq *ExternalModelUsageQuery) ForUpdate(opts ...sql.LockOption) *ExternalModelUsageQuery {
	if emuq.driver.Dialect() == dialect.Postgres {
		emuq.Unique(false)
	}
	emuq.modifiers = append(emuq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return emuq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (emuq *ExternalModelUsageQuery) ForShare(opts ...sql.LockOption) *ExternalModelUsageQuery {
	if emuq.driver.Dialect() == dialect.Postgres {
		emuq.Unique(false)
	}
	emuq.modifiers = append(emuq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return emuq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (emuq *ExternalModelUsageQuery) Modify(modifiers ...func(s *sql.Selector)) *ExternalModelUsageSelect {
	emuq.modifiers = append(emuq.modifiers, modifiers...)
	return emuq.Select()
}

// ExternalModelUsageGroupBy is the group-by builder for ExternalModelUsage entities.
type ExternalModelUsageGroupBy struct {
	selector
	build *ExternalModelUsageQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (emugb *ExternalModelUsageGroupBy) Aggregate(fns ...AggregateFunc) *ExternalModelUsageGroupBy {
	emugb.fns = append(emugb.fns, fns...)
	return emugb
}

// Scan applies the selector query and scans the result into the given value.
func (emugb *ExternalModelUsageGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, emugb.build.ctx, "GroupBy")
	if err := emugb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ExternalModelUsageQuery, *ExternalModelUsageGroupBy](ctx, emugb.build, emugb, emugb.build.inters, v)
}

func (emugb *ExternalModelUsageGroupBy) sqlScan(ctx context.Context, root *ExternalModelUsageQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(emugb.fns))
	for _, fn := range emugb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*emugb.flds)+len(emugb.fns))
		for _, f := range *emugb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*emugb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := emugb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ExternalModelUsageSelect is the builder for selecting fields of ExternalModelUsage entities.
type ExternalModelUsageSelect struct {
	*ExternalModelUsageQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (emus *ExternalModelUsageSelect) Aggregate(fns ...AggregateFunc) *ExternalModelUsageSelect {
	emus.fns = append(emus.fns, fns...)
	return emus
}

// Scan applies the selector query and scans the result into the given value.
func (emus *ExternalModelUsageSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, emus.ctx, "Select")
	if err := emus.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ExternalModelUsageQuery, *ExternalModelUsageSelect](ctx, emus.ExternalModelUsageQuery, emus, emus.inters, v)
}

func (emus *ExternalModelUsageSelect) sqlScan(ctx context.Context, root *ExternalModelUsageQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(emus.fns))
	for _, fn := range emus.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*emus.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := emus.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (emus *ExternalModelUsageSelect) Modify(modifiers ...func(s *sql.Selector)) *ExternalModelUsageSelect {
	emus.modifiers = append(emus.modifiers, modifiers...)
	return emus
}

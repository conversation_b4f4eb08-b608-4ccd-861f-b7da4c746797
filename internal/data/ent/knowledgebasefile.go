// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebasefile"
)

// KnowledgeBaseFile is the model entity for the KnowledgeBaseFile schema.
type KnowledgeBaseFile struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 租户ID
	TenantID int64 `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 知识库id
	KnowledgeBaseID int64 `json:"knowledge_base_id,omitempty"`
	// 数据类型 1文档 2表格
	DataType int32 `json:"data_type,omitempty"`
	// 文件id
	FileRelationID int64 `json:"file_relation_id,omitempty"`
	// 元数据 excel_schema
	Metadata string `json:"metadata,omitempty"`
	// 状态 0等待 1处理中 2成功 3失败
	Status int32 `json:"status,omitempty"`
	// 失败原因
	FailedReason string `json:"failed_reason,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*KnowledgeBaseFile) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case knowledgebasefile.FieldID, knowledgebasefile.FieldTenantID, knowledgebasefile.FieldKnowledgeBaseID, knowledgebasefile.FieldDataType, knowledgebasefile.FieldFileRelationID, knowledgebasefile.FieldStatus:
			values[i] = new(sql.NullInt64)
		case knowledgebasefile.FieldMetadata, knowledgebasefile.FieldFailedReason:
			values[i] = new(sql.NullString)
		case knowledgebasefile.FieldCreatedAt, knowledgebasefile.FieldUpdatedAt, knowledgebasefile.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the KnowledgeBaseFile fields.
func (kbf *KnowledgeBaseFile) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case knowledgebasefile.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			kbf.ID = int64(value.Int64)
		case knowledgebasefile.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				kbf.TenantID = value.Int64
			}
		case knowledgebasefile.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				kbf.CreatedAt = value.Time
			}
		case knowledgebasefile.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				kbf.UpdatedAt = value.Time
			}
		case knowledgebasefile.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				kbf.DeletedAt = value.Time
			}
		case knowledgebasefile.FieldKnowledgeBaseID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field knowledge_base_id", values[i])
			} else if value.Valid {
				kbf.KnowledgeBaseID = value.Int64
			}
		case knowledgebasefile.FieldDataType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field data_type", values[i])
			} else if value.Valid {
				kbf.DataType = int32(value.Int64)
			}
		case knowledgebasefile.FieldFileRelationID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field file_relation_id", values[i])
			} else if value.Valid {
				kbf.FileRelationID = value.Int64
			}
		case knowledgebasefile.FieldMetadata:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field metadata", values[i])
			} else if value.Valid {
				kbf.Metadata = value.String
			}
		case knowledgebasefile.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				kbf.Status = int32(value.Int64)
			}
		case knowledgebasefile.FieldFailedReason:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field failed_reason", values[i])
			} else if value.Valid {
				kbf.FailedReason = value.String
			}
		default:
			kbf.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the KnowledgeBaseFile.
// This includes values selected through modifiers, order, etc.
func (kbf *KnowledgeBaseFile) Value(name string) (ent.Value, error) {
	return kbf.selectValues.Get(name)
}

// Update returns a builder for updating this KnowledgeBaseFile.
// Note that you need to call KnowledgeBaseFile.Unwrap() before calling this method if this KnowledgeBaseFile
// was returned from a transaction, and the transaction was committed or rolled back.
func (kbf *KnowledgeBaseFile) Update() *KnowledgeBaseFileUpdateOne {
	return NewKnowledgeBaseFileClient(kbf.config).UpdateOne(kbf)
}

// Unwrap unwraps the KnowledgeBaseFile entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (kbf *KnowledgeBaseFile) Unwrap() *KnowledgeBaseFile {
	_tx, ok := kbf.config.driver.(*txDriver)
	if !ok {
		panic("ent: KnowledgeBaseFile is not a transactional entity")
	}
	kbf.config.driver = _tx.drv
	return kbf
}

// String implements the fmt.Stringer.
func (kbf *KnowledgeBaseFile) String() string {
	var builder strings.Builder
	builder.WriteString("KnowledgeBaseFile(")
	builder.WriteString(fmt.Sprintf("id=%v, ", kbf.ID))
	builder.WriteString("tenant_id=")
	builder.WriteString(fmt.Sprintf("%v", kbf.TenantID))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(kbf.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(kbf.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(kbf.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("knowledge_base_id=")
	builder.WriteString(fmt.Sprintf("%v", kbf.KnowledgeBaseID))
	builder.WriteString(", ")
	builder.WriteString("data_type=")
	builder.WriteString(fmt.Sprintf("%v", kbf.DataType))
	builder.WriteString(", ")
	builder.WriteString("file_relation_id=")
	builder.WriteString(fmt.Sprintf("%v", kbf.FileRelationID))
	builder.WriteString(", ")
	builder.WriteString("metadata=")
	builder.WriteString(kbf.Metadata)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", kbf.Status))
	builder.WriteString(", ")
	builder.WriteString("failed_reason=")
	builder.WriteString(kbf.FailedReason)
	builder.WriteByte(')')
	return builder.String()
}

// KnowledgeBaseFiles is a parsable slice of KnowledgeBaseFile.
type KnowledgeBaseFiles []*KnowledgeBaseFile

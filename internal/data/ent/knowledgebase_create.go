// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
)

// KnowledgeBaseCreate is the builder for creating a KnowledgeBase entity.
type KnowledgeBaseCreate struct {
	config
	mutation *KnowledgeBaseMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetTenantID sets the "tenant_id" field.
func (kbc *KnowledgeBaseCreate) SetTenantID(i int64) *KnowledgeBaseCreate {
	kbc.mutation.SetTenantID(i)
	return kbc
}

// SetCreatedAt sets the "created_at" field.
func (kbc *KnowledgeBaseCreate) SetCreatedAt(t time.Time) *KnowledgeBaseCreate {
	kbc.mutation.SetCreatedAt(t)
	return kbc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (kbc *KnowledgeBaseCreate) SetNillableCreatedAt(t *time.Time) *KnowledgeBaseCreate {
	if t != nil {
		kbc.SetCreatedAt(*t)
	}
	return kbc
}

// SetUpdatedAt sets the "updated_at" field.
func (kbc *KnowledgeBaseCreate) SetUpdatedAt(t time.Time) *KnowledgeBaseCreate {
	kbc.mutation.SetUpdatedAt(t)
	return kbc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (kbc *KnowledgeBaseCreate) SetNillableUpdatedAt(t *time.Time) *KnowledgeBaseCreate {
	if t != nil {
		kbc.SetUpdatedAt(*t)
	}
	return kbc
}

// SetDeletedAt sets the "deleted_at" field.
func (kbc *KnowledgeBaseCreate) SetDeletedAt(t time.Time) *KnowledgeBaseCreate {
	kbc.mutation.SetDeletedAt(t)
	return kbc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (kbc *KnowledgeBaseCreate) SetNillableDeletedAt(t *time.Time) *KnowledgeBaseCreate {
	if t != nil {
		kbc.SetDeletedAt(*t)
	}
	return kbc
}

// SetName sets the "name" field.
func (kbc *KnowledgeBaseCreate) SetName(s string) *KnowledgeBaseCreate {
	kbc.mutation.SetName(s)
	return kbc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (kbc *KnowledgeBaseCreate) SetNillableName(s *string) *KnowledgeBaseCreate {
	if s != nil {
		kbc.SetName(*s)
	}
	return kbc
}

// SetPublic sets the "public" field.
func (kbc *KnowledgeBaseCreate) SetPublic(b bool) *KnowledgeBaseCreate {
	kbc.mutation.SetPublic(b)
	return kbc
}

// SetNillablePublic sets the "public" field if the given value is not nil.
func (kbc *KnowledgeBaseCreate) SetNillablePublic(b *bool) *KnowledgeBaseCreate {
	if b != nil {
		kbc.SetPublic(*b)
	}
	return kbc
}

// SetDataType sets the "data_type" field.
func (kbc *KnowledgeBaseCreate) SetDataType(i int32) *KnowledgeBaseCreate {
	kbc.mutation.SetDataType(i)
	return kbc
}

// SetNillableDataType sets the "data_type" field if the given value is not nil.
func (kbc *KnowledgeBaseCreate) SetNillableDataType(i *int32) *KnowledgeBaseCreate {
	if i != nil {
		kbc.SetDataType(*i)
	}
	return kbc
}

// SetUserID sets the "user_id" field.
func (kbc *KnowledgeBaseCreate) SetUserID(i int64) *KnowledgeBaseCreate {
	kbc.mutation.SetUserID(i)
	return kbc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (kbc *KnowledgeBaseCreate) SetNillableUserID(i *int64) *KnowledgeBaseCreate {
	if i != nil {
		kbc.SetUserID(*i)
	}
	return kbc
}

// SetManagerUserIds sets the "manager_user_ids" field.
func (kbc *KnowledgeBaseCreate) SetManagerUserIds(pq *pq.Int64Array) *KnowledgeBaseCreate {
	kbc.mutation.SetManagerUserIds(pq)
	return kbc
}

// SetEditableUserIds sets the "editable_user_ids" field.
func (kbc *KnowledgeBaseCreate) SetEditableUserIds(pq *pq.Int64Array) *KnowledgeBaseCreate {
	kbc.mutation.SetEditableUserIds(pq)
	return kbc
}

// SetID sets the "id" field.
func (kbc *KnowledgeBaseCreate) SetID(i int64) *KnowledgeBaseCreate {
	kbc.mutation.SetID(i)
	return kbc
}

// Mutation returns the KnowledgeBaseMutation object of the builder.
func (kbc *KnowledgeBaseCreate) Mutation() *KnowledgeBaseMutation {
	return kbc.mutation
}

// Save creates the KnowledgeBase in the database.
func (kbc *KnowledgeBaseCreate) Save(ctx context.Context) (*KnowledgeBase, error) {
	if err := kbc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, kbc.sqlSave, kbc.mutation, kbc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (kbc *KnowledgeBaseCreate) SaveX(ctx context.Context) *KnowledgeBase {
	v, err := kbc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (kbc *KnowledgeBaseCreate) Exec(ctx context.Context) error {
	_, err := kbc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (kbc *KnowledgeBaseCreate) ExecX(ctx context.Context) {
	if err := kbc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (kbc *KnowledgeBaseCreate) defaults() error {
	if _, ok := kbc.mutation.CreatedAt(); !ok {
		if knowledgebase.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized knowledgebase.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := knowledgebase.DefaultCreatedAt()
		kbc.mutation.SetCreatedAt(v)
	}
	if _, ok := kbc.mutation.UpdatedAt(); !ok {
		if knowledgebase.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized knowledgebase.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := knowledgebase.DefaultUpdatedAt()
		kbc.mutation.SetUpdatedAt(v)
	}
	if _, ok := kbc.mutation.DeletedAt(); !ok {
		v := knowledgebase.DefaultDeletedAt
		kbc.mutation.SetDeletedAt(v)
	}
	if _, ok := kbc.mutation.Name(); !ok {
		v := knowledgebase.DefaultName
		kbc.mutation.SetName(v)
	}
	if _, ok := kbc.mutation.Public(); !ok {
		v := knowledgebase.DefaultPublic
		kbc.mutation.SetPublic(v)
	}
	if _, ok := kbc.mutation.DataType(); !ok {
		v := knowledgebase.DefaultDataType
		kbc.mutation.SetDataType(v)
	}
	if _, ok := kbc.mutation.UserID(); !ok {
		v := knowledgebase.DefaultUserID
		kbc.mutation.SetUserID(v)
	}
	if _, ok := kbc.mutation.ManagerUserIds(); !ok {
		v := knowledgebase.DefaultManagerUserIds
		kbc.mutation.SetManagerUserIds(v)
	}
	if _, ok := kbc.mutation.EditableUserIds(); !ok {
		v := knowledgebase.DefaultEditableUserIds
		kbc.mutation.SetEditableUserIds(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (kbc *KnowledgeBaseCreate) check() error {
	if _, ok := kbc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "KnowledgeBase.tenant_id"`)}
	}
	if _, ok := kbc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "KnowledgeBase.created_at"`)}
	}
	if _, ok := kbc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "KnowledgeBase.updated_at"`)}
	}
	if _, ok := kbc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "KnowledgeBase.name"`)}
	}
	if _, ok := kbc.mutation.Public(); !ok {
		return &ValidationError{Name: "public", err: errors.New(`ent: missing required field "KnowledgeBase.public"`)}
	}
	if _, ok := kbc.mutation.DataType(); !ok {
		return &ValidationError{Name: "data_type", err: errors.New(`ent: missing required field "KnowledgeBase.data_type"`)}
	}
	if _, ok := kbc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "KnowledgeBase.user_id"`)}
	}
	if _, ok := kbc.mutation.ManagerUserIds(); !ok {
		return &ValidationError{Name: "manager_user_ids", err: errors.New(`ent: missing required field "KnowledgeBase.manager_user_ids"`)}
	}
	if _, ok := kbc.mutation.EditableUserIds(); !ok {
		return &ValidationError{Name: "editable_user_ids", err: errors.New(`ent: missing required field "KnowledgeBase.editable_user_ids"`)}
	}
	return nil
}

func (kbc *KnowledgeBaseCreate) sqlSave(ctx context.Context) (*KnowledgeBase, error) {
	if err := kbc.check(); err != nil {
		return nil, err
	}
	_node, _spec := kbc.createSpec()
	if err := sqlgraph.CreateNode(ctx, kbc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	kbc.mutation.id = &_node.ID
	kbc.mutation.done = true
	return _node, nil
}

func (kbc *KnowledgeBaseCreate) createSpec() (*KnowledgeBase, *sqlgraph.CreateSpec) {
	var (
		_node = &KnowledgeBase{config: kbc.config}
		_spec = sqlgraph.NewCreateSpec(knowledgebase.Table, sqlgraph.NewFieldSpec(knowledgebase.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = kbc.conflict
	if id, ok := kbc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := kbc.mutation.TenantID(); ok {
		_spec.SetField(knowledgebase.FieldTenantID, field.TypeInt64, value)
		_node.TenantID = value
	}
	if value, ok := kbc.mutation.CreatedAt(); ok {
		_spec.SetField(knowledgebase.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := kbc.mutation.UpdatedAt(); ok {
		_spec.SetField(knowledgebase.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := kbc.mutation.DeletedAt(); ok {
		_spec.SetField(knowledgebase.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := kbc.mutation.Name(); ok {
		_spec.SetField(knowledgebase.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := kbc.mutation.Public(); ok {
		_spec.SetField(knowledgebase.FieldPublic, field.TypeBool, value)
		_node.Public = value
	}
	if value, ok := kbc.mutation.DataType(); ok {
		_spec.SetField(knowledgebase.FieldDataType, field.TypeInt32, value)
		_node.DataType = value
	}
	if value, ok := kbc.mutation.UserID(); ok {
		_spec.SetField(knowledgebase.FieldUserID, field.TypeInt64, value)
		_node.UserID = value
	}
	if value, ok := kbc.mutation.ManagerUserIds(); ok {
		_spec.SetField(knowledgebase.FieldManagerUserIds, field.TypeOther, value)
		_node.ManagerUserIds = value
	}
	if value, ok := kbc.mutation.EditableUserIds(); ok {
		_spec.SetField(knowledgebase.FieldEditableUserIds, field.TypeOther, value)
		_node.EditableUserIds = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.KnowledgeBase.Create().
//		SetTenantID(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.KnowledgeBaseUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (kbc *KnowledgeBaseCreate) OnConflict(opts ...sql.ConflictOption) *KnowledgeBaseUpsertOne {
	kbc.conflict = opts
	return &KnowledgeBaseUpsertOne{
		create: kbc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.KnowledgeBase.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (kbc *KnowledgeBaseCreate) OnConflictColumns(columns ...string) *KnowledgeBaseUpsertOne {
	kbc.conflict = append(kbc.conflict, sql.ConflictColumns(columns...))
	return &KnowledgeBaseUpsertOne{
		create: kbc,
	}
}

type (
	// KnowledgeBaseUpsertOne is the builder for "upsert"-ing
	//  one KnowledgeBase node.
	KnowledgeBaseUpsertOne struct {
		create *KnowledgeBaseCreate
	}

	// KnowledgeBaseUpsert is the "OnConflict" setter.
	KnowledgeBaseUpsert struct {
		*sql.UpdateSet
	}
)

// SetTenantID sets the "tenant_id" field.
func (u *KnowledgeBaseUpsert) SetTenantID(v int64) *KnowledgeBaseUpsert {
	u.Set(knowledgebase.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *KnowledgeBaseUpsert) UpdateTenantID() *KnowledgeBaseUpsert {
	u.SetExcluded(knowledgebase.FieldTenantID)
	return u
}

// AddTenantID adds v to the "tenant_id" field.
func (u *KnowledgeBaseUpsert) AddTenantID(v int64) *KnowledgeBaseUpsert {
	u.Add(knowledgebase.FieldTenantID, v)
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *KnowledgeBaseUpsert) SetUpdatedAt(v time.Time) *KnowledgeBaseUpsert {
	u.Set(knowledgebase.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *KnowledgeBaseUpsert) UpdateUpdatedAt() *KnowledgeBaseUpsert {
	u.SetExcluded(knowledgebase.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *KnowledgeBaseUpsert) SetDeletedAt(v time.Time) *KnowledgeBaseUpsert {
	u.Set(knowledgebase.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *KnowledgeBaseUpsert) UpdateDeletedAt() *KnowledgeBaseUpsert {
	u.SetExcluded(knowledgebase.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *KnowledgeBaseUpsert) ClearDeletedAt() *KnowledgeBaseUpsert {
	u.SetNull(knowledgebase.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *KnowledgeBaseUpsert) SetName(v string) *KnowledgeBaseUpsert {
	u.Set(knowledgebase.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *KnowledgeBaseUpsert) UpdateName() *KnowledgeBaseUpsert {
	u.SetExcluded(knowledgebase.FieldName)
	return u
}

// SetPublic sets the "public" field.
func (u *KnowledgeBaseUpsert) SetPublic(v bool) *KnowledgeBaseUpsert {
	u.Set(knowledgebase.FieldPublic, v)
	return u
}

// UpdatePublic sets the "public" field to the value that was provided on create.
func (u *KnowledgeBaseUpsert) UpdatePublic() *KnowledgeBaseUpsert {
	u.SetExcluded(knowledgebase.FieldPublic)
	return u
}

// SetDataType sets the "data_type" field.
func (u *KnowledgeBaseUpsert) SetDataType(v int32) *KnowledgeBaseUpsert {
	u.Set(knowledgebase.FieldDataType, v)
	return u
}

// UpdateDataType sets the "data_type" field to the value that was provided on create.
func (u *KnowledgeBaseUpsert) UpdateDataType() *KnowledgeBaseUpsert {
	u.SetExcluded(knowledgebase.FieldDataType)
	return u
}

// AddDataType adds v to the "data_type" field.
func (u *KnowledgeBaseUpsert) AddDataType(v int32) *KnowledgeBaseUpsert {
	u.Add(knowledgebase.FieldDataType, v)
	return u
}

// SetUserID sets the "user_id" field.
func (u *KnowledgeBaseUpsert) SetUserID(v int64) *KnowledgeBaseUpsert {
	u.Set(knowledgebase.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *KnowledgeBaseUpsert) UpdateUserID() *KnowledgeBaseUpsert {
	u.SetExcluded(knowledgebase.FieldUserID)
	return u
}

// AddUserID adds v to the "user_id" field.
func (u *KnowledgeBaseUpsert) AddUserID(v int64) *KnowledgeBaseUpsert {
	u.Add(knowledgebase.FieldUserID, v)
	return u
}

// SetManagerUserIds sets the "manager_user_ids" field.
func (u *KnowledgeBaseUpsert) SetManagerUserIds(v *pq.Int64Array) *KnowledgeBaseUpsert {
	u.Set(knowledgebase.FieldManagerUserIds, v)
	return u
}

// UpdateManagerUserIds sets the "manager_user_ids" field to the value that was provided on create.
func (u *KnowledgeBaseUpsert) UpdateManagerUserIds() *KnowledgeBaseUpsert {
	u.SetExcluded(knowledgebase.FieldManagerUserIds)
	return u
}

// SetEditableUserIds sets the "editable_user_ids" field.
func (u *KnowledgeBaseUpsert) SetEditableUserIds(v *pq.Int64Array) *KnowledgeBaseUpsert {
	u.Set(knowledgebase.FieldEditableUserIds, v)
	return u
}

// UpdateEditableUserIds sets the "editable_user_ids" field to the value that was provided on create.
func (u *KnowledgeBaseUpsert) UpdateEditableUserIds() *KnowledgeBaseUpsert {
	u.SetExcluded(knowledgebase.FieldEditableUserIds)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.KnowledgeBase.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(knowledgebase.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *KnowledgeBaseUpsertOne) UpdateNewValues() *KnowledgeBaseUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(knowledgebase.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(knowledgebase.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.KnowledgeBase.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *KnowledgeBaseUpsertOne) Ignore() *KnowledgeBaseUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *KnowledgeBaseUpsertOne) DoNothing() *KnowledgeBaseUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the KnowledgeBaseCreate.OnConflict
// documentation for more info.
func (u *KnowledgeBaseUpsertOne) Update(set func(*KnowledgeBaseUpsert)) *KnowledgeBaseUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&KnowledgeBaseUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *KnowledgeBaseUpsertOne) SetTenantID(v int64) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *KnowledgeBaseUpsertOne) AddTenantID(v int64) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertOne) UpdateTenantID() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *KnowledgeBaseUpsertOne) SetUpdatedAt(v time.Time) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertOne) UpdateUpdatedAt() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *KnowledgeBaseUpsertOne) SetDeletedAt(v time.Time) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertOne) UpdateDeletedAt() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *KnowledgeBaseUpsertOne) ClearDeletedAt() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *KnowledgeBaseUpsertOne) SetName(v string) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertOne) UpdateName() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateName()
	})
}

// SetPublic sets the "public" field.
func (u *KnowledgeBaseUpsertOne) SetPublic(v bool) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetPublic(v)
	})
}

// UpdatePublic sets the "public" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertOne) UpdatePublic() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdatePublic()
	})
}

// SetDataType sets the "data_type" field.
func (u *KnowledgeBaseUpsertOne) SetDataType(v int32) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetDataType(v)
	})
}

// AddDataType adds v to the "data_type" field.
func (u *KnowledgeBaseUpsertOne) AddDataType(v int32) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.AddDataType(v)
	})
}

// UpdateDataType sets the "data_type" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertOne) UpdateDataType() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateDataType()
	})
}

// SetUserID sets the "user_id" field.
func (u *KnowledgeBaseUpsertOne) SetUserID(v int64) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *KnowledgeBaseUpsertOne) AddUserID(v int64) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertOne) UpdateUserID() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateUserID()
	})
}

// SetManagerUserIds sets the "manager_user_ids" field.
func (u *KnowledgeBaseUpsertOne) SetManagerUserIds(v *pq.Int64Array) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetManagerUserIds(v)
	})
}

// UpdateManagerUserIds sets the "manager_user_ids" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertOne) UpdateManagerUserIds() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateManagerUserIds()
	})
}

// SetEditableUserIds sets the "editable_user_ids" field.
func (u *KnowledgeBaseUpsertOne) SetEditableUserIds(v *pq.Int64Array) *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetEditableUserIds(v)
	})
}

// UpdateEditableUserIds sets the "editable_user_ids" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertOne) UpdateEditableUserIds() *KnowledgeBaseUpsertOne {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateEditableUserIds()
	})
}

// Exec executes the query.
func (u *KnowledgeBaseUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for KnowledgeBaseCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *KnowledgeBaseUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *KnowledgeBaseUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *KnowledgeBaseUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// KnowledgeBaseCreateBulk is the builder for creating many KnowledgeBase entities in bulk.
type KnowledgeBaseCreateBulk struct {
	config
	err      error
	builders []*KnowledgeBaseCreate
	conflict []sql.ConflictOption
}

// Save creates the KnowledgeBase entities in the database.
func (kbcb *KnowledgeBaseCreateBulk) Save(ctx context.Context) ([]*KnowledgeBase, error) {
	if kbcb.err != nil {
		return nil, kbcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(kbcb.builders))
	nodes := make([]*KnowledgeBase, len(kbcb.builders))
	mutators := make([]Mutator, len(kbcb.builders))
	for i := range kbcb.builders {
		func(i int, root context.Context) {
			builder := kbcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*KnowledgeBaseMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, kbcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = kbcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, kbcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, kbcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (kbcb *KnowledgeBaseCreateBulk) SaveX(ctx context.Context) []*KnowledgeBase {
	v, err := kbcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (kbcb *KnowledgeBaseCreateBulk) Exec(ctx context.Context) error {
	_, err := kbcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (kbcb *KnowledgeBaseCreateBulk) ExecX(ctx context.Context) {
	if err := kbcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.KnowledgeBase.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.KnowledgeBaseUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (kbcb *KnowledgeBaseCreateBulk) OnConflict(opts ...sql.ConflictOption) *KnowledgeBaseUpsertBulk {
	kbcb.conflict = opts
	return &KnowledgeBaseUpsertBulk{
		create: kbcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.KnowledgeBase.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (kbcb *KnowledgeBaseCreateBulk) OnConflictColumns(columns ...string) *KnowledgeBaseUpsertBulk {
	kbcb.conflict = append(kbcb.conflict, sql.ConflictColumns(columns...))
	return &KnowledgeBaseUpsertBulk{
		create: kbcb,
	}
}

// KnowledgeBaseUpsertBulk is the builder for "upsert"-ing
// a bulk of KnowledgeBase nodes.
type KnowledgeBaseUpsertBulk struct {
	create *KnowledgeBaseCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.KnowledgeBase.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(knowledgebase.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *KnowledgeBaseUpsertBulk) UpdateNewValues() *KnowledgeBaseUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(knowledgebase.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(knowledgebase.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.KnowledgeBase.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *KnowledgeBaseUpsertBulk) Ignore() *KnowledgeBaseUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *KnowledgeBaseUpsertBulk) DoNothing() *KnowledgeBaseUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the KnowledgeBaseCreateBulk.OnConflict
// documentation for more info.
func (u *KnowledgeBaseUpsertBulk) Update(set func(*KnowledgeBaseUpsert)) *KnowledgeBaseUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&KnowledgeBaseUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *KnowledgeBaseUpsertBulk) SetTenantID(v int64) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *KnowledgeBaseUpsertBulk) AddTenantID(v int64) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertBulk) UpdateTenantID() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *KnowledgeBaseUpsertBulk) SetUpdatedAt(v time.Time) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertBulk) UpdateUpdatedAt() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *KnowledgeBaseUpsertBulk) SetDeletedAt(v time.Time) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertBulk) UpdateDeletedAt() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *KnowledgeBaseUpsertBulk) ClearDeletedAt() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *KnowledgeBaseUpsertBulk) SetName(v string) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertBulk) UpdateName() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateName()
	})
}

// SetPublic sets the "public" field.
func (u *KnowledgeBaseUpsertBulk) SetPublic(v bool) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetPublic(v)
	})
}

// UpdatePublic sets the "public" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertBulk) UpdatePublic() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdatePublic()
	})
}

// SetDataType sets the "data_type" field.
func (u *KnowledgeBaseUpsertBulk) SetDataType(v int32) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetDataType(v)
	})
}

// AddDataType adds v to the "data_type" field.
func (u *KnowledgeBaseUpsertBulk) AddDataType(v int32) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.AddDataType(v)
	})
}

// UpdateDataType sets the "data_type" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertBulk) UpdateDataType() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateDataType()
	})
}

// SetUserID sets the "user_id" field.
func (u *KnowledgeBaseUpsertBulk) SetUserID(v int64) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *KnowledgeBaseUpsertBulk) AddUserID(v int64) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertBulk) UpdateUserID() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateUserID()
	})
}

// SetManagerUserIds sets the "manager_user_ids" field.
func (u *KnowledgeBaseUpsertBulk) SetManagerUserIds(v *pq.Int64Array) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetManagerUserIds(v)
	})
}

// UpdateManagerUserIds sets the "manager_user_ids" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertBulk) UpdateManagerUserIds() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateManagerUserIds()
	})
}

// SetEditableUserIds sets the "editable_user_ids" field.
func (u *KnowledgeBaseUpsertBulk) SetEditableUserIds(v *pq.Int64Array) *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.SetEditableUserIds(v)
	})
}

// UpdateEditableUserIds sets the "editable_user_ids" field to the value that was provided on create.
func (u *KnowledgeBaseUpsertBulk) UpdateEditableUserIds() *KnowledgeBaseUpsertBulk {
	return u.Update(func(s *KnowledgeBaseUpsert) {
		s.UpdateEditableUserIds()
	})
}

// Exec executes the query.
func (u *KnowledgeBaseUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the KnowledgeBaseCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for KnowledgeBaseCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *KnowledgeBaseUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package aiagent

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/lib/pq"
)

const (
	// Label holds the string label denoting the aiagent type in the database.
	Label = "ai_agent"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldAvatar holds the string denoting the avatar field in the database.
	FieldAvatar = "avatar"
	// FieldClickedAvatar holds the string denoting the clicked_avatar field in the database.
	FieldClickedAvatar = "clicked_avatar"
	// FieldWelcomeMsg holds the string denoting the welcome_msg field in the database.
	FieldWelcomeMsg = "welcome_msg"
	// FieldFallbackMsg holds the string denoting the fallback_msg field in the database.
	FieldFallbackMsg = "fallback_msg"
	// FieldOwnerID holds the string denoting the owner_id field in the database.
	FieldOwnerID = "owner_id"
	// FieldVisibilityType holds the string denoting the visibility_type field in the database.
	FieldVisibilityType = "visibility_type"
	// FieldManageableToUser holds the string denoting the manageable_to_user field in the database.
	FieldManageableToUser = "manageable_to_user"
	// FieldVisibleToUser holds the string denoting the visible_to_user field in the database.
	FieldVisibleToUser = "visible_to_user"
	// FieldVisibleToDept holds the string denoting the visible_to_dept field in the database.
	FieldVisibleToDept = "visible_to_dept"
	// FieldKnowledgeBaseIds holds the string denoting the knowledge_base_ids field in the database.
	FieldKnowledgeBaseIds = "knowledge_base_ids"
	// FieldSchema holds the string denoting the schema field in the database.
	FieldSchema = "schema"
	// FieldIsPublic holds the string denoting the is_public field in the database.
	FieldIsPublic = "is_public"
	// FieldIsEnabled holds the string denoting the is_enabled field in the database.
	FieldIsEnabled = "is_enabled"
	// FieldIsRefFiles holds the string denoting the is_ref_files field in the database.
	FieldIsRefFiles = "is_ref_files"
	// FieldModelType holds the string denoting the model_type field in the database.
	FieldModelType = "model_type"
	// FieldModelID holds the string denoting the model_id field in the database.
	FieldModelID = "model_id"
	// FieldUseCount holds the string denoting the use_count field in the database.
	FieldUseCount = "use_count"
	// FieldKnowledgeBaseType holds the string denoting the knowledge_base_type field in the database.
	FieldKnowledgeBaseType = "knowledge_base_type"
	// FieldInternetSearch holds the string denoting the internet_search field in the database.
	FieldInternetSearch = "internet_search"
	// FieldAgentType holds the string denoting the agent_type field in the database.
	FieldAgentType = "agent_type"
	// FieldThinking holds the string denoting the thinking field in the database.
	FieldThinking = "thinking"
	// FieldThinkingModelID holds the string denoting the thinking_model_id field in the database.
	FieldThinkingModelID = "thinking_model_id"
	// FieldRoleSetting holds the string denoting the role_setting field in the database.
	FieldRoleSetting = "role_setting"
	// FieldUploadFile holds the string denoting the upload_file field in the database.
	FieldUploadFile = "upload_file"
	// FieldSemanticCache holds the string denoting the semantic_cache field in the database.
	FieldSemanticCache = "semantic_cache"
	// EdgeAiChat holds the string denoting the ai_chat edge name in mutations.
	EdgeAiChat = "ai_chat"
	// Table holds the table name of the aiagent in the database.
	Table = "ai_agent"
	// AiChatTable is the table that holds the ai_chat relation/edge.
	AiChatTable = "ai_chat"
	// AiChatInverseTable is the table name for the AiChat entity.
	// It exists in this package in order to avoid circular dependency with the "aichat" package.
	AiChatInverseTable = "ai_chat"
	// AiChatColumn is the table column denoting the ai_chat relation/edge.
	AiChatColumn = "agent_id"
)

// Columns holds all SQL columns for aiagent fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldName,
	FieldDescription,
	FieldAvatar,
	FieldClickedAvatar,
	FieldWelcomeMsg,
	FieldFallbackMsg,
	FieldOwnerID,
	FieldVisibilityType,
	FieldManageableToUser,
	FieldVisibleToUser,
	FieldVisibleToDept,
	FieldKnowledgeBaseIds,
	FieldSchema,
	FieldIsPublic,
	FieldIsEnabled,
	FieldIsRefFiles,
	FieldModelType,
	FieldModelID,
	FieldUseCount,
	FieldKnowledgeBaseType,
	FieldInternetSearch,
	FieldAgentType,
	FieldThinking,
	FieldThinkingModelID,
	FieldRoleSetting,
	FieldUploadFile,
	FieldSemanticCache,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultDeletedAt holds the default value on creation for the "deleted_at" field.
	DefaultDeletedAt time.Time
	// DefaultManageableToUser holds the default value on creation for the "manageable_to_user" field.
	DefaultManageableToUser *pq.Int64Array
	// DefaultVisibleToUser holds the default value on creation for the "visible_to_user" field.
	DefaultVisibleToUser *pq.Int64Array
	// DefaultVisibleToDept holds the default value on creation for the "visible_to_dept" field.
	DefaultVisibleToDept *pq.Int64Array
	// DefaultKnowledgeBaseIds holds the default value on creation for the "knowledge_base_ids" field.
	DefaultKnowledgeBaseIds *pq.Int64Array
	// DefaultModelType holds the default value on creation for the "model_type" field.
	DefaultModelType int64
	// DefaultModelID holds the default value on creation for the "model_id" field.
	DefaultModelID int64
	// DefaultUseCount holds the default value on creation for the "use_count" field.
	DefaultUseCount int64
	// DefaultKnowledgeBaseType holds the default value on creation for the "knowledge_base_type" field.
	DefaultKnowledgeBaseType int64
	// DefaultInternetSearch holds the default value on creation for the "internet_search" field.
	DefaultInternetSearch bool
	// DefaultAgentType holds the default value on creation for the "agent_type" field.
	DefaultAgentType int64
	// DefaultThinking holds the default value on creation for the "thinking" field.
	DefaultThinking bool
	// DefaultThinkingModelID holds the default value on creation for the "thinking_model_id" field.
	DefaultThinkingModelID int64
	// DefaultUploadFile holds the default value on creation for the "upload_file" field.
	DefaultUploadFile bool
	// DefaultSemanticCache holds the default value on creation for the "semantic_cache" field.
	DefaultSemanticCache bool
)

// OrderOption defines the ordering options for the AiAgent queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByAvatar orders the results by the avatar field.
func ByAvatar(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAvatar, opts...).ToFunc()
}

// ByClickedAvatar orders the results by the clicked_avatar field.
func ByClickedAvatar(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldClickedAvatar, opts...).ToFunc()
}

// ByWelcomeMsg orders the results by the welcome_msg field.
func ByWelcomeMsg(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldWelcomeMsg, opts...).ToFunc()
}

// ByFallbackMsg orders the results by the fallback_msg field.
func ByFallbackMsg(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFallbackMsg, opts...).ToFunc()
}

// ByOwnerID orders the results by the owner_id field.
func ByOwnerID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldOwnerID, opts...).ToFunc()
}

// ByVisibilityType orders the results by the visibility_type field.
func ByVisibilityType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVisibilityType, opts...).ToFunc()
}

// ByManageableToUser orders the results by the manageable_to_user field.
func ByManageableToUser(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldManageableToUser, opts...).ToFunc()
}

// ByVisibleToUser orders the results by the visible_to_user field.
func ByVisibleToUser(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVisibleToUser, opts...).ToFunc()
}

// ByVisibleToDept orders the results by the visible_to_dept field.
func ByVisibleToDept(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldVisibleToDept, opts...).ToFunc()
}

// ByKnowledgeBaseIds orders the results by the knowledge_base_ids field.
func ByKnowledgeBaseIds(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKnowledgeBaseIds, opts...).ToFunc()
}

// BySchema orders the results by the schema field.
func BySchema(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSchema, opts...).ToFunc()
}

// ByIsPublic orders the results by the is_public field.
func ByIsPublic(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsPublic, opts...).ToFunc()
}

// ByIsEnabled orders the results by the is_enabled field.
func ByIsEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsEnabled, opts...).ToFunc()
}

// ByIsRefFiles orders the results by the is_ref_files field.
func ByIsRefFiles(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsRefFiles, opts...).ToFunc()
}

// ByModelType orders the results by the model_type field.
func ByModelType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelType, opts...).ToFunc()
}

// ByModelID orders the results by the model_id field.
func ByModelID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelID, opts...).ToFunc()
}

// ByUseCount orders the results by the use_count field.
func ByUseCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUseCount, opts...).ToFunc()
}

// ByKnowledgeBaseType orders the results by the knowledge_base_type field.
func ByKnowledgeBaseType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKnowledgeBaseType, opts...).ToFunc()
}

// ByInternetSearch orders the results by the internet_search field.
func ByInternetSearch(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldInternetSearch, opts...).ToFunc()
}

// ByAgentType orders the results by the agent_type field.
func ByAgentType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAgentType, opts...).ToFunc()
}

// ByThinking orders the results by the thinking field.
func ByThinking(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldThinking, opts...).ToFunc()
}

// ByThinkingModelID orders the results by the thinking_model_id field.
func ByThinkingModelID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldThinkingModelID, opts...).ToFunc()
}

// ByRoleSetting orders the results by the role_setting field.
func ByRoleSetting(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRoleSetting, opts...).ToFunc()
}

// ByUploadFile orders the results by the upload_file field.
func ByUploadFile(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUploadFile, opts...).ToFunc()
}

// BySemanticCache orders the results by the semantic_cache field.
func BySemanticCache(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSemanticCache, opts...).ToFunc()
}

// ByAiChatCount orders the results by ai_chat count.
func ByAiChatCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newAiChatStep(), opts...)
	}
}

// ByAiChat orders the results by ai_chat terms.
func ByAiChat(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAiChatStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newAiChatStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AiChatInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, AiChatTable, AiChatColumn),
	)
}

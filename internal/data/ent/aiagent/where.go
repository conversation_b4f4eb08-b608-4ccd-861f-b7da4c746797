// Code generated by ent, DO NOT EDIT.

package aiagent

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldDeletedAt, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldName, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldDescription, v))
}

// Avatar applies equality check predicate on the "avatar" field. It's identical to AvatarEQ.
func Avatar(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldAvatar, v))
}

// ClickedAvatar applies equality check predicate on the "clicked_avatar" field. It's identical to ClickedAvatarEQ.
func ClickedAvatar(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldClickedAvatar, v))
}

// WelcomeMsg applies equality check predicate on the "welcome_msg" field. It's identical to WelcomeMsgEQ.
func WelcomeMsg(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldWelcomeMsg, v))
}

// FallbackMsg applies equality check predicate on the "fallback_msg" field. It's identical to FallbackMsgEQ.
func FallbackMsg(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldFallbackMsg, v))
}

// OwnerID applies equality check predicate on the "owner_id" field. It's identical to OwnerIDEQ.
func OwnerID(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldOwnerID, v))
}

// VisibilityType applies equality check predicate on the "visibility_type" field. It's identical to VisibilityTypeEQ.
func VisibilityType(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldVisibilityType, v))
}

// ManageableToUser applies equality check predicate on the "manageable_to_user" field. It's identical to ManageableToUserEQ.
func ManageableToUser(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldManageableToUser, v))
}

// VisibleToUser applies equality check predicate on the "visible_to_user" field. It's identical to VisibleToUserEQ.
func VisibleToUser(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldVisibleToUser, v))
}

// VisibleToDept applies equality check predicate on the "visible_to_dept" field. It's identical to VisibleToDeptEQ.
func VisibleToDept(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldVisibleToDept, v))
}

// KnowledgeBaseIds applies equality check predicate on the "knowledge_base_ids" field. It's identical to KnowledgeBaseIdsEQ.
func KnowledgeBaseIds(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldKnowledgeBaseIds, v))
}

// Schema applies equality check predicate on the "schema" field. It's identical to SchemaEQ.
func Schema(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldSchema, v))
}

// IsPublic applies equality check predicate on the "is_public" field. It's identical to IsPublicEQ.
func IsPublic(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldIsPublic, v))
}

// IsEnabled applies equality check predicate on the "is_enabled" field. It's identical to IsEnabledEQ.
func IsEnabled(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldIsEnabled, v))
}

// IsRefFiles applies equality check predicate on the "is_ref_files" field. It's identical to IsRefFilesEQ.
func IsRefFiles(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldIsRefFiles, v))
}

// ModelType applies equality check predicate on the "model_type" field. It's identical to ModelTypeEQ.
func ModelType(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldModelType, v))
}

// ModelID applies equality check predicate on the "model_id" field. It's identical to ModelIDEQ.
func ModelID(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldModelID, v))
}

// UseCount applies equality check predicate on the "use_count" field. It's identical to UseCountEQ.
func UseCount(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldUseCount, v))
}

// KnowledgeBaseType applies equality check predicate on the "knowledge_base_type" field. It's identical to KnowledgeBaseTypeEQ.
func KnowledgeBaseType(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldKnowledgeBaseType, v))
}

// InternetSearch applies equality check predicate on the "internet_search" field. It's identical to InternetSearchEQ.
func InternetSearch(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldInternetSearch, v))
}

// AgentType applies equality check predicate on the "agent_type" field. It's identical to AgentTypeEQ.
func AgentType(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldAgentType, v))
}

// Thinking applies equality check predicate on the "thinking" field. It's identical to ThinkingEQ.
func Thinking(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldThinking, v))
}

// ThinkingModelID applies equality check predicate on the "thinking_model_id" field. It's identical to ThinkingModelIDEQ.
func ThinkingModelID(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldThinkingModelID, v))
}

// RoleSetting applies equality check predicate on the "role_setting" field. It's identical to RoleSettingEQ.
func RoleSetting(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldRoleSetting, v))
}

// UploadFile applies equality check predicate on the "upload_file" field. It's identical to UploadFileEQ.
func UploadFile(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldUploadFile, v))
}

// SemanticCache applies equality check predicate on the "semantic_cache" field. It's identical to SemanticCacheEQ.
func SemanticCache(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldSemanticCache, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotNull(FieldDeletedAt))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldName, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldDescription, v))
}

// AvatarEQ applies the EQ predicate on the "avatar" field.
func AvatarEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldAvatar, v))
}

// AvatarNEQ applies the NEQ predicate on the "avatar" field.
func AvatarNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldAvatar, v))
}

// AvatarIn applies the In predicate on the "avatar" field.
func AvatarIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldAvatar, vs...))
}

// AvatarNotIn applies the NotIn predicate on the "avatar" field.
func AvatarNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldAvatar, vs...))
}

// AvatarGT applies the GT predicate on the "avatar" field.
func AvatarGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldAvatar, v))
}

// AvatarGTE applies the GTE predicate on the "avatar" field.
func AvatarGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldAvatar, v))
}

// AvatarLT applies the LT predicate on the "avatar" field.
func AvatarLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldAvatar, v))
}

// AvatarLTE applies the LTE predicate on the "avatar" field.
func AvatarLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldAvatar, v))
}

// AvatarContains applies the Contains predicate on the "avatar" field.
func AvatarContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldAvatar, v))
}

// AvatarHasPrefix applies the HasPrefix predicate on the "avatar" field.
func AvatarHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldAvatar, v))
}

// AvatarHasSuffix applies the HasSuffix predicate on the "avatar" field.
func AvatarHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldAvatar, v))
}

// AvatarEqualFold applies the EqualFold predicate on the "avatar" field.
func AvatarEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldAvatar, v))
}

// AvatarContainsFold applies the ContainsFold predicate on the "avatar" field.
func AvatarContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldAvatar, v))
}

// ClickedAvatarEQ applies the EQ predicate on the "clicked_avatar" field.
func ClickedAvatarEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldClickedAvatar, v))
}

// ClickedAvatarNEQ applies the NEQ predicate on the "clicked_avatar" field.
func ClickedAvatarNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldClickedAvatar, v))
}

// ClickedAvatarIn applies the In predicate on the "clicked_avatar" field.
func ClickedAvatarIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldClickedAvatar, vs...))
}

// ClickedAvatarNotIn applies the NotIn predicate on the "clicked_avatar" field.
func ClickedAvatarNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldClickedAvatar, vs...))
}

// ClickedAvatarGT applies the GT predicate on the "clicked_avatar" field.
func ClickedAvatarGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldClickedAvatar, v))
}

// ClickedAvatarGTE applies the GTE predicate on the "clicked_avatar" field.
func ClickedAvatarGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldClickedAvatar, v))
}

// ClickedAvatarLT applies the LT predicate on the "clicked_avatar" field.
func ClickedAvatarLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldClickedAvatar, v))
}

// ClickedAvatarLTE applies the LTE predicate on the "clicked_avatar" field.
func ClickedAvatarLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldClickedAvatar, v))
}

// ClickedAvatarContains applies the Contains predicate on the "clicked_avatar" field.
func ClickedAvatarContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldClickedAvatar, v))
}

// ClickedAvatarHasPrefix applies the HasPrefix predicate on the "clicked_avatar" field.
func ClickedAvatarHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldClickedAvatar, v))
}

// ClickedAvatarHasSuffix applies the HasSuffix predicate on the "clicked_avatar" field.
func ClickedAvatarHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldClickedAvatar, v))
}

// ClickedAvatarEqualFold applies the EqualFold predicate on the "clicked_avatar" field.
func ClickedAvatarEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldClickedAvatar, v))
}

// ClickedAvatarContainsFold applies the ContainsFold predicate on the "clicked_avatar" field.
func ClickedAvatarContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldClickedAvatar, v))
}

// WelcomeMsgEQ applies the EQ predicate on the "welcome_msg" field.
func WelcomeMsgEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldWelcomeMsg, v))
}

// WelcomeMsgNEQ applies the NEQ predicate on the "welcome_msg" field.
func WelcomeMsgNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldWelcomeMsg, v))
}

// WelcomeMsgIn applies the In predicate on the "welcome_msg" field.
func WelcomeMsgIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldWelcomeMsg, vs...))
}

// WelcomeMsgNotIn applies the NotIn predicate on the "welcome_msg" field.
func WelcomeMsgNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldWelcomeMsg, vs...))
}

// WelcomeMsgGT applies the GT predicate on the "welcome_msg" field.
func WelcomeMsgGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldWelcomeMsg, v))
}

// WelcomeMsgGTE applies the GTE predicate on the "welcome_msg" field.
func WelcomeMsgGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldWelcomeMsg, v))
}

// WelcomeMsgLT applies the LT predicate on the "welcome_msg" field.
func WelcomeMsgLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldWelcomeMsg, v))
}

// WelcomeMsgLTE applies the LTE predicate on the "welcome_msg" field.
func WelcomeMsgLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldWelcomeMsg, v))
}

// WelcomeMsgContains applies the Contains predicate on the "welcome_msg" field.
func WelcomeMsgContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldWelcomeMsg, v))
}

// WelcomeMsgHasPrefix applies the HasPrefix predicate on the "welcome_msg" field.
func WelcomeMsgHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldWelcomeMsg, v))
}

// WelcomeMsgHasSuffix applies the HasSuffix predicate on the "welcome_msg" field.
func WelcomeMsgHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldWelcomeMsg, v))
}

// WelcomeMsgEqualFold applies the EqualFold predicate on the "welcome_msg" field.
func WelcomeMsgEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldWelcomeMsg, v))
}

// WelcomeMsgContainsFold applies the ContainsFold predicate on the "welcome_msg" field.
func WelcomeMsgContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldWelcomeMsg, v))
}

// FallbackMsgEQ applies the EQ predicate on the "fallback_msg" field.
func FallbackMsgEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldFallbackMsg, v))
}

// FallbackMsgNEQ applies the NEQ predicate on the "fallback_msg" field.
func FallbackMsgNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldFallbackMsg, v))
}

// FallbackMsgIn applies the In predicate on the "fallback_msg" field.
func FallbackMsgIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldFallbackMsg, vs...))
}

// FallbackMsgNotIn applies the NotIn predicate on the "fallback_msg" field.
func FallbackMsgNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldFallbackMsg, vs...))
}

// FallbackMsgGT applies the GT predicate on the "fallback_msg" field.
func FallbackMsgGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldFallbackMsg, v))
}

// FallbackMsgGTE applies the GTE predicate on the "fallback_msg" field.
func FallbackMsgGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldFallbackMsg, v))
}

// FallbackMsgLT applies the LT predicate on the "fallback_msg" field.
func FallbackMsgLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldFallbackMsg, v))
}

// FallbackMsgLTE applies the LTE predicate on the "fallback_msg" field.
func FallbackMsgLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldFallbackMsg, v))
}

// FallbackMsgContains applies the Contains predicate on the "fallback_msg" field.
func FallbackMsgContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldFallbackMsg, v))
}

// FallbackMsgHasPrefix applies the HasPrefix predicate on the "fallback_msg" field.
func FallbackMsgHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldFallbackMsg, v))
}

// FallbackMsgHasSuffix applies the HasSuffix predicate on the "fallback_msg" field.
func FallbackMsgHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldFallbackMsg, v))
}

// FallbackMsgEqualFold applies the EqualFold predicate on the "fallback_msg" field.
func FallbackMsgEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldFallbackMsg, v))
}

// FallbackMsgContainsFold applies the ContainsFold predicate on the "fallback_msg" field.
func FallbackMsgContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldFallbackMsg, v))
}

// OwnerIDEQ applies the EQ predicate on the "owner_id" field.
func OwnerIDEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldOwnerID, v))
}

// OwnerIDNEQ applies the NEQ predicate on the "owner_id" field.
func OwnerIDNEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldOwnerID, v))
}

// OwnerIDIn applies the In predicate on the "owner_id" field.
func OwnerIDIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldOwnerID, vs...))
}

// OwnerIDNotIn applies the NotIn predicate on the "owner_id" field.
func OwnerIDNotIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldOwnerID, vs...))
}

// OwnerIDGT applies the GT predicate on the "owner_id" field.
func OwnerIDGT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldOwnerID, v))
}

// OwnerIDGTE applies the GTE predicate on the "owner_id" field.
func OwnerIDGTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldOwnerID, v))
}

// OwnerIDLT applies the LT predicate on the "owner_id" field.
func OwnerIDLT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldOwnerID, v))
}

// OwnerIDLTE applies the LTE predicate on the "owner_id" field.
func OwnerIDLTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldOwnerID, v))
}

// VisibilityTypeEQ applies the EQ predicate on the "visibility_type" field.
func VisibilityTypeEQ(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldVisibilityType, v))
}

// VisibilityTypeNEQ applies the NEQ predicate on the "visibility_type" field.
func VisibilityTypeNEQ(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldVisibilityType, v))
}

// VisibilityTypeIn applies the In predicate on the "visibility_type" field.
func VisibilityTypeIn(vs ...int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldVisibilityType, vs...))
}

// VisibilityTypeNotIn applies the NotIn predicate on the "visibility_type" field.
func VisibilityTypeNotIn(vs ...int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldVisibilityType, vs...))
}

// VisibilityTypeGT applies the GT predicate on the "visibility_type" field.
func VisibilityTypeGT(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldVisibilityType, v))
}

// VisibilityTypeGTE applies the GTE predicate on the "visibility_type" field.
func VisibilityTypeGTE(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldVisibilityType, v))
}

// VisibilityTypeLT applies the LT predicate on the "visibility_type" field.
func VisibilityTypeLT(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldVisibilityType, v))
}

// VisibilityTypeLTE applies the LTE predicate on the "visibility_type" field.
func VisibilityTypeLTE(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldVisibilityType, v))
}

// ManageableToUserEQ applies the EQ predicate on the "manageable_to_user" field.
func ManageableToUserEQ(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldManageableToUser, v))
}

// ManageableToUserNEQ applies the NEQ predicate on the "manageable_to_user" field.
func ManageableToUserNEQ(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldManageableToUser, v))
}

// ManageableToUserIn applies the In predicate on the "manageable_to_user" field.
func ManageableToUserIn(vs ...*pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldManageableToUser, vs...))
}

// ManageableToUserNotIn applies the NotIn predicate on the "manageable_to_user" field.
func ManageableToUserNotIn(vs ...*pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldManageableToUser, vs...))
}

// ManageableToUserGT applies the GT predicate on the "manageable_to_user" field.
func ManageableToUserGT(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldManageableToUser, v))
}

// ManageableToUserGTE applies the GTE predicate on the "manageable_to_user" field.
func ManageableToUserGTE(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldManageableToUser, v))
}

// ManageableToUserLT applies the LT predicate on the "manageable_to_user" field.
func ManageableToUserLT(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldManageableToUser, v))
}

// ManageableToUserLTE applies the LTE predicate on the "manageable_to_user" field.
func ManageableToUserLTE(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldManageableToUser, v))
}

// VisibleToUserEQ applies the EQ predicate on the "visible_to_user" field.
func VisibleToUserEQ(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldVisibleToUser, v))
}

// VisibleToUserNEQ applies the NEQ predicate on the "visible_to_user" field.
func VisibleToUserNEQ(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldVisibleToUser, v))
}

// VisibleToUserIn applies the In predicate on the "visible_to_user" field.
func VisibleToUserIn(vs ...*pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldVisibleToUser, vs...))
}

// VisibleToUserNotIn applies the NotIn predicate on the "visible_to_user" field.
func VisibleToUserNotIn(vs ...*pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldVisibleToUser, vs...))
}

// VisibleToUserGT applies the GT predicate on the "visible_to_user" field.
func VisibleToUserGT(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldVisibleToUser, v))
}

// VisibleToUserGTE applies the GTE predicate on the "visible_to_user" field.
func VisibleToUserGTE(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldVisibleToUser, v))
}

// VisibleToUserLT applies the LT predicate on the "visible_to_user" field.
func VisibleToUserLT(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldVisibleToUser, v))
}

// VisibleToUserLTE applies the LTE predicate on the "visible_to_user" field.
func VisibleToUserLTE(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldVisibleToUser, v))
}

// VisibleToDeptEQ applies the EQ predicate on the "visible_to_dept" field.
func VisibleToDeptEQ(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldVisibleToDept, v))
}

// VisibleToDeptNEQ applies the NEQ predicate on the "visible_to_dept" field.
func VisibleToDeptNEQ(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldVisibleToDept, v))
}

// VisibleToDeptIn applies the In predicate on the "visible_to_dept" field.
func VisibleToDeptIn(vs ...*pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldVisibleToDept, vs...))
}

// VisibleToDeptNotIn applies the NotIn predicate on the "visible_to_dept" field.
func VisibleToDeptNotIn(vs ...*pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldVisibleToDept, vs...))
}

// VisibleToDeptGT applies the GT predicate on the "visible_to_dept" field.
func VisibleToDeptGT(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldVisibleToDept, v))
}

// VisibleToDeptGTE applies the GTE predicate on the "visible_to_dept" field.
func VisibleToDeptGTE(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldVisibleToDept, v))
}

// VisibleToDeptLT applies the LT predicate on the "visible_to_dept" field.
func VisibleToDeptLT(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldVisibleToDept, v))
}

// VisibleToDeptLTE applies the LTE predicate on the "visible_to_dept" field.
func VisibleToDeptLTE(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldVisibleToDept, v))
}

// KnowledgeBaseIdsEQ applies the EQ predicate on the "knowledge_base_ids" field.
func KnowledgeBaseIdsEQ(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldKnowledgeBaseIds, v))
}

// KnowledgeBaseIdsNEQ applies the NEQ predicate on the "knowledge_base_ids" field.
func KnowledgeBaseIdsNEQ(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldKnowledgeBaseIds, v))
}

// KnowledgeBaseIdsIn applies the In predicate on the "knowledge_base_ids" field.
func KnowledgeBaseIdsIn(vs ...*pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldKnowledgeBaseIds, vs...))
}

// KnowledgeBaseIdsNotIn applies the NotIn predicate on the "knowledge_base_ids" field.
func KnowledgeBaseIdsNotIn(vs ...*pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldKnowledgeBaseIds, vs...))
}

// KnowledgeBaseIdsGT applies the GT predicate on the "knowledge_base_ids" field.
func KnowledgeBaseIdsGT(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldKnowledgeBaseIds, v))
}

// KnowledgeBaseIdsGTE applies the GTE predicate on the "knowledge_base_ids" field.
func KnowledgeBaseIdsGTE(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldKnowledgeBaseIds, v))
}

// KnowledgeBaseIdsLT applies the LT predicate on the "knowledge_base_ids" field.
func KnowledgeBaseIdsLT(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldKnowledgeBaseIds, v))
}

// KnowledgeBaseIdsLTE applies the LTE predicate on the "knowledge_base_ids" field.
func KnowledgeBaseIdsLTE(v *pq.Int64Array) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldKnowledgeBaseIds, v))
}

// SchemaEQ applies the EQ predicate on the "schema" field.
func SchemaEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldSchema, v))
}

// SchemaNEQ applies the NEQ predicate on the "schema" field.
func SchemaNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldSchema, v))
}

// SchemaIn applies the In predicate on the "schema" field.
func SchemaIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldSchema, vs...))
}

// SchemaNotIn applies the NotIn predicate on the "schema" field.
func SchemaNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldSchema, vs...))
}

// SchemaGT applies the GT predicate on the "schema" field.
func SchemaGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldSchema, v))
}

// SchemaGTE applies the GTE predicate on the "schema" field.
func SchemaGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldSchema, v))
}

// SchemaLT applies the LT predicate on the "schema" field.
func SchemaLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldSchema, v))
}

// SchemaLTE applies the LTE predicate on the "schema" field.
func SchemaLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldSchema, v))
}

// SchemaContains applies the Contains predicate on the "schema" field.
func SchemaContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldSchema, v))
}

// SchemaHasPrefix applies the HasPrefix predicate on the "schema" field.
func SchemaHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldSchema, v))
}

// SchemaHasSuffix applies the HasSuffix predicate on the "schema" field.
func SchemaHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldSchema, v))
}

// SchemaEqualFold applies the EqualFold predicate on the "schema" field.
func SchemaEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldSchema, v))
}

// SchemaContainsFold applies the ContainsFold predicate on the "schema" field.
func SchemaContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldSchema, v))
}

// IsPublicEQ applies the EQ predicate on the "is_public" field.
func IsPublicEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldIsPublic, v))
}

// IsPublicNEQ applies the NEQ predicate on the "is_public" field.
func IsPublicNEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldIsPublic, v))
}

// IsEnabledEQ applies the EQ predicate on the "is_enabled" field.
func IsEnabledEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldIsEnabled, v))
}

// IsEnabledNEQ applies the NEQ predicate on the "is_enabled" field.
func IsEnabledNEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldIsEnabled, v))
}

// IsRefFilesEQ applies the EQ predicate on the "is_ref_files" field.
func IsRefFilesEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldIsRefFiles, v))
}

// IsRefFilesNEQ applies the NEQ predicate on the "is_ref_files" field.
func IsRefFilesNEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldIsRefFiles, v))
}

// ModelTypeEQ applies the EQ predicate on the "model_type" field.
func ModelTypeEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldModelType, v))
}

// ModelTypeNEQ applies the NEQ predicate on the "model_type" field.
func ModelTypeNEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldModelType, v))
}

// ModelTypeIn applies the In predicate on the "model_type" field.
func ModelTypeIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldModelType, vs...))
}

// ModelTypeNotIn applies the NotIn predicate on the "model_type" field.
func ModelTypeNotIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldModelType, vs...))
}

// ModelTypeGT applies the GT predicate on the "model_type" field.
func ModelTypeGT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldModelType, v))
}

// ModelTypeGTE applies the GTE predicate on the "model_type" field.
func ModelTypeGTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldModelType, v))
}

// ModelTypeLT applies the LT predicate on the "model_type" field.
func ModelTypeLT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldModelType, v))
}

// ModelTypeLTE applies the LTE predicate on the "model_type" field.
func ModelTypeLTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldModelType, v))
}

// ModelIDEQ applies the EQ predicate on the "model_id" field.
func ModelIDEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldModelID, v))
}

// ModelIDNEQ applies the NEQ predicate on the "model_id" field.
func ModelIDNEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldModelID, v))
}

// ModelIDIn applies the In predicate on the "model_id" field.
func ModelIDIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldModelID, vs...))
}

// ModelIDNotIn applies the NotIn predicate on the "model_id" field.
func ModelIDNotIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldModelID, vs...))
}

// ModelIDGT applies the GT predicate on the "model_id" field.
func ModelIDGT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldModelID, v))
}

// ModelIDGTE applies the GTE predicate on the "model_id" field.
func ModelIDGTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldModelID, v))
}

// ModelIDLT applies the LT predicate on the "model_id" field.
func ModelIDLT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldModelID, v))
}

// ModelIDLTE applies the LTE predicate on the "model_id" field.
func ModelIDLTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldModelID, v))
}

// UseCountEQ applies the EQ predicate on the "use_count" field.
func UseCountEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldUseCount, v))
}

// UseCountNEQ applies the NEQ predicate on the "use_count" field.
func UseCountNEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldUseCount, v))
}

// UseCountIn applies the In predicate on the "use_count" field.
func UseCountIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldUseCount, vs...))
}

// UseCountNotIn applies the NotIn predicate on the "use_count" field.
func UseCountNotIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldUseCount, vs...))
}

// UseCountGT applies the GT predicate on the "use_count" field.
func UseCountGT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldUseCount, v))
}

// UseCountGTE applies the GTE predicate on the "use_count" field.
func UseCountGTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldUseCount, v))
}

// UseCountLT applies the LT predicate on the "use_count" field.
func UseCountLT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldUseCount, v))
}

// UseCountLTE applies the LTE predicate on the "use_count" field.
func UseCountLTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldUseCount, v))
}

// KnowledgeBaseTypeEQ applies the EQ predicate on the "knowledge_base_type" field.
func KnowledgeBaseTypeEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldKnowledgeBaseType, v))
}

// KnowledgeBaseTypeNEQ applies the NEQ predicate on the "knowledge_base_type" field.
func KnowledgeBaseTypeNEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldKnowledgeBaseType, v))
}

// KnowledgeBaseTypeIn applies the In predicate on the "knowledge_base_type" field.
func KnowledgeBaseTypeIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldKnowledgeBaseType, vs...))
}

// KnowledgeBaseTypeNotIn applies the NotIn predicate on the "knowledge_base_type" field.
func KnowledgeBaseTypeNotIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldKnowledgeBaseType, vs...))
}

// KnowledgeBaseTypeGT applies the GT predicate on the "knowledge_base_type" field.
func KnowledgeBaseTypeGT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldKnowledgeBaseType, v))
}

// KnowledgeBaseTypeGTE applies the GTE predicate on the "knowledge_base_type" field.
func KnowledgeBaseTypeGTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldKnowledgeBaseType, v))
}

// KnowledgeBaseTypeLT applies the LT predicate on the "knowledge_base_type" field.
func KnowledgeBaseTypeLT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldKnowledgeBaseType, v))
}

// KnowledgeBaseTypeLTE applies the LTE predicate on the "knowledge_base_type" field.
func KnowledgeBaseTypeLTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldKnowledgeBaseType, v))
}

// InternetSearchEQ applies the EQ predicate on the "internet_search" field.
func InternetSearchEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldInternetSearch, v))
}

// InternetSearchNEQ applies the NEQ predicate on the "internet_search" field.
func InternetSearchNEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldInternetSearch, v))
}

// AgentTypeEQ applies the EQ predicate on the "agent_type" field.
func AgentTypeEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldAgentType, v))
}

// AgentTypeNEQ applies the NEQ predicate on the "agent_type" field.
func AgentTypeNEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldAgentType, v))
}

// AgentTypeIn applies the In predicate on the "agent_type" field.
func AgentTypeIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldAgentType, vs...))
}

// AgentTypeNotIn applies the NotIn predicate on the "agent_type" field.
func AgentTypeNotIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldAgentType, vs...))
}

// AgentTypeGT applies the GT predicate on the "agent_type" field.
func AgentTypeGT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldAgentType, v))
}

// AgentTypeGTE applies the GTE predicate on the "agent_type" field.
func AgentTypeGTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldAgentType, v))
}

// AgentTypeLT applies the LT predicate on the "agent_type" field.
func AgentTypeLT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldAgentType, v))
}

// AgentTypeLTE applies the LTE predicate on the "agent_type" field.
func AgentTypeLTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldAgentType, v))
}

// ThinkingEQ applies the EQ predicate on the "thinking" field.
func ThinkingEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldThinking, v))
}

// ThinkingNEQ applies the NEQ predicate on the "thinking" field.
func ThinkingNEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldThinking, v))
}

// ThinkingModelIDEQ applies the EQ predicate on the "thinking_model_id" field.
func ThinkingModelIDEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldThinkingModelID, v))
}

// ThinkingModelIDNEQ applies the NEQ predicate on the "thinking_model_id" field.
func ThinkingModelIDNEQ(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldThinkingModelID, v))
}

// ThinkingModelIDIn applies the In predicate on the "thinking_model_id" field.
func ThinkingModelIDIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldThinkingModelID, vs...))
}

// ThinkingModelIDNotIn applies the NotIn predicate on the "thinking_model_id" field.
func ThinkingModelIDNotIn(vs ...int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldThinkingModelID, vs...))
}

// ThinkingModelIDGT applies the GT predicate on the "thinking_model_id" field.
func ThinkingModelIDGT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldThinkingModelID, v))
}

// ThinkingModelIDGTE applies the GTE predicate on the "thinking_model_id" field.
func ThinkingModelIDGTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldThinkingModelID, v))
}

// ThinkingModelIDLT applies the LT predicate on the "thinking_model_id" field.
func ThinkingModelIDLT(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldThinkingModelID, v))
}

// ThinkingModelIDLTE applies the LTE predicate on the "thinking_model_id" field.
func ThinkingModelIDLTE(v int64) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldThinkingModelID, v))
}

// RoleSettingEQ applies the EQ predicate on the "role_setting" field.
func RoleSettingEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldRoleSetting, v))
}

// RoleSettingNEQ applies the NEQ predicate on the "role_setting" field.
func RoleSettingNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldRoleSetting, v))
}

// RoleSettingIn applies the In predicate on the "role_setting" field.
func RoleSettingIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldRoleSetting, vs...))
}

// RoleSettingNotIn applies the NotIn predicate on the "role_setting" field.
func RoleSettingNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldRoleSetting, vs...))
}

// RoleSettingGT applies the GT predicate on the "role_setting" field.
func RoleSettingGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldRoleSetting, v))
}

// RoleSettingGTE applies the GTE predicate on the "role_setting" field.
func RoleSettingGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldRoleSetting, v))
}

// RoleSettingLT applies the LT predicate on the "role_setting" field.
func RoleSettingLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldRoleSetting, v))
}

// RoleSettingLTE applies the LTE predicate on the "role_setting" field.
func RoleSettingLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldRoleSetting, v))
}

// RoleSettingContains applies the Contains predicate on the "role_setting" field.
func RoleSettingContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldRoleSetting, v))
}

// RoleSettingHasPrefix applies the HasPrefix predicate on the "role_setting" field.
func RoleSettingHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldRoleSetting, v))
}

// RoleSettingHasSuffix applies the HasSuffix predicate on the "role_setting" field.
func RoleSettingHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldRoleSetting, v))
}

// RoleSettingEqualFold applies the EqualFold predicate on the "role_setting" field.
func RoleSettingEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldRoleSetting, v))
}

// RoleSettingContainsFold applies the ContainsFold predicate on the "role_setting" field.
func RoleSettingContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldRoleSetting, v))
}

// UploadFileEQ applies the EQ predicate on the "upload_file" field.
func UploadFileEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldUploadFile, v))
}

// UploadFileNEQ applies the NEQ predicate on the "upload_file" field.
func UploadFileNEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldUploadFile, v))
}

// SemanticCacheEQ applies the EQ predicate on the "semantic_cache" field.
func SemanticCacheEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldSemanticCache, v))
}

// SemanticCacheNEQ applies the NEQ predicate on the "semantic_cache" field.
func SemanticCacheNEQ(v bool) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldSemanticCache, v))
}

// HasAiChat applies the HasEdge predicate on the "ai_chat" edge.
func HasAiChat() predicate.AiAgent {
	return predicate.AiAgent(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, AiChatTable, AiChatColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAiChatWith applies the HasEdge predicate on the "ai_chat" edge with a given conditions (other predicates).
func HasAiChatWith(preds ...predicate.AiChat) predicate.AiAgent {
	return predicate.AiAgent(func(s *sql.Selector) {
		step := newAiChatStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AiAgent) predicate.AiAgent {
	return predicate.AiAgent(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AiAgent) predicate.AiAgent {
	return predicate.AiAgent(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AiAgent) predicate.AiAgent {
	return predicate.AiAgent(sql.NotPredicates(p))
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritylog"
)

// AiAgentSecurityLogCreate is the builder for creating a AiAgentSecurityLog entity.
type AiAgentSecurityLogCreate struct {
	config
	mutation *AiAgentSecurityLogMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (aaslc *AiAgentSecurityLogCreate) SetCreatedAt(t time.Time) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetCreatedAt(t)
	return aaslc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (aaslc *AiAgentSecurityLogCreate) SetNillableCreatedAt(t *time.Time) *AiAgentSecurityLogCreate {
	if t != nil {
		aaslc.SetCreatedAt(*t)
	}
	return aaslc
}

// SetUpdatedAt sets the "updated_at" field.
func (aaslc *AiAgentSecurityLogCreate) SetUpdatedAt(t time.Time) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetUpdatedAt(t)
	return aaslc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (aaslc *AiAgentSecurityLogCreate) SetNillableUpdatedAt(t *time.Time) *AiAgentSecurityLogCreate {
	if t != nil {
		aaslc.SetUpdatedAt(*t)
	}
	return aaslc
}

// SetDeletedAt sets the "deleted_at" field.
func (aaslc *AiAgentSecurityLogCreate) SetDeletedAt(t time.Time) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetDeletedAt(t)
	return aaslc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aaslc *AiAgentSecurityLogCreate) SetNillableDeletedAt(t *time.Time) *AiAgentSecurityLogCreate {
	if t != nil {
		aaslc.SetDeletedAt(*t)
	}
	return aaslc
}

// SetRiskLevel sets the "risk_level" field.
func (aaslc *AiAgentSecurityLogCreate) SetRiskLevel(i int64) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetRiskLevel(i)
	return aaslc
}

// SetUserID sets the "user_id" field.
func (aaslc *AiAgentSecurityLogCreate) SetUserID(i int64) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetUserID(i)
	return aaslc
}

// SetUserName sets the "user_name" field.
func (aaslc *AiAgentSecurityLogCreate) SetUserName(s string) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetUserName(s)
	return aaslc
}

// SetDeptID sets the "dept_id" field.
func (aaslc *AiAgentSecurityLogCreate) SetDeptID(i int64) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetDeptID(i)
	return aaslc
}

// SetDeptName sets the "dept_name" field.
func (aaslc *AiAgentSecurityLogCreate) SetDeptName(s string) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetDeptName(s)
	return aaslc
}

// SetPcName sets the "pc_name" field.
func (aaslc *AiAgentSecurityLogCreate) SetPcName(s string) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetPcName(s)
	return aaslc
}

// SetAgentID sets the "agent_id" field.
func (aaslc *AiAgentSecurityLogCreate) SetAgentID(i int64) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetAgentID(i)
	return aaslc
}

// SetAgentName sets the "agent_name" field.
func (aaslc *AiAgentSecurityLogCreate) SetAgentName(s string) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetAgentName(s)
	return aaslc
}

// SetAgentDescription sets the "agent_description" field.
func (aaslc *AiAgentSecurityLogCreate) SetAgentDescription(s string) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetAgentDescription(s)
	return aaslc
}

// SetHitAction sets the "hit_action" field.
func (aaslc *AiAgentSecurityLogCreate) SetHitAction(i int64) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetHitAction(i)
	return aaslc
}

// SetNillableHitAction sets the "hit_action" field if the given value is not nil.
func (aaslc *AiAgentSecurityLogCreate) SetNillableHitAction(i *int64) *AiAgentSecurityLogCreate {
	if i != nil {
		aaslc.SetHitAction(*i)
	}
	return aaslc
}

// SetQuestion sets the "question" field.
func (aaslc *AiAgentSecurityLogCreate) SetQuestion(s string) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetQuestion(s)
	return aaslc
}

// SetActionCategory sets the "action_category" field.
func (aaslc *AiAgentSecurityLogCreate) SetActionCategory(i int64) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetActionCategory(i)
	return aaslc
}

// SetNillableActionCategory sets the "action_category" field if the given value is not nil.
func (aaslc *AiAgentSecurityLogCreate) SetNillableActionCategory(i *int64) *AiAgentSecurityLogCreate {
	if i != nil {
		aaslc.SetActionCategory(*i)
	}
	return aaslc
}

// SetUploadedFiles sets the "uploaded_files" field.
func (aaslc *AiAgentSecurityLogCreate) SetUploadedFiles(pa *pq.StringArray) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetUploadedFiles(pa)
	return aaslc
}

// SetHitPolicies sets the "hit_policies" field.
func (aaslc *AiAgentSecurityLogCreate) SetHitPolicies(pa *pq.StringArray) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetHitPolicies(pa)
	return aaslc
}

// SetID sets the "id" field.
func (aaslc *AiAgentSecurityLogCreate) SetID(i int64) *AiAgentSecurityLogCreate {
	aaslc.mutation.SetID(i)
	return aaslc
}

// Mutation returns the AiAgentSecurityLogMutation object of the builder.
func (aaslc *AiAgentSecurityLogCreate) Mutation() *AiAgentSecurityLogMutation {
	return aaslc.mutation
}

// Save creates the AiAgentSecurityLog in the database.
func (aaslc *AiAgentSecurityLogCreate) Save(ctx context.Context) (*AiAgentSecurityLog, error) {
	if err := aaslc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, aaslc.sqlSave, aaslc.mutation, aaslc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (aaslc *AiAgentSecurityLogCreate) SaveX(ctx context.Context) *AiAgentSecurityLog {
	v, err := aaslc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aaslc *AiAgentSecurityLogCreate) Exec(ctx context.Context) error {
	_, err := aaslc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aaslc *AiAgentSecurityLogCreate) ExecX(ctx context.Context) {
	if err := aaslc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aaslc *AiAgentSecurityLogCreate) defaults() error {
	if _, ok := aaslc.mutation.CreatedAt(); !ok {
		if aiagentsecuritylog.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagentsecuritylog.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := aiagentsecuritylog.DefaultCreatedAt()
		aaslc.mutation.SetCreatedAt(v)
	}
	if _, ok := aaslc.mutation.UpdatedAt(); !ok {
		if aiagentsecuritylog.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagentsecuritylog.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aiagentsecuritylog.DefaultUpdatedAt()
		aaslc.mutation.SetUpdatedAt(v)
	}
	if _, ok := aaslc.mutation.DeletedAt(); !ok {
		v := aiagentsecuritylog.DefaultDeletedAt
		aaslc.mutation.SetDeletedAt(v)
	}
	if _, ok := aaslc.mutation.HitAction(); !ok {
		v := aiagentsecuritylog.DefaultHitAction
		aaslc.mutation.SetHitAction(v)
	}
	if _, ok := aaslc.mutation.ActionCategory(); !ok {
		v := aiagentsecuritylog.DefaultActionCategory
		aaslc.mutation.SetActionCategory(v)
	}
	if _, ok := aaslc.mutation.UploadedFiles(); !ok {
		v := aiagentsecuritylog.DefaultUploadedFiles
		aaslc.mutation.SetUploadedFiles(v)
	}
	if _, ok := aaslc.mutation.HitPolicies(); !ok {
		v := aiagentsecuritylog.DefaultHitPolicies
		aaslc.mutation.SetHitPolicies(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (aaslc *AiAgentSecurityLogCreate) check() error {
	if _, ok := aaslc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AiAgentSecurityLog.created_at"`)}
	}
	if _, ok := aaslc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AiAgentSecurityLog.updated_at"`)}
	}
	if _, ok := aaslc.mutation.RiskLevel(); !ok {
		return &ValidationError{Name: "risk_level", err: errors.New(`ent: missing required field "AiAgentSecurityLog.risk_level"`)}
	}
	if _, ok := aaslc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "AiAgentSecurityLog.user_id"`)}
	}
	if _, ok := aaslc.mutation.UserName(); !ok {
		return &ValidationError{Name: "user_name", err: errors.New(`ent: missing required field "AiAgentSecurityLog.user_name"`)}
	}
	if _, ok := aaslc.mutation.DeptID(); !ok {
		return &ValidationError{Name: "dept_id", err: errors.New(`ent: missing required field "AiAgentSecurityLog.dept_id"`)}
	}
	if _, ok := aaslc.mutation.DeptName(); !ok {
		return &ValidationError{Name: "dept_name", err: errors.New(`ent: missing required field "AiAgentSecurityLog.dept_name"`)}
	}
	if _, ok := aaslc.mutation.PcName(); !ok {
		return &ValidationError{Name: "pc_name", err: errors.New(`ent: missing required field "AiAgentSecurityLog.pc_name"`)}
	}
	if _, ok := aaslc.mutation.AgentID(); !ok {
		return &ValidationError{Name: "agent_id", err: errors.New(`ent: missing required field "AiAgentSecurityLog.agent_id"`)}
	}
	if _, ok := aaslc.mutation.AgentName(); !ok {
		return &ValidationError{Name: "agent_name", err: errors.New(`ent: missing required field "AiAgentSecurityLog.agent_name"`)}
	}
	if _, ok := aaslc.mutation.AgentDescription(); !ok {
		return &ValidationError{Name: "agent_description", err: errors.New(`ent: missing required field "AiAgentSecurityLog.agent_description"`)}
	}
	if _, ok := aaslc.mutation.HitAction(); !ok {
		return &ValidationError{Name: "hit_action", err: errors.New(`ent: missing required field "AiAgentSecurityLog.hit_action"`)}
	}
	if _, ok := aaslc.mutation.Question(); !ok {
		return &ValidationError{Name: "question", err: errors.New(`ent: missing required field "AiAgentSecurityLog.question"`)}
	}
	if _, ok := aaslc.mutation.ActionCategory(); !ok {
		return &ValidationError{Name: "action_category", err: errors.New(`ent: missing required field "AiAgentSecurityLog.action_category"`)}
	}
	if _, ok := aaslc.mutation.UploadedFiles(); !ok {
		return &ValidationError{Name: "uploaded_files", err: errors.New(`ent: missing required field "AiAgentSecurityLog.uploaded_files"`)}
	}
	if _, ok := aaslc.mutation.HitPolicies(); !ok {
		return &ValidationError{Name: "hit_policies", err: errors.New(`ent: missing required field "AiAgentSecurityLog.hit_policies"`)}
	}
	return nil
}

func (aaslc *AiAgentSecurityLogCreate) sqlSave(ctx context.Context) (*AiAgentSecurityLog, error) {
	if err := aaslc.check(); err != nil {
		return nil, err
	}
	_node, _spec := aaslc.createSpec()
	if err := sqlgraph.CreateNode(ctx, aaslc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	aaslc.mutation.id = &_node.ID
	aaslc.mutation.done = true
	return _node, nil
}

func (aaslc *AiAgentSecurityLogCreate) createSpec() (*AiAgentSecurityLog, *sqlgraph.CreateSpec) {
	var (
		_node = &AiAgentSecurityLog{config: aaslc.config}
		_spec = sqlgraph.NewCreateSpec(aiagentsecuritylog.Table, sqlgraph.NewFieldSpec(aiagentsecuritylog.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = aaslc.conflict
	if id, ok := aaslc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := aaslc.mutation.CreatedAt(); ok {
		_spec.SetField(aiagentsecuritylog.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := aaslc.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := aaslc.mutation.DeletedAt(); ok {
		_spec.SetField(aiagentsecuritylog.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := aaslc.mutation.RiskLevel(); ok {
		_spec.SetField(aiagentsecuritylog.FieldRiskLevel, field.TypeInt64, value)
		_node.RiskLevel = value
	}
	if value, ok := aaslc.mutation.UserID(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUserID, field.TypeInt64, value)
		_node.UserID = value
	}
	if value, ok := aaslc.mutation.UserName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUserName, field.TypeString, value)
		_node.UserName = value
	}
	if value, ok := aaslc.mutation.DeptID(); ok {
		_spec.SetField(aiagentsecuritylog.FieldDeptID, field.TypeInt64, value)
		_node.DeptID = value
	}
	if value, ok := aaslc.mutation.DeptName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldDeptName, field.TypeString, value)
		_node.DeptName = value
	}
	if value, ok := aaslc.mutation.PcName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldPcName, field.TypeString, value)
		_node.PcName = value
	}
	if value, ok := aaslc.mutation.AgentID(); ok {
		_spec.SetField(aiagentsecuritylog.FieldAgentID, field.TypeInt64, value)
		_node.AgentID = value
	}
	if value, ok := aaslc.mutation.AgentName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldAgentName, field.TypeString, value)
		_node.AgentName = value
	}
	if value, ok := aaslc.mutation.AgentDescription(); ok {
		_spec.SetField(aiagentsecuritylog.FieldAgentDescription, field.TypeString, value)
		_node.AgentDescription = value
	}
	if value, ok := aaslc.mutation.HitAction(); ok {
		_spec.SetField(aiagentsecuritylog.FieldHitAction, field.TypeInt64, value)
		_node.HitAction = value
	}
	if value, ok := aaslc.mutation.Question(); ok {
		_spec.SetField(aiagentsecuritylog.FieldQuestion, field.TypeString, value)
		_node.Question = value
	}
	if value, ok := aaslc.mutation.ActionCategory(); ok {
		_spec.SetField(aiagentsecuritylog.FieldActionCategory, field.TypeInt64, value)
		_node.ActionCategory = value
	}
	if value, ok := aaslc.mutation.UploadedFiles(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUploadedFiles, field.TypeOther, value)
		_node.UploadedFiles = value
	}
	if value, ok := aaslc.mutation.HitPolicies(); ok {
		_spec.SetField(aiagentsecuritylog.FieldHitPolicies, field.TypeOther, value)
		_node.HitPolicies = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiAgentSecurityLog.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiAgentSecurityLogUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (aaslc *AiAgentSecurityLogCreate) OnConflict(opts ...sql.ConflictOption) *AiAgentSecurityLogUpsertOne {
	aaslc.conflict = opts
	return &AiAgentSecurityLogUpsertOne{
		create: aaslc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiAgentSecurityLog.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aaslc *AiAgentSecurityLogCreate) OnConflictColumns(columns ...string) *AiAgentSecurityLogUpsertOne {
	aaslc.conflict = append(aaslc.conflict, sql.ConflictColumns(columns...))
	return &AiAgentSecurityLogUpsertOne{
		create: aaslc,
	}
}

type (
	// AiAgentSecurityLogUpsertOne is the builder for "upsert"-ing
	//  one AiAgentSecurityLog node.
	AiAgentSecurityLogUpsertOne struct {
		create *AiAgentSecurityLogCreate
	}

	// AiAgentSecurityLogUpsert is the "OnConflict" setter.
	AiAgentSecurityLogUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *AiAgentSecurityLogUpsert) SetUpdatedAt(v time.Time) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateUpdatedAt() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiAgentSecurityLogUpsert) SetDeletedAt(v time.Time) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateDeletedAt() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiAgentSecurityLogUpsert) ClearDeletedAt() *AiAgentSecurityLogUpsert {
	u.SetNull(aiagentsecuritylog.FieldDeletedAt)
	return u
}

// SetRiskLevel sets the "risk_level" field.
func (u *AiAgentSecurityLogUpsert) SetRiskLevel(v int64) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldRiskLevel, v)
	return u
}

// UpdateRiskLevel sets the "risk_level" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateRiskLevel() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldRiskLevel)
	return u
}

// AddRiskLevel adds v to the "risk_level" field.
func (u *AiAgentSecurityLogUpsert) AddRiskLevel(v int64) *AiAgentSecurityLogUpsert {
	u.Add(aiagentsecuritylog.FieldRiskLevel, v)
	return u
}

// SetUserID sets the "user_id" field.
func (u *AiAgentSecurityLogUpsert) SetUserID(v int64) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateUserID() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldUserID)
	return u
}

// AddUserID adds v to the "user_id" field.
func (u *AiAgentSecurityLogUpsert) AddUserID(v int64) *AiAgentSecurityLogUpsert {
	u.Add(aiagentsecuritylog.FieldUserID, v)
	return u
}

// SetUserName sets the "user_name" field.
func (u *AiAgentSecurityLogUpsert) SetUserName(v string) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldUserName, v)
	return u
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateUserName() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldUserName)
	return u
}

// SetDeptID sets the "dept_id" field.
func (u *AiAgentSecurityLogUpsert) SetDeptID(v int64) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldDeptID, v)
	return u
}

// UpdateDeptID sets the "dept_id" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateDeptID() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldDeptID)
	return u
}

// AddDeptID adds v to the "dept_id" field.
func (u *AiAgentSecurityLogUpsert) AddDeptID(v int64) *AiAgentSecurityLogUpsert {
	u.Add(aiagentsecuritylog.FieldDeptID, v)
	return u
}

// SetDeptName sets the "dept_name" field.
func (u *AiAgentSecurityLogUpsert) SetDeptName(v string) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldDeptName, v)
	return u
}

// UpdateDeptName sets the "dept_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateDeptName() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldDeptName)
	return u
}

// SetPcName sets the "pc_name" field.
func (u *AiAgentSecurityLogUpsert) SetPcName(v string) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldPcName, v)
	return u
}

// UpdatePcName sets the "pc_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdatePcName() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldPcName)
	return u
}

// SetAgentID sets the "agent_id" field.
func (u *AiAgentSecurityLogUpsert) SetAgentID(v int64) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldAgentID, v)
	return u
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateAgentID() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldAgentID)
	return u
}

// AddAgentID adds v to the "agent_id" field.
func (u *AiAgentSecurityLogUpsert) AddAgentID(v int64) *AiAgentSecurityLogUpsert {
	u.Add(aiagentsecuritylog.FieldAgentID, v)
	return u
}

// SetAgentName sets the "agent_name" field.
func (u *AiAgentSecurityLogUpsert) SetAgentName(v string) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldAgentName, v)
	return u
}

// UpdateAgentName sets the "agent_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateAgentName() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldAgentName)
	return u
}

// SetAgentDescription sets the "agent_description" field.
func (u *AiAgentSecurityLogUpsert) SetAgentDescription(v string) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldAgentDescription, v)
	return u
}

// UpdateAgentDescription sets the "agent_description" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateAgentDescription() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldAgentDescription)
	return u
}

// SetHitAction sets the "hit_action" field.
func (u *AiAgentSecurityLogUpsert) SetHitAction(v int64) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldHitAction, v)
	return u
}

// UpdateHitAction sets the "hit_action" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateHitAction() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldHitAction)
	return u
}

// AddHitAction adds v to the "hit_action" field.
func (u *AiAgentSecurityLogUpsert) AddHitAction(v int64) *AiAgentSecurityLogUpsert {
	u.Add(aiagentsecuritylog.FieldHitAction, v)
	return u
}

// SetQuestion sets the "question" field.
func (u *AiAgentSecurityLogUpsert) SetQuestion(v string) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldQuestion, v)
	return u
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateQuestion() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldQuestion)
	return u
}

// SetActionCategory sets the "action_category" field.
func (u *AiAgentSecurityLogUpsert) SetActionCategory(v int64) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldActionCategory, v)
	return u
}

// UpdateActionCategory sets the "action_category" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateActionCategory() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldActionCategory)
	return u
}

// AddActionCategory adds v to the "action_category" field.
func (u *AiAgentSecurityLogUpsert) AddActionCategory(v int64) *AiAgentSecurityLogUpsert {
	u.Add(aiagentsecuritylog.FieldActionCategory, v)
	return u
}

// SetUploadedFiles sets the "uploaded_files" field.
func (u *AiAgentSecurityLogUpsert) SetUploadedFiles(v *pq.StringArray) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldUploadedFiles, v)
	return u
}

// UpdateUploadedFiles sets the "uploaded_files" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateUploadedFiles() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldUploadedFiles)
	return u
}

// SetHitPolicies sets the "hit_policies" field.
func (u *AiAgentSecurityLogUpsert) SetHitPolicies(v *pq.StringArray) *AiAgentSecurityLogUpsert {
	u.Set(aiagentsecuritylog.FieldHitPolicies, v)
	return u
}

// UpdateHitPolicies sets the "hit_policies" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsert) UpdateHitPolicies() *AiAgentSecurityLogUpsert {
	u.SetExcluded(aiagentsecuritylog.FieldHitPolicies)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AiAgentSecurityLog.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aiagentsecuritylog.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiAgentSecurityLogUpsertOne) UpdateNewValues() *AiAgentSecurityLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(aiagentsecuritylog.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(aiagentsecuritylog.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiAgentSecurityLog.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AiAgentSecurityLogUpsertOne) Ignore() *AiAgentSecurityLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiAgentSecurityLogUpsertOne) DoNothing() *AiAgentSecurityLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiAgentSecurityLogCreate.OnConflict
// documentation for more info.
func (u *AiAgentSecurityLogUpsertOne) Update(set func(*AiAgentSecurityLogUpsert)) *AiAgentSecurityLogUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiAgentSecurityLogUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiAgentSecurityLogUpsertOne) SetUpdatedAt(v time.Time) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateUpdatedAt() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiAgentSecurityLogUpsertOne) SetDeletedAt(v time.Time) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateDeletedAt() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiAgentSecurityLogUpsertOne) ClearDeletedAt() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.ClearDeletedAt()
	})
}

// SetRiskLevel sets the "risk_level" field.
func (u *AiAgentSecurityLogUpsertOne) SetRiskLevel(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetRiskLevel(v)
	})
}

// AddRiskLevel adds v to the "risk_level" field.
func (u *AiAgentSecurityLogUpsertOne) AddRiskLevel(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddRiskLevel(v)
	})
}

// UpdateRiskLevel sets the "risk_level" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateRiskLevel() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateRiskLevel()
	})
}

// SetUserID sets the "user_id" field.
func (u *AiAgentSecurityLogUpsertOne) SetUserID(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *AiAgentSecurityLogUpsertOne) AddUserID(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateUserID() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateUserID()
	})
}

// SetUserName sets the "user_name" field.
func (u *AiAgentSecurityLogUpsertOne) SetUserName(v string) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetUserName(v)
	})
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateUserName() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateUserName()
	})
}

// SetDeptID sets the "dept_id" field.
func (u *AiAgentSecurityLogUpsertOne) SetDeptID(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetDeptID(v)
	})
}

// AddDeptID adds v to the "dept_id" field.
func (u *AiAgentSecurityLogUpsertOne) AddDeptID(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddDeptID(v)
	})
}

// UpdateDeptID sets the "dept_id" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateDeptID() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateDeptID()
	})
}

// SetDeptName sets the "dept_name" field.
func (u *AiAgentSecurityLogUpsertOne) SetDeptName(v string) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetDeptName(v)
	})
}

// UpdateDeptName sets the "dept_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateDeptName() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateDeptName()
	})
}

// SetPcName sets the "pc_name" field.
func (u *AiAgentSecurityLogUpsertOne) SetPcName(v string) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetPcName(v)
	})
}

// UpdatePcName sets the "pc_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdatePcName() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdatePcName()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *AiAgentSecurityLogUpsertOne) SetAgentID(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetAgentID(v)
	})
}

// AddAgentID adds v to the "agent_id" field.
func (u *AiAgentSecurityLogUpsertOne) AddAgentID(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateAgentID() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateAgentID()
	})
}

// SetAgentName sets the "agent_name" field.
func (u *AiAgentSecurityLogUpsertOne) SetAgentName(v string) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetAgentName(v)
	})
}

// UpdateAgentName sets the "agent_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateAgentName() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateAgentName()
	})
}

// SetAgentDescription sets the "agent_description" field.
func (u *AiAgentSecurityLogUpsertOne) SetAgentDescription(v string) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetAgentDescription(v)
	})
}

// UpdateAgentDescription sets the "agent_description" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateAgentDescription() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateAgentDescription()
	})
}

// SetHitAction sets the "hit_action" field.
func (u *AiAgentSecurityLogUpsertOne) SetHitAction(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetHitAction(v)
	})
}

// AddHitAction adds v to the "hit_action" field.
func (u *AiAgentSecurityLogUpsertOne) AddHitAction(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddHitAction(v)
	})
}

// UpdateHitAction sets the "hit_action" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateHitAction() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateHitAction()
	})
}

// SetQuestion sets the "question" field.
func (u *AiAgentSecurityLogUpsertOne) SetQuestion(v string) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetQuestion(v)
	})
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateQuestion() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateQuestion()
	})
}

// SetActionCategory sets the "action_category" field.
func (u *AiAgentSecurityLogUpsertOne) SetActionCategory(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetActionCategory(v)
	})
}

// AddActionCategory adds v to the "action_category" field.
func (u *AiAgentSecurityLogUpsertOne) AddActionCategory(v int64) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddActionCategory(v)
	})
}

// UpdateActionCategory sets the "action_category" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateActionCategory() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateActionCategory()
	})
}

// SetUploadedFiles sets the "uploaded_files" field.
func (u *AiAgentSecurityLogUpsertOne) SetUploadedFiles(v *pq.StringArray) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetUploadedFiles(v)
	})
}

// UpdateUploadedFiles sets the "uploaded_files" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateUploadedFiles() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateUploadedFiles()
	})
}

// SetHitPolicies sets the "hit_policies" field.
func (u *AiAgentSecurityLogUpsertOne) SetHitPolicies(v *pq.StringArray) *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetHitPolicies(v)
	})
}

// UpdateHitPolicies sets the "hit_policies" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertOne) UpdateHitPolicies() *AiAgentSecurityLogUpsertOne {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateHitPolicies()
	})
}

// Exec executes the query.
func (u *AiAgentSecurityLogUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiAgentSecurityLogCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiAgentSecurityLogUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AiAgentSecurityLogUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AiAgentSecurityLogUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AiAgentSecurityLogCreateBulk is the builder for creating many AiAgentSecurityLog entities in bulk.
type AiAgentSecurityLogCreateBulk struct {
	config
	err      error
	builders []*AiAgentSecurityLogCreate
	conflict []sql.ConflictOption
}

// Save creates the AiAgentSecurityLog entities in the database.
func (aaslcb *AiAgentSecurityLogCreateBulk) Save(ctx context.Context) ([]*AiAgentSecurityLog, error) {
	if aaslcb.err != nil {
		return nil, aaslcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(aaslcb.builders))
	nodes := make([]*AiAgentSecurityLog, len(aaslcb.builders))
	mutators := make([]Mutator, len(aaslcb.builders))
	for i := range aaslcb.builders {
		func(i int, root context.Context) {
			builder := aaslcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AiAgentSecurityLogMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, aaslcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = aaslcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, aaslcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, aaslcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (aaslcb *AiAgentSecurityLogCreateBulk) SaveX(ctx context.Context) []*AiAgentSecurityLog {
	v, err := aaslcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aaslcb *AiAgentSecurityLogCreateBulk) Exec(ctx context.Context) error {
	_, err := aaslcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aaslcb *AiAgentSecurityLogCreateBulk) ExecX(ctx context.Context) {
	if err := aaslcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiAgentSecurityLog.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiAgentSecurityLogUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (aaslcb *AiAgentSecurityLogCreateBulk) OnConflict(opts ...sql.ConflictOption) *AiAgentSecurityLogUpsertBulk {
	aaslcb.conflict = opts
	return &AiAgentSecurityLogUpsertBulk{
		create: aaslcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiAgentSecurityLog.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aaslcb *AiAgentSecurityLogCreateBulk) OnConflictColumns(columns ...string) *AiAgentSecurityLogUpsertBulk {
	aaslcb.conflict = append(aaslcb.conflict, sql.ConflictColumns(columns...))
	return &AiAgentSecurityLogUpsertBulk{
		create: aaslcb,
	}
}

// AiAgentSecurityLogUpsertBulk is the builder for "upsert"-ing
// a bulk of AiAgentSecurityLog nodes.
type AiAgentSecurityLogUpsertBulk struct {
	create *AiAgentSecurityLogCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AiAgentSecurityLog.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aiagentsecuritylog.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiAgentSecurityLogUpsertBulk) UpdateNewValues() *AiAgentSecurityLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(aiagentsecuritylog.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(aiagentsecuritylog.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiAgentSecurityLog.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AiAgentSecurityLogUpsertBulk) Ignore() *AiAgentSecurityLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiAgentSecurityLogUpsertBulk) DoNothing() *AiAgentSecurityLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiAgentSecurityLogCreateBulk.OnConflict
// documentation for more info.
func (u *AiAgentSecurityLogUpsertBulk) Update(set func(*AiAgentSecurityLogUpsert)) *AiAgentSecurityLogUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiAgentSecurityLogUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiAgentSecurityLogUpsertBulk) SetUpdatedAt(v time.Time) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateUpdatedAt() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiAgentSecurityLogUpsertBulk) SetDeletedAt(v time.Time) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateDeletedAt() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiAgentSecurityLogUpsertBulk) ClearDeletedAt() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.ClearDeletedAt()
	})
}

// SetRiskLevel sets the "risk_level" field.
func (u *AiAgentSecurityLogUpsertBulk) SetRiskLevel(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetRiskLevel(v)
	})
}

// AddRiskLevel adds v to the "risk_level" field.
func (u *AiAgentSecurityLogUpsertBulk) AddRiskLevel(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddRiskLevel(v)
	})
}

// UpdateRiskLevel sets the "risk_level" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateRiskLevel() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateRiskLevel()
	})
}

// SetUserID sets the "user_id" field.
func (u *AiAgentSecurityLogUpsertBulk) SetUserID(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *AiAgentSecurityLogUpsertBulk) AddUserID(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateUserID() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateUserID()
	})
}

// SetUserName sets the "user_name" field.
func (u *AiAgentSecurityLogUpsertBulk) SetUserName(v string) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetUserName(v)
	})
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateUserName() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateUserName()
	})
}

// SetDeptID sets the "dept_id" field.
func (u *AiAgentSecurityLogUpsertBulk) SetDeptID(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetDeptID(v)
	})
}

// AddDeptID adds v to the "dept_id" field.
func (u *AiAgentSecurityLogUpsertBulk) AddDeptID(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddDeptID(v)
	})
}

// UpdateDeptID sets the "dept_id" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateDeptID() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateDeptID()
	})
}

// SetDeptName sets the "dept_name" field.
func (u *AiAgentSecurityLogUpsertBulk) SetDeptName(v string) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetDeptName(v)
	})
}

// UpdateDeptName sets the "dept_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateDeptName() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateDeptName()
	})
}

// SetPcName sets the "pc_name" field.
func (u *AiAgentSecurityLogUpsertBulk) SetPcName(v string) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetPcName(v)
	})
}

// UpdatePcName sets the "pc_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdatePcName() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdatePcName()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *AiAgentSecurityLogUpsertBulk) SetAgentID(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetAgentID(v)
	})
}

// AddAgentID adds v to the "agent_id" field.
func (u *AiAgentSecurityLogUpsertBulk) AddAgentID(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateAgentID() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateAgentID()
	})
}

// SetAgentName sets the "agent_name" field.
func (u *AiAgentSecurityLogUpsertBulk) SetAgentName(v string) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetAgentName(v)
	})
}

// UpdateAgentName sets the "agent_name" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateAgentName() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateAgentName()
	})
}

// SetAgentDescription sets the "agent_description" field.
func (u *AiAgentSecurityLogUpsertBulk) SetAgentDescription(v string) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetAgentDescription(v)
	})
}

// UpdateAgentDescription sets the "agent_description" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateAgentDescription() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateAgentDescription()
	})
}

// SetHitAction sets the "hit_action" field.
func (u *AiAgentSecurityLogUpsertBulk) SetHitAction(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetHitAction(v)
	})
}

// AddHitAction adds v to the "hit_action" field.
func (u *AiAgentSecurityLogUpsertBulk) AddHitAction(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddHitAction(v)
	})
}

// UpdateHitAction sets the "hit_action" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateHitAction() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateHitAction()
	})
}

// SetQuestion sets the "question" field.
func (u *AiAgentSecurityLogUpsertBulk) SetQuestion(v string) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetQuestion(v)
	})
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateQuestion() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateQuestion()
	})
}

// SetActionCategory sets the "action_category" field.
func (u *AiAgentSecurityLogUpsertBulk) SetActionCategory(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetActionCategory(v)
	})
}

// AddActionCategory adds v to the "action_category" field.
func (u *AiAgentSecurityLogUpsertBulk) AddActionCategory(v int64) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.AddActionCategory(v)
	})
}

// UpdateActionCategory sets the "action_category" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateActionCategory() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateActionCategory()
	})
}

// SetUploadedFiles sets the "uploaded_files" field.
func (u *AiAgentSecurityLogUpsertBulk) SetUploadedFiles(v *pq.StringArray) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetUploadedFiles(v)
	})
}

// UpdateUploadedFiles sets the "uploaded_files" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateUploadedFiles() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateUploadedFiles()
	})
}

// SetHitPolicies sets the "hit_policies" field.
func (u *AiAgentSecurityLogUpsertBulk) SetHitPolicies(v *pq.StringArray) *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.SetHitPolicies(v)
	})
}

// UpdateHitPolicies sets the "hit_policies" field to the value that was provided on create.
func (u *AiAgentSecurityLogUpsertBulk) UpdateHitPolicies() *AiAgentSecurityLogUpsertBulk {
	return u.Update(func(s *AiAgentSecurityLogUpsert) {
		s.UpdateHitPolicies()
	})
}

// Exec executes the query.
func (u *AiAgentSecurityLogUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AiAgentSecurityLogCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiAgentSecurityLogCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiAgentSecurityLogUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

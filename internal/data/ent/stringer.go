// Code generated by ent, DO NOT EDIT.

package ent

import "fmt"

func (aa *AiAgent) GoString() string {
	if aa == nil {
		return fmt.Sprintf("AiAgent(nil)")
	}
	return aa.String()
}

func (aasl *AiAgentSecurityLog) GoString() string {
	if aasl == nil {
		return fmt.Sprintf("AiAgentSecurityLog(nil)")
	}
	return aasl.String()
}

func (aasp *AiAgentSecurityPolicy) GoString() string {
	if aasp == nil {
		return fmt.Sprintf("AiAgentSecurityPolicy(nil)")
	}
	return aasp.String()
}

func (ac *AiChat) GoString() string {
	if ac == nil {
		return fmt.Sprintf("AiChat(nil)")
	}
	return ac.String()
}

func (aci *AiChatItem) GoString() string {
	if aci == nil {
		return fmt.Sprintf("AiChatItem(nil)")
	}
	return aci.String()
}

func (am *AiModel) GoString() string {
	if am == nil {
		return fmt.Sprintf("AiModel(nil)")
	}
	return am.String()
}

func (amd *AiModelDetail) GoString() string {
	if amd == nil {
		return fmt.Sprintf("AiModelDetail(nil)")
	}
	return amd.String()
}

func (amu *AiModelUsage) GoString() string {
	if amu == nil {
		return fmt.Sprintf("AiModelUsage(nil)")
	}
	return amu.String()
}

func (aq *AtomicQuestions) GoString() string {
	if aq == nil {
		return fmt.Sprintf("AtomicQuestions(nil)")
	}
	return aq.String()
}

func (cf *ClassificationFiles) GoString() string {
	if cf == nil {
		return fmt.Sprintf("ClassificationFiles(nil)")
	}
	return cf.String()
}

func (daa *DefaultAgentAvatar) GoString() string {
	if daa == nil {
		return fmt.Sprintf("DefaultAgentAvatar(nil)")
	}
	return daa.String()
}

func (emu *ExternalModelUsage) GoString() string {
	if emu == nil {
		return fmt.Sprintf("ExternalModelUsage(nil)")
	}
	return emu.String()
}

func (kb *KnowledgeBase) GoString() string {
	if kb == nil {
		return fmt.Sprintf("KnowledgeBase(nil)")
	}
	return kb.String()
}

func (kbf *KnowledgeBaseFile) GoString() string {
	if kbf == nil {
		return fmt.Sprintf("KnowledgeBaseFile(nil)")
	}
	return kbf.String()
}

func (uao *UserAgentOrder) GoString() string {
	if uao == nil {
		return fmt.Sprintf("UserAgentOrder(nil)")
	}
	return uao.String()
}

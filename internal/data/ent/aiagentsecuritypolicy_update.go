// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritypolicy"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiAgentSecurityPolicyUpdate is the builder for updating AiAgentSecurityPolicy entities.
type AiAgentSecurityPolicyUpdate struct {
	config
	hooks     []Hook
	mutation  *AiAgentSecurityPolicyMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AiAgentSecurityPolicyUpdate builder.
func (aaspu *AiAgentSecurityPolicyUpdate) Where(ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.Where(ps...)
	return aaspu
}

// SetUpdatedAt sets the "updated_at" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetUpdatedAt(t time.Time) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.SetUpdatedAt(t)
	return aaspu
}

// SetDeletedAt sets the "deleted_at" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetDeletedAt(t time.Time) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.SetDeletedAt(t)
	return aaspu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aaspu *AiAgentSecurityPolicyUpdate) SetNillableDeletedAt(t *time.Time) *AiAgentSecurityPolicyUpdate {
	if t != nil {
		aaspu.SetDeletedAt(*t)
	}
	return aaspu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (aaspu *AiAgentSecurityPolicyUpdate) ClearDeletedAt() *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.ClearDeletedAt()
	return aaspu
}

// SetName sets the "name" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetName(s string) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.SetName(s)
	return aaspu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (aaspu *AiAgentSecurityPolicyUpdate) SetNillableName(s *string) *AiAgentSecurityPolicyUpdate {
	if s != nil {
		aaspu.SetName(*s)
	}
	return aaspu
}

// SetAgentID sets the "agent_id" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetAgentID(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.ResetAgentID()
	aaspu.mutation.SetAgentID(i)
	return aaspu
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (aaspu *AiAgentSecurityPolicyUpdate) SetNillableAgentID(i *int64) *AiAgentSecurityPolicyUpdate {
	if i != nil {
		aaspu.SetAgentID(*i)
	}
	return aaspu
}

// AddAgentID adds i to the "agent_id" field.
func (aaspu *AiAgentSecurityPolicyUpdate) AddAgentID(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.AddAgentID(i)
	return aaspu
}

// SetPolicyCategory sets the "policy_category" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetPolicyCategory(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.ResetPolicyCategory()
	aaspu.mutation.SetPolicyCategory(i)
	return aaspu
}

// SetNillablePolicyCategory sets the "policy_category" field if the given value is not nil.
func (aaspu *AiAgentSecurityPolicyUpdate) SetNillablePolicyCategory(i *int64) *AiAgentSecurityPolicyUpdate {
	if i != nil {
		aaspu.SetPolicyCategory(*i)
	}
	return aaspu
}

// AddPolicyCategory adds i to the "policy_category" field.
func (aaspu *AiAgentSecurityPolicyUpdate) AddPolicyCategory(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.AddPolicyCategory(i)
	return aaspu
}

// SetRiskLevel sets the "risk_level" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetRiskLevel(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.ResetRiskLevel()
	aaspu.mutation.SetRiskLevel(i)
	return aaspu
}

// SetNillableRiskLevel sets the "risk_level" field if the given value is not nil.
func (aaspu *AiAgentSecurityPolicyUpdate) SetNillableRiskLevel(i *int64) *AiAgentSecurityPolicyUpdate {
	if i != nil {
		aaspu.SetRiskLevel(*i)
	}
	return aaspu
}

// AddRiskLevel adds i to the "risk_level" field.
func (aaspu *AiAgentSecurityPolicyUpdate) AddRiskLevel(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.AddRiskLevel(i)
	return aaspu
}

// SetEnabled sets the "enabled" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetEnabled(b bool) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.SetEnabled(b)
	return aaspu
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (aaspu *AiAgentSecurityPolicyUpdate) SetNillableEnabled(b *bool) *AiAgentSecurityPolicyUpdate {
	if b != nil {
		aaspu.SetEnabled(*b)
	}
	return aaspu
}

// SetPolicies sets the "policies" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetPolicies(pa *pq.StringArray) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.SetPolicies(pa)
	return aaspu
}

// SetHitAction sets the "hit_action" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetHitAction(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.ResetHitAction()
	aaspu.mutation.SetHitAction(i)
	return aaspu
}

// SetNillableHitAction sets the "hit_action" field if the given value is not nil.
func (aaspu *AiAgentSecurityPolicyUpdate) SetNillableHitAction(i *int64) *AiAgentSecurityPolicyUpdate {
	if i != nil {
		aaspu.SetHitAction(*i)
	}
	return aaspu
}

// AddHitAction adds i to the "hit_action" field.
func (aaspu *AiAgentSecurityPolicyUpdate) AddHitAction(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.AddHitAction(i)
	return aaspu
}

// SetHitResponse sets the "hit_response" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetHitResponse(s string) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.SetHitResponse(s)
	return aaspu
}

// SetNillableHitResponse sets the "hit_response" field if the given value is not nil.
func (aaspu *AiAgentSecurityPolicyUpdate) SetNillableHitResponse(s *string) *AiAgentSecurityPolicyUpdate {
	if s != nil {
		aaspu.SetHitResponse(*s)
	}
	return aaspu
}

// SetUpdatedBy sets the "updated_by" field.
func (aaspu *AiAgentSecurityPolicyUpdate) SetUpdatedBy(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.ResetUpdatedBy()
	aaspu.mutation.SetUpdatedBy(i)
	return aaspu
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (aaspu *AiAgentSecurityPolicyUpdate) SetNillableUpdatedBy(i *int64) *AiAgentSecurityPolicyUpdate {
	if i != nil {
		aaspu.SetUpdatedBy(*i)
	}
	return aaspu
}

// AddUpdatedBy adds i to the "updated_by" field.
func (aaspu *AiAgentSecurityPolicyUpdate) AddUpdatedBy(i int64) *AiAgentSecurityPolicyUpdate {
	aaspu.mutation.AddUpdatedBy(i)
	return aaspu
}

// Mutation returns the AiAgentSecurityPolicyMutation object of the builder.
func (aaspu *AiAgentSecurityPolicyUpdate) Mutation() *AiAgentSecurityPolicyMutation {
	return aaspu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (aaspu *AiAgentSecurityPolicyUpdate) Save(ctx context.Context) (int, error) {
	if err := aaspu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, aaspu.sqlSave, aaspu.mutation, aaspu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aaspu *AiAgentSecurityPolicyUpdate) SaveX(ctx context.Context) int {
	affected, err := aaspu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (aaspu *AiAgentSecurityPolicyUpdate) Exec(ctx context.Context) error {
	_, err := aaspu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aaspu *AiAgentSecurityPolicyUpdate) ExecX(ctx context.Context) {
	if err := aaspu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aaspu *AiAgentSecurityPolicyUpdate) defaults() error {
	if _, ok := aaspu.mutation.UpdatedAt(); !ok {
		if aiagentsecuritypolicy.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagentsecuritypolicy.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aiagentsecuritypolicy.UpdateDefaultUpdatedAt()
		aaspu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aaspu *AiAgentSecurityPolicyUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiAgentSecurityPolicyUpdate {
	aaspu.modifiers = append(aaspu.modifiers, modifiers...)
	return aaspu
}

func (aaspu *AiAgentSecurityPolicyUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(aiagentsecuritypolicy.Table, aiagentsecuritypolicy.Columns, sqlgraph.NewFieldSpec(aiagentsecuritypolicy.FieldID, field.TypeInt64))
	if ps := aaspu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aaspu.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aaspu.mutation.DeletedAt(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldDeletedAt, field.TypeTime, value)
	}
	if aaspu.mutation.DeletedAtCleared() {
		_spec.ClearField(aiagentsecuritypolicy.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := aaspu.mutation.Name(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldName, field.TypeString, value)
	}
	if value, ok := aaspu.mutation.AgentID(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := aaspu.mutation.AddedAgentID(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := aaspu.mutation.PolicyCategory(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldPolicyCategory, field.TypeInt64, value)
	}
	if value, ok := aaspu.mutation.AddedPolicyCategory(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldPolicyCategory, field.TypeInt64, value)
	}
	if value, ok := aaspu.mutation.RiskLevel(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldRiskLevel, field.TypeInt64, value)
	}
	if value, ok := aaspu.mutation.AddedRiskLevel(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldRiskLevel, field.TypeInt64, value)
	}
	if value, ok := aaspu.mutation.Enabled(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := aaspu.mutation.Policies(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldPolicies, field.TypeOther, value)
	}
	if value, ok := aaspu.mutation.HitAction(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aaspu.mutation.AddedHitAction(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aaspu.mutation.HitResponse(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldHitResponse, field.TypeString, value)
	}
	if value, ok := aaspu.mutation.UpdatedBy(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldUpdatedBy, field.TypeInt64, value)
	}
	if value, ok := aaspu.mutation.AddedUpdatedBy(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldUpdatedBy, field.TypeInt64, value)
	}
	_spec.AddModifiers(aaspu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, aaspu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aiagentsecuritypolicy.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	aaspu.mutation.done = true
	return n, nil
}

// AiAgentSecurityPolicyUpdateOne is the builder for updating a single AiAgentSecurityPolicy entity.
type AiAgentSecurityPolicyUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AiAgentSecurityPolicyMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetUpdatedAt(t time.Time) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.SetUpdatedAt(t)
	return aaspuo
}

// SetDeletedAt sets the "deleted_at" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetDeletedAt(t time.Time) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.SetDeletedAt(t)
	return aaspuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetNillableDeletedAt(t *time.Time) *AiAgentSecurityPolicyUpdateOne {
	if t != nil {
		aaspuo.SetDeletedAt(*t)
	}
	return aaspuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) ClearDeletedAt() *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.ClearDeletedAt()
	return aaspuo
}

// SetName sets the "name" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetName(s string) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.SetName(s)
	return aaspuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetNillableName(s *string) *AiAgentSecurityPolicyUpdateOne {
	if s != nil {
		aaspuo.SetName(*s)
	}
	return aaspuo
}

// SetAgentID sets the "agent_id" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetAgentID(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.ResetAgentID()
	aaspuo.mutation.SetAgentID(i)
	return aaspuo
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetNillableAgentID(i *int64) *AiAgentSecurityPolicyUpdateOne {
	if i != nil {
		aaspuo.SetAgentID(*i)
	}
	return aaspuo
}

// AddAgentID adds i to the "agent_id" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) AddAgentID(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.AddAgentID(i)
	return aaspuo
}

// SetPolicyCategory sets the "policy_category" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetPolicyCategory(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.ResetPolicyCategory()
	aaspuo.mutation.SetPolicyCategory(i)
	return aaspuo
}

// SetNillablePolicyCategory sets the "policy_category" field if the given value is not nil.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetNillablePolicyCategory(i *int64) *AiAgentSecurityPolicyUpdateOne {
	if i != nil {
		aaspuo.SetPolicyCategory(*i)
	}
	return aaspuo
}

// AddPolicyCategory adds i to the "policy_category" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) AddPolicyCategory(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.AddPolicyCategory(i)
	return aaspuo
}

// SetRiskLevel sets the "risk_level" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetRiskLevel(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.ResetRiskLevel()
	aaspuo.mutation.SetRiskLevel(i)
	return aaspuo
}

// SetNillableRiskLevel sets the "risk_level" field if the given value is not nil.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetNillableRiskLevel(i *int64) *AiAgentSecurityPolicyUpdateOne {
	if i != nil {
		aaspuo.SetRiskLevel(*i)
	}
	return aaspuo
}

// AddRiskLevel adds i to the "risk_level" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) AddRiskLevel(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.AddRiskLevel(i)
	return aaspuo
}

// SetEnabled sets the "enabled" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetEnabled(b bool) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.SetEnabled(b)
	return aaspuo
}

// SetNillableEnabled sets the "enabled" field if the given value is not nil.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetNillableEnabled(b *bool) *AiAgentSecurityPolicyUpdateOne {
	if b != nil {
		aaspuo.SetEnabled(*b)
	}
	return aaspuo
}

// SetPolicies sets the "policies" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetPolicies(pa *pq.StringArray) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.SetPolicies(pa)
	return aaspuo
}

// SetHitAction sets the "hit_action" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetHitAction(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.ResetHitAction()
	aaspuo.mutation.SetHitAction(i)
	return aaspuo
}

// SetNillableHitAction sets the "hit_action" field if the given value is not nil.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetNillableHitAction(i *int64) *AiAgentSecurityPolicyUpdateOne {
	if i != nil {
		aaspuo.SetHitAction(*i)
	}
	return aaspuo
}

// AddHitAction adds i to the "hit_action" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) AddHitAction(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.AddHitAction(i)
	return aaspuo
}

// SetHitResponse sets the "hit_response" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetHitResponse(s string) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.SetHitResponse(s)
	return aaspuo
}

// SetNillableHitResponse sets the "hit_response" field if the given value is not nil.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetNillableHitResponse(s *string) *AiAgentSecurityPolicyUpdateOne {
	if s != nil {
		aaspuo.SetHitResponse(*s)
	}
	return aaspuo
}

// SetUpdatedBy sets the "updated_by" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetUpdatedBy(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.ResetUpdatedBy()
	aaspuo.mutation.SetUpdatedBy(i)
	return aaspuo
}

// SetNillableUpdatedBy sets the "updated_by" field if the given value is not nil.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SetNillableUpdatedBy(i *int64) *AiAgentSecurityPolicyUpdateOne {
	if i != nil {
		aaspuo.SetUpdatedBy(*i)
	}
	return aaspuo
}

// AddUpdatedBy adds i to the "updated_by" field.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) AddUpdatedBy(i int64) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.AddUpdatedBy(i)
	return aaspuo
}

// Mutation returns the AiAgentSecurityPolicyMutation object of the builder.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) Mutation() *AiAgentSecurityPolicyMutation {
	return aaspuo.mutation
}

// Where appends a list predicates to the AiAgentSecurityPolicyUpdate builder.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) Where(ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.mutation.Where(ps...)
	return aaspuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) Select(field string, fields ...string) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.fields = append([]string{field}, fields...)
	return aaspuo
}

// Save executes the query and returns the updated AiAgentSecurityPolicy entity.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) Save(ctx context.Context) (*AiAgentSecurityPolicy, error) {
	if err := aaspuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, aaspuo.sqlSave, aaspuo.mutation, aaspuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) SaveX(ctx context.Context) *AiAgentSecurityPolicy {
	node, err := aaspuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) Exec(ctx context.Context) error {
	_, err := aaspuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) ExecX(ctx context.Context) {
	if err := aaspuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) defaults() error {
	if _, ok := aaspuo.mutation.UpdatedAt(); !ok {
		if aiagentsecuritypolicy.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagentsecuritypolicy.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aiagentsecuritypolicy.UpdateDefaultUpdatedAt()
		aaspuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aaspuo *AiAgentSecurityPolicyUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiAgentSecurityPolicyUpdateOne {
	aaspuo.modifiers = append(aaspuo.modifiers, modifiers...)
	return aaspuo
}

func (aaspuo *AiAgentSecurityPolicyUpdateOne) sqlSave(ctx context.Context) (_node *AiAgentSecurityPolicy, err error) {
	_spec := sqlgraph.NewUpdateSpec(aiagentsecuritypolicy.Table, aiagentsecuritypolicy.Columns, sqlgraph.NewFieldSpec(aiagentsecuritypolicy.FieldID, field.TypeInt64))
	id, ok := aaspuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AiAgentSecurityPolicy.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := aaspuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aiagentsecuritypolicy.FieldID)
		for _, f := range fields {
			if !aiagentsecuritypolicy.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != aiagentsecuritypolicy.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := aaspuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aaspuo.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aaspuo.mutation.DeletedAt(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldDeletedAt, field.TypeTime, value)
	}
	if aaspuo.mutation.DeletedAtCleared() {
		_spec.ClearField(aiagentsecuritypolicy.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := aaspuo.mutation.Name(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldName, field.TypeString, value)
	}
	if value, ok := aaspuo.mutation.AgentID(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := aaspuo.mutation.AddedAgentID(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := aaspuo.mutation.PolicyCategory(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldPolicyCategory, field.TypeInt64, value)
	}
	if value, ok := aaspuo.mutation.AddedPolicyCategory(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldPolicyCategory, field.TypeInt64, value)
	}
	if value, ok := aaspuo.mutation.RiskLevel(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldRiskLevel, field.TypeInt64, value)
	}
	if value, ok := aaspuo.mutation.AddedRiskLevel(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldRiskLevel, field.TypeInt64, value)
	}
	if value, ok := aaspuo.mutation.Enabled(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldEnabled, field.TypeBool, value)
	}
	if value, ok := aaspuo.mutation.Policies(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldPolicies, field.TypeOther, value)
	}
	if value, ok := aaspuo.mutation.HitAction(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aaspuo.mutation.AddedHitAction(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aaspuo.mutation.HitResponse(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldHitResponse, field.TypeString, value)
	}
	if value, ok := aaspuo.mutation.UpdatedBy(); ok {
		_spec.SetField(aiagentsecuritypolicy.FieldUpdatedBy, field.TypeInt64, value)
	}
	if value, ok := aaspuo.mutation.AddedUpdatedBy(); ok {
		_spec.AddField(aiagentsecuritypolicy.FieldUpdatedBy, field.TypeInt64, value)
	}
	_spec.AddModifiers(aaspuo.modifiers...)
	_node = &AiAgentSecurityPolicy{config: aaspuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, aaspuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aiagentsecuritypolicy.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	aaspuo.mutation.done = true
	return _node, nil
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
)

// AiAgentCreate is the builder for creating a AiAgent entity.
type AiAgentCreate struct {
	config
	mutation *AiAgentMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (aac *AiAgentCreate) SetCreatedAt(t time.Time) *AiAgentCreate {
	aac.mutation.SetCreatedAt(t)
	return aac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableCreatedAt(t *time.Time) *AiAgentCreate {
	if t != nil {
		aac.SetCreatedAt(*t)
	}
	return aac
}

// SetUpdatedAt sets the "updated_at" field.
func (aac *AiAgentCreate) SetUpdatedAt(t time.Time) *AiAgentCreate {
	aac.mutation.SetUpdatedAt(t)
	return aac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableUpdatedAt(t *time.Time) *AiAgentCreate {
	if t != nil {
		aac.SetUpdatedAt(*t)
	}
	return aac
}

// SetDeletedAt sets the "deleted_at" field.
func (aac *AiAgentCreate) SetDeletedAt(t time.Time) *AiAgentCreate {
	aac.mutation.SetDeletedAt(t)
	return aac
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableDeletedAt(t *time.Time) *AiAgentCreate {
	if t != nil {
		aac.SetDeletedAt(*t)
	}
	return aac
}

// SetName sets the "name" field.
func (aac *AiAgentCreate) SetName(s string) *AiAgentCreate {
	aac.mutation.SetName(s)
	return aac
}

// SetDescription sets the "description" field.
func (aac *AiAgentCreate) SetDescription(s string) *AiAgentCreate {
	aac.mutation.SetDescription(s)
	return aac
}

// SetAvatar sets the "avatar" field.
func (aac *AiAgentCreate) SetAvatar(s string) *AiAgentCreate {
	aac.mutation.SetAvatar(s)
	return aac
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (aac *AiAgentCreate) SetClickedAvatar(s string) *AiAgentCreate {
	aac.mutation.SetClickedAvatar(s)
	return aac
}

// SetWelcomeMsg sets the "welcome_msg" field.
func (aac *AiAgentCreate) SetWelcomeMsg(s string) *AiAgentCreate {
	aac.mutation.SetWelcomeMsg(s)
	return aac
}

// SetFallbackMsg sets the "fallback_msg" field.
func (aac *AiAgentCreate) SetFallbackMsg(s string) *AiAgentCreate {
	aac.mutation.SetFallbackMsg(s)
	return aac
}

// SetOwnerID sets the "owner_id" field.
func (aac *AiAgentCreate) SetOwnerID(i int64) *AiAgentCreate {
	aac.mutation.SetOwnerID(i)
	return aac
}

// SetVisibilityType sets the "visibility_type" field.
func (aac *AiAgentCreate) SetVisibilityType(i int8) *AiAgentCreate {
	aac.mutation.SetVisibilityType(i)
	return aac
}

// SetManageableToUser sets the "manageable_to_user" field.
func (aac *AiAgentCreate) SetManageableToUser(pq *pq.Int64Array) *AiAgentCreate {
	aac.mutation.SetManageableToUser(pq)
	return aac
}

// SetVisibleToUser sets the "visible_to_user" field.
func (aac *AiAgentCreate) SetVisibleToUser(pq *pq.Int64Array) *AiAgentCreate {
	aac.mutation.SetVisibleToUser(pq)
	return aac
}

// SetVisibleToDept sets the "visible_to_dept" field.
func (aac *AiAgentCreate) SetVisibleToDept(pq *pq.Int64Array) *AiAgentCreate {
	aac.mutation.SetVisibleToDept(pq)
	return aac
}

// SetKnowledgeBaseIds sets the "knowledge_base_ids" field.
func (aac *AiAgentCreate) SetKnowledgeBaseIds(pq *pq.Int64Array) *AiAgentCreate {
	aac.mutation.SetKnowledgeBaseIds(pq)
	return aac
}

// SetSchema sets the "schema" field.
func (aac *AiAgentCreate) SetSchema(s string) *AiAgentCreate {
	aac.mutation.SetSchema(s)
	return aac
}

// SetIsPublic sets the "is_public" field.
func (aac *AiAgentCreate) SetIsPublic(b bool) *AiAgentCreate {
	aac.mutation.SetIsPublic(b)
	return aac
}

// SetIsEnabled sets the "is_enabled" field.
func (aac *AiAgentCreate) SetIsEnabled(b bool) *AiAgentCreate {
	aac.mutation.SetIsEnabled(b)
	return aac
}

// SetIsRefFiles sets the "is_ref_files" field.
func (aac *AiAgentCreate) SetIsRefFiles(b bool) *AiAgentCreate {
	aac.mutation.SetIsRefFiles(b)
	return aac
}

// SetModelType sets the "model_type" field.
func (aac *AiAgentCreate) SetModelType(i int64) *AiAgentCreate {
	aac.mutation.SetModelType(i)
	return aac
}

// SetNillableModelType sets the "model_type" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableModelType(i *int64) *AiAgentCreate {
	if i != nil {
		aac.SetModelType(*i)
	}
	return aac
}

// SetModelID sets the "model_id" field.
func (aac *AiAgentCreate) SetModelID(i int64) *AiAgentCreate {
	aac.mutation.SetModelID(i)
	return aac
}

// SetNillableModelID sets the "model_id" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableModelID(i *int64) *AiAgentCreate {
	if i != nil {
		aac.SetModelID(*i)
	}
	return aac
}

// SetUseCount sets the "use_count" field.
func (aac *AiAgentCreate) SetUseCount(i int64) *AiAgentCreate {
	aac.mutation.SetUseCount(i)
	return aac
}

// SetNillableUseCount sets the "use_count" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableUseCount(i *int64) *AiAgentCreate {
	if i != nil {
		aac.SetUseCount(*i)
	}
	return aac
}

// SetKnowledgeBaseType sets the "knowledge_base_type" field.
func (aac *AiAgentCreate) SetKnowledgeBaseType(i int64) *AiAgentCreate {
	aac.mutation.SetKnowledgeBaseType(i)
	return aac
}

// SetNillableKnowledgeBaseType sets the "knowledge_base_type" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableKnowledgeBaseType(i *int64) *AiAgentCreate {
	if i != nil {
		aac.SetKnowledgeBaseType(*i)
	}
	return aac
}

// SetInternetSearch sets the "internet_search" field.
func (aac *AiAgentCreate) SetInternetSearch(b bool) *AiAgentCreate {
	aac.mutation.SetInternetSearch(b)
	return aac
}

// SetNillableInternetSearch sets the "internet_search" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableInternetSearch(b *bool) *AiAgentCreate {
	if b != nil {
		aac.SetInternetSearch(*b)
	}
	return aac
}

// SetAgentType sets the "agent_type" field.
func (aac *AiAgentCreate) SetAgentType(i int64) *AiAgentCreate {
	aac.mutation.SetAgentType(i)
	return aac
}

// SetNillableAgentType sets the "agent_type" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableAgentType(i *int64) *AiAgentCreate {
	if i != nil {
		aac.SetAgentType(*i)
	}
	return aac
}

// SetThinking sets the "thinking" field.
func (aac *AiAgentCreate) SetThinking(b bool) *AiAgentCreate {
	aac.mutation.SetThinking(b)
	return aac
}

// SetNillableThinking sets the "thinking" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableThinking(b *bool) *AiAgentCreate {
	if b != nil {
		aac.SetThinking(*b)
	}
	return aac
}

// SetThinkingModelID sets the "thinking_model_id" field.
func (aac *AiAgentCreate) SetThinkingModelID(i int64) *AiAgentCreate {
	aac.mutation.SetThinkingModelID(i)
	return aac
}

// SetNillableThinkingModelID sets the "thinking_model_id" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableThinkingModelID(i *int64) *AiAgentCreate {
	if i != nil {
		aac.SetThinkingModelID(*i)
	}
	return aac
}

// SetRoleSetting sets the "role_setting" field.
func (aac *AiAgentCreate) SetRoleSetting(s string) *AiAgentCreate {
	aac.mutation.SetRoleSetting(s)
	return aac
}

// SetUploadFile sets the "upload_file" field.
func (aac *AiAgentCreate) SetUploadFile(b bool) *AiAgentCreate {
	aac.mutation.SetUploadFile(b)
	return aac
}

// SetNillableUploadFile sets the "upload_file" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableUploadFile(b *bool) *AiAgentCreate {
	if b != nil {
		aac.SetUploadFile(*b)
	}
	return aac
}

// SetSemanticCache sets the "semantic_cache" field.
func (aac *AiAgentCreate) SetSemanticCache(b bool) *AiAgentCreate {
	aac.mutation.SetSemanticCache(b)
	return aac
}

// SetNillableSemanticCache sets the "semantic_cache" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableSemanticCache(b *bool) *AiAgentCreate {
	if b != nil {
		aac.SetSemanticCache(*b)
	}
	return aac
}

// SetID sets the "id" field.
func (aac *AiAgentCreate) SetID(i int64) *AiAgentCreate {
	aac.mutation.SetID(i)
	return aac
}

// AddAiChatIDs adds the "ai_chat" edge to the AiChat entity by IDs.
func (aac *AiAgentCreate) AddAiChatIDs(ids ...int64) *AiAgentCreate {
	aac.mutation.AddAiChatIDs(ids...)
	return aac
}

// AddAiChat adds the "ai_chat" edges to the AiChat entity.
func (aac *AiAgentCreate) AddAiChat(a ...*AiChat) *AiAgentCreate {
	ids := make([]int64, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return aac.AddAiChatIDs(ids...)
}

// Mutation returns the AiAgentMutation object of the builder.
func (aac *AiAgentCreate) Mutation() *AiAgentMutation {
	return aac.mutation
}

// Save creates the AiAgent in the database.
func (aac *AiAgentCreate) Save(ctx context.Context) (*AiAgent, error) {
	if err := aac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, aac.sqlSave, aac.mutation, aac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (aac *AiAgentCreate) SaveX(ctx context.Context) *AiAgent {
	v, err := aac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aac *AiAgentCreate) Exec(ctx context.Context) error {
	_, err := aac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aac *AiAgentCreate) ExecX(ctx context.Context) {
	if err := aac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aac *AiAgentCreate) defaults() error {
	if _, ok := aac.mutation.CreatedAt(); !ok {
		if aiagent.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagent.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := aiagent.DefaultCreatedAt()
		aac.mutation.SetCreatedAt(v)
	}
	if _, ok := aac.mutation.UpdatedAt(); !ok {
		if aiagent.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagent.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aiagent.DefaultUpdatedAt()
		aac.mutation.SetUpdatedAt(v)
	}
	if _, ok := aac.mutation.DeletedAt(); !ok {
		v := aiagent.DefaultDeletedAt
		aac.mutation.SetDeletedAt(v)
	}
	if _, ok := aac.mutation.ManageableToUser(); !ok {
		v := aiagent.DefaultManageableToUser
		aac.mutation.SetManageableToUser(v)
	}
	if _, ok := aac.mutation.VisibleToUser(); !ok {
		v := aiagent.DefaultVisibleToUser
		aac.mutation.SetVisibleToUser(v)
	}
	if _, ok := aac.mutation.VisibleToDept(); !ok {
		v := aiagent.DefaultVisibleToDept
		aac.mutation.SetVisibleToDept(v)
	}
	if _, ok := aac.mutation.KnowledgeBaseIds(); !ok {
		v := aiagent.DefaultKnowledgeBaseIds
		aac.mutation.SetKnowledgeBaseIds(v)
	}
	if _, ok := aac.mutation.ModelType(); !ok {
		v := aiagent.DefaultModelType
		aac.mutation.SetModelType(v)
	}
	if _, ok := aac.mutation.ModelID(); !ok {
		v := aiagent.DefaultModelID
		aac.mutation.SetModelID(v)
	}
	if _, ok := aac.mutation.UseCount(); !ok {
		v := aiagent.DefaultUseCount
		aac.mutation.SetUseCount(v)
	}
	if _, ok := aac.mutation.KnowledgeBaseType(); !ok {
		v := aiagent.DefaultKnowledgeBaseType
		aac.mutation.SetKnowledgeBaseType(v)
	}
	if _, ok := aac.mutation.InternetSearch(); !ok {
		v := aiagent.DefaultInternetSearch
		aac.mutation.SetInternetSearch(v)
	}
	if _, ok := aac.mutation.AgentType(); !ok {
		v := aiagent.DefaultAgentType
		aac.mutation.SetAgentType(v)
	}
	if _, ok := aac.mutation.Thinking(); !ok {
		v := aiagent.DefaultThinking
		aac.mutation.SetThinking(v)
	}
	if _, ok := aac.mutation.ThinkingModelID(); !ok {
		v := aiagent.DefaultThinkingModelID
		aac.mutation.SetThinkingModelID(v)
	}
	if _, ok := aac.mutation.UploadFile(); !ok {
		v := aiagent.DefaultUploadFile
		aac.mutation.SetUploadFile(v)
	}
	if _, ok := aac.mutation.SemanticCache(); !ok {
		v := aiagent.DefaultSemanticCache
		aac.mutation.SetSemanticCache(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (aac *AiAgentCreate) check() error {
	if _, ok := aac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AiAgent.created_at"`)}
	}
	if _, ok := aac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AiAgent.updated_at"`)}
	}
	if _, ok := aac.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "AiAgent.name"`)}
	}
	if _, ok := aac.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "AiAgent.description"`)}
	}
	if _, ok := aac.mutation.Avatar(); !ok {
		return &ValidationError{Name: "avatar", err: errors.New(`ent: missing required field "AiAgent.avatar"`)}
	}
	if _, ok := aac.mutation.ClickedAvatar(); !ok {
		return &ValidationError{Name: "clicked_avatar", err: errors.New(`ent: missing required field "AiAgent.clicked_avatar"`)}
	}
	if _, ok := aac.mutation.WelcomeMsg(); !ok {
		return &ValidationError{Name: "welcome_msg", err: errors.New(`ent: missing required field "AiAgent.welcome_msg"`)}
	}
	if _, ok := aac.mutation.FallbackMsg(); !ok {
		return &ValidationError{Name: "fallback_msg", err: errors.New(`ent: missing required field "AiAgent.fallback_msg"`)}
	}
	if _, ok := aac.mutation.OwnerID(); !ok {
		return &ValidationError{Name: "owner_id", err: errors.New(`ent: missing required field "AiAgent.owner_id"`)}
	}
	if _, ok := aac.mutation.VisibilityType(); !ok {
		return &ValidationError{Name: "visibility_type", err: errors.New(`ent: missing required field "AiAgent.visibility_type"`)}
	}
	if _, ok := aac.mutation.ManageableToUser(); !ok {
		return &ValidationError{Name: "manageable_to_user", err: errors.New(`ent: missing required field "AiAgent.manageable_to_user"`)}
	}
	if _, ok := aac.mutation.VisibleToUser(); !ok {
		return &ValidationError{Name: "visible_to_user", err: errors.New(`ent: missing required field "AiAgent.visible_to_user"`)}
	}
	if _, ok := aac.mutation.VisibleToDept(); !ok {
		return &ValidationError{Name: "visible_to_dept", err: errors.New(`ent: missing required field "AiAgent.visible_to_dept"`)}
	}
	if _, ok := aac.mutation.KnowledgeBaseIds(); !ok {
		return &ValidationError{Name: "knowledge_base_ids", err: errors.New(`ent: missing required field "AiAgent.knowledge_base_ids"`)}
	}
	if _, ok := aac.mutation.Schema(); !ok {
		return &ValidationError{Name: "schema", err: errors.New(`ent: missing required field "AiAgent.schema"`)}
	}
	if _, ok := aac.mutation.IsPublic(); !ok {
		return &ValidationError{Name: "is_public", err: errors.New(`ent: missing required field "AiAgent.is_public"`)}
	}
	if _, ok := aac.mutation.IsEnabled(); !ok {
		return &ValidationError{Name: "is_enabled", err: errors.New(`ent: missing required field "AiAgent.is_enabled"`)}
	}
	if _, ok := aac.mutation.IsRefFiles(); !ok {
		return &ValidationError{Name: "is_ref_files", err: errors.New(`ent: missing required field "AiAgent.is_ref_files"`)}
	}
	if _, ok := aac.mutation.ModelType(); !ok {
		return &ValidationError{Name: "model_type", err: errors.New(`ent: missing required field "AiAgent.model_type"`)}
	}
	if _, ok := aac.mutation.ModelID(); !ok {
		return &ValidationError{Name: "model_id", err: errors.New(`ent: missing required field "AiAgent.model_id"`)}
	}
	if _, ok := aac.mutation.UseCount(); !ok {
		return &ValidationError{Name: "use_count", err: errors.New(`ent: missing required field "AiAgent.use_count"`)}
	}
	if _, ok := aac.mutation.KnowledgeBaseType(); !ok {
		return &ValidationError{Name: "knowledge_base_type", err: errors.New(`ent: missing required field "AiAgent.knowledge_base_type"`)}
	}
	if _, ok := aac.mutation.InternetSearch(); !ok {
		return &ValidationError{Name: "internet_search", err: errors.New(`ent: missing required field "AiAgent.internet_search"`)}
	}
	if _, ok := aac.mutation.AgentType(); !ok {
		return &ValidationError{Name: "agent_type", err: errors.New(`ent: missing required field "AiAgent.agent_type"`)}
	}
	if _, ok := aac.mutation.Thinking(); !ok {
		return &ValidationError{Name: "thinking", err: errors.New(`ent: missing required field "AiAgent.thinking"`)}
	}
	if _, ok := aac.mutation.ThinkingModelID(); !ok {
		return &ValidationError{Name: "thinking_model_id", err: errors.New(`ent: missing required field "AiAgent.thinking_model_id"`)}
	}
	if _, ok := aac.mutation.RoleSetting(); !ok {
		return &ValidationError{Name: "role_setting", err: errors.New(`ent: missing required field "AiAgent.role_setting"`)}
	}
	if _, ok := aac.mutation.UploadFile(); !ok {
		return &ValidationError{Name: "upload_file", err: errors.New(`ent: missing required field "AiAgent.upload_file"`)}
	}
	if _, ok := aac.mutation.SemanticCache(); !ok {
		return &ValidationError{Name: "semantic_cache", err: errors.New(`ent: missing required field "AiAgent.semantic_cache"`)}
	}
	return nil
}

func (aac *AiAgentCreate) sqlSave(ctx context.Context) (*AiAgent, error) {
	if err := aac.check(); err != nil {
		return nil, err
	}
	_node, _spec := aac.createSpec()
	if err := sqlgraph.CreateNode(ctx, aac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	aac.mutation.id = &_node.ID
	aac.mutation.done = true
	return _node, nil
}

func (aac *AiAgentCreate) createSpec() (*AiAgent, *sqlgraph.CreateSpec) {
	var (
		_node = &AiAgent{config: aac.config}
		_spec = sqlgraph.NewCreateSpec(aiagent.Table, sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = aac.conflict
	if id, ok := aac.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := aac.mutation.CreatedAt(); ok {
		_spec.SetField(aiagent.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := aac.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagent.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := aac.mutation.DeletedAt(); ok {
		_spec.SetField(aiagent.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := aac.mutation.Name(); ok {
		_spec.SetField(aiagent.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := aac.mutation.Description(); ok {
		_spec.SetField(aiagent.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := aac.mutation.Avatar(); ok {
		_spec.SetField(aiagent.FieldAvatar, field.TypeString, value)
		_node.Avatar = value
	}
	if value, ok := aac.mutation.ClickedAvatar(); ok {
		_spec.SetField(aiagent.FieldClickedAvatar, field.TypeString, value)
		_node.ClickedAvatar = value
	}
	if value, ok := aac.mutation.WelcomeMsg(); ok {
		_spec.SetField(aiagent.FieldWelcomeMsg, field.TypeString, value)
		_node.WelcomeMsg = value
	}
	if value, ok := aac.mutation.FallbackMsg(); ok {
		_spec.SetField(aiagent.FieldFallbackMsg, field.TypeString, value)
		_node.FallbackMsg = value
	}
	if value, ok := aac.mutation.OwnerID(); ok {
		_spec.SetField(aiagent.FieldOwnerID, field.TypeInt64, value)
		_node.OwnerID = value
	}
	if value, ok := aac.mutation.VisibilityType(); ok {
		_spec.SetField(aiagent.FieldVisibilityType, field.TypeInt8, value)
		_node.VisibilityType = value
	}
	if value, ok := aac.mutation.ManageableToUser(); ok {
		_spec.SetField(aiagent.FieldManageableToUser, field.TypeOther, value)
		_node.ManageableToUser = value
	}
	if value, ok := aac.mutation.VisibleToUser(); ok {
		_spec.SetField(aiagent.FieldVisibleToUser, field.TypeOther, value)
		_node.VisibleToUser = value
	}
	if value, ok := aac.mutation.VisibleToDept(); ok {
		_spec.SetField(aiagent.FieldVisibleToDept, field.TypeOther, value)
		_node.VisibleToDept = value
	}
	if value, ok := aac.mutation.KnowledgeBaseIds(); ok {
		_spec.SetField(aiagent.FieldKnowledgeBaseIds, field.TypeOther, value)
		_node.KnowledgeBaseIds = value
	}
	if value, ok := aac.mutation.Schema(); ok {
		_spec.SetField(aiagent.FieldSchema, field.TypeString, value)
		_node.Schema = value
	}
	if value, ok := aac.mutation.IsPublic(); ok {
		_spec.SetField(aiagent.FieldIsPublic, field.TypeBool, value)
		_node.IsPublic = value
	}
	if value, ok := aac.mutation.IsEnabled(); ok {
		_spec.SetField(aiagent.FieldIsEnabled, field.TypeBool, value)
		_node.IsEnabled = value
	}
	if value, ok := aac.mutation.IsRefFiles(); ok {
		_spec.SetField(aiagent.FieldIsRefFiles, field.TypeBool, value)
		_node.IsRefFiles = value
	}
	if value, ok := aac.mutation.ModelType(); ok {
		_spec.SetField(aiagent.FieldModelType, field.TypeInt64, value)
		_node.ModelType = value
	}
	if value, ok := aac.mutation.ModelID(); ok {
		_spec.SetField(aiagent.FieldModelID, field.TypeInt64, value)
		_node.ModelID = value
	}
	if value, ok := aac.mutation.UseCount(); ok {
		_spec.SetField(aiagent.FieldUseCount, field.TypeInt64, value)
		_node.UseCount = value
	}
	if value, ok := aac.mutation.KnowledgeBaseType(); ok {
		_spec.SetField(aiagent.FieldKnowledgeBaseType, field.TypeInt64, value)
		_node.KnowledgeBaseType = value
	}
	if value, ok := aac.mutation.InternetSearch(); ok {
		_spec.SetField(aiagent.FieldInternetSearch, field.TypeBool, value)
		_node.InternetSearch = value
	}
	if value, ok := aac.mutation.AgentType(); ok {
		_spec.SetField(aiagent.FieldAgentType, field.TypeInt64, value)
		_node.AgentType = value
	}
	if value, ok := aac.mutation.Thinking(); ok {
		_spec.SetField(aiagent.FieldThinking, field.TypeBool, value)
		_node.Thinking = value
	}
	if value, ok := aac.mutation.ThinkingModelID(); ok {
		_spec.SetField(aiagent.FieldThinkingModelID, field.TypeInt64, value)
		_node.ThinkingModelID = value
	}
	if value, ok := aac.mutation.RoleSetting(); ok {
		_spec.SetField(aiagent.FieldRoleSetting, field.TypeString, value)
		_node.RoleSetting = value
	}
	if value, ok := aac.mutation.UploadFile(); ok {
		_spec.SetField(aiagent.FieldUploadFile, field.TypeBool, value)
		_node.UploadFile = value
	}
	if value, ok := aac.mutation.SemanticCache(); ok {
		_spec.SetField(aiagent.FieldSemanticCache, field.TypeBool, value)
		_node.SemanticCache = value
	}
	if nodes := aac.mutation.AiChatIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.AiChatTable,
			Columns: []string{aiagent.AiChatColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiAgent.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiAgentUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (aac *AiAgentCreate) OnConflict(opts ...sql.ConflictOption) *AiAgentUpsertOne {
	aac.conflict = opts
	return &AiAgentUpsertOne{
		create: aac,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiAgent.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aac *AiAgentCreate) OnConflictColumns(columns ...string) *AiAgentUpsertOne {
	aac.conflict = append(aac.conflict, sql.ConflictColumns(columns...))
	return &AiAgentUpsertOne{
		create: aac,
	}
}

type (
	// AiAgentUpsertOne is the builder for "upsert"-ing
	//  one AiAgent node.
	AiAgentUpsertOne struct {
		create *AiAgentCreate
	}

	// AiAgentUpsert is the "OnConflict" setter.
	AiAgentUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *AiAgentUpsert) SetUpdatedAt(v time.Time) *AiAgentUpsert {
	u.Set(aiagent.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateUpdatedAt() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiAgentUpsert) SetDeletedAt(v time.Time) *AiAgentUpsert {
	u.Set(aiagent.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateDeletedAt() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiAgentUpsert) ClearDeletedAt() *AiAgentUpsert {
	u.SetNull(aiagent.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *AiAgentUpsert) SetName(v string) *AiAgentUpsert {
	u.Set(aiagent.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateName() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldName)
	return u
}

// SetDescription sets the "description" field.
func (u *AiAgentUpsert) SetDescription(v string) *AiAgentUpsert {
	u.Set(aiagent.FieldDescription, v)
	return u
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateDescription() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldDescription)
	return u
}

// SetAvatar sets the "avatar" field.
func (u *AiAgentUpsert) SetAvatar(v string) *AiAgentUpsert {
	u.Set(aiagent.FieldAvatar, v)
	return u
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateAvatar() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldAvatar)
	return u
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (u *AiAgentUpsert) SetClickedAvatar(v string) *AiAgentUpsert {
	u.Set(aiagent.FieldClickedAvatar, v)
	return u
}

// UpdateClickedAvatar sets the "clicked_avatar" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateClickedAvatar() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldClickedAvatar)
	return u
}

// SetWelcomeMsg sets the "welcome_msg" field.
func (u *AiAgentUpsert) SetWelcomeMsg(v string) *AiAgentUpsert {
	u.Set(aiagent.FieldWelcomeMsg, v)
	return u
}

// UpdateWelcomeMsg sets the "welcome_msg" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateWelcomeMsg() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldWelcomeMsg)
	return u
}

// SetFallbackMsg sets the "fallback_msg" field.
func (u *AiAgentUpsert) SetFallbackMsg(v string) *AiAgentUpsert {
	u.Set(aiagent.FieldFallbackMsg, v)
	return u
}

// UpdateFallbackMsg sets the "fallback_msg" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateFallbackMsg() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldFallbackMsg)
	return u
}

// SetOwnerID sets the "owner_id" field.
func (u *AiAgentUpsert) SetOwnerID(v int64) *AiAgentUpsert {
	u.Set(aiagent.FieldOwnerID, v)
	return u
}

// UpdateOwnerID sets the "owner_id" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateOwnerID() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldOwnerID)
	return u
}

// AddOwnerID adds v to the "owner_id" field.
func (u *AiAgentUpsert) AddOwnerID(v int64) *AiAgentUpsert {
	u.Add(aiagent.FieldOwnerID, v)
	return u
}

// SetVisibilityType sets the "visibility_type" field.
func (u *AiAgentUpsert) SetVisibilityType(v int8) *AiAgentUpsert {
	u.Set(aiagent.FieldVisibilityType, v)
	return u
}

// UpdateVisibilityType sets the "visibility_type" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateVisibilityType() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldVisibilityType)
	return u
}

// AddVisibilityType adds v to the "visibility_type" field.
func (u *AiAgentUpsert) AddVisibilityType(v int8) *AiAgentUpsert {
	u.Add(aiagent.FieldVisibilityType, v)
	return u
}

// SetManageableToUser sets the "manageable_to_user" field.
func (u *AiAgentUpsert) SetManageableToUser(v *pq.Int64Array) *AiAgentUpsert {
	u.Set(aiagent.FieldManageableToUser, v)
	return u
}

// UpdateManageableToUser sets the "manageable_to_user" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateManageableToUser() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldManageableToUser)
	return u
}

// SetVisibleToUser sets the "visible_to_user" field.
func (u *AiAgentUpsert) SetVisibleToUser(v *pq.Int64Array) *AiAgentUpsert {
	u.Set(aiagent.FieldVisibleToUser, v)
	return u
}

// UpdateVisibleToUser sets the "visible_to_user" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateVisibleToUser() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldVisibleToUser)
	return u
}

// SetVisibleToDept sets the "visible_to_dept" field.
func (u *AiAgentUpsert) SetVisibleToDept(v *pq.Int64Array) *AiAgentUpsert {
	u.Set(aiagent.FieldVisibleToDept, v)
	return u
}

// UpdateVisibleToDept sets the "visible_to_dept" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateVisibleToDept() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldVisibleToDept)
	return u
}

// SetKnowledgeBaseIds sets the "knowledge_base_ids" field.
func (u *AiAgentUpsert) SetKnowledgeBaseIds(v *pq.Int64Array) *AiAgentUpsert {
	u.Set(aiagent.FieldKnowledgeBaseIds, v)
	return u
}

// UpdateKnowledgeBaseIds sets the "knowledge_base_ids" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateKnowledgeBaseIds() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldKnowledgeBaseIds)
	return u
}

// SetSchema sets the "schema" field.
func (u *AiAgentUpsert) SetSchema(v string) *AiAgentUpsert {
	u.Set(aiagent.FieldSchema, v)
	return u
}

// UpdateSchema sets the "schema" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateSchema() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldSchema)
	return u
}

// SetIsPublic sets the "is_public" field.
func (u *AiAgentUpsert) SetIsPublic(v bool) *AiAgentUpsert {
	u.Set(aiagent.FieldIsPublic, v)
	return u
}

// UpdateIsPublic sets the "is_public" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateIsPublic() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldIsPublic)
	return u
}

// SetIsEnabled sets the "is_enabled" field.
func (u *AiAgentUpsert) SetIsEnabled(v bool) *AiAgentUpsert {
	u.Set(aiagent.FieldIsEnabled, v)
	return u
}

// UpdateIsEnabled sets the "is_enabled" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateIsEnabled() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldIsEnabled)
	return u
}

// SetIsRefFiles sets the "is_ref_files" field.
func (u *AiAgentUpsert) SetIsRefFiles(v bool) *AiAgentUpsert {
	u.Set(aiagent.FieldIsRefFiles, v)
	return u
}

// UpdateIsRefFiles sets the "is_ref_files" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateIsRefFiles() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldIsRefFiles)
	return u
}

// SetModelType sets the "model_type" field.
func (u *AiAgentUpsert) SetModelType(v int64) *AiAgentUpsert {
	u.Set(aiagent.FieldModelType, v)
	return u
}

// UpdateModelType sets the "model_type" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateModelType() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldModelType)
	return u
}

// AddModelType adds v to the "model_type" field.
func (u *AiAgentUpsert) AddModelType(v int64) *AiAgentUpsert {
	u.Add(aiagent.FieldModelType, v)
	return u
}

// SetModelID sets the "model_id" field.
func (u *AiAgentUpsert) SetModelID(v int64) *AiAgentUpsert {
	u.Set(aiagent.FieldModelID, v)
	return u
}

// UpdateModelID sets the "model_id" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateModelID() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldModelID)
	return u
}

// AddModelID adds v to the "model_id" field.
func (u *AiAgentUpsert) AddModelID(v int64) *AiAgentUpsert {
	u.Add(aiagent.FieldModelID, v)
	return u
}

// SetUseCount sets the "use_count" field.
func (u *AiAgentUpsert) SetUseCount(v int64) *AiAgentUpsert {
	u.Set(aiagent.FieldUseCount, v)
	return u
}

// UpdateUseCount sets the "use_count" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateUseCount() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldUseCount)
	return u
}

// AddUseCount adds v to the "use_count" field.
func (u *AiAgentUpsert) AddUseCount(v int64) *AiAgentUpsert {
	u.Add(aiagent.FieldUseCount, v)
	return u
}

// SetKnowledgeBaseType sets the "knowledge_base_type" field.
func (u *AiAgentUpsert) SetKnowledgeBaseType(v int64) *AiAgentUpsert {
	u.Set(aiagent.FieldKnowledgeBaseType, v)
	return u
}

// UpdateKnowledgeBaseType sets the "knowledge_base_type" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateKnowledgeBaseType() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldKnowledgeBaseType)
	return u
}

// AddKnowledgeBaseType adds v to the "knowledge_base_type" field.
func (u *AiAgentUpsert) AddKnowledgeBaseType(v int64) *AiAgentUpsert {
	u.Add(aiagent.FieldKnowledgeBaseType, v)
	return u
}

// SetInternetSearch sets the "internet_search" field.
func (u *AiAgentUpsert) SetInternetSearch(v bool) *AiAgentUpsert {
	u.Set(aiagent.FieldInternetSearch, v)
	return u
}

// UpdateInternetSearch sets the "internet_search" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateInternetSearch() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldInternetSearch)
	return u
}

// SetAgentType sets the "agent_type" field.
func (u *AiAgentUpsert) SetAgentType(v int64) *AiAgentUpsert {
	u.Set(aiagent.FieldAgentType, v)
	return u
}

// UpdateAgentType sets the "agent_type" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateAgentType() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldAgentType)
	return u
}

// AddAgentType adds v to the "agent_type" field.
func (u *AiAgentUpsert) AddAgentType(v int64) *AiAgentUpsert {
	u.Add(aiagent.FieldAgentType, v)
	return u
}

// SetThinking sets the "thinking" field.
func (u *AiAgentUpsert) SetThinking(v bool) *AiAgentUpsert {
	u.Set(aiagent.FieldThinking, v)
	return u
}

// UpdateThinking sets the "thinking" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateThinking() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldThinking)
	return u
}

// SetThinkingModelID sets the "thinking_model_id" field.
func (u *AiAgentUpsert) SetThinkingModelID(v int64) *AiAgentUpsert {
	u.Set(aiagent.FieldThinkingModelID, v)
	return u
}

// UpdateThinkingModelID sets the "thinking_model_id" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateThinkingModelID() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldThinkingModelID)
	return u
}

// AddThinkingModelID adds v to the "thinking_model_id" field.
func (u *AiAgentUpsert) AddThinkingModelID(v int64) *AiAgentUpsert {
	u.Add(aiagent.FieldThinkingModelID, v)
	return u
}

// SetRoleSetting sets the "role_setting" field.
func (u *AiAgentUpsert) SetRoleSetting(v string) *AiAgentUpsert {
	u.Set(aiagent.FieldRoleSetting, v)
	return u
}

// UpdateRoleSetting sets the "role_setting" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateRoleSetting() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldRoleSetting)
	return u
}

// SetUploadFile sets the "upload_file" field.
func (u *AiAgentUpsert) SetUploadFile(v bool) *AiAgentUpsert {
	u.Set(aiagent.FieldUploadFile, v)
	return u
}

// UpdateUploadFile sets the "upload_file" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateUploadFile() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldUploadFile)
	return u
}

// SetSemanticCache sets the "semantic_cache" field.
func (u *AiAgentUpsert) SetSemanticCache(v bool) *AiAgentUpsert {
	u.Set(aiagent.FieldSemanticCache, v)
	return u
}

// UpdateSemanticCache sets the "semantic_cache" field to the value that was provided on create.
func (u *AiAgentUpsert) UpdateSemanticCache() *AiAgentUpsert {
	u.SetExcluded(aiagent.FieldSemanticCache)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AiAgent.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aiagent.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiAgentUpsertOne) UpdateNewValues() *AiAgentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(aiagent.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(aiagent.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiAgent.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AiAgentUpsertOne) Ignore() *AiAgentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiAgentUpsertOne) DoNothing() *AiAgentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiAgentCreate.OnConflict
// documentation for more info.
func (u *AiAgentUpsertOne) Update(set func(*AiAgentUpsert)) *AiAgentUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiAgentUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiAgentUpsertOne) SetUpdatedAt(v time.Time) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateUpdatedAt() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiAgentUpsertOne) SetDeletedAt(v time.Time) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateDeletedAt() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiAgentUpsertOne) ClearDeletedAt() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *AiAgentUpsertOne) SetName(v string) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateName() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateName()
	})
}

// SetDescription sets the "description" field.
func (u *AiAgentUpsertOne) SetDescription(v string) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateDescription() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateDescription()
	})
}

// SetAvatar sets the "avatar" field.
func (u *AiAgentUpsertOne) SetAvatar(v string) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetAvatar(v)
	})
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateAvatar() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateAvatar()
	})
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (u *AiAgentUpsertOne) SetClickedAvatar(v string) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetClickedAvatar(v)
	})
}

// UpdateClickedAvatar sets the "clicked_avatar" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateClickedAvatar() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateClickedAvatar()
	})
}

// SetWelcomeMsg sets the "welcome_msg" field.
func (u *AiAgentUpsertOne) SetWelcomeMsg(v string) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetWelcomeMsg(v)
	})
}

// UpdateWelcomeMsg sets the "welcome_msg" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateWelcomeMsg() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateWelcomeMsg()
	})
}

// SetFallbackMsg sets the "fallback_msg" field.
func (u *AiAgentUpsertOne) SetFallbackMsg(v string) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetFallbackMsg(v)
	})
}

// UpdateFallbackMsg sets the "fallback_msg" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateFallbackMsg() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateFallbackMsg()
	})
}

// SetOwnerID sets the "owner_id" field.
func (u *AiAgentUpsertOne) SetOwnerID(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetOwnerID(v)
	})
}

// AddOwnerID adds v to the "owner_id" field.
func (u *AiAgentUpsertOne) AddOwnerID(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddOwnerID(v)
	})
}

// UpdateOwnerID sets the "owner_id" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateOwnerID() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateOwnerID()
	})
}

// SetVisibilityType sets the "visibility_type" field.
func (u *AiAgentUpsertOne) SetVisibilityType(v int8) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetVisibilityType(v)
	})
}

// AddVisibilityType adds v to the "visibility_type" field.
func (u *AiAgentUpsertOne) AddVisibilityType(v int8) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddVisibilityType(v)
	})
}

// UpdateVisibilityType sets the "visibility_type" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateVisibilityType() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateVisibilityType()
	})
}

// SetManageableToUser sets the "manageable_to_user" field.
func (u *AiAgentUpsertOne) SetManageableToUser(v *pq.Int64Array) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetManageableToUser(v)
	})
}

// UpdateManageableToUser sets the "manageable_to_user" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateManageableToUser() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateManageableToUser()
	})
}

// SetVisibleToUser sets the "visible_to_user" field.
func (u *AiAgentUpsertOne) SetVisibleToUser(v *pq.Int64Array) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetVisibleToUser(v)
	})
}

// UpdateVisibleToUser sets the "visible_to_user" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateVisibleToUser() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateVisibleToUser()
	})
}

// SetVisibleToDept sets the "visible_to_dept" field.
func (u *AiAgentUpsertOne) SetVisibleToDept(v *pq.Int64Array) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetVisibleToDept(v)
	})
}

// UpdateVisibleToDept sets the "visible_to_dept" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateVisibleToDept() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateVisibleToDept()
	})
}

// SetKnowledgeBaseIds sets the "knowledge_base_ids" field.
func (u *AiAgentUpsertOne) SetKnowledgeBaseIds(v *pq.Int64Array) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetKnowledgeBaseIds(v)
	})
}

// UpdateKnowledgeBaseIds sets the "knowledge_base_ids" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateKnowledgeBaseIds() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateKnowledgeBaseIds()
	})
}

// SetSchema sets the "schema" field.
func (u *AiAgentUpsertOne) SetSchema(v string) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetSchema(v)
	})
}

// UpdateSchema sets the "schema" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateSchema() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateSchema()
	})
}

// SetIsPublic sets the "is_public" field.
func (u *AiAgentUpsertOne) SetIsPublic(v bool) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetIsPublic(v)
	})
}

// UpdateIsPublic sets the "is_public" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateIsPublic() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateIsPublic()
	})
}

// SetIsEnabled sets the "is_enabled" field.
func (u *AiAgentUpsertOne) SetIsEnabled(v bool) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetIsEnabled(v)
	})
}

// UpdateIsEnabled sets the "is_enabled" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateIsEnabled() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateIsEnabled()
	})
}

// SetIsRefFiles sets the "is_ref_files" field.
func (u *AiAgentUpsertOne) SetIsRefFiles(v bool) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetIsRefFiles(v)
	})
}

// UpdateIsRefFiles sets the "is_ref_files" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateIsRefFiles() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateIsRefFiles()
	})
}

// SetModelType sets the "model_type" field.
func (u *AiAgentUpsertOne) SetModelType(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetModelType(v)
	})
}

// AddModelType adds v to the "model_type" field.
func (u *AiAgentUpsertOne) AddModelType(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddModelType(v)
	})
}

// UpdateModelType sets the "model_type" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateModelType() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateModelType()
	})
}

// SetModelID sets the "model_id" field.
func (u *AiAgentUpsertOne) SetModelID(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetModelID(v)
	})
}

// AddModelID adds v to the "model_id" field.
func (u *AiAgentUpsertOne) AddModelID(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddModelID(v)
	})
}

// UpdateModelID sets the "model_id" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateModelID() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateModelID()
	})
}

// SetUseCount sets the "use_count" field.
func (u *AiAgentUpsertOne) SetUseCount(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetUseCount(v)
	})
}

// AddUseCount adds v to the "use_count" field.
func (u *AiAgentUpsertOne) AddUseCount(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddUseCount(v)
	})
}

// UpdateUseCount sets the "use_count" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateUseCount() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateUseCount()
	})
}

// SetKnowledgeBaseType sets the "knowledge_base_type" field.
func (u *AiAgentUpsertOne) SetKnowledgeBaseType(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetKnowledgeBaseType(v)
	})
}

// AddKnowledgeBaseType adds v to the "knowledge_base_type" field.
func (u *AiAgentUpsertOne) AddKnowledgeBaseType(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddKnowledgeBaseType(v)
	})
}

// UpdateKnowledgeBaseType sets the "knowledge_base_type" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateKnowledgeBaseType() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateKnowledgeBaseType()
	})
}

// SetInternetSearch sets the "internet_search" field.
func (u *AiAgentUpsertOne) SetInternetSearch(v bool) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetInternetSearch(v)
	})
}

// UpdateInternetSearch sets the "internet_search" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateInternetSearch() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateInternetSearch()
	})
}

// SetAgentType sets the "agent_type" field.
func (u *AiAgentUpsertOne) SetAgentType(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetAgentType(v)
	})
}

// AddAgentType adds v to the "agent_type" field.
func (u *AiAgentUpsertOne) AddAgentType(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddAgentType(v)
	})
}

// UpdateAgentType sets the "agent_type" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateAgentType() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateAgentType()
	})
}

// SetThinking sets the "thinking" field.
func (u *AiAgentUpsertOne) SetThinking(v bool) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetThinking(v)
	})
}

// UpdateThinking sets the "thinking" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateThinking() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateThinking()
	})
}

// SetThinkingModelID sets the "thinking_model_id" field.
func (u *AiAgentUpsertOne) SetThinkingModelID(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetThinkingModelID(v)
	})
}

// AddThinkingModelID adds v to the "thinking_model_id" field.
func (u *AiAgentUpsertOne) AddThinkingModelID(v int64) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddThinkingModelID(v)
	})
}

// UpdateThinkingModelID sets the "thinking_model_id" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateThinkingModelID() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateThinkingModelID()
	})
}

// SetRoleSetting sets the "role_setting" field.
func (u *AiAgentUpsertOne) SetRoleSetting(v string) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetRoleSetting(v)
	})
}

// UpdateRoleSetting sets the "role_setting" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateRoleSetting() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateRoleSetting()
	})
}

// SetUploadFile sets the "upload_file" field.
func (u *AiAgentUpsertOne) SetUploadFile(v bool) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetUploadFile(v)
	})
}

// UpdateUploadFile sets the "upload_file" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateUploadFile() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateUploadFile()
	})
}

// SetSemanticCache sets the "semantic_cache" field.
func (u *AiAgentUpsertOne) SetSemanticCache(v bool) *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetSemanticCache(v)
	})
}

// UpdateSemanticCache sets the "semantic_cache" field to the value that was provided on create.
func (u *AiAgentUpsertOne) UpdateSemanticCache() *AiAgentUpsertOne {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateSemanticCache()
	})
}

// Exec executes the query.
func (u *AiAgentUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiAgentCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiAgentUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AiAgentUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AiAgentUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AiAgentCreateBulk is the builder for creating many AiAgent entities in bulk.
type AiAgentCreateBulk struct {
	config
	err      error
	builders []*AiAgentCreate
	conflict []sql.ConflictOption
}

// Save creates the AiAgent entities in the database.
func (aacb *AiAgentCreateBulk) Save(ctx context.Context) ([]*AiAgent, error) {
	if aacb.err != nil {
		return nil, aacb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(aacb.builders))
	nodes := make([]*AiAgent, len(aacb.builders))
	mutators := make([]Mutator, len(aacb.builders))
	for i := range aacb.builders {
		func(i int, root context.Context) {
			builder := aacb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AiAgentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, aacb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = aacb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, aacb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, aacb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (aacb *AiAgentCreateBulk) SaveX(ctx context.Context) []*AiAgent {
	v, err := aacb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aacb *AiAgentCreateBulk) Exec(ctx context.Context) error {
	_, err := aacb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aacb *AiAgentCreateBulk) ExecX(ctx context.Context) {
	if err := aacb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiAgent.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiAgentUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (aacb *AiAgentCreateBulk) OnConflict(opts ...sql.ConflictOption) *AiAgentUpsertBulk {
	aacb.conflict = opts
	return &AiAgentUpsertBulk{
		create: aacb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiAgent.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aacb *AiAgentCreateBulk) OnConflictColumns(columns ...string) *AiAgentUpsertBulk {
	aacb.conflict = append(aacb.conflict, sql.ConflictColumns(columns...))
	return &AiAgentUpsertBulk{
		create: aacb,
	}
}

// AiAgentUpsertBulk is the builder for "upsert"-ing
// a bulk of AiAgent nodes.
type AiAgentUpsertBulk struct {
	create *AiAgentCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AiAgent.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aiagent.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiAgentUpsertBulk) UpdateNewValues() *AiAgentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(aiagent.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(aiagent.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiAgent.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AiAgentUpsertBulk) Ignore() *AiAgentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiAgentUpsertBulk) DoNothing() *AiAgentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiAgentCreateBulk.OnConflict
// documentation for more info.
func (u *AiAgentUpsertBulk) Update(set func(*AiAgentUpsert)) *AiAgentUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiAgentUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiAgentUpsertBulk) SetUpdatedAt(v time.Time) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateUpdatedAt() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiAgentUpsertBulk) SetDeletedAt(v time.Time) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateDeletedAt() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiAgentUpsertBulk) ClearDeletedAt() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *AiAgentUpsertBulk) SetName(v string) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateName() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateName()
	})
}

// SetDescription sets the "description" field.
func (u *AiAgentUpsertBulk) SetDescription(v string) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateDescription() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateDescription()
	})
}

// SetAvatar sets the "avatar" field.
func (u *AiAgentUpsertBulk) SetAvatar(v string) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetAvatar(v)
	})
}

// UpdateAvatar sets the "avatar" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateAvatar() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateAvatar()
	})
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (u *AiAgentUpsertBulk) SetClickedAvatar(v string) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetClickedAvatar(v)
	})
}

// UpdateClickedAvatar sets the "clicked_avatar" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateClickedAvatar() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateClickedAvatar()
	})
}

// SetWelcomeMsg sets the "welcome_msg" field.
func (u *AiAgentUpsertBulk) SetWelcomeMsg(v string) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetWelcomeMsg(v)
	})
}

// UpdateWelcomeMsg sets the "welcome_msg" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateWelcomeMsg() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateWelcomeMsg()
	})
}

// SetFallbackMsg sets the "fallback_msg" field.
func (u *AiAgentUpsertBulk) SetFallbackMsg(v string) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetFallbackMsg(v)
	})
}

// UpdateFallbackMsg sets the "fallback_msg" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateFallbackMsg() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateFallbackMsg()
	})
}

// SetOwnerID sets the "owner_id" field.
func (u *AiAgentUpsertBulk) SetOwnerID(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetOwnerID(v)
	})
}

// AddOwnerID adds v to the "owner_id" field.
func (u *AiAgentUpsertBulk) AddOwnerID(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddOwnerID(v)
	})
}

// UpdateOwnerID sets the "owner_id" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateOwnerID() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateOwnerID()
	})
}

// SetVisibilityType sets the "visibility_type" field.
func (u *AiAgentUpsertBulk) SetVisibilityType(v int8) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetVisibilityType(v)
	})
}

// AddVisibilityType adds v to the "visibility_type" field.
func (u *AiAgentUpsertBulk) AddVisibilityType(v int8) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddVisibilityType(v)
	})
}

// UpdateVisibilityType sets the "visibility_type" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateVisibilityType() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateVisibilityType()
	})
}

// SetManageableToUser sets the "manageable_to_user" field.
func (u *AiAgentUpsertBulk) SetManageableToUser(v *pq.Int64Array) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetManageableToUser(v)
	})
}

// UpdateManageableToUser sets the "manageable_to_user" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateManageableToUser() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateManageableToUser()
	})
}

// SetVisibleToUser sets the "visible_to_user" field.
func (u *AiAgentUpsertBulk) SetVisibleToUser(v *pq.Int64Array) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetVisibleToUser(v)
	})
}

// UpdateVisibleToUser sets the "visible_to_user" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateVisibleToUser() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateVisibleToUser()
	})
}

// SetVisibleToDept sets the "visible_to_dept" field.
func (u *AiAgentUpsertBulk) SetVisibleToDept(v *pq.Int64Array) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetVisibleToDept(v)
	})
}

// UpdateVisibleToDept sets the "visible_to_dept" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateVisibleToDept() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateVisibleToDept()
	})
}

// SetKnowledgeBaseIds sets the "knowledge_base_ids" field.
func (u *AiAgentUpsertBulk) SetKnowledgeBaseIds(v *pq.Int64Array) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetKnowledgeBaseIds(v)
	})
}

// UpdateKnowledgeBaseIds sets the "knowledge_base_ids" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateKnowledgeBaseIds() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateKnowledgeBaseIds()
	})
}

// SetSchema sets the "schema" field.
func (u *AiAgentUpsertBulk) SetSchema(v string) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetSchema(v)
	})
}

// UpdateSchema sets the "schema" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateSchema() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateSchema()
	})
}

// SetIsPublic sets the "is_public" field.
func (u *AiAgentUpsertBulk) SetIsPublic(v bool) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetIsPublic(v)
	})
}

// UpdateIsPublic sets the "is_public" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateIsPublic() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateIsPublic()
	})
}

// SetIsEnabled sets the "is_enabled" field.
func (u *AiAgentUpsertBulk) SetIsEnabled(v bool) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetIsEnabled(v)
	})
}

// UpdateIsEnabled sets the "is_enabled" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateIsEnabled() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateIsEnabled()
	})
}

// SetIsRefFiles sets the "is_ref_files" field.
func (u *AiAgentUpsertBulk) SetIsRefFiles(v bool) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetIsRefFiles(v)
	})
}

// UpdateIsRefFiles sets the "is_ref_files" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateIsRefFiles() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateIsRefFiles()
	})
}

// SetModelType sets the "model_type" field.
func (u *AiAgentUpsertBulk) SetModelType(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetModelType(v)
	})
}

// AddModelType adds v to the "model_type" field.
func (u *AiAgentUpsertBulk) AddModelType(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddModelType(v)
	})
}

// UpdateModelType sets the "model_type" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateModelType() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateModelType()
	})
}

// SetModelID sets the "model_id" field.
func (u *AiAgentUpsertBulk) SetModelID(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetModelID(v)
	})
}

// AddModelID adds v to the "model_id" field.
func (u *AiAgentUpsertBulk) AddModelID(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddModelID(v)
	})
}

// UpdateModelID sets the "model_id" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateModelID() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateModelID()
	})
}

// SetUseCount sets the "use_count" field.
func (u *AiAgentUpsertBulk) SetUseCount(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetUseCount(v)
	})
}

// AddUseCount adds v to the "use_count" field.
func (u *AiAgentUpsertBulk) AddUseCount(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddUseCount(v)
	})
}

// UpdateUseCount sets the "use_count" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateUseCount() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateUseCount()
	})
}

// SetKnowledgeBaseType sets the "knowledge_base_type" field.
func (u *AiAgentUpsertBulk) SetKnowledgeBaseType(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetKnowledgeBaseType(v)
	})
}

// AddKnowledgeBaseType adds v to the "knowledge_base_type" field.
func (u *AiAgentUpsertBulk) AddKnowledgeBaseType(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddKnowledgeBaseType(v)
	})
}

// UpdateKnowledgeBaseType sets the "knowledge_base_type" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateKnowledgeBaseType() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateKnowledgeBaseType()
	})
}

// SetInternetSearch sets the "internet_search" field.
func (u *AiAgentUpsertBulk) SetInternetSearch(v bool) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetInternetSearch(v)
	})
}

// UpdateInternetSearch sets the "internet_search" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateInternetSearch() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateInternetSearch()
	})
}

// SetAgentType sets the "agent_type" field.
func (u *AiAgentUpsertBulk) SetAgentType(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetAgentType(v)
	})
}

// AddAgentType adds v to the "agent_type" field.
func (u *AiAgentUpsertBulk) AddAgentType(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddAgentType(v)
	})
}

// UpdateAgentType sets the "agent_type" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateAgentType() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateAgentType()
	})
}

// SetThinking sets the "thinking" field.
func (u *AiAgentUpsertBulk) SetThinking(v bool) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetThinking(v)
	})
}

// UpdateThinking sets the "thinking" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateThinking() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateThinking()
	})
}

// SetThinkingModelID sets the "thinking_model_id" field.
func (u *AiAgentUpsertBulk) SetThinkingModelID(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetThinkingModelID(v)
	})
}

// AddThinkingModelID adds v to the "thinking_model_id" field.
func (u *AiAgentUpsertBulk) AddThinkingModelID(v int64) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.AddThinkingModelID(v)
	})
}

// UpdateThinkingModelID sets the "thinking_model_id" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateThinkingModelID() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateThinkingModelID()
	})
}

// SetRoleSetting sets the "role_setting" field.
func (u *AiAgentUpsertBulk) SetRoleSetting(v string) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetRoleSetting(v)
	})
}

// UpdateRoleSetting sets the "role_setting" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateRoleSetting() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateRoleSetting()
	})
}

// SetUploadFile sets the "upload_file" field.
func (u *AiAgentUpsertBulk) SetUploadFile(v bool) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetUploadFile(v)
	})
}

// UpdateUploadFile sets the "upload_file" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateUploadFile() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateUploadFile()
	})
}

// SetSemanticCache sets the "semantic_cache" field.
func (u *AiAgentUpsertBulk) SetSemanticCache(v bool) *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.SetSemanticCache(v)
	})
}

// UpdateSemanticCache sets the "semantic_cache" field to the value that was provided on create.
func (u *AiAgentUpsertBulk) UpdateSemanticCache() *AiAgentUpsertBulk {
	return u.Update(func(s *AiAgentUpsert) {
		s.UpdateSemanticCache()
	})
}

// Exec executes the query.
func (u *AiAgentUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AiAgentCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiAgentCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiAgentUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

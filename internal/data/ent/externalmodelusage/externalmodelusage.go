// Code generated by ent, DO NOT EDIT.

package externalmodelusage

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

const (
	// Label holds the string label denoting the externalmodelusage type in the database.
	Label = "external_model_usage"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldModelName holds the string denoting the model_name field in the database.
	FieldModelName = "model_name"
	// FieldQuestion holds the string denoting the question field in the database.
	FieldQuestion = "question"
	// FieldQuestionTag holds the string denoting the question_tag field in the database.
	FieldQuestionTag = "question_tag"
	// FieldFiles holds the string denoting the files field in the database.
	FieldFiles = "files"
	// FieldMimeTypes holds the string denoting the mime_types field in the database.
	FieldMimeTypes = "mime_types"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldUserName holds the string denoting the user_name field in the database.
	FieldUserName = "user_name"
	// FieldDeptID holds the string denoting the dept_id field in the database.
	FieldDeptID = "dept_id"
	// FieldDeptName holds the string denoting the dept_name field in the database.
	FieldDeptName = "dept_name"
	// FieldPcName holds the string denoting the pc_name field in the database.
	FieldPcName = "pc_name"
	// FieldHappenedAt holds the string denoting the happened_at field in the database.
	FieldHappenedAt = "happened_at"
	// Table holds the table name of the externalmodelusage in the database.
	Table = "external_model_usage"
)

// Columns holds all SQL columns for externalmodelusage fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldModelName,
	FieldQuestion,
	FieldQuestionTag,
	FieldFiles,
	FieldMimeTypes,
	FieldUserID,
	FieldUserName,
	FieldDeptID,
	FieldDeptName,
	FieldPcName,
	FieldHappenedAt,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultDeletedAt holds the default value on creation for the "deleted_at" field.
	DefaultDeletedAt time.Time
	// DefaultModelName holds the default value on creation for the "model_name" field.
	DefaultModelName string
	// DefaultQuestion holds the default value on creation for the "question" field.
	DefaultQuestion string
	// DefaultQuestionTag holds the default value on creation for the "question_tag" field.
	DefaultQuestionTag string
	// DefaultFiles holds the default value on creation for the "files" field.
	DefaultFiles string
	// DefaultMimeTypes holds the default value on creation for the "mime_types" field.
	DefaultMimeTypes *pq.StringArray
	// DefaultUserID holds the default value on creation for the "user_id" field.
	DefaultUserID int64
	// DefaultUserName holds the default value on creation for the "user_name" field.
	DefaultUserName string
	// DefaultDeptID holds the default value on creation for the "dept_id" field.
	DefaultDeptID int64
	// DefaultDeptName holds the default value on creation for the "dept_name" field.
	DefaultDeptName string
	// DefaultPcName holds the default value on creation for the "pc_name" field.
	DefaultPcName string
	// DefaultHappenedAt holds the default value on creation for the "happened_at" field.
	DefaultHappenedAt time.Time
)

// OrderOption defines the ordering options for the ExternalModelUsage queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByModelName orders the results by the model_name field.
func ByModelName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelName, opts...).ToFunc()
}

// ByQuestion orders the results by the question field.
func ByQuestion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldQuestion, opts...).ToFunc()
}

// ByQuestionTag orders the results by the question_tag field.
func ByQuestionTag(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldQuestionTag, opts...).ToFunc()
}

// ByFiles orders the results by the files field.
func ByFiles(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFiles, opts...).ToFunc()
}

// ByMimeTypes orders the results by the mime_types field.
func ByMimeTypes(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMimeTypes, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByUserName orders the results by the user_name field.
func ByUserName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserName, opts...).ToFunc()
}

// ByDeptID orders the results by the dept_id field.
func ByDeptID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeptID, opts...).ToFunc()
}

// ByDeptName orders the results by the dept_name field.
func ByDeptName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeptName, opts...).ToFunc()
}

// ByPcName orders the results by the pc_name field.
func ByPcName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPcName, opts...).ToFunc()
}

// ByHappenedAt orders the results by the happened_at field.
func ByHappenedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHappenedAt, opts...).ToFunc()
}

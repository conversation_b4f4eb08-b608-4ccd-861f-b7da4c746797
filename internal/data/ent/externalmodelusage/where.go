// Code generated by ent, DO NOT EDIT.

package externalmodelusage

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldDeletedAt, v))
}

// ModelName applies equality check predicate on the "model_name" field. It's identical to ModelNameEQ.
func ModelName(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldModelName, v))
}

// Question applies equality check predicate on the "question" field. It's identical to QuestionEQ.
func Question(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldQuestion, v))
}

// QuestionTag applies equality check predicate on the "question_tag" field. It's identical to QuestionTagEQ.
func QuestionTag(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldQuestionTag, v))
}

// Files applies equality check predicate on the "files" field. It's identical to FilesEQ.
func Files(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldFiles, v))
}

// MimeTypes applies equality check predicate on the "mime_types" field. It's identical to MimeTypesEQ.
func MimeTypes(v *pq.StringArray) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldMimeTypes, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldUserID, v))
}

// UserName applies equality check predicate on the "user_name" field. It's identical to UserNameEQ.
func UserName(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldUserName, v))
}

// DeptID applies equality check predicate on the "dept_id" field. It's identical to DeptIDEQ.
func DeptID(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldDeptID, v))
}

// DeptName applies equality check predicate on the "dept_name" field. It's identical to DeptNameEQ.
func DeptName(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldDeptName, v))
}

// PcName applies equality check predicate on the "pc_name" field. It's identical to PcNameEQ.
func PcName(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldPcName, v))
}

// HappenedAt applies equality check predicate on the "happened_at" field. It's identical to HappenedAtEQ.
func HappenedAt(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldHappenedAt, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotNull(FieldDeletedAt))
}

// ModelNameEQ applies the EQ predicate on the "model_name" field.
func ModelNameEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldModelName, v))
}

// ModelNameNEQ applies the NEQ predicate on the "model_name" field.
func ModelNameNEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldModelName, v))
}

// ModelNameIn applies the In predicate on the "model_name" field.
func ModelNameIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldModelName, vs...))
}

// ModelNameNotIn applies the NotIn predicate on the "model_name" field.
func ModelNameNotIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldModelName, vs...))
}

// ModelNameGT applies the GT predicate on the "model_name" field.
func ModelNameGT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldModelName, v))
}

// ModelNameGTE applies the GTE predicate on the "model_name" field.
func ModelNameGTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldModelName, v))
}

// ModelNameLT applies the LT predicate on the "model_name" field.
func ModelNameLT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldModelName, v))
}

// ModelNameLTE applies the LTE predicate on the "model_name" field.
func ModelNameLTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldModelName, v))
}

// ModelNameContains applies the Contains predicate on the "model_name" field.
func ModelNameContains(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContains(FieldModelName, v))
}

// ModelNameHasPrefix applies the HasPrefix predicate on the "model_name" field.
func ModelNameHasPrefix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasPrefix(FieldModelName, v))
}

// ModelNameHasSuffix applies the HasSuffix predicate on the "model_name" field.
func ModelNameHasSuffix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasSuffix(FieldModelName, v))
}

// ModelNameEqualFold applies the EqualFold predicate on the "model_name" field.
func ModelNameEqualFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEqualFold(FieldModelName, v))
}

// ModelNameContainsFold applies the ContainsFold predicate on the "model_name" field.
func ModelNameContainsFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContainsFold(FieldModelName, v))
}

// QuestionEQ applies the EQ predicate on the "question" field.
func QuestionEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldQuestion, v))
}

// QuestionNEQ applies the NEQ predicate on the "question" field.
func QuestionNEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldQuestion, v))
}

// QuestionIn applies the In predicate on the "question" field.
func QuestionIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldQuestion, vs...))
}

// QuestionNotIn applies the NotIn predicate on the "question" field.
func QuestionNotIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldQuestion, vs...))
}

// QuestionGT applies the GT predicate on the "question" field.
func QuestionGT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldQuestion, v))
}

// QuestionGTE applies the GTE predicate on the "question" field.
func QuestionGTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldQuestion, v))
}

// QuestionLT applies the LT predicate on the "question" field.
func QuestionLT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldQuestion, v))
}

// QuestionLTE applies the LTE predicate on the "question" field.
func QuestionLTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldQuestion, v))
}

// QuestionContains applies the Contains predicate on the "question" field.
func QuestionContains(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContains(FieldQuestion, v))
}

// QuestionHasPrefix applies the HasPrefix predicate on the "question" field.
func QuestionHasPrefix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasPrefix(FieldQuestion, v))
}

// QuestionHasSuffix applies the HasSuffix predicate on the "question" field.
func QuestionHasSuffix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasSuffix(FieldQuestion, v))
}

// QuestionEqualFold applies the EqualFold predicate on the "question" field.
func QuestionEqualFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEqualFold(FieldQuestion, v))
}

// QuestionContainsFold applies the ContainsFold predicate on the "question" field.
func QuestionContainsFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContainsFold(FieldQuestion, v))
}

// QuestionTagEQ applies the EQ predicate on the "question_tag" field.
func QuestionTagEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldQuestionTag, v))
}

// QuestionTagNEQ applies the NEQ predicate on the "question_tag" field.
func QuestionTagNEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldQuestionTag, v))
}

// QuestionTagIn applies the In predicate on the "question_tag" field.
func QuestionTagIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldQuestionTag, vs...))
}

// QuestionTagNotIn applies the NotIn predicate on the "question_tag" field.
func QuestionTagNotIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldQuestionTag, vs...))
}

// QuestionTagGT applies the GT predicate on the "question_tag" field.
func QuestionTagGT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldQuestionTag, v))
}

// QuestionTagGTE applies the GTE predicate on the "question_tag" field.
func QuestionTagGTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldQuestionTag, v))
}

// QuestionTagLT applies the LT predicate on the "question_tag" field.
func QuestionTagLT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldQuestionTag, v))
}

// QuestionTagLTE applies the LTE predicate on the "question_tag" field.
func QuestionTagLTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldQuestionTag, v))
}

// QuestionTagContains applies the Contains predicate on the "question_tag" field.
func QuestionTagContains(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContains(FieldQuestionTag, v))
}

// QuestionTagHasPrefix applies the HasPrefix predicate on the "question_tag" field.
func QuestionTagHasPrefix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasPrefix(FieldQuestionTag, v))
}

// QuestionTagHasSuffix applies the HasSuffix predicate on the "question_tag" field.
func QuestionTagHasSuffix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasSuffix(FieldQuestionTag, v))
}

// QuestionTagEqualFold applies the EqualFold predicate on the "question_tag" field.
func QuestionTagEqualFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEqualFold(FieldQuestionTag, v))
}

// QuestionTagContainsFold applies the ContainsFold predicate on the "question_tag" field.
func QuestionTagContainsFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContainsFold(FieldQuestionTag, v))
}

// FilesEQ applies the EQ predicate on the "files" field.
func FilesEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldFiles, v))
}

// FilesNEQ applies the NEQ predicate on the "files" field.
func FilesNEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldFiles, v))
}

// FilesIn applies the In predicate on the "files" field.
func FilesIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldFiles, vs...))
}

// FilesNotIn applies the NotIn predicate on the "files" field.
func FilesNotIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldFiles, vs...))
}

// FilesGT applies the GT predicate on the "files" field.
func FilesGT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldFiles, v))
}

// FilesGTE applies the GTE predicate on the "files" field.
func FilesGTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldFiles, v))
}

// FilesLT applies the LT predicate on the "files" field.
func FilesLT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldFiles, v))
}

// FilesLTE applies the LTE predicate on the "files" field.
func FilesLTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldFiles, v))
}

// FilesContains applies the Contains predicate on the "files" field.
func FilesContains(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContains(FieldFiles, v))
}

// FilesHasPrefix applies the HasPrefix predicate on the "files" field.
func FilesHasPrefix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasPrefix(FieldFiles, v))
}

// FilesHasSuffix applies the HasSuffix predicate on the "files" field.
func FilesHasSuffix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasSuffix(FieldFiles, v))
}

// FilesEqualFold applies the EqualFold predicate on the "files" field.
func FilesEqualFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEqualFold(FieldFiles, v))
}

// FilesContainsFold applies the ContainsFold predicate on the "files" field.
func FilesContainsFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContainsFold(FieldFiles, v))
}

// MimeTypesEQ applies the EQ predicate on the "mime_types" field.
func MimeTypesEQ(v *pq.StringArray) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldMimeTypes, v))
}

// MimeTypesNEQ applies the NEQ predicate on the "mime_types" field.
func MimeTypesNEQ(v *pq.StringArray) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldMimeTypes, v))
}

// MimeTypesIn applies the In predicate on the "mime_types" field.
func MimeTypesIn(vs ...*pq.StringArray) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldMimeTypes, vs...))
}

// MimeTypesNotIn applies the NotIn predicate on the "mime_types" field.
func MimeTypesNotIn(vs ...*pq.StringArray) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldMimeTypes, vs...))
}

// MimeTypesGT applies the GT predicate on the "mime_types" field.
func MimeTypesGT(v *pq.StringArray) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldMimeTypes, v))
}

// MimeTypesGTE applies the GTE predicate on the "mime_types" field.
func MimeTypesGTE(v *pq.StringArray) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldMimeTypes, v))
}

// MimeTypesLT applies the LT predicate on the "mime_types" field.
func MimeTypesLT(v *pq.StringArray) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldMimeTypes, v))
}

// MimeTypesLTE applies the LTE predicate on the "mime_types" field.
func MimeTypesLTE(v *pq.StringArray) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldMimeTypes, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldUserID, v))
}

// UserNameEQ applies the EQ predicate on the "user_name" field.
func UserNameEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldUserName, v))
}

// UserNameNEQ applies the NEQ predicate on the "user_name" field.
func UserNameNEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldUserName, v))
}

// UserNameIn applies the In predicate on the "user_name" field.
func UserNameIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldUserName, vs...))
}

// UserNameNotIn applies the NotIn predicate on the "user_name" field.
func UserNameNotIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldUserName, vs...))
}

// UserNameGT applies the GT predicate on the "user_name" field.
func UserNameGT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldUserName, v))
}

// UserNameGTE applies the GTE predicate on the "user_name" field.
func UserNameGTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldUserName, v))
}

// UserNameLT applies the LT predicate on the "user_name" field.
func UserNameLT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldUserName, v))
}

// UserNameLTE applies the LTE predicate on the "user_name" field.
func UserNameLTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldUserName, v))
}

// UserNameContains applies the Contains predicate on the "user_name" field.
func UserNameContains(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContains(FieldUserName, v))
}

// UserNameHasPrefix applies the HasPrefix predicate on the "user_name" field.
func UserNameHasPrefix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasPrefix(FieldUserName, v))
}

// UserNameHasSuffix applies the HasSuffix predicate on the "user_name" field.
func UserNameHasSuffix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasSuffix(FieldUserName, v))
}

// UserNameEqualFold applies the EqualFold predicate on the "user_name" field.
func UserNameEqualFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEqualFold(FieldUserName, v))
}

// UserNameContainsFold applies the ContainsFold predicate on the "user_name" field.
func UserNameContainsFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContainsFold(FieldUserName, v))
}

// DeptIDEQ applies the EQ predicate on the "dept_id" field.
func DeptIDEQ(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldDeptID, v))
}

// DeptIDNEQ applies the NEQ predicate on the "dept_id" field.
func DeptIDNEQ(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldDeptID, v))
}

// DeptIDIn applies the In predicate on the "dept_id" field.
func DeptIDIn(vs ...int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldDeptID, vs...))
}

// DeptIDNotIn applies the NotIn predicate on the "dept_id" field.
func DeptIDNotIn(vs ...int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldDeptID, vs...))
}

// DeptIDGT applies the GT predicate on the "dept_id" field.
func DeptIDGT(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldDeptID, v))
}

// DeptIDGTE applies the GTE predicate on the "dept_id" field.
func DeptIDGTE(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldDeptID, v))
}

// DeptIDLT applies the LT predicate on the "dept_id" field.
func DeptIDLT(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldDeptID, v))
}

// DeptIDLTE applies the LTE predicate on the "dept_id" field.
func DeptIDLTE(v int64) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldDeptID, v))
}

// DeptNameEQ applies the EQ predicate on the "dept_name" field.
func DeptNameEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldDeptName, v))
}

// DeptNameNEQ applies the NEQ predicate on the "dept_name" field.
func DeptNameNEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldDeptName, v))
}

// DeptNameIn applies the In predicate on the "dept_name" field.
func DeptNameIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldDeptName, vs...))
}

// DeptNameNotIn applies the NotIn predicate on the "dept_name" field.
func DeptNameNotIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldDeptName, vs...))
}

// DeptNameGT applies the GT predicate on the "dept_name" field.
func DeptNameGT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldDeptName, v))
}

// DeptNameGTE applies the GTE predicate on the "dept_name" field.
func DeptNameGTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldDeptName, v))
}

// DeptNameLT applies the LT predicate on the "dept_name" field.
func DeptNameLT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldDeptName, v))
}

// DeptNameLTE applies the LTE predicate on the "dept_name" field.
func DeptNameLTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldDeptName, v))
}

// DeptNameContains applies the Contains predicate on the "dept_name" field.
func DeptNameContains(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContains(FieldDeptName, v))
}

// DeptNameHasPrefix applies the HasPrefix predicate on the "dept_name" field.
func DeptNameHasPrefix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasPrefix(FieldDeptName, v))
}

// DeptNameHasSuffix applies the HasSuffix predicate on the "dept_name" field.
func DeptNameHasSuffix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasSuffix(FieldDeptName, v))
}

// DeptNameEqualFold applies the EqualFold predicate on the "dept_name" field.
func DeptNameEqualFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEqualFold(FieldDeptName, v))
}

// DeptNameContainsFold applies the ContainsFold predicate on the "dept_name" field.
func DeptNameContainsFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContainsFold(FieldDeptName, v))
}

// PcNameEQ applies the EQ predicate on the "pc_name" field.
func PcNameEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldPcName, v))
}

// PcNameNEQ applies the NEQ predicate on the "pc_name" field.
func PcNameNEQ(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldPcName, v))
}

// PcNameIn applies the In predicate on the "pc_name" field.
func PcNameIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldPcName, vs...))
}

// PcNameNotIn applies the NotIn predicate on the "pc_name" field.
func PcNameNotIn(vs ...string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldPcName, vs...))
}

// PcNameGT applies the GT predicate on the "pc_name" field.
func PcNameGT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldPcName, v))
}

// PcNameGTE applies the GTE predicate on the "pc_name" field.
func PcNameGTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldPcName, v))
}

// PcNameLT applies the LT predicate on the "pc_name" field.
func PcNameLT(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldPcName, v))
}

// PcNameLTE applies the LTE predicate on the "pc_name" field.
func PcNameLTE(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldPcName, v))
}

// PcNameContains applies the Contains predicate on the "pc_name" field.
func PcNameContains(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContains(FieldPcName, v))
}

// PcNameHasPrefix applies the HasPrefix predicate on the "pc_name" field.
func PcNameHasPrefix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasPrefix(FieldPcName, v))
}

// PcNameHasSuffix applies the HasSuffix predicate on the "pc_name" field.
func PcNameHasSuffix(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldHasSuffix(FieldPcName, v))
}

// PcNameEqualFold applies the EqualFold predicate on the "pc_name" field.
func PcNameEqualFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEqualFold(FieldPcName, v))
}

// PcNameContainsFold applies the ContainsFold predicate on the "pc_name" field.
func PcNameContainsFold(v string) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldContainsFold(FieldPcName, v))
}

// HappenedAtEQ applies the EQ predicate on the "happened_at" field.
func HappenedAtEQ(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldEQ(FieldHappenedAt, v))
}

// HappenedAtNEQ applies the NEQ predicate on the "happened_at" field.
func HappenedAtNEQ(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNEQ(FieldHappenedAt, v))
}

// HappenedAtIn applies the In predicate on the "happened_at" field.
func HappenedAtIn(vs ...time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldIn(FieldHappenedAt, vs...))
}

// HappenedAtNotIn applies the NotIn predicate on the "happened_at" field.
func HappenedAtNotIn(vs ...time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldNotIn(FieldHappenedAt, vs...))
}

// HappenedAtGT applies the GT predicate on the "happened_at" field.
func HappenedAtGT(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGT(FieldHappenedAt, v))
}

// HappenedAtGTE applies the GTE predicate on the "happened_at" field.
func HappenedAtGTE(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldGTE(FieldHappenedAt, v))
}

// HappenedAtLT applies the LT predicate on the "happened_at" field.
func HappenedAtLT(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLT(FieldHappenedAt, v))
}

// HappenedAtLTE applies the LTE predicate on the "happened_at" field.
func HappenedAtLTE(v time.Time) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.FieldLTE(FieldHappenedAt, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.ExternalModelUsage) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.ExternalModelUsage) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.ExternalModelUsage) predicate.ExternalModelUsage {
	return predicate.ExternalModelUsage(sql.NotPredicates(p))
}

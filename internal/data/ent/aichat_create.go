// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
)

// AiChatCreate is the builder for creating a AiChat entity.
type AiChatCreate struct {
	config
	mutation *AiChatMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetTenantID sets the "tenant_id" field.
func (acc *AiChatCreate) SetTenantID(i int64) *AiChatCreate {
	acc.mutation.SetTenantID(i)
	return acc
}

// SetCreatedAt sets the "created_at" field.
func (acc *AiChatCreate) SetCreatedAt(t time.Time) *AiChatCreate {
	acc.mutation.SetCreatedAt(t)
	return acc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (acc *AiChatCreate) SetNillableCreatedAt(t *time.Time) *AiChatCreate {
	if t != nil {
		acc.SetCreatedAt(*t)
	}
	return acc
}

// SetUpdatedAt sets the "updated_at" field.
func (acc *AiChatCreate) SetUpdatedAt(t time.Time) *AiChatCreate {
	acc.mutation.SetUpdatedAt(t)
	return acc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (acc *AiChatCreate) SetNillableUpdatedAt(t *time.Time) *AiChatCreate {
	if t != nil {
		acc.SetUpdatedAt(*t)
	}
	return acc
}

// SetDeletedAt sets the "deleted_at" field.
func (acc *AiChatCreate) SetDeletedAt(t time.Time) *AiChatCreate {
	acc.mutation.SetDeletedAt(t)
	return acc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (acc *AiChatCreate) SetNillableDeletedAt(t *time.Time) *AiChatCreate {
	if t != nil {
		acc.SetDeletedAt(*t)
	}
	return acc
}

// SetName sets the "name" field.
func (acc *AiChatCreate) SetName(s string) *AiChatCreate {
	acc.mutation.SetName(s)
	return acc
}

// SetUserID sets the "user_id" field.
func (acc *AiChatCreate) SetUserID(i int64) *AiChatCreate {
	acc.mutation.SetUserID(i)
	return acc
}

// SetChatType sets the "chat_type" field.
func (acc *AiChatCreate) SetChatType(i int64) *AiChatCreate {
	acc.mutation.SetChatType(i)
	return acc
}

// SetAgentID sets the "agent_id" field.
func (acc *AiChatCreate) SetAgentID(i int64) *AiChatCreate {
	acc.mutation.SetAgentID(i)
	return acc
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (acc *AiChatCreate) SetNillableAgentID(i *int64) *AiChatCreate {
	if i != nil {
		acc.SetAgentID(*i)
	}
	return acc
}

// SetID sets the "id" field.
func (acc *AiChatCreate) SetID(i int64) *AiChatCreate {
	acc.mutation.SetID(i)
	return acc
}

// SetAiAgentID sets the "ai_agent" edge to the AiAgent entity by ID.
func (acc *AiChatCreate) SetAiAgentID(id int64) *AiChatCreate {
	acc.mutation.SetAiAgentID(id)
	return acc
}

// SetNillableAiAgentID sets the "ai_agent" edge to the AiAgent entity by ID if the given value is not nil.
func (acc *AiChatCreate) SetNillableAiAgentID(id *int64) *AiChatCreate {
	if id != nil {
		acc = acc.SetAiAgentID(*id)
	}
	return acc
}

// SetAiAgent sets the "ai_agent" edge to the AiAgent entity.
func (acc *AiChatCreate) SetAiAgent(a *AiAgent) *AiChatCreate {
	return acc.SetAiAgentID(a.ID)
}

// Mutation returns the AiChatMutation object of the builder.
func (acc *AiChatCreate) Mutation() *AiChatMutation {
	return acc.mutation
}

// Save creates the AiChat in the database.
func (acc *AiChatCreate) Save(ctx context.Context) (*AiChat, error) {
	if err := acc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, acc.sqlSave, acc.mutation, acc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (acc *AiChatCreate) SaveX(ctx context.Context) *AiChat {
	v, err := acc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acc *AiChatCreate) Exec(ctx context.Context) error {
	_, err := acc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acc *AiChatCreate) ExecX(ctx context.Context) {
	if err := acc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (acc *AiChatCreate) defaults() error {
	if _, ok := acc.mutation.CreatedAt(); !ok {
		if aichat.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized aichat.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := aichat.DefaultCreatedAt()
		acc.mutation.SetCreatedAt(v)
	}
	if _, ok := acc.mutation.UpdatedAt(); !ok {
		if aichat.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aichat.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aichat.DefaultUpdatedAt()
		acc.mutation.SetUpdatedAt(v)
	}
	if _, ok := acc.mutation.DeletedAt(); !ok {
		v := aichat.DefaultDeletedAt
		acc.mutation.SetDeletedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (acc *AiChatCreate) check() error {
	if _, ok := acc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "AiChat.tenant_id"`)}
	}
	if _, ok := acc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AiChat.created_at"`)}
	}
	if _, ok := acc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AiChat.updated_at"`)}
	}
	if _, ok := acc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "AiChat.name"`)}
	}
	if _, ok := acc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "AiChat.user_id"`)}
	}
	if _, ok := acc.mutation.ChatType(); !ok {
		return &ValidationError{Name: "chat_type", err: errors.New(`ent: missing required field "AiChat.chat_type"`)}
	}
	return nil
}

func (acc *AiChatCreate) sqlSave(ctx context.Context) (*AiChat, error) {
	if err := acc.check(); err != nil {
		return nil, err
	}
	_node, _spec := acc.createSpec()
	if err := sqlgraph.CreateNode(ctx, acc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	acc.mutation.id = &_node.ID
	acc.mutation.done = true
	return _node, nil
}

func (acc *AiChatCreate) createSpec() (*AiChat, *sqlgraph.CreateSpec) {
	var (
		_node = &AiChat{config: acc.config}
		_spec = sqlgraph.NewCreateSpec(aichat.Table, sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = acc.conflict
	if id, ok := acc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := acc.mutation.TenantID(); ok {
		_spec.SetField(aichat.FieldTenantID, field.TypeInt64, value)
		_node.TenantID = value
	}
	if value, ok := acc.mutation.CreatedAt(); ok {
		_spec.SetField(aichat.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := acc.mutation.UpdatedAt(); ok {
		_spec.SetField(aichat.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := acc.mutation.DeletedAt(); ok {
		_spec.SetField(aichat.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := acc.mutation.Name(); ok {
		_spec.SetField(aichat.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := acc.mutation.UserID(); ok {
		_spec.SetField(aichat.FieldUserID, field.TypeInt64, value)
		_node.UserID = value
	}
	if value, ok := acc.mutation.ChatType(); ok {
		_spec.SetField(aichat.FieldChatType, field.TypeInt64, value)
		_node.ChatType = value
	}
	if nodes := acc.mutation.AiAgentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   aichat.AiAgentTable,
			Columns: []string{aichat.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.AgentID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiChat.Create().
//		SetTenantID(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiChatUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (acc *AiChatCreate) OnConflict(opts ...sql.ConflictOption) *AiChatUpsertOne {
	acc.conflict = opts
	return &AiChatUpsertOne{
		create: acc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiChat.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (acc *AiChatCreate) OnConflictColumns(columns ...string) *AiChatUpsertOne {
	acc.conflict = append(acc.conflict, sql.ConflictColumns(columns...))
	return &AiChatUpsertOne{
		create: acc,
	}
}

type (
	// AiChatUpsertOne is the builder for "upsert"-ing
	//  one AiChat node.
	AiChatUpsertOne struct {
		create *AiChatCreate
	}

	// AiChatUpsert is the "OnConflict" setter.
	AiChatUpsert struct {
		*sql.UpdateSet
	}
)

// SetTenantID sets the "tenant_id" field.
func (u *AiChatUpsert) SetTenantID(v int64) *AiChatUpsert {
	u.Set(aichat.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *AiChatUpsert) UpdateTenantID() *AiChatUpsert {
	u.SetExcluded(aichat.FieldTenantID)
	return u
}

// AddTenantID adds v to the "tenant_id" field.
func (u *AiChatUpsert) AddTenantID(v int64) *AiChatUpsert {
	u.Add(aichat.FieldTenantID, v)
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiChatUpsert) SetUpdatedAt(v time.Time) *AiChatUpsert {
	u.Set(aichat.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiChatUpsert) UpdateUpdatedAt() *AiChatUpsert {
	u.SetExcluded(aichat.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiChatUpsert) SetDeletedAt(v time.Time) *AiChatUpsert {
	u.Set(aichat.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiChatUpsert) UpdateDeletedAt() *AiChatUpsert {
	u.SetExcluded(aichat.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiChatUpsert) ClearDeletedAt() *AiChatUpsert {
	u.SetNull(aichat.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *AiChatUpsert) SetName(v string) *AiChatUpsert {
	u.Set(aichat.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiChatUpsert) UpdateName() *AiChatUpsert {
	u.SetExcluded(aichat.FieldName)
	return u
}

// SetUserID sets the "user_id" field.
func (u *AiChatUpsert) SetUserID(v int64) *AiChatUpsert {
	u.Set(aichat.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AiChatUpsert) UpdateUserID() *AiChatUpsert {
	u.SetExcluded(aichat.FieldUserID)
	return u
}

// AddUserID adds v to the "user_id" field.
func (u *AiChatUpsert) AddUserID(v int64) *AiChatUpsert {
	u.Add(aichat.FieldUserID, v)
	return u
}

// SetChatType sets the "chat_type" field.
func (u *AiChatUpsert) SetChatType(v int64) *AiChatUpsert {
	u.Set(aichat.FieldChatType, v)
	return u
}

// UpdateChatType sets the "chat_type" field to the value that was provided on create.
func (u *AiChatUpsert) UpdateChatType() *AiChatUpsert {
	u.SetExcluded(aichat.FieldChatType)
	return u
}

// AddChatType adds v to the "chat_type" field.
func (u *AiChatUpsert) AddChatType(v int64) *AiChatUpsert {
	u.Add(aichat.FieldChatType, v)
	return u
}

// SetAgentID sets the "agent_id" field.
func (u *AiChatUpsert) SetAgentID(v int64) *AiChatUpsert {
	u.Set(aichat.FieldAgentID, v)
	return u
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiChatUpsert) UpdateAgentID() *AiChatUpsert {
	u.SetExcluded(aichat.FieldAgentID)
	return u
}

// ClearAgentID clears the value of the "agent_id" field.
func (u *AiChatUpsert) ClearAgentID() *AiChatUpsert {
	u.SetNull(aichat.FieldAgentID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AiChat.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aichat.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiChatUpsertOne) UpdateNewValues() *AiChatUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(aichat.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(aichat.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiChat.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AiChatUpsertOne) Ignore() *AiChatUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiChatUpsertOne) DoNothing() *AiChatUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiChatCreate.OnConflict
// documentation for more info.
func (u *AiChatUpsertOne) Update(set func(*AiChatUpsert)) *AiChatUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiChatUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *AiChatUpsertOne) SetTenantID(v int64) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *AiChatUpsertOne) AddTenantID(v int64) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *AiChatUpsertOne) UpdateTenantID() *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiChatUpsertOne) SetUpdatedAt(v time.Time) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiChatUpsertOne) UpdateUpdatedAt() *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiChatUpsertOne) SetDeletedAt(v time.Time) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiChatUpsertOne) UpdateDeletedAt() *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiChatUpsertOne) ClearDeletedAt() *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *AiChatUpsertOne) SetName(v string) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiChatUpsertOne) UpdateName() *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateName()
	})
}

// SetUserID sets the "user_id" field.
func (u *AiChatUpsertOne) SetUserID(v int64) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *AiChatUpsertOne) AddUserID(v int64) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AiChatUpsertOne) UpdateUserID() *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateUserID()
	})
}

// SetChatType sets the "chat_type" field.
func (u *AiChatUpsertOne) SetChatType(v int64) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.SetChatType(v)
	})
}

// AddChatType adds v to the "chat_type" field.
func (u *AiChatUpsertOne) AddChatType(v int64) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.AddChatType(v)
	})
}

// UpdateChatType sets the "chat_type" field to the value that was provided on create.
func (u *AiChatUpsertOne) UpdateChatType() *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateChatType()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *AiChatUpsertOne) SetAgentID(v int64) *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.SetAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiChatUpsertOne) UpdateAgentID() *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateAgentID()
	})
}

// ClearAgentID clears the value of the "agent_id" field.
func (u *AiChatUpsertOne) ClearAgentID() *AiChatUpsertOne {
	return u.Update(func(s *AiChatUpsert) {
		s.ClearAgentID()
	})
}

// Exec executes the query.
func (u *AiChatUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiChatCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiChatUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AiChatUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AiChatUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AiChatCreateBulk is the builder for creating many AiChat entities in bulk.
type AiChatCreateBulk struct {
	config
	err      error
	builders []*AiChatCreate
	conflict []sql.ConflictOption
}

// Save creates the AiChat entities in the database.
func (accb *AiChatCreateBulk) Save(ctx context.Context) ([]*AiChat, error) {
	if accb.err != nil {
		return nil, accb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(accb.builders))
	nodes := make([]*AiChat, len(accb.builders))
	mutators := make([]Mutator, len(accb.builders))
	for i := range accb.builders {
		func(i int, root context.Context) {
			builder := accb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AiChatMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, accb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = accb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, accb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, accb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (accb *AiChatCreateBulk) SaveX(ctx context.Context) []*AiChat {
	v, err := accb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (accb *AiChatCreateBulk) Exec(ctx context.Context) error {
	_, err := accb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (accb *AiChatCreateBulk) ExecX(ctx context.Context) {
	if err := accb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiChat.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiChatUpsert) {
//			SetTenantID(v+v).
//		}).
//		Exec(ctx)
func (accb *AiChatCreateBulk) OnConflict(opts ...sql.ConflictOption) *AiChatUpsertBulk {
	accb.conflict = opts
	return &AiChatUpsertBulk{
		create: accb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiChat.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (accb *AiChatCreateBulk) OnConflictColumns(columns ...string) *AiChatUpsertBulk {
	accb.conflict = append(accb.conflict, sql.ConflictColumns(columns...))
	return &AiChatUpsertBulk{
		create: accb,
	}
}

// AiChatUpsertBulk is the builder for "upsert"-ing
// a bulk of AiChat nodes.
type AiChatUpsertBulk struct {
	create *AiChatCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AiChat.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aichat.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiChatUpsertBulk) UpdateNewValues() *AiChatUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(aichat.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(aichat.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiChat.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AiChatUpsertBulk) Ignore() *AiChatUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiChatUpsertBulk) DoNothing() *AiChatUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiChatCreateBulk.OnConflict
// documentation for more info.
func (u *AiChatUpsertBulk) Update(set func(*AiChatUpsert)) *AiChatUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiChatUpsert{UpdateSet: update})
	}))
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *AiChatUpsertBulk) SetTenantID(v int64) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.SetTenantID(v)
	})
}

// AddTenantID adds v to the "tenant_id" field.
func (u *AiChatUpsertBulk) AddTenantID(v int64) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.AddTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *AiChatUpsertBulk) UpdateTenantID() *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateTenantID()
	})
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiChatUpsertBulk) SetUpdatedAt(v time.Time) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiChatUpsertBulk) UpdateUpdatedAt() *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiChatUpsertBulk) SetDeletedAt(v time.Time) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiChatUpsertBulk) UpdateDeletedAt() *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiChatUpsertBulk) ClearDeletedAt() *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *AiChatUpsertBulk) SetName(v string) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *AiChatUpsertBulk) UpdateName() *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateName()
	})
}

// SetUserID sets the "user_id" field.
func (u *AiChatUpsertBulk) SetUserID(v int64) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *AiChatUpsertBulk) AddUserID(v int64) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AiChatUpsertBulk) UpdateUserID() *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateUserID()
	})
}

// SetChatType sets the "chat_type" field.
func (u *AiChatUpsertBulk) SetChatType(v int64) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.SetChatType(v)
	})
}

// AddChatType adds v to the "chat_type" field.
func (u *AiChatUpsertBulk) AddChatType(v int64) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.AddChatType(v)
	})
}

// UpdateChatType sets the "chat_type" field to the value that was provided on create.
func (u *AiChatUpsertBulk) UpdateChatType() *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateChatType()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *AiChatUpsertBulk) SetAgentID(v int64) *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.SetAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiChatUpsertBulk) UpdateAgentID() *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.UpdateAgentID()
	})
}

// ClearAgentID clears the value of the "agent_id" field.
func (u *AiChatUpsertBulk) ClearAgentID() *AiChatUpsertBulk {
	return u.Update(func(s *AiChatUpsert) {
		s.ClearAgentID()
	})
}

// Exec executes the query.
func (u *AiChatUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AiChatCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiChatCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiChatUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

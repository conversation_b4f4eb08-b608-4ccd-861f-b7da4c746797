// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritypolicy"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiAgentSecurityPolicyDelete is the builder for deleting a AiAgentSecurityPolicy entity.
type AiAgentSecurityPolicyDelete struct {
	config
	hooks    []Hook
	mutation *AiAgentSecurityPolicyMutation
}

// Where appends a list predicates to the AiAgentSecurityPolicyDelete builder.
func (aaspd *AiAgentSecurityPolicyDelete) Where(ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyDelete {
	aaspd.mutation.Where(ps...)
	return aaspd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (aaspd *AiAgentSecurityPolicyDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, aaspd.sqlExec, aaspd.mutation, aaspd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (aaspd *AiAgentSecurityPolicyDelete) ExecX(ctx context.Context) int {
	n, err := aaspd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (aaspd *AiAgentSecurityPolicyDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(aiagentsecuritypolicy.Table, sqlgraph.NewFieldSpec(aiagentsecuritypolicy.FieldID, field.TypeInt64))
	if ps := aaspd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, aaspd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	aaspd.mutation.done = true
	return affected, err
}

// AiAgentSecurityPolicyDeleteOne is the builder for deleting a single AiAgentSecurityPolicy entity.
type AiAgentSecurityPolicyDeleteOne struct {
	aaspd *AiAgentSecurityPolicyDelete
}

// Where appends a list predicates to the AiAgentSecurityPolicyDelete builder.
func (aaspdo *AiAgentSecurityPolicyDeleteOne) Where(ps ...predicate.AiAgentSecurityPolicy) *AiAgentSecurityPolicyDeleteOne {
	aaspdo.aaspd.mutation.Where(ps...)
	return aaspdo
}

// Exec executes the deletion query.
func (aaspdo *AiAgentSecurityPolicyDeleteOne) Exec(ctx context.Context) error {
	n, err := aaspdo.aaspd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{aiagentsecuritypolicy.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (aaspdo *AiAgentSecurityPolicyDeleteOne) ExecX(ctx context.Context) {
	if err := aaspdo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/atomicquestions"
)

// AtomicQuestionsCreate is the builder for creating a AtomicQuestions entity.
type AtomicQuestionsCreate struct {
	config
	mutation *AtomicQuestionsMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (aqc *AtomicQuestionsCreate) SetCreatedAt(t time.Time) *AtomicQuestionsCreate {
	aqc.mutation.SetCreatedAt(t)
	return aqc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (aqc *AtomicQuestionsCreate) SetNillableCreatedAt(t *time.Time) *AtomicQuestionsCreate {
	if t != nil {
		aqc.SetCreatedAt(*t)
	}
	return aqc
}

// SetUpdatedAt sets the "updated_at" field.
func (aqc *AtomicQuestionsCreate) SetUpdatedAt(t time.Time) *AtomicQuestionsCreate {
	aqc.mutation.SetUpdatedAt(t)
	return aqc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (aqc *AtomicQuestionsCreate) SetNillableUpdatedAt(t *time.Time) *AtomicQuestionsCreate {
	if t != nil {
		aqc.SetUpdatedAt(*t)
	}
	return aqc
}

// SetEntityTag sets the "entity_tag" field.
func (aqc *AtomicQuestionsCreate) SetEntityTag(s string) *AtomicQuestionsCreate {
	aqc.mutation.SetEntityTag(s)
	return aqc
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (aqc *AtomicQuestionsCreate) SetPreEntityTag(s string) *AtomicQuestionsCreate {
	aqc.mutation.SetPreEntityTag(s)
	return aqc
}

// SetFileRelationID sets the "file_relation_id" field.
func (aqc *AtomicQuestionsCreate) SetFileRelationID(i int64) *AtomicQuestionsCreate {
	aqc.mutation.SetFileRelationID(i)
	return aqc
}

// SetChunkIndex sets the "chunk_index" field.
func (aqc *AtomicQuestionsCreate) SetChunkIndex(i int64) *AtomicQuestionsCreate {
	aqc.mutation.SetChunkIndex(i)
	return aqc
}

// SetChunkSize sets the "chunk_size" field.
func (aqc *AtomicQuestionsCreate) SetChunkSize(i int64) *AtomicQuestionsCreate {
	aqc.mutation.SetChunkSize(i)
	return aqc
}

// SetIndex sets the "index" field.
func (aqc *AtomicQuestionsCreate) SetIndex(i int64) *AtomicQuestionsCreate {
	aqc.mutation.SetIndex(i)
	return aqc
}

// SetQuestion sets the "question" field.
func (aqc *AtomicQuestionsCreate) SetQuestion(pa *pq.StringArray) *AtomicQuestionsCreate {
	aqc.mutation.SetQuestion(pa)
	return aqc
}

// SetIsHandle sets the "is_handle" field.
func (aqc *AtomicQuestionsCreate) SetIsHandle(b bool) *AtomicQuestionsCreate {
	aqc.mutation.SetIsHandle(b)
	return aqc
}

// SetNillableIsHandle sets the "is_handle" field if the given value is not nil.
func (aqc *AtomicQuestionsCreate) SetNillableIsHandle(b *bool) *AtomicQuestionsCreate {
	if b != nil {
		aqc.SetIsHandle(*b)
	}
	return aqc
}

// SetID sets the "id" field.
func (aqc *AtomicQuestionsCreate) SetID(i int64) *AtomicQuestionsCreate {
	aqc.mutation.SetID(i)
	return aqc
}

// Mutation returns the AtomicQuestionsMutation object of the builder.
func (aqc *AtomicQuestionsCreate) Mutation() *AtomicQuestionsMutation {
	return aqc.mutation
}

// Save creates the AtomicQuestions in the database.
func (aqc *AtomicQuestionsCreate) Save(ctx context.Context) (*AtomicQuestions, error) {
	aqc.defaults()
	return withHooks(ctx, aqc.sqlSave, aqc.mutation, aqc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (aqc *AtomicQuestionsCreate) SaveX(ctx context.Context) *AtomicQuestions {
	v, err := aqc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aqc *AtomicQuestionsCreate) Exec(ctx context.Context) error {
	_, err := aqc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aqc *AtomicQuestionsCreate) ExecX(ctx context.Context) {
	if err := aqc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aqc *AtomicQuestionsCreate) defaults() {
	if _, ok := aqc.mutation.CreatedAt(); !ok {
		v := atomicquestions.DefaultCreatedAt()
		aqc.mutation.SetCreatedAt(v)
	}
	if _, ok := aqc.mutation.UpdatedAt(); !ok {
		v := atomicquestions.DefaultUpdatedAt()
		aqc.mutation.SetUpdatedAt(v)
	}
	if _, ok := aqc.mutation.Question(); !ok {
		v := atomicquestions.DefaultQuestion
		aqc.mutation.SetQuestion(v)
	}
	if _, ok := aqc.mutation.IsHandle(); !ok {
		v := atomicquestions.DefaultIsHandle
		aqc.mutation.SetIsHandle(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (aqc *AtomicQuestionsCreate) check() error {
	if _, ok := aqc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AtomicQuestions.created_at"`)}
	}
	if _, ok := aqc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AtomicQuestions.updated_at"`)}
	}
	if _, ok := aqc.mutation.EntityTag(); !ok {
		return &ValidationError{Name: "entity_tag", err: errors.New(`ent: missing required field "AtomicQuestions.entity_tag"`)}
	}
	if _, ok := aqc.mutation.PreEntityTag(); !ok {
		return &ValidationError{Name: "pre_entity_tag", err: errors.New(`ent: missing required field "AtomicQuestions.pre_entity_tag"`)}
	}
	if _, ok := aqc.mutation.FileRelationID(); !ok {
		return &ValidationError{Name: "file_relation_id", err: errors.New(`ent: missing required field "AtomicQuestions.file_relation_id"`)}
	}
	if _, ok := aqc.mutation.ChunkIndex(); !ok {
		return &ValidationError{Name: "chunk_index", err: errors.New(`ent: missing required field "AtomicQuestions.chunk_index"`)}
	}
	if _, ok := aqc.mutation.ChunkSize(); !ok {
		return &ValidationError{Name: "chunk_size", err: errors.New(`ent: missing required field "AtomicQuestions.chunk_size"`)}
	}
	if _, ok := aqc.mutation.Index(); !ok {
		return &ValidationError{Name: "index", err: errors.New(`ent: missing required field "AtomicQuestions.index"`)}
	}
	if _, ok := aqc.mutation.Question(); !ok {
		return &ValidationError{Name: "question", err: errors.New(`ent: missing required field "AtomicQuestions.question"`)}
	}
	if _, ok := aqc.mutation.IsHandle(); !ok {
		return &ValidationError{Name: "is_handle", err: errors.New(`ent: missing required field "AtomicQuestions.is_handle"`)}
	}
	return nil
}

func (aqc *AtomicQuestionsCreate) sqlSave(ctx context.Context) (*AtomicQuestions, error) {
	if err := aqc.check(); err != nil {
		return nil, err
	}
	_node, _spec := aqc.createSpec()
	if err := sqlgraph.CreateNode(ctx, aqc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	aqc.mutation.id = &_node.ID
	aqc.mutation.done = true
	return _node, nil
}

func (aqc *AtomicQuestionsCreate) createSpec() (*AtomicQuestions, *sqlgraph.CreateSpec) {
	var (
		_node = &AtomicQuestions{config: aqc.config}
		_spec = sqlgraph.NewCreateSpec(atomicquestions.Table, sqlgraph.NewFieldSpec(atomicquestions.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = aqc.conflict
	if id, ok := aqc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := aqc.mutation.CreatedAt(); ok {
		_spec.SetField(atomicquestions.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := aqc.mutation.UpdatedAt(); ok {
		_spec.SetField(atomicquestions.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := aqc.mutation.EntityTag(); ok {
		_spec.SetField(atomicquestions.FieldEntityTag, field.TypeString, value)
		_node.EntityTag = value
	}
	if value, ok := aqc.mutation.PreEntityTag(); ok {
		_spec.SetField(atomicquestions.FieldPreEntityTag, field.TypeString, value)
		_node.PreEntityTag = value
	}
	if value, ok := aqc.mutation.FileRelationID(); ok {
		_spec.SetField(atomicquestions.FieldFileRelationID, field.TypeInt64, value)
		_node.FileRelationID = value
	}
	if value, ok := aqc.mutation.ChunkIndex(); ok {
		_spec.SetField(atomicquestions.FieldChunkIndex, field.TypeInt64, value)
		_node.ChunkIndex = value
	}
	if value, ok := aqc.mutation.ChunkSize(); ok {
		_spec.SetField(atomicquestions.FieldChunkSize, field.TypeInt64, value)
		_node.ChunkSize = value
	}
	if value, ok := aqc.mutation.Index(); ok {
		_spec.SetField(atomicquestions.FieldIndex, field.TypeInt64, value)
		_node.Index = value
	}
	if value, ok := aqc.mutation.Question(); ok {
		_spec.SetField(atomicquestions.FieldQuestion, field.TypeOther, value)
		_node.Question = value
	}
	if value, ok := aqc.mutation.IsHandle(); ok {
		_spec.SetField(atomicquestions.FieldIsHandle, field.TypeBool, value)
		_node.IsHandle = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AtomicQuestions.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AtomicQuestionsUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (aqc *AtomicQuestionsCreate) OnConflict(opts ...sql.ConflictOption) *AtomicQuestionsUpsertOne {
	aqc.conflict = opts
	return &AtomicQuestionsUpsertOne{
		create: aqc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AtomicQuestions.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aqc *AtomicQuestionsCreate) OnConflictColumns(columns ...string) *AtomicQuestionsUpsertOne {
	aqc.conflict = append(aqc.conflict, sql.ConflictColumns(columns...))
	return &AtomicQuestionsUpsertOne{
		create: aqc,
	}
}

type (
	// AtomicQuestionsUpsertOne is the builder for "upsert"-ing
	//  one AtomicQuestions node.
	AtomicQuestionsUpsertOne struct {
		create *AtomicQuestionsCreate
	}

	// AtomicQuestionsUpsert is the "OnConflict" setter.
	AtomicQuestionsUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *AtomicQuestionsUpsert) SetUpdatedAt(v time.Time) *AtomicQuestionsUpsert {
	u.Set(atomicquestions.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AtomicQuestionsUpsert) UpdateUpdatedAt() *AtomicQuestionsUpsert {
	u.SetExcluded(atomicquestions.FieldUpdatedAt)
	return u
}

// SetEntityTag sets the "entity_tag" field.
func (u *AtomicQuestionsUpsert) SetEntityTag(v string) *AtomicQuestionsUpsert {
	u.Set(atomicquestions.FieldEntityTag, v)
	return u
}

// UpdateEntityTag sets the "entity_tag" field to the value that was provided on create.
func (u *AtomicQuestionsUpsert) UpdateEntityTag() *AtomicQuestionsUpsert {
	u.SetExcluded(atomicquestions.FieldEntityTag)
	return u
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (u *AtomicQuestionsUpsert) SetPreEntityTag(v string) *AtomicQuestionsUpsert {
	u.Set(atomicquestions.FieldPreEntityTag, v)
	return u
}

// UpdatePreEntityTag sets the "pre_entity_tag" field to the value that was provided on create.
func (u *AtomicQuestionsUpsert) UpdatePreEntityTag() *AtomicQuestionsUpsert {
	u.SetExcluded(atomicquestions.FieldPreEntityTag)
	return u
}

// SetFileRelationID sets the "file_relation_id" field.
func (u *AtomicQuestionsUpsert) SetFileRelationID(v int64) *AtomicQuestionsUpsert {
	u.Set(atomicquestions.FieldFileRelationID, v)
	return u
}

// UpdateFileRelationID sets the "file_relation_id" field to the value that was provided on create.
func (u *AtomicQuestionsUpsert) UpdateFileRelationID() *AtomicQuestionsUpsert {
	u.SetExcluded(atomicquestions.FieldFileRelationID)
	return u
}

// AddFileRelationID adds v to the "file_relation_id" field.
func (u *AtomicQuestionsUpsert) AddFileRelationID(v int64) *AtomicQuestionsUpsert {
	u.Add(atomicquestions.FieldFileRelationID, v)
	return u
}

// SetChunkIndex sets the "chunk_index" field.
func (u *AtomicQuestionsUpsert) SetChunkIndex(v int64) *AtomicQuestionsUpsert {
	u.Set(atomicquestions.FieldChunkIndex, v)
	return u
}

// UpdateChunkIndex sets the "chunk_index" field to the value that was provided on create.
func (u *AtomicQuestionsUpsert) UpdateChunkIndex() *AtomicQuestionsUpsert {
	u.SetExcluded(atomicquestions.FieldChunkIndex)
	return u
}

// AddChunkIndex adds v to the "chunk_index" field.
func (u *AtomicQuestionsUpsert) AddChunkIndex(v int64) *AtomicQuestionsUpsert {
	u.Add(atomicquestions.FieldChunkIndex, v)
	return u
}

// SetChunkSize sets the "chunk_size" field.
func (u *AtomicQuestionsUpsert) SetChunkSize(v int64) *AtomicQuestionsUpsert {
	u.Set(atomicquestions.FieldChunkSize, v)
	return u
}

// UpdateChunkSize sets the "chunk_size" field to the value that was provided on create.
func (u *AtomicQuestionsUpsert) UpdateChunkSize() *AtomicQuestionsUpsert {
	u.SetExcluded(atomicquestions.FieldChunkSize)
	return u
}

// AddChunkSize adds v to the "chunk_size" field.
func (u *AtomicQuestionsUpsert) AddChunkSize(v int64) *AtomicQuestionsUpsert {
	u.Add(atomicquestions.FieldChunkSize, v)
	return u
}

// SetIndex sets the "index" field.
func (u *AtomicQuestionsUpsert) SetIndex(v int64) *AtomicQuestionsUpsert {
	u.Set(atomicquestions.FieldIndex, v)
	return u
}

// UpdateIndex sets the "index" field to the value that was provided on create.
func (u *AtomicQuestionsUpsert) UpdateIndex() *AtomicQuestionsUpsert {
	u.SetExcluded(atomicquestions.FieldIndex)
	return u
}

// AddIndex adds v to the "index" field.
func (u *AtomicQuestionsUpsert) AddIndex(v int64) *AtomicQuestionsUpsert {
	u.Add(atomicquestions.FieldIndex, v)
	return u
}

// SetQuestion sets the "question" field.
func (u *AtomicQuestionsUpsert) SetQuestion(v *pq.StringArray) *AtomicQuestionsUpsert {
	u.Set(atomicquestions.FieldQuestion, v)
	return u
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *AtomicQuestionsUpsert) UpdateQuestion() *AtomicQuestionsUpsert {
	u.SetExcluded(atomicquestions.FieldQuestion)
	return u
}

// SetIsHandle sets the "is_handle" field.
func (u *AtomicQuestionsUpsert) SetIsHandle(v bool) *AtomicQuestionsUpsert {
	u.Set(atomicquestions.FieldIsHandle, v)
	return u
}

// UpdateIsHandle sets the "is_handle" field to the value that was provided on create.
func (u *AtomicQuestionsUpsert) UpdateIsHandle() *AtomicQuestionsUpsert {
	u.SetExcluded(atomicquestions.FieldIsHandle)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AtomicQuestions.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(atomicquestions.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AtomicQuestionsUpsertOne) UpdateNewValues() *AtomicQuestionsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(atomicquestions.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(atomicquestions.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AtomicQuestions.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AtomicQuestionsUpsertOne) Ignore() *AtomicQuestionsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AtomicQuestionsUpsertOne) DoNothing() *AtomicQuestionsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AtomicQuestionsCreate.OnConflict
// documentation for more info.
func (u *AtomicQuestionsUpsertOne) Update(set func(*AtomicQuestionsUpsert)) *AtomicQuestionsUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AtomicQuestionsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AtomicQuestionsUpsertOne) SetUpdatedAt(v time.Time) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertOne) UpdateUpdatedAt() *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetEntityTag sets the "entity_tag" field.
func (u *AtomicQuestionsUpsertOne) SetEntityTag(v string) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetEntityTag(v)
	})
}

// UpdateEntityTag sets the "entity_tag" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertOne) UpdateEntityTag() *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateEntityTag()
	})
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (u *AtomicQuestionsUpsertOne) SetPreEntityTag(v string) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetPreEntityTag(v)
	})
}

// UpdatePreEntityTag sets the "pre_entity_tag" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertOne) UpdatePreEntityTag() *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdatePreEntityTag()
	})
}

// SetFileRelationID sets the "file_relation_id" field.
func (u *AtomicQuestionsUpsertOne) SetFileRelationID(v int64) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetFileRelationID(v)
	})
}

// AddFileRelationID adds v to the "file_relation_id" field.
func (u *AtomicQuestionsUpsertOne) AddFileRelationID(v int64) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.AddFileRelationID(v)
	})
}

// UpdateFileRelationID sets the "file_relation_id" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertOne) UpdateFileRelationID() *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateFileRelationID()
	})
}

// SetChunkIndex sets the "chunk_index" field.
func (u *AtomicQuestionsUpsertOne) SetChunkIndex(v int64) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetChunkIndex(v)
	})
}

// AddChunkIndex adds v to the "chunk_index" field.
func (u *AtomicQuestionsUpsertOne) AddChunkIndex(v int64) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.AddChunkIndex(v)
	})
}

// UpdateChunkIndex sets the "chunk_index" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertOne) UpdateChunkIndex() *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateChunkIndex()
	})
}

// SetChunkSize sets the "chunk_size" field.
func (u *AtomicQuestionsUpsertOne) SetChunkSize(v int64) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetChunkSize(v)
	})
}

// AddChunkSize adds v to the "chunk_size" field.
func (u *AtomicQuestionsUpsertOne) AddChunkSize(v int64) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.AddChunkSize(v)
	})
}

// UpdateChunkSize sets the "chunk_size" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertOne) UpdateChunkSize() *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateChunkSize()
	})
}

// SetIndex sets the "index" field.
func (u *AtomicQuestionsUpsertOne) SetIndex(v int64) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetIndex(v)
	})
}

// AddIndex adds v to the "index" field.
func (u *AtomicQuestionsUpsertOne) AddIndex(v int64) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.AddIndex(v)
	})
}

// UpdateIndex sets the "index" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertOne) UpdateIndex() *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateIndex()
	})
}

// SetQuestion sets the "question" field.
func (u *AtomicQuestionsUpsertOne) SetQuestion(v *pq.StringArray) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetQuestion(v)
	})
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertOne) UpdateQuestion() *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateQuestion()
	})
}

// SetIsHandle sets the "is_handle" field.
func (u *AtomicQuestionsUpsertOne) SetIsHandle(v bool) *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetIsHandle(v)
	})
}

// UpdateIsHandle sets the "is_handle" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertOne) UpdateIsHandle() *AtomicQuestionsUpsertOne {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateIsHandle()
	})
}

// Exec executes the query.
func (u *AtomicQuestionsUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AtomicQuestionsCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AtomicQuestionsUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AtomicQuestionsUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AtomicQuestionsUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AtomicQuestionsCreateBulk is the builder for creating many AtomicQuestions entities in bulk.
type AtomicQuestionsCreateBulk struct {
	config
	err      error
	builders []*AtomicQuestionsCreate
	conflict []sql.ConflictOption
}

// Save creates the AtomicQuestions entities in the database.
func (aqcb *AtomicQuestionsCreateBulk) Save(ctx context.Context) ([]*AtomicQuestions, error) {
	if aqcb.err != nil {
		return nil, aqcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(aqcb.builders))
	nodes := make([]*AtomicQuestions, len(aqcb.builders))
	mutators := make([]Mutator, len(aqcb.builders))
	for i := range aqcb.builders {
		func(i int, root context.Context) {
			builder := aqcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AtomicQuestionsMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, aqcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = aqcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, aqcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, aqcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (aqcb *AtomicQuestionsCreateBulk) SaveX(ctx context.Context) []*AtomicQuestions {
	v, err := aqcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aqcb *AtomicQuestionsCreateBulk) Exec(ctx context.Context) error {
	_, err := aqcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aqcb *AtomicQuestionsCreateBulk) ExecX(ctx context.Context) {
	if err := aqcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AtomicQuestions.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AtomicQuestionsUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (aqcb *AtomicQuestionsCreateBulk) OnConflict(opts ...sql.ConflictOption) *AtomicQuestionsUpsertBulk {
	aqcb.conflict = opts
	return &AtomicQuestionsUpsertBulk{
		create: aqcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AtomicQuestions.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (aqcb *AtomicQuestionsCreateBulk) OnConflictColumns(columns ...string) *AtomicQuestionsUpsertBulk {
	aqcb.conflict = append(aqcb.conflict, sql.ConflictColumns(columns...))
	return &AtomicQuestionsUpsertBulk{
		create: aqcb,
	}
}

// AtomicQuestionsUpsertBulk is the builder for "upsert"-ing
// a bulk of AtomicQuestions nodes.
type AtomicQuestionsUpsertBulk struct {
	create *AtomicQuestionsCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AtomicQuestions.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(atomicquestions.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AtomicQuestionsUpsertBulk) UpdateNewValues() *AtomicQuestionsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(atomicquestions.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(atomicquestions.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AtomicQuestions.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AtomicQuestionsUpsertBulk) Ignore() *AtomicQuestionsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AtomicQuestionsUpsertBulk) DoNothing() *AtomicQuestionsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AtomicQuestionsCreateBulk.OnConflict
// documentation for more info.
func (u *AtomicQuestionsUpsertBulk) Update(set func(*AtomicQuestionsUpsert)) *AtomicQuestionsUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AtomicQuestionsUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AtomicQuestionsUpsertBulk) SetUpdatedAt(v time.Time) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertBulk) UpdateUpdatedAt() *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetEntityTag sets the "entity_tag" field.
func (u *AtomicQuestionsUpsertBulk) SetEntityTag(v string) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetEntityTag(v)
	})
}

// UpdateEntityTag sets the "entity_tag" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertBulk) UpdateEntityTag() *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateEntityTag()
	})
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (u *AtomicQuestionsUpsertBulk) SetPreEntityTag(v string) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetPreEntityTag(v)
	})
}

// UpdatePreEntityTag sets the "pre_entity_tag" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertBulk) UpdatePreEntityTag() *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdatePreEntityTag()
	})
}

// SetFileRelationID sets the "file_relation_id" field.
func (u *AtomicQuestionsUpsertBulk) SetFileRelationID(v int64) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetFileRelationID(v)
	})
}

// AddFileRelationID adds v to the "file_relation_id" field.
func (u *AtomicQuestionsUpsertBulk) AddFileRelationID(v int64) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.AddFileRelationID(v)
	})
}

// UpdateFileRelationID sets the "file_relation_id" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertBulk) UpdateFileRelationID() *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateFileRelationID()
	})
}

// SetChunkIndex sets the "chunk_index" field.
func (u *AtomicQuestionsUpsertBulk) SetChunkIndex(v int64) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetChunkIndex(v)
	})
}

// AddChunkIndex adds v to the "chunk_index" field.
func (u *AtomicQuestionsUpsertBulk) AddChunkIndex(v int64) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.AddChunkIndex(v)
	})
}

// UpdateChunkIndex sets the "chunk_index" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertBulk) UpdateChunkIndex() *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateChunkIndex()
	})
}

// SetChunkSize sets the "chunk_size" field.
func (u *AtomicQuestionsUpsertBulk) SetChunkSize(v int64) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetChunkSize(v)
	})
}

// AddChunkSize adds v to the "chunk_size" field.
func (u *AtomicQuestionsUpsertBulk) AddChunkSize(v int64) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.AddChunkSize(v)
	})
}

// UpdateChunkSize sets the "chunk_size" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertBulk) UpdateChunkSize() *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateChunkSize()
	})
}

// SetIndex sets the "index" field.
func (u *AtomicQuestionsUpsertBulk) SetIndex(v int64) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetIndex(v)
	})
}

// AddIndex adds v to the "index" field.
func (u *AtomicQuestionsUpsertBulk) AddIndex(v int64) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.AddIndex(v)
	})
}

// UpdateIndex sets the "index" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertBulk) UpdateIndex() *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateIndex()
	})
}

// SetQuestion sets the "question" field.
func (u *AtomicQuestionsUpsertBulk) SetQuestion(v *pq.StringArray) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetQuestion(v)
	})
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertBulk) UpdateQuestion() *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateQuestion()
	})
}

// SetIsHandle sets the "is_handle" field.
func (u *AtomicQuestionsUpsertBulk) SetIsHandle(v bool) *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.SetIsHandle(v)
	})
}

// UpdateIsHandle sets the "is_handle" field to the value that was provided on create.
func (u *AtomicQuestionsUpsertBulk) UpdateIsHandle() *AtomicQuestionsUpsertBulk {
	return u.Update(func(s *AtomicQuestionsUpsert) {
		s.UpdateIsHandle()
	})
}

// Exec executes the query.
func (u *AtomicQuestionsUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AtomicQuestionsCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AtomicQuestionsCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AtomicQuestionsUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/externalmodelusage"
)

// ExternalModelUsage is the model entity for the ExternalModelUsage schema.
type ExternalModelUsage struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 模型名称
	ModelName string `json:"model_name,omitempty"`
	// 问题
	Question string `json:"question,omitempty"`
	// 问题分类
	QuestionTag string `json:"question_tag,omitempty"`
	// 上传的文件
	Files string `json:"files,omitempty"`
	// 文件类型
	MimeTypes *pq.StringArray `json:"mime_types,omitempty"`
	// 用户ID
	UserID int64 `json:"user_id,omitempty"`
	// 用户名称
	UserName string `json:"user_name,omitempty"`
	// 部门ID
	DeptID int64 `json:"dept_id,omitempty"`
	// 部门名称
	DeptName string `json:"dept_name,omitempty"`
	// 主机名称
	PcName string `json:"pc_name,omitempty"`
	// 发生时间
	HappenedAt   time.Time `json:"happened_at,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*ExternalModelUsage) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case externalmodelusage.FieldMimeTypes:
			values[i] = new(pq.StringArray)
		case externalmodelusage.FieldID, externalmodelusage.FieldUserID, externalmodelusage.FieldDeptID:
			values[i] = new(sql.NullInt64)
		case externalmodelusage.FieldModelName, externalmodelusage.FieldQuestion, externalmodelusage.FieldQuestionTag, externalmodelusage.FieldFiles, externalmodelusage.FieldUserName, externalmodelusage.FieldDeptName, externalmodelusage.FieldPcName:
			values[i] = new(sql.NullString)
		case externalmodelusage.FieldCreatedAt, externalmodelusage.FieldUpdatedAt, externalmodelusage.FieldDeletedAt, externalmodelusage.FieldHappenedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the ExternalModelUsage fields.
func (emu *ExternalModelUsage) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case externalmodelusage.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			emu.ID = int64(value.Int64)
		case externalmodelusage.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				emu.CreatedAt = value.Time
			}
		case externalmodelusage.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				emu.UpdatedAt = value.Time
			}
		case externalmodelusage.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				emu.DeletedAt = value.Time
			}
		case externalmodelusage.FieldModelName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field model_name", values[i])
			} else if value.Valid {
				emu.ModelName = value.String
			}
		case externalmodelusage.FieldQuestion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field question", values[i])
			} else if value.Valid {
				emu.Question = value.String
			}
		case externalmodelusage.FieldQuestionTag:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field question_tag", values[i])
			} else if value.Valid {
				emu.QuestionTag = value.String
			}
		case externalmodelusage.FieldFiles:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field files", values[i])
			} else if value.Valid {
				emu.Files = value.String
			}
		case externalmodelusage.FieldMimeTypes:
			if value, ok := values[i].(*pq.StringArray); !ok {
				return fmt.Errorf("unexpected type %T for field mime_types", values[i])
			} else if value != nil {
				emu.MimeTypes = value
			}
		case externalmodelusage.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				emu.UserID = value.Int64
			}
		case externalmodelusage.FieldUserName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_name", values[i])
			} else if value.Valid {
				emu.UserName = value.String
			}
		case externalmodelusage.FieldDeptID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field dept_id", values[i])
			} else if value.Valid {
				emu.DeptID = value.Int64
			}
		case externalmodelusage.FieldDeptName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field dept_name", values[i])
			} else if value.Valid {
				emu.DeptName = value.String
			}
		case externalmodelusage.FieldPcName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field pc_name", values[i])
			} else if value.Valid {
				emu.PcName = value.String
			}
		case externalmodelusage.FieldHappenedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field happened_at", values[i])
			} else if value.Valid {
				emu.HappenedAt = value.Time
			}
		default:
			emu.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the ExternalModelUsage.
// This includes values selected through modifiers, order, etc.
func (emu *ExternalModelUsage) Value(name string) (ent.Value, error) {
	return emu.selectValues.Get(name)
}

// Update returns a builder for updating this ExternalModelUsage.
// Note that you need to call ExternalModelUsage.Unwrap() before calling this method if this ExternalModelUsage
// was returned from a transaction, and the transaction was committed or rolled back.
func (emu *ExternalModelUsage) Update() *ExternalModelUsageUpdateOne {
	return NewExternalModelUsageClient(emu.config).UpdateOne(emu)
}

// Unwrap unwraps the ExternalModelUsage entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (emu *ExternalModelUsage) Unwrap() *ExternalModelUsage {
	_tx, ok := emu.config.driver.(*txDriver)
	if !ok {
		panic("ent: ExternalModelUsage is not a transactional entity")
	}
	emu.config.driver = _tx.drv
	return emu
}

// String implements the fmt.Stringer.
func (emu *ExternalModelUsage) String() string {
	var builder strings.Builder
	builder.WriteString("ExternalModelUsage(")
	builder.WriteString(fmt.Sprintf("id=%v, ", emu.ID))
	builder.WriteString("created_at=")
	builder.WriteString(emu.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(emu.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(emu.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("model_name=")
	builder.WriteString(emu.ModelName)
	builder.WriteString(", ")
	builder.WriteString("question=")
	builder.WriteString(emu.Question)
	builder.WriteString(", ")
	builder.WriteString("question_tag=")
	builder.WriteString(emu.QuestionTag)
	builder.WriteString(", ")
	builder.WriteString("files=")
	builder.WriteString(emu.Files)
	builder.WriteString(", ")
	builder.WriteString("mime_types=")
	builder.WriteString(fmt.Sprintf("%v", emu.MimeTypes))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", emu.UserID))
	builder.WriteString(", ")
	builder.WriteString("user_name=")
	builder.WriteString(emu.UserName)
	builder.WriteString(", ")
	builder.WriteString("dept_id=")
	builder.WriteString(fmt.Sprintf("%v", emu.DeptID))
	builder.WriteString(", ")
	builder.WriteString("dept_name=")
	builder.WriteString(emu.DeptName)
	builder.WriteString(", ")
	builder.WriteString("pc_name=")
	builder.WriteString(emu.PcName)
	builder.WriteString(", ")
	builder.WriteString("happened_at=")
	builder.WriteString(emu.HappenedAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// ExternalModelUsages is a parsable slice of ExternalModelUsage.
type ExternalModelUsages []*ExternalModelUsage

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodeldetail"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiModelDetailDelete is the builder for deleting a AiModelDetail entity.
type AiModelDetailDelete struct {
	config
	hooks    []Hook
	mutation *AiModelDetailMutation
}

// Where appends a list predicates to the AiModelDetailDelete builder.
func (amdd *AiModelDetailDelete) Where(ps ...predicate.AiModelDetail) *AiModelDetailDelete {
	amdd.mutation.Where(ps...)
	return amdd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (amdd *AiModelDetailDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, amdd.sqlExec, amdd.mutation, amdd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (amdd *AiModelDetailDelete) ExecX(ctx context.Context) int {
	n, err := amdd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (amdd *AiModelDetailDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(aimodeldetail.Table, sqlgraph.NewFieldSpec(aimodeldetail.FieldID, field.TypeInt64))
	if ps := amdd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, amdd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	amdd.mutation.done = true
	return affected, err
}

// AiModelDetailDeleteOne is the builder for deleting a single AiModelDetail entity.
type AiModelDetailDeleteOne struct {
	amdd *AiModelDetailDelete
}

// Where appends a list predicates to the AiModelDetailDelete builder.
func (amddo *AiModelDetailDeleteOne) Where(ps ...predicate.AiModelDetail) *AiModelDetailDeleteOne {
	amddo.amdd.mutation.Where(ps...)
	return amddo
}

// Exec executes the deletion query.
func (amddo *AiModelDetailDeleteOne) Exec(ctx context.Context) error {
	n, err := amddo.amdd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{aimodeldetail.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (amddo *AiModelDetailDeleteOne) ExecX(ctx context.Context) {
	if err := amddo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiAgentUpdate is the builder for updating AiAgent entities.
type AiAgentUpdate struct {
	config
	hooks     []Hook
	mutation  *AiAgentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AiAgentUpdate builder.
func (aau *AiAgentUpdate) Where(ps ...predicate.AiAgent) *AiAgentUpdate {
	aau.mutation.Where(ps...)
	return aau
}

// SetUpdatedAt sets the "updated_at" field.
func (aau *AiAgentUpdate) SetUpdatedAt(t time.Time) *AiAgentUpdate {
	aau.mutation.SetUpdatedAt(t)
	return aau
}

// SetDeletedAt sets the "deleted_at" field.
func (aau *AiAgentUpdate) SetDeletedAt(t time.Time) *AiAgentUpdate {
	aau.mutation.SetDeletedAt(t)
	return aau
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableDeletedAt(t *time.Time) *AiAgentUpdate {
	if t != nil {
		aau.SetDeletedAt(*t)
	}
	return aau
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (aau *AiAgentUpdate) ClearDeletedAt() *AiAgentUpdate {
	aau.mutation.ClearDeletedAt()
	return aau
}

// SetName sets the "name" field.
func (aau *AiAgentUpdate) SetName(s string) *AiAgentUpdate {
	aau.mutation.SetName(s)
	return aau
}

// SetNillableName sets the "name" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableName(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetName(*s)
	}
	return aau
}

// SetDescription sets the "description" field.
func (aau *AiAgentUpdate) SetDescription(s string) *AiAgentUpdate {
	aau.mutation.SetDescription(s)
	return aau
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableDescription(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetDescription(*s)
	}
	return aau
}

// SetAvatar sets the "avatar" field.
func (aau *AiAgentUpdate) SetAvatar(s string) *AiAgentUpdate {
	aau.mutation.SetAvatar(s)
	return aau
}

// SetNillableAvatar sets the "avatar" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableAvatar(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetAvatar(*s)
	}
	return aau
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (aau *AiAgentUpdate) SetClickedAvatar(s string) *AiAgentUpdate {
	aau.mutation.SetClickedAvatar(s)
	return aau
}

// SetNillableClickedAvatar sets the "clicked_avatar" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableClickedAvatar(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetClickedAvatar(*s)
	}
	return aau
}

// SetWelcomeMsg sets the "welcome_msg" field.
func (aau *AiAgentUpdate) SetWelcomeMsg(s string) *AiAgentUpdate {
	aau.mutation.SetWelcomeMsg(s)
	return aau
}

// SetNillableWelcomeMsg sets the "welcome_msg" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableWelcomeMsg(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetWelcomeMsg(*s)
	}
	return aau
}

// SetFallbackMsg sets the "fallback_msg" field.
func (aau *AiAgentUpdate) SetFallbackMsg(s string) *AiAgentUpdate {
	aau.mutation.SetFallbackMsg(s)
	return aau
}

// SetNillableFallbackMsg sets the "fallback_msg" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableFallbackMsg(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetFallbackMsg(*s)
	}
	return aau
}

// SetOwnerID sets the "owner_id" field.
func (aau *AiAgentUpdate) SetOwnerID(i int64) *AiAgentUpdate {
	aau.mutation.ResetOwnerID()
	aau.mutation.SetOwnerID(i)
	return aau
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableOwnerID(i *int64) *AiAgentUpdate {
	if i != nil {
		aau.SetOwnerID(*i)
	}
	return aau
}

// AddOwnerID adds i to the "owner_id" field.
func (aau *AiAgentUpdate) AddOwnerID(i int64) *AiAgentUpdate {
	aau.mutation.AddOwnerID(i)
	return aau
}

// SetVisibilityType sets the "visibility_type" field.
func (aau *AiAgentUpdate) SetVisibilityType(i int8) *AiAgentUpdate {
	aau.mutation.ResetVisibilityType()
	aau.mutation.SetVisibilityType(i)
	return aau
}

// SetNillableVisibilityType sets the "visibility_type" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableVisibilityType(i *int8) *AiAgentUpdate {
	if i != nil {
		aau.SetVisibilityType(*i)
	}
	return aau
}

// AddVisibilityType adds i to the "visibility_type" field.
func (aau *AiAgentUpdate) AddVisibilityType(i int8) *AiAgentUpdate {
	aau.mutation.AddVisibilityType(i)
	return aau
}

// SetManageableToUser sets the "manageable_to_user" field.
func (aau *AiAgentUpdate) SetManageableToUser(pq *pq.Int64Array) *AiAgentUpdate {
	aau.mutation.SetManageableToUser(pq)
	return aau
}

// SetVisibleToUser sets the "visible_to_user" field.
func (aau *AiAgentUpdate) SetVisibleToUser(pq *pq.Int64Array) *AiAgentUpdate {
	aau.mutation.SetVisibleToUser(pq)
	return aau
}

// SetVisibleToDept sets the "visible_to_dept" field.
func (aau *AiAgentUpdate) SetVisibleToDept(pq *pq.Int64Array) *AiAgentUpdate {
	aau.mutation.SetVisibleToDept(pq)
	return aau
}

// SetKnowledgeBaseIds sets the "knowledge_base_ids" field.
func (aau *AiAgentUpdate) SetKnowledgeBaseIds(pq *pq.Int64Array) *AiAgentUpdate {
	aau.mutation.SetKnowledgeBaseIds(pq)
	return aau
}

// SetSchema sets the "schema" field.
func (aau *AiAgentUpdate) SetSchema(s string) *AiAgentUpdate {
	aau.mutation.SetSchema(s)
	return aau
}

// SetNillableSchema sets the "schema" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableSchema(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetSchema(*s)
	}
	return aau
}

// SetIsPublic sets the "is_public" field.
func (aau *AiAgentUpdate) SetIsPublic(b bool) *AiAgentUpdate {
	aau.mutation.SetIsPublic(b)
	return aau
}

// SetNillableIsPublic sets the "is_public" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableIsPublic(b *bool) *AiAgentUpdate {
	if b != nil {
		aau.SetIsPublic(*b)
	}
	return aau
}

// SetIsEnabled sets the "is_enabled" field.
func (aau *AiAgentUpdate) SetIsEnabled(b bool) *AiAgentUpdate {
	aau.mutation.SetIsEnabled(b)
	return aau
}

// SetNillableIsEnabled sets the "is_enabled" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableIsEnabled(b *bool) *AiAgentUpdate {
	if b != nil {
		aau.SetIsEnabled(*b)
	}
	return aau
}

// SetIsRefFiles sets the "is_ref_files" field.
func (aau *AiAgentUpdate) SetIsRefFiles(b bool) *AiAgentUpdate {
	aau.mutation.SetIsRefFiles(b)
	return aau
}

// SetNillableIsRefFiles sets the "is_ref_files" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableIsRefFiles(b *bool) *AiAgentUpdate {
	if b != nil {
		aau.SetIsRefFiles(*b)
	}
	return aau
}

// SetModelType sets the "model_type" field.
func (aau *AiAgentUpdate) SetModelType(i int64) *AiAgentUpdate {
	aau.mutation.ResetModelType()
	aau.mutation.SetModelType(i)
	return aau
}

// SetNillableModelType sets the "model_type" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableModelType(i *int64) *AiAgentUpdate {
	if i != nil {
		aau.SetModelType(*i)
	}
	return aau
}

// AddModelType adds i to the "model_type" field.
func (aau *AiAgentUpdate) AddModelType(i int64) *AiAgentUpdate {
	aau.mutation.AddModelType(i)
	return aau
}

// SetModelID sets the "model_id" field.
func (aau *AiAgentUpdate) SetModelID(i int64) *AiAgentUpdate {
	aau.mutation.ResetModelID()
	aau.mutation.SetModelID(i)
	return aau
}

// SetNillableModelID sets the "model_id" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableModelID(i *int64) *AiAgentUpdate {
	if i != nil {
		aau.SetModelID(*i)
	}
	return aau
}

// AddModelID adds i to the "model_id" field.
func (aau *AiAgentUpdate) AddModelID(i int64) *AiAgentUpdate {
	aau.mutation.AddModelID(i)
	return aau
}

// SetUseCount sets the "use_count" field.
func (aau *AiAgentUpdate) SetUseCount(i int64) *AiAgentUpdate {
	aau.mutation.ResetUseCount()
	aau.mutation.SetUseCount(i)
	return aau
}

// SetNillableUseCount sets the "use_count" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableUseCount(i *int64) *AiAgentUpdate {
	if i != nil {
		aau.SetUseCount(*i)
	}
	return aau
}

// AddUseCount adds i to the "use_count" field.
func (aau *AiAgentUpdate) AddUseCount(i int64) *AiAgentUpdate {
	aau.mutation.AddUseCount(i)
	return aau
}

// SetKnowledgeBaseType sets the "knowledge_base_type" field.
func (aau *AiAgentUpdate) SetKnowledgeBaseType(i int64) *AiAgentUpdate {
	aau.mutation.ResetKnowledgeBaseType()
	aau.mutation.SetKnowledgeBaseType(i)
	return aau
}

// SetNillableKnowledgeBaseType sets the "knowledge_base_type" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableKnowledgeBaseType(i *int64) *AiAgentUpdate {
	if i != nil {
		aau.SetKnowledgeBaseType(*i)
	}
	return aau
}

// AddKnowledgeBaseType adds i to the "knowledge_base_type" field.
func (aau *AiAgentUpdate) AddKnowledgeBaseType(i int64) *AiAgentUpdate {
	aau.mutation.AddKnowledgeBaseType(i)
	return aau
}

// SetInternetSearch sets the "internet_search" field.
func (aau *AiAgentUpdate) SetInternetSearch(b bool) *AiAgentUpdate {
	aau.mutation.SetInternetSearch(b)
	return aau
}

// SetNillableInternetSearch sets the "internet_search" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableInternetSearch(b *bool) *AiAgentUpdate {
	if b != nil {
		aau.SetInternetSearch(*b)
	}
	return aau
}

// SetAgentType sets the "agent_type" field.
func (aau *AiAgentUpdate) SetAgentType(i int64) *AiAgentUpdate {
	aau.mutation.ResetAgentType()
	aau.mutation.SetAgentType(i)
	return aau
}

// SetNillableAgentType sets the "agent_type" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableAgentType(i *int64) *AiAgentUpdate {
	if i != nil {
		aau.SetAgentType(*i)
	}
	return aau
}

// AddAgentType adds i to the "agent_type" field.
func (aau *AiAgentUpdate) AddAgentType(i int64) *AiAgentUpdate {
	aau.mutation.AddAgentType(i)
	return aau
}

// SetThinking sets the "thinking" field.
func (aau *AiAgentUpdate) SetThinking(b bool) *AiAgentUpdate {
	aau.mutation.SetThinking(b)
	return aau
}

// SetNillableThinking sets the "thinking" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableThinking(b *bool) *AiAgentUpdate {
	if b != nil {
		aau.SetThinking(*b)
	}
	return aau
}

// SetThinkingModelID sets the "thinking_model_id" field.
func (aau *AiAgentUpdate) SetThinkingModelID(i int64) *AiAgentUpdate {
	aau.mutation.ResetThinkingModelID()
	aau.mutation.SetThinkingModelID(i)
	return aau
}

// SetNillableThinkingModelID sets the "thinking_model_id" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableThinkingModelID(i *int64) *AiAgentUpdate {
	if i != nil {
		aau.SetThinkingModelID(*i)
	}
	return aau
}

// AddThinkingModelID adds i to the "thinking_model_id" field.
func (aau *AiAgentUpdate) AddThinkingModelID(i int64) *AiAgentUpdate {
	aau.mutation.AddThinkingModelID(i)
	return aau
}

// SetRoleSetting sets the "role_setting" field.
func (aau *AiAgentUpdate) SetRoleSetting(s string) *AiAgentUpdate {
	aau.mutation.SetRoleSetting(s)
	return aau
}

// SetNillableRoleSetting sets the "role_setting" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableRoleSetting(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetRoleSetting(*s)
	}
	return aau
}

// SetUploadFile sets the "upload_file" field.
func (aau *AiAgentUpdate) SetUploadFile(b bool) *AiAgentUpdate {
	aau.mutation.SetUploadFile(b)
	return aau
}

// SetNillableUploadFile sets the "upload_file" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableUploadFile(b *bool) *AiAgentUpdate {
	if b != nil {
		aau.SetUploadFile(*b)
	}
	return aau
}

// SetSemanticCache sets the "semantic_cache" field.
func (aau *AiAgentUpdate) SetSemanticCache(b bool) *AiAgentUpdate {
	aau.mutation.SetSemanticCache(b)
	return aau
}

// SetNillableSemanticCache sets the "semantic_cache" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableSemanticCache(b *bool) *AiAgentUpdate {
	if b != nil {
		aau.SetSemanticCache(*b)
	}
	return aau
}

// AddAiChatIDs adds the "ai_chat" edge to the AiChat entity by IDs.
func (aau *AiAgentUpdate) AddAiChatIDs(ids ...int64) *AiAgentUpdate {
	aau.mutation.AddAiChatIDs(ids...)
	return aau
}

// AddAiChat adds the "ai_chat" edges to the AiChat entity.
func (aau *AiAgentUpdate) AddAiChat(a ...*AiChat) *AiAgentUpdate {
	ids := make([]int64, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return aau.AddAiChatIDs(ids...)
}

// Mutation returns the AiAgentMutation object of the builder.
func (aau *AiAgentUpdate) Mutation() *AiAgentMutation {
	return aau.mutation
}

// ClearAiChat clears all "ai_chat" edges to the AiChat entity.
func (aau *AiAgentUpdate) ClearAiChat() *AiAgentUpdate {
	aau.mutation.ClearAiChat()
	return aau
}

// RemoveAiChatIDs removes the "ai_chat" edge to AiChat entities by IDs.
func (aau *AiAgentUpdate) RemoveAiChatIDs(ids ...int64) *AiAgentUpdate {
	aau.mutation.RemoveAiChatIDs(ids...)
	return aau
}

// RemoveAiChat removes "ai_chat" edges to AiChat entities.
func (aau *AiAgentUpdate) RemoveAiChat(a ...*AiChat) *AiAgentUpdate {
	ids := make([]int64, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return aau.RemoveAiChatIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (aau *AiAgentUpdate) Save(ctx context.Context) (int, error) {
	if err := aau.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, aau.sqlSave, aau.mutation, aau.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aau *AiAgentUpdate) SaveX(ctx context.Context) int {
	affected, err := aau.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (aau *AiAgentUpdate) Exec(ctx context.Context) error {
	_, err := aau.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aau *AiAgentUpdate) ExecX(ctx context.Context) {
	if err := aau.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aau *AiAgentUpdate) defaults() error {
	if _, ok := aau.mutation.UpdatedAt(); !ok {
		if aiagent.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagent.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aiagent.UpdateDefaultUpdatedAt()
		aau.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aau *AiAgentUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiAgentUpdate {
	aau.modifiers = append(aau.modifiers, modifiers...)
	return aau
}

func (aau *AiAgentUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(aiagent.Table, aiagent.Columns, sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt64))
	if ps := aau.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aau.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagent.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aau.mutation.DeletedAt(); ok {
		_spec.SetField(aiagent.FieldDeletedAt, field.TypeTime, value)
	}
	if aau.mutation.DeletedAtCleared() {
		_spec.ClearField(aiagent.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := aau.mutation.Name(); ok {
		_spec.SetField(aiagent.FieldName, field.TypeString, value)
	}
	if value, ok := aau.mutation.Description(); ok {
		_spec.SetField(aiagent.FieldDescription, field.TypeString, value)
	}
	if value, ok := aau.mutation.Avatar(); ok {
		_spec.SetField(aiagent.FieldAvatar, field.TypeString, value)
	}
	if value, ok := aau.mutation.ClickedAvatar(); ok {
		_spec.SetField(aiagent.FieldClickedAvatar, field.TypeString, value)
	}
	if value, ok := aau.mutation.WelcomeMsg(); ok {
		_spec.SetField(aiagent.FieldWelcomeMsg, field.TypeString, value)
	}
	if value, ok := aau.mutation.FallbackMsg(); ok {
		_spec.SetField(aiagent.FieldFallbackMsg, field.TypeString, value)
	}
	if value, ok := aau.mutation.OwnerID(); ok {
		_spec.SetField(aiagent.FieldOwnerID, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.AddedOwnerID(); ok {
		_spec.AddField(aiagent.FieldOwnerID, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.VisibilityType(); ok {
		_spec.SetField(aiagent.FieldVisibilityType, field.TypeInt8, value)
	}
	if value, ok := aau.mutation.AddedVisibilityType(); ok {
		_spec.AddField(aiagent.FieldVisibilityType, field.TypeInt8, value)
	}
	if value, ok := aau.mutation.ManageableToUser(); ok {
		_spec.SetField(aiagent.FieldManageableToUser, field.TypeOther, value)
	}
	if value, ok := aau.mutation.VisibleToUser(); ok {
		_spec.SetField(aiagent.FieldVisibleToUser, field.TypeOther, value)
	}
	if value, ok := aau.mutation.VisibleToDept(); ok {
		_spec.SetField(aiagent.FieldVisibleToDept, field.TypeOther, value)
	}
	if value, ok := aau.mutation.KnowledgeBaseIds(); ok {
		_spec.SetField(aiagent.FieldKnowledgeBaseIds, field.TypeOther, value)
	}
	if value, ok := aau.mutation.Schema(); ok {
		_spec.SetField(aiagent.FieldSchema, field.TypeString, value)
	}
	if value, ok := aau.mutation.IsPublic(); ok {
		_spec.SetField(aiagent.FieldIsPublic, field.TypeBool, value)
	}
	if value, ok := aau.mutation.IsEnabled(); ok {
		_spec.SetField(aiagent.FieldIsEnabled, field.TypeBool, value)
	}
	if value, ok := aau.mutation.IsRefFiles(); ok {
		_spec.SetField(aiagent.FieldIsRefFiles, field.TypeBool, value)
	}
	if value, ok := aau.mutation.ModelType(); ok {
		_spec.SetField(aiagent.FieldModelType, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.AddedModelType(); ok {
		_spec.AddField(aiagent.FieldModelType, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.ModelID(); ok {
		_spec.SetField(aiagent.FieldModelID, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.AddedModelID(); ok {
		_spec.AddField(aiagent.FieldModelID, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.UseCount(); ok {
		_spec.SetField(aiagent.FieldUseCount, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.AddedUseCount(); ok {
		_spec.AddField(aiagent.FieldUseCount, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.KnowledgeBaseType(); ok {
		_spec.SetField(aiagent.FieldKnowledgeBaseType, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.AddedKnowledgeBaseType(); ok {
		_spec.AddField(aiagent.FieldKnowledgeBaseType, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.InternetSearch(); ok {
		_spec.SetField(aiagent.FieldInternetSearch, field.TypeBool, value)
	}
	if value, ok := aau.mutation.AgentType(); ok {
		_spec.SetField(aiagent.FieldAgentType, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.AddedAgentType(); ok {
		_spec.AddField(aiagent.FieldAgentType, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.Thinking(); ok {
		_spec.SetField(aiagent.FieldThinking, field.TypeBool, value)
	}
	if value, ok := aau.mutation.ThinkingModelID(); ok {
		_spec.SetField(aiagent.FieldThinkingModelID, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.AddedThinkingModelID(); ok {
		_spec.AddField(aiagent.FieldThinkingModelID, field.TypeInt64, value)
	}
	if value, ok := aau.mutation.RoleSetting(); ok {
		_spec.SetField(aiagent.FieldRoleSetting, field.TypeString, value)
	}
	if value, ok := aau.mutation.UploadFile(); ok {
		_spec.SetField(aiagent.FieldUploadFile, field.TypeBool, value)
	}
	if value, ok := aau.mutation.SemanticCache(); ok {
		_spec.SetField(aiagent.FieldSemanticCache, field.TypeBool, value)
	}
	if aau.mutation.AiChatCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.AiChatTable,
			Columns: []string{aiagent.AiChatColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := aau.mutation.RemovedAiChatIDs(); len(nodes) > 0 && !aau.mutation.AiChatCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.AiChatTable,
			Columns: []string{aiagent.AiChatColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := aau.mutation.AiChatIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.AiChatTable,
			Columns: []string{aiagent.AiChatColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(aau.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, aau.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aiagent.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	aau.mutation.done = true
	return n, nil
}

// AiAgentUpdateOne is the builder for updating a single AiAgent entity.
type AiAgentUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AiAgentMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (aauo *AiAgentUpdateOne) SetUpdatedAt(t time.Time) *AiAgentUpdateOne {
	aauo.mutation.SetUpdatedAt(t)
	return aauo
}

// SetDeletedAt sets the "deleted_at" field.
func (aauo *AiAgentUpdateOne) SetDeletedAt(t time.Time) *AiAgentUpdateOne {
	aauo.mutation.SetDeletedAt(t)
	return aauo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableDeletedAt(t *time.Time) *AiAgentUpdateOne {
	if t != nil {
		aauo.SetDeletedAt(*t)
	}
	return aauo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (aauo *AiAgentUpdateOne) ClearDeletedAt() *AiAgentUpdateOne {
	aauo.mutation.ClearDeletedAt()
	return aauo
}

// SetName sets the "name" field.
func (aauo *AiAgentUpdateOne) SetName(s string) *AiAgentUpdateOne {
	aauo.mutation.SetName(s)
	return aauo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableName(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetName(*s)
	}
	return aauo
}

// SetDescription sets the "description" field.
func (aauo *AiAgentUpdateOne) SetDescription(s string) *AiAgentUpdateOne {
	aauo.mutation.SetDescription(s)
	return aauo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableDescription(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetDescription(*s)
	}
	return aauo
}

// SetAvatar sets the "avatar" field.
func (aauo *AiAgentUpdateOne) SetAvatar(s string) *AiAgentUpdateOne {
	aauo.mutation.SetAvatar(s)
	return aauo
}

// SetNillableAvatar sets the "avatar" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableAvatar(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetAvatar(*s)
	}
	return aauo
}

// SetClickedAvatar sets the "clicked_avatar" field.
func (aauo *AiAgentUpdateOne) SetClickedAvatar(s string) *AiAgentUpdateOne {
	aauo.mutation.SetClickedAvatar(s)
	return aauo
}

// SetNillableClickedAvatar sets the "clicked_avatar" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableClickedAvatar(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetClickedAvatar(*s)
	}
	return aauo
}

// SetWelcomeMsg sets the "welcome_msg" field.
func (aauo *AiAgentUpdateOne) SetWelcomeMsg(s string) *AiAgentUpdateOne {
	aauo.mutation.SetWelcomeMsg(s)
	return aauo
}

// SetNillableWelcomeMsg sets the "welcome_msg" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableWelcomeMsg(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetWelcomeMsg(*s)
	}
	return aauo
}

// SetFallbackMsg sets the "fallback_msg" field.
func (aauo *AiAgentUpdateOne) SetFallbackMsg(s string) *AiAgentUpdateOne {
	aauo.mutation.SetFallbackMsg(s)
	return aauo
}

// SetNillableFallbackMsg sets the "fallback_msg" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableFallbackMsg(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetFallbackMsg(*s)
	}
	return aauo
}

// SetOwnerID sets the "owner_id" field.
func (aauo *AiAgentUpdateOne) SetOwnerID(i int64) *AiAgentUpdateOne {
	aauo.mutation.ResetOwnerID()
	aauo.mutation.SetOwnerID(i)
	return aauo
}

// SetNillableOwnerID sets the "owner_id" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableOwnerID(i *int64) *AiAgentUpdateOne {
	if i != nil {
		aauo.SetOwnerID(*i)
	}
	return aauo
}

// AddOwnerID adds i to the "owner_id" field.
func (aauo *AiAgentUpdateOne) AddOwnerID(i int64) *AiAgentUpdateOne {
	aauo.mutation.AddOwnerID(i)
	return aauo
}

// SetVisibilityType sets the "visibility_type" field.
func (aauo *AiAgentUpdateOne) SetVisibilityType(i int8) *AiAgentUpdateOne {
	aauo.mutation.ResetVisibilityType()
	aauo.mutation.SetVisibilityType(i)
	return aauo
}

// SetNillableVisibilityType sets the "visibility_type" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableVisibilityType(i *int8) *AiAgentUpdateOne {
	if i != nil {
		aauo.SetVisibilityType(*i)
	}
	return aauo
}

// AddVisibilityType adds i to the "visibility_type" field.
func (aauo *AiAgentUpdateOne) AddVisibilityType(i int8) *AiAgentUpdateOne {
	aauo.mutation.AddVisibilityType(i)
	return aauo
}

// SetManageableToUser sets the "manageable_to_user" field.
func (aauo *AiAgentUpdateOne) SetManageableToUser(pq *pq.Int64Array) *AiAgentUpdateOne {
	aauo.mutation.SetManageableToUser(pq)
	return aauo
}

// SetVisibleToUser sets the "visible_to_user" field.
func (aauo *AiAgentUpdateOne) SetVisibleToUser(pq *pq.Int64Array) *AiAgentUpdateOne {
	aauo.mutation.SetVisibleToUser(pq)
	return aauo
}

// SetVisibleToDept sets the "visible_to_dept" field.
func (aauo *AiAgentUpdateOne) SetVisibleToDept(pq *pq.Int64Array) *AiAgentUpdateOne {
	aauo.mutation.SetVisibleToDept(pq)
	return aauo
}

// SetKnowledgeBaseIds sets the "knowledge_base_ids" field.
func (aauo *AiAgentUpdateOne) SetKnowledgeBaseIds(pq *pq.Int64Array) *AiAgentUpdateOne {
	aauo.mutation.SetKnowledgeBaseIds(pq)
	return aauo
}

// SetSchema sets the "schema" field.
func (aauo *AiAgentUpdateOne) SetSchema(s string) *AiAgentUpdateOne {
	aauo.mutation.SetSchema(s)
	return aauo
}

// SetNillableSchema sets the "schema" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableSchema(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetSchema(*s)
	}
	return aauo
}

// SetIsPublic sets the "is_public" field.
func (aauo *AiAgentUpdateOne) SetIsPublic(b bool) *AiAgentUpdateOne {
	aauo.mutation.SetIsPublic(b)
	return aauo
}

// SetNillableIsPublic sets the "is_public" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableIsPublic(b *bool) *AiAgentUpdateOne {
	if b != nil {
		aauo.SetIsPublic(*b)
	}
	return aauo
}

// SetIsEnabled sets the "is_enabled" field.
func (aauo *AiAgentUpdateOne) SetIsEnabled(b bool) *AiAgentUpdateOne {
	aauo.mutation.SetIsEnabled(b)
	return aauo
}

// SetNillableIsEnabled sets the "is_enabled" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableIsEnabled(b *bool) *AiAgentUpdateOne {
	if b != nil {
		aauo.SetIsEnabled(*b)
	}
	return aauo
}

// SetIsRefFiles sets the "is_ref_files" field.
func (aauo *AiAgentUpdateOne) SetIsRefFiles(b bool) *AiAgentUpdateOne {
	aauo.mutation.SetIsRefFiles(b)
	return aauo
}

// SetNillableIsRefFiles sets the "is_ref_files" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableIsRefFiles(b *bool) *AiAgentUpdateOne {
	if b != nil {
		aauo.SetIsRefFiles(*b)
	}
	return aauo
}

// SetModelType sets the "model_type" field.
func (aauo *AiAgentUpdateOne) SetModelType(i int64) *AiAgentUpdateOne {
	aauo.mutation.ResetModelType()
	aauo.mutation.SetModelType(i)
	return aauo
}

// SetNillableModelType sets the "model_type" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableModelType(i *int64) *AiAgentUpdateOne {
	if i != nil {
		aauo.SetModelType(*i)
	}
	return aauo
}

// AddModelType adds i to the "model_type" field.
func (aauo *AiAgentUpdateOne) AddModelType(i int64) *AiAgentUpdateOne {
	aauo.mutation.AddModelType(i)
	return aauo
}

// SetModelID sets the "model_id" field.
func (aauo *AiAgentUpdateOne) SetModelID(i int64) *AiAgentUpdateOne {
	aauo.mutation.ResetModelID()
	aauo.mutation.SetModelID(i)
	return aauo
}

// SetNillableModelID sets the "model_id" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableModelID(i *int64) *AiAgentUpdateOne {
	if i != nil {
		aauo.SetModelID(*i)
	}
	return aauo
}

// AddModelID adds i to the "model_id" field.
func (aauo *AiAgentUpdateOne) AddModelID(i int64) *AiAgentUpdateOne {
	aauo.mutation.AddModelID(i)
	return aauo
}

// SetUseCount sets the "use_count" field.
func (aauo *AiAgentUpdateOne) SetUseCount(i int64) *AiAgentUpdateOne {
	aauo.mutation.ResetUseCount()
	aauo.mutation.SetUseCount(i)
	return aauo
}

// SetNillableUseCount sets the "use_count" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableUseCount(i *int64) *AiAgentUpdateOne {
	if i != nil {
		aauo.SetUseCount(*i)
	}
	return aauo
}

// AddUseCount adds i to the "use_count" field.
func (aauo *AiAgentUpdateOne) AddUseCount(i int64) *AiAgentUpdateOne {
	aauo.mutation.AddUseCount(i)
	return aauo
}

// SetKnowledgeBaseType sets the "knowledge_base_type" field.
func (aauo *AiAgentUpdateOne) SetKnowledgeBaseType(i int64) *AiAgentUpdateOne {
	aauo.mutation.ResetKnowledgeBaseType()
	aauo.mutation.SetKnowledgeBaseType(i)
	return aauo
}

// SetNillableKnowledgeBaseType sets the "knowledge_base_type" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableKnowledgeBaseType(i *int64) *AiAgentUpdateOne {
	if i != nil {
		aauo.SetKnowledgeBaseType(*i)
	}
	return aauo
}

// AddKnowledgeBaseType adds i to the "knowledge_base_type" field.
func (aauo *AiAgentUpdateOne) AddKnowledgeBaseType(i int64) *AiAgentUpdateOne {
	aauo.mutation.AddKnowledgeBaseType(i)
	return aauo
}

// SetInternetSearch sets the "internet_search" field.
func (aauo *AiAgentUpdateOne) SetInternetSearch(b bool) *AiAgentUpdateOne {
	aauo.mutation.SetInternetSearch(b)
	return aauo
}

// SetNillableInternetSearch sets the "internet_search" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableInternetSearch(b *bool) *AiAgentUpdateOne {
	if b != nil {
		aauo.SetInternetSearch(*b)
	}
	return aauo
}

// SetAgentType sets the "agent_type" field.
func (aauo *AiAgentUpdateOne) SetAgentType(i int64) *AiAgentUpdateOne {
	aauo.mutation.ResetAgentType()
	aauo.mutation.SetAgentType(i)
	return aauo
}

// SetNillableAgentType sets the "agent_type" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableAgentType(i *int64) *AiAgentUpdateOne {
	if i != nil {
		aauo.SetAgentType(*i)
	}
	return aauo
}

// AddAgentType adds i to the "agent_type" field.
func (aauo *AiAgentUpdateOne) AddAgentType(i int64) *AiAgentUpdateOne {
	aauo.mutation.AddAgentType(i)
	return aauo
}

// SetThinking sets the "thinking" field.
func (aauo *AiAgentUpdateOne) SetThinking(b bool) *AiAgentUpdateOne {
	aauo.mutation.SetThinking(b)
	return aauo
}

// SetNillableThinking sets the "thinking" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableThinking(b *bool) *AiAgentUpdateOne {
	if b != nil {
		aauo.SetThinking(*b)
	}
	return aauo
}

// SetThinkingModelID sets the "thinking_model_id" field.
func (aauo *AiAgentUpdateOne) SetThinkingModelID(i int64) *AiAgentUpdateOne {
	aauo.mutation.ResetThinkingModelID()
	aauo.mutation.SetThinkingModelID(i)
	return aauo
}

// SetNillableThinkingModelID sets the "thinking_model_id" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableThinkingModelID(i *int64) *AiAgentUpdateOne {
	if i != nil {
		aauo.SetThinkingModelID(*i)
	}
	return aauo
}

// AddThinkingModelID adds i to the "thinking_model_id" field.
func (aauo *AiAgentUpdateOne) AddThinkingModelID(i int64) *AiAgentUpdateOne {
	aauo.mutation.AddThinkingModelID(i)
	return aauo
}

// SetRoleSetting sets the "role_setting" field.
func (aauo *AiAgentUpdateOne) SetRoleSetting(s string) *AiAgentUpdateOne {
	aauo.mutation.SetRoleSetting(s)
	return aauo
}

// SetNillableRoleSetting sets the "role_setting" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableRoleSetting(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetRoleSetting(*s)
	}
	return aauo
}

// SetUploadFile sets the "upload_file" field.
func (aauo *AiAgentUpdateOne) SetUploadFile(b bool) *AiAgentUpdateOne {
	aauo.mutation.SetUploadFile(b)
	return aauo
}

// SetNillableUploadFile sets the "upload_file" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableUploadFile(b *bool) *AiAgentUpdateOne {
	if b != nil {
		aauo.SetUploadFile(*b)
	}
	return aauo
}

// SetSemanticCache sets the "semantic_cache" field.
func (aauo *AiAgentUpdateOne) SetSemanticCache(b bool) *AiAgentUpdateOne {
	aauo.mutation.SetSemanticCache(b)
	return aauo
}

// SetNillableSemanticCache sets the "semantic_cache" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableSemanticCache(b *bool) *AiAgentUpdateOne {
	if b != nil {
		aauo.SetSemanticCache(*b)
	}
	return aauo
}

// AddAiChatIDs adds the "ai_chat" edge to the AiChat entity by IDs.
func (aauo *AiAgentUpdateOne) AddAiChatIDs(ids ...int64) *AiAgentUpdateOne {
	aauo.mutation.AddAiChatIDs(ids...)
	return aauo
}

// AddAiChat adds the "ai_chat" edges to the AiChat entity.
func (aauo *AiAgentUpdateOne) AddAiChat(a ...*AiChat) *AiAgentUpdateOne {
	ids := make([]int64, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return aauo.AddAiChatIDs(ids...)
}

// Mutation returns the AiAgentMutation object of the builder.
func (aauo *AiAgentUpdateOne) Mutation() *AiAgentMutation {
	return aauo.mutation
}

// ClearAiChat clears all "ai_chat" edges to the AiChat entity.
func (aauo *AiAgentUpdateOne) ClearAiChat() *AiAgentUpdateOne {
	aauo.mutation.ClearAiChat()
	return aauo
}

// RemoveAiChatIDs removes the "ai_chat" edge to AiChat entities by IDs.
func (aauo *AiAgentUpdateOne) RemoveAiChatIDs(ids ...int64) *AiAgentUpdateOne {
	aauo.mutation.RemoveAiChatIDs(ids...)
	return aauo
}

// RemoveAiChat removes "ai_chat" edges to AiChat entities.
func (aauo *AiAgentUpdateOne) RemoveAiChat(a ...*AiChat) *AiAgentUpdateOne {
	ids := make([]int64, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return aauo.RemoveAiChatIDs(ids...)
}

// Where appends a list predicates to the AiAgentUpdate builder.
func (aauo *AiAgentUpdateOne) Where(ps ...predicate.AiAgent) *AiAgentUpdateOne {
	aauo.mutation.Where(ps...)
	return aauo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (aauo *AiAgentUpdateOne) Select(field string, fields ...string) *AiAgentUpdateOne {
	aauo.fields = append([]string{field}, fields...)
	return aauo
}

// Save executes the query and returns the updated AiAgent entity.
func (aauo *AiAgentUpdateOne) Save(ctx context.Context) (*AiAgent, error) {
	if err := aauo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, aauo.sqlSave, aauo.mutation, aauo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aauo *AiAgentUpdateOne) SaveX(ctx context.Context) *AiAgent {
	node, err := aauo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (aauo *AiAgentUpdateOne) Exec(ctx context.Context) error {
	_, err := aauo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aauo *AiAgentUpdateOne) ExecX(ctx context.Context) {
	if err := aauo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aauo *AiAgentUpdateOne) defaults() error {
	if _, ok := aauo.mutation.UpdatedAt(); !ok {
		if aiagent.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagent.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aiagent.UpdateDefaultUpdatedAt()
		aauo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aauo *AiAgentUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiAgentUpdateOne {
	aauo.modifiers = append(aauo.modifiers, modifiers...)
	return aauo
}

func (aauo *AiAgentUpdateOne) sqlSave(ctx context.Context) (_node *AiAgent, err error) {
	_spec := sqlgraph.NewUpdateSpec(aiagent.Table, aiagent.Columns, sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt64))
	id, ok := aauo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AiAgent.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := aauo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aiagent.FieldID)
		for _, f := range fields {
			if !aiagent.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != aiagent.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := aauo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aauo.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagent.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aauo.mutation.DeletedAt(); ok {
		_spec.SetField(aiagent.FieldDeletedAt, field.TypeTime, value)
	}
	if aauo.mutation.DeletedAtCleared() {
		_spec.ClearField(aiagent.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := aauo.mutation.Name(); ok {
		_spec.SetField(aiagent.FieldName, field.TypeString, value)
	}
	if value, ok := aauo.mutation.Description(); ok {
		_spec.SetField(aiagent.FieldDescription, field.TypeString, value)
	}
	if value, ok := aauo.mutation.Avatar(); ok {
		_spec.SetField(aiagent.FieldAvatar, field.TypeString, value)
	}
	if value, ok := aauo.mutation.ClickedAvatar(); ok {
		_spec.SetField(aiagent.FieldClickedAvatar, field.TypeString, value)
	}
	if value, ok := aauo.mutation.WelcomeMsg(); ok {
		_spec.SetField(aiagent.FieldWelcomeMsg, field.TypeString, value)
	}
	if value, ok := aauo.mutation.FallbackMsg(); ok {
		_spec.SetField(aiagent.FieldFallbackMsg, field.TypeString, value)
	}
	if value, ok := aauo.mutation.OwnerID(); ok {
		_spec.SetField(aiagent.FieldOwnerID, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.AddedOwnerID(); ok {
		_spec.AddField(aiagent.FieldOwnerID, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.VisibilityType(); ok {
		_spec.SetField(aiagent.FieldVisibilityType, field.TypeInt8, value)
	}
	if value, ok := aauo.mutation.AddedVisibilityType(); ok {
		_spec.AddField(aiagent.FieldVisibilityType, field.TypeInt8, value)
	}
	if value, ok := aauo.mutation.ManageableToUser(); ok {
		_spec.SetField(aiagent.FieldManageableToUser, field.TypeOther, value)
	}
	if value, ok := aauo.mutation.VisibleToUser(); ok {
		_spec.SetField(aiagent.FieldVisibleToUser, field.TypeOther, value)
	}
	if value, ok := aauo.mutation.VisibleToDept(); ok {
		_spec.SetField(aiagent.FieldVisibleToDept, field.TypeOther, value)
	}
	if value, ok := aauo.mutation.KnowledgeBaseIds(); ok {
		_spec.SetField(aiagent.FieldKnowledgeBaseIds, field.TypeOther, value)
	}
	if value, ok := aauo.mutation.Schema(); ok {
		_spec.SetField(aiagent.FieldSchema, field.TypeString, value)
	}
	if value, ok := aauo.mutation.IsPublic(); ok {
		_spec.SetField(aiagent.FieldIsPublic, field.TypeBool, value)
	}
	if value, ok := aauo.mutation.IsEnabled(); ok {
		_spec.SetField(aiagent.FieldIsEnabled, field.TypeBool, value)
	}
	if value, ok := aauo.mutation.IsRefFiles(); ok {
		_spec.SetField(aiagent.FieldIsRefFiles, field.TypeBool, value)
	}
	if value, ok := aauo.mutation.ModelType(); ok {
		_spec.SetField(aiagent.FieldModelType, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.AddedModelType(); ok {
		_spec.AddField(aiagent.FieldModelType, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.ModelID(); ok {
		_spec.SetField(aiagent.FieldModelID, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.AddedModelID(); ok {
		_spec.AddField(aiagent.FieldModelID, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.UseCount(); ok {
		_spec.SetField(aiagent.FieldUseCount, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.AddedUseCount(); ok {
		_spec.AddField(aiagent.FieldUseCount, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.KnowledgeBaseType(); ok {
		_spec.SetField(aiagent.FieldKnowledgeBaseType, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.AddedKnowledgeBaseType(); ok {
		_spec.AddField(aiagent.FieldKnowledgeBaseType, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.InternetSearch(); ok {
		_spec.SetField(aiagent.FieldInternetSearch, field.TypeBool, value)
	}
	if value, ok := aauo.mutation.AgentType(); ok {
		_spec.SetField(aiagent.FieldAgentType, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.AddedAgentType(); ok {
		_spec.AddField(aiagent.FieldAgentType, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.Thinking(); ok {
		_spec.SetField(aiagent.FieldThinking, field.TypeBool, value)
	}
	if value, ok := aauo.mutation.ThinkingModelID(); ok {
		_spec.SetField(aiagent.FieldThinkingModelID, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.AddedThinkingModelID(); ok {
		_spec.AddField(aiagent.FieldThinkingModelID, field.TypeInt64, value)
	}
	if value, ok := aauo.mutation.RoleSetting(); ok {
		_spec.SetField(aiagent.FieldRoleSetting, field.TypeString, value)
	}
	if value, ok := aauo.mutation.UploadFile(); ok {
		_spec.SetField(aiagent.FieldUploadFile, field.TypeBool, value)
	}
	if value, ok := aauo.mutation.SemanticCache(); ok {
		_spec.SetField(aiagent.FieldSemanticCache, field.TypeBool, value)
	}
	if aauo.mutation.AiChatCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.AiChatTable,
			Columns: []string{aiagent.AiChatColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := aauo.mutation.RemovedAiChatIDs(); len(nodes) > 0 && !aauo.mutation.AiChatCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.AiChatTable,
			Columns: []string{aiagent.AiChatColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := aauo.mutation.AiChatIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.AiChatTable,
			Columns: []string{aiagent.AiChatColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_spec.AddModifiers(aauo.modifiers...)
	_node = &AiAgent{config: aauo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, aauo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aiagent.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	aauo.mutation.done = true
	return _node, nil
}

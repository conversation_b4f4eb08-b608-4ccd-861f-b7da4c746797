// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
)

// KnowledgeBase is the model entity for the KnowledgeBase schema.
type KnowledgeBase struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 租户ID
	TenantID int64 `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 名称
	Name string `json:"name,omitempty"`
	// 是否公开
	Public bool `json:"public,omitempty"`
	// 数据类型 1文档 2表格
	DataType int32 `json:"data_type,omitempty"`
	// 用户id
	UserID int64 `json:"user_id,omitempty"`
	// 管理者用户ids
	ManagerUserIds *pq.Int64Array `json:"manager_user_ids,omitempty"`
	// 可编辑用户ids
	EditableUserIds *pq.Int64Array `json:"editable_user_ids,omitempty"`
	selectValues    sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*KnowledgeBase) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case knowledgebase.FieldManagerUserIds, knowledgebase.FieldEditableUserIds:
			values[i] = new(pq.Int64Array)
		case knowledgebase.FieldPublic:
			values[i] = new(sql.NullBool)
		case knowledgebase.FieldID, knowledgebase.FieldTenantID, knowledgebase.FieldDataType, knowledgebase.FieldUserID:
			values[i] = new(sql.NullInt64)
		case knowledgebase.FieldName:
			values[i] = new(sql.NullString)
		case knowledgebase.FieldCreatedAt, knowledgebase.FieldUpdatedAt, knowledgebase.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the KnowledgeBase fields.
func (kb *KnowledgeBase) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case knowledgebase.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			kb.ID = int64(value.Int64)
		case knowledgebase.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				kb.TenantID = value.Int64
			}
		case knowledgebase.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				kb.CreatedAt = value.Time
			}
		case knowledgebase.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				kb.UpdatedAt = value.Time
			}
		case knowledgebase.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				kb.DeletedAt = value.Time
			}
		case knowledgebase.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				kb.Name = value.String
			}
		case knowledgebase.FieldPublic:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field public", values[i])
			} else if value.Valid {
				kb.Public = value.Bool
			}
		case knowledgebase.FieldDataType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field data_type", values[i])
			} else if value.Valid {
				kb.DataType = int32(value.Int64)
			}
		case knowledgebase.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				kb.UserID = value.Int64
			}
		case knowledgebase.FieldManagerUserIds:
			if value, ok := values[i].(*pq.Int64Array); !ok {
				return fmt.Errorf("unexpected type %T for field manager_user_ids", values[i])
			} else if value != nil {
				kb.ManagerUserIds = value
			}
		case knowledgebase.FieldEditableUserIds:
			if value, ok := values[i].(*pq.Int64Array); !ok {
				return fmt.Errorf("unexpected type %T for field editable_user_ids", values[i])
			} else if value != nil {
				kb.EditableUserIds = value
			}
		default:
			kb.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the KnowledgeBase.
// This includes values selected through modifiers, order, etc.
func (kb *KnowledgeBase) Value(name string) (ent.Value, error) {
	return kb.selectValues.Get(name)
}

// Update returns a builder for updating this KnowledgeBase.
// Note that you need to call KnowledgeBase.Unwrap() before calling this method if this KnowledgeBase
// was returned from a transaction, and the transaction was committed or rolled back.
func (kb *KnowledgeBase) Update() *KnowledgeBaseUpdateOne {
	return NewKnowledgeBaseClient(kb.config).UpdateOne(kb)
}

// Unwrap unwraps the KnowledgeBase entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (kb *KnowledgeBase) Unwrap() *KnowledgeBase {
	_tx, ok := kb.config.driver.(*txDriver)
	if !ok {
		panic("ent: KnowledgeBase is not a transactional entity")
	}
	kb.config.driver = _tx.drv
	return kb
}

// String implements the fmt.Stringer.
func (kb *KnowledgeBase) String() string {
	var builder strings.Builder
	builder.WriteString("KnowledgeBase(")
	builder.WriteString(fmt.Sprintf("id=%v, ", kb.ID))
	builder.WriteString("tenant_id=")
	builder.WriteString(fmt.Sprintf("%v", kb.TenantID))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(kb.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(kb.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(kb.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(kb.Name)
	builder.WriteString(", ")
	builder.WriteString("public=")
	builder.WriteString(fmt.Sprintf("%v", kb.Public))
	builder.WriteString(", ")
	builder.WriteString("data_type=")
	builder.WriteString(fmt.Sprintf("%v", kb.DataType))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", kb.UserID))
	builder.WriteString(", ")
	builder.WriteString("manager_user_ids=")
	builder.WriteString(fmt.Sprintf("%v", kb.ManagerUserIds))
	builder.WriteString(", ")
	builder.WriteString("editable_user_ids=")
	builder.WriteString(fmt.Sprintf("%v", kb.EditableUserIds))
	builder.WriteByte(')')
	return builder.String()
}

// KnowledgeBases is a parsable slice of KnowledgeBase.
type KnowledgeBases []*KnowledgeBase

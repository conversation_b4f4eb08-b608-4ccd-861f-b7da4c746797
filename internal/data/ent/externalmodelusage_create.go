// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/externalmodelusage"
)

// ExternalModelUsageCreate is the builder for creating a ExternalModelUsage entity.
type ExternalModelUsageCreate struct {
	config
	mutation *ExternalModelUsageMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (emuc *ExternalModelUsageCreate) SetCreatedAt(t time.Time) *ExternalModelUsageCreate {
	emuc.mutation.SetCreatedAt(t)
	return emuc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableCreatedAt(t *time.Time) *ExternalModelUsageCreate {
	if t != nil {
		emuc.SetCreatedAt(*t)
	}
	return emuc
}

// SetUpdatedAt sets the "updated_at" field.
func (emuc *ExternalModelUsageCreate) SetUpdatedAt(t time.Time) *ExternalModelUsageCreate {
	emuc.mutation.SetUpdatedAt(t)
	return emuc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableUpdatedAt(t *time.Time) *ExternalModelUsageCreate {
	if t != nil {
		emuc.SetUpdatedAt(*t)
	}
	return emuc
}

// SetDeletedAt sets the "deleted_at" field.
func (emuc *ExternalModelUsageCreate) SetDeletedAt(t time.Time) *ExternalModelUsageCreate {
	emuc.mutation.SetDeletedAt(t)
	return emuc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableDeletedAt(t *time.Time) *ExternalModelUsageCreate {
	if t != nil {
		emuc.SetDeletedAt(*t)
	}
	return emuc
}

// SetModelName sets the "model_name" field.
func (emuc *ExternalModelUsageCreate) SetModelName(s string) *ExternalModelUsageCreate {
	emuc.mutation.SetModelName(s)
	return emuc
}

// SetNillableModelName sets the "model_name" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableModelName(s *string) *ExternalModelUsageCreate {
	if s != nil {
		emuc.SetModelName(*s)
	}
	return emuc
}

// SetQuestion sets the "question" field.
func (emuc *ExternalModelUsageCreate) SetQuestion(s string) *ExternalModelUsageCreate {
	emuc.mutation.SetQuestion(s)
	return emuc
}

// SetNillableQuestion sets the "question" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableQuestion(s *string) *ExternalModelUsageCreate {
	if s != nil {
		emuc.SetQuestion(*s)
	}
	return emuc
}

// SetQuestionTag sets the "question_tag" field.
func (emuc *ExternalModelUsageCreate) SetQuestionTag(s string) *ExternalModelUsageCreate {
	emuc.mutation.SetQuestionTag(s)
	return emuc
}

// SetNillableQuestionTag sets the "question_tag" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableQuestionTag(s *string) *ExternalModelUsageCreate {
	if s != nil {
		emuc.SetQuestionTag(*s)
	}
	return emuc
}

// SetFiles sets the "files" field.
func (emuc *ExternalModelUsageCreate) SetFiles(s string) *ExternalModelUsageCreate {
	emuc.mutation.SetFiles(s)
	return emuc
}

// SetNillableFiles sets the "files" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableFiles(s *string) *ExternalModelUsageCreate {
	if s != nil {
		emuc.SetFiles(*s)
	}
	return emuc
}

// SetMimeTypes sets the "mime_types" field.
func (emuc *ExternalModelUsageCreate) SetMimeTypes(pa *pq.StringArray) *ExternalModelUsageCreate {
	emuc.mutation.SetMimeTypes(pa)
	return emuc
}

// SetUserID sets the "user_id" field.
func (emuc *ExternalModelUsageCreate) SetUserID(i int64) *ExternalModelUsageCreate {
	emuc.mutation.SetUserID(i)
	return emuc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableUserID(i *int64) *ExternalModelUsageCreate {
	if i != nil {
		emuc.SetUserID(*i)
	}
	return emuc
}

// SetUserName sets the "user_name" field.
func (emuc *ExternalModelUsageCreate) SetUserName(s string) *ExternalModelUsageCreate {
	emuc.mutation.SetUserName(s)
	return emuc
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableUserName(s *string) *ExternalModelUsageCreate {
	if s != nil {
		emuc.SetUserName(*s)
	}
	return emuc
}

// SetDeptID sets the "dept_id" field.
func (emuc *ExternalModelUsageCreate) SetDeptID(i int64) *ExternalModelUsageCreate {
	emuc.mutation.SetDeptID(i)
	return emuc
}

// SetNillableDeptID sets the "dept_id" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableDeptID(i *int64) *ExternalModelUsageCreate {
	if i != nil {
		emuc.SetDeptID(*i)
	}
	return emuc
}

// SetDeptName sets the "dept_name" field.
func (emuc *ExternalModelUsageCreate) SetDeptName(s string) *ExternalModelUsageCreate {
	emuc.mutation.SetDeptName(s)
	return emuc
}

// SetNillableDeptName sets the "dept_name" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableDeptName(s *string) *ExternalModelUsageCreate {
	if s != nil {
		emuc.SetDeptName(*s)
	}
	return emuc
}

// SetPcName sets the "pc_name" field.
func (emuc *ExternalModelUsageCreate) SetPcName(s string) *ExternalModelUsageCreate {
	emuc.mutation.SetPcName(s)
	return emuc
}

// SetNillablePcName sets the "pc_name" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillablePcName(s *string) *ExternalModelUsageCreate {
	if s != nil {
		emuc.SetPcName(*s)
	}
	return emuc
}

// SetHappenedAt sets the "happened_at" field.
func (emuc *ExternalModelUsageCreate) SetHappenedAt(t time.Time) *ExternalModelUsageCreate {
	emuc.mutation.SetHappenedAt(t)
	return emuc
}

// SetNillableHappenedAt sets the "happened_at" field if the given value is not nil.
func (emuc *ExternalModelUsageCreate) SetNillableHappenedAt(t *time.Time) *ExternalModelUsageCreate {
	if t != nil {
		emuc.SetHappenedAt(*t)
	}
	return emuc
}

// SetID sets the "id" field.
func (emuc *ExternalModelUsageCreate) SetID(i int64) *ExternalModelUsageCreate {
	emuc.mutation.SetID(i)
	return emuc
}

// Mutation returns the ExternalModelUsageMutation object of the builder.
func (emuc *ExternalModelUsageCreate) Mutation() *ExternalModelUsageMutation {
	return emuc.mutation
}

// Save creates the ExternalModelUsage in the database.
func (emuc *ExternalModelUsageCreate) Save(ctx context.Context) (*ExternalModelUsage, error) {
	if err := emuc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, emuc.sqlSave, emuc.mutation, emuc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (emuc *ExternalModelUsageCreate) SaveX(ctx context.Context) *ExternalModelUsage {
	v, err := emuc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (emuc *ExternalModelUsageCreate) Exec(ctx context.Context) error {
	_, err := emuc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (emuc *ExternalModelUsageCreate) ExecX(ctx context.Context) {
	if err := emuc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (emuc *ExternalModelUsageCreate) defaults() error {
	if _, ok := emuc.mutation.CreatedAt(); !ok {
		if externalmodelusage.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized externalmodelusage.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := externalmodelusage.DefaultCreatedAt()
		emuc.mutation.SetCreatedAt(v)
	}
	if _, ok := emuc.mutation.UpdatedAt(); !ok {
		if externalmodelusage.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized externalmodelusage.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := externalmodelusage.DefaultUpdatedAt()
		emuc.mutation.SetUpdatedAt(v)
	}
	if _, ok := emuc.mutation.DeletedAt(); !ok {
		v := externalmodelusage.DefaultDeletedAt
		emuc.mutation.SetDeletedAt(v)
	}
	if _, ok := emuc.mutation.ModelName(); !ok {
		v := externalmodelusage.DefaultModelName
		emuc.mutation.SetModelName(v)
	}
	if _, ok := emuc.mutation.Question(); !ok {
		v := externalmodelusage.DefaultQuestion
		emuc.mutation.SetQuestion(v)
	}
	if _, ok := emuc.mutation.QuestionTag(); !ok {
		v := externalmodelusage.DefaultQuestionTag
		emuc.mutation.SetQuestionTag(v)
	}
	if _, ok := emuc.mutation.Files(); !ok {
		v := externalmodelusage.DefaultFiles
		emuc.mutation.SetFiles(v)
	}
	if _, ok := emuc.mutation.MimeTypes(); !ok {
		v := externalmodelusage.DefaultMimeTypes
		emuc.mutation.SetMimeTypes(v)
	}
	if _, ok := emuc.mutation.UserID(); !ok {
		v := externalmodelusage.DefaultUserID
		emuc.mutation.SetUserID(v)
	}
	if _, ok := emuc.mutation.UserName(); !ok {
		v := externalmodelusage.DefaultUserName
		emuc.mutation.SetUserName(v)
	}
	if _, ok := emuc.mutation.DeptID(); !ok {
		v := externalmodelusage.DefaultDeptID
		emuc.mutation.SetDeptID(v)
	}
	if _, ok := emuc.mutation.DeptName(); !ok {
		v := externalmodelusage.DefaultDeptName
		emuc.mutation.SetDeptName(v)
	}
	if _, ok := emuc.mutation.PcName(); !ok {
		v := externalmodelusage.DefaultPcName
		emuc.mutation.SetPcName(v)
	}
	if _, ok := emuc.mutation.HappenedAt(); !ok {
		v := externalmodelusage.DefaultHappenedAt
		emuc.mutation.SetHappenedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (emuc *ExternalModelUsageCreate) check() error {
	if _, ok := emuc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "ExternalModelUsage.created_at"`)}
	}
	if _, ok := emuc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "ExternalModelUsage.updated_at"`)}
	}
	if _, ok := emuc.mutation.ModelName(); !ok {
		return &ValidationError{Name: "model_name", err: errors.New(`ent: missing required field "ExternalModelUsage.model_name"`)}
	}
	if _, ok := emuc.mutation.Question(); !ok {
		return &ValidationError{Name: "question", err: errors.New(`ent: missing required field "ExternalModelUsage.question"`)}
	}
	if _, ok := emuc.mutation.QuestionTag(); !ok {
		return &ValidationError{Name: "question_tag", err: errors.New(`ent: missing required field "ExternalModelUsage.question_tag"`)}
	}
	if _, ok := emuc.mutation.Files(); !ok {
		return &ValidationError{Name: "files", err: errors.New(`ent: missing required field "ExternalModelUsage.files"`)}
	}
	if _, ok := emuc.mutation.MimeTypes(); !ok {
		return &ValidationError{Name: "mime_types", err: errors.New(`ent: missing required field "ExternalModelUsage.mime_types"`)}
	}
	if _, ok := emuc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "ExternalModelUsage.user_id"`)}
	}
	if _, ok := emuc.mutation.UserName(); !ok {
		return &ValidationError{Name: "user_name", err: errors.New(`ent: missing required field "ExternalModelUsage.user_name"`)}
	}
	if _, ok := emuc.mutation.DeptID(); !ok {
		return &ValidationError{Name: "dept_id", err: errors.New(`ent: missing required field "ExternalModelUsage.dept_id"`)}
	}
	if _, ok := emuc.mutation.DeptName(); !ok {
		return &ValidationError{Name: "dept_name", err: errors.New(`ent: missing required field "ExternalModelUsage.dept_name"`)}
	}
	if _, ok := emuc.mutation.PcName(); !ok {
		return &ValidationError{Name: "pc_name", err: errors.New(`ent: missing required field "ExternalModelUsage.pc_name"`)}
	}
	if _, ok := emuc.mutation.HappenedAt(); !ok {
		return &ValidationError{Name: "happened_at", err: errors.New(`ent: missing required field "ExternalModelUsage.happened_at"`)}
	}
	return nil
}

func (emuc *ExternalModelUsageCreate) sqlSave(ctx context.Context) (*ExternalModelUsage, error) {
	if err := emuc.check(); err != nil {
		return nil, err
	}
	_node, _spec := emuc.createSpec()
	if err := sqlgraph.CreateNode(ctx, emuc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	emuc.mutation.id = &_node.ID
	emuc.mutation.done = true
	return _node, nil
}

func (emuc *ExternalModelUsageCreate) createSpec() (*ExternalModelUsage, *sqlgraph.CreateSpec) {
	var (
		_node = &ExternalModelUsage{config: emuc.config}
		_spec = sqlgraph.NewCreateSpec(externalmodelusage.Table, sqlgraph.NewFieldSpec(externalmodelusage.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = emuc.conflict
	if id, ok := emuc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := emuc.mutation.CreatedAt(); ok {
		_spec.SetField(externalmodelusage.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := emuc.mutation.UpdatedAt(); ok {
		_spec.SetField(externalmodelusage.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := emuc.mutation.DeletedAt(); ok {
		_spec.SetField(externalmodelusage.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := emuc.mutation.ModelName(); ok {
		_spec.SetField(externalmodelusage.FieldModelName, field.TypeString, value)
		_node.ModelName = value
	}
	if value, ok := emuc.mutation.Question(); ok {
		_spec.SetField(externalmodelusage.FieldQuestion, field.TypeString, value)
		_node.Question = value
	}
	if value, ok := emuc.mutation.QuestionTag(); ok {
		_spec.SetField(externalmodelusage.FieldQuestionTag, field.TypeString, value)
		_node.QuestionTag = value
	}
	if value, ok := emuc.mutation.Files(); ok {
		_spec.SetField(externalmodelusage.FieldFiles, field.TypeString, value)
		_node.Files = value
	}
	if value, ok := emuc.mutation.MimeTypes(); ok {
		_spec.SetField(externalmodelusage.FieldMimeTypes, field.TypeOther, value)
		_node.MimeTypes = value
	}
	if value, ok := emuc.mutation.UserID(); ok {
		_spec.SetField(externalmodelusage.FieldUserID, field.TypeInt64, value)
		_node.UserID = value
	}
	if value, ok := emuc.mutation.UserName(); ok {
		_spec.SetField(externalmodelusage.FieldUserName, field.TypeString, value)
		_node.UserName = value
	}
	if value, ok := emuc.mutation.DeptID(); ok {
		_spec.SetField(externalmodelusage.FieldDeptID, field.TypeInt64, value)
		_node.DeptID = value
	}
	if value, ok := emuc.mutation.DeptName(); ok {
		_spec.SetField(externalmodelusage.FieldDeptName, field.TypeString, value)
		_node.DeptName = value
	}
	if value, ok := emuc.mutation.PcName(); ok {
		_spec.SetField(externalmodelusage.FieldPcName, field.TypeString, value)
		_node.PcName = value
	}
	if value, ok := emuc.mutation.HappenedAt(); ok {
		_spec.SetField(externalmodelusage.FieldHappenedAt, field.TypeTime, value)
		_node.HappenedAt = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ExternalModelUsage.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ExternalModelUsageUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (emuc *ExternalModelUsageCreate) OnConflict(opts ...sql.ConflictOption) *ExternalModelUsageUpsertOne {
	emuc.conflict = opts
	return &ExternalModelUsageUpsertOne{
		create: emuc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ExternalModelUsage.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (emuc *ExternalModelUsageCreate) OnConflictColumns(columns ...string) *ExternalModelUsageUpsertOne {
	emuc.conflict = append(emuc.conflict, sql.ConflictColumns(columns...))
	return &ExternalModelUsageUpsertOne{
		create: emuc,
	}
}

type (
	// ExternalModelUsageUpsertOne is the builder for "upsert"-ing
	//  one ExternalModelUsage node.
	ExternalModelUsageUpsertOne struct {
		create *ExternalModelUsageCreate
	}

	// ExternalModelUsageUpsert is the "OnConflict" setter.
	ExternalModelUsageUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *ExternalModelUsageUpsert) SetUpdatedAt(v time.Time) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateUpdatedAt() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ExternalModelUsageUpsert) SetDeletedAt(v time.Time) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateDeletedAt() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ExternalModelUsageUpsert) ClearDeletedAt() *ExternalModelUsageUpsert {
	u.SetNull(externalmodelusage.FieldDeletedAt)
	return u
}

// SetModelName sets the "model_name" field.
func (u *ExternalModelUsageUpsert) SetModelName(v string) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldModelName, v)
	return u
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateModelName() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldModelName)
	return u
}

// SetQuestion sets the "question" field.
func (u *ExternalModelUsageUpsert) SetQuestion(v string) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldQuestion, v)
	return u
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateQuestion() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldQuestion)
	return u
}

// SetQuestionTag sets the "question_tag" field.
func (u *ExternalModelUsageUpsert) SetQuestionTag(v string) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldQuestionTag, v)
	return u
}

// UpdateQuestionTag sets the "question_tag" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateQuestionTag() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldQuestionTag)
	return u
}

// SetFiles sets the "files" field.
func (u *ExternalModelUsageUpsert) SetFiles(v string) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldFiles, v)
	return u
}

// UpdateFiles sets the "files" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateFiles() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldFiles)
	return u
}

// SetMimeTypes sets the "mime_types" field.
func (u *ExternalModelUsageUpsert) SetMimeTypes(v *pq.StringArray) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldMimeTypes, v)
	return u
}

// UpdateMimeTypes sets the "mime_types" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateMimeTypes() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldMimeTypes)
	return u
}

// SetUserID sets the "user_id" field.
func (u *ExternalModelUsageUpsert) SetUserID(v int64) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateUserID() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldUserID)
	return u
}

// AddUserID adds v to the "user_id" field.
func (u *ExternalModelUsageUpsert) AddUserID(v int64) *ExternalModelUsageUpsert {
	u.Add(externalmodelusage.FieldUserID, v)
	return u
}

// SetUserName sets the "user_name" field.
func (u *ExternalModelUsageUpsert) SetUserName(v string) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldUserName, v)
	return u
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateUserName() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldUserName)
	return u
}

// SetDeptID sets the "dept_id" field.
func (u *ExternalModelUsageUpsert) SetDeptID(v int64) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldDeptID, v)
	return u
}

// UpdateDeptID sets the "dept_id" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateDeptID() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldDeptID)
	return u
}

// AddDeptID adds v to the "dept_id" field.
func (u *ExternalModelUsageUpsert) AddDeptID(v int64) *ExternalModelUsageUpsert {
	u.Add(externalmodelusage.FieldDeptID, v)
	return u
}

// SetDeptName sets the "dept_name" field.
func (u *ExternalModelUsageUpsert) SetDeptName(v string) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldDeptName, v)
	return u
}

// UpdateDeptName sets the "dept_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateDeptName() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldDeptName)
	return u
}

// SetPcName sets the "pc_name" field.
func (u *ExternalModelUsageUpsert) SetPcName(v string) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldPcName, v)
	return u
}

// UpdatePcName sets the "pc_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdatePcName() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldPcName)
	return u
}

// SetHappenedAt sets the "happened_at" field.
func (u *ExternalModelUsageUpsert) SetHappenedAt(v time.Time) *ExternalModelUsageUpsert {
	u.Set(externalmodelusage.FieldHappenedAt, v)
	return u
}

// UpdateHappenedAt sets the "happened_at" field to the value that was provided on create.
func (u *ExternalModelUsageUpsert) UpdateHappenedAt() *ExternalModelUsageUpsert {
	u.SetExcluded(externalmodelusage.FieldHappenedAt)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.ExternalModelUsage.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(externalmodelusage.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ExternalModelUsageUpsertOne) UpdateNewValues() *ExternalModelUsageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(externalmodelusage.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(externalmodelusage.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ExternalModelUsage.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ExternalModelUsageUpsertOne) Ignore() *ExternalModelUsageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ExternalModelUsageUpsertOne) DoNothing() *ExternalModelUsageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ExternalModelUsageCreate.OnConflict
// documentation for more info.
func (u *ExternalModelUsageUpsertOne) Update(set func(*ExternalModelUsageUpsert)) *ExternalModelUsageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ExternalModelUsageUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *ExternalModelUsageUpsertOne) SetUpdatedAt(v time.Time) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateUpdatedAt() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ExternalModelUsageUpsertOne) SetDeletedAt(v time.Time) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateDeletedAt() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ExternalModelUsageUpsertOne) ClearDeletedAt() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.ClearDeletedAt()
	})
}

// SetModelName sets the "model_name" field.
func (u *ExternalModelUsageUpsertOne) SetModelName(v string) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetModelName(v)
	})
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateModelName() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateModelName()
	})
}

// SetQuestion sets the "question" field.
func (u *ExternalModelUsageUpsertOne) SetQuestion(v string) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetQuestion(v)
	})
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateQuestion() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateQuestion()
	})
}

// SetQuestionTag sets the "question_tag" field.
func (u *ExternalModelUsageUpsertOne) SetQuestionTag(v string) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetQuestionTag(v)
	})
}

// UpdateQuestionTag sets the "question_tag" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateQuestionTag() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateQuestionTag()
	})
}

// SetFiles sets the "files" field.
func (u *ExternalModelUsageUpsertOne) SetFiles(v string) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetFiles(v)
	})
}

// UpdateFiles sets the "files" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateFiles() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateFiles()
	})
}

// SetMimeTypes sets the "mime_types" field.
func (u *ExternalModelUsageUpsertOne) SetMimeTypes(v *pq.StringArray) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetMimeTypes(v)
	})
}

// UpdateMimeTypes sets the "mime_types" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateMimeTypes() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateMimeTypes()
	})
}

// SetUserID sets the "user_id" field.
func (u *ExternalModelUsageUpsertOne) SetUserID(v int64) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *ExternalModelUsageUpsertOne) AddUserID(v int64) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateUserID() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateUserID()
	})
}

// SetUserName sets the "user_name" field.
func (u *ExternalModelUsageUpsertOne) SetUserName(v string) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetUserName(v)
	})
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateUserName() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateUserName()
	})
}

// SetDeptID sets the "dept_id" field.
func (u *ExternalModelUsageUpsertOne) SetDeptID(v int64) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetDeptID(v)
	})
}

// AddDeptID adds v to the "dept_id" field.
func (u *ExternalModelUsageUpsertOne) AddDeptID(v int64) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.AddDeptID(v)
	})
}

// UpdateDeptID sets the "dept_id" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateDeptID() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateDeptID()
	})
}

// SetDeptName sets the "dept_name" field.
func (u *ExternalModelUsageUpsertOne) SetDeptName(v string) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetDeptName(v)
	})
}

// UpdateDeptName sets the "dept_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateDeptName() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateDeptName()
	})
}

// SetPcName sets the "pc_name" field.
func (u *ExternalModelUsageUpsertOne) SetPcName(v string) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetPcName(v)
	})
}

// UpdatePcName sets the "pc_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdatePcName() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdatePcName()
	})
}

// SetHappenedAt sets the "happened_at" field.
func (u *ExternalModelUsageUpsertOne) SetHappenedAt(v time.Time) *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetHappenedAt(v)
	})
}

// UpdateHappenedAt sets the "happened_at" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertOne) UpdateHappenedAt() *ExternalModelUsageUpsertOne {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateHappenedAt()
	})
}

// Exec executes the query.
func (u *ExternalModelUsageUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ExternalModelUsageCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ExternalModelUsageUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ExternalModelUsageUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ExternalModelUsageUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ExternalModelUsageCreateBulk is the builder for creating many ExternalModelUsage entities in bulk.
type ExternalModelUsageCreateBulk struct {
	config
	err      error
	builders []*ExternalModelUsageCreate
	conflict []sql.ConflictOption
}

// Save creates the ExternalModelUsage entities in the database.
func (emucb *ExternalModelUsageCreateBulk) Save(ctx context.Context) ([]*ExternalModelUsage, error) {
	if emucb.err != nil {
		return nil, emucb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(emucb.builders))
	nodes := make([]*ExternalModelUsage, len(emucb.builders))
	mutators := make([]Mutator, len(emucb.builders))
	for i := range emucb.builders {
		func(i int, root context.Context) {
			builder := emucb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ExternalModelUsageMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, emucb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = emucb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, emucb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, emucb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (emucb *ExternalModelUsageCreateBulk) SaveX(ctx context.Context) []*ExternalModelUsage {
	v, err := emucb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (emucb *ExternalModelUsageCreateBulk) Exec(ctx context.Context) error {
	_, err := emucb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (emucb *ExternalModelUsageCreateBulk) ExecX(ctx context.Context) {
	if err := emucb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ExternalModelUsage.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ExternalModelUsageUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (emucb *ExternalModelUsageCreateBulk) OnConflict(opts ...sql.ConflictOption) *ExternalModelUsageUpsertBulk {
	emucb.conflict = opts
	return &ExternalModelUsageUpsertBulk{
		create: emucb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ExternalModelUsage.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (emucb *ExternalModelUsageCreateBulk) OnConflictColumns(columns ...string) *ExternalModelUsageUpsertBulk {
	emucb.conflict = append(emucb.conflict, sql.ConflictColumns(columns...))
	return &ExternalModelUsageUpsertBulk{
		create: emucb,
	}
}

// ExternalModelUsageUpsertBulk is the builder for "upsert"-ing
// a bulk of ExternalModelUsage nodes.
type ExternalModelUsageUpsertBulk struct {
	create *ExternalModelUsageCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.ExternalModelUsage.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(externalmodelusage.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ExternalModelUsageUpsertBulk) UpdateNewValues() *ExternalModelUsageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(externalmodelusage.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(externalmodelusage.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ExternalModelUsage.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ExternalModelUsageUpsertBulk) Ignore() *ExternalModelUsageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ExternalModelUsageUpsertBulk) DoNothing() *ExternalModelUsageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ExternalModelUsageCreateBulk.OnConflict
// documentation for more info.
func (u *ExternalModelUsageUpsertBulk) Update(set func(*ExternalModelUsageUpsert)) *ExternalModelUsageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ExternalModelUsageUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *ExternalModelUsageUpsertBulk) SetUpdatedAt(v time.Time) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateUpdatedAt() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ExternalModelUsageUpsertBulk) SetDeletedAt(v time.Time) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateDeletedAt() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ExternalModelUsageUpsertBulk) ClearDeletedAt() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.ClearDeletedAt()
	})
}

// SetModelName sets the "model_name" field.
func (u *ExternalModelUsageUpsertBulk) SetModelName(v string) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetModelName(v)
	})
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateModelName() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateModelName()
	})
}

// SetQuestion sets the "question" field.
func (u *ExternalModelUsageUpsertBulk) SetQuestion(v string) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetQuestion(v)
	})
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateQuestion() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateQuestion()
	})
}

// SetQuestionTag sets the "question_tag" field.
func (u *ExternalModelUsageUpsertBulk) SetQuestionTag(v string) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetQuestionTag(v)
	})
}

// UpdateQuestionTag sets the "question_tag" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateQuestionTag() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateQuestionTag()
	})
}

// SetFiles sets the "files" field.
func (u *ExternalModelUsageUpsertBulk) SetFiles(v string) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetFiles(v)
	})
}

// UpdateFiles sets the "files" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateFiles() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateFiles()
	})
}

// SetMimeTypes sets the "mime_types" field.
func (u *ExternalModelUsageUpsertBulk) SetMimeTypes(v *pq.StringArray) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetMimeTypes(v)
	})
}

// UpdateMimeTypes sets the "mime_types" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateMimeTypes() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateMimeTypes()
	})
}

// SetUserID sets the "user_id" field.
func (u *ExternalModelUsageUpsertBulk) SetUserID(v int64) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *ExternalModelUsageUpsertBulk) AddUserID(v int64) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateUserID() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateUserID()
	})
}

// SetUserName sets the "user_name" field.
func (u *ExternalModelUsageUpsertBulk) SetUserName(v string) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetUserName(v)
	})
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateUserName() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateUserName()
	})
}

// SetDeptID sets the "dept_id" field.
func (u *ExternalModelUsageUpsertBulk) SetDeptID(v int64) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetDeptID(v)
	})
}

// AddDeptID adds v to the "dept_id" field.
func (u *ExternalModelUsageUpsertBulk) AddDeptID(v int64) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.AddDeptID(v)
	})
}

// UpdateDeptID sets the "dept_id" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateDeptID() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateDeptID()
	})
}

// SetDeptName sets the "dept_name" field.
func (u *ExternalModelUsageUpsertBulk) SetDeptName(v string) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetDeptName(v)
	})
}

// UpdateDeptName sets the "dept_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateDeptName() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateDeptName()
	})
}

// SetPcName sets the "pc_name" field.
func (u *ExternalModelUsageUpsertBulk) SetPcName(v string) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetPcName(v)
	})
}

// UpdatePcName sets the "pc_name" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdatePcName() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdatePcName()
	})
}

// SetHappenedAt sets the "happened_at" field.
func (u *ExternalModelUsageUpsertBulk) SetHappenedAt(v time.Time) *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.SetHappenedAt(v)
	})
}

// UpdateHappenedAt sets the "happened_at" field to the value that was provided on create.
func (u *ExternalModelUsageUpsertBulk) UpdateHappenedAt() *ExternalModelUsageUpsertBulk {
	return u.Update(func(s *ExternalModelUsageUpsert) {
		s.UpdateHappenedAt()
	})
}

// Exec executes the query.
func (u *ExternalModelUsageUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the ExternalModelUsageCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ExternalModelUsageCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ExternalModelUsageUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

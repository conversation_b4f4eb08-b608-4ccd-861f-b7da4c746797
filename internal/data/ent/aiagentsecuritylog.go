// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritylog"
)

// AiAgentSecurityLog is the model entity for the AiAgentSecurityLog schema.
type AiAgentSecurityLog struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 风险等级 1.低 2.中 3.高
	RiskLevel int64 `json:"risk_level,omitempty"`
	// 用户ID
	UserID int64 `json:"user_id,omitempty"`
	// 用户名
	UserName string `json:"user_name,omitempty"`
	// 部门ID
	DeptID int64 `json:"dept_id,omitempty"`
	// 部门名称
	DeptName string `json:"dept_name,omitempty"`
	// PC名称
	PcName string `json:"pc_name,omitempty"`
	// Agent ID
	AgentID int64 `json:"agent_id,omitempty"`
	// Agent名称
	AgentName string `json:"agent_name,omitempty"`
	// Agent描述
	AgentDescription string `json:"agent_description,omitempty"`
	// 命中策略后的操作 1.阻断 2.警告
	HitAction int64 `json:"hit_action,omitempty"`
	// 用户提问
	Question string `json:"question,omitempty"`
	// 操作分类 1.智能问答
	ActionCategory int64 `json:"action_category,omitempty"`
	// 上传文件信息
	UploadedFiles *pq.StringArray `json:"uploaded_files,omitempty"`
	// 命中策略内容
	HitPolicies  *pq.StringArray `json:"hit_policies,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AiAgentSecurityLog) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case aiagentsecuritylog.FieldUploadedFiles, aiagentsecuritylog.FieldHitPolicies:
			values[i] = new(pq.StringArray)
		case aiagentsecuritylog.FieldID, aiagentsecuritylog.FieldRiskLevel, aiagentsecuritylog.FieldUserID, aiagentsecuritylog.FieldDeptID, aiagentsecuritylog.FieldAgentID, aiagentsecuritylog.FieldHitAction, aiagentsecuritylog.FieldActionCategory:
			values[i] = new(sql.NullInt64)
		case aiagentsecuritylog.FieldUserName, aiagentsecuritylog.FieldDeptName, aiagentsecuritylog.FieldPcName, aiagentsecuritylog.FieldAgentName, aiagentsecuritylog.FieldAgentDescription, aiagentsecuritylog.FieldQuestion:
			values[i] = new(sql.NullString)
		case aiagentsecuritylog.FieldCreatedAt, aiagentsecuritylog.FieldUpdatedAt, aiagentsecuritylog.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AiAgentSecurityLog fields.
func (aasl *AiAgentSecurityLog) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case aiagentsecuritylog.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			aasl.ID = int64(value.Int64)
		case aiagentsecuritylog.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				aasl.CreatedAt = value.Time
			}
		case aiagentsecuritylog.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				aasl.UpdatedAt = value.Time
			}
		case aiagentsecuritylog.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				aasl.DeletedAt = value.Time
			}
		case aiagentsecuritylog.FieldRiskLevel:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field risk_level", values[i])
			} else if value.Valid {
				aasl.RiskLevel = value.Int64
			}
		case aiagentsecuritylog.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				aasl.UserID = value.Int64
			}
		case aiagentsecuritylog.FieldUserName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_name", values[i])
			} else if value.Valid {
				aasl.UserName = value.String
			}
		case aiagentsecuritylog.FieldDeptID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field dept_id", values[i])
			} else if value.Valid {
				aasl.DeptID = value.Int64
			}
		case aiagentsecuritylog.FieldDeptName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field dept_name", values[i])
			} else if value.Valid {
				aasl.DeptName = value.String
			}
		case aiagentsecuritylog.FieldPcName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field pc_name", values[i])
			} else if value.Valid {
				aasl.PcName = value.String
			}
		case aiagentsecuritylog.FieldAgentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field agent_id", values[i])
			} else if value.Valid {
				aasl.AgentID = value.Int64
			}
		case aiagentsecuritylog.FieldAgentName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field agent_name", values[i])
			} else if value.Valid {
				aasl.AgentName = value.String
			}
		case aiagentsecuritylog.FieldAgentDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field agent_description", values[i])
			} else if value.Valid {
				aasl.AgentDescription = value.String
			}
		case aiagentsecuritylog.FieldHitAction:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field hit_action", values[i])
			} else if value.Valid {
				aasl.HitAction = value.Int64
			}
		case aiagentsecuritylog.FieldQuestion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field question", values[i])
			} else if value.Valid {
				aasl.Question = value.String
			}
		case aiagentsecuritylog.FieldActionCategory:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field action_category", values[i])
			} else if value.Valid {
				aasl.ActionCategory = value.Int64
			}
		case aiagentsecuritylog.FieldUploadedFiles:
			if value, ok := values[i].(*pq.StringArray); !ok {
				return fmt.Errorf("unexpected type %T for field uploaded_files", values[i])
			} else if value != nil {
				aasl.UploadedFiles = value
			}
		case aiagentsecuritylog.FieldHitPolicies:
			if value, ok := values[i].(*pq.StringArray); !ok {
				return fmt.Errorf("unexpected type %T for field hit_policies", values[i])
			} else if value != nil {
				aasl.HitPolicies = value
			}
		default:
			aasl.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AiAgentSecurityLog.
// This includes values selected through modifiers, order, etc.
func (aasl *AiAgentSecurityLog) Value(name string) (ent.Value, error) {
	return aasl.selectValues.Get(name)
}

// Update returns a builder for updating this AiAgentSecurityLog.
// Note that you need to call AiAgentSecurityLog.Unwrap() before calling this method if this AiAgentSecurityLog
// was returned from a transaction, and the transaction was committed or rolled back.
func (aasl *AiAgentSecurityLog) Update() *AiAgentSecurityLogUpdateOne {
	return NewAiAgentSecurityLogClient(aasl.config).UpdateOne(aasl)
}

// Unwrap unwraps the AiAgentSecurityLog entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (aasl *AiAgentSecurityLog) Unwrap() *AiAgentSecurityLog {
	_tx, ok := aasl.config.driver.(*txDriver)
	if !ok {
		panic("ent: AiAgentSecurityLog is not a transactional entity")
	}
	aasl.config.driver = _tx.drv
	return aasl
}

// String implements the fmt.Stringer.
func (aasl *AiAgentSecurityLog) String() string {
	var builder strings.Builder
	builder.WriteString("AiAgentSecurityLog(")
	builder.WriteString(fmt.Sprintf("id=%v, ", aasl.ID))
	builder.WriteString("created_at=")
	builder.WriteString(aasl.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(aasl.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(aasl.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("risk_level=")
	builder.WriteString(fmt.Sprintf("%v", aasl.RiskLevel))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", aasl.UserID))
	builder.WriteString(", ")
	builder.WriteString("user_name=")
	builder.WriteString(aasl.UserName)
	builder.WriteString(", ")
	builder.WriteString("dept_id=")
	builder.WriteString(fmt.Sprintf("%v", aasl.DeptID))
	builder.WriteString(", ")
	builder.WriteString("dept_name=")
	builder.WriteString(aasl.DeptName)
	builder.WriteString(", ")
	builder.WriteString("pc_name=")
	builder.WriteString(aasl.PcName)
	builder.WriteString(", ")
	builder.WriteString("agent_id=")
	builder.WriteString(fmt.Sprintf("%v", aasl.AgentID))
	builder.WriteString(", ")
	builder.WriteString("agent_name=")
	builder.WriteString(aasl.AgentName)
	builder.WriteString(", ")
	builder.WriteString("agent_description=")
	builder.WriteString(aasl.AgentDescription)
	builder.WriteString(", ")
	builder.WriteString("hit_action=")
	builder.WriteString(fmt.Sprintf("%v", aasl.HitAction))
	builder.WriteString(", ")
	builder.WriteString("question=")
	builder.WriteString(aasl.Question)
	builder.WriteString(", ")
	builder.WriteString("action_category=")
	builder.WriteString(fmt.Sprintf("%v", aasl.ActionCategory))
	builder.WriteString(", ")
	builder.WriteString("uploaded_files=")
	builder.WriteString(fmt.Sprintf("%v", aasl.UploadedFiles))
	builder.WriteString(", ")
	builder.WriteString("hit_policies=")
	builder.WriteString(fmt.Sprintf("%v", aasl.HitPolicies))
	builder.WriteByte(')')
	return builder.String()
}

// AiAgentSecurityLogs is a parsable slice of AiAgentSecurityLog.
type AiAgentSecurityLogs []*AiAgentSecurityLog

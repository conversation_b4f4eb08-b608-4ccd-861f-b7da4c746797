// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodeldetail"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiModelDetailUpdate is the builder for updating AiModelDetail entities.
type AiModelDetailUpdate struct {
	config
	hooks     []Hook
	mutation  *AiModelDetailMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AiModelDetailUpdate builder.
func (amdu *AiModelDetailUpdate) Where(ps ...predicate.AiModelDetail) *AiModelDetailUpdate {
	amdu.mutation.Where(ps...)
	return amdu
}

// SetUpdatedAt sets the "updated_at" field.
func (amdu *AiModelDetailUpdate) SetUpdatedAt(t time.Time) *AiModelDetailUpdate {
	amdu.mutation.SetUpdatedAt(t)
	return amdu
}

// SetDeletedAt sets the "deleted_at" field.
func (amdu *AiModelDetailUpdate) SetDeletedAt(t time.Time) *AiModelDetailUpdate {
	amdu.mutation.SetDeletedAt(t)
	return amdu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (amdu *AiModelDetailUpdate) SetNillableDeletedAt(t *time.Time) *AiModelDetailUpdate {
	if t != nil {
		amdu.SetDeletedAt(*t)
	}
	return amdu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (amdu *AiModelDetailUpdate) ClearDeletedAt() *AiModelDetailUpdate {
	amdu.mutation.ClearDeletedAt()
	return amdu
}

// SetModelName sets the "model_name" field.
func (amdu *AiModelDetailUpdate) SetModelName(s string) *AiModelDetailUpdate {
	amdu.mutation.SetModelName(s)
	return amdu
}

// SetNillableModelName sets the "model_name" field if the given value is not nil.
func (amdu *AiModelDetailUpdate) SetNillableModelName(s *string) *AiModelDetailUpdate {
	if s != nil {
		amdu.SetModelName(*s)
	}
	return amdu
}

// SetName sets the "name" field.
func (amdu *AiModelDetailUpdate) SetName(s string) *AiModelDetailUpdate {
	amdu.mutation.SetName(s)
	return amdu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (amdu *AiModelDetailUpdate) SetNillableName(s *string) *AiModelDetailUpdate {
	if s != nil {
		amdu.SetName(*s)
	}
	return amdu
}

// SetURL sets the "url" field.
func (amdu *AiModelDetailUpdate) SetURL(s string) *AiModelDetailUpdate {
	amdu.mutation.SetURL(s)
	return amdu
}

// SetNillableURL sets the "url" field if the given value is not nil.
func (amdu *AiModelDetailUpdate) SetNillableURL(s *string) *AiModelDetailUpdate {
	if s != nil {
		amdu.SetURL(*s)
	}
	return amdu
}

// SetAvatar sets the "avatar" field.
func (amdu *AiModelDetailUpdate) SetAvatar(s string) *AiModelDetailUpdate {
	amdu.mutation.SetAvatar(s)
	return amdu
}

// SetNillableAvatar sets the "avatar" field if the given value is not nil.
func (amdu *AiModelDetailUpdate) SetNillableAvatar(s *string) *AiModelDetailUpdate {
	if s != nil {
		amdu.SetAvatar(*s)
	}
	return amdu
}

// SetCanInternetSearch sets the "can_internet_search" field.
func (amdu *AiModelDetailUpdate) SetCanInternetSearch(b bool) *AiModelDetailUpdate {
	amdu.mutation.SetCanInternetSearch(b)
	return amdu
}

// SetNillableCanInternetSearch sets the "can_internet_search" field if the given value is not nil.
func (amdu *AiModelDetailUpdate) SetNillableCanInternetSearch(b *bool) *AiModelDetailUpdate {
	if b != nil {
		amdu.SetCanInternetSearch(*b)
	}
	return amdu
}

// SetBalanceSearchURL sets the "balance_search_url" field.
func (amdu *AiModelDetailUpdate) SetBalanceSearchURL(s string) *AiModelDetailUpdate {
	amdu.mutation.SetBalanceSearchURL(s)
	return amdu
}

// SetNillableBalanceSearchURL sets the "balance_search_url" field if the given value is not nil.
func (amdu *AiModelDetailUpdate) SetNillableBalanceSearchURL(s *string) *AiModelDetailUpdate {
	if s != nil {
		amdu.SetBalanceSearchURL(*s)
	}
	return amdu
}

// SetThinkingEnableStatus sets the "thinking_enable_status" field.
func (amdu *AiModelDetailUpdate) SetThinkingEnableStatus(i int64) *AiModelDetailUpdate {
	amdu.mutation.ResetThinkingEnableStatus()
	amdu.mutation.SetThinkingEnableStatus(i)
	return amdu
}

// SetNillableThinkingEnableStatus sets the "thinking_enable_status" field if the given value is not nil.
func (amdu *AiModelDetailUpdate) SetNillableThinkingEnableStatus(i *int64) *AiModelDetailUpdate {
	if i != nil {
		amdu.SetThinkingEnableStatus(*i)
	}
	return amdu
}

// AddThinkingEnableStatus adds i to the "thinking_enable_status" field.
func (amdu *AiModelDetailUpdate) AddThinkingEnableStatus(i int64) *AiModelDetailUpdate {
	amdu.mutation.AddThinkingEnableStatus(i)
	return amdu
}

// SetBackgroundURL sets the "background_url" field.
func (amdu *AiModelDetailUpdate) SetBackgroundURL(s string) *AiModelDetailUpdate {
	amdu.mutation.SetBackgroundURL(s)
	return amdu
}

// SetNillableBackgroundURL sets the "background_url" field if the given value is not nil.
func (amdu *AiModelDetailUpdate) SetNillableBackgroundURL(s *string) *AiModelDetailUpdate {
	if s != nil {
		amdu.SetBackgroundURL(*s)
	}
	return amdu
}

// Mutation returns the AiModelDetailMutation object of the builder.
func (amdu *AiModelDetailUpdate) Mutation() *AiModelDetailMutation {
	return amdu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (amdu *AiModelDetailUpdate) Save(ctx context.Context) (int, error) {
	if err := amdu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, amdu.sqlSave, amdu.mutation, amdu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (amdu *AiModelDetailUpdate) SaveX(ctx context.Context) int {
	affected, err := amdu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (amdu *AiModelDetailUpdate) Exec(ctx context.Context) error {
	_, err := amdu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amdu *AiModelDetailUpdate) ExecX(ctx context.Context) {
	if err := amdu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (amdu *AiModelDetailUpdate) defaults() error {
	if _, ok := amdu.mutation.UpdatedAt(); !ok {
		if aimodeldetail.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodeldetail.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aimodeldetail.UpdateDefaultUpdatedAt()
		amdu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (amdu *AiModelDetailUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiModelDetailUpdate {
	amdu.modifiers = append(amdu.modifiers, modifiers...)
	return amdu
}

func (amdu *AiModelDetailUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(aimodeldetail.Table, aimodeldetail.Columns, sqlgraph.NewFieldSpec(aimodeldetail.FieldID, field.TypeInt64))
	if ps := amdu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := amdu.mutation.UpdatedAt(); ok {
		_spec.SetField(aimodeldetail.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := amdu.mutation.DeletedAt(); ok {
		_spec.SetField(aimodeldetail.FieldDeletedAt, field.TypeTime, value)
	}
	if amdu.mutation.DeletedAtCleared() {
		_spec.ClearField(aimodeldetail.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := amdu.mutation.ModelName(); ok {
		_spec.SetField(aimodeldetail.FieldModelName, field.TypeString, value)
	}
	if value, ok := amdu.mutation.Name(); ok {
		_spec.SetField(aimodeldetail.FieldName, field.TypeString, value)
	}
	if value, ok := amdu.mutation.URL(); ok {
		_spec.SetField(aimodeldetail.FieldURL, field.TypeString, value)
	}
	if value, ok := amdu.mutation.Avatar(); ok {
		_spec.SetField(aimodeldetail.FieldAvatar, field.TypeString, value)
	}
	if value, ok := amdu.mutation.CanInternetSearch(); ok {
		_spec.SetField(aimodeldetail.FieldCanInternetSearch, field.TypeBool, value)
	}
	if value, ok := amdu.mutation.BalanceSearchURL(); ok {
		_spec.SetField(aimodeldetail.FieldBalanceSearchURL, field.TypeString, value)
	}
	if value, ok := amdu.mutation.ThinkingEnableStatus(); ok {
		_spec.SetField(aimodeldetail.FieldThinkingEnableStatus, field.TypeInt64, value)
	}
	if value, ok := amdu.mutation.AddedThinkingEnableStatus(); ok {
		_spec.AddField(aimodeldetail.FieldThinkingEnableStatus, field.TypeInt64, value)
	}
	if value, ok := amdu.mutation.BackgroundURL(); ok {
		_spec.SetField(aimodeldetail.FieldBackgroundURL, field.TypeString, value)
	}
	_spec.AddModifiers(amdu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, amdu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aimodeldetail.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	amdu.mutation.done = true
	return n, nil
}

// AiModelDetailUpdateOne is the builder for updating a single AiModelDetail entity.
type AiModelDetailUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AiModelDetailMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (amduo *AiModelDetailUpdateOne) SetUpdatedAt(t time.Time) *AiModelDetailUpdateOne {
	amduo.mutation.SetUpdatedAt(t)
	return amduo
}

// SetDeletedAt sets the "deleted_at" field.
func (amduo *AiModelDetailUpdateOne) SetDeletedAt(t time.Time) *AiModelDetailUpdateOne {
	amduo.mutation.SetDeletedAt(t)
	return amduo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (amduo *AiModelDetailUpdateOne) SetNillableDeletedAt(t *time.Time) *AiModelDetailUpdateOne {
	if t != nil {
		amduo.SetDeletedAt(*t)
	}
	return amduo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (amduo *AiModelDetailUpdateOne) ClearDeletedAt() *AiModelDetailUpdateOne {
	amduo.mutation.ClearDeletedAt()
	return amduo
}

// SetModelName sets the "model_name" field.
func (amduo *AiModelDetailUpdateOne) SetModelName(s string) *AiModelDetailUpdateOne {
	amduo.mutation.SetModelName(s)
	return amduo
}

// SetNillableModelName sets the "model_name" field if the given value is not nil.
func (amduo *AiModelDetailUpdateOne) SetNillableModelName(s *string) *AiModelDetailUpdateOne {
	if s != nil {
		amduo.SetModelName(*s)
	}
	return amduo
}

// SetName sets the "name" field.
func (amduo *AiModelDetailUpdateOne) SetName(s string) *AiModelDetailUpdateOne {
	amduo.mutation.SetName(s)
	return amduo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (amduo *AiModelDetailUpdateOne) SetNillableName(s *string) *AiModelDetailUpdateOne {
	if s != nil {
		amduo.SetName(*s)
	}
	return amduo
}

// SetURL sets the "url" field.
func (amduo *AiModelDetailUpdateOne) SetURL(s string) *AiModelDetailUpdateOne {
	amduo.mutation.SetURL(s)
	return amduo
}

// SetNillableURL sets the "url" field if the given value is not nil.
func (amduo *AiModelDetailUpdateOne) SetNillableURL(s *string) *AiModelDetailUpdateOne {
	if s != nil {
		amduo.SetURL(*s)
	}
	return amduo
}

// SetAvatar sets the "avatar" field.
func (amduo *AiModelDetailUpdateOne) SetAvatar(s string) *AiModelDetailUpdateOne {
	amduo.mutation.SetAvatar(s)
	return amduo
}

// SetNillableAvatar sets the "avatar" field if the given value is not nil.
func (amduo *AiModelDetailUpdateOne) SetNillableAvatar(s *string) *AiModelDetailUpdateOne {
	if s != nil {
		amduo.SetAvatar(*s)
	}
	return amduo
}

// SetCanInternetSearch sets the "can_internet_search" field.
func (amduo *AiModelDetailUpdateOne) SetCanInternetSearch(b bool) *AiModelDetailUpdateOne {
	amduo.mutation.SetCanInternetSearch(b)
	return amduo
}

// SetNillableCanInternetSearch sets the "can_internet_search" field if the given value is not nil.
func (amduo *AiModelDetailUpdateOne) SetNillableCanInternetSearch(b *bool) *AiModelDetailUpdateOne {
	if b != nil {
		amduo.SetCanInternetSearch(*b)
	}
	return amduo
}

// SetBalanceSearchURL sets the "balance_search_url" field.
func (amduo *AiModelDetailUpdateOne) SetBalanceSearchURL(s string) *AiModelDetailUpdateOne {
	amduo.mutation.SetBalanceSearchURL(s)
	return amduo
}

// SetNillableBalanceSearchURL sets the "balance_search_url" field if the given value is not nil.
func (amduo *AiModelDetailUpdateOne) SetNillableBalanceSearchURL(s *string) *AiModelDetailUpdateOne {
	if s != nil {
		amduo.SetBalanceSearchURL(*s)
	}
	return amduo
}

// SetThinkingEnableStatus sets the "thinking_enable_status" field.
func (amduo *AiModelDetailUpdateOne) SetThinkingEnableStatus(i int64) *AiModelDetailUpdateOne {
	amduo.mutation.ResetThinkingEnableStatus()
	amduo.mutation.SetThinkingEnableStatus(i)
	return amduo
}

// SetNillableThinkingEnableStatus sets the "thinking_enable_status" field if the given value is not nil.
func (amduo *AiModelDetailUpdateOne) SetNillableThinkingEnableStatus(i *int64) *AiModelDetailUpdateOne {
	if i != nil {
		amduo.SetThinkingEnableStatus(*i)
	}
	return amduo
}

// AddThinkingEnableStatus adds i to the "thinking_enable_status" field.
func (amduo *AiModelDetailUpdateOne) AddThinkingEnableStatus(i int64) *AiModelDetailUpdateOne {
	amduo.mutation.AddThinkingEnableStatus(i)
	return amduo
}

// SetBackgroundURL sets the "background_url" field.
func (amduo *AiModelDetailUpdateOne) SetBackgroundURL(s string) *AiModelDetailUpdateOne {
	amduo.mutation.SetBackgroundURL(s)
	return amduo
}

// SetNillableBackgroundURL sets the "background_url" field if the given value is not nil.
func (amduo *AiModelDetailUpdateOne) SetNillableBackgroundURL(s *string) *AiModelDetailUpdateOne {
	if s != nil {
		amduo.SetBackgroundURL(*s)
	}
	return amduo
}

// Mutation returns the AiModelDetailMutation object of the builder.
func (amduo *AiModelDetailUpdateOne) Mutation() *AiModelDetailMutation {
	return amduo.mutation
}

// Where appends a list predicates to the AiModelDetailUpdate builder.
func (amduo *AiModelDetailUpdateOne) Where(ps ...predicate.AiModelDetail) *AiModelDetailUpdateOne {
	amduo.mutation.Where(ps...)
	return amduo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (amduo *AiModelDetailUpdateOne) Select(field string, fields ...string) *AiModelDetailUpdateOne {
	amduo.fields = append([]string{field}, fields...)
	return amduo
}

// Save executes the query and returns the updated AiModelDetail entity.
func (amduo *AiModelDetailUpdateOne) Save(ctx context.Context) (*AiModelDetail, error) {
	if err := amduo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, amduo.sqlSave, amduo.mutation, amduo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (amduo *AiModelDetailUpdateOne) SaveX(ctx context.Context) *AiModelDetail {
	node, err := amduo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (amduo *AiModelDetailUpdateOne) Exec(ctx context.Context) error {
	_, err := amduo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amduo *AiModelDetailUpdateOne) ExecX(ctx context.Context) {
	if err := amduo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (amduo *AiModelDetailUpdateOne) defaults() error {
	if _, ok := amduo.mutation.UpdatedAt(); !ok {
		if aimodeldetail.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodeldetail.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aimodeldetail.UpdateDefaultUpdatedAt()
		amduo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (amduo *AiModelDetailUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiModelDetailUpdateOne {
	amduo.modifiers = append(amduo.modifiers, modifiers...)
	return amduo
}

func (amduo *AiModelDetailUpdateOne) sqlSave(ctx context.Context) (_node *AiModelDetail, err error) {
	_spec := sqlgraph.NewUpdateSpec(aimodeldetail.Table, aimodeldetail.Columns, sqlgraph.NewFieldSpec(aimodeldetail.FieldID, field.TypeInt64))
	id, ok := amduo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AiModelDetail.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := amduo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aimodeldetail.FieldID)
		for _, f := range fields {
			if !aimodeldetail.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != aimodeldetail.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := amduo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := amduo.mutation.UpdatedAt(); ok {
		_spec.SetField(aimodeldetail.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := amduo.mutation.DeletedAt(); ok {
		_spec.SetField(aimodeldetail.FieldDeletedAt, field.TypeTime, value)
	}
	if amduo.mutation.DeletedAtCleared() {
		_spec.ClearField(aimodeldetail.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := amduo.mutation.ModelName(); ok {
		_spec.SetField(aimodeldetail.FieldModelName, field.TypeString, value)
	}
	if value, ok := amduo.mutation.Name(); ok {
		_spec.SetField(aimodeldetail.FieldName, field.TypeString, value)
	}
	if value, ok := amduo.mutation.URL(); ok {
		_spec.SetField(aimodeldetail.FieldURL, field.TypeString, value)
	}
	if value, ok := amduo.mutation.Avatar(); ok {
		_spec.SetField(aimodeldetail.FieldAvatar, field.TypeString, value)
	}
	if value, ok := amduo.mutation.CanInternetSearch(); ok {
		_spec.SetField(aimodeldetail.FieldCanInternetSearch, field.TypeBool, value)
	}
	if value, ok := amduo.mutation.BalanceSearchURL(); ok {
		_spec.SetField(aimodeldetail.FieldBalanceSearchURL, field.TypeString, value)
	}
	if value, ok := amduo.mutation.ThinkingEnableStatus(); ok {
		_spec.SetField(aimodeldetail.FieldThinkingEnableStatus, field.TypeInt64, value)
	}
	if value, ok := amduo.mutation.AddedThinkingEnableStatus(); ok {
		_spec.AddField(aimodeldetail.FieldThinkingEnableStatus, field.TypeInt64, value)
	}
	if value, ok := amduo.mutation.BackgroundURL(); ok {
		_spec.SetField(aimodeldetail.FieldBackgroundURL, field.TypeString, value)
	}
	_spec.AddModifiers(amduo.modifiers...)
	_node = &AiModelDetail{config: amduo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, amduo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aimodeldetail.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	amduo.mutation.done = true
	return _node, nil
}

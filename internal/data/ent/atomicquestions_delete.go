// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/atomicquestions"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AtomicQuestionsDelete is the builder for deleting a AtomicQuestions entity.
type AtomicQuestionsDelete struct {
	config
	hooks    []Hook
	mutation *AtomicQuestionsMutation
}

// Where appends a list predicates to the AtomicQuestionsDelete builder.
func (aqd *AtomicQuestionsDelete) Where(ps ...predicate.AtomicQuestions) *AtomicQuestionsDelete {
	aqd.mutation.Where(ps...)
	return aqd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (aqd *AtomicQuestionsDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, aqd.sqlExec, aqd.mutation, aqd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (aqd *AtomicQuestionsDelete) ExecX(ctx context.Context) int {
	n, err := aqd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (aqd *AtomicQuestionsDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(atomicquestions.Table, sqlgraph.NewFieldSpec(atomicquestions.FieldID, field.TypeInt64))
	if ps := aqd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, aqd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	aqd.mutation.done = true
	return affected, err
}

// AtomicQuestionsDeleteOne is the builder for deleting a single AtomicQuestions entity.
type AtomicQuestionsDeleteOne struct {
	aqd *AtomicQuestionsDelete
}

// Where appends a list predicates to the AtomicQuestionsDelete builder.
func (aqdo *AtomicQuestionsDeleteOne) Where(ps ...predicate.AtomicQuestions) *AtomicQuestionsDeleteOne {
	aqdo.aqd.mutation.Where(ps...)
	return aqdo
}

// Exec executes the deletion query.
func (aqdo *AtomicQuestionsDeleteOne) Exec(ctx context.Context) error {
	n, err := aqdo.aqd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{atomicquestions.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (aqdo *AtomicQuestionsDeleteOne) ExecX(ctx context.Context) {
	if err := aqdo.Exec(ctx); err != nil {
		panic(err)
	}
}

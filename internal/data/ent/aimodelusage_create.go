// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodelusage"
)

// AiModelUsageCreate is the builder for creating a AiModelUsage entity.
type AiModelUsageCreate struct {
	config
	mutation *AiModelUsageMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (amuc *AiModelUsageCreate) SetCreatedAt(t time.Time) *AiModelUsageCreate {
	amuc.mutation.SetCreatedAt(t)
	return amuc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (amuc *AiModelUsageCreate) SetNillableCreatedAt(t *time.Time) *AiModelUsageCreate {
	if t != nil {
		amuc.SetCreatedAt(*t)
	}
	return amuc
}

// SetUpdatedAt sets the "updated_at" field.
func (amuc *AiModelUsageCreate) SetUpdatedAt(t time.Time) *AiModelUsageCreate {
	amuc.mutation.SetUpdatedAt(t)
	return amuc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (amuc *AiModelUsageCreate) SetNillableUpdatedAt(t *time.Time) *AiModelUsageCreate {
	if t != nil {
		amuc.SetUpdatedAt(*t)
	}
	return amuc
}

// SetDeletedAt sets the "deleted_at" field.
func (amuc *AiModelUsageCreate) SetDeletedAt(t time.Time) *AiModelUsageCreate {
	amuc.mutation.SetDeletedAt(t)
	return amuc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (amuc *AiModelUsageCreate) SetNillableDeletedAt(t *time.Time) *AiModelUsageCreate {
	if t != nil {
		amuc.SetDeletedAt(*t)
	}
	return amuc
}

// SetModelDetailID sets the "model_detail_id" field.
func (amuc *AiModelUsageCreate) SetModelDetailID(i int64) *AiModelUsageCreate {
	amuc.mutation.SetModelDetailID(i)
	return amuc
}

// SetModelName sets the "model_name" field.
func (amuc *AiModelUsageCreate) SetModelName(s string) *AiModelUsageCreate {
	amuc.mutation.SetModelName(s)
	return amuc
}

// SetModelGatewayName sets the "model_gateway_name" field.
func (amuc *AiModelUsageCreate) SetModelGatewayName(s string) *AiModelUsageCreate {
	amuc.mutation.SetModelGatewayName(s)
	return amuc
}

// SetAgentID sets the "agent_id" field.
func (amuc *AiModelUsageCreate) SetAgentID(i int64) *AiModelUsageCreate {
	amuc.mutation.SetAgentID(i)
	return amuc
}

// SetAgentName sets the "agent_name" field.
func (amuc *AiModelUsageCreate) SetAgentName(s string) *AiModelUsageCreate {
	amuc.mutation.SetAgentName(s)
	return amuc
}

// SetUserID sets the "user_id" field.
func (amuc *AiModelUsageCreate) SetUserID(i int64) *AiModelUsageCreate {
	amuc.mutation.SetUserID(i)
	return amuc
}

// SetUserName sets the "user_name" field.
func (amuc *AiModelUsageCreate) SetUserName(s string) *AiModelUsageCreate {
	amuc.mutation.SetUserName(s)
	return amuc
}

// SetQuestion sets the "question" field.
func (amuc *AiModelUsageCreate) SetQuestion(s string) *AiModelUsageCreate {
	amuc.mutation.SetQuestion(s)
	return amuc
}

// SetAnswer sets the "answer" field.
func (amuc *AiModelUsageCreate) SetAnswer(s string) *AiModelUsageCreate {
	amuc.mutation.SetAnswer(s)
	return amuc
}

// SetPromptTokens sets the "prompt_tokens" field.
func (amuc *AiModelUsageCreate) SetPromptTokens(i int64) *AiModelUsageCreate {
	amuc.mutation.SetPromptTokens(i)
	return amuc
}

// SetCompletionTokens sets the "completion_tokens" field.
func (amuc *AiModelUsageCreate) SetCompletionTokens(i int64) *AiModelUsageCreate {
	amuc.mutation.SetCompletionTokens(i)
	return amuc
}

// SetRequestStatus sets the "request_status" field.
func (amuc *AiModelUsageCreate) SetRequestStatus(i int8) *AiModelUsageCreate {
	amuc.mutation.SetRequestStatus(i)
	return amuc
}

// SetNillableRequestStatus sets the "request_status" field if the given value is not nil.
func (amuc *AiModelUsageCreate) SetNillableRequestStatus(i *int8) *AiModelUsageCreate {
	if i != nil {
		amuc.SetRequestStatus(*i)
	}
	return amuc
}

// SetErrorCode sets the "error_code" field.
func (amuc *AiModelUsageCreate) SetErrorCode(s string) *AiModelUsageCreate {
	amuc.mutation.SetErrorCode(s)
	return amuc
}

// SetNillableErrorCode sets the "error_code" field if the given value is not nil.
func (amuc *AiModelUsageCreate) SetNillableErrorCode(s *string) *AiModelUsageCreate {
	if s != nil {
		amuc.SetErrorCode(*s)
	}
	return amuc
}

// SetID sets the "id" field.
func (amuc *AiModelUsageCreate) SetID(i int64) *AiModelUsageCreate {
	amuc.mutation.SetID(i)
	return amuc
}

// Mutation returns the AiModelUsageMutation object of the builder.
func (amuc *AiModelUsageCreate) Mutation() *AiModelUsageMutation {
	return amuc.mutation
}

// Save creates the AiModelUsage in the database.
func (amuc *AiModelUsageCreate) Save(ctx context.Context) (*AiModelUsage, error) {
	if err := amuc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, amuc.sqlSave, amuc.mutation, amuc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (amuc *AiModelUsageCreate) SaveX(ctx context.Context) *AiModelUsage {
	v, err := amuc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (amuc *AiModelUsageCreate) Exec(ctx context.Context) error {
	_, err := amuc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amuc *AiModelUsageCreate) ExecX(ctx context.Context) {
	if err := amuc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (amuc *AiModelUsageCreate) defaults() error {
	if _, ok := amuc.mutation.CreatedAt(); !ok {
		if aimodelusage.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodelusage.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := aimodelusage.DefaultCreatedAt()
		amuc.mutation.SetCreatedAt(v)
	}
	if _, ok := amuc.mutation.UpdatedAt(); !ok {
		if aimodelusage.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodelusage.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aimodelusage.DefaultUpdatedAt()
		amuc.mutation.SetUpdatedAt(v)
	}
	if _, ok := amuc.mutation.DeletedAt(); !ok {
		v := aimodelusage.DefaultDeletedAt
		amuc.mutation.SetDeletedAt(v)
	}
	if _, ok := amuc.mutation.RequestStatus(); !ok {
		v := aimodelusage.DefaultRequestStatus
		amuc.mutation.SetRequestStatus(v)
	}
	if _, ok := amuc.mutation.ErrorCode(); !ok {
		v := aimodelusage.DefaultErrorCode
		amuc.mutation.SetErrorCode(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (amuc *AiModelUsageCreate) check() error {
	if _, ok := amuc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AiModelUsage.created_at"`)}
	}
	if _, ok := amuc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AiModelUsage.updated_at"`)}
	}
	if _, ok := amuc.mutation.ModelDetailID(); !ok {
		return &ValidationError{Name: "model_detail_id", err: errors.New(`ent: missing required field "AiModelUsage.model_detail_id"`)}
	}
	if _, ok := amuc.mutation.ModelName(); !ok {
		return &ValidationError{Name: "model_name", err: errors.New(`ent: missing required field "AiModelUsage.model_name"`)}
	}
	if _, ok := amuc.mutation.ModelGatewayName(); !ok {
		return &ValidationError{Name: "model_gateway_name", err: errors.New(`ent: missing required field "AiModelUsage.model_gateway_name"`)}
	}
	if _, ok := amuc.mutation.AgentID(); !ok {
		return &ValidationError{Name: "agent_id", err: errors.New(`ent: missing required field "AiModelUsage.agent_id"`)}
	}
	if _, ok := amuc.mutation.AgentName(); !ok {
		return &ValidationError{Name: "agent_name", err: errors.New(`ent: missing required field "AiModelUsage.agent_name"`)}
	}
	if _, ok := amuc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "AiModelUsage.user_id"`)}
	}
	if _, ok := amuc.mutation.UserName(); !ok {
		return &ValidationError{Name: "user_name", err: errors.New(`ent: missing required field "AiModelUsage.user_name"`)}
	}
	if _, ok := amuc.mutation.Question(); !ok {
		return &ValidationError{Name: "question", err: errors.New(`ent: missing required field "AiModelUsage.question"`)}
	}
	if _, ok := amuc.mutation.Answer(); !ok {
		return &ValidationError{Name: "answer", err: errors.New(`ent: missing required field "AiModelUsage.answer"`)}
	}
	if _, ok := amuc.mutation.PromptTokens(); !ok {
		return &ValidationError{Name: "prompt_tokens", err: errors.New(`ent: missing required field "AiModelUsage.prompt_tokens"`)}
	}
	if _, ok := amuc.mutation.CompletionTokens(); !ok {
		return &ValidationError{Name: "completion_tokens", err: errors.New(`ent: missing required field "AiModelUsage.completion_tokens"`)}
	}
	if _, ok := amuc.mutation.RequestStatus(); !ok {
		return &ValidationError{Name: "request_status", err: errors.New(`ent: missing required field "AiModelUsage.request_status"`)}
	}
	if _, ok := amuc.mutation.ErrorCode(); !ok {
		return &ValidationError{Name: "error_code", err: errors.New(`ent: missing required field "AiModelUsage.error_code"`)}
	}
	return nil
}

func (amuc *AiModelUsageCreate) sqlSave(ctx context.Context) (*AiModelUsage, error) {
	if err := amuc.check(); err != nil {
		return nil, err
	}
	_node, _spec := amuc.createSpec()
	if err := sqlgraph.CreateNode(ctx, amuc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	amuc.mutation.id = &_node.ID
	amuc.mutation.done = true
	return _node, nil
}

func (amuc *AiModelUsageCreate) createSpec() (*AiModelUsage, *sqlgraph.CreateSpec) {
	var (
		_node = &AiModelUsage{config: amuc.config}
		_spec = sqlgraph.NewCreateSpec(aimodelusage.Table, sqlgraph.NewFieldSpec(aimodelusage.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = amuc.conflict
	if id, ok := amuc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := amuc.mutation.CreatedAt(); ok {
		_spec.SetField(aimodelusage.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := amuc.mutation.UpdatedAt(); ok {
		_spec.SetField(aimodelusage.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := amuc.mutation.DeletedAt(); ok {
		_spec.SetField(aimodelusage.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := amuc.mutation.ModelDetailID(); ok {
		_spec.SetField(aimodelusage.FieldModelDetailID, field.TypeInt64, value)
		_node.ModelDetailID = value
	}
	if value, ok := amuc.mutation.ModelName(); ok {
		_spec.SetField(aimodelusage.FieldModelName, field.TypeString, value)
		_node.ModelName = value
	}
	if value, ok := amuc.mutation.ModelGatewayName(); ok {
		_spec.SetField(aimodelusage.FieldModelGatewayName, field.TypeString, value)
		_node.ModelGatewayName = value
	}
	if value, ok := amuc.mutation.AgentID(); ok {
		_spec.SetField(aimodelusage.FieldAgentID, field.TypeInt64, value)
		_node.AgentID = value
	}
	if value, ok := amuc.mutation.AgentName(); ok {
		_spec.SetField(aimodelusage.FieldAgentName, field.TypeString, value)
		_node.AgentName = value
	}
	if value, ok := amuc.mutation.UserID(); ok {
		_spec.SetField(aimodelusage.FieldUserID, field.TypeInt64, value)
		_node.UserID = value
	}
	if value, ok := amuc.mutation.UserName(); ok {
		_spec.SetField(aimodelusage.FieldUserName, field.TypeString, value)
		_node.UserName = value
	}
	if value, ok := amuc.mutation.Question(); ok {
		_spec.SetField(aimodelusage.FieldQuestion, field.TypeString, value)
		_node.Question = value
	}
	if value, ok := amuc.mutation.Answer(); ok {
		_spec.SetField(aimodelusage.FieldAnswer, field.TypeString, value)
		_node.Answer = value
	}
	if value, ok := amuc.mutation.PromptTokens(); ok {
		_spec.SetField(aimodelusage.FieldPromptTokens, field.TypeInt64, value)
		_node.PromptTokens = value
	}
	if value, ok := amuc.mutation.CompletionTokens(); ok {
		_spec.SetField(aimodelusage.FieldCompletionTokens, field.TypeInt64, value)
		_node.CompletionTokens = value
	}
	if value, ok := amuc.mutation.RequestStatus(); ok {
		_spec.SetField(aimodelusage.FieldRequestStatus, field.TypeInt8, value)
		_node.RequestStatus = value
	}
	if value, ok := amuc.mutation.ErrorCode(); ok {
		_spec.SetField(aimodelusage.FieldErrorCode, field.TypeString, value)
		_node.ErrorCode = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiModelUsage.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiModelUsageUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (amuc *AiModelUsageCreate) OnConflict(opts ...sql.ConflictOption) *AiModelUsageUpsertOne {
	amuc.conflict = opts
	return &AiModelUsageUpsertOne{
		create: amuc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiModelUsage.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (amuc *AiModelUsageCreate) OnConflictColumns(columns ...string) *AiModelUsageUpsertOne {
	amuc.conflict = append(amuc.conflict, sql.ConflictColumns(columns...))
	return &AiModelUsageUpsertOne{
		create: amuc,
	}
}

type (
	// AiModelUsageUpsertOne is the builder for "upsert"-ing
	//  one AiModelUsage node.
	AiModelUsageUpsertOne struct {
		create *AiModelUsageCreate
	}

	// AiModelUsageUpsert is the "OnConflict" setter.
	AiModelUsageUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *AiModelUsageUpsert) SetUpdatedAt(v time.Time) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateUpdatedAt() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldUpdatedAt)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiModelUsageUpsert) SetDeletedAt(v time.Time) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateDeletedAt() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiModelUsageUpsert) ClearDeletedAt() *AiModelUsageUpsert {
	u.SetNull(aimodelusage.FieldDeletedAt)
	return u
}

// SetModelDetailID sets the "model_detail_id" field.
func (u *AiModelUsageUpsert) SetModelDetailID(v int64) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldModelDetailID, v)
	return u
}

// UpdateModelDetailID sets the "model_detail_id" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateModelDetailID() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldModelDetailID)
	return u
}

// AddModelDetailID adds v to the "model_detail_id" field.
func (u *AiModelUsageUpsert) AddModelDetailID(v int64) *AiModelUsageUpsert {
	u.Add(aimodelusage.FieldModelDetailID, v)
	return u
}

// SetModelName sets the "model_name" field.
func (u *AiModelUsageUpsert) SetModelName(v string) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldModelName, v)
	return u
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateModelName() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldModelName)
	return u
}

// SetModelGatewayName sets the "model_gateway_name" field.
func (u *AiModelUsageUpsert) SetModelGatewayName(v string) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldModelGatewayName, v)
	return u
}

// UpdateModelGatewayName sets the "model_gateway_name" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateModelGatewayName() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldModelGatewayName)
	return u
}

// SetAgentID sets the "agent_id" field.
func (u *AiModelUsageUpsert) SetAgentID(v int64) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldAgentID, v)
	return u
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateAgentID() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldAgentID)
	return u
}

// AddAgentID adds v to the "agent_id" field.
func (u *AiModelUsageUpsert) AddAgentID(v int64) *AiModelUsageUpsert {
	u.Add(aimodelusage.FieldAgentID, v)
	return u
}

// SetAgentName sets the "agent_name" field.
func (u *AiModelUsageUpsert) SetAgentName(v string) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldAgentName, v)
	return u
}

// UpdateAgentName sets the "agent_name" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateAgentName() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldAgentName)
	return u
}

// SetUserID sets the "user_id" field.
func (u *AiModelUsageUpsert) SetUserID(v int64) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateUserID() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldUserID)
	return u
}

// AddUserID adds v to the "user_id" field.
func (u *AiModelUsageUpsert) AddUserID(v int64) *AiModelUsageUpsert {
	u.Add(aimodelusage.FieldUserID, v)
	return u
}

// SetUserName sets the "user_name" field.
func (u *AiModelUsageUpsert) SetUserName(v string) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldUserName, v)
	return u
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateUserName() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldUserName)
	return u
}

// SetQuestion sets the "question" field.
func (u *AiModelUsageUpsert) SetQuestion(v string) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldQuestion, v)
	return u
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateQuestion() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldQuestion)
	return u
}

// SetAnswer sets the "answer" field.
func (u *AiModelUsageUpsert) SetAnswer(v string) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldAnswer, v)
	return u
}

// UpdateAnswer sets the "answer" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateAnswer() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldAnswer)
	return u
}

// SetPromptTokens sets the "prompt_tokens" field.
func (u *AiModelUsageUpsert) SetPromptTokens(v int64) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldPromptTokens, v)
	return u
}

// UpdatePromptTokens sets the "prompt_tokens" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdatePromptTokens() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldPromptTokens)
	return u
}

// AddPromptTokens adds v to the "prompt_tokens" field.
func (u *AiModelUsageUpsert) AddPromptTokens(v int64) *AiModelUsageUpsert {
	u.Add(aimodelusage.FieldPromptTokens, v)
	return u
}

// SetCompletionTokens sets the "completion_tokens" field.
func (u *AiModelUsageUpsert) SetCompletionTokens(v int64) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldCompletionTokens, v)
	return u
}

// UpdateCompletionTokens sets the "completion_tokens" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateCompletionTokens() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldCompletionTokens)
	return u
}

// AddCompletionTokens adds v to the "completion_tokens" field.
func (u *AiModelUsageUpsert) AddCompletionTokens(v int64) *AiModelUsageUpsert {
	u.Add(aimodelusage.FieldCompletionTokens, v)
	return u
}

// SetRequestStatus sets the "request_status" field.
func (u *AiModelUsageUpsert) SetRequestStatus(v int8) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldRequestStatus, v)
	return u
}

// UpdateRequestStatus sets the "request_status" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateRequestStatus() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldRequestStatus)
	return u
}

// AddRequestStatus adds v to the "request_status" field.
func (u *AiModelUsageUpsert) AddRequestStatus(v int8) *AiModelUsageUpsert {
	u.Add(aimodelusage.FieldRequestStatus, v)
	return u
}

// SetErrorCode sets the "error_code" field.
func (u *AiModelUsageUpsert) SetErrorCode(v string) *AiModelUsageUpsert {
	u.Set(aimodelusage.FieldErrorCode, v)
	return u
}

// UpdateErrorCode sets the "error_code" field to the value that was provided on create.
func (u *AiModelUsageUpsert) UpdateErrorCode() *AiModelUsageUpsert {
	u.SetExcluded(aimodelusage.FieldErrorCode)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.AiModelUsage.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aimodelusage.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiModelUsageUpsertOne) UpdateNewValues() *AiModelUsageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(aimodelusage.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(aimodelusage.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiModelUsage.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *AiModelUsageUpsertOne) Ignore() *AiModelUsageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiModelUsageUpsertOne) DoNothing() *AiModelUsageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiModelUsageCreate.OnConflict
// documentation for more info.
func (u *AiModelUsageUpsertOne) Update(set func(*AiModelUsageUpsert)) *AiModelUsageUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiModelUsageUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiModelUsageUpsertOne) SetUpdatedAt(v time.Time) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateUpdatedAt() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiModelUsageUpsertOne) SetDeletedAt(v time.Time) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateDeletedAt() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiModelUsageUpsertOne) ClearDeletedAt() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.ClearDeletedAt()
	})
}

// SetModelDetailID sets the "model_detail_id" field.
func (u *AiModelUsageUpsertOne) SetModelDetailID(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetModelDetailID(v)
	})
}

// AddModelDetailID adds v to the "model_detail_id" field.
func (u *AiModelUsageUpsertOne) AddModelDetailID(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddModelDetailID(v)
	})
}

// UpdateModelDetailID sets the "model_detail_id" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateModelDetailID() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateModelDetailID()
	})
}

// SetModelName sets the "model_name" field.
func (u *AiModelUsageUpsertOne) SetModelName(v string) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetModelName(v)
	})
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateModelName() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateModelName()
	})
}

// SetModelGatewayName sets the "model_gateway_name" field.
func (u *AiModelUsageUpsertOne) SetModelGatewayName(v string) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetModelGatewayName(v)
	})
}

// UpdateModelGatewayName sets the "model_gateway_name" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateModelGatewayName() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateModelGatewayName()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *AiModelUsageUpsertOne) SetAgentID(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetAgentID(v)
	})
}

// AddAgentID adds v to the "agent_id" field.
func (u *AiModelUsageUpsertOne) AddAgentID(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateAgentID() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateAgentID()
	})
}

// SetAgentName sets the "agent_name" field.
func (u *AiModelUsageUpsertOne) SetAgentName(v string) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetAgentName(v)
	})
}

// UpdateAgentName sets the "agent_name" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateAgentName() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateAgentName()
	})
}

// SetUserID sets the "user_id" field.
func (u *AiModelUsageUpsertOne) SetUserID(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *AiModelUsageUpsertOne) AddUserID(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateUserID() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateUserID()
	})
}

// SetUserName sets the "user_name" field.
func (u *AiModelUsageUpsertOne) SetUserName(v string) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetUserName(v)
	})
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateUserName() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateUserName()
	})
}

// SetQuestion sets the "question" field.
func (u *AiModelUsageUpsertOne) SetQuestion(v string) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetQuestion(v)
	})
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateQuestion() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateQuestion()
	})
}

// SetAnswer sets the "answer" field.
func (u *AiModelUsageUpsertOne) SetAnswer(v string) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetAnswer(v)
	})
}

// UpdateAnswer sets the "answer" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateAnswer() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateAnswer()
	})
}

// SetPromptTokens sets the "prompt_tokens" field.
func (u *AiModelUsageUpsertOne) SetPromptTokens(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetPromptTokens(v)
	})
}

// AddPromptTokens adds v to the "prompt_tokens" field.
func (u *AiModelUsageUpsertOne) AddPromptTokens(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddPromptTokens(v)
	})
}

// UpdatePromptTokens sets the "prompt_tokens" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdatePromptTokens() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdatePromptTokens()
	})
}

// SetCompletionTokens sets the "completion_tokens" field.
func (u *AiModelUsageUpsertOne) SetCompletionTokens(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetCompletionTokens(v)
	})
}

// AddCompletionTokens adds v to the "completion_tokens" field.
func (u *AiModelUsageUpsertOne) AddCompletionTokens(v int64) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddCompletionTokens(v)
	})
}

// UpdateCompletionTokens sets the "completion_tokens" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateCompletionTokens() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateCompletionTokens()
	})
}

// SetRequestStatus sets the "request_status" field.
func (u *AiModelUsageUpsertOne) SetRequestStatus(v int8) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetRequestStatus(v)
	})
}

// AddRequestStatus adds v to the "request_status" field.
func (u *AiModelUsageUpsertOne) AddRequestStatus(v int8) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddRequestStatus(v)
	})
}

// UpdateRequestStatus sets the "request_status" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateRequestStatus() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateRequestStatus()
	})
}

// SetErrorCode sets the "error_code" field.
func (u *AiModelUsageUpsertOne) SetErrorCode(v string) *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetErrorCode(v)
	})
}

// UpdateErrorCode sets the "error_code" field to the value that was provided on create.
func (u *AiModelUsageUpsertOne) UpdateErrorCode() *AiModelUsageUpsertOne {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateErrorCode()
	})
}

// Exec executes the query.
func (u *AiModelUsageUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiModelUsageCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiModelUsageUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *AiModelUsageUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *AiModelUsageUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// AiModelUsageCreateBulk is the builder for creating many AiModelUsage entities in bulk.
type AiModelUsageCreateBulk struct {
	config
	err      error
	builders []*AiModelUsageCreate
	conflict []sql.ConflictOption
}

// Save creates the AiModelUsage entities in the database.
func (amucb *AiModelUsageCreateBulk) Save(ctx context.Context) ([]*AiModelUsage, error) {
	if amucb.err != nil {
		return nil, amucb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(amucb.builders))
	nodes := make([]*AiModelUsage, len(amucb.builders))
	mutators := make([]Mutator, len(amucb.builders))
	for i := range amucb.builders {
		func(i int, root context.Context) {
			builder := amucb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AiModelUsageMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, amucb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = amucb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, amucb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, amucb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (amucb *AiModelUsageCreateBulk) SaveX(ctx context.Context) []*AiModelUsage {
	v, err := amucb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (amucb *AiModelUsageCreateBulk) Exec(ctx context.Context) error {
	_, err := amucb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amucb *AiModelUsageCreateBulk) ExecX(ctx context.Context) {
	if err := amucb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.AiModelUsage.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.AiModelUsageUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (amucb *AiModelUsageCreateBulk) OnConflict(opts ...sql.ConflictOption) *AiModelUsageUpsertBulk {
	amucb.conflict = opts
	return &AiModelUsageUpsertBulk{
		create: amucb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.AiModelUsage.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (amucb *AiModelUsageCreateBulk) OnConflictColumns(columns ...string) *AiModelUsageUpsertBulk {
	amucb.conflict = append(amucb.conflict, sql.ConflictColumns(columns...))
	return &AiModelUsageUpsertBulk{
		create: amucb,
	}
}

// AiModelUsageUpsertBulk is the builder for "upsert"-ing
// a bulk of AiModelUsage nodes.
type AiModelUsageUpsertBulk struct {
	create *AiModelUsageCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.AiModelUsage.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(aimodelusage.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *AiModelUsageUpsertBulk) UpdateNewValues() *AiModelUsageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(aimodelusage.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(aimodelusage.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.AiModelUsage.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *AiModelUsageUpsertBulk) Ignore() *AiModelUsageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *AiModelUsageUpsertBulk) DoNothing() *AiModelUsageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the AiModelUsageCreateBulk.OnConflict
// documentation for more info.
func (u *AiModelUsageUpsertBulk) Update(set func(*AiModelUsageUpsert)) *AiModelUsageUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&AiModelUsageUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *AiModelUsageUpsertBulk) SetUpdatedAt(v time.Time) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateUpdatedAt() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *AiModelUsageUpsertBulk) SetDeletedAt(v time.Time) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateDeletedAt() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *AiModelUsageUpsertBulk) ClearDeletedAt() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.ClearDeletedAt()
	})
}

// SetModelDetailID sets the "model_detail_id" field.
func (u *AiModelUsageUpsertBulk) SetModelDetailID(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetModelDetailID(v)
	})
}

// AddModelDetailID adds v to the "model_detail_id" field.
func (u *AiModelUsageUpsertBulk) AddModelDetailID(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddModelDetailID(v)
	})
}

// UpdateModelDetailID sets the "model_detail_id" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateModelDetailID() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateModelDetailID()
	})
}

// SetModelName sets the "model_name" field.
func (u *AiModelUsageUpsertBulk) SetModelName(v string) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetModelName(v)
	})
}

// UpdateModelName sets the "model_name" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateModelName() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateModelName()
	})
}

// SetModelGatewayName sets the "model_gateway_name" field.
func (u *AiModelUsageUpsertBulk) SetModelGatewayName(v string) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetModelGatewayName(v)
	})
}

// UpdateModelGatewayName sets the "model_gateway_name" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateModelGatewayName() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateModelGatewayName()
	})
}

// SetAgentID sets the "agent_id" field.
func (u *AiModelUsageUpsertBulk) SetAgentID(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetAgentID(v)
	})
}

// AddAgentID adds v to the "agent_id" field.
func (u *AiModelUsageUpsertBulk) AddAgentID(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddAgentID(v)
	})
}

// UpdateAgentID sets the "agent_id" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateAgentID() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateAgentID()
	})
}

// SetAgentName sets the "agent_name" field.
func (u *AiModelUsageUpsertBulk) SetAgentName(v string) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetAgentName(v)
	})
}

// UpdateAgentName sets the "agent_name" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateAgentName() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateAgentName()
	})
}

// SetUserID sets the "user_id" field.
func (u *AiModelUsageUpsertBulk) SetUserID(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *AiModelUsageUpsertBulk) AddUserID(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateUserID() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateUserID()
	})
}

// SetUserName sets the "user_name" field.
func (u *AiModelUsageUpsertBulk) SetUserName(v string) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetUserName(v)
	})
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateUserName() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateUserName()
	})
}

// SetQuestion sets the "question" field.
func (u *AiModelUsageUpsertBulk) SetQuestion(v string) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetQuestion(v)
	})
}

// UpdateQuestion sets the "question" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateQuestion() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateQuestion()
	})
}

// SetAnswer sets the "answer" field.
func (u *AiModelUsageUpsertBulk) SetAnswer(v string) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetAnswer(v)
	})
}

// UpdateAnswer sets the "answer" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateAnswer() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateAnswer()
	})
}

// SetPromptTokens sets the "prompt_tokens" field.
func (u *AiModelUsageUpsertBulk) SetPromptTokens(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetPromptTokens(v)
	})
}

// AddPromptTokens adds v to the "prompt_tokens" field.
func (u *AiModelUsageUpsertBulk) AddPromptTokens(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddPromptTokens(v)
	})
}

// UpdatePromptTokens sets the "prompt_tokens" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdatePromptTokens() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdatePromptTokens()
	})
}

// SetCompletionTokens sets the "completion_tokens" field.
func (u *AiModelUsageUpsertBulk) SetCompletionTokens(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetCompletionTokens(v)
	})
}

// AddCompletionTokens adds v to the "completion_tokens" field.
func (u *AiModelUsageUpsertBulk) AddCompletionTokens(v int64) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddCompletionTokens(v)
	})
}

// UpdateCompletionTokens sets the "completion_tokens" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateCompletionTokens() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateCompletionTokens()
	})
}

// SetRequestStatus sets the "request_status" field.
func (u *AiModelUsageUpsertBulk) SetRequestStatus(v int8) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetRequestStatus(v)
	})
}

// AddRequestStatus adds v to the "request_status" field.
func (u *AiModelUsageUpsertBulk) AddRequestStatus(v int8) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.AddRequestStatus(v)
	})
}

// UpdateRequestStatus sets the "request_status" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateRequestStatus() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateRequestStatus()
	})
}

// SetErrorCode sets the "error_code" field.
func (u *AiModelUsageUpsertBulk) SetErrorCode(v string) *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.SetErrorCode(v)
	})
}

// UpdateErrorCode sets the "error_code" field to the value that was provided on create.
func (u *AiModelUsageUpsertBulk) UpdateErrorCode() *AiModelUsageUpsertBulk {
	return u.Update(func(s *AiModelUsageUpsert) {
		s.UpdateErrorCode()
	})
}

// Exec executes the query.
func (u *AiModelUsageUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the AiModelUsageCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for AiModelUsageCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *AiModelUsageUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

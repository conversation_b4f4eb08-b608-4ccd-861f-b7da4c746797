// Code generated by ent, DO NOT EDIT.

package aiagentsecuritypolicy

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

const (
	// Label holds the string label denoting the aiagentsecuritypolicy type in the database.
	Label = "ai_agent_security_policy"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldAgentID holds the string denoting the agent_id field in the database.
	FieldAgentID = "agent_id"
	// FieldPolicyCategory holds the string denoting the policy_category field in the database.
	FieldPolicyCategory = "policy_category"
	// FieldRiskLevel holds the string denoting the risk_level field in the database.
	FieldRiskLevel = "risk_level"
	// FieldEnabled holds the string denoting the enabled field in the database.
	FieldEnabled = "enabled"
	// FieldPolicies holds the string denoting the policies field in the database.
	FieldPolicies = "policies"
	// FieldHitAction holds the string denoting the hit_action field in the database.
	FieldHitAction = "hit_action"
	// FieldHitResponse holds the string denoting the hit_response field in the database.
	FieldHitResponse = "hit_response"
	// FieldUpdatedBy holds the string denoting the updated_by field in the database.
	FieldUpdatedBy = "updated_by"
	// Table holds the table name of the aiagentsecuritypolicy in the database.
	Table = "ai_agent_security_policy"
)

// Columns holds all SQL columns for aiagentsecuritypolicy fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldName,
	FieldAgentID,
	FieldPolicyCategory,
	FieldRiskLevel,
	FieldEnabled,
	FieldPolicies,
	FieldHitAction,
	FieldHitResponse,
	FieldUpdatedBy,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultDeletedAt holds the default value on creation for the "deleted_at" field.
	DefaultDeletedAt time.Time
	// DefaultEnabled holds the default value on creation for the "enabled" field.
	DefaultEnabled bool
	// DefaultPolicies holds the default value on creation for the "policies" field.
	DefaultPolicies *pq.StringArray
	// DefaultHitAction holds the default value on creation for the "hit_action" field.
	DefaultHitAction int64
	// DefaultHitResponse holds the default value on creation for the "hit_response" field.
	DefaultHitResponse string
)

// OrderOption defines the ordering options for the AiAgentSecurityPolicy queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByAgentID orders the results by the agent_id field.
func ByAgentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAgentID, opts...).ToFunc()
}

// ByPolicyCategory orders the results by the policy_category field.
func ByPolicyCategory(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPolicyCategory, opts...).ToFunc()
}

// ByRiskLevel orders the results by the risk_level field.
func ByRiskLevel(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRiskLevel, opts...).ToFunc()
}

// ByEnabled orders the results by the enabled field.
func ByEnabled(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEnabled, opts...).ToFunc()
}

// ByPolicies orders the results by the policies field.
func ByPolicies(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPolicies, opts...).ToFunc()
}

// ByHitAction orders the results by the hit_action field.
func ByHitAction(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHitAction, opts...).ToFunc()
}

// ByHitResponse orders the results by the hit_response field.
func ByHitResponse(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHitResponse, opts...).ToFunc()
}

// ByUpdatedBy orders the results by the updated_by field.
func ByUpdatedBy(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedBy, opts...).ToFunc()
}

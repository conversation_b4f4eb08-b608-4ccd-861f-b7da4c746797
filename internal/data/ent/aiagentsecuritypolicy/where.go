// Code generated by ent, DO NOT EDIT.

package aiagentsecuritypolicy

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldID, id))
}

// ID<PERSON>Q applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldDeletedAt, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldName, v))
}

// AgentID applies equality check predicate on the "agent_id" field. It's identical to AgentIDEQ.
func AgentID(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldAgentID, v))
}

// PolicyCategory applies equality check predicate on the "policy_category" field. It's identical to PolicyCategoryEQ.
func PolicyCategory(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldPolicyCategory, v))
}

// RiskLevel applies equality check predicate on the "risk_level" field. It's identical to RiskLevelEQ.
func RiskLevel(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldRiskLevel, v))
}

// Enabled applies equality check predicate on the "enabled" field. It's identical to EnabledEQ.
func Enabled(v bool) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldEnabled, v))
}

// Policies applies equality check predicate on the "policies" field. It's identical to PoliciesEQ.
func Policies(v *pq.StringArray) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldPolicies, v))
}

// HitAction applies equality check predicate on the "hit_action" field. It's identical to HitActionEQ.
func HitAction(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldHitAction, v))
}

// HitResponse applies equality check predicate on the "hit_response" field. It's identical to HitResponseEQ.
func HitResponse(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldHitResponse, v))
}

// UpdatedBy applies equality check predicate on the "updated_by" field. It's identical to UpdatedByEQ.
func UpdatedBy(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldUpdatedBy, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotNull(FieldDeletedAt))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldContainsFold(FieldName, v))
}

// AgentIDEQ applies the EQ predicate on the "agent_id" field.
func AgentIDEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldAgentID, v))
}

// AgentIDNEQ applies the NEQ predicate on the "agent_id" field.
func AgentIDNEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldAgentID, v))
}

// AgentIDIn applies the In predicate on the "agent_id" field.
func AgentIDIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldAgentID, vs...))
}

// AgentIDNotIn applies the NotIn predicate on the "agent_id" field.
func AgentIDNotIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldAgentID, vs...))
}

// AgentIDGT applies the GT predicate on the "agent_id" field.
func AgentIDGT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldAgentID, v))
}

// AgentIDGTE applies the GTE predicate on the "agent_id" field.
func AgentIDGTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldAgentID, v))
}

// AgentIDLT applies the LT predicate on the "agent_id" field.
func AgentIDLT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldAgentID, v))
}

// AgentIDLTE applies the LTE predicate on the "agent_id" field.
func AgentIDLTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldAgentID, v))
}

// PolicyCategoryEQ applies the EQ predicate on the "policy_category" field.
func PolicyCategoryEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldPolicyCategory, v))
}

// PolicyCategoryNEQ applies the NEQ predicate on the "policy_category" field.
func PolicyCategoryNEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldPolicyCategory, v))
}

// PolicyCategoryIn applies the In predicate on the "policy_category" field.
func PolicyCategoryIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldPolicyCategory, vs...))
}

// PolicyCategoryNotIn applies the NotIn predicate on the "policy_category" field.
func PolicyCategoryNotIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldPolicyCategory, vs...))
}

// PolicyCategoryGT applies the GT predicate on the "policy_category" field.
func PolicyCategoryGT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldPolicyCategory, v))
}

// PolicyCategoryGTE applies the GTE predicate on the "policy_category" field.
func PolicyCategoryGTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldPolicyCategory, v))
}

// PolicyCategoryLT applies the LT predicate on the "policy_category" field.
func PolicyCategoryLT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldPolicyCategory, v))
}

// PolicyCategoryLTE applies the LTE predicate on the "policy_category" field.
func PolicyCategoryLTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldPolicyCategory, v))
}

// RiskLevelEQ applies the EQ predicate on the "risk_level" field.
func RiskLevelEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldRiskLevel, v))
}

// RiskLevelNEQ applies the NEQ predicate on the "risk_level" field.
func RiskLevelNEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldRiskLevel, v))
}

// RiskLevelIn applies the In predicate on the "risk_level" field.
func RiskLevelIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldRiskLevel, vs...))
}

// RiskLevelNotIn applies the NotIn predicate on the "risk_level" field.
func RiskLevelNotIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldRiskLevel, vs...))
}

// RiskLevelGT applies the GT predicate on the "risk_level" field.
func RiskLevelGT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldRiskLevel, v))
}

// RiskLevelGTE applies the GTE predicate on the "risk_level" field.
func RiskLevelGTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldRiskLevel, v))
}

// RiskLevelLT applies the LT predicate on the "risk_level" field.
func RiskLevelLT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldRiskLevel, v))
}

// RiskLevelLTE applies the LTE predicate on the "risk_level" field.
func RiskLevelLTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldRiskLevel, v))
}

// EnabledEQ applies the EQ predicate on the "enabled" field.
func EnabledEQ(v bool) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldEnabled, v))
}

// EnabledNEQ applies the NEQ predicate on the "enabled" field.
func EnabledNEQ(v bool) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldEnabled, v))
}

// PoliciesEQ applies the EQ predicate on the "policies" field.
func PoliciesEQ(v *pq.StringArray) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldPolicies, v))
}

// PoliciesNEQ applies the NEQ predicate on the "policies" field.
func PoliciesNEQ(v *pq.StringArray) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldPolicies, v))
}

// PoliciesIn applies the In predicate on the "policies" field.
func PoliciesIn(vs ...*pq.StringArray) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldPolicies, vs...))
}

// PoliciesNotIn applies the NotIn predicate on the "policies" field.
func PoliciesNotIn(vs ...*pq.StringArray) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldPolicies, vs...))
}

// PoliciesGT applies the GT predicate on the "policies" field.
func PoliciesGT(v *pq.StringArray) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldPolicies, v))
}

// PoliciesGTE applies the GTE predicate on the "policies" field.
func PoliciesGTE(v *pq.StringArray) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldPolicies, v))
}

// PoliciesLT applies the LT predicate on the "policies" field.
func PoliciesLT(v *pq.StringArray) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldPolicies, v))
}

// PoliciesLTE applies the LTE predicate on the "policies" field.
func PoliciesLTE(v *pq.StringArray) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldPolicies, v))
}

// HitActionEQ applies the EQ predicate on the "hit_action" field.
func HitActionEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldHitAction, v))
}

// HitActionNEQ applies the NEQ predicate on the "hit_action" field.
func HitActionNEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldHitAction, v))
}

// HitActionIn applies the In predicate on the "hit_action" field.
func HitActionIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldHitAction, vs...))
}

// HitActionNotIn applies the NotIn predicate on the "hit_action" field.
func HitActionNotIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldHitAction, vs...))
}

// HitActionGT applies the GT predicate on the "hit_action" field.
func HitActionGT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldHitAction, v))
}

// HitActionGTE applies the GTE predicate on the "hit_action" field.
func HitActionGTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldHitAction, v))
}

// HitActionLT applies the LT predicate on the "hit_action" field.
func HitActionLT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldHitAction, v))
}

// HitActionLTE applies the LTE predicate on the "hit_action" field.
func HitActionLTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldHitAction, v))
}

// HitResponseEQ applies the EQ predicate on the "hit_response" field.
func HitResponseEQ(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldHitResponse, v))
}

// HitResponseNEQ applies the NEQ predicate on the "hit_response" field.
func HitResponseNEQ(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldHitResponse, v))
}

// HitResponseIn applies the In predicate on the "hit_response" field.
func HitResponseIn(vs ...string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldHitResponse, vs...))
}

// HitResponseNotIn applies the NotIn predicate on the "hit_response" field.
func HitResponseNotIn(vs ...string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldHitResponse, vs...))
}

// HitResponseGT applies the GT predicate on the "hit_response" field.
func HitResponseGT(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldHitResponse, v))
}

// HitResponseGTE applies the GTE predicate on the "hit_response" field.
func HitResponseGTE(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldHitResponse, v))
}

// HitResponseLT applies the LT predicate on the "hit_response" field.
func HitResponseLT(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldHitResponse, v))
}

// HitResponseLTE applies the LTE predicate on the "hit_response" field.
func HitResponseLTE(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldHitResponse, v))
}

// HitResponseContains applies the Contains predicate on the "hit_response" field.
func HitResponseContains(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldContains(FieldHitResponse, v))
}

// HitResponseHasPrefix applies the HasPrefix predicate on the "hit_response" field.
func HitResponseHasPrefix(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldHasPrefix(FieldHitResponse, v))
}

// HitResponseHasSuffix applies the HasSuffix predicate on the "hit_response" field.
func HitResponseHasSuffix(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldHasSuffix(FieldHitResponse, v))
}

// HitResponseEqualFold applies the EqualFold predicate on the "hit_response" field.
func HitResponseEqualFold(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEqualFold(FieldHitResponse, v))
}

// HitResponseContainsFold applies the ContainsFold predicate on the "hit_response" field.
func HitResponseContainsFold(v string) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldContainsFold(FieldHitResponse, v))
}

// UpdatedByEQ applies the EQ predicate on the "updated_by" field.
func UpdatedByEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldEQ(FieldUpdatedBy, v))
}

// UpdatedByNEQ applies the NEQ predicate on the "updated_by" field.
func UpdatedByNEQ(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNEQ(FieldUpdatedBy, v))
}

// UpdatedByIn applies the In predicate on the "updated_by" field.
func UpdatedByIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldIn(FieldUpdatedBy, vs...))
}

// UpdatedByNotIn applies the NotIn predicate on the "updated_by" field.
func UpdatedByNotIn(vs ...int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldNotIn(FieldUpdatedBy, vs...))
}

// UpdatedByGT applies the GT predicate on the "updated_by" field.
func UpdatedByGT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGT(FieldUpdatedBy, v))
}

// UpdatedByGTE applies the GTE predicate on the "updated_by" field.
func UpdatedByGTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldGTE(FieldUpdatedBy, v))
}

// UpdatedByLT applies the LT predicate on the "updated_by" field.
func UpdatedByLT(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLT(FieldUpdatedBy, v))
}

// UpdatedByLTE applies the LTE predicate on the "updated_by" field.
func UpdatedByLTE(v int64) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.FieldLTE(FieldUpdatedBy, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AiAgentSecurityPolicy) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AiAgentSecurityPolicy) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AiAgentSecurityPolicy) predicate.AiAgentSecurityPolicy {
	return predicate.AiAgentSecurityPolicy(sql.NotPredicates(p))
}

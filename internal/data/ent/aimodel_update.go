// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiModelUpdate is the builder for updating AiModel entities.
type AiModelUpdate struct {
	config
	hooks     []Hook
	mutation  *AiModelMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AiModelUpdate builder.
func (amu *AiModelUpdate) Where(ps ...predicate.AiModel) *AiModelUpdate {
	amu.mutation.Where(ps...)
	return amu
}

// SetUpdatedAt sets the "updated_at" field.
func (amu *AiModelUpdate) SetUpdatedAt(t time.Time) *AiModelUpdate {
	amu.mutation.SetUpdatedAt(t)
	return amu
}

// SetDeletedAt sets the "deleted_at" field.
func (amu *AiModelUpdate) SetDeletedAt(t time.Time) *AiModelUpdate {
	amu.mutation.SetDeletedAt(t)
	return amu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (amu *AiModelUpdate) SetNillableDeletedAt(t *time.Time) *AiModelUpdate {
	if t != nil {
		amu.SetDeletedAt(*t)
	}
	return amu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (amu *AiModelUpdate) ClearDeletedAt() *AiModelUpdate {
	amu.mutation.ClearDeletedAt()
	return amu
}

// SetModelName sets the "model_name" field.
func (amu *AiModelUpdate) SetModelName(s string) *AiModelUpdate {
	amu.mutation.SetModelName(s)
	return amu
}

// SetNillableModelName sets the "model_name" field if the given value is not nil.
func (amu *AiModelUpdate) SetNillableModelName(s *string) *AiModelUpdate {
	if s != nil {
		amu.SetModelName(*s)
	}
	return amu
}

// SetModel sets the "model" field.
func (amu *AiModelUpdate) SetModel(i int64) *AiModelUpdate {
	amu.mutation.ResetModel()
	amu.mutation.SetModel(i)
	return amu
}

// SetNillableModel sets the "model" field if the given value is not nil.
func (amu *AiModelUpdate) SetNillableModel(i *int64) *AiModelUpdate {
	if i != nil {
		amu.SetModel(*i)
	}
	return amu
}

// AddModel adds i to the "model" field.
func (amu *AiModelUpdate) AddModel(i int64) *AiModelUpdate {
	amu.mutation.AddModel(i)
	return amu
}

// SetAPIKey sets the "api_key" field.
func (amu *AiModelUpdate) SetAPIKey(s string) *AiModelUpdate {
	amu.mutation.SetAPIKey(s)
	return amu
}

// SetNillableAPIKey sets the "api_key" field if the given value is not nil.
func (amu *AiModelUpdate) SetNillableAPIKey(s *string) *AiModelUpdate {
	if s != nil {
		amu.SetAPIKey(*s)
	}
	return amu
}

// Mutation returns the AiModelMutation object of the builder.
func (amu *AiModelUpdate) Mutation() *AiModelMutation {
	return amu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (amu *AiModelUpdate) Save(ctx context.Context) (int, error) {
	if err := amu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, amu.sqlSave, amu.mutation, amu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (amu *AiModelUpdate) SaveX(ctx context.Context) int {
	affected, err := amu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (amu *AiModelUpdate) Exec(ctx context.Context) error {
	_, err := amu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amu *AiModelUpdate) ExecX(ctx context.Context) {
	if err := amu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (amu *AiModelUpdate) defaults() error {
	if _, ok := amu.mutation.UpdatedAt(); !ok {
		if aimodel.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodel.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aimodel.UpdateDefaultUpdatedAt()
		amu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (amu *AiModelUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiModelUpdate {
	amu.modifiers = append(amu.modifiers, modifiers...)
	return amu
}

func (amu *AiModelUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(aimodel.Table, aimodel.Columns, sqlgraph.NewFieldSpec(aimodel.FieldID, field.TypeInt64))
	if ps := amu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := amu.mutation.UpdatedAt(); ok {
		_spec.SetField(aimodel.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := amu.mutation.DeletedAt(); ok {
		_spec.SetField(aimodel.FieldDeletedAt, field.TypeTime, value)
	}
	if amu.mutation.DeletedAtCleared() {
		_spec.ClearField(aimodel.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := amu.mutation.ModelName(); ok {
		_spec.SetField(aimodel.FieldModelName, field.TypeString, value)
	}
	if value, ok := amu.mutation.Model(); ok {
		_spec.SetField(aimodel.FieldModel, field.TypeInt64, value)
	}
	if value, ok := amu.mutation.AddedModel(); ok {
		_spec.AddField(aimodel.FieldModel, field.TypeInt64, value)
	}
	if value, ok := amu.mutation.APIKey(); ok {
		_spec.SetField(aimodel.FieldAPIKey, field.TypeString, value)
	}
	_spec.AddModifiers(amu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, amu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aimodel.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	amu.mutation.done = true
	return n, nil
}

// AiModelUpdateOne is the builder for updating a single AiModel entity.
type AiModelUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AiModelMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (amuo *AiModelUpdateOne) SetUpdatedAt(t time.Time) *AiModelUpdateOne {
	amuo.mutation.SetUpdatedAt(t)
	return amuo
}

// SetDeletedAt sets the "deleted_at" field.
func (amuo *AiModelUpdateOne) SetDeletedAt(t time.Time) *AiModelUpdateOne {
	amuo.mutation.SetDeletedAt(t)
	return amuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (amuo *AiModelUpdateOne) SetNillableDeletedAt(t *time.Time) *AiModelUpdateOne {
	if t != nil {
		amuo.SetDeletedAt(*t)
	}
	return amuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (amuo *AiModelUpdateOne) ClearDeletedAt() *AiModelUpdateOne {
	amuo.mutation.ClearDeletedAt()
	return amuo
}

// SetModelName sets the "model_name" field.
func (amuo *AiModelUpdateOne) SetModelName(s string) *AiModelUpdateOne {
	amuo.mutation.SetModelName(s)
	return amuo
}

// SetNillableModelName sets the "model_name" field if the given value is not nil.
func (amuo *AiModelUpdateOne) SetNillableModelName(s *string) *AiModelUpdateOne {
	if s != nil {
		amuo.SetModelName(*s)
	}
	return amuo
}

// SetModel sets the "model" field.
func (amuo *AiModelUpdateOne) SetModel(i int64) *AiModelUpdateOne {
	amuo.mutation.ResetModel()
	amuo.mutation.SetModel(i)
	return amuo
}

// SetNillableModel sets the "model" field if the given value is not nil.
func (amuo *AiModelUpdateOne) SetNillableModel(i *int64) *AiModelUpdateOne {
	if i != nil {
		amuo.SetModel(*i)
	}
	return amuo
}

// AddModel adds i to the "model" field.
func (amuo *AiModelUpdateOne) AddModel(i int64) *AiModelUpdateOne {
	amuo.mutation.AddModel(i)
	return amuo
}

// SetAPIKey sets the "api_key" field.
func (amuo *AiModelUpdateOne) SetAPIKey(s string) *AiModelUpdateOne {
	amuo.mutation.SetAPIKey(s)
	return amuo
}

// SetNillableAPIKey sets the "api_key" field if the given value is not nil.
func (amuo *AiModelUpdateOne) SetNillableAPIKey(s *string) *AiModelUpdateOne {
	if s != nil {
		amuo.SetAPIKey(*s)
	}
	return amuo
}

// Mutation returns the AiModelMutation object of the builder.
func (amuo *AiModelUpdateOne) Mutation() *AiModelMutation {
	return amuo.mutation
}

// Where appends a list predicates to the AiModelUpdate builder.
func (amuo *AiModelUpdateOne) Where(ps ...predicate.AiModel) *AiModelUpdateOne {
	amuo.mutation.Where(ps...)
	return amuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (amuo *AiModelUpdateOne) Select(field string, fields ...string) *AiModelUpdateOne {
	amuo.fields = append([]string{field}, fields...)
	return amuo
}

// Save executes the query and returns the updated AiModel entity.
func (amuo *AiModelUpdateOne) Save(ctx context.Context) (*AiModel, error) {
	if err := amuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, amuo.sqlSave, amuo.mutation, amuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (amuo *AiModelUpdateOne) SaveX(ctx context.Context) *AiModel {
	node, err := amuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (amuo *AiModelUpdateOne) Exec(ctx context.Context) error {
	_, err := amuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (amuo *AiModelUpdateOne) ExecX(ctx context.Context) {
	if err := amuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (amuo *AiModelUpdateOne) defaults() error {
	if _, ok := amuo.mutation.UpdatedAt(); !ok {
		if aimodel.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aimodel.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aimodel.UpdateDefaultUpdatedAt()
		amuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (amuo *AiModelUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiModelUpdateOne {
	amuo.modifiers = append(amuo.modifiers, modifiers...)
	return amuo
}

func (amuo *AiModelUpdateOne) sqlSave(ctx context.Context) (_node *AiModel, err error) {
	_spec := sqlgraph.NewUpdateSpec(aimodel.Table, aimodel.Columns, sqlgraph.NewFieldSpec(aimodel.FieldID, field.TypeInt64))
	id, ok := amuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AiModel.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := amuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aimodel.FieldID)
		for _, f := range fields {
			if !aimodel.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != aimodel.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := amuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := amuo.mutation.UpdatedAt(); ok {
		_spec.SetField(aimodel.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := amuo.mutation.DeletedAt(); ok {
		_spec.SetField(aimodel.FieldDeletedAt, field.TypeTime, value)
	}
	if amuo.mutation.DeletedAtCleared() {
		_spec.ClearField(aimodel.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := amuo.mutation.ModelName(); ok {
		_spec.SetField(aimodel.FieldModelName, field.TypeString, value)
	}
	if value, ok := amuo.mutation.Model(); ok {
		_spec.SetField(aimodel.FieldModel, field.TypeInt64, value)
	}
	if value, ok := amuo.mutation.AddedModel(); ok {
		_spec.AddField(aimodel.FieldModel, field.TypeInt64, value)
	}
	if value, ok := amuo.mutation.APIKey(); ok {
		_spec.SetField(aimodel.FieldAPIKey, field.TypeString, value)
	}
	_spec.AddModifiers(amuo.modifiers...)
	_node = &AiModel{config: amuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, amuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aimodel.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	amuo.mutation.done = true
	return _node, nil
}

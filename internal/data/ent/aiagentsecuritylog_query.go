// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritylog"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiAgentSecurityLogQuery is the builder for querying AiAgentSecurityLog entities.
type AiAgentSecurityLogQuery struct {
	config
	ctx        *QueryContext
	order      []aiagentsecuritylog.OrderOption
	inters     []Interceptor
	predicates []predicate.AiAgentSecurityLog
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AiAgentSecurityLogQuery builder.
func (aaslq *AiAgentSecurityLogQuery) Where(ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogQuery {
	aaslq.predicates = append(aaslq.predicates, ps...)
	return aaslq
}

// Limit the number of records to be returned by this query.
func (aaslq *AiAgentSecurityLogQuery) Limit(limit int) *AiAgentSecurityLogQuery {
	aaslq.ctx.Limit = &limit
	return aaslq
}

// Offset to start from.
func (aaslq *AiAgentSecurityLogQuery) Offset(offset int) *AiAgentSecurityLogQuery {
	aaslq.ctx.Offset = &offset
	return aaslq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (aaslq *AiAgentSecurityLogQuery) Unique(unique bool) *AiAgentSecurityLogQuery {
	aaslq.ctx.Unique = &unique
	return aaslq
}

// Order specifies how the records should be ordered.
func (aaslq *AiAgentSecurityLogQuery) Order(o ...aiagentsecuritylog.OrderOption) *AiAgentSecurityLogQuery {
	aaslq.order = append(aaslq.order, o...)
	return aaslq
}

// First returns the first AiAgentSecurityLog entity from the query.
// Returns a *NotFoundError when no AiAgentSecurityLog was found.
func (aaslq *AiAgentSecurityLogQuery) First(ctx context.Context) (*AiAgentSecurityLog, error) {
	nodes, err := aaslq.Limit(1).All(setContextOp(ctx, aaslq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{aiagentsecuritylog.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (aaslq *AiAgentSecurityLogQuery) FirstX(ctx context.Context) *AiAgentSecurityLog {
	node, err := aaslq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AiAgentSecurityLog ID from the query.
// Returns a *NotFoundError when no AiAgentSecurityLog ID was found.
func (aaslq *AiAgentSecurityLogQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = aaslq.Limit(1).IDs(setContextOp(ctx, aaslq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{aiagentsecuritylog.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (aaslq *AiAgentSecurityLogQuery) FirstIDX(ctx context.Context) int64 {
	id, err := aaslq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AiAgentSecurityLog entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AiAgentSecurityLog entity is found.
// Returns a *NotFoundError when no AiAgentSecurityLog entities are found.
func (aaslq *AiAgentSecurityLogQuery) Only(ctx context.Context) (*AiAgentSecurityLog, error) {
	nodes, err := aaslq.Limit(2).All(setContextOp(ctx, aaslq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{aiagentsecuritylog.Label}
	default:
		return nil, &NotSingularError{aiagentsecuritylog.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (aaslq *AiAgentSecurityLogQuery) OnlyX(ctx context.Context) *AiAgentSecurityLog {
	node, err := aaslq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AiAgentSecurityLog ID in the query.
// Returns a *NotSingularError when more than one AiAgentSecurityLog ID is found.
// Returns a *NotFoundError when no entities are found.
func (aaslq *AiAgentSecurityLogQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = aaslq.Limit(2).IDs(setContextOp(ctx, aaslq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{aiagentsecuritylog.Label}
	default:
		err = &NotSingularError{aiagentsecuritylog.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (aaslq *AiAgentSecurityLogQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := aaslq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AiAgentSecurityLogs.
func (aaslq *AiAgentSecurityLogQuery) All(ctx context.Context) ([]*AiAgentSecurityLog, error) {
	ctx = setContextOp(ctx, aaslq.ctx, "All")
	if err := aaslq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AiAgentSecurityLog, *AiAgentSecurityLogQuery]()
	return withInterceptors[[]*AiAgentSecurityLog](ctx, aaslq, qr, aaslq.inters)
}

// AllX is like All, but panics if an error occurs.
func (aaslq *AiAgentSecurityLogQuery) AllX(ctx context.Context) []*AiAgentSecurityLog {
	nodes, err := aaslq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AiAgentSecurityLog IDs.
func (aaslq *AiAgentSecurityLogQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if aaslq.ctx.Unique == nil && aaslq.path != nil {
		aaslq.Unique(true)
	}
	ctx = setContextOp(ctx, aaslq.ctx, "IDs")
	if err = aaslq.Select(aiagentsecuritylog.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (aaslq *AiAgentSecurityLogQuery) IDsX(ctx context.Context) []int64 {
	ids, err := aaslq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (aaslq *AiAgentSecurityLogQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, aaslq.ctx, "Count")
	if err := aaslq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, aaslq, querierCount[*AiAgentSecurityLogQuery](), aaslq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (aaslq *AiAgentSecurityLogQuery) CountX(ctx context.Context) int {
	count, err := aaslq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (aaslq *AiAgentSecurityLogQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, aaslq.ctx, "Exist")
	switch _, err := aaslq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (aaslq *AiAgentSecurityLogQuery) ExistX(ctx context.Context) bool {
	exist, err := aaslq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AiAgentSecurityLogQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (aaslq *AiAgentSecurityLogQuery) Clone() *AiAgentSecurityLogQuery {
	if aaslq == nil {
		return nil
	}
	return &AiAgentSecurityLogQuery{
		config:     aaslq.config,
		ctx:        aaslq.ctx.Clone(),
		order:      append([]aiagentsecuritylog.OrderOption{}, aaslq.order...),
		inters:     append([]Interceptor{}, aaslq.inters...),
		predicates: append([]predicate.AiAgentSecurityLog{}, aaslq.predicates...),
		// clone intermediate query.
		sql:  aaslq.sql.Clone(),
		path: aaslq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AiAgentSecurityLog.Query().
//		GroupBy(aiagentsecuritylog.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (aaslq *AiAgentSecurityLogQuery) GroupBy(field string, fields ...string) *AiAgentSecurityLogGroupBy {
	aaslq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AiAgentSecurityLogGroupBy{build: aaslq}
	grbuild.flds = &aaslq.ctx.Fields
	grbuild.label = aiagentsecuritylog.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.AiAgentSecurityLog.Query().
//		Select(aiagentsecuritylog.FieldCreatedAt).
//		Scan(ctx, &v)
func (aaslq *AiAgentSecurityLogQuery) Select(fields ...string) *AiAgentSecurityLogSelect {
	aaslq.ctx.Fields = append(aaslq.ctx.Fields, fields...)
	sbuild := &AiAgentSecurityLogSelect{AiAgentSecurityLogQuery: aaslq}
	sbuild.label = aiagentsecuritylog.Label
	sbuild.flds, sbuild.scan = &aaslq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AiAgentSecurityLogSelect configured with the given aggregations.
func (aaslq *AiAgentSecurityLogQuery) Aggregate(fns ...AggregateFunc) *AiAgentSecurityLogSelect {
	return aaslq.Select().Aggregate(fns...)
}

func (aaslq *AiAgentSecurityLogQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range aaslq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, aaslq); err != nil {
				return err
			}
		}
	}
	for _, f := range aaslq.ctx.Fields {
		if !aiagentsecuritylog.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if aaslq.path != nil {
		prev, err := aaslq.path(ctx)
		if err != nil {
			return err
		}
		aaslq.sql = prev
	}
	return nil
}

func (aaslq *AiAgentSecurityLogQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AiAgentSecurityLog, error) {
	var (
		nodes = []*AiAgentSecurityLog{}
		_spec = aaslq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AiAgentSecurityLog).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AiAgentSecurityLog{config: aaslq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(aaslq.modifiers) > 0 {
		_spec.Modifiers = aaslq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, aaslq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (aaslq *AiAgentSecurityLogQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := aaslq.querySpec()
	if len(aaslq.modifiers) > 0 {
		_spec.Modifiers = aaslq.modifiers
	}
	_spec.Node.Columns = aaslq.ctx.Fields
	if len(aaslq.ctx.Fields) > 0 {
		_spec.Unique = aaslq.ctx.Unique != nil && *aaslq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, aaslq.driver, _spec)
}

func (aaslq *AiAgentSecurityLogQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(aiagentsecuritylog.Table, aiagentsecuritylog.Columns, sqlgraph.NewFieldSpec(aiagentsecuritylog.FieldID, field.TypeInt64))
	_spec.From = aaslq.sql
	if unique := aaslq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if aaslq.path != nil {
		_spec.Unique = true
	}
	if fields := aaslq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aiagentsecuritylog.FieldID)
		for i := range fields {
			if fields[i] != aiagentsecuritylog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := aaslq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := aaslq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := aaslq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := aaslq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (aaslq *AiAgentSecurityLogQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(aaslq.driver.Dialect())
	t1 := builder.Table(aiagentsecuritylog.Table)
	columns := aaslq.ctx.Fields
	if len(columns) == 0 {
		columns = aiagentsecuritylog.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if aaslq.sql != nil {
		selector = aaslq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if aaslq.ctx.Unique != nil && *aaslq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range aaslq.modifiers {
		m(selector)
	}
	for _, p := range aaslq.predicates {
		p(selector)
	}
	for _, p := range aaslq.order {
		p(selector)
	}
	if offset := aaslq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := aaslq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (aaslq *AiAgentSecurityLogQuery) ForUpdate(opts ...sql.LockOption) *AiAgentSecurityLogQuery {
	if aaslq.driver.Dialect() == dialect.Postgres {
		aaslq.Unique(false)
	}
	aaslq.modifiers = append(aaslq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return aaslq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (aaslq *AiAgentSecurityLogQuery) ForShare(opts ...sql.LockOption) *AiAgentSecurityLogQuery {
	if aaslq.driver.Dialect() == dialect.Postgres {
		aaslq.Unique(false)
	}
	aaslq.modifiers = append(aaslq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return aaslq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (aaslq *AiAgentSecurityLogQuery) Modify(modifiers ...func(s *sql.Selector)) *AiAgentSecurityLogSelect {
	aaslq.modifiers = append(aaslq.modifiers, modifiers...)
	return aaslq.Select()
}

// AiAgentSecurityLogGroupBy is the group-by builder for AiAgentSecurityLog entities.
type AiAgentSecurityLogGroupBy struct {
	selector
	build *AiAgentSecurityLogQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (aaslgb *AiAgentSecurityLogGroupBy) Aggregate(fns ...AggregateFunc) *AiAgentSecurityLogGroupBy {
	aaslgb.fns = append(aaslgb.fns, fns...)
	return aaslgb
}

// Scan applies the selector query and scans the result into the given value.
func (aaslgb *AiAgentSecurityLogGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aaslgb.build.ctx, "GroupBy")
	if err := aaslgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiAgentSecurityLogQuery, *AiAgentSecurityLogGroupBy](ctx, aaslgb.build, aaslgb, aaslgb.build.inters, v)
}

func (aaslgb *AiAgentSecurityLogGroupBy) sqlScan(ctx context.Context, root *AiAgentSecurityLogQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(aaslgb.fns))
	for _, fn := range aaslgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*aaslgb.flds)+len(aaslgb.fns))
		for _, f := range *aaslgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*aaslgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aaslgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AiAgentSecurityLogSelect is the builder for selecting fields of AiAgentSecurityLog entities.
type AiAgentSecurityLogSelect struct {
	*AiAgentSecurityLogQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (aasls *AiAgentSecurityLogSelect) Aggregate(fns ...AggregateFunc) *AiAgentSecurityLogSelect {
	aasls.fns = append(aasls.fns, fns...)
	return aasls
}

// Scan applies the selector query and scans the result into the given value.
func (aasls *AiAgentSecurityLogSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aasls.ctx, "Select")
	if err := aasls.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiAgentSecurityLogQuery, *AiAgentSecurityLogSelect](ctx, aasls.AiAgentSecurityLogQuery, aasls, aasls.inters, v)
}

func (aasls *AiAgentSecurityLogSelect) sqlScan(ctx context.Context, root *AiAgentSecurityLogQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(aasls.fns))
	for _, fn := range aasls.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*aasls.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aasls.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (aasls *AiAgentSecurityLogSelect) Modify(modifiers ...func(s *sql.Selector)) *AiAgentSecurityLogSelect {
	aasls.modifiers = append(aasls.modifiers, modifiers...)
	return aasls
}

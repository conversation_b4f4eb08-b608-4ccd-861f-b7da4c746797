// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebasefile"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// KnowledgeBaseFileDelete is the builder for deleting a KnowledgeBaseFile entity.
type KnowledgeBaseFileDelete struct {
	config
	hooks    []Hook
	mutation *KnowledgeBaseFileMutation
}

// Where appends a list predicates to the KnowledgeBaseFileDelete builder.
func (kbfd *KnowledgeBaseFileDelete) Where(ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileDelete {
	kbfd.mutation.Where(ps...)
	return kbfd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (kbfd *KnowledgeBaseFileDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, kbfd.sqlExec, kbfd.mutation, kbfd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (kbfd *KnowledgeBaseFileDelete) ExecX(ctx context.Context) int {
	n, err := kbfd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (kbfd *KnowledgeBaseFileDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(knowledgebasefile.Table, sqlgraph.NewFieldSpec(knowledgebasefile.FieldID, field.TypeInt64))
	if ps := kbfd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, kbfd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	kbfd.mutation.done = true
	return affected, err
}

// KnowledgeBaseFileDeleteOne is the builder for deleting a single KnowledgeBaseFile entity.
type KnowledgeBaseFileDeleteOne struct {
	kbfd *KnowledgeBaseFileDelete
}

// Where appends a list predicates to the KnowledgeBaseFileDelete builder.
func (kbfdo *KnowledgeBaseFileDeleteOne) Where(ps ...predicate.KnowledgeBaseFile) *KnowledgeBaseFileDeleteOne {
	kbfdo.kbfd.mutation.Where(ps...)
	return kbfdo
}

// Exec executes the deletion query.
func (kbfdo *KnowledgeBaseFileDeleteOne) Exec(ctx context.Context) error {
	n, err := kbfdo.kbfd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{knowledgebasefile.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (kbfdo *KnowledgeBaseFileDeleteOne) ExecX(ctx context.Context) {
	if err := kbfdo.Exec(ctx); err != nil {
		panic(err)
	}
}

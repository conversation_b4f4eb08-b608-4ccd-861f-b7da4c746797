// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiChatQuery is the builder for querying AiChat entities.
type AiChatQuery struct {
	config
	ctx         *QueryContext
	order       []aichat.OrderOption
	inters      []Interceptor
	predicates  []predicate.AiChat
	withAiAgent *AiAgentQuery
	modifiers   []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AiChatQuery builder.
func (acq *AiChatQuery) Where(ps ...predicate.AiChat) *AiChatQuery {
	acq.predicates = append(acq.predicates, ps...)
	return acq
}

// Limit the number of records to be returned by this query.
func (acq *AiChatQuery) Limit(limit int) *AiChatQuery {
	acq.ctx.Limit = &limit
	return acq
}

// Offset to start from.
func (acq *AiChatQuery) Offset(offset int) *AiChatQuery {
	acq.ctx.Offset = &offset
	return acq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (acq *AiChatQuery) Unique(unique bool) *AiChatQuery {
	acq.ctx.Unique = &unique
	return acq
}

// Order specifies how the records should be ordered.
func (acq *AiChatQuery) Order(o ...aichat.OrderOption) *AiChatQuery {
	acq.order = append(acq.order, o...)
	return acq
}

// QueryAiAgent chains the current query on the "ai_agent" edge.
func (acq *AiChatQuery) QueryAiAgent() *AiAgentQuery {
	query := (&AiAgentClient{config: acq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := acq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := acq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(aichat.Table, aichat.FieldID, selector),
			sqlgraph.To(aiagent.Table, aiagent.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, aichat.AiAgentTable, aichat.AiAgentColumn),
		)
		fromU = sqlgraph.SetNeighbors(acq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first AiChat entity from the query.
// Returns a *NotFoundError when no AiChat was found.
func (acq *AiChatQuery) First(ctx context.Context) (*AiChat, error) {
	nodes, err := acq.Limit(1).All(setContextOp(ctx, acq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{aichat.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (acq *AiChatQuery) FirstX(ctx context.Context) *AiChat {
	node, err := acq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AiChat ID from the query.
// Returns a *NotFoundError when no AiChat ID was found.
func (acq *AiChatQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = acq.Limit(1).IDs(setContextOp(ctx, acq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{aichat.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (acq *AiChatQuery) FirstIDX(ctx context.Context) int64 {
	id, err := acq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AiChat entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AiChat entity is found.
// Returns a *NotFoundError when no AiChat entities are found.
func (acq *AiChatQuery) Only(ctx context.Context) (*AiChat, error) {
	nodes, err := acq.Limit(2).All(setContextOp(ctx, acq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{aichat.Label}
	default:
		return nil, &NotSingularError{aichat.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (acq *AiChatQuery) OnlyX(ctx context.Context) *AiChat {
	node, err := acq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AiChat ID in the query.
// Returns a *NotSingularError when more than one AiChat ID is found.
// Returns a *NotFoundError when no entities are found.
func (acq *AiChatQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = acq.Limit(2).IDs(setContextOp(ctx, acq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{aichat.Label}
	default:
		err = &NotSingularError{aichat.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (acq *AiChatQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := acq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AiChats.
func (acq *AiChatQuery) All(ctx context.Context) ([]*AiChat, error) {
	ctx = setContextOp(ctx, acq.ctx, "All")
	if err := acq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AiChat, *AiChatQuery]()
	return withInterceptors[[]*AiChat](ctx, acq, qr, acq.inters)
}

// AllX is like All, but panics if an error occurs.
func (acq *AiChatQuery) AllX(ctx context.Context) []*AiChat {
	nodes, err := acq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AiChat IDs.
func (acq *AiChatQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if acq.ctx.Unique == nil && acq.path != nil {
		acq.Unique(true)
	}
	ctx = setContextOp(ctx, acq.ctx, "IDs")
	if err = acq.Select(aichat.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (acq *AiChatQuery) IDsX(ctx context.Context) []int64 {
	ids, err := acq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (acq *AiChatQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, acq.ctx, "Count")
	if err := acq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, acq, querierCount[*AiChatQuery](), acq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (acq *AiChatQuery) CountX(ctx context.Context) int {
	count, err := acq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (acq *AiChatQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, acq.ctx, "Exist")
	switch _, err := acq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (acq *AiChatQuery) ExistX(ctx context.Context) bool {
	exist, err := acq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AiChatQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (acq *AiChatQuery) Clone() *AiChatQuery {
	if acq == nil {
		return nil
	}
	return &AiChatQuery{
		config:      acq.config,
		ctx:         acq.ctx.Clone(),
		order:       append([]aichat.OrderOption{}, acq.order...),
		inters:      append([]Interceptor{}, acq.inters...),
		predicates:  append([]predicate.AiChat{}, acq.predicates...),
		withAiAgent: acq.withAiAgent.Clone(),
		// clone intermediate query.
		sql:  acq.sql.Clone(),
		path: acq.path,
	}
}

// WithAiAgent tells the query-builder to eager-load the nodes that are connected to
// the "ai_agent" edge. The optional arguments are used to configure the query builder of the edge.
func (acq *AiChatQuery) WithAiAgent(opts ...func(*AiAgentQuery)) *AiChatQuery {
	query := (&AiAgentClient{config: acq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	acq.withAiAgent = query
	return acq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AiChat.Query().
//		GroupBy(aichat.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (acq *AiChatQuery) GroupBy(field string, fields ...string) *AiChatGroupBy {
	acq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AiChatGroupBy{build: acq}
	grbuild.flds = &acq.ctx.Fields
	grbuild.label = aichat.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//	}
//
//	client.AiChat.Query().
//		Select(aichat.FieldTenantID).
//		Scan(ctx, &v)
func (acq *AiChatQuery) Select(fields ...string) *AiChatSelect {
	acq.ctx.Fields = append(acq.ctx.Fields, fields...)
	sbuild := &AiChatSelect{AiChatQuery: acq}
	sbuild.label = aichat.Label
	sbuild.flds, sbuild.scan = &acq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AiChatSelect configured with the given aggregations.
func (acq *AiChatQuery) Aggregate(fns ...AggregateFunc) *AiChatSelect {
	return acq.Select().Aggregate(fns...)
}

func (acq *AiChatQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range acq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, acq); err != nil {
				return err
			}
		}
	}
	for _, f := range acq.ctx.Fields {
		if !aichat.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if acq.path != nil {
		prev, err := acq.path(ctx)
		if err != nil {
			return err
		}
		acq.sql = prev
	}
	return nil
}

func (acq *AiChatQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AiChat, error) {
	var (
		nodes       = []*AiChat{}
		_spec       = acq.querySpec()
		loadedTypes = [1]bool{
			acq.withAiAgent != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AiChat).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AiChat{config: acq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	if len(acq.modifiers) > 0 {
		_spec.Modifiers = acq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, acq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := acq.withAiAgent; query != nil {
		if err := acq.loadAiAgent(ctx, query, nodes, nil,
			func(n *AiChat, e *AiAgent) { n.Edges.AiAgent = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (acq *AiChatQuery) loadAiAgent(ctx context.Context, query *AiAgentQuery, nodes []*AiChat, init func(*AiChat), assign func(*AiChat, *AiAgent)) error {
	ids := make([]int64, 0, len(nodes))
	nodeids := make(map[int64][]*AiChat)
	for i := range nodes {
		fk := nodes[i].AgentID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(aiagent.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "agent_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (acq *AiChatQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := acq.querySpec()
	if len(acq.modifiers) > 0 {
		_spec.Modifiers = acq.modifiers
	}
	_spec.Node.Columns = acq.ctx.Fields
	if len(acq.ctx.Fields) > 0 {
		_spec.Unique = acq.ctx.Unique != nil && *acq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, acq.driver, _spec)
}

func (acq *AiChatQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(aichat.Table, aichat.Columns, sqlgraph.NewFieldSpec(aichat.FieldID, field.TypeInt64))
	_spec.From = acq.sql
	if unique := acq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if acq.path != nil {
		_spec.Unique = true
	}
	if fields := acq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aichat.FieldID)
		for i := range fields {
			if fields[i] != aichat.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if acq.withAiAgent != nil {
			_spec.Node.AddColumnOnce(aichat.FieldAgentID)
		}
	}
	if ps := acq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := acq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := acq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := acq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (acq *AiChatQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(acq.driver.Dialect())
	t1 := builder.Table(aichat.Table)
	columns := acq.ctx.Fields
	if len(columns) == 0 {
		columns = aichat.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if acq.sql != nil {
		selector = acq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if acq.ctx.Unique != nil && *acq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range acq.modifiers {
		m(selector)
	}
	for _, p := range acq.predicates {
		p(selector)
	}
	for _, p := range acq.order {
		p(selector)
	}
	if offset := acq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := acq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (acq *AiChatQuery) ForUpdate(opts ...sql.LockOption) *AiChatQuery {
	if acq.driver.Dialect() == dialect.Postgres {
		acq.Unique(false)
	}
	acq.modifiers = append(acq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return acq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (acq *AiChatQuery) ForShare(opts ...sql.LockOption) *AiChatQuery {
	if acq.driver.Dialect() == dialect.Postgres {
		acq.Unique(false)
	}
	acq.modifiers = append(acq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return acq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (acq *AiChatQuery) Modify(modifiers ...func(s *sql.Selector)) *AiChatSelect {
	acq.modifiers = append(acq.modifiers, modifiers...)
	return acq.Select()
}

// AiChatGroupBy is the group-by builder for AiChat entities.
type AiChatGroupBy struct {
	selector
	build *AiChatQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (acgb *AiChatGroupBy) Aggregate(fns ...AggregateFunc) *AiChatGroupBy {
	acgb.fns = append(acgb.fns, fns...)
	return acgb
}

// Scan applies the selector query and scans the result into the given value.
func (acgb *AiChatGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, acgb.build.ctx, "GroupBy")
	if err := acgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiChatQuery, *AiChatGroupBy](ctx, acgb.build, acgb, acgb.build.inters, v)
}

func (acgb *AiChatGroupBy) sqlScan(ctx context.Context, root *AiChatQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(acgb.fns))
	for _, fn := range acgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*acgb.flds)+len(acgb.fns))
		for _, f := range *acgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*acgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := acgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AiChatSelect is the builder for selecting fields of AiChat entities.
type AiChatSelect struct {
	*AiChatQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (acs *AiChatSelect) Aggregate(fns ...AggregateFunc) *AiChatSelect {
	acs.fns = append(acs.fns, fns...)
	return acs
}

// Scan applies the selector query and scans the result into the given value.
func (acs *AiChatSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, acs.ctx, "Select")
	if err := acs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiChatQuery, *AiChatSelect](ctx, acs.AiChatQuery, acs, acs.inters, v)
}

func (acs *AiChatSelect) sqlScan(ctx context.Context, root *AiChatQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(acs.fns))
	for _, fn := range acs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*acs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := acs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (acs *AiChatSelect) Modify(modifiers ...func(s *sql.Selector)) *AiChatSelect {
	acs.modifiers = append(acs.modifiers, modifiers...)
	return acs
}

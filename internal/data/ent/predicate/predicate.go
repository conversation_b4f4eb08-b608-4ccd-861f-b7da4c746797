// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// AiAgent is the predicate function for aiagent builders.
type AiAgent func(*sql.Selector)

// AiAgentSecurityLog is the predicate function for aiagentsecuritylog builders.
type AiAgentSecurityLog func(*sql.Selector)

// AiAgentSecurityPolicy is the predicate function for aiagentsecuritypolicy builders.
type AiAgentSecurityPolicy func(*sql.Selector)

// AiChat is the predicate function for aichat builders.
type AiChat func(*sql.Selector)

// AiChatItem is the predicate function for aichatitem builders.
type AiChatItem func(*sql.Selector)

// AiModel is the predicate function for aimodel builders.
type AiModel func(*sql.Selector)

// AiModelDetail is the predicate function for aimodeldetail builders.
type AiModelDetail func(*sql.Selector)

// AiModelUsage is the predicate function for aimodelusage builders.
type AiModelUsage func(*sql.Selector)

// AtomicQuestions is the predicate function for atomicquestions builders.
type AtomicQuestions func(*sql.Selector)

// ClassificationFiles is the predicate function for classificationfiles builders.
type ClassificationFiles func(*sql.Selector)

// DefaultAgentAvatar is the predicate function for defaultagentavatar builders.
type DefaultAgentAvatar func(*sql.Selector)

// ExternalModelUsage is the predicate function for externalmodelusage builders.
type ExternalModelUsage func(*sql.Selector)

// KnowledgeBase is the predicate function for knowledgebase builders.
type KnowledgeBase func(*sql.Selector)

// KnowledgeBaseFile is the predicate function for knowledgebasefile builders.
type KnowledgeBaseFile func(*sql.Selector)

// UserAgentOrder is the predicate function for useragentorder builders.
type UserAgentOrder func(*sql.Selector)

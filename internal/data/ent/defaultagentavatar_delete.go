// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/defaultagentavatar"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// DefaultAgentAvatarDelete is the builder for deleting a DefaultAgentAvatar entity.
type DefaultAgentAvatarDelete struct {
	config
	hooks    []Hook
	mutation *DefaultAgentAvatarMutation
}

// Where appends a list predicates to the DefaultAgentAvatarDelete builder.
func (daad *DefaultAgentAvatarDelete) Where(ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarDelete {
	daad.mutation.Where(ps...)
	return daad
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (daad *DefaultAgentAvatarDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, daad.sqlExec, daad.mutation, daad.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (daad *DefaultAgentAvatarDelete) ExecX(ctx context.Context) int {
	n, err := daad.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (daad *DefaultAgentAvatarDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(defaultagentavatar.Table, sqlgraph.NewFieldSpec(defaultagentavatar.FieldID, field.TypeInt64))
	if ps := daad.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, daad.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	daad.mutation.done = true
	return affected, err
}

// DefaultAgentAvatarDeleteOne is the builder for deleting a single DefaultAgentAvatar entity.
type DefaultAgentAvatarDeleteOne struct {
	daad *DefaultAgentAvatarDelete
}

// Where appends a list predicates to the DefaultAgentAvatarDelete builder.
func (daado *DefaultAgentAvatarDeleteOne) Where(ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarDeleteOne {
	daado.daad.mutation.Where(ps...)
	return daado
}

// Exec executes the deletion query.
func (daado *DefaultAgentAvatarDeleteOne) Exec(ctx context.Context) error {
	n, err := daado.daad.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{defaultagentavatar.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (daado *DefaultAgentAvatarDeleteOne) ExecX(ctx context.Context) {
	if err := daado.Exec(ctx); err != nil {
		panic(err)
	}
}

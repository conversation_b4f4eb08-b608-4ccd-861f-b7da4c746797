// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
)

// Countz use count(*)
func (aa *AiAgentQuery) Countz(ctx context.Context) (int, error) {
	return aa.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (aasl *AiAgentSecurityLogQuery) Countz(ctx context.Context) (int, error) {
	return aasl.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (aasp *AiAgentSecurityPolicyQuery) Countz(ctx context.Context) (int, error) {
	return aasp.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (ac *AiChatQuery) Countz(ctx context.Context) (int, error) {
	return ac.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (aci *AiChatItemQuery) Countz(ctx context.Context) (int, error) {
	return aci.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (am *AiModelQuery) Countz(ctx context.Context) (int, error) {
	return am.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (amd *AiModelDetailQuery) Countz(ctx context.Context) (int, error) {
	return amd.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (amu *AiModelUsageQuery) Countz(ctx context.Context) (int, error) {
	return amu.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (aq *AtomicQuestionsQuery) Countz(ctx context.Context) (int, error) {
	return aq.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (cf *ClassificationFilesQuery) Countz(ctx context.Context) (int, error) {
	return cf.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (daa *DefaultAgentAvatarQuery) Countz(ctx context.Context) (int, error) {
	return daa.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (emu *ExternalModelUsageQuery) Countz(ctx context.Context) (int, error) {
	return emu.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (kb *KnowledgeBaseQuery) Countz(ctx context.Context) (int, error) {
	return kb.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (kbf *KnowledgeBaseFileQuery) Countz(ctx context.Context) (int, error) {
	return kbf.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Countz use count(*)
func (uao *UserAgentOrderQuery) Countz(ctx context.Context) (int, error) {
	return uao.Modify(func(s *sql.Selector) {
		s.Select("count(*)")
	}).Int(ctx)
}

// Code generated by ent, DO NOT EDIT.

package useragentorder

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldDeletedAt, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldUserID, v))
}

// AgentID applies equality check predicate on the "agent_id" field. It's identical to AgentIDEQ.
func AgentID(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldAgentID, v))
}

// OrderIndex applies equality check predicate on the "order_index" field. It's identical to OrderIndexEQ.
func OrderIndex(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldOrderIndex, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNotNull(FieldDeletedAt))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLTE(FieldUserID, v))
}

// AgentIDEQ applies the EQ predicate on the "agent_id" field.
func AgentIDEQ(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldAgentID, v))
}

// AgentIDNEQ applies the NEQ predicate on the "agent_id" field.
func AgentIDNEQ(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNEQ(FieldAgentID, v))
}

// AgentIDIn applies the In predicate on the "agent_id" field.
func AgentIDIn(vs ...int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldIn(FieldAgentID, vs...))
}

// AgentIDNotIn applies the NotIn predicate on the "agent_id" field.
func AgentIDNotIn(vs ...int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNotIn(FieldAgentID, vs...))
}

// AgentIDGT applies the GT predicate on the "agent_id" field.
func AgentIDGT(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGT(FieldAgentID, v))
}

// AgentIDGTE applies the GTE predicate on the "agent_id" field.
func AgentIDGTE(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGTE(FieldAgentID, v))
}

// AgentIDLT applies the LT predicate on the "agent_id" field.
func AgentIDLT(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLT(FieldAgentID, v))
}

// AgentIDLTE applies the LTE predicate on the "agent_id" field.
func AgentIDLTE(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLTE(FieldAgentID, v))
}

// OrderIndexEQ applies the EQ predicate on the "order_index" field.
func OrderIndexEQ(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldEQ(FieldOrderIndex, v))
}

// OrderIndexNEQ applies the NEQ predicate on the "order_index" field.
func OrderIndexNEQ(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNEQ(FieldOrderIndex, v))
}

// OrderIndexIn applies the In predicate on the "order_index" field.
func OrderIndexIn(vs ...int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldIn(FieldOrderIndex, vs...))
}

// OrderIndexNotIn applies the NotIn predicate on the "order_index" field.
func OrderIndexNotIn(vs ...int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldNotIn(FieldOrderIndex, vs...))
}

// OrderIndexGT applies the GT predicate on the "order_index" field.
func OrderIndexGT(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGT(FieldOrderIndex, v))
}

// OrderIndexGTE applies the GTE predicate on the "order_index" field.
func OrderIndexGTE(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldGTE(FieldOrderIndex, v))
}

// OrderIndexLT applies the LT predicate on the "order_index" field.
func OrderIndexLT(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLT(FieldOrderIndex, v))
}

// OrderIndexLTE applies the LTE predicate on the "order_index" field.
func OrderIndexLTE(v int64) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.FieldLTE(FieldOrderIndex, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.UserAgentOrder) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.UserAgentOrder) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.UserAgentOrder) predicate.UserAgentOrder {
	return predicate.UserAgentOrder(sql.NotPredicates(p))
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/classificationfiles"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ClassificationFilesDelete is the builder for deleting a ClassificationFiles entity.
type ClassificationFilesDelete struct {
	config
	hooks    []Hook
	mutation *ClassificationFilesMutation
}

// Where appends a list predicates to the ClassificationFilesDelete builder.
func (cfd *ClassificationFilesDelete) Where(ps ...predicate.ClassificationFiles) *ClassificationFilesDelete {
	cfd.mutation.Where(ps...)
	return cfd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (cfd *ClassificationFilesDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, cfd.sqlExec, cfd.mutation, cfd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (cfd *ClassificationFilesDelete) ExecX(ctx context.Context) int {
	n, err := cfd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (cfd *ClassificationFilesDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(classificationfiles.Table, sqlgraph.NewFieldSpec(classificationfiles.FieldID, field.TypeInt64))
	if ps := cfd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, cfd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	cfd.mutation.done = true
	return affected, err
}

// ClassificationFilesDeleteOne is the builder for deleting a single ClassificationFiles entity.
type ClassificationFilesDeleteOne struct {
	cfd *ClassificationFilesDelete
}

// Where appends a list predicates to the ClassificationFilesDelete builder.
func (cfdo *ClassificationFilesDeleteOne) Where(ps ...predicate.ClassificationFiles) *ClassificationFilesDeleteOne {
	cfdo.cfd.mutation.Where(ps...)
	return cfdo
}

// Exec executes the deletion query.
func (cfdo *ClassificationFilesDeleteOne) Exec(ctx context.Context) error {
	n, err := cfdo.cfd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{classificationfiles.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (cfdo *ClassificationFilesDeleteOne) ExecX(ctx context.Context) {
	if err := cfdo.Exec(ctx); err != nil {
		panic(err)
	}
}

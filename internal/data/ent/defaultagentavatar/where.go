// Code generated by ent, DO NOT EDIT.

package defaultagentavatar

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldDeletedAt, v))
}

// Avatar applies equality check predicate on the "avatar" field. It's identical to AvatarEQ.
func Avatar(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldAvatar, v))
}

// ClickedAvatar applies equality check predicate on the "clicked_avatar" field. It's identical to ClickedAvatarEQ.
func ClickedAvatar(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldClickedAvatar, v))
}

// AvatarType applies equality check predicate on the "avatar_type" field. It's identical to AvatarTypeEQ.
func AvatarType(v int8) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldAvatarType, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v int64) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLTE(FieldTenantID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNotNull(FieldDeletedAt))
}

// AvatarEQ applies the EQ predicate on the "avatar" field.
func AvatarEQ(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldAvatar, v))
}

// AvatarNEQ applies the NEQ predicate on the "avatar" field.
func AvatarNEQ(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNEQ(FieldAvatar, v))
}

// AvatarIn applies the In predicate on the "avatar" field.
func AvatarIn(vs ...string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldIn(FieldAvatar, vs...))
}

// AvatarNotIn applies the NotIn predicate on the "avatar" field.
func AvatarNotIn(vs ...string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNotIn(FieldAvatar, vs...))
}

// AvatarGT applies the GT predicate on the "avatar" field.
func AvatarGT(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGT(FieldAvatar, v))
}

// AvatarGTE applies the GTE predicate on the "avatar" field.
func AvatarGTE(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGTE(FieldAvatar, v))
}

// AvatarLT applies the LT predicate on the "avatar" field.
func AvatarLT(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLT(FieldAvatar, v))
}

// AvatarLTE applies the LTE predicate on the "avatar" field.
func AvatarLTE(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLTE(FieldAvatar, v))
}

// AvatarContains applies the Contains predicate on the "avatar" field.
func AvatarContains(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldContains(FieldAvatar, v))
}

// AvatarHasPrefix applies the HasPrefix predicate on the "avatar" field.
func AvatarHasPrefix(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldHasPrefix(FieldAvatar, v))
}

// AvatarHasSuffix applies the HasSuffix predicate on the "avatar" field.
func AvatarHasSuffix(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldHasSuffix(FieldAvatar, v))
}

// AvatarEqualFold applies the EqualFold predicate on the "avatar" field.
func AvatarEqualFold(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEqualFold(FieldAvatar, v))
}

// AvatarContainsFold applies the ContainsFold predicate on the "avatar" field.
func AvatarContainsFold(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldContainsFold(FieldAvatar, v))
}

// ClickedAvatarEQ applies the EQ predicate on the "clicked_avatar" field.
func ClickedAvatarEQ(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldClickedAvatar, v))
}

// ClickedAvatarNEQ applies the NEQ predicate on the "clicked_avatar" field.
func ClickedAvatarNEQ(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNEQ(FieldClickedAvatar, v))
}

// ClickedAvatarIn applies the In predicate on the "clicked_avatar" field.
func ClickedAvatarIn(vs ...string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldIn(FieldClickedAvatar, vs...))
}

// ClickedAvatarNotIn applies the NotIn predicate on the "clicked_avatar" field.
func ClickedAvatarNotIn(vs ...string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNotIn(FieldClickedAvatar, vs...))
}

// ClickedAvatarGT applies the GT predicate on the "clicked_avatar" field.
func ClickedAvatarGT(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGT(FieldClickedAvatar, v))
}

// ClickedAvatarGTE applies the GTE predicate on the "clicked_avatar" field.
func ClickedAvatarGTE(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGTE(FieldClickedAvatar, v))
}

// ClickedAvatarLT applies the LT predicate on the "clicked_avatar" field.
func ClickedAvatarLT(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLT(FieldClickedAvatar, v))
}

// ClickedAvatarLTE applies the LTE predicate on the "clicked_avatar" field.
func ClickedAvatarLTE(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLTE(FieldClickedAvatar, v))
}

// ClickedAvatarContains applies the Contains predicate on the "clicked_avatar" field.
func ClickedAvatarContains(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldContains(FieldClickedAvatar, v))
}

// ClickedAvatarHasPrefix applies the HasPrefix predicate on the "clicked_avatar" field.
func ClickedAvatarHasPrefix(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldHasPrefix(FieldClickedAvatar, v))
}

// ClickedAvatarHasSuffix applies the HasSuffix predicate on the "clicked_avatar" field.
func ClickedAvatarHasSuffix(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldHasSuffix(FieldClickedAvatar, v))
}

// ClickedAvatarEqualFold applies the EqualFold predicate on the "clicked_avatar" field.
func ClickedAvatarEqualFold(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEqualFold(FieldClickedAvatar, v))
}

// ClickedAvatarContainsFold applies the ContainsFold predicate on the "clicked_avatar" field.
func ClickedAvatarContainsFold(v string) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldContainsFold(FieldClickedAvatar, v))
}

// AvatarTypeEQ applies the EQ predicate on the "avatar_type" field.
func AvatarTypeEQ(v int8) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldEQ(FieldAvatarType, v))
}

// AvatarTypeNEQ applies the NEQ predicate on the "avatar_type" field.
func AvatarTypeNEQ(v int8) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNEQ(FieldAvatarType, v))
}

// AvatarTypeIn applies the In predicate on the "avatar_type" field.
func AvatarTypeIn(vs ...int8) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldIn(FieldAvatarType, vs...))
}

// AvatarTypeNotIn applies the NotIn predicate on the "avatar_type" field.
func AvatarTypeNotIn(vs ...int8) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldNotIn(FieldAvatarType, vs...))
}

// AvatarTypeGT applies the GT predicate on the "avatar_type" field.
func AvatarTypeGT(v int8) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGT(FieldAvatarType, v))
}

// AvatarTypeGTE applies the GTE predicate on the "avatar_type" field.
func AvatarTypeGTE(v int8) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldGTE(FieldAvatarType, v))
}

// AvatarTypeLT applies the LT predicate on the "avatar_type" field.
func AvatarTypeLT(v int8) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLT(FieldAvatarType, v))
}

// AvatarTypeLTE applies the LTE predicate on the "avatar_type" field.
func AvatarTypeLTE(v int8) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.FieldLTE(FieldAvatarType, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.DefaultAgentAvatar) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.DefaultAgentAvatar) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.DefaultAgentAvatar) predicate.DefaultAgentAvatar {
	return predicate.DefaultAgentAvatar(sql.NotPredicates(p))
}

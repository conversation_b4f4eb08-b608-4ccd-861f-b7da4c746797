// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/defaultagentavatar"
)

// DefaultAgentAvatar is the model entity for the DefaultAgentAvatar schema.
type DefaultAgentAvatar struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 租户ID
	TenantID int64 `json:"tenant_id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 头像key
	Avatar string `json:"avatar,omitempty"`
	// 点击头像key
	ClickedAvatar string `json:"clicked_avatar,omitempty"`
	// 头像类型 1.部门应用图标  2.数字员工图标
	AvatarType   int8 `json:"avatar_type,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*DefaultAgentAvatar) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case defaultagentavatar.FieldID, defaultagentavatar.FieldTenantID, defaultagentavatar.FieldAvatarType:
			values[i] = new(sql.NullInt64)
		case defaultagentavatar.FieldAvatar, defaultagentavatar.FieldClickedAvatar:
			values[i] = new(sql.NullString)
		case defaultagentavatar.FieldCreatedAt, defaultagentavatar.FieldUpdatedAt, defaultagentavatar.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the DefaultAgentAvatar fields.
func (daa *DefaultAgentAvatar) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case defaultagentavatar.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			daa.ID = int64(value.Int64)
		case defaultagentavatar.FieldTenantID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				daa.TenantID = value.Int64
			}
		case defaultagentavatar.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				daa.CreatedAt = value.Time
			}
		case defaultagentavatar.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				daa.UpdatedAt = value.Time
			}
		case defaultagentavatar.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				daa.DeletedAt = value.Time
			}
		case defaultagentavatar.FieldAvatar:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field avatar", values[i])
			} else if value.Valid {
				daa.Avatar = value.String
			}
		case defaultagentavatar.FieldClickedAvatar:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field clicked_avatar", values[i])
			} else if value.Valid {
				daa.ClickedAvatar = value.String
			}
		case defaultagentavatar.FieldAvatarType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field avatar_type", values[i])
			} else if value.Valid {
				daa.AvatarType = int8(value.Int64)
			}
		default:
			daa.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the DefaultAgentAvatar.
// This includes values selected through modifiers, order, etc.
func (daa *DefaultAgentAvatar) Value(name string) (ent.Value, error) {
	return daa.selectValues.Get(name)
}

// Update returns a builder for updating this DefaultAgentAvatar.
// Note that you need to call DefaultAgentAvatar.Unwrap() before calling this method if this DefaultAgentAvatar
// was returned from a transaction, and the transaction was committed or rolled back.
func (daa *DefaultAgentAvatar) Update() *DefaultAgentAvatarUpdateOne {
	return NewDefaultAgentAvatarClient(daa.config).UpdateOne(daa)
}

// Unwrap unwraps the DefaultAgentAvatar entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (daa *DefaultAgentAvatar) Unwrap() *DefaultAgentAvatar {
	_tx, ok := daa.config.driver.(*txDriver)
	if !ok {
		panic("ent: DefaultAgentAvatar is not a transactional entity")
	}
	daa.config.driver = _tx.drv
	return daa
}

// String implements the fmt.Stringer.
func (daa *DefaultAgentAvatar) String() string {
	var builder strings.Builder
	builder.WriteString("DefaultAgentAvatar(")
	builder.WriteString(fmt.Sprintf("id=%v, ", daa.ID))
	builder.WriteString("tenant_id=")
	builder.WriteString(fmt.Sprintf("%v", daa.TenantID))
	builder.WriteString(", ")
	builder.WriteString("created_at=")
	builder.WriteString(daa.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(daa.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(daa.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("avatar=")
	builder.WriteString(daa.Avatar)
	builder.WriteString(", ")
	builder.WriteString("clicked_avatar=")
	builder.WriteString(daa.ClickedAvatar)
	builder.WriteString(", ")
	builder.WriteString("avatar_type=")
	builder.WriteString(fmt.Sprintf("%v", daa.AvatarType))
	builder.WriteByte(')')
	return builder.String()
}

// DefaultAgentAvatars is a parsable slice of DefaultAgentAvatar.
type DefaultAgentAvatars []*DefaultAgentAvatar

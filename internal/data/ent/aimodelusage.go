// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodelusage"
)

// AiModelUsage is the model entity for the AiModelUsage schema.
type AiModelUsage struct {
	config `json:"-"`
	// ID of the ent.
	// 主键
	ID int64 `json:"id,omitempty"`
	// 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// DeletedAt holds the value of the "deleted_at" field.
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// 模型详情ID
	ModelDetailID int64 `json:"model_detail_id,omitempty"`
	// 模型名称
	ModelName string `json:"model_name,omitempty"`
	// 模型网关名称
	ModelGatewayName string `json:"model_gateway_name,omitempty"`
	// 智能体ID
	AgentID int64 `json:"agent_id,omitempty"`
	// 智能体名称
	AgentName string `json:"agent_name,omitempty"`
	// 用户ID
	UserID int64 `json:"user_id,omitempty"`
	// 用户名称
	UserName string `json:"user_name,omitempty"`
	// 用户提问
	Question string `json:"question,omitempty"`
	// 模型回答
	Answer string `json:"answer,omitempty"`
	// 提示词token数量
	PromptTokens int64 `json:"prompt_tokens,omitempty"`
	// 完成token数量
	CompletionTokens int64 `json:"completion_tokens,omitempty"`
	// 0: 未知, 1: 成功, 2: 失败
	RequestStatus int8 `json:"request_status,omitempty"`
	// 错误码
	ErrorCode    string `json:"error_code,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AiModelUsage) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case aimodelusage.FieldID, aimodelusage.FieldModelDetailID, aimodelusage.FieldAgentID, aimodelusage.FieldUserID, aimodelusage.FieldPromptTokens, aimodelusage.FieldCompletionTokens, aimodelusage.FieldRequestStatus:
			values[i] = new(sql.NullInt64)
		case aimodelusage.FieldModelName, aimodelusage.FieldModelGatewayName, aimodelusage.FieldAgentName, aimodelusage.FieldUserName, aimodelusage.FieldQuestion, aimodelusage.FieldAnswer, aimodelusage.FieldErrorCode:
			values[i] = new(sql.NullString)
		case aimodelusage.FieldCreatedAt, aimodelusage.FieldUpdatedAt, aimodelusage.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AiModelUsage fields.
func (amu *AiModelUsage) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case aimodelusage.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			amu.ID = int64(value.Int64)
		case aimodelusage.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				amu.CreatedAt = value.Time
			}
		case aimodelusage.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				amu.UpdatedAt = value.Time
			}
		case aimodelusage.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				amu.DeletedAt = value.Time
			}
		case aimodelusage.FieldModelDetailID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field model_detail_id", values[i])
			} else if value.Valid {
				amu.ModelDetailID = value.Int64
			}
		case aimodelusage.FieldModelName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field model_name", values[i])
			} else if value.Valid {
				amu.ModelName = value.String
			}
		case aimodelusage.FieldModelGatewayName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field model_gateway_name", values[i])
			} else if value.Valid {
				amu.ModelGatewayName = value.String
			}
		case aimodelusage.FieldAgentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field agent_id", values[i])
			} else if value.Valid {
				amu.AgentID = value.Int64
			}
		case aimodelusage.FieldAgentName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field agent_name", values[i])
			} else if value.Valid {
				amu.AgentName = value.String
			}
		case aimodelusage.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				amu.UserID = value.Int64
			}
		case aimodelusage.FieldUserName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_name", values[i])
			} else if value.Valid {
				amu.UserName = value.String
			}
		case aimodelusage.FieldQuestion:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field question", values[i])
			} else if value.Valid {
				amu.Question = value.String
			}
		case aimodelusage.FieldAnswer:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field answer", values[i])
			} else if value.Valid {
				amu.Answer = value.String
			}
		case aimodelusage.FieldPromptTokens:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field prompt_tokens", values[i])
			} else if value.Valid {
				amu.PromptTokens = value.Int64
			}
		case aimodelusage.FieldCompletionTokens:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field completion_tokens", values[i])
			} else if value.Valid {
				amu.CompletionTokens = value.Int64
			}
		case aimodelusage.FieldRequestStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field request_status", values[i])
			} else if value.Valid {
				amu.RequestStatus = int8(value.Int64)
			}
		case aimodelusage.FieldErrorCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field error_code", values[i])
			} else if value.Valid {
				amu.ErrorCode = value.String
			}
		default:
			amu.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AiModelUsage.
// This includes values selected through modifiers, order, etc.
func (amu *AiModelUsage) Value(name string) (ent.Value, error) {
	return amu.selectValues.Get(name)
}

// Update returns a builder for updating this AiModelUsage.
// Note that you need to call AiModelUsage.Unwrap() before calling this method if this AiModelUsage
// was returned from a transaction, and the transaction was committed or rolled back.
func (amu *AiModelUsage) Update() *AiModelUsageUpdateOne {
	return NewAiModelUsageClient(amu.config).UpdateOne(amu)
}

// Unwrap unwraps the AiModelUsage entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (amu *AiModelUsage) Unwrap() *AiModelUsage {
	_tx, ok := amu.config.driver.(*txDriver)
	if !ok {
		panic("ent: AiModelUsage is not a transactional entity")
	}
	amu.config.driver = _tx.drv
	return amu
}

// String implements the fmt.Stringer.
func (amu *AiModelUsage) String() string {
	var builder strings.Builder
	builder.WriteString("AiModelUsage(")
	builder.WriteString(fmt.Sprintf("id=%v, ", amu.ID))
	builder.WriteString("created_at=")
	builder.WriteString(amu.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(amu.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(amu.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("model_detail_id=")
	builder.WriteString(fmt.Sprintf("%v", amu.ModelDetailID))
	builder.WriteString(", ")
	builder.WriteString("model_name=")
	builder.WriteString(amu.ModelName)
	builder.WriteString(", ")
	builder.WriteString("model_gateway_name=")
	builder.WriteString(amu.ModelGatewayName)
	builder.WriteString(", ")
	builder.WriteString("agent_id=")
	builder.WriteString(fmt.Sprintf("%v", amu.AgentID))
	builder.WriteString(", ")
	builder.WriteString("agent_name=")
	builder.WriteString(amu.AgentName)
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", amu.UserID))
	builder.WriteString(", ")
	builder.WriteString("user_name=")
	builder.WriteString(amu.UserName)
	builder.WriteString(", ")
	builder.WriteString("question=")
	builder.WriteString(amu.Question)
	builder.WriteString(", ")
	builder.WriteString("answer=")
	builder.WriteString(amu.Answer)
	builder.WriteString(", ")
	builder.WriteString("prompt_tokens=")
	builder.WriteString(fmt.Sprintf("%v", amu.PromptTokens))
	builder.WriteString(", ")
	builder.WriteString("completion_tokens=")
	builder.WriteString(fmt.Sprintf("%v", amu.CompletionTokens))
	builder.WriteString(", ")
	builder.WriteString("request_status=")
	builder.WriteString(fmt.Sprintf("%v", amu.RequestStatus))
	builder.WriteString(", ")
	builder.WriteString("error_code=")
	builder.WriteString(amu.ErrorCode)
	builder.WriteByte(')')
	return builder.String()
}

// AiModelUsages is a parsable slice of AiModelUsage.
type AiModelUsages []*AiModelUsage

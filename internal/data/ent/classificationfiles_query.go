// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/classificationfiles"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ClassificationFilesQuery is the builder for querying ClassificationFiles entities.
type ClassificationFilesQuery struct {
	config
	ctx        *QueryContext
	order      []classificationfiles.OrderOption
	inters     []Interceptor
	predicates []predicate.ClassificationFiles
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ClassificationFilesQuery builder.
func (cfq *ClassificationFilesQuery) Where(ps ...predicate.ClassificationFiles) *ClassificationFilesQuery {
	cfq.predicates = append(cfq.predicates, ps...)
	return cfq
}

// Limit the number of records to be returned by this query.
func (cfq *ClassificationFilesQuery) Limit(limit int) *ClassificationFilesQuery {
	cfq.ctx.Limit = &limit
	return cfq
}

// Offset to start from.
func (cfq *ClassificationFilesQuery) Offset(offset int) *ClassificationFilesQuery {
	cfq.ctx.Offset = &offset
	return cfq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (cfq *ClassificationFilesQuery) Unique(unique bool) *ClassificationFilesQuery {
	cfq.ctx.Unique = &unique
	return cfq
}

// Order specifies how the records should be ordered.
func (cfq *ClassificationFilesQuery) Order(o ...classificationfiles.OrderOption) *ClassificationFilesQuery {
	cfq.order = append(cfq.order, o...)
	return cfq
}

// First returns the first ClassificationFiles entity from the query.
// Returns a *NotFoundError when no ClassificationFiles was found.
func (cfq *ClassificationFilesQuery) First(ctx context.Context) (*ClassificationFiles, error) {
	nodes, err := cfq.Limit(1).All(setContextOp(ctx, cfq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{classificationfiles.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (cfq *ClassificationFilesQuery) FirstX(ctx context.Context) *ClassificationFiles {
	node, err := cfq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first ClassificationFiles ID from the query.
// Returns a *NotFoundError when no ClassificationFiles ID was found.
func (cfq *ClassificationFilesQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = cfq.Limit(1).IDs(setContextOp(ctx, cfq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{classificationfiles.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (cfq *ClassificationFilesQuery) FirstIDX(ctx context.Context) int64 {
	id, err := cfq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single ClassificationFiles entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one ClassificationFiles entity is found.
// Returns a *NotFoundError when no ClassificationFiles entities are found.
func (cfq *ClassificationFilesQuery) Only(ctx context.Context) (*ClassificationFiles, error) {
	nodes, err := cfq.Limit(2).All(setContextOp(ctx, cfq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{classificationfiles.Label}
	default:
		return nil, &NotSingularError{classificationfiles.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (cfq *ClassificationFilesQuery) OnlyX(ctx context.Context) *ClassificationFiles {
	node, err := cfq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only ClassificationFiles ID in the query.
// Returns a *NotSingularError when more than one ClassificationFiles ID is found.
// Returns a *NotFoundError when no entities are found.
func (cfq *ClassificationFilesQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = cfq.Limit(2).IDs(setContextOp(ctx, cfq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{classificationfiles.Label}
	default:
		err = &NotSingularError{classificationfiles.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (cfq *ClassificationFilesQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := cfq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of ClassificationFilesSlice.
func (cfq *ClassificationFilesQuery) All(ctx context.Context) ([]*ClassificationFiles, error) {
	ctx = setContextOp(ctx, cfq.ctx, "All")
	if err := cfq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*ClassificationFiles, *ClassificationFilesQuery]()
	return withInterceptors[[]*ClassificationFiles](ctx, cfq, qr, cfq.inters)
}

// AllX is like All, but panics if an error occurs.
func (cfq *ClassificationFilesQuery) AllX(ctx context.Context) []*ClassificationFiles {
	nodes, err := cfq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of ClassificationFiles IDs.
func (cfq *ClassificationFilesQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if cfq.ctx.Unique == nil && cfq.path != nil {
		cfq.Unique(true)
	}
	ctx = setContextOp(ctx, cfq.ctx, "IDs")
	if err = cfq.Select(classificationfiles.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (cfq *ClassificationFilesQuery) IDsX(ctx context.Context) []int64 {
	ids, err := cfq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (cfq *ClassificationFilesQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, cfq.ctx, "Count")
	if err := cfq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, cfq, querierCount[*ClassificationFilesQuery](), cfq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (cfq *ClassificationFilesQuery) CountX(ctx context.Context) int {
	count, err := cfq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (cfq *ClassificationFilesQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, cfq.ctx, "Exist")
	switch _, err := cfq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (cfq *ClassificationFilesQuery) ExistX(ctx context.Context) bool {
	exist, err := cfq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ClassificationFilesQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (cfq *ClassificationFilesQuery) Clone() *ClassificationFilesQuery {
	if cfq == nil {
		return nil
	}
	return &ClassificationFilesQuery{
		config:     cfq.config,
		ctx:        cfq.ctx.Clone(),
		order:      append([]classificationfiles.OrderOption{}, cfq.order...),
		inters:     append([]Interceptor{}, cfq.inters...),
		predicates: append([]predicate.ClassificationFiles{}, cfq.predicates...),
		// clone intermediate query.
		sql:  cfq.sql.Clone(),
		path: cfq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.ClassificationFiles.Query().
//		GroupBy(classificationfiles.FieldName).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (cfq *ClassificationFilesQuery) GroupBy(field string, fields ...string) *ClassificationFilesGroupBy {
	cfq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ClassificationFilesGroupBy{build: cfq}
	grbuild.flds = &cfq.ctx.Fields
	grbuild.label = classificationfiles.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		Name string `json:"name,omitempty"`
//	}
//
//	client.ClassificationFiles.Query().
//		Select(classificationfiles.FieldName).
//		Scan(ctx, &v)
func (cfq *ClassificationFilesQuery) Select(fields ...string) *ClassificationFilesSelect {
	cfq.ctx.Fields = append(cfq.ctx.Fields, fields...)
	sbuild := &ClassificationFilesSelect{ClassificationFilesQuery: cfq}
	sbuild.label = classificationfiles.Label
	sbuild.flds, sbuild.scan = &cfq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ClassificationFilesSelect configured with the given aggregations.
func (cfq *ClassificationFilesQuery) Aggregate(fns ...AggregateFunc) *ClassificationFilesSelect {
	return cfq.Select().Aggregate(fns...)
}

func (cfq *ClassificationFilesQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range cfq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, cfq); err != nil {
				return err
			}
		}
	}
	for _, f := range cfq.ctx.Fields {
		if !classificationfiles.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if cfq.path != nil {
		prev, err := cfq.path(ctx)
		if err != nil {
			return err
		}
		cfq.sql = prev
	}
	return nil
}

func (cfq *ClassificationFilesQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*ClassificationFiles, error) {
	var (
		nodes = []*ClassificationFiles{}
		_spec = cfq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*ClassificationFiles).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &ClassificationFiles{config: cfq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(cfq.modifiers) > 0 {
		_spec.Modifiers = cfq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, cfq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (cfq *ClassificationFilesQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := cfq.querySpec()
	if len(cfq.modifiers) > 0 {
		_spec.Modifiers = cfq.modifiers
	}
	_spec.Node.Columns = cfq.ctx.Fields
	if len(cfq.ctx.Fields) > 0 {
		_spec.Unique = cfq.ctx.Unique != nil && *cfq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, cfq.driver, _spec)
}

func (cfq *ClassificationFilesQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(classificationfiles.Table, classificationfiles.Columns, sqlgraph.NewFieldSpec(classificationfiles.FieldID, field.TypeInt64))
	_spec.From = cfq.sql
	if unique := cfq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if cfq.path != nil {
		_spec.Unique = true
	}
	if fields := cfq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, classificationfiles.FieldID)
		for i := range fields {
			if fields[i] != classificationfiles.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := cfq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := cfq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := cfq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := cfq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (cfq *ClassificationFilesQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(cfq.driver.Dialect())
	t1 := builder.Table(classificationfiles.Table)
	columns := cfq.ctx.Fields
	if len(columns) == 0 {
		columns = classificationfiles.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if cfq.sql != nil {
		selector = cfq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if cfq.ctx.Unique != nil && *cfq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range cfq.modifiers {
		m(selector)
	}
	for _, p := range cfq.predicates {
		p(selector)
	}
	for _, p := range cfq.order {
		p(selector)
	}
	if offset := cfq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := cfq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (cfq *ClassificationFilesQuery) ForUpdate(opts ...sql.LockOption) *ClassificationFilesQuery {
	if cfq.driver.Dialect() == dialect.Postgres {
		cfq.Unique(false)
	}
	cfq.modifiers = append(cfq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return cfq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (cfq *ClassificationFilesQuery) ForShare(opts ...sql.LockOption) *ClassificationFilesQuery {
	if cfq.driver.Dialect() == dialect.Postgres {
		cfq.Unique(false)
	}
	cfq.modifiers = append(cfq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return cfq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (cfq *ClassificationFilesQuery) Modify(modifiers ...func(s *sql.Selector)) *ClassificationFilesSelect {
	cfq.modifiers = append(cfq.modifiers, modifiers...)
	return cfq.Select()
}

// ClassificationFilesGroupBy is the group-by builder for ClassificationFiles entities.
type ClassificationFilesGroupBy struct {
	selector
	build *ClassificationFilesQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (cfgb *ClassificationFilesGroupBy) Aggregate(fns ...AggregateFunc) *ClassificationFilesGroupBy {
	cfgb.fns = append(cfgb.fns, fns...)
	return cfgb
}

// Scan applies the selector query and scans the result into the given value.
func (cfgb *ClassificationFilesGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cfgb.build.ctx, "GroupBy")
	if err := cfgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ClassificationFilesQuery, *ClassificationFilesGroupBy](ctx, cfgb.build, cfgb, cfgb.build.inters, v)
}

func (cfgb *ClassificationFilesGroupBy) sqlScan(ctx context.Context, root *ClassificationFilesQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(cfgb.fns))
	for _, fn := range cfgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*cfgb.flds)+len(cfgb.fns))
		for _, f := range *cfgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*cfgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cfgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ClassificationFilesSelect is the builder for selecting fields of ClassificationFiles entities.
type ClassificationFilesSelect struct {
	*ClassificationFilesQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (cfs *ClassificationFilesSelect) Aggregate(fns ...AggregateFunc) *ClassificationFilesSelect {
	cfs.fns = append(cfs.fns, fns...)
	return cfs
}

// Scan applies the selector query and scans the result into the given value.
func (cfs *ClassificationFilesSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, cfs.ctx, "Select")
	if err := cfs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ClassificationFilesQuery, *ClassificationFilesSelect](ctx, cfs.ClassificationFilesQuery, cfs, cfs.inters, v)
}

func (cfs *ClassificationFilesSelect) sqlScan(ctx context.Context, root *ClassificationFilesQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(cfs.fns))
	for _, fn := range cfs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*cfs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := cfs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (cfs *ClassificationFilesSelect) Modify(modifiers ...func(s *sql.Selector)) *ClassificationFilesSelect {
	cfs.modifiers = append(cfs.modifiers, modifiers...)
	return cfs
}

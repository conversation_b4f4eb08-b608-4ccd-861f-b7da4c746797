// Code generated by ent, DO NOT EDIT.

package atomicquestions

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

const (
	// Label holds the string label denoting the atomicquestions type in the database.
	Label = "atomic_questions"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldEntityTag holds the string denoting the entity_tag field in the database.
	FieldEntityTag = "entity_tag"
	// FieldPreEntityTag holds the string denoting the pre_entity_tag field in the database.
	FieldPreEntityTag = "pre_entity_tag"
	// FieldFileRelationID holds the string denoting the file_relation_id field in the database.
	FieldFileRelationID = "file_relation_id"
	// FieldChunkIndex holds the string denoting the chunk_index field in the database.
	FieldChunkIndex = "chunk_index"
	// FieldChunkSize holds the string denoting the chunk_size field in the database.
	FieldChunkSize = "chunk_size"
	// FieldIndex holds the string denoting the index field in the database.
	FieldIndex = "index"
	// FieldQuestion holds the string denoting the question field in the database.
	FieldQuestion = "question"
	// FieldIsHandle holds the string denoting the is_handle field in the database.
	FieldIsHandle = "is_handle"
	// Table holds the table name of the atomicquestions in the database.
	Table = "atomic_questions"
)

// Columns holds all SQL columns for atomicquestions fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldEntityTag,
	FieldPreEntityTag,
	FieldFileRelationID,
	FieldChunkIndex,
	FieldChunkSize,
	FieldIndex,
	FieldQuestion,
	FieldIsHandle,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultQuestion holds the default value on creation for the "question" field.
	DefaultQuestion *pq.StringArray
	// DefaultIsHandle holds the default value on creation for the "is_handle" field.
	DefaultIsHandle bool
)

// OrderOption defines the ordering options for the AtomicQuestions queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByEntityTag orders the results by the entity_tag field.
func ByEntityTag(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEntityTag, opts...).ToFunc()
}

// ByPreEntityTag orders the results by the pre_entity_tag field.
func ByPreEntityTag(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPreEntityTag, opts...).ToFunc()
}

// ByFileRelationID orders the results by the file_relation_id field.
func ByFileRelationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileRelationID, opts...).ToFunc()
}

// ByChunkIndex orders the results by the chunk_index field.
func ByChunkIndex(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldChunkIndex, opts...).ToFunc()
}

// ByChunkSize orders the results by the chunk_size field.
func ByChunkSize(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldChunkSize, opts...).ToFunc()
}

// ByIndex orders the results by the index field.
func ByIndex(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIndex, opts...).ToFunc()
}

// ByQuestion orders the results by the question field.
func ByQuestion(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldQuestion, opts...).ToFunc()
}

// ByIsHandle orders the results by the is_handle field.
func ByIsHandle(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsHandle, opts...).ToFunc()
}

// Code generated by ent, DO NOT EDIT.

package atomicquestions

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldUpdatedAt, v))
}

// EntityTag applies equality check predicate on the "entity_tag" field. It's identical to EntityTagEQ.
func EntityTag(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldEntityTag, v))
}

// PreEntityTag applies equality check predicate on the "pre_entity_tag" field. It's identical to PreEntityTagEQ.
func PreEntityTag(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldPreEntityTag, v))
}

// FileRelationID applies equality check predicate on the "file_relation_id" field. It's identical to FileRelationIDEQ.
func FileRelationID(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldFileRelationID, v))
}

// ChunkIndex applies equality check predicate on the "chunk_index" field. It's identical to ChunkIndexEQ.
func ChunkIndex(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldChunkIndex, v))
}

// ChunkSize applies equality check predicate on the "chunk_size" field. It's identical to ChunkSizeEQ.
func ChunkSize(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldChunkSize, v))
}

// Index applies equality check predicate on the "index" field. It's identical to IndexEQ.
func Index(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldIndex, v))
}

// Question applies equality check predicate on the "question" field. It's identical to QuestionEQ.
func Question(v *pq.StringArray) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldQuestion, v))
}

// IsHandle applies equality check predicate on the "is_handle" field. It's identical to IsHandleEQ.
func IsHandle(v bool) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldIsHandle, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldUpdatedAt, v))
}

// EntityTagEQ applies the EQ predicate on the "entity_tag" field.
func EntityTagEQ(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldEntityTag, v))
}

// EntityTagNEQ applies the NEQ predicate on the "entity_tag" field.
func EntityTagNEQ(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldEntityTag, v))
}

// EntityTagIn applies the In predicate on the "entity_tag" field.
func EntityTagIn(vs ...string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldEntityTag, vs...))
}

// EntityTagNotIn applies the NotIn predicate on the "entity_tag" field.
func EntityTagNotIn(vs ...string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldEntityTag, vs...))
}

// EntityTagGT applies the GT predicate on the "entity_tag" field.
func EntityTagGT(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldEntityTag, v))
}

// EntityTagGTE applies the GTE predicate on the "entity_tag" field.
func EntityTagGTE(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldEntityTag, v))
}

// EntityTagLT applies the LT predicate on the "entity_tag" field.
func EntityTagLT(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldEntityTag, v))
}

// EntityTagLTE applies the LTE predicate on the "entity_tag" field.
func EntityTagLTE(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldEntityTag, v))
}

// EntityTagContains applies the Contains predicate on the "entity_tag" field.
func EntityTagContains(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldContains(FieldEntityTag, v))
}

// EntityTagHasPrefix applies the HasPrefix predicate on the "entity_tag" field.
func EntityTagHasPrefix(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldHasPrefix(FieldEntityTag, v))
}

// EntityTagHasSuffix applies the HasSuffix predicate on the "entity_tag" field.
func EntityTagHasSuffix(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldHasSuffix(FieldEntityTag, v))
}

// EntityTagEqualFold applies the EqualFold predicate on the "entity_tag" field.
func EntityTagEqualFold(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEqualFold(FieldEntityTag, v))
}

// EntityTagContainsFold applies the ContainsFold predicate on the "entity_tag" field.
func EntityTagContainsFold(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldContainsFold(FieldEntityTag, v))
}

// PreEntityTagEQ applies the EQ predicate on the "pre_entity_tag" field.
func PreEntityTagEQ(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldPreEntityTag, v))
}

// PreEntityTagNEQ applies the NEQ predicate on the "pre_entity_tag" field.
func PreEntityTagNEQ(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldPreEntityTag, v))
}

// PreEntityTagIn applies the In predicate on the "pre_entity_tag" field.
func PreEntityTagIn(vs ...string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldPreEntityTag, vs...))
}

// PreEntityTagNotIn applies the NotIn predicate on the "pre_entity_tag" field.
func PreEntityTagNotIn(vs ...string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldPreEntityTag, vs...))
}

// PreEntityTagGT applies the GT predicate on the "pre_entity_tag" field.
func PreEntityTagGT(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldPreEntityTag, v))
}

// PreEntityTagGTE applies the GTE predicate on the "pre_entity_tag" field.
func PreEntityTagGTE(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldPreEntityTag, v))
}

// PreEntityTagLT applies the LT predicate on the "pre_entity_tag" field.
func PreEntityTagLT(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldPreEntityTag, v))
}

// PreEntityTagLTE applies the LTE predicate on the "pre_entity_tag" field.
func PreEntityTagLTE(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldPreEntityTag, v))
}

// PreEntityTagContains applies the Contains predicate on the "pre_entity_tag" field.
func PreEntityTagContains(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldContains(FieldPreEntityTag, v))
}

// PreEntityTagHasPrefix applies the HasPrefix predicate on the "pre_entity_tag" field.
func PreEntityTagHasPrefix(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldHasPrefix(FieldPreEntityTag, v))
}

// PreEntityTagHasSuffix applies the HasSuffix predicate on the "pre_entity_tag" field.
func PreEntityTagHasSuffix(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldHasSuffix(FieldPreEntityTag, v))
}

// PreEntityTagEqualFold applies the EqualFold predicate on the "pre_entity_tag" field.
func PreEntityTagEqualFold(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEqualFold(FieldPreEntityTag, v))
}

// PreEntityTagContainsFold applies the ContainsFold predicate on the "pre_entity_tag" field.
func PreEntityTagContainsFold(v string) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldContainsFold(FieldPreEntityTag, v))
}

// FileRelationIDEQ applies the EQ predicate on the "file_relation_id" field.
func FileRelationIDEQ(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldFileRelationID, v))
}

// FileRelationIDNEQ applies the NEQ predicate on the "file_relation_id" field.
func FileRelationIDNEQ(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldFileRelationID, v))
}

// FileRelationIDIn applies the In predicate on the "file_relation_id" field.
func FileRelationIDIn(vs ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldFileRelationID, vs...))
}

// FileRelationIDNotIn applies the NotIn predicate on the "file_relation_id" field.
func FileRelationIDNotIn(vs ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldFileRelationID, vs...))
}

// FileRelationIDGT applies the GT predicate on the "file_relation_id" field.
func FileRelationIDGT(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldFileRelationID, v))
}

// FileRelationIDGTE applies the GTE predicate on the "file_relation_id" field.
func FileRelationIDGTE(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldFileRelationID, v))
}

// FileRelationIDLT applies the LT predicate on the "file_relation_id" field.
func FileRelationIDLT(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldFileRelationID, v))
}

// FileRelationIDLTE applies the LTE predicate on the "file_relation_id" field.
func FileRelationIDLTE(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldFileRelationID, v))
}

// ChunkIndexEQ applies the EQ predicate on the "chunk_index" field.
func ChunkIndexEQ(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldChunkIndex, v))
}

// ChunkIndexNEQ applies the NEQ predicate on the "chunk_index" field.
func ChunkIndexNEQ(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldChunkIndex, v))
}

// ChunkIndexIn applies the In predicate on the "chunk_index" field.
func ChunkIndexIn(vs ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldChunkIndex, vs...))
}

// ChunkIndexNotIn applies the NotIn predicate on the "chunk_index" field.
func ChunkIndexNotIn(vs ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldChunkIndex, vs...))
}

// ChunkIndexGT applies the GT predicate on the "chunk_index" field.
func ChunkIndexGT(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldChunkIndex, v))
}

// ChunkIndexGTE applies the GTE predicate on the "chunk_index" field.
func ChunkIndexGTE(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldChunkIndex, v))
}

// ChunkIndexLT applies the LT predicate on the "chunk_index" field.
func ChunkIndexLT(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldChunkIndex, v))
}

// ChunkIndexLTE applies the LTE predicate on the "chunk_index" field.
func ChunkIndexLTE(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldChunkIndex, v))
}

// ChunkSizeEQ applies the EQ predicate on the "chunk_size" field.
func ChunkSizeEQ(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldChunkSize, v))
}

// ChunkSizeNEQ applies the NEQ predicate on the "chunk_size" field.
func ChunkSizeNEQ(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldChunkSize, v))
}

// ChunkSizeIn applies the In predicate on the "chunk_size" field.
func ChunkSizeIn(vs ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldChunkSize, vs...))
}

// ChunkSizeNotIn applies the NotIn predicate on the "chunk_size" field.
func ChunkSizeNotIn(vs ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldChunkSize, vs...))
}

// ChunkSizeGT applies the GT predicate on the "chunk_size" field.
func ChunkSizeGT(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldChunkSize, v))
}

// ChunkSizeGTE applies the GTE predicate on the "chunk_size" field.
func ChunkSizeGTE(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldChunkSize, v))
}

// ChunkSizeLT applies the LT predicate on the "chunk_size" field.
func ChunkSizeLT(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldChunkSize, v))
}

// ChunkSizeLTE applies the LTE predicate on the "chunk_size" field.
func ChunkSizeLTE(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldChunkSize, v))
}

// IndexEQ applies the EQ predicate on the "index" field.
func IndexEQ(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldIndex, v))
}

// IndexNEQ applies the NEQ predicate on the "index" field.
func IndexNEQ(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldIndex, v))
}

// IndexIn applies the In predicate on the "index" field.
func IndexIn(vs ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldIndex, vs...))
}

// IndexNotIn applies the NotIn predicate on the "index" field.
func IndexNotIn(vs ...int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldIndex, vs...))
}

// IndexGT applies the GT predicate on the "index" field.
func IndexGT(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldIndex, v))
}

// IndexGTE applies the GTE predicate on the "index" field.
func IndexGTE(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldIndex, v))
}

// IndexLT applies the LT predicate on the "index" field.
func IndexLT(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldIndex, v))
}

// IndexLTE applies the LTE predicate on the "index" field.
func IndexLTE(v int64) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldIndex, v))
}

// QuestionEQ applies the EQ predicate on the "question" field.
func QuestionEQ(v *pq.StringArray) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldQuestion, v))
}

// QuestionNEQ applies the NEQ predicate on the "question" field.
func QuestionNEQ(v *pq.StringArray) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldQuestion, v))
}

// QuestionIn applies the In predicate on the "question" field.
func QuestionIn(vs ...*pq.StringArray) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldIn(FieldQuestion, vs...))
}

// QuestionNotIn applies the NotIn predicate on the "question" field.
func QuestionNotIn(vs ...*pq.StringArray) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNotIn(FieldQuestion, vs...))
}

// QuestionGT applies the GT predicate on the "question" field.
func QuestionGT(v *pq.StringArray) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGT(FieldQuestion, v))
}

// QuestionGTE applies the GTE predicate on the "question" field.
func QuestionGTE(v *pq.StringArray) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldGTE(FieldQuestion, v))
}

// QuestionLT applies the LT predicate on the "question" field.
func QuestionLT(v *pq.StringArray) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLT(FieldQuestion, v))
}

// QuestionLTE applies the LTE predicate on the "question" field.
func QuestionLTE(v *pq.StringArray) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldLTE(FieldQuestion, v))
}

// IsHandleEQ applies the EQ predicate on the "is_handle" field.
func IsHandleEQ(v bool) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldEQ(FieldIsHandle, v))
}

// IsHandleNEQ applies the NEQ predicate on the "is_handle" field.
func IsHandleNEQ(v bool) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.FieldNEQ(FieldIsHandle, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AtomicQuestions) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AtomicQuestions) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AtomicQuestions) predicate.AtomicQuestions {
	return predicate.AtomicQuestions(sql.NotPredicates(p))
}

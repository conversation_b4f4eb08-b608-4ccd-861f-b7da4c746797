// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiModelUsageDelete is the builder for deleting a AiModelUsage entity.
type AiModelUsageDelete struct {
	config
	hooks    []Hook
	mutation *AiModelUsageMutation
}

// Where appends a list predicates to the AiModelUsageDelete builder.
func (amud *AiModelUsageDelete) Where(ps ...predicate.AiModelUsage) *AiModelUsageDelete {
	amud.mutation.Where(ps...)
	return amud
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (amud *AiModelUsageDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, amud.sqlExec, amud.mutation, amud.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (amud *AiModelUsageDelete) ExecX(ctx context.Context) int {
	n, err := amud.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (amud *AiModelUsageDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(aimodelusage.Table, sqlgraph.NewFieldSpec(aimodelusage.FieldID, field.TypeInt64))
	if ps := amud.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, amud.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	amud.mutation.done = true
	return affected, err
}

// AiModelUsageDeleteOne is the builder for deleting a single AiModelUsage entity.
type AiModelUsageDeleteOne struct {
	amud *AiModelUsageDelete
}

// Where appends a list predicates to the AiModelUsageDelete builder.
func (amudo *AiModelUsageDeleteOne) Where(ps ...predicate.AiModelUsage) *AiModelUsageDeleteOne {
	amudo.amud.mutation.Where(ps...)
	return amudo
}

// Exec executes the deletion query.
func (amudo *AiModelUsageDeleteOne) Exec(ctx context.Context) error {
	n, err := amudo.amud.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{aimodelusage.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (amudo *AiModelUsageDeleteOne) ExecX(ctx context.Context) {
	if err := amudo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package classificationfiles

import (
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldID, id))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldName, v))
}

// FileRelationID applies equality check predicate on the "file_relation_id" field. It's identical to FileRelationIDEQ.
func FileRelationID(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldFileRelationID, v))
}

// PreEntityTag applies equality check predicate on the "pre_entity_tag" field. It's identical to PreEntityTagEQ.
func PreEntityTag(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldPreEntityTag, v))
}

// EntityTag applies equality check predicate on the "entity_tag" field. It's identical to EntityTagEQ.
func EntityTag(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldEntityTag, v))
}

// Filename applies equality check predicate on the "filename" field. It's identical to FilenameEQ.
func Filename(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldFilename, v))
}

// MimeType applies equality check predicate on the "mime_type" field. It's identical to MimeTypeEQ.
func MimeType(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldMimeType, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldUserID, v))
}

// UserName applies equality check predicate on the "user_name" field. It's identical to UserNameEQ.
func UserName(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldUserName, v))
}

// DeptIds applies equality check predicate on the "dept_ids" field. It's identical to DeptIdsEQ.
func DeptIds(v *pq.Int64Array) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldDeptIds, v))
}

// DeptName applies equality check predicate on the "dept_name" field. It's identical to DeptNameEQ.
func DeptName(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldDeptName, v))
}

// Path applies equality check predicate on the "path" field. It's identical to PathEQ.
func Path(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldPath, v))
}

// SecurityLevel applies equality check predicate on the "security_level" field. It's identical to SecurityLevelEQ.
func SecurityLevel(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldSecurityLevel, v))
}

// NodeLevel applies equality check predicate on the "node_level" field. It's identical to NodeLevelEQ.
func NodeLevel(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldNodeLevel, v))
}

// CheckStatus applies equality check predicate on the "check_status" field. It's identical to CheckStatusEQ.
func CheckStatus(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldCheckStatus, v))
}

// TreeType applies equality check predicate on the "tree_type" field. It's identical to TreeTypeEQ.
func TreeType(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldTreeType, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContainsFold(FieldName, v))
}

// FileRelationIDEQ applies the EQ predicate on the "file_relation_id" field.
func FileRelationIDEQ(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldFileRelationID, v))
}

// FileRelationIDNEQ applies the NEQ predicate on the "file_relation_id" field.
func FileRelationIDNEQ(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldFileRelationID, v))
}

// FileRelationIDIn applies the In predicate on the "file_relation_id" field.
func FileRelationIDIn(vs ...int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldFileRelationID, vs...))
}

// FileRelationIDNotIn applies the NotIn predicate on the "file_relation_id" field.
func FileRelationIDNotIn(vs ...int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldFileRelationID, vs...))
}

// FileRelationIDGT applies the GT predicate on the "file_relation_id" field.
func FileRelationIDGT(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldFileRelationID, v))
}

// FileRelationIDGTE applies the GTE predicate on the "file_relation_id" field.
func FileRelationIDGTE(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldFileRelationID, v))
}

// FileRelationIDLT applies the LT predicate on the "file_relation_id" field.
func FileRelationIDLT(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldFileRelationID, v))
}

// FileRelationIDLTE applies the LTE predicate on the "file_relation_id" field.
func FileRelationIDLTE(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldFileRelationID, v))
}

// PreEntityTagEQ applies the EQ predicate on the "pre_entity_tag" field.
func PreEntityTagEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldPreEntityTag, v))
}

// PreEntityTagNEQ applies the NEQ predicate on the "pre_entity_tag" field.
func PreEntityTagNEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldPreEntityTag, v))
}

// PreEntityTagIn applies the In predicate on the "pre_entity_tag" field.
func PreEntityTagIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldPreEntityTag, vs...))
}

// PreEntityTagNotIn applies the NotIn predicate on the "pre_entity_tag" field.
func PreEntityTagNotIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldPreEntityTag, vs...))
}

// PreEntityTagGT applies the GT predicate on the "pre_entity_tag" field.
func PreEntityTagGT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldPreEntityTag, v))
}

// PreEntityTagGTE applies the GTE predicate on the "pre_entity_tag" field.
func PreEntityTagGTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldPreEntityTag, v))
}

// PreEntityTagLT applies the LT predicate on the "pre_entity_tag" field.
func PreEntityTagLT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldPreEntityTag, v))
}

// PreEntityTagLTE applies the LTE predicate on the "pre_entity_tag" field.
func PreEntityTagLTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldPreEntityTag, v))
}

// PreEntityTagContains applies the Contains predicate on the "pre_entity_tag" field.
func PreEntityTagContains(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContains(FieldPreEntityTag, v))
}

// PreEntityTagHasPrefix applies the HasPrefix predicate on the "pre_entity_tag" field.
func PreEntityTagHasPrefix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasPrefix(FieldPreEntityTag, v))
}

// PreEntityTagHasSuffix applies the HasSuffix predicate on the "pre_entity_tag" field.
func PreEntityTagHasSuffix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasSuffix(FieldPreEntityTag, v))
}

// PreEntityTagEqualFold applies the EqualFold predicate on the "pre_entity_tag" field.
func PreEntityTagEqualFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEqualFold(FieldPreEntityTag, v))
}

// PreEntityTagContainsFold applies the ContainsFold predicate on the "pre_entity_tag" field.
func PreEntityTagContainsFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContainsFold(FieldPreEntityTag, v))
}

// EntityTagEQ applies the EQ predicate on the "entity_tag" field.
func EntityTagEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldEntityTag, v))
}

// EntityTagNEQ applies the NEQ predicate on the "entity_tag" field.
func EntityTagNEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldEntityTag, v))
}

// EntityTagIn applies the In predicate on the "entity_tag" field.
func EntityTagIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldEntityTag, vs...))
}

// EntityTagNotIn applies the NotIn predicate on the "entity_tag" field.
func EntityTagNotIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldEntityTag, vs...))
}

// EntityTagGT applies the GT predicate on the "entity_tag" field.
func EntityTagGT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldEntityTag, v))
}

// EntityTagGTE applies the GTE predicate on the "entity_tag" field.
func EntityTagGTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldEntityTag, v))
}

// EntityTagLT applies the LT predicate on the "entity_tag" field.
func EntityTagLT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldEntityTag, v))
}

// EntityTagLTE applies the LTE predicate on the "entity_tag" field.
func EntityTagLTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldEntityTag, v))
}

// EntityTagContains applies the Contains predicate on the "entity_tag" field.
func EntityTagContains(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContains(FieldEntityTag, v))
}

// EntityTagHasPrefix applies the HasPrefix predicate on the "entity_tag" field.
func EntityTagHasPrefix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasPrefix(FieldEntityTag, v))
}

// EntityTagHasSuffix applies the HasSuffix predicate on the "entity_tag" field.
func EntityTagHasSuffix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasSuffix(FieldEntityTag, v))
}

// EntityTagEqualFold applies the EqualFold predicate on the "entity_tag" field.
func EntityTagEqualFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEqualFold(FieldEntityTag, v))
}

// EntityTagContainsFold applies the ContainsFold predicate on the "entity_tag" field.
func EntityTagContainsFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContainsFold(FieldEntityTag, v))
}

// FilenameEQ applies the EQ predicate on the "filename" field.
func FilenameEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldFilename, v))
}

// FilenameNEQ applies the NEQ predicate on the "filename" field.
func FilenameNEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldFilename, v))
}

// FilenameIn applies the In predicate on the "filename" field.
func FilenameIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldFilename, vs...))
}

// FilenameNotIn applies the NotIn predicate on the "filename" field.
func FilenameNotIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldFilename, vs...))
}

// FilenameGT applies the GT predicate on the "filename" field.
func FilenameGT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldFilename, v))
}

// FilenameGTE applies the GTE predicate on the "filename" field.
func FilenameGTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldFilename, v))
}

// FilenameLT applies the LT predicate on the "filename" field.
func FilenameLT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldFilename, v))
}

// FilenameLTE applies the LTE predicate on the "filename" field.
func FilenameLTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldFilename, v))
}

// FilenameContains applies the Contains predicate on the "filename" field.
func FilenameContains(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContains(FieldFilename, v))
}

// FilenameHasPrefix applies the HasPrefix predicate on the "filename" field.
func FilenameHasPrefix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasPrefix(FieldFilename, v))
}

// FilenameHasSuffix applies the HasSuffix predicate on the "filename" field.
func FilenameHasSuffix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasSuffix(FieldFilename, v))
}

// FilenameEqualFold applies the EqualFold predicate on the "filename" field.
func FilenameEqualFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEqualFold(FieldFilename, v))
}

// FilenameContainsFold applies the ContainsFold predicate on the "filename" field.
func FilenameContainsFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContainsFold(FieldFilename, v))
}

// MimeTypeEQ applies the EQ predicate on the "mime_type" field.
func MimeTypeEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldMimeType, v))
}

// MimeTypeNEQ applies the NEQ predicate on the "mime_type" field.
func MimeTypeNEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldMimeType, v))
}

// MimeTypeIn applies the In predicate on the "mime_type" field.
func MimeTypeIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldMimeType, vs...))
}

// MimeTypeNotIn applies the NotIn predicate on the "mime_type" field.
func MimeTypeNotIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldMimeType, vs...))
}

// MimeTypeGT applies the GT predicate on the "mime_type" field.
func MimeTypeGT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldMimeType, v))
}

// MimeTypeGTE applies the GTE predicate on the "mime_type" field.
func MimeTypeGTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldMimeType, v))
}

// MimeTypeLT applies the LT predicate on the "mime_type" field.
func MimeTypeLT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldMimeType, v))
}

// MimeTypeLTE applies the LTE predicate on the "mime_type" field.
func MimeTypeLTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldMimeType, v))
}

// MimeTypeContains applies the Contains predicate on the "mime_type" field.
func MimeTypeContains(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContains(FieldMimeType, v))
}

// MimeTypeHasPrefix applies the HasPrefix predicate on the "mime_type" field.
func MimeTypeHasPrefix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasPrefix(FieldMimeType, v))
}

// MimeTypeHasSuffix applies the HasSuffix predicate on the "mime_type" field.
func MimeTypeHasSuffix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasSuffix(FieldMimeType, v))
}

// MimeTypeEqualFold applies the EqualFold predicate on the "mime_type" field.
func MimeTypeEqualFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEqualFold(FieldMimeType, v))
}

// MimeTypeContainsFold applies the ContainsFold predicate on the "mime_type" field.
func MimeTypeContainsFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContainsFold(FieldMimeType, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int64) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldUserID, v))
}

// UserNameEQ applies the EQ predicate on the "user_name" field.
func UserNameEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldUserName, v))
}

// UserNameNEQ applies the NEQ predicate on the "user_name" field.
func UserNameNEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldUserName, v))
}

// UserNameIn applies the In predicate on the "user_name" field.
func UserNameIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldUserName, vs...))
}

// UserNameNotIn applies the NotIn predicate on the "user_name" field.
func UserNameNotIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldUserName, vs...))
}

// UserNameGT applies the GT predicate on the "user_name" field.
func UserNameGT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldUserName, v))
}

// UserNameGTE applies the GTE predicate on the "user_name" field.
func UserNameGTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldUserName, v))
}

// UserNameLT applies the LT predicate on the "user_name" field.
func UserNameLT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldUserName, v))
}

// UserNameLTE applies the LTE predicate on the "user_name" field.
func UserNameLTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldUserName, v))
}

// UserNameContains applies the Contains predicate on the "user_name" field.
func UserNameContains(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContains(FieldUserName, v))
}

// UserNameHasPrefix applies the HasPrefix predicate on the "user_name" field.
func UserNameHasPrefix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasPrefix(FieldUserName, v))
}

// UserNameHasSuffix applies the HasSuffix predicate on the "user_name" field.
func UserNameHasSuffix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasSuffix(FieldUserName, v))
}

// UserNameEqualFold applies the EqualFold predicate on the "user_name" field.
func UserNameEqualFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEqualFold(FieldUserName, v))
}

// UserNameContainsFold applies the ContainsFold predicate on the "user_name" field.
func UserNameContainsFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContainsFold(FieldUserName, v))
}

// DeptIdsEQ applies the EQ predicate on the "dept_ids" field.
func DeptIdsEQ(v *pq.Int64Array) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldDeptIds, v))
}

// DeptIdsNEQ applies the NEQ predicate on the "dept_ids" field.
func DeptIdsNEQ(v *pq.Int64Array) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldDeptIds, v))
}

// DeptIdsIn applies the In predicate on the "dept_ids" field.
func DeptIdsIn(vs ...*pq.Int64Array) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldDeptIds, vs...))
}

// DeptIdsNotIn applies the NotIn predicate on the "dept_ids" field.
func DeptIdsNotIn(vs ...*pq.Int64Array) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldDeptIds, vs...))
}

// DeptIdsGT applies the GT predicate on the "dept_ids" field.
func DeptIdsGT(v *pq.Int64Array) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldDeptIds, v))
}

// DeptIdsGTE applies the GTE predicate on the "dept_ids" field.
func DeptIdsGTE(v *pq.Int64Array) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldDeptIds, v))
}

// DeptIdsLT applies the LT predicate on the "dept_ids" field.
func DeptIdsLT(v *pq.Int64Array) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldDeptIds, v))
}

// DeptIdsLTE applies the LTE predicate on the "dept_ids" field.
func DeptIdsLTE(v *pq.Int64Array) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldDeptIds, v))
}

// DeptNameEQ applies the EQ predicate on the "dept_name" field.
func DeptNameEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldDeptName, v))
}

// DeptNameNEQ applies the NEQ predicate on the "dept_name" field.
func DeptNameNEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldDeptName, v))
}

// DeptNameIn applies the In predicate on the "dept_name" field.
func DeptNameIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldDeptName, vs...))
}

// DeptNameNotIn applies the NotIn predicate on the "dept_name" field.
func DeptNameNotIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldDeptName, vs...))
}

// DeptNameGT applies the GT predicate on the "dept_name" field.
func DeptNameGT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldDeptName, v))
}

// DeptNameGTE applies the GTE predicate on the "dept_name" field.
func DeptNameGTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldDeptName, v))
}

// DeptNameLT applies the LT predicate on the "dept_name" field.
func DeptNameLT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldDeptName, v))
}

// DeptNameLTE applies the LTE predicate on the "dept_name" field.
func DeptNameLTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldDeptName, v))
}

// DeptNameContains applies the Contains predicate on the "dept_name" field.
func DeptNameContains(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContains(FieldDeptName, v))
}

// DeptNameHasPrefix applies the HasPrefix predicate on the "dept_name" field.
func DeptNameHasPrefix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasPrefix(FieldDeptName, v))
}

// DeptNameHasSuffix applies the HasSuffix predicate on the "dept_name" field.
func DeptNameHasSuffix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasSuffix(FieldDeptName, v))
}

// DeptNameEqualFold applies the EqualFold predicate on the "dept_name" field.
func DeptNameEqualFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEqualFold(FieldDeptName, v))
}

// DeptNameContainsFold applies the ContainsFold predicate on the "dept_name" field.
func DeptNameContainsFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContainsFold(FieldDeptName, v))
}

// PathEQ applies the EQ predicate on the "path" field.
func PathEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldPath, v))
}

// PathNEQ applies the NEQ predicate on the "path" field.
func PathNEQ(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldPath, v))
}

// PathIn applies the In predicate on the "path" field.
func PathIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldPath, vs...))
}

// PathNotIn applies the NotIn predicate on the "path" field.
func PathNotIn(vs ...string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldPath, vs...))
}

// PathGT applies the GT predicate on the "path" field.
func PathGT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldPath, v))
}

// PathGTE applies the GTE predicate on the "path" field.
func PathGTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldPath, v))
}

// PathLT applies the LT predicate on the "path" field.
func PathLT(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldPath, v))
}

// PathLTE applies the LTE predicate on the "path" field.
func PathLTE(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldPath, v))
}

// PathContains applies the Contains predicate on the "path" field.
func PathContains(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContains(FieldPath, v))
}

// PathHasPrefix applies the HasPrefix predicate on the "path" field.
func PathHasPrefix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasPrefix(FieldPath, v))
}

// PathHasSuffix applies the HasSuffix predicate on the "path" field.
func PathHasSuffix(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldHasSuffix(FieldPath, v))
}

// PathEqualFold applies the EqualFold predicate on the "path" field.
func PathEqualFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEqualFold(FieldPath, v))
}

// PathContainsFold applies the ContainsFold predicate on the "path" field.
func PathContainsFold(v string) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldContainsFold(FieldPath, v))
}

// SecurityLevelEQ applies the EQ predicate on the "security_level" field.
func SecurityLevelEQ(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldSecurityLevel, v))
}

// SecurityLevelNEQ applies the NEQ predicate on the "security_level" field.
func SecurityLevelNEQ(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldSecurityLevel, v))
}

// SecurityLevelIn applies the In predicate on the "security_level" field.
func SecurityLevelIn(vs ...int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldSecurityLevel, vs...))
}

// SecurityLevelNotIn applies the NotIn predicate on the "security_level" field.
func SecurityLevelNotIn(vs ...int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldSecurityLevel, vs...))
}

// SecurityLevelGT applies the GT predicate on the "security_level" field.
func SecurityLevelGT(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldSecurityLevel, v))
}

// SecurityLevelGTE applies the GTE predicate on the "security_level" field.
func SecurityLevelGTE(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldSecurityLevel, v))
}

// SecurityLevelLT applies the LT predicate on the "security_level" field.
func SecurityLevelLT(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldSecurityLevel, v))
}

// SecurityLevelLTE applies the LTE predicate on the "security_level" field.
func SecurityLevelLTE(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldSecurityLevel, v))
}

// NodeLevelEQ applies the EQ predicate on the "node_level" field.
func NodeLevelEQ(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldNodeLevel, v))
}

// NodeLevelNEQ applies the NEQ predicate on the "node_level" field.
func NodeLevelNEQ(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldNodeLevel, v))
}

// NodeLevelIn applies the In predicate on the "node_level" field.
func NodeLevelIn(vs ...int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldNodeLevel, vs...))
}

// NodeLevelNotIn applies the NotIn predicate on the "node_level" field.
func NodeLevelNotIn(vs ...int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldNodeLevel, vs...))
}

// NodeLevelGT applies the GT predicate on the "node_level" field.
func NodeLevelGT(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldNodeLevel, v))
}

// NodeLevelGTE applies the GTE predicate on the "node_level" field.
func NodeLevelGTE(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldNodeLevel, v))
}

// NodeLevelLT applies the LT predicate on the "node_level" field.
func NodeLevelLT(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldNodeLevel, v))
}

// NodeLevelLTE applies the LTE predicate on the "node_level" field.
func NodeLevelLTE(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldNodeLevel, v))
}

// CheckStatusEQ applies the EQ predicate on the "check_status" field.
func CheckStatusEQ(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldCheckStatus, v))
}

// CheckStatusNEQ applies the NEQ predicate on the "check_status" field.
func CheckStatusNEQ(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldCheckStatus, v))
}

// CheckStatusIn applies the In predicate on the "check_status" field.
func CheckStatusIn(vs ...int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldCheckStatus, vs...))
}

// CheckStatusNotIn applies the NotIn predicate on the "check_status" field.
func CheckStatusNotIn(vs ...int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldCheckStatus, vs...))
}

// CheckStatusGT applies the GT predicate on the "check_status" field.
func CheckStatusGT(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldCheckStatus, v))
}

// CheckStatusGTE applies the GTE predicate on the "check_status" field.
func CheckStatusGTE(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldCheckStatus, v))
}

// CheckStatusLT applies the LT predicate on the "check_status" field.
func CheckStatusLT(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldCheckStatus, v))
}

// CheckStatusLTE applies the LTE predicate on the "check_status" field.
func CheckStatusLTE(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldCheckStatus, v))
}

// TreeTypeEQ applies the EQ predicate on the "tree_type" field.
func TreeTypeEQ(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldEQ(FieldTreeType, v))
}

// TreeTypeNEQ applies the NEQ predicate on the "tree_type" field.
func TreeTypeNEQ(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNEQ(FieldTreeType, v))
}

// TreeTypeIn applies the In predicate on the "tree_type" field.
func TreeTypeIn(vs ...int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldIn(FieldTreeType, vs...))
}

// TreeTypeNotIn applies the NotIn predicate on the "tree_type" field.
func TreeTypeNotIn(vs ...int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldNotIn(FieldTreeType, vs...))
}

// TreeTypeGT applies the GT predicate on the "tree_type" field.
func TreeTypeGT(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGT(FieldTreeType, v))
}

// TreeTypeGTE applies the GTE predicate on the "tree_type" field.
func TreeTypeGTE(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldGTE(FieldTreeType, v))
}

// TreeTypeLT applies the LT predicate on the "tree_type" field.
func TreeTypeLT(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLT(FieldTreeType, v))
}

// TreeTypeLTE applies the LTE predicate on the "tree_type" field.
func TreeTypeLTE(v int32) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.FieldLTE(FieldTreeType, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.ClassificationFiles) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.ClassificationFiles) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.ClassificationFiles) predicate.ClassificationFiles {
	return predicate.ClassificationFiles(sql.NotPredicates(p))
}

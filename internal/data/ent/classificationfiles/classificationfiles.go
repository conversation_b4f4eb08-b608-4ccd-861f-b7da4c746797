// Code generated by ent, DO NOT EDIT.

package classificationfiles

import (
	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
)

const (
	// Label holds the string label denoting the classificationfiles type in the database.
	Label = "classification_files"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldFileRelationID holds the string denoting the file_relation_id field in the database.
	FieldFileRelationID = "file_relation_id"
	// FieldPreEntityTag holds the string denoting the pre_entity_tag field in the database.
	FieldPreEntityTag = "pre_entity_tag"
	// FieldEntityTag holds the string denoting the entity_tag field in the database.
	FieldEntityTag = "entity_tag"
	// FieldFilename holds the string denoting the filename field in the database.
	FieldFilename = "filename"
	// FieldMimeType holds the string denoting the mime_type field in the database.
	FieldMimeType = "mime_type"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldUserName holds the string denoting the user_name field in the database.
	FieldUserName = "user_name"
	// FieldDeptIds holds the string denoting the dept_ids field in the database.
	FieldDeptIds = "dept_ids"
	// FieldDeptName holds the string denoting the dept_name field in the database.
	FieldDeptName = "dept_name"
	// FieldPath holds the string denoting the path field in the database.
	FieldPath = "path"
	// FieldSecurityLevel holds the string denoting the security_level field in the database.
	FieldSecurityLevel = "security_level"
	// FieldNodeLevel holds the string denoting the node_level field in the database.
	FieldNodeLevel = "node_level"
	// FieldCheckStatus holds the string denoting the check_status field in the database.
	FieldCheckStatus = "check_status"
	// FieldTreeType holds the string denoting the tree_type field in the database.
	FieldTreeType = "tree_type"
	// Table holds the table name of the classificationfiles in the database.
	Table = "classification_files"
)

// Columns holds all SQL columns for classificationfiles fields.
var Columns = []string{
	FieldID,
	FieldName,
	FieldFileRelationID,
	FieldPreEntityTag,
	FieldEntityTag,
	FieldFilename,
	FieldMimeType,
	FieldUserID,
	FieldUserName,
	FieldDeptIds,
	FieldDeptName,
	FieldPath,
	FieldSecurityLevel,
	FieldNodeLevel,
	FieldCheckStatus,
	FieldTreeType,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultName holds the default value on creation for the "name" field.
	DefaultName string
	// DefaultFileRelationID holds the default value on creation for the "file_relation_id" field.
	DefaultFileRelationID int64
	// DefaultPreEntityTag holds the default value on creation for the "pre_entity_tag" field.
	DefaultPreEntityTag string
	// DefaultEntityTag holds the default value on creation for the "entity_tag" field.
	DefaultEntityTag string
	// DefaultFilename holds the default value on creation for the "filename" field.
	DefaultFilename string
	// DefaultMimeType holds the default value on creation for the "mime_type" field.
	DefaultMimeType string
	// DefaultUserID holds the default value on creation for the "user_id" field.
	DefaultUserID int64
	// DefaultUserName holds the default value on creation for the "user_name" field.
	DefaultUserName string
	// DefaultDeptIds holds the default value on creation for the "dept_ids" field.
	DefaultDeptIds *pq.Int64Array
	// DefaultDeptName holds the default value on creation for the "dept_name" field.
	DefaultDeptName string
	// DefaultPath holds the default value on creation for the "path" field.
	DefaultPath string
	// DefaultSecurityLevel holds the default value on creation for the "security_level" field.
	DefaultSecurityLevel int32
	// DefaultNodeLevel holds the default value on creation for the "node_level" field.
	DefaultNodeLevel int32
	// DefaultCheckStatus holds the default value on creation for the "check_status" field.
	DefaultCheckStatus int32
	// DefaultTreeType holds the default value on creation for the "tree_type" field.
	DefaultTreeType int32
)

// OrderOption defines the ordering options for the ClassificationFiles queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByFileRelationID orders the results by the file_relation_id field.
func ByFileRelationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFileRelationID, opts...).ToFunc()
}

// ByPreEntityTag orders the results by the pre_entity_tag field.
func ByPreEntityTag(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPreEntityTag, opts...).ToFunc()
}

// ByEntityTag orders the results by the entity_tag field.
func ByEntityTag(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEntityTag, opts...).ToFunc()
}

// ByFilename orders the results by the filename field.
func ByFilename(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFilename, opts...).ToFunc()
}

// ByMimeType orders the results by the mime_type field.
func ByMimeType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMimeType, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByUserName orders the results by the user_name field.
func ByUserName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserName, opts...).ToFunc()
}

// ByDeptIds orders the results by the dept_ids field.
func ByDeptIds(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeptIds, opts...).ToFunc()
}

// ByDeptName orders the results by the dept_name field.
func ByDeptName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeptName, opts...).ToFunc()
}

// ByPath orders the results by the path field.
func ByPath(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPath, opts...).ToFunc()
}

// BySecurityLevel orders the results by the security_level field.
func BySecurityLevel(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSecurityLevel, opts...).ToFunc()
}

// ByNodeLevel orders the results by the node_level field.
func ByNodeLevel(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNodeLevel, opts...).ToFunc()
}

// ByCheckStatus orders the results by the check_status field.
func ByCheckStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCheckStatus, opts...).ToFunc()
}

// ByTreeType orders the results by the tree_type field.
func ByTreeType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTreeType, opts...).ToFunc()
}

// Code generated by ent, DO NOT EDIT.

package knowledgebase

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldID, id))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldTenantID, v))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldDeletedAt, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldName, v))
}

// Public applies equality check predicate on the "public" field. It's identical to PublicEQ.
func Public(v bool) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldPublic, v))
}

// DataType applies equality check predicate on the "data_type" field. It's identical to DataTypeEQ.
func DataType(v int32) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldDataType, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldUserID, v))
}

// ManagerUserIds applies equality check predicate on the "manager_user_ids" field. It's identical to ManagerUserIdsEQ.
func ManagerUserIds(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldManagerUserIds, v))
}

// EditableUserIds applies equality check predicate on the "editable_user_ids" field. It's identical to EditableUserIdsEQ.
func EditableUserIds(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldEditableUserIds, v))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldTenantID, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotNull(FieldDeletedAt))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldContainsFold(FieldName, v))
}

// PublicEQ applies the EQ predicate on the "public" field.
func PublicEQ(v bool) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldPublic, v))
}

// PublicNEQ applies the NEQ predicate on the "public" field.
func PublicNEQ(v bool) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldPublic, v))
}

// DataTypeEQ applies the EQ predicate on the "data_type" field.
func DataTypeEQ(v int32) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldDataType, v))
}

// DataTypeNEQ applies the NEQ predicate on the "data_type" field.
func DataTypeNEQ(v int32) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldDataType, v))
}

// DataTypeIn applies the In predicate on the "data_type" field.
func DataTypeIn(vs ...int32) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldDataType, vs...))
}

// DataTypeNotIn applies the NotIn predicate on the "data_type" field.
func DataTypeNotIn(vs ...int32) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldDataType, vs...))
}

// DataTypeGT applies the GT predicate on the "data_type" field.
func DataTypeGT(v int32) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldDataType, v))
}

// DataTypeGTE applies the GTE predicate on the "data_type" field.
func DataTypeGTE(v int32) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldDataType, v))
}

// DataTypeLT applies the LT predicate on the "data_type" field.
func DataTypeLT(v int32) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldDataType, v))
}

// DataTypeLTE applies the LTE predicate on the "data_type" field.
func DataTypeLTE(v int32) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldDataType, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int64) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldUserID, v))
}

// ManagerUserIdsEQ applies the EQ predicate on the "manager_user_ids" field.
func ManagerUserIdsEQ(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldManagerUserIds, v))
}

// ManagerUserIdsNEQ applies the NEQ predicate on the "manager_user_ids" field.
func ManagerUserIdsNEQ(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldManagerUserIds, v))
}

// ManagerUserIdsIn applies the In predicate on the "manager_user_ids" field.
func ManagerUserIdsIn(vs ...*pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldManagerUserIds, vs...))
}

// ManagerUserIdsNotIn applies the NotIn predicate on the "manager_user_ids" field.
func ManagerUserIdsNotIn(vs ...*pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldManagerUserIds, vs...))
}

// ManagerUserIdsGT applies the GT predicate on the "manager_user_ids" field.
func ManagerUserIdsGT(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldManagerUserIds, v))
}

// ManagerUserIdsGTE applies the GTE predicate on the "manager_user_ids" field.
func ManagerUserIdsGTE(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldManagerUserIds, v))
}

// ManagerUserIdsLT applies the LT predicate on the "manager_user_ids" field.
func ManagerUserIdsLT(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldManagerUserIds, v))
}

// ManagerUserIdsLTE applies the LTE predicate on the "manager_user_ids" field.
func ManagerUserIdsLTE(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldManagerUserIds, v))
}

// EditableUserIdsEQ applies the EQ predicate on the "editable_user_ids" field.
func EditableUserIdsEQ(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldEQ(FieldEditableUserIds, v))
}

// EditableUserIdsNEQ applies the NEQ predicate on the "editable_user_ids" field.
func EditableUserIdsNEQ(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNEQ(FieldEditableUserIds, v))
}

// EditableUserIdsIn applies the In predicate on the "editable_user_ids" field.
func EditableUserIdsIn(vs ...*pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldIn(FieldEditableUserIds, vs...))
}

// EditableUserIdsNotIn applies the NotIn predicate on the "editable_user_ids" field.
func EditableUserIdsNotIn(vs ...*pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldNotIn(FieldEditableUserIds, vs...))
}

// EditableUserIdsGT applies the GT predicate on the "editable_user_ids" field.
func EditableUserIdsGT(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGT(FieldEditableUserIds, v))
}

// EditableUserIdsGTE applies the GTE predicate on the "editable_user_ids" field.
func EditableUserIdsGTE(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldGTE(FieldEditableUserIds, v))
}

// EditableUserIdsLT applies the LT predicate on the "editable_user_ids" field.
func EditableUserIdsLT(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLT(FieldEditableUserIds, v))
}

// EditableUserIdsLTE applies the LTE predicate on the "editable_user_ids" field.
func EditableUserIdsLTE(v *pq.Int64Array) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.FieldLTE(FieldEditableUserIds, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.KnowledgeBase) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.KnowledgeBase) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.KnowledgeBase) predicate.KnowledgeBase {
	return predicate.KnowledgeBase(sql.NotPredicates(p))
}

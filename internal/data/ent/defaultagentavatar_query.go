// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/defaultagentavatar"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// DefaultAgentAvatarQuery is the builder for querying DefaultAgentAvatar entities.
type DefaultAgentAvatarQuery struct {
	config
	ctx        *QueryContext
	order      []defaultagentavatar.OrderOption
	inters     []Interceptor
	predicates []predicate.DefaultAgentAvatar
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the DefaultAgentAvatarQuery builder.
func (daaq *DefaultAgentAvatarQuery) Where(ps ...predicate.DefaultAgentAvatar) *DefaultAgentAvatarQuery {
	daaq.predicates = append(daaq.predicates, ps...)
	return daaq
}

// Limit the number of records to be returned by this query.
func (daaq *DefaultAgentAvatarQuery) Limit(limit int) *DefaultAgentAvatarQuery {
	daaq.ctx.Limit = &limit
	return daaq
}

// Offset to start from.
func (daaq *DefaultAgentAvatarQuery) Offset(offset int) *DefaultAgentAvatarQuery {
	daaq.ctx.Offset = &offset
	return daaq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (daaq *DefaultAgentAvatarQuery) Unique(unique bool) *DefaultAgentAvatarQuery {
	daaq.ctx.Unique = &unique
	return daaq
}

// Order specifies how the records should be ordered.
func (daaq *DefaultAgentAvatarQuery) Order(o ...defaultagentavatar.OrderOption) *DefaultAgentAvatarQuery {
	daaq.order = append(daaq.order, o...)
	return daaq
}

// First returns the first DefaultAgentAvatar entity from the query.
// Returns a *NotFoundError when no DefaultAgentAvatar was found.
func (daaq *DefaultAgentAvatarQuery) First(ctx context.Context) (*DefaultAgentAvatar, error) {
	nodes, err := daaq.Limit(1).All(setContextOp(ctx, daaq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{defaultagentavatar.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (daaq *DefaultAgentAvatarQuery) FirstX(ctx context.Context) *DefaultAgentAvatar {
	node, err := daaq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first DefaultAgentAvatar ID from the query.
// Returns a *NotFoundError when no DefaultAgentAvatar ID was found.
func (daaq *DefaultAgentAvatarQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = daaq.Limit(1).IDs(setContextOp(ctx, daaq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{defaultagentavatar.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (daaq *DefaultAgentAvatarQuery) FirstIDX(ctx context.Context) int64 {
	id, err := daaq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single DefaultAgentAvatar entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one DefaultAgentAvatar entity is found.
// Returns a *NotFoundError when no DefaultAgentAvatar entities are found.
func (daaq *DefaultAgentAvatarQuery) Only(ctx context.Context) (*DefaultAgentAvatar, error) {
	nodes, err := daaq.Limit(2).All(setContextOp(ctx, daaq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{defaultagentavatar.Label}
	default:
		return nil, &NotSingularError{defaultagentavatar.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (daaq *DefaultAgentAvatarQuery) OnlyX(ctx context.Context) *DefaultAgentAvatar {
	node, err := daaq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only DefaultAgentAvatar ID in the query.
// Returns a *NotSingularError when more than one DefaultAgentAvatar ID is found.
// Returns a *NotFoundError when no entities are found.
func (daaq *DefaultAgentAvatarQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = daaq.Limit(2).IDs(setContextOp(ctx, daaq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{defaultagentavatar.Label}
	default:
		err = &NotSingularError{defaultagentavatar.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (daaq *DefaultAgentAvatarQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := daaq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of DefaultAgentAvatars.
func (daaq *DefaultAgentAvatarQuery) All(ctx context.Context) ([]*DefaultAgentAvatar, error) {
	ctx = setContextOp(ctx, daaq.ctx, "All")
	if err := daaq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*DefaultAgentAvatar, *DefaultAgentAvatarQuery]()
	return withInterceptors[[]*DefaultAgentAvatar](ctx, daaq, qr, daaq.inters)
}

// AllX is like All, but panics if an error occurs.
func (daaq *DefaultAgentAvatarQuery) AllX(ctx context.Context) []*DefaultAgentAvatar {
	nodes, err := daaq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of DefaultAgentAvatar IDs.
func (daaq *DefaultAgentAvatarQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if daaq.ctx.Unique == nil && daaq.path != nil {
		daaq.Unique(true)
	}
	ctx = setContextOp(ctx, daaq.ctx, "IDs")
	if err = daaq.Select(defaultagentavatar.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (daaq *DefaultAgentAvatarQuery) IDsX(ctx context.Context) []int64 {
	ids, err := daaq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (daaq *DefaultAgentAvatarQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, daaq.ctx, "Count")
	if err := daaq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, daaq, querierCount[*DefaultAgentAvatarQuery](), daaq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (daaq *DefaultAgentAvatarQuery) CountX(ctx context.Context) int {
	count, err := daaq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (daaq *DefaultAgentAvatarQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, daaq.ctx, "Exist")
	switch _, err := daaq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (daaq *DefaultAgentAvatarQuery) ExistX(ctx context.Context) bool {
	exist, err := daaq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the DefaultAgentAvatarQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (daaq *DefaultAgentAvatarQuery) Clone() *DefaultAgentAvatarQuery {
	if daaq == nil {
		return nil
	}
	return &DefaultAgentAvatarQuery{
		config:     daaq.config,
		ctx:        daaq.ctx.Clone(),
		order:      append([]defaultagentavatar.OrderOption{}, daaq.order...),
		inters:     append([]Interceptor{}, daaq.inters...),
		predicates: append([]predicate.DefaultAgentAvatar{}, daaq.predicates...),
		// clone intermediate query.
		sql:  daaq.sql.Clone(),
		path: daaq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.DefaultAgentAvatar.Query().
//		GroupBy(defaultagentavatar.FieldTenantID).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (daaq *DefaultAgentAvatarQuery) GroupBy(field string, fields ...string) *DefaultAgentAvatarGroupBy {
	daaq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &DefaultAgentAvatarGroupBy{build: daaq}
	grbuild.flds = &daaq.ctx.Fields
	grbuild.label = defaultagentavatar.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		TenantID int64 `json:"tenant_id,omitempty"`
//	}
//
//	client.DefaultAgentAvatar.Query().
//		Select(defaultagentavatar.FieldTenantID).
//		Scan(ctx, &v)
func (daaq *DefaultAgentAvatarQuery) Select(fields ...string) *DefaultAgentAvatarSelect {
	daaq.ctx.Fields = append(daaq.ctx.Fields, fields...)
	sbuild := &DefaultAgentAvatarSelect{DefaultAgentAvatarQuery: daaq}
	sbuild.label = defaultagentavatar.Label
	sbuild.flds, sbuild.scan = &daaq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a DefaultAgentAvatarSelect configured with the given aggregations.
func (daaq *DefaultAgentAvatarQuery) Aggregate(fns ...AggregateFunc) *DefaultAgentAvatarSelect {
	return daaq.Select().Aggregate(fns...)
}

func (daaq *DefaultAgentAvatarQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range daaq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, daaq); err != nil {
				return err
			}
		}
	}
	for _, f := range daaq.ctx.Fields {
		if !defaultagentavatar.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if daaq.path != nil {
		prev, err := daaq.path(ctx)
		if err != nil {
			return err
		}
		daaq.sql = prev
	}
	return nil
}

func (daaq *DefaultAgentAvatarQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*DefaultAgentAvatar, error) {
	var (
		nodes = []*DefaultAgentAvatar{}
		_spec = daaq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*DefaultAgentAvatar).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &DefaultAgentAvatar{config: daaq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(daaq.modifiers) > 0 {
		_spec.Modifiers = daaq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, daaq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (daaq *DefaultAgentAvatarQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := daaq.querySpec()
	if len(daaq.modifiers) > 0 {
		_spec.Modifiers = daaq.modifiers
	}
	_spec.Node.Columns = daaq.ctx.Fields
	if len(daaq.ctx.Fields) > 0 {
		_spec.Unique = daaq.ctx.Unique != nil && *daaq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, daaq.driver, _spec)
}

func (daaq *DefaultAgentAvatarQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(defaultagentavatar.Table, defaultagentavatar.Columns, sqlgraph.NewFieldSpec(defaultagentavatar.FieldID, field.TypeInt64))
	_spec.From = daaq.sql
	if unique := daaq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if daaq.path != nil {
		_spec.Unique = true
	}
	if fields := daaq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, defaultagentavatar.FieldID)
		for i := range fields {
			if fields[i] != defaultagentavatar.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := daaq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := daaq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := daaq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := daaq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (daaq *DefaultAgentAvatarQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(daaq.driver.Dialect())
	t1 := builder.Table(defaultagentavatar.Table)
	columns := daaq.ctx.Fields
	if len(columns) == 0 {
		columns = defaultagentavatar.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if daaq.sql != nil {
		selector = daaq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if daaq.ctx.Unique != nil && *daaq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range daaq.modifiers {
		m(selector)
	}
	for _, p := range daaq.predicates {
		p(selector)
	}
	for _, p := range daaq.order {
		p(selector)
	}
	if offset := daaq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := daaq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (daaq *DefaultAgentAvatarQuery) ForUpdate(opts ...sql.LockOption) *DefaultAgentAvatarQuery {
	if daaq.driver.Dialect() == dialect.Postgres {
		daaq.Unique(false)
	}
	daaq.modifiers = append(daaq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return daaq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (daaq *DefaultAgentAvatarQuery) ForShare(opts ...sql.LockOption) *DefaultAgentAvatarQuery {
	if daaq.driver.Dialect() == dialect.Postgres {
		daaq.Unique(false)
	}
	daaq.modifiers = append(daaq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return daaq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (daaq *DefaultAgentAvatarQuery) Modify(modifiers ...func(s *sql.Selector)) *DefaultAgentAvatarSelect {
	daaq.modifiers = append(daaq.modifiers, modifiers...)
	return daaq.Select()
}

// DefaultAgentAvatarGroupBy is the group-by builder for DefaultAgentAvatar entities.
type DefaultAgentAvatarGroupBy struct {
	selector
	build *DefaultAgentAvatarQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (daagb *DefaultAgentAvatarGroupBy) Aggregate(fns ...AggregateFunc) *DefaultAgentAvatarGroupBy {
	daagb.fns = append(daagb.fns, fns...)
	return daagb
}

// Scan applies the selector query and scans the result into the given value.
func (daagb *DefaultAgentAvatarGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, daagb.build.ctx, "GroupBy")
	if err := daagb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DefaultAgentAvatarQuery, *DefaultAgentAvatarGroupBy](ctx, daagb.build, daagb, daagb.build.inters, v)
}

func (daagb *DefaultAgentAvatarGroupBy) sqlScan(ctx context.Context, root *DefaultAgentAvatarQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(daagb.fns))
	for _, fn := range daagb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*daagb.flds)+len(daagb.fns))
		for _, f := range *daagb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*daagb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := daagb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// DefaultAgentAvatarSelect is the builder for selecting fields of DefaultAgentAvatar entities.
type DefaultAgentAvatarSelect struct {
	*DefaultAgentAvatarQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (daas *DefaultAgentAvatarSelect) Aggregate(fns ...AggregateFunc) *DefaultAgentAvatarSelect {
	daas.fns = append(daas.fns, fns...)
	return daas
}

// Scan applies the selector query and scans the result into the given value.
func (daas *DefaultAgentAvatarSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, daas.ctx, "Select")
	if err := daas.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*DefaultAgentAvatarQuery, *DefaultAgentAvatarSelect](ctx, daas.DefaultAgentAvatarQuery, daas, daas.inters, v)
}

func (daas *DefaultAgentAvatarSelect) sqlScan(ctx context.Context, root *DefaultAgentAvatarQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(daas.fns))
	for _, fn := range daas.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*daas.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := daas.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (daas *DefaultAgentAvatarSelect) Modify(modifiers ...func(s *sql.Selector)) *DefaultAgentAvatarSelect {
	daas.modifiers = append(daas.modifiers, modifiers...)
	return daas
}

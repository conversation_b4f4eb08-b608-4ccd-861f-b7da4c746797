// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritylog"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiAgentSecurityLogUpdate is the builder for updating AiAgentSecurityLog entities.
type AiAgentSecurityLogUpdate struct {
	config
	hooks     []Hook
	mutation  *AiAgentSecurityLogMutation
	modifiers []func(*sql.UpdateBuilder)
}

// Where appends a list predicates to the AiAgentSecurityLogUpdate builder.
func (aaslu *AiAgentSecurityLogUpdate) Where(ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogUpdate {
	aaslu.mutation.Where(ps...)
	return aaslu
}

// SetUpdatedAt sets the "updated_at" field.
func (aaslu *AiAgentSecurityLogUpdate) SetUpdatedAt(t time.Time) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetUpdatedAt(t)
	return aaslu
}

// SetDeletedAt sets the "deleted_at" field.
func (aaslu *AiAgentSecurityLogUpdate) SetDeletedAt(t time.Time) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetDeletedAt(t)
	return aaslu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableDeletedAt(t *time.Time) *AiAgentSecurityLogUpdate {
	if t != nil {
		aaslu.SetDeletedAt(*t)
	}
	return aaslu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (aaslu *AiAgentSecurityLogUpdate) ClearDeletedAt() *AiAgentSecurityLogUpdate {
	aaslu.mutation.ClearDeletedAt()
	return aaslu
}

// SetRiskLevel sets the "risk_level" field.
func (aaslu *AiAgentSecurityLogUpdate) SetRiskLevel(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.ResetRiskLevel()
	aaslu.mutation.SetRiskLevel(i)
	return aaslu
}

// SetNillableRiskLevel sets the "risk_level" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableRiskLevel(i *int64) *AiAgentSecurityLogUpdate {
	if i != nil {
		aaslu.SetRiskLevel(*i)
	}
	return aaslu
}

// AddRiskLevel adds i to the "risk_level" field.
func (aaslu *AiAgentSecurityLogUpdate) AddRiskLevel(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.AddRiskLevel(i)
	return aaslu
}

// SetUserID sets the "user_id" field.
func (aaslu *AiAgentSecurityLogUpdate) SetUserID(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.ResetUserID()
	aaslu.mutation.SetUserID(i)
	return aaslu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableUserID(i *int64) *AiAgentSecurityLogUpdate {
	if i != nil {
		aaslu.SetUserID(*i)
	}
	return aaslu
}

// AddUserID adds i to the "user_id" field.
func (aaslu *AiAgentSecurityLogUpdate) AddUserID(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.AddUserID(i)
	return aaslu
}

// SetUserName sets the "user_name" field.
func (aaslu *AiAgentSecurityLogUpdate) SetUserName(s string) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetUserName(s)
	return aaslu
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableUserName(s *string) *AiAgentSecurityLogUpdate {
	if s != nil {
		aaslu.SetUserName(*s)
	}
	return aaslu
}

// SetDeptID sets the "dept_id" field.
func (aaslu *AiAgentSecurityLogUpdate) SetDeptID(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.ResetDeptID()
	aaslu.mutation.SetDeptID(i)
	return aaslu
}

// SetNillableDeptID sets the "dept_id" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableDeptID(i *int64) *AiAgentSecurityLogUpdate {
	if i != nil {
		aaslu.SetDeptID(*i)
	}
	return aaslu
}

// AddDeptID adds i to the "dept_id" field.
func (aaslu *AiAgentSecurityLogUpdate) AddDeptID(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.AddDeptID(i)
	return aaslu
}

// SetDeptName sets the "dept_name" field.
func (aaslu *AiAgentSecurityLogUpdate) SetDeptName(s string) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetDeptName(s)
	return aaslu
}

// SetNillableDeptName sets the "dept_name" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableDeptName(s *string) *AiAgentSecurityLogUpdate {
	if s != nil {
		aaslu.SetDeptName(*s)
	}
	return aaslu
}

// SetPcName sets the "pc_name" field.
func (aaslu *AiAgentSecurityLogUpdate) SetPcName(s string) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetPcName(s)
	return aaslu
}

// SetNillablePcName sets the "pc_name" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillablePcName(s *string) *AiAgentSecurityLogUpdate {
	if s != nil {
		aaslu.SetPcName(*s)
	}
	return aaslu
}

// SetAgentID sets the "agent_id" field.
func (aaslu *AiAgentSecurityLogUpdate) SetAgentID(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.ResetAgentID()
	aaslu.mutation.SetAgentID(i)
	return aaslu
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableAgentID(i *int64) *AiAgentSecurityLogUpdate {
	if i != nil {
		aaslu.SetAgentID(*i)
	}
	return aaslu
}

// AddAgentID adds i to the "agent_id" field.
func (aaslu *AiAgentSecurityLogUpdate) AddAgentID(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.AddAgentID(i)
	return aaslu
}

// SetAgentName sets the "agent_name" field.
func (aaslu *AiAgentSecurityLogUpdate) SetAgentName(s string) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetAgentName(s)
	return aaslu
}

// SetNillableAgentName sets the "agent_name" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableAgentName(s *string) *AiAgentSecurityLogUpdate {
	if s != nil {
		aaslu.SetAgentName(*s)
	}
	return aaslu
}

// SetAgentDescription sets the "agent_description" field.
func (aaslu *AiAgentSecurityLogUpdate) SetAgentDescription(s string) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetAgentDescription(s)
	return aaslu
}

// SetNillableAgentDescription sets the "agent_description" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableAgentDescription(s *string) *AiAgentSecurityLogUpdate {
	if s != nil {
		aaslu.SetAgentDescription(*s)
	}
	return aaslu
}

// SetHitAction sets the "hit_action" field.
func (aaslu *AiAgentSecurityLogUpdate) SetHitAction(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.ResetHitAction()
	aaslu.mutation.SetHitAction(i)
	return aaslu
}

// SetNillableHitAction sets the "hit_action" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableHitAction(i *int64) *AiAgentSecurityLogUpdate {
	if i != nil {
		aaslu.SetHitAction(*i)
	}
	return aaslu
}

// AddHitAction adds i to the "hit_action" field.
func (aaslu *AiAgentSecurityLogUpdate) AddHitAction(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.AddHitAction(i)
	return aaslu
}

// SetQuestion sets the "question" field.
func (aaslu *AiAgentSecurityLogUpdate) SetQuestion(s string) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetQuestion(s)
	return aaslu
}

// SetNillableQuestion sets the "question" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableQuestion(s *string) *AiAgentSecurityLogUpdate {
	if s != nil {
		aaslu.SetQuestion(*s)
	}
	return aaslu
}

// SetActionCategory sets the "action_category" field.
func (aaslu *AiAgentSecurityLogUpdate) SetActionCategory(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.ResetActionCategory()
	aaslu.mutation.SetActionCategory(i)
	return aaslu
}

// SetNillableActionCategory sets the "action_category" field if the given value is not nil.
func (aaslu *AiAgentSecurityLogUpdate) SetNillableActionCategory(i *int64) *AiAgentSecurityLogUpdate {
	if i != nil {
		aaslu.SetActionCategory(*i)
	}
	return aaslu
}

// AddActionCategory adds i to the "action_category" field.
func (aaslu *AiAgentSecurityLogUpdate) AddActionCategory(i int64) *AiAgentSecurityLogUpdate {
	aaslu.mutation.AddActionCategory(i)
	return aaslu
}

// SetUploadedFiles sets the "uploaded_files" field.
func (aaslu *AiAgentSecurityLogUpdate) SetUploadedFiles(pa *pq.StringArray) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetUploadedFiles(pa)
	return aaslu
}

// SetHitPolicies sets the "hit_policies" field.
func (aaslu *AiAgentSecurityLogUpdate) SetHitPolicies(pa *pq.StringArray) *AiAgentSecurityLogUpdate {
	aaslu.mutation.SetHitPolicies(pa)
	return aaslu
}

// Mutation returns the AiAgentSecurityLogMutation object of the builder.
func (aaslu *AiAgentSecurityLogUpdate) Mutation() *AiAgentSecurityLogMutation {
	return aaslu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (aaslu *AiAgentSecurityLogUpdate) Save(ctx context.Context) (int, error) {
	if err := aaslu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, aaslu.sqlSave, aaslu.mutation, aaslu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aaslu *AiAgentSecurityLogUpdate) SaveX(ctx context.Context) int {
	affected, err := aaslu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (aaslu *AiAgentSecurityLogUpdate) Exec(ctx context.Context) error {
	_, err := aaslu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aaslu *AiAgentSecurityLogUpdate) ExecX(ctx context.Context) {
	if err := aaslu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aaslu *AiAgentSecurityLogUpdate) defaults() error {
	if _, ok := aaslu.mutation.UpdatedAt(); !ok {
		if aiagentsecuritylog.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagentsecuritylog.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aiagentsecuritylog.UpdateDefaultUpdatedAt()
		aaslu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aaslu *AiAgentSecurityLogUpdate) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiAgentSecurityLogUpdate {
	aaslu.modifiers = append(aaslu.modifiers, modifiers...)
	return aaslu
}

func (aaslu *AiAgentSecurityLogUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(aiagentsecuritylog.Table, aiagentsecuritylog.Columns, sqlgraph.NewFieldSpec(aiagentsecuritylog.FieldID, field.TypeInt64))
	if ps := aaslu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aaslu.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aaslu.mutation.DeletedAt(); ok {
		_spec.SetField(aiagentsecuritylog.FieldDeletedAt, field.TypeTime, value)
	}
	if aaslu.mutation.DeletedAtCleared() {
		_spec.ClearField(aiagentsecuritylog.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := aaslu.mutation.RiskLevel(); ok {
		_spec.SetField(aiagentsecuritylog.FieldRiskLevel, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.AddedRiskLevel(); ok {
		_spec.AddField(aiagentsecuritylog.FieldRiskLevel, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.UserID(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.AddedUserID(); ok {
		_spec.AddField(aiagentsecuritylog.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.UserName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUserName, field.TypeString, value)
	}
	if value, ok := aaslu.mutation.DeptID(); ok {
		_spec.SetField(aiagentsecuritylog.FieldDeptID, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.AddedDeptID(); ok {
		_spec.AddField(aiagentsecuritylog.FieldDeptID, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.DeptName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldDeptName, field.TypeString, value)
	}
	if value, ok := aaslu.mutation.PcName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldPcName, field.TypeString, value)
	}
	if value, ok := aaslu.mutation.AgentID(); ok {
		_spec.SetField(aiagentsecuritylog.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.AddedAgentID(); ok {
		_spec.AddField(aiagentsecuritylog.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.AgentName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldAgentName, field.TypeString, value)
	}
	if value, ok := aaslu.mutation.AgentDescription(); ok {
		_spec.SetField(aiagentsecuritylog.FieldAgentDescription, field.TypeString, value)
	}
	if value, ok := aaslu.mutation.HitAction(); ok {
		_spec.SetField(aiagentsecuritylog.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.AddedHitAction(); ok {
		_spec.AddField(aiagentsecuritylog.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.Question(); ok {
		_spec.SetField(aiagentsecuritylog.FieldQuestion, field.TypeString, value)
	}
	if value, ok := aaslu.mutation.ActionCategory(); ok {
		_spec.SetField(aiagentsecuritylog.FieldActionCategory, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.AddedActionCategory(); ok {
		_spec.AddField(aiagentsecuritylog.FieldActionCategory, field.TypeInt64, value)
	}
	if value, ok := aaslu.mutation.UploadedFiles(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUploadedFiles, field.TypeOther, value)
	}
	if value, ok := aaslu.mutation.HitPolicies(); ok {
		_spec.SetField(aiagentsecuritylog.FieldHitPolicies, field.TypeOther, value)
	}
	_spec.AddModifiers(aaslu.modifiers...)
	if n, err = sqlgraph.UpdateNodes(ctx, aaslu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aiagentsecuritylog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	aaslu.mutation.done = true
	return n, nil
}

// AiAgentSecurityLogUpdateOne is the builder for updating a single AiAgentSecurityLog entity.
type AiAgentSecurityLogUpdateOne struct {
	config
	fields    []string
	hooks     []Hook
	mutation  *AiAgentSecurityLogMutation
	modifiers []func(*sql.UpdateBuilder)
}

// SetUpdatedAt sets the "updated_at" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetUpdatedAt(t time.Time) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetUpdatedAt(t)
	return aasluo
}

// SetDeletedAt sets the "deleted_at" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetDeletedAt(t time.Time) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetDeletedAt(t)
	return aasluo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableDeletedAt(t *time.Time) *AiAgentSecurityLogUpdateOne {
	if t != nil {
		aasluo.SetDeletedAt(*t)
	}
	return aasluo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (aasluo *AiAgentSecurityLogUpdateOne) ClearDeletedAt() *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.ClearDeletedAt()
	return aasluo
}

// SetRiskLevel sets the "risk_level" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetRiskLevel(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.ResetRiskLevel()
	aasluo.mutation.SetRiskLevel(i)
	return aasluo
}

// SetNillableRiskLevel sets the "risk_level" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableRiskLevel(i *int64) *AiAgentSecurityLogUpdateOne {
	if i != nil {
		aasluo.SetRiskLevel(*i)
	}
	return aasluo
}

// AddRiskLevel adds i to the "risk_level" field.
func (aasluo *AiAgentSecurityLogUpdateOne) AddRiskLevel(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.AddRiskLevel(i)
	return aasluo
}

// SetUserID sets the "user_id" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetUserID(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.ResetUserID()
	aasluo.mutation.SetUserID(i)
	return aasluo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableUserID(i *int64) *AiAgentSecurityLogUpdateOne {
	if i != nil {
		aasluo.SetUserID(*i)
	}
	return aasluo
}

// AddUserID adds i to the "user_id" field.
func (aasluo *AiAgentSecurityLogUpdateOne) AddUserID(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.AddUserID(i)
	return aasluo
}

// SetUserName sets the "user_name" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetUserName(s string) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetUserName(s)
	return aasluo
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableUserName(s *string) *AiAgentSecurityLogUpdateOne {
	if s != nil {
		aasluo.SetUserName(*s)
	}
	return aasluo
}

// SetDeptID sets the "dept_id" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetDeptID(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.ResetDeptID()
	aasluo.mutation.SetDeptID(i)
	return aasluo
}

// SetNillableDeptID sets the "dept_id" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableDeptID(i *int64) *AiAgentSecurityLogUpdateOne {
	if i != nil {
		aasluo.SetDeptID(*i)
	}
	return aasluo
}

// AddDeptID adds i to the "dept_id" field.
func (aasluo *AiAgentSecurityLogUpdateOne) AddDeptID(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.AddDeptID(i)
	return aasluo
}

// SetDeptName sets the "dept_name" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetDeptName(s string) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetDeptName(s)
	return aasluo
}

// SetNillableDeptName sets the "dept_name" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableDeptName(s *string) *AiAgentSecurityLogUpdateOne {
	if s != nil {
		aasluo.SetDeptName(*s)
	}
	return aasluo
}

// SetPcName sets the "pc_name" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetPcName(s string) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetPcName(s)
	return aasluo
}

// SetNillablePcName sets the "pc_name" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillablePcName(s *string) *AiAgentSecurityLogUpdateOne {
	if s != nil {
		aasluo.SetPcName(*s)
	}
	return aasluo
}

// SetAgentID sets the "agent_id" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetAgentID(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.ResetAgentID()
	aasluo.mutation.SetAgentID(i)
	return aasluo
}

// SetNillableAgentID sets the "agent_id" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableAgentID(i *int64) *AiAgentSecurityLogUpdateOne {
	if i != nil {
		aasluo.SetAgentID(*i)
	}
	return aasluo
}

// AddAgentID adds i to the "agent_id" field.
func (aasluo *AiAgentSecurityLogUpdateOne) AddAgentID(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.AddAgentID(i)
	return aasluo
}

// SetAgentName sets the "agent_name" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetAgentName(s string) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetAgentName(s)
	return aasluo
}

// SetNillableAgentName sets the "agent_name" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableAgentName(s *string) *AiAgentSecurityLogUpdateOne {
	if s != nil {
		aasluo.SetAgentName(*s)
	}
	return aasluo
}

// SetAgentDescription sets the "agent_description" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetAgentDescription(s string) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetAgentDescription(s)
	return aasluo
}

// SetNillableAgentDescription sets the "agent_description" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableAgentDescription(s *string) *AiAgentSecurityLogUpdateOne {
	if s != nil {
		aasluo.SetAgentDescription(*s)
	}
	return aasluo
}

// SetHitAction sets the "hit_action" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetHitAction(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.ResetHitAction()
	aasluo.mutation.SetHitAction(i)
	return aasluo
}

// SetNillableHitAction sets the "hit_action" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableHitAction(i *int64) *AiAgentSecurityLogUpdateOne {
	if i != nil {
		aasluo.SetHitAction(*i)
	}
	return aasluo
}

// AddHitAction adds i to the "hit_action" field.
func (aasluo *AiAgentSecurityLogUpdateOne) AddHitAction(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.AddHitAction(i)
	return aasluo
}

// SetQuestion sets the "question" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetQuestion(s string) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetQuestion(s)
	return aasluo
}

// SetNillableQuestion sets the "question" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableQuestion(s *string) *AiAgentSecurityLogUpdateOne {
	if s != nil {
		aasluo.SetQuestion(*s)
	}
	return aasluo
}

// SetActionCategory sets the "action_category" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetActionCategory(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.ResetActionCategory()
	aasluo.mutation.SetActionCategory(i)
	return aasluo
}

// SetNillableActionCategory sets the "action_category" field if the given value is not nil.
func (aasluo *AiAgentSecurityLogUpdateOne) SetNillableActionCategory(i *int64) *AiAgentSecurityLogUpdateOne {
	if i != nil {
		aasluo.SetActionCategory(*i)
	}
	return aasluo
}

// AddActionCategory adds i to the "action_category" field.
func (aasluo *AiAgentSecurityLogUpdateOne) AddActionCategory(i int64) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.AddActionCategory(i)
	return aasluo
}

// SetUploadedFiles sets the "uploaded_files" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetUploadedFiles(pa *pq.StringArray) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetUploadedFiles(pa)
	return aasluo
}

// SetHitPolicies sets the "hit_policies" field.
func (aasluo *AiAgentSecurityLogUpdateOne) SetHitPolicies(pa *pq.StringArray) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.SetHitPolicies(pa)
	return aasluo
}

// Mutation returns the AiAgentSecurityLogMutation object of the builder.
func (aasluo *AiAgentSecurityLogUpdateOne) Mutation() *AiAgentSecurityLogMutation {
	return aasluo.mutation
}

// Where appends a list predicates to the AiAgentSecurityLogUpdate builder.
func (aasluo *AiAgentSecurityLogUpdateOne) Where(ps ...predicate.AiAgentSecurityLog) *AiAgentSecurityLogUpdateOne {
	aasluo.mutation.Where(ps...)
	return aasluo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (aasluo *AiAgentSecurityLogUpdateOne) Select(field string, fields ...string) *AiAgentSecurityLogUpdateOne {
	aasluo.fields = append([]string{field}, fields...)
	return aasluo
}

// Save executes the query and returns the updated AiAgentSecurityLog entity.
func (aasluo *AiAgentSecurityLogUpdateOne) Save(ctx context.Context) (*AiAgentSecurityLog, error) {
	if err := aasluo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, aasluo.sqlSave, aasluo.mutation, aasluo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aasluo *AiAgentSecurityLogUpdateOne) SaveX(ctx context.Context) *AiAgentSecurityLog {
	node, err := aasluo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (aasluo *AiAgentSecurityLogUpdateOne) Exec(ctx context.Context) error {
	_, err := aasluo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aasluo *AiAgentSecurityLogUpdateOne) ExecX(ctx context.Context) {
	if err := aasluo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aasluo *AiAgentSecurityLogUpdateOne) defaults() error {
	if _, ok := aasluo.mutation.UpdatedAt(); !ok {
		if aiagentsecuritylog.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized aiagentsecuritylog.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := aiagentsecuritylog.UpdateDefaultUpdatedAt()
		aasluo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// Modify adds a statement modifier for attaching custom logic to the UPDATE statement.
func (aasluo *AiAgentSecurityLogUpdateOne) Modify(modifiers ...func(u *sql.UpdateBuilder)) *AiAgentSecurityLogUpdateOne {
	aasluo.modifiers = append(aasluo.modifiers, modifiers...)
	return aasluo
}

func (aasluo *AiAgentSecurityLogUpdateOne) sqlSave(ctx context.Context) (_node *AiAgentSecurityLog, err error) {
	_spec := sqlgraph.NewUpdateSpec(aiagentsecuritylog.Table, aiagentsecuritylog.Columns, sqlgraph.NewFieldSpec(aiagentsecuritylog.FieldID, field.TypeInt64))
	id, ok := aasluo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AiAgentSecurityLog.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := aasluo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aiagentsecuritylog.FieldID)
		for _, f := range fields {
			if !aiagentsecuritylog.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != aiagentsecuritylog.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := aasluo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aasluo.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aasluo.mutation.DeletedAt(); ok {
		_spec.SetField(aiagentsecuritylog.FieldDeletedAt, field.TypeTime, value)
	}
	if aasluo.mutation.DeletedAtCleared() {
		_spec.ClearField(aiagentsecuritylog.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := aasluo.mutation.RiskLevel(); ok {
		_spec.SetField(aiagentsecuritylog.FieldRiskLevel, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.AddedRiskLevel(); ok {
		_spec.AddField(aiagentsecuritylog.FieldRiskLevel, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.UserID(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.AddedUserID(); ok {
		_spec.AddField(aiagentsecuritylog.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.UserName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUserName, field.TypeString, value)
	}
	if value, ok := aasluo.mutation.DeptID(); ok {
		_spec.SetField(aiagentsecuritylog.FieldDeptID, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.AddedDeptID(); ok {
		_spec.AddField(aiagentsecuritylog.FieldDeptID, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.DeptName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldDeptName, field.TypeString, value)
	}
	if value, ok := aasluo.mutation.PcName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldPcName, field.TypeString, value)
	}
	if value, ok := aasluo.mutation.AgentID(); ok {
		_spec.SetField(aiagentsecuritylog.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.AddedAgentID(); ok {
		_spec.AddField(aiagentsecuritylog.FieldAgentID, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.AgentName(); ok {
		_spec.SetField(aiagentsecuritylog.FieldAgentName, field.TypeString, value)
	}
	if value, ok := aasluo.mutation.AgentDescription(); ok {
		_spec.SetField(aiagentsecuritylog.FieldAgentDescription, field.TypeString, value)
	}
	if value, ok := aasluo.mutation.HitAction(); ok {
		_spec.SetField(aiagentsecuritylog.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.AddedHitAction(); ok {
		_spec.AddField(aiagentsecuritylog.FieldHitAction, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.Question(); ok {
		_spec.SetField(aiagentsecuritylog.FieldQuestion, field.TypeString, value)
	}
	if value, ok := aasluo.mutation.ActionCategory(); ok {
		_spec.SetField(aiagentsecuritylog.FieldActionCategory, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.AddedActionCategory(); ok {
		_spec.AddField(aiagentsecuritylog.FieldActionCategory, field.TypeInt64, value)
	}
	if value, ok := aasluo.mutation.UploadedFiles(); ok {
		_spec.SetField(aiagentsecuritylog.FieldUploadedFiles, field.TypeOther, value)
	}
	if value, ok := aasluo.mutation.HitPolicies(); ok {
		_spec.SetField(aiagentsecuritylog.FieldHitPolicies, field.TypeOther, value)
	}
	_spec.AddModifiers(aasluo.modifiers...)
	_node = &AiAgentSecurityLog{config: aasluo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, aasluo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aiagentsecuritylog.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	aasluo.mutation.done = true
	return _node, nil
}

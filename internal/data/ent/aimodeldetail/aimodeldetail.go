// Code generated by ent, DO NOT EDIT.

package aimodeldetail

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Label holds the string label denoting the aimodeldetail type in the database.
	Label = "ai_model_detail"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldModelName holds the string denoting the model_name field in the database.
	FieldModelName = "model_name"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldURL holds the string denoting the url field in the database.
	FieldURL = "url"
	// FieldAvatar holds the string denoting the avatar field in the database.
	FieldAvatar = "avatar"
	// FieldCanInternetSearch holds the string denoting the can_internet_search field in the database.
	FieldCanInternetSearch = "can_internet_search"
	// FieldBalanceSearchURL holds the string denoting the balance_search_url field in the database.
	FieldBalanceSearchURL = "balance_search_url"
	// FieldThinkingEnableStatus holds the string denoting the thinking_enable_status field in the database.
	FieldThinkingEnableStatus = "thinking_enable_status"
	// FieldBackgroundURL holds the string denoting the background_url field in the database.
	FieldBackgroundURL = "background_url"
	// Table holds the table name of the aimodeldetail in the database.
	Table = "ai_model_detail"
)

// Columns holds all SQL columns for aimodeldetail fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldDeletedAt,
	FieldModelName,
	FieldName,
	FieldURL,
	FieldAvatar,
	FieldCanInternetSearch,
	FieldBalanceSearchURL,
	FieldThinkingEnableStatus,
	FieldBackgroundURL,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultDeletedAt holds the default value on creation for the "deleted_at" field.
	DefaultDeletedAt time.Time
	// DefaultThinkingEnableStatus holds the default value on creation for the "thinking_enable_status" field.
	DefaultThinkingEnableStatus int64
)

// OrderOption defines the ordering options for the AiModelDetail queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByModelName orders the results by the model_name field.
func ByModelName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldModelName, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByURL orders the results by the url field.
func ByURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldURL, opts...).ToFunc()
}

// ByAvatar orders the results by the avatar field.
func ByAvatar(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAvatar, opts...).ToFunc()
}

// ByCanInternetSearch orders the results by the can_internet_search field.
func ByCanInternetSearch(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCanInternetSearch, opts...).ToFunc()
}

// ByBalanceSearchURL orders the results by the balance_search_url field.
func ByBalanceSearchURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBalanceSearchURL, opts...).ToFunc()
}

// ByThinkingEnableStatus orders the results by the thinking_enable_status field.
func ByThinkingEnableStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldThinkingEnableStatus, opts...).ToFunc()
}

// ByBackgroundURL orders the results by the background_url field.
func ByBackgroundURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBackgroundURL, opts...).ToFunc()
}

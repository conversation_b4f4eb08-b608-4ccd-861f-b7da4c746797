// Code generated by ent, DO NOT EDIT.

package aimodeldetail

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ID filters vertices based on their ID field.
func ID(id int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldDeletedAt, v))
}

// ModelName applies equality check predicate on the "model_name" field. It's identical to ModelNameEQ.
func ModelName(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldModelName, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldName, v))
}

// URL applies equality check predicate on the "url" field. It's identical to URLEQ.
func URL(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldURL, v))
}

// Avatar applies equality check predicate on the "avatar" field. It's identical to AvatarEQ.
func Avatar(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldAvatar, v))
}

// CanInternetSearch applies equality check predicate on the "can_internet_search" field. It's identical to CanInternetSearchEQ.
func CanInternetSearch(v bool) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldCanInternetSearch, v))
}

// BalanceSearchURL applies equality check predicate on the "balance_search_url" field. It's identical to BalanceSearchURLEQ.
func BalanceSearchURL(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldBalanceSearchURL, v))
}

// ThinkingEnableStatus applies equality check predicate on the "thinking_enable_status" field. It's identical to ThinkingEnableStatusEQ.
func ThinkingEnableStatus(v int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldThinkingEnableStatus, v))
}

// BackgroundURL applies equality check predicate on the "background_url" field. It's identical to BackgroundURLEQ.
func BackgroundURL(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldBackgroundURL, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldUpdatedAt, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotNull(FieldDeletedAt))
}

// ModelNameEQ applies the EQ predicate on the "model_name" field.
func ModelNameEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldModelName, v))
}

// ModelNameNEQ applies the NEQ predicate on the "model_name" field.
func ModelNameNEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldModelName, v))
}

// ModelNameIn applies the In predicate on the "model_name" field.
func ModelNameIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldModelName, vs...))
}

// ModelNameNotIn applies the NotIn predicate on the "model_name" field.
func ModelNameNotIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldModelName, vs...))
}

// ModelNameGT applies the GT predicate on the "model_name" field.
func ModelNameGT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldModelName, v))
}

// ModelNameGTE applies the GTE predicate on the "model_name" field.
func ModelNameGTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldModelName, v))
}

// ModelNameLT applies the LT predicate on the "model_name" field.
func ModelNameLT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldModelName, v))
}

// ModelNameLTE applies the LTE predicate on the "model_name" field.
func ModelNameLTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldModelName, v))
}

// ModelNameContains applies the Contains predicate on the "model_name" field.
func ModelNameContains(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContains(FieldModelName, v))
}

// ModelNameHasPrefix applies the HasPrefix predicate on the "model_name" field.
func ModelNameHasPrefix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasPrefix(FieldModelName, v))
}

// ModelNameHasSuffix applies the HasSuffix predicate on the "model_name" field.
func ModelNameHasSuffix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasSuffix(FieldModelName, v))
}

// ModelNameEqualFold applies the EqualFold predicate on the "model_name" field.
func ModelNameEqualFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEqualFold(FieldModelName, v))
}

// ModelNameContainsFold applies the ContainsFold predicate on the "model_name" field.
func ModelNameContainsFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContainsFold(FieldModelName, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContainsFold(FieldName, v))
}

// URLEQ applies the EQ predicate on the "url" field.
func URLEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldURL, v))
}

// URLNEQ applies the NEQ predicate on the "url" field.
func URLNEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldURL, v))
}

// URLIn applies the In predicate on the "url" field.
func URLIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldURL, vs...))
}

// URLNotIn applies the NotIn predicate on the "url" field.
func URLNotIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldURL, vs...))
}

// URLGT applies the GT predicate on the "url" field.
func URLGT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldURL, v))
}

// URLGTE applies the GTE predicate on the "url" field.
func URLGTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldURL, v))
}

// URLLT applies the LT predicate on the "url" field.
func URLLT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldURL, v))
}

// URLLTE applies the LTE predicate on the "url" field.
func URLLTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldURL, v))
}

// URLContains applies the Contains predicate on the "url" field.
func URLContains(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContains(FieldURL, v))
}

// URLHasPrefix applies the HasPrefix predicate on the "url" field.
func URLHasPrefix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasPrefix(FieldURL, v))
}

// URLHasSuffix applies the HasSuffix predicate on the "url" field.
func URLHasSuffix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasSuffix(FieldURL, v))
}

// URLEqualFold applies the EqualFold predicate on the "url" field.
func URLEqualFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEqualFold(FieldURL, v))
}

// URLContainsFold applies the ContainsFold predicate on the "url" field.
func URLContainsFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContainsFold(FieldURL, v))
}

// AvatarEQ applies the EQ predicate on the "avatar" field.
func AvatarEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldAvatar, v))
}

// AvatarNEQ applies the NEQ predicate on the "avatar" field.
func AvatarNEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldAvatar, v))
}

// AvatarIn applies the In predicate on the "avatar" field.
func AvatarIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldAvatar, vs...))
}

// AvatarNotIn applies the NotIn predicate on the "avatar" field.
func AvatarNotIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldAvatar, vs...))
}

// AvatarGT applies the GT predicate on the "avatar" field.
func AvatarGT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldAvatar, v))
}

// AvatarGTE applies the GTE predicate on the "avatar" field.
func AvatarGTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldAvatar, v))
}

// AvatarLT applies the LT predicate on the "avatar" field.
func AvatarLT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldAvatar, v))
}

// AvatarLTE applies the LTE predicate on the "avatar" field.
func AvatarLTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldAvatar, v))
}

// AvatarContains applies the Contains predicate on the "avatar" field.
func AvatarContains(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContains(FieldAvatar, v))
}

// AvatarHasPrefix applies the HasPrefix predicate on the "avatar" field.
func AvatarHasPrefix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasPrefix(FieldAvatar, v))
}

// AvatarHasSuffix applies the HasSuffix predicate on the "avatar" field.
func AvatarHasSuffix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasSuffix(FieldAvatar, v))
}

// AvatarEqualFold applies the EqualFold predicate on the "avatar" field.
func AvatarEqualFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEqualFold(FieldAvatar, v))
}

// AvatarContainsFold applies the ContainsFold predicate on the "avatar" field.
func AvatarContainsFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContainsFold(FieldAvatar, v))
}

// CanInternetSearchEQ applies the EQ predicate on the "can_internet_search" field.
func CanInternetSearchEQ(v bool) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldCanInternetSearch, v))
}

// CanInternetSearchNEQ applies the NEQ predicate on the "can_internet_search" field.
func CanInternetSearchNEQ(v bool) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldCanInternetSearch, v))
}

// BalanceSearchURLEQ applies the EQ predicate on the "balance_search_url" field.
func BalanceSearchURLEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldBalanceSearchURL, v))
}

// BalanceSearchURLNEQ applies the NEQ predicate on the "balance_search_url" field.
func BalanceSearchURLNEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldBalanceSearchURL, v))
}

// BalanceSearchURLIn applies the In predicate on the "balance_search_url" field.
func BalanceSearchURLIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldBalanceSearchURL, vs...))
}

// BalanceSearchURLNotIn applies the NotIn predicate on the "balance_search_url" field.
func BalanceSearchURLNotIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldBalanceSearchURL, vs...))
}

// BalanceSearchURLGT applies the GT predicate on the "balance_search_url" field.
func BalanceSearchURLGT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldBalanceSearchURL, v))
}

// BalanceSearchURLGTE applies the GTE predicate on the "balance_search_url" field.
func BalanceSearchURLGTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldBalanceSearchURL, v))
}

// BalanceSearchURLLT applies the LT predicate on the "balance_search_url" field.
func BalanceSearchURLLT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldBalanceSearchURL, v))
}

// BalanceSearchURLLTE applies the LTE predicate on the "balance_search_url" field.
func BalanceSearchURLLTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldBalanceSearchURL, v))
}

// BalanceSearchURLContains applies the Contains predicate on the "balance_search_url" field.
func BalanceSearchURLContains(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContains(FieldBalanceSearchURL, v))
}

// BalanceSearchURLHasPrefix applies the HasPrefix predicate on the "balance_search_url" field.
func BalanceSearchURLHasPrefix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasPrefix(FieldBalanceSearchURL, v))
}

// BalanceSearchURLHasSuffix applies the HasSuffix predicate on the "balance_search_url" field.
func BalanceSearchURLHasSuffix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasSuffix(FieldBalanceSearchURL, v))
}

// BalanceSearchURLEqualFold applies the EqualFold predicate on the "balance_search_url" field.
func BalanceSearchURLEqualFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEqualFold(FieldBalanceSearchURL, v))
}

// BalanceSearchURLContainsFold applies the ContainsFold predicate on the "balance_search_url" field.
func BalanceSearchURLContainsFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContainsFold(FieldBalanceSearchURL, v))
}

// ThinkingEnableStatusEQ applies the EQ predicate on the "thinking_enable_status" field.
func ThinkingEnableStatusEQ(v int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldThinkingEnableStatus, v))
}

// ThinkingEnableStatusNEQ applies the NEQ predicate on the "thinking_enable_status" field.
func ThinkingEnableStatusNEQ(v int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldThinkingEnableStatus, v))
}

// ThinkingEnableStatusIn applies the In predicate on the "thinking_enable_status" field.
func ThinkingEnableStatusIn(vs ...int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldThinkingEnableStatus, vs...))
}

// ThinkingEnableStatusNotIn applies the NotIn predicate on the "thinking_enable_status" field.
func ThinkingEnableStatusNotIn(vs ...int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldThinkingEnableStatus, vs...))
}

// ThinkingEnableStatusGT applies the GT predicate on the "thinking_enable_status" field.
func ThinkingEnableStatusGT(v int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldThinkingEnableStatus, v))
}

// ThinkingEnableStatusGTE applies the GTE predicate on the "thinking_enable_status" field.
func ThinkingEnableStatusGTE(v int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldThinkingEnableStatus, v))
}

// ThinkingEnableStatusLT applies the LT predicate on the "thinking_enable_status" field.
func ThinkingEnableStatusLT(v int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldThinkingEnableStatus, v))
}

// ThinkingEnableStatusLTE applies the LTE predicate on the "thinking_enable_status" field.
func ThinkingEnableStatusLTE(v int64) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldThinkingEnableStatus, v))
}

// BackgroundURLEQ applies the EQ predicate on the "background_url" field.
func BackgroundURLEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEQ(FieldBackgroundURL, v))
}

// BackgroundURLNEQ applies the NEQ predicate on the "background_url" field.
func BackgroundURLNEQ(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNEQ(FieldBackgroundURL, v))
}

// BackgroundURLIn applies the In predicate on the "background_url" field.
func BackgroundURLIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldIn(FieldBackgroundURL, vs...))
}

// BackgroundURLNotIn applies the NotIn predicate on the "background_url" field.
func BackgroundURLNotIn(vs ...string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldNotIn(FieldBackgroundURL, vs...))
}

// BackgroundURLGT applies the GT predicate on the "background_url" field.
func BackgroundURLGT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGT(FieldBackgroundURL, v))
}

// BackgroundURLGTE applies the GTE predicate on the "background_url" field.
func BackgroundURLGTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldGTE(FieldBackgroundURL, v))
}

// BackgroundURLLT applies the LT predicate on the "background_url" field.
func BackgroundURLLT(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLT(FieldBackgroundURL, v))
}

// BackgroundURLLTE applies the LTE predicate on the "background_url" field.
func BackgroundURLLTE(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldLTE(FieldBackgroundURL, v))
}

// BackgroundURLContains applies the Contains predicate on the "background_url" field.
func BackgroundURLContains(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContains(FieldBackgroundURL, v))
}

// BackgroundURLHasPrefix applies the HasPrefix predicate on the "background_url" field.
func BackgroundURLHasPrefix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasPrefix(FieldBackgroundURL, v))
}

// BackgroundURLHasSuffix applies the HasSuffix predicate on the "background_url" field.
func BackgroundURLHasSuffix(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldHasSuffix(FieldBackgroundURL, v))
}

// BackgroundURLEqualFold applies the EqualFold predicate on the "background_url" field.
func BackgroundURLEqualFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldEqualFold(FieldBackgroundURL, v))
}

// BackgroundURLContainsFold applies the ContainsFold predicate on the "background_url" field.
func BackgroundURLContainsFold(v string) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.FieldContainsFold(FieldBackgroundURL, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AiModelDetail) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AiModelDetail) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AiModelDetail) predicate.AiModelDetail {
	return predicate.AiModelDetail(sql.NotPredicates(p))
}

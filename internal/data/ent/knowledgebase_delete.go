// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// KnowledgeBaseDelete is the builder for deleting a KnowledgeBase entity.
type KnowledgeBaseDelete struct {
	config
	hooks    []Hook
	mutation *KnowledgeBaseMutation
}

// Where appends a list predicates to the KnowledgeBaseDelete builder.
func (kbd *KnowledgeBaseDelete) Where(ps ...predicate.KnowledgeBase) *KnowledgeBaseDelete {
	kbd.mutation.Where(ps...)
	return kbd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (kbd *KnowledgeBaseDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, kbd.sqlExec, kbd.mutation, kbd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (kbd *KnowledgeBaseDelete) ExecX(ctx context.Context) int {
	n, err := kbd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (kbd *KnowledgeBaseDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(knowledgebase.Table, sqlgraph.NewFieldSpec(knowledgebase.FieldID, field.TypeInt64))
	if ps := kbd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, kbd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	kbd.mutation.done = true
	return affected, err
}

// KnowledgeBaseDeleteOne is the builder for deleting a single KnowledgeBase entity.
type KnowledgeBaseDeleteOne struct {
	kbd *KnowledgeBaseDelete
}

// Where appends a list predicates to the KnowledgeBaseDelete builder.
func (kbdo *KnowledgeBaseDeleteOne) Where(ps ...predicate.KnowledgeBase) *KnowledgeBaseDeleteOne {
	kbdo.kbd.mutation.Where(ps...)
	return kbdo
}

// Exec executes the deletion query.
func (kbdo *KnowledgeBaseDeleteOne) Exec(ctx context.Context) error {
	n, err := kbdo.kbd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{knowledgebase.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (kbdo *KnowledgeBaseDeleteOne) ExecX(ctx context.Context) {
	if err := kbdo.Exec(ctx); err != nil {
		panic(err)
	}
}

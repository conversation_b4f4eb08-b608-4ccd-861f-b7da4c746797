// Code generated by ent, DO NOT EDIT.

package runtime

import (
	"time"

	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritylog"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritypolicy"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichatitem"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodeldetail"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/atomicquestions"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/classificationfiles"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/defaultagentavatar"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/externalmodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebasefile"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/useragentorder"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	aiagentMixin := schema.AiAgent{}.Mixin()
	aiagentMixinHooks1 := aiagentMixin[1].Hooks()
	aiagent.Hooks[0] = aiagentMixinHooks1[0]
	aiagentMixinInters1 := aiagentMixin[1].Interceptors()
	aiagent.Interceptors[0] = aiagentMixinInters1[0]
	aiagentMixinFields0 := aiagentMixin[0].Fields()
	_ = aiagentMixinFields0
	aiagentMixinFields1 := aiagentMixin[1].Fields()
	_ = aiagentMixinFields1
	aiagentFields := schema.AiAgent{}.Fields()
	_ = aiagentFields
	// aiagentDescCreatedAt is the schema descriptor for created_at field.
	aiagentDescCreatedAt := aiagentMixinFields0[0].Descriptor()
	// aiagent.DefaultCreatedAt holds the default value on creation for the created_at field.
	aiagent.DefaultCreatedAt = aiagentDescCreatedAt.Default.(func() time.Time)
	// aiagentDescUpdatedAt is the schema descriptor for updated_at field.
	aiagentDescUpdatedAt := aiagentMixinFields0[1].Descriptor()
	// aiagent.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	aiagent.DefaultUpdatedAt = aiagentDescUpdatedAt.Default.(func() time.Time)
	// aiagent.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	aiagent.UpdateDefaultUpdatedAt = aiagentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// aiagentDescDeletedAt is the schema descriptor for deleted_at field.
	aiagentDescDeletedAt := aiagentMixinFields1[0].Descriptor()
	// aiagent.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	aiagent.DefaultDeletedAt = aiagentDescDeletedAt.Default.(time.Time)
	// aiagentDescManageableToUser is the schema descriptor for manageable_to_user field.
	aiagentDescManageableToUser := aiagentFields[9].Descriptor()
	// aiagent.DefaultManageableToUser holds the default value on creation for the manageable_to_user field.
	aiagent.DefaultManageableToUser = aiagentDescManageableToUser.Default.(*pq.Int64Array)
	// aiagentDescVisibleToUser is the schema descriptor for visible_to_user field.
	aiagentDescVisibleToUser := aiagentFields[10].Descriptor()
	// aiagent.DefaultVisibleToUser holds the default value on creation for the visible_to_user field.
	aiagent.DefaultVisibleToUser = aiagentDescVisibleToUser.Default.(*pq.Int64Array)
	// aiagentDescVisibleToDept is the schema descriptor for visible_to_dept field.
	aiagentDescVisibleToDept := aiagentFields[11].Descriptor()
	// aiagent.DefaultVisibleToDept holds the default value on creation for the visible_to_dept field.
	aiagent.DefaultVisibleToDept = aiagentDescVisibleToDept.Default.(*pq.Int64Array)
	// aiagentDescKnowledgeBaseIds is the schema descriptor for knowledge_base_ids field.
	aiagentDescKnowledgeBaseIds := aiagentFields[12].Descriptor()
	// aiagent.DefaultKnowledgeBaseIds holds the default value on creation for the knowledge_base_ids field.
	aiagent.DefaultKnowledgeBaseIds = aiagentDescKnowledgeBaseIds.Default.(*pq.Int64Array)
	// aiagentDescModelType is the schema descriptor for model_type field.
	aiagentDescModelType := aiagentFields[17].Descriptor()
	// aiagent.DefaultModelType holds the default value on creation for the model_type field.
	aiagent.DefaultModelType = aiagentDescModelType.Default.(int64)
	// aiagentDescModelID is the schema descriptor for model_id field.
	aiagentDescModelID := aiagentFields[18].Descriptor()
	// aiagent.DefaultModelID holds the default value on creation for the model_id field.
	aiagent.DefaultModelID = aiagentDescModelID.Default.(int64)
	// aiagentDescUseCount is the schema descriptor for use_count field.
	aiagentDescUseCount := aiagentFields[19].Descriptor()
	// aiagent.DefaultUseCount holds the default value on creation for the use_count field.
	aiagent.DefaultUseCount = aiagentDescUseCount.Default.(int64)
	// aiagentDescKnowledgeBaseType is the schema descriptor for knowledge_base_type field.
	aiagentDescKnowledgeBaseType := aiagentFields[20].Descriptor()
	// aiagent.DefaultKnowledgeBaseType holds the default value on creation for the knowledge_base_type field.
	aiagent.DefaultKnowledgeBaseType = aiagentDescKnowledgeBaseType.Default.(int64)
	// aiagentDescInternetSearch is the schema descriptor for internet_search field.
	aiagentDescInternetSearch := aiagentFields[21].Descriptor()
	// aiagent.DefaultInternetSearch holds the default value on creation for the internet_search field.
	aiagent.DefaultInternetSearch = aiagentDescInternetSearch.Default.(bool)
	// aiagentDescAgentType is the schema descriptor for agent_type field.
	aiagentDescAgentType := aiagentFields[22].Descriptor()
	// aiagent.DefaultAgentType holds the default value on creation for the agent_type field.
	aiagent.DefaultAgentType = aiagentDescAgentType.Default.(int64)
	// aiagentDescThinking is the schema descriptor for thinking field.
	aiagentDescThinking := aiagentFields[23].Descriptor()
	// aiagent.DefaultThinking holds the default value on creation for the thinking field.
	aiagent.DefaultThinking = aiagentDescThinking.Default.(bool)
	// aiagentDescThinkingModelID is the schema descriptor for thinking_model_id field.
	aiagentDescThinkingModelID := aiagentFields[24].Descriptor()
	// aiagent.DefaultThinkingModelID holds the default value on creation for the thinking_model_id field.
	aiagent.DefaultThinkingModelID = aiagentDescThinkingModelID.Default.(int64)
	// aiagentDescUploadFile is the schema descriptor for upload_file field.
	aiagentDescUploadFile := aiagentFields[26].Descriptor()
	// aiagent.DefaultUploadFile holds the default value on creation for the upload_file field.
	aiagent.DefaultUploadFile = aiagentDescUploadFile.Default.(bool)
	// aiagentDescSemanticCache is the schema descriptor for semantic_cache field.
	aiagentDescSemanticCache := aiagentFields[27].Descriptor()
	// aiagent.DefaultSemanticCache holds the default value on creation for the semantic_cache field.
	aiagent.DefaultSemanticCache = aiagentDescSemanticCache.Default.(bool)
	aiagentsecuritylogMixin := schema.AiAgentSecurityLog{}.Mixin()
	aiagentsecuritylogMixinHooks1 := aiagentsecuritylogMixin[1].Hooks()
	aiagentsecuritylog.Hooks[0] = aiagentsecuritylogMixinHooks1[0]
	aiagentsecuritylogMixinInters1 := aiagentsecuritylogMixin[1].Interceptors()
	aiagentsecuritylog.Interceptors[0] = aiagentsecuritylogMixinInters1[0]
	aiagentsecuritylogMixinFields0 := aiagentsecuritylogMixin[0].Fields()
	_ = aiagentsecuritylogMixinFields0
	aiagentsecuritylogMixinFields1 := aiagentsecuritylogMixin[1].Fields()
	_ = aiagentsecuritylogMixinFields1
	aiagentsecuritylogFields := schema.AiAgentSecurityLog{}.Fields()
	_ = aiagentsecuritylogFields
	// aiagentsecuritylogDescCreatedAt is the schema descriptor for created_at field.
	aiagentsecuritylogDescCreatedAt := aiagentsecuritylogMixinFields0[0].Descriptor()
	// aiagentsecuritylog.DefaultCreatedAt holds the default value on creation for the created_at field.
	aiagentsecuritylog.DefaultCreatedAt = aiagentsecuritylogDescCreatedAt.Default.(func() time.Time)
	// aiagentsecuritylogDescUpdatedAt is the schema descriptor for updated_at field.
	aiagentsecuritylogDescUpdatedAt := aiagentsecuritylogMixinFields0[1].Descriptor()
	// aiagentsecuritylog.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	aiagentsecuritylog.DefaultUpdatedAt = aiagentsecuritylogDescUpdatedAt.Default.(func() time.Time)
	// aiagentsecuritylog.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	aiagentsecuritylog.UpdateDefaultUpdatedAt = aiagentsecuritylogDescUpdatedAt.UpdateDefault.(func() time.Time)
	// aiagentsecuritylogDescDeletedAt is the schema descriptor for deleted_at field.
	aiagentsecuritylogDescDeletedAt := aiagentsecuritylogMixinFields1[0].Descriptor()
	// aiagentsecuritylog.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	aiagentsecuritylog.DefaultDeletedAt = aiagentsecuritylogDescDeletedAt.Default.(time.Time)
	// aiagentsecuritylogDescHitAction is the schema descriptor for hit_action field.
	aiagentsecuritylogDescHitAction := aiagentsecuritylogFields[10].Descriptor()
	// aiagentsecuritylog.DefaultHitAction holds the default value on creation for the hit_action field.
	aiagentsecuritylog.DefaultHitAction = aiagentsecuritylogDescHitAction.Default.(int64)
	// aiagentsecuritylogDescActionCategory is the schema descriptor for action_category field.
	aiagentsecuritylogDescActionCategory := aiagentsecuritylogFields[12].Descriptor()
	// aiagentsecuritylog.DefaultActionCategory holds the default value on creation for the action_category field.
	aiagentsecuritylog.DefaultActionCategory = aiagentsecuritylogDescActionCategory.Default.(int64)
	// aiagentsecuritylogDescUploadedFiles is the schema descriptor for uploaded_files field.
	aiagentsecuritylogDescUploadedFiles := aiagentsecuritylogFields[13].Descriptor()
	// aiagentsecuritylog.DefaultUploadedFiles holds the default value on creation for the uploaded_files field.
	aiagentsecuritylog.DefaultUploadedFiles = aiagentsecuritylogDescUploadedFiles.Default.(*pq.StringArray)
	// aiagentsecuritylogDescHitPolicies is the schema descriptor for hit_policies field.
	aiagentsecuritylogDescHitPolicies := aiagentsecuritylogFields[14].Descriptor()
	// aiagentsecuritylog.DefaultHitPolicies holds the default value on creation for the hit_policies field.
	aiagentsecuritylog.DefaultHitPolicies = aiagentsecuritylogDescHitPolicies.Default.(*pq.StringArray)
	aiagentsecuritypolicyMixin := schema.AiAgentSecurityPolicy{}.Mixin()
	aiagentsecuritypolicyMixinHooks1 := aiagentsecuritypolicyMixin[1].Hooks()
	aiagentsecuritypolicy.Hooks[0] = aiagentsecuritypolicyMixinHooks1[0]
	aiagentsecuritypolicyMixinInters1 := aiagentsecuritypolicyMixin[1].Interceptors()
	aiagentsecuritypolicy.Interceptors[0] = aiagentsecuritypolicyMixinInters1[0]
	aiagentsecuritypolicyMixinFields0 := aiagentsecuritypolicyMixin[0].Fields()
	_ = aiagentsecuritypolicyMixinFields0
	aiagentsecuritypolicyMixinFields1 := aiagentsecuritypolicyMixin[1].Fields()
	_ = aiagentsecuritypolicyMixinFields1
	aiagentsecuritypolicyFields := schema.AiAgentSecurityPolicy{}.Fields()
	_ = aiagentsecuritypolicyFields
	// aiagentsecuritypolicyDescCreatedAt is the schema descriptor for created_at field.
	aiagentsecuritypolicyDescCreatedAt := aiagentsecuritypolicyMixinFields0[0].Descriptor()
	// aiagentsecuritypolicy.DefaultCreatedAt holds the default value on creation for the created_at field.
	aiagentsecuritypolicy.DefaultCreatedAt = aiagentsecuritypolicyDescCreatedAt.Default.(func() time.Time)
	// aiagentsecuritypolicyDescUpdatedAt is the schema descriptor for updated_at field.
	aiagentsecuritypolicyDescUpdatedAt := aiagentsecuritypolicyMixinFields0[1].Descriptor()
	// aiagentsecuritypolicy.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	aiagentsecuritypolicy.DefaultUpdatedAt = aiagentsecuritypolicyDescUpdatedAt.Default.(func() time.Time)
	// aiagentsecuritypolicy.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	aiagentsecuritypolicy.UpdateDefaultUpdatedAt = aiagentsecuritypolicyDescUpdatedAt.UpdateDefault.(func() time.Time)
	// aiagentsecuritypolicyDescDeletedAt is the schema descriptor for deleted_at field.
	aiagentsecuritypolicyDescDeletedAt := aiagentsecuritypolicyMixinFields1[0].Descriptor()
	// aiagentsecuritypolicy.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	aiagentsecuritypolicy.DefaultDeletedAt = aiagentsecuritypolicyDescDeletedAt.Default.(time.Time)
	// aiagentsecuritypolicyDescEnabled is the schema descriptor for enabled field.
	aiagentsecuritypolicyDescEnabled := aiagentsecuritypolicyFields[5].Descriptor()
	// aiagentsecuritypolicy.DefaultEnabled holds the default value on creation for the enabled field.
	aiagentsecuritypolicy.DefaultEnabled = aiagentsecuritypolicyDescEnabled.Default.(bool)
	// aiagentsecuritypolicyDescPolicies is the schema descriptor for policies field.
	aiagentsecuritypolicyDescPolicies := aiagentsecuritypolicyFields[6].Descriptor()
	// aiagentsecuritypolicy.DefaultPolicies holds the default value on creation for the policies field.
	aiagentsecuritypolicy.DefaultPolicies = aiagentsecuritypolicyDescPolicies.Default.(*pq.StringArray)
	// aiagentsecuritypolicyDescHitAction is the schema descriptor for hit_action field.
	aiagentsecuritypolicyDescHitAction := aiagentsecuritypolicyFields[7].Descriptor()
	// aiagentsecuritypolicy.DefaultHitAction holds the default value on creation for the hit_action field.
	aiagentsecuritypolicy.DefaultHitAction = aiagentsecuritypolicyDescHitAction.Default.(int64)
	// aiagentsecuritypolicyDescHitResponse is the schema descriptor for hit_response field.
	aiagentsecuritypolicyDescHitResponse := aiagentsecuritypolicyFields[8].Descriptor()
	// aiagentsecuritypolicy.DefaultHitResponse holds the default value on creation for the hit_response field.
	aiagentsecuritypolicy.DefaultHitResponse = aiagentsecuritypolicyDescHitResponse.Default.(string)
	aichatMixin := schema.AiChat{}.Mixin()
	aichatMixinHooks2 := aichatMixin[2].Hooks()
	aichat.Hooks[0] = aichatMixinHooks2[0]
	aichatMixinInters2 := aichatMixin[2].Interceptors()
	aichat.Interceptors[0] = aichatMixinInters2[0]
	aichatMixinFields1 := aichatMixin[1].Fields()
	_ = aichatMixinFields1
	aichatMixinFields2 := aichatMixin[2].Fields()
	_ = aichatMixinFields2
	aichatFields := schema.AiChat{}.Fields()
	_ = aichatFields
	// aichatDescCreatedAt is the schema descriptor for created_at field.
	aichatDescCreatedAt := aichatMixinFields1[0].Descriptor()
	// aichat.DefaultCreatedAt holds the default value on creation for the created_at field.
	aichat.DefaultCreatedAt = aichatDescCreatedAt.Default.(func() time.Time)
	// aichatDescUpdatedAt is the schema descriptor for updated_at field.
	aichatDescUpdatedAt := aichatMixinFields1[1].Descriptor()
	// aichat.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	aichat.DefaultUpdatedAt = aichatDescUpdatedAt.Default.(func() time.Time)
	// aichat.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	aichat.UpdateDefaultUpdatedAt = aichatDescUpdatedAt.UpdateDefault.(func() time.Time)
	// aichatDescDeletedAt is the schema descriptor for deleted_at field.
	aichatDescDeletedAt := aichatMixinFields2[0].Descriptor()
	// aichat.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	aichat.DefaultDeletedAt = aichatDescDeletedAt.Default.(time.Time)
	aichatitemMixin := schema.AiChatItem{}.Mixin()
	aichatitemMixinHooks2 := aichatitemMixin[2].Hooks()
	aichatitem.Hooks[0] = aichatitemMixinHooks2[0]
	aichatitemMixinInters2 := aichatitemMixin[2].Interceptors()
	aichatitem.Interceptors[0] = aichatitemMixinInters2[0]
	aichatitemMixinFields1 := aichatitemMixin[1].Fields()
	_ = aichatitemMixinFields1
	aichatitemMixinFields2 := aichatitemMixin[2].Fields()
	_ = aichatitemMixinFields2
	aichatitemFields := schema.AiChatItem{}.Fields()
	_ = aichatitemFields
	// aichatitemDescCreatedAt is the schema descriptor for created_at field.
	aichatitemDescCreatedAt := aichatitemMixinFields1[0].Descriptor()
	// aichatitem.DefaultCreatedAt holds the default value on creation for the created_at field.
	aichatitem.DefaultCreatedAt = aichatitemDescCreatedAt.Default.(func() time.Time)
	// aichatitemDescUpdatedAt is the schema descriptor for updated_at field.
	aichatitemDescUpdatedAt := aichatitemMixinFields1[1].Descriptor()
	// aichatitem.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	aichatitem.DefaultUpdatedAt = aichatitemDescUpdatedAt.Default.(func() time.Time)
	// aichatitem.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	aichatitem.UpdateDefaultUpdatedAt = aichatitemDescUpdatedAt.UpdateDefault.(func() time.Time)
	// aichatitemDescDeletedAt is the schema descriptor for deleted_at field.
	aichatitemDescDeletedAt := aichatitemMixinFields2[0].Descriptor()
	// aichatitem.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	aichatitem.DefaultDeletedAt = aichatitemDescDeletedAt.Default.(time.Time)
	// aichatitemDescAgreeStatus is the schema descriptor for agree_status field.
	aichatitemDescAgreeStatus := aichatitemFields[6].Descriptor()
	// aichatitem.DefaultAgreeStatus holds the default value on creation for the agree_status field.
	aichatitem.DefaultAgreeStatus = aichatitemDescAgreeStatus.Default.(int8)
	// aichatitemDescRoundID is the schema descriptor for round_id field.
	aichatitemDescRoundID := aichatitemFields[7].Descriptor()
	// aichatitem.DefaultRoundID holds the default value on creation for the round_id field.
	aichatitem.DefaultRoundID = aichatitemDescRoundID.Default.(int64)
	// aichatitemDescPcName is the schema descriptor for pc_name field.
	aichatitemDescPcName := aichatitemFields[8].Descriptor()
	// aichatitem.DefaultPcName holds the default value on creation for the pc_name field.
	aichatitem.DefaultPcName = aichatitemDescPcName.Default.(string)
	// aichatitemDescReason is the schema descriptor for reason field.
	aichatitemDescReason := aichatitemFields[9].Descriptor()
	// aichatitem.DefaultReason holds the default value on creation for the reason field.
	aichatitem.DefaultReason = aichatitemDescReason.Default.(string)
	// aichatitemDescPrimaryClassification is the schema descriptor for primary_classification field.
	aichatitemDescPrimaryClassification := aichatitemFields[10].Descriptor()
	// aichatitem.DefaultPrimaryClassification holds the default value on creation for the primary_classification field.
	aichatitem.DefaultPrimaryClassification = aichatitemDescPrimaryClassification.Default.(string)
	// aichatitemDescSecondaryClassification is the schema descriptor for secondary_classification field.
	aichatitemDescSecondaryClassification := aichatitemFields[11].Descriptor()
	// aichatitem.DefaultSecondaryClassification holds the default value on creation for the secondary_classification field.
	aichatitem.DefaultSecondaryClassification = aichatitemDescSecondaryClassification.Default.(string)
	// aichatitemDescMineTypes is the schema descriptor for mine_types field.
	aichatitemDescMineTypes := aichatitemFields[12].Descriptor()
	// aichatitem.DefaultMineTypes holds the default value on creation for the mine_types field.
	aichatitem.DefaultMineTypes = aichatitemDescMineTypes.Default.(*pq.StringArray)
	// aichatitemDescSuggestQuestions is the schema descriptor for suggest_questions field.
	aichatitemDescSuggestQuestions := aichatitemFields[13].Descriptor()
	// aichatitem.DefaultSuggestQuestions holds the default value on creation for the suggest_questions field.
	aichatitem.DefaultSuggestQuestions = aichatitemDescSuggestQuestions.Default.(*pq.StringArray)
	// aichatitemDescHitAction is the schema descriptor for hit_action field.
	aichatitemDescHitAction := aichatitemFields[14].Descriptor()
	// aichatitem.DefaultHitAction holds the default value on creation for the hit_action field.
	aichatitem.DefaultHitAction = aichatitemDescHitAction.Default.(int64)
	// aichatitemDescHitResponse is the schema descriptor for hit_response field.
	aichatitemDescHitResponse := aichatitemFields[15].Descriptor()
	// aichatitem.DefaultHitResponse holds the default value on creation for the hit_response field.
	aichatitem.DefaultHitResponse = aichatitemDescHitResponse.Default.(string)
	// aichatitemDescHitContinueSend is the schema descriptor for hit_continue_send field.
	aichatitemDescHitContinueSend := aichatitemFields[16].Descriptor()
	// aichatitem.DefaultHitContinueSend holds the default value on creation for the hit_continue_send field.
	aichatitem.DefaultHitContinueSend = aichatitemDescHitContinueSend.Default.(bool)
	// aichatitemDescIsInternetSearch is the schema descriptor for is_internet_search field.
	aichatitemDescIsInternetSearch := aichatitemFields[17].Descriptor()
	// aichatitem.DefaultIsInternetSearch holds the default value on creation for the is_internet_search field.
	aichatitem.DefaultIsInternetSearch = aichatitemDescIsInternetSearch.Default.(bool)
	aimodelMixin := schema.AiModel{}.Mixin()
	aimodelMixinHooks1 := aimodelMixin[1].Hooks()
	aimodel.Hooks[0] = aimodelMixinHooks1[0]
	aimodelMixinInters1 := aimodelMixin[1].Interceptors()
	aimodel.Interceptors[0] = aimodelMixinInters1[0]
	aimodelMixinFields0 := aimodelMixin[0].Fields()
	_ = aimodelMixinFields0
	aimodelMixinFields1 := aimodelMixin[1].Fields()
	_ = aimodelMixinFields1
	aimodelFields := schema.AiModel{}.Fields()
	_ = aimodelFields
	// aimodelDescCreatedAt is the schema descriptor for created_at field.
	aimodelDescCreatedAt := aimodelMixinFields0[0].Descriptor()
	// aimodel.DefaultCreatedAt holds the default value on creation for the created_at field.
	aimodel.DefaultCreatedAt = aimodelDescCreatedAt.Default.(func() time.Time)
	// aimodelDescUpdatedAt is the schema descriptor for updated_at field.
	aimodelDescUpdatedAt := aimodelMixinFields0[1].Descriptor()
	// aimodel.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	aimodel.DefaultUpdatedAt = aimodelDescUpdatedAt.Default.(func() time.Time)
	// aimodel.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	aimodel.UpdateDefaultUpdatedAt = aimodelDescUpdatedAt.UpdateDefault.(func() time.Time)
	// aimodelDescDeletedAt is the schema descriptor for deleted_at field.
	aimodelDescDeletedAt := aimodelMixinFields1[0].Descriptor()
	// aimodel.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	aimodel.DefaultDeletedAt = aimodelDescDeletedAt.Default.(time.Time)
	aimodeldetailMixin := schema.AiModelDetail{}.Mixin()
	aimodeldetailMixinHooks1 := aimodeldetailMixin[1].Hooks()
	aimodeldetail.Hooks[0] = aimodeldetailMixinHooks1[0]
	aimodeldetailMixinInters1 := aimodeldetailMixin[1].Interceptors()
	aimodeldetail.Interceptors[0] = aimodeldetailMixinInters1[0]
	aimodeldetailMixinFields0 := aimodeldetailMixin[0].Fields()
	_ = aimodeldetailMixinFields0
	aimodeldetailMixinFields1 := aimodeldetailMixin[1].Fields()
	_ = aimodeldetailMixinFields1
	aimodeldetailFields := schema.AiModelDetail{}.Fields()
	_ = aimodeldetailFields
	// aimodeldetailDescCreatedAt is the schema descriptor for created_at field.
	aimodeldetailDescCreatedAt := aimodeldetailMixinFields0[0].Descriptor()
	// aimodeldetail.DefaultCreatedAt holds the default value on creation for the created_at field.
	aimodeldetail.DefaultCreatedAt = aimodeldetailDescCreatedAt.Default.(func() time.Time)
	// aimodeldetailDescUpdatedAt is the schema descriptor for updated_at field.
	aimodeldetailDescUpdatedAt := aimodeldetailMixinFields0[1].Descriptor()
	// aimodeldetail.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	aimodeldetail.DefaultUpdatedAt = aimodeldetailDescUpdatedAt.Default.(func() time.Time)
	// aimodeldetail.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	aimodeldetail.UpdateDefaultUpdatedAt = aimodeldetailDescUpdatedAt.UpdateDefault.(func() time.Time)
	// aimodeldetailDescDeletedAt is the schema descriptor for deleted_at field.
	aimodeldetailDescDeletedAt := aimodeldetailMixinFields1[0].Descriptor()
	// aimodeldetail.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	aimodeldetail.DefaultDeletedAt = aimodeldetailDescDeletedAt.Default.(time.Time)
	// aimodeldetailDescThinkingEnableStatus is the schema descriptor for thinking_enable_status field.
	aimodeldetailDescThinkingEnableStatus := aimodeldetailFields[7].Descriptor()
	// aimodeldetail.DefaultThinkingEnableStatus holds the default value on creation for the thinking_enable_status field.
	aimodeldetail.DefaultThinkingEnableStatus = aimodeldetailDescThinkingEnableStatus.Default.(int64)
	aimodelusageMixin := schema.AiModelUsage{}.Mixin()
	aimodelusageMixinHooks1 := aimodelusageMixin[1].Hooks()
	aimodelusage.Hooks[0] = aimodelusageMixinHooks1[0]
	aimodelusageMixinInters1 := aimodelusageMixin[1].Interceptors()
	aimodelusage.Interceptors[0] = aimodelusageMixinInters1[0]
	aimodelusageMixinFields0 := aimodelusageMixin[0].Fields()
	_ = aimodelusageMixinFields0
	aimodelusageMixinFields1 := aimodelusageMixin[1].Fields()
	_ = aimodelusageMixinFields1
	aimodelusageFields := schema.AiModelUsage{}.Fields()
	_ = aimodelusageFields
	// aimodelusageDescCreatedAt is the schema descriptor for created_at field.
	aimodelusageDescCreatedAt := aimodelusageMixinFields0[0].Descriptor()
	// aimodelusage.DefaultCreatedAt holds the default value on creation for the created_at field.
	aimodelusage.DefaultCreatedAt = aimodelusageDescCreatedAt.Default.(func() time.Time)
	// aimodelusageDescUpdatedAt is the schema descriptor for updated_at field.
	aimodelusageDescUpdatedAt := aimodelusageMixinFields0[1].Descriptor()
	// aimodelusage.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	aimodelusage.DefaultUpdatedAt = aimodelusageDescUpdatedAt.Default.(func() time.Time)
	// aimodelusage.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	aimodelusage.UpdateDefaultUpdatedAt = aimodelusageDescUpdatedAt.UpdateDefault.(func() time.Time)
	// aimodelusageDescDeletedAt is the schema descriptor for deleted_at field.
	aimodelusageDescDeletedAt := aimodelusageMixinFields1[0].Descriptor()
	// aimodelusage.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	aimodelusage.DefaultDeletedAt = aimodelusageDescDeletedAt.Default.(time.Time)
	// aimodelusageDescRequestStatus is the schema descriptor for request_status field.
	aimodelusageDescRequestStatus := aimodelusageFields[12].Descriptor()
	// aimodelusage.DefaultRequestStatus holds the default value on creation for the request_status field.
	aimodelusage.DefaultRequestStatus = aimodelusageDescRequestStatus.Default.(int8)
	// aimodelusageDescErrorCode is the schema descriptor for error_code field.
	aimodelusageDescErrorCode := aimodelusageFields[13].Descriptor()
	// aimodelusage.DefaultErrorCode holds the default value on creation for the error_code field.
	aimodelusage.DefaultErrorCode = aimodelusageDescErrorCode.Default.(string)
	atomicquestionsMixin := schema.AtomicQuestions{}.Mixin()
	atomicquestionsMixinFields0 := atomicquestionsMixin[0].Fields()
	_ = atomicquestionsMixinFields0
	atomicquestionsFields := schema.AtomicQuestions{}.Fields()
	_ = atomicquestionsFields
	// atomicquestionsDescCreatedAt is the schema descriptor for created_at field.
	atomicquestionsDescCreatedAt := atomicquestionsMixinFields0[0].Descriptor()
	// atomicquestions.DefaultCreatedAt holds the default value on creation for the created_at field.
	atomicquestions.DefaultCreatedAt = atomicquestionsDescCreatedAt.Default.(func() time.Time)
	// atomicquestionsDescUpdatedAt is the schema descriptor for updated_at field.
	atomicquestionsDescUpdatedAt := atomicquestionsMixinFields0[1].Descriptor()
	// atomicquestions.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	atomicquestions.DefaultUpdatedAt = atomicquestionsDescUpdatedAt.Default.(func() time.Time)
	// atomicquestions.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	atomicquestions.UpdateDefaultUpdatedAt = atomicquestionsDescUpdatedAt.UpdateDefault.(func() time.Time)
	// atomicquestionsDescQuestion is the schema descriptor for question field.
	atomicquestionsDescQuestion := atomicquestionsFields[7].Descriptor()
	// atomicquestions.DefaultQuestion holds the default value on creation for the question field.
	atomicquestions.DefaultQuestion = atomicquestionsDescQuestion.Default.(*pq.StringArray)
	// atomicquestionsDescIsHandle is the schema descriptor for is_handle field.
	atomicquestionsDescIsHandle := atomicquestionsFields[8].Descriptor()
	// atomicquestions.DefaultIsHandle holds the default value on creation for the is_handle field.
	atomicquestions.DefaultIsHandle = atomicquestionsDescIsHandle.Default.(bool)
	classificationfilesFields := schema.ClassificationFiles{}.Fields()
	_ = classificationfilesFields
	// classificationfilesDescName is the schema descriptor for name field.
	classificationfilesDescName := classificationfilesFields[1].Descriptor()
	// classificationfiles.DefaultName holds the default value on creation for the name field.
	classificationfiles.DefaultName = classificationfilesDescName.Default.(string)
	// classificationfilesDescFileRelationID is the schema descriptor for file_relation_id field.
	classificationfilesDescFileRelationID := classificationfilesFields[2].Descriptor()
	// classificationfiles.DefaultFileRelationID holds the default value on creation for the file_relation_id field.
	classificationfiles.DefaultFileRelationID = classificationfilesDescFileRelationID.Default.(int64)
	// classificationfilesDescPreEntityTag is the schema descriptor for pre_entity_tag field.
	classificationfilesDescPreEntityTag := classificationfilesFields[3].Descriptor()
	// classificationfiles.DefaultPreEntityTag holds the default value on creation for the pre_entity_tag field.
	classificationfiles.DefaultPreEntityTag = classificationfilesDescPreEntityTag.Default.(string)
	// classificationfilesDescEntityTag is the schema descriptor for entity_tag field.
	classificationfilesDescEntityTag := classificationfilesFields[4].Descriptor()
	// classificationfiles.DefaultEntityTag holds the default value on creation for the entity_tag field.
	classificationfiles.DefaultEntityTag = classificationfilesDescEntityTag.Default.(string)
	// classificationfilesDescFilename is the schema descriptor for filename field.
	classificationfilesDescFilename := classificationfilesFields[5].Descriptor()
	// classificationfiles.DefaultFilename holds the default value on creation for the filename field.
	classificationfiles.DefaultFilename = classificationfilesDescFilename.Default.(string)
	// classificationfilesDescMimeType is the schema descriptor for mime_type field.
	classificationfilesDescMimeType := classificationfilesFields[6].Descriptor()
	// classificationfiles.DefaultMimeType holds the default value on creation for the mime_type field.
	classificationfiles.DefaultMimeType = classificationfilesDescMimeType.Default.(string)
	// classificationfilesDescUserID is the schema descriptor for user_id field.
	classificationfilesDescUserID := classificationfilesFields[7].Descriptor()
	// classificationfiles.DefaultUserID holds the default value on creation for the user_id field.
	classificationfiles.DefaultUserID = classificationfilesDescUserID.Default.(int64)
	// classificationfilesDescUserName is the schema descriptor for user_name field.
	classificationfilesDescUserName := classificationfilesFields[8].Descriptor()
	// classificationfiles.DefaultUserName holds the default value on creation for the user_name field.
	classificationfiles.DefaultUserName = classificationfilesDescUserName.Default.(string)
	// classificationfilesDescDeptIds is the schema descriptor for dept_ids field.
	classificationfilesDescDeptIds := classificationfilesFields[9].Descriptor()
	// classificationfiles.DefaultDeptIds holds the default value on creation for the dept_ids field.
	classificationfiles.DefaultDeptIds = classificationfilesDescDeptIds.Default.(*pq.Int64Array)
	// classificationfilesDescDeptName is the schema descriptor for dept_name field.
	classificationfilesDescDeptName := classificationfilesFields[10].Descriptor()
	// classificationfiles.DefaultDeptName holds the default value on creation for the dept_name field.
	classificationfiles.DefaultDeptName = classificationfilesDescDeptName.Default.(string)
	// classificationfilesDescPath is the schema descriptor for path field.
	classificationfilesDescPath := classificationfilesFields[11].Descriptor()
	// classificationfiles.DefaultPath holds the default value on creation for the path field.
	classificationfiles.DefaultPath = classificationfilesDescPath.Default.(string)
	// classificationfilesDescSecurityLevel is the schema descriptor for security_level field.
	classificationfilesDescSecurityLevel := classificationfilesFields[12].Descriptor()
	// classificationfiles.DefaultSecurityLevel holds the default value on creation for the security_level field.
	classificationfiles.DefaultSecurityLevel = classificationfilesDescSecurityLevel.Default.(int32)
	// classificationfilesDescNodeLevel is the schema descriptor for node_level field.
	classificationfilesDescNodeLevel := classificationfilesFields[13].Descriptor()
	// classificationfiles.DefaultNodeLevel holds the default value on creation for the node_level field.
	classificationfiles.DefaultNodeLevel = classificationfilesDescNodeLevel.Default.(int32)
	// classificationfilesDescCheckStatus is the schema descriptor for check_status field.
	classificationfilesDescCheckStatus := classificationfilesFields[14].Descriptor()
	// classificationfiles.DefaultCheckStatus holds the default value on creation for the check_status field.
	classificationfiles.DefaultCheckStatus = classificationfilesDescCheckStatus.Default.(int32)
	// classificationfilesDescTreeType is the schema descriptor for tree_type field.
	classificationfilesDescTreeType := classificationfilesFields[15].Descriptor()
	// classificationfiles.DefaultTreeType holds the default value on creation for the tree_type field.
	classificationfiles.DefaultTreeType = classificationfilesDescTreeType.Default.(int32)
	defaultagentavatarMixin := schema.DefaultAgentAvatar{}.Mixin()
	defaultagentavatarMixinHooks2 := defaultagentavatarMixin[2].Hooks()
	defaultagentavatar.Hooks[0] = defaultagentavatarMixinHooks2[0]
	defaultagentavatarMixinInters2 := defaultagentavatarMixin[2].Interceptors()
	defaultagentavatar.Interceptors[0] = defaultagentavatarMixinInters2[0]
	defaultagentavatarMixinFields1 := defaultagentavatarMixin[1].Fields()
	_ = defaultagentavatarMixinFields1
	defaultagentavatarMixinFields2 := defaultagentavatarMixin[2].Fields()
	_ = defaultagentavatarMixinFields2
	defaultagentavatarFields := schema.DefaultAgentAvatar{}.Fields()
	_ = defaultagentavatarFields
	// defaultagentavatarDescCreatedAt is the schema descriptor for created_at field.
	defaultagentavatarDescCreatedAt := defaultagentavatarMixinFields1[0].Descriptor()
	// defaultagentavatar.DefaultCreatedAt holds the default value on creation for the created_at field.
	defaultagentavatar.DefaultCreatedAt = defaultagentavatarDescCreatedAt.Default.(func() time.Time)
	// defaultagentavatarDescUpdatedAt is the schema descriptor for updated_at field.
	defaultagentavatarDescUpdatedAt := defaultagentavatarMixinFields1[1].Descriptor()
	// defaultagentavatar.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	defaultagentavatar.DefaultUpdatedAt = defaultagentavatarDescUpdatedAt.Default.(func() time.Time)
	// defaultagentavatar.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	defaultagentavatar.UpdateDefaultUpdatedAt = defaultagentavatarDescUpdatedAt.UpdateDefault.(func() time.Time)
	// defaultagentavatarDescDeletedAt is the schema descriptor for deleted_at field.
	defaultagentavatarDescDeletedAt := defaultagentavatarMixinFields2[0].Descriptor()
	// defaultagentavatar.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	defaultagentavatar.DefaultDeletedAt = defaultagentavatarDescDeletedAt.Default.(time.Time)
	externalmodelusageMixin := schema.ExternalModelUsage{}.Mixin()
	externalmodelusageMixinHooks1 := externalmodelusageMixin[1].Hooks()
	externalmodelusage.Hooks[0] = externalmodelusageMixinHooks1[0]
	externalmodelusageMixinInters1 := externalmodelusageMixin[1].Interceptors()
	externalmodelusage.Interceptors[0] = externalmodelusageMixinInters1[0]
	externalmodelusageMixinFields0 := externalmodelusageMixin[0].Fields()
	_ = externalmodelusageMixinFields0
	externalmodelusageMixinFields1 := externalmodelusageMixin[1].Fields()
	_ = externalmodelusageMixinFields1
	externalmodelusageFields := schema.ExternalModelUsage{}.Fields()
	_ = externalmodelusageFields
	// externalmodelusageDescCreatedAt is the schema descriptor for created_at field.
	externalmodelusageDescCreatedAt := externalmodelusageMixinFields0[0].Descriptor()
	// externalmodelusage.DefaultCreatedAt holds the default value on creation for the created_at field.
	externalmodelusage.DefaultCreatedAt = externalmodelusageDescCreatedAt.Default.(func() time.Time)
	// externalmodelusageDescUpdatedAt is the schema descriptor for updated_at field.
	externalmodelusageDescUpdatedAt := externalmodelusageMixinFields0[1].Descriptor()
	// externalmodelusage.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	externalmodelusage.DefaultUpdatedAt = externalmodelusageDescUpdatedAt.Default.(func() time.Time)
	// externalmodelusage.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	externalmodelusage.UpdateDefaultUpdatedAt = externalmodelusageDescUpdatedAt.UpdateDefault.(func() time.Time)
	// externalmodelusageDescDeletedAt is the schema descriptor for deleted_at field.
	externalmodelusageDescDeletedAt := externalmodelusageMixinFields1[0].Descriptor()
	// externalmodelusage.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	externalmodelusage.DefaultDeletedAt = externalmodelusageDescDeletedAt.Default.(time.Time)
	// externalmodelusageDescModelName is the schema descriptor for model_name field.
	externalmodelusageDescModelName := externalmodelusageFields[1].Descriptor()
	// externalmodelusage.DefaultModelName holds the default value on creation for the model_name field.
	externalmodelusage.DefaultModelName = externalmodelusageDescModelName.Default.(string)
	// externalmodelusageDescQuestion is the schema descriptor for question field.
	externalmodelusageDescQuestion := externalmodelusageFields[2].Descriptor()
	// externalmodelusage.DefaultQuestion holds the default value on creation for the question field.
	externalmodelusage.DefaultQuestion = externalmodelusageDescQuestion.Default.(string)
	// externalmodelusageDescQuestionTag is the schema descriptor for question_tag field.
	externalmodelusageDescQuestionTag := externalmodelusageFields[3].Descriptor()
	// externalmodelusage.DefaultQuestionTag holds the default value on creation for the question_tag field.
	externalmodelusage.DefaultQuestionTag = externalmodelusageDescQuestionTag.Default.(string)
	// externalmodelusageDescFiles is the schema descriptor for files field.
	externalmodelusageDescFiles := externalmodelusageFields[4].Descriptor()
	// externalmodelusage.DefaultFiles holds the default value on creation for the files field.
	externalmodelusage.DefaultFiles = externalmodelusageDescFiles.Default.(string)
	// externalmodelusageDescMimeTypes is the schema descriptor for mime_types field.
	externalmodelusageDescMimeTypes := externalmodelusageFields[5].Descriptor()
	// externalmodelusage.DefaultMimeTypes holds the default value on creation for the mime_types field.
	externalmodelusage.DefaultMimeTypes = externalmodelusageDescMimeTypes.Default.(*pq.StringArray)
	// externalmodelusageDescUserID is the schema descriptor for user_id field.
	externalmodelusageDescUserID := externalmodelusageFields[6].Descriptor()
	// externalmodelusage.DefaultUserID holds the default value on creation for the user_id field.
	externalmodelusage.DefaultUserID = externalmodelusageDescUserID.Default.(int64)
	// externalmodelusageDescUserName is the schema descriptor for user_name field.
	externalmodelusageDescUserName := externalmodelusageFields[7].Descriptor()
	// externalmodelusage.DefaultUserName holds the default value on creation for the user_name field.
	externalmodelusage.DefaultUserName = externalmodelusageDescUserName.Default.(string)
	// externalmodelusageDescDeptID is the schema descriptor for dept_id field.
	externalmodelusageDescDeptID := externalmodelusageFields[8].Descriptor()
	// externalmodelusage.DefaultDeptID holds the default value on creation for the dept_id field.
	externalmodelusage.DefaultDeptID = externalmodelusageDescDeptID.Default.(int64)
	// externalmodelusageDescDeptName is the schema descriptor for dept_name field.
	externalmodelusageDescDeptName := externalmodelusageFields[9].Descriptor()
	// externalmodelusage.DefaultDeptName holds the default value on creation for the dept_name field.
	externalmodelusage.DefaultDeptName = externalmodelusageDescDeptName.Default.(string)
	// externalmodelusageDescPcName is the schema descriptor for pc_name field.
	externalmodelusageDescPcName := externalmodelusageFields[10].Descriptor()
	// externalmodelusage.DefaultPcName holds the default value on creation for the pc_name field.
	externalmodelusage.DefaultPcName = externalmodelusageDescPcName.Default.(string)
	// externalmodelusageDescHappenedAt is the schema descriptor for happened_at field.
	externalmodelusageDescHappenedAt := externalmodelusageFields[11].Descriptor()
	// externalmodelusage.DefaultHappenedAt holds the default value on creation for the happened_at field.
	externalmodelusage.DefaultHappenedAt = externalmodelusageDescHappenedAt.Default.(time.Time)
	knowledgebaseMixin := schema.KnowledgeBase{}.Mixin()
	knowledgebaseMixinHooks2 := knowledgebaseMixin[2].Hooks()
	knowledgebase.Hooks[0] = knowledgebaseMixinHooks2[0]
	knowledgebaseMixinInters2 := knowledgebaseMixin[2].Interceptors()
	knowledgebase.Interceptors[0] = knowledgebaseMixinInters2[0]
	knowledgebaseMixinFields1 := knowledgebaseMixin[1].Fields()
	_ = knowledgebaseMixinFields1
	knowledgebaseMixinFields2 := knowledgebaseMixin[2].Fields()
	_ = knowledgebaseMixinFields2
	knowledgebaseFields := schema.KnowledgeBase{}.Fields()
	_ = knowledgebaseFields
	// knowledgebaseDescCreatedAt is the schema descriptor for created_at field.
	knowledgebaseDescCreatedAt := knowledgebaseMixinFields1[0].Descriptor()
	// knowledgebase.DefaultCreatedAt holds the default value on creation for the created_at field.
	knowledgebase.DefaultCreatedAt = knowledgebaseDescCreatedAt.Default.(func() time.Time)
	// knowledgebaseDescUpdatedAt is the schema descriptor for updated_at field.
	knowledgebaseDescUpdatedAt := knowledgebaseMixinFields1[1].Descriptor()
	// knowledgebase.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	knowledgebase.DefaultUpdatedAt = knowledgebaseDescUpdatedAt.Default.(func() time.Time)
	// knowledgebase.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	knowledgebase.UpdateDefaultUpdatedAt = knowledgebaseDescUpdatedAt.UpdateDefault.(func() time.Time)
	// knowledgebaseDescDeletedAt is the schema descriptor for deleted_at field.
	knowledgebaseDescDeletedAt := knowledgebaseMixinFields2[0].Descriptor()
	// knowledgebase.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	knowledgebase.DefaultDeletedAt = knowledgebaseDescDeletedAt.Default.(time.Time)
	// knowledgebaseDescName is the schema descriptor for name field.
	knowledgebaseDescName := knowledgebaseFields[1].Descriptor()
	// knowledgebase.DefaultName holds the default value on creation for the name field.
	knowledgebase.DefaultName = knowledgebaseDescName.Default.(string)
	// knowledgebaseDescPublic is the schema descriptor for public field.
	knowledgebaseDescPublic := knowledgebaseFields[2].Descriptor()
	// knowledgebase.DefaultPublic holds the default value on creation for the public field.
	knowledgebase.DefaultPublic = knowledgebaseDescPublic.Default.(bool)
	// knowledgebaseDescDataType is the schema descriptor for data_type field.
	knowledgebaseDescDataType := knowledgebaseFields[3].Descriptor()
	// knowledgebase.DefaultDataType holds the default value on creation for the data_type field.
	knowledgebase.DefaultDataType = knowledgebaseDescDataType.Default.(int32)
	// knowledgebaseDescUserID is the schema descriptor for user_id field.
	knowledgebaseDescUserID := knowledgebaseFields[4].Descriptor()
	// knowledgebase.DefaultUserID holds the default value on creation for the user_id field.
	knowledgebase.DefaultUserID = knowledgebaseDescUserID.Default.(int64)
	// knowledgebaseDescManagerUserIds is the schema descriptor for manager_user_ids field.
	knowledgebaseDescManagerUserIds := knowledgebaseFields[5].Descriptor()
	// knowledgebase.DefaultManagerUserIds holds the default value on creation for the manager_user_ids field.
	knowledgebase.DefaultManagerUserIds = knowledgebaseDescManagerUserIds.Default.(*pq.Int64Array)
	// knowledgebaseDescEditableUserIds is the schema descriptor for editable_user_ids field.
	knowledgebaseDescEditableUserIds := knowledgebaseFields[6].Descriptor()
	// knowledgebase.DefaultEditableUserIds holds the default value on creation for the editable_user_ids field.
	knowledgebase.DefaultEditableUserIds = knowledgebaseDescEditableUserIds.Default.(*pq.Int64Array)
	knowledgebasefileMixin := schema.KnowledgeBaseFile{}.Mixin()
	knowledgebasefileMixinHooks2 := knowledgebasefileMixin[2].Hooks()
	knowledgebasefile.Hooks[0] = knowledgebasefileMixinHooks2[0]
	knowledgebasefileMixinInters2 := knowledgebasefileMixin[2].Interceptors()
	knowledgebasefile.Interceptors[0] = knowledgebasefileMixinInters2[0]
	knowledgebasefileMixinFields1 := knowledgebasefileMixin[1].Fields()
	_ = knowledgebasefileMixinFields1
	knowledgebasefileMixinFields2 := knowledgebasefileMixin[2].Fields()
	_ = knowledgebasefileMixinFields2
	knowledgebasefileFields := schema.KnowledgeBaseFile{}.Fields()
	_ = knowledgebasefileFields
	// knowledgebasefileDescCreatedAt is the schema descriptor for created_at field.
	knowledgebasefileDescCreatedAt := knowledgebasefileMixinFields1[0].Descriptor()
	// knowledgebasefile.DefaultCreatedAt holds the default value on creation for the created_at field.
	knowledgebasefile.DefaultCreatedAt = knowledgebasefileDescCreatedAt.Default.(func() time.Time)
	// knowledgebasefileDescUpdatedAt is the schema descriptor for updated_at field.
	knowledgebasefileDescUpdatedAt := knowledgebasefileMixinFields1[1].Descriptor()
	// knowledgebasefile.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	knowledgebasefile.DefaultUpdatedAt = knowledgebasefileDescUpdatedAt.Default.(func() time.Time)
	// knowledgebasefile.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	knowledgebasefile.UpdateDefaultUpdatedAt = knowledgebasefileDescUpdatedAt.UpdateDefault.(func() time.Time)
	// knowledgebasefileDescDeletedAt is the schema descriptor for deleted_at field.
	knowledgebasefileDescDeletedAt := knowledgebasefileMixinFields2[0].Descriptor()
	// knowledgebasefile.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	knowledgebasefile.DefaultDeletedAt = knowledgebasefileDescDeletedAt.Default.(time.Time)
	// knowledgebasefileDescKnowledgeBaseID is the schema descriptor for knowledge_base_id field.
	knowledgebasefileDescKnowledgeBaseID := knowledgebasefileFields[1].Descriptor()
	// knowledgebasefile.DefaultKnowledgeBaseID holds the default value on creation for the knowledge_base_id field.
	knowledgebasefile.DefaultKnowledgeBaseID = knowledgebasefileDescKnowledgeBaseID.Default.(int64)
	// knowledgebasefileDescDataType is the schema descriptor for data_type field.
	knowledgebasefileDescDataType := knowledgebasefileFields[2].Descriptor()
	// knowledgebasefile.DefaultDataType holds the default value on creation for the data_type field.
	knowledgebasefile.DefaultDataType = knowledgebasefileDescDataType.Default.(int32)
	// knowledgebasefileDescFileRelationID is the schema descriptor for file_relation_id field.
	knowledgebasefileDescFileRelationID := knowledgebasefileFields[3].Descriptor()
	// knowledgebasefile.DefaultFileRelationID holds the default value on creation for the file_relation_id field.
	knowledgebasefile.DefaultFileRelationID = knowledgebasefileDescFileRelationID.Default.(int64)
	// knowledgebasefileDescMetadata is the schema descriptor for metadata field.
	knowledgebasefileDescMetadata := knowledgebasefileFields[4].Descriptor()
	// knowledgebasefile.DefaultMetadata holds the default value on creation for the metadata field.
	knowledgebasefile.DefaultMetadata = knowledgebasefileDescMetadata.Default.(string)
	// knowledgebasefileDescStatus is the schema descriptor for status field.
	knowledgebasefileDescStatus := knowledgebasefileFields[5].Descriptor()
	// knowledgebasefile.DefaultStatus holds the default value on creation for the status field.
	knowledgebasefile.DefaultStatus = knowledgebasefileDescStatus.Default.(int32)
	// knowledgebasefileDescFailedReason is the schema descriptor for failed_reason field.
	knowledgebasefileDescFailedReason := knowledgebasefileFields[6].Descriptor()
	// knowledgebasefile.DefaultFailedReason holds the default value on creation for the failed_reason field.
	knowledgebasefile.DefaultFailedReason = knowledgebasefileDescFailedReason.Default.(string)
	useragentorderMixin := schema.UserAgentOrder{}.Mixin()
	useragentorderMixinHooks1 := useragentorderMixin[1].Hooks()
	useragentorder.Hooks[0] = useragentorderMixinHooks1[0]
	useragentorderMixinInters1 := useragentorderMixin[1].Interceptors()
	useragentorder.Interceptors[0] = useragentorderMixinInters1[0]
	useragentorderMixinFields0 := useragentorderMixin[0].Fields()
	_ = useragentorderMixinFields0
	useragentorderMixinFields1 := useragentorderMixin[1].Fields()
	_ = useragentorderMixinFields1
	useragentorderFields := schema.UserAgentOrder{}.Fields()
	_ = useragentorderFields
	// useragentorderDescCreatedAt is the schema descriptor for created_at field.
	useragentorderDescCreatedAt := useragentorderMixinFields0[0].Descriptor()
	// useragentorder.DefaultCreatedAt holds the default value on creation for the created_at field.
	useragentorder.DefaultCreatedAt = useragentorderDescCreatedAt.Default.(func() time.Time)
	// useragentorderDescUpdatedAt is the schema descriptor for updated_at field.
	useragentorderDescUpdatedAt := useragentorderMixinFields0[1].Descriptor()
	// useragentorder.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	useragentorder.DefaultUpdatedAt = useragentorderDescUpdatedAt.Default.(func() time.Time)
	// useragentorder.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	useragentorder.UpdateDefaultUpdatedAt = useragentorderDescUpdatedAt.UpdateDefault.(func() time.Time)
	// useragentorderDescDeletedAt is the schema descriptor for deleted_at field.
	useragentorderDescDeletedAt := useragentorderMixinFields1[0].Descriptor()
	// useragentorder.DefaultDeletedAt holds the default value on creation for the deleted_at field.
	useragentorder.DefaultDeletedAt = useragentorderDescDeletedAt.Default.(time.Time)
	// useragentorderDescUserID is the schema descriptor for user_id field.
	useragentorderDescUserID := useragentorderFields[1].Descriptor()
	// useragentorder.DefaultUserID holds the default value on creation for the user_id field.
	useragentorder.DefaultUserID = useragentorderDescUserID.Default.(int64)
	// useragentorderDescAgentID is the schema descriptor for agent_id field.
	useragentorderDescAgentID := useragentorderFields[2].Descriptor()
	// useragentorder.DefaultAgentID holds the default value on creation for the agent_id field.
	useragentorder.DefaultAgentID = useragentorderDescAgentID.Default.(int64)
	// useragentorderDescOrderIndex is the schema descriptor for order_index field.
	useragentorderDescOrderIndex := useragentorderFields[3].Descriptor()
	// useragentorder.DefaultOrderIndex holds the default value on creation for the order_index field.
	useragentorder.DefaultOrderIndex = useragentorderDescOrderIndex.Default.(int64)
}

const (
	Version = "v0.13.0"                                         // Version of ent codegen.
	Sum     = "h1:DclxWczaCpyiKn6ZWVcJjq1zIKtJ11iNKy+08lNYsJE=" // Sum of ent codegen.
)

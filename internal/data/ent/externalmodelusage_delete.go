// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/externalmodelusage"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// ExternalModelUsageDelete is the builder for deleting a ExternalModelUsage entity.
type ExternalModelUsageDelete struct {
	config
	hooks    []Hook
	mutation *ExternalModelUsageMutation
}

// Where appends a list predicates to the ExternalModelUsageDelete builder.
func (emud *ExternalModelUsageDelete) Where(ps ...predicate.ExternalModelUsage) *ExternalModelUsageDelete {
	emud.mutation.Where(ps...)
	return emud
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (emud *ExternalModelUsageDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, emud.sqlExec, emud.mutation, emud.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (emud *ExternalModelUsageDelete) ExecX(ctx context.Context) int {
	n, err := emud.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (emud *ExternalModelUsageDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(externalmodelusage.Table, sqlgraph.NewFieldSpec(externalmodelusage.FieldID, field.TypeInt64))
	if ps := emud.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, emud.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	emud.mutation.done = true
	return affected, err
}

// ExternalModelUsageDeleteOne is the builder for deleting a single ExternalModelUsage entity.
type ExternalModelUsageDeleteOne struct {
	emud *ExternalModelUsageDelete
}

// Where appends a list predicates to the ExternalModelUsageDelete builder.
func (emudo *ExternalModelUsageDeleteOne) Where(ps ...predicate.ExternalModelUsage) *ExternalModelUsageDeleteOne {
	emudo.emud.mutation.Where(ps...)
	return emudo
}

// Exec executes the deletion query.
func (emudo *ExternalModelUsageDeleteOne) Exec(ctx context.Context) error {
	n, err := emudo.emud.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{externalmodelusage.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (emudo *ExternalModelUsageDeleteOne) ExecX(ctx context.Context) {
	if err := emudo.Exec(ctx); err != nil {
		panic(err)
	}
}

// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiModelQuery is the builder for querying AiModel entities.
type AiModelQuery struct {
	config
	ctx        *QueryContext
	order      []aimodel.OrderOption
	inters     []Interceptor
	predicates []predicate.AiModel
	modifiers  []func(*sql.Selector)
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AiModelQuery builder.
func (amq *AiModelQuery) Where(ps ...predicate.AiModel) *AiModelQuery {
	amq.predicates = append(amq.predicates, ps...)
	return amq
}

// Limit the number of records to be returned by this query.
func (amq *AiModelQuery) Limit(limit int) *AiModelQuery {
	amq.ctx.Limit = &limit
	return amq
}

// Offset to start from.
func (amq *AiModelQuery) Offset(offset int) *AiModelQuery {
	amq.ctx.Offset = &offset
	return amq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (amq *AiModelQuery) Unique(unique bool) *AiModelQuery {
	amq.ctx.Unique = &unique
	return amq
}

// Order specifies how the records should be ordered.
func (amq *AiModelQuery) Order(o ...aimodel.OrderOption) *AiModelQuery {
	amq.order = append(amq.order, o...)
	return amq
}

// First returns the first AiModel entity from the query.
// Returns a *NotFoundError when no AiModel was found.
func (amq *AiModelQuery) First(ctx context.Context) (*AiModel, error) {
	nodes, err := amq.Limit(1).All(setContextOp(ctx, amq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{aimodel.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (amq *AiModelQuery) FirstX(ctx context.Context) *AiModel {
	node, err := amq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AiModel ID from the query.
// Returns a *NotFoundError when no AiModel ID was found.
func (amq *AiModelQuery) FirstID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = amq.Limit(1).IDs(setContextOp(ctx, amq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{aimodel.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (amq *AiModelQuery) FirstIDX(ctx context.Context) int64 {
	id, err := amq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AiModel entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AiModel entity is found.
// Returns a *NotFoundError when no AiModel entities are found.
func (amq *AiModelQuery) Only(ctx context.Context) (*AiModel, error) {
	nodes, err := amq.Limit(2).All(setContextOp(ctx, amq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{aimodel.Label}
	default:
		return nil, &NotSingularError{aimodel.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (amq *AiModelQuery) OnlyX(ctx context.Context) *AiModel {
	node, err := amq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AiModel ID in the query.
// Returns a *NotSingularError when more than one AiModel ID is found.
// Returns a *NotFoundError when no entities are found.
func (amq *AiModelQuery) OnlyID(ctx context.Context) (id int64, err error) {
	var ids []int64
	if ids, err = amq.Limit(2).IDs(setContextOp(ctx, amq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{aimodel.Label}
	default:
		err = &NotSingularError{aimodel.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (amq *AiModelQuery) OnlyIDX(ctx context.Context) int64 {
	id, err := amq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AiModels.
func (amq *AiModelQuery) All(ctx context.Context) ([]*AiModel, error) {
	ctx = setContextOp(ctx, amq.ctx, "All")
	if err := amq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AiModel, *AiModelQuery]()
	return withInterceptors[[]*AiModel](ctx, amq, qr, amq.inters)
}

// AllX is like All, but panics if an error occurs.
func (amq *AiModelQuery) AllX(ctx context.Context) []*AiModel {
	nodes, err := amq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AiModel IDs.
func (amq *AiModelQuery) IDs(ctx context.Context) (ids []int64, err error) {
	if amq.ctx.Unique == nil && amq.path != nil {
		amq.Unique(true)
	}
	ctx = setContextOp(ctx, amq.ctx, "IDs")
	if err = amq.Select(aimodel.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (amq *AiModelQuery) IDsX(ctx context.Context) []int64 {
	ids, err := amq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (amq *AiModelQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, amq.ctx, "Count")
	if err := amq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, amq, querierCount[*AiModelQuery](), amq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (amq *AiModelQuery) CountX(ctx context.Context) int {
	count, err := amq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (amq *AiModelQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, amq.ctx, "Exist")
	switch _, err := amq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (amq *AiModelQuery) ExistX(ctx context.Context) bool {
	exist, err := amq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AiModelQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (amq *AiModelQuery) Clone() *AiModelQuery {
	if amq == nil {
		return nil
	}
	return &AiModelQuery{
		config:     amq.config,
		ctx:        amq.ctx.Clone(),
		order:      append([]aimodel.OrderOption{}, amq.order...),
		inters:     append([]Interceptor{}, amq.inters...),
		predicates: append([]predicate.AiModel{}, amq.predicates...),
		// clone intermediate query.
		sql:  amq.sql.Clone(),
		path: amq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AiModel.Query().
//		GroupBy(aimodel.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (amq *AiModelQuery) GroupBy(field string, fields ...string) *AiModelGroupBy {
	amq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AiModelGroupBy{build: amq}
	grbuild.flds = &amq.ctx.Fields
	grbuild.label = aimodel.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.AiModel.Query().
//		Select(aimodel.FieldCreatedAt).
//		Scan(ctx, &v)
func (amq *AiModelQuery) Select(fields ...string) *AiModelSelect {
	amq.ctx.Fields = append(amq.ctx.Fields, fields...)
	sbuild := &AiModelSelect{AiModelQuery: amq}
	sbuild.label = aimodel.Label
	sbuild.flds, sbuild.scan = &amq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AiModelSelect configured with the given aggregations.
func (amq *AiModelQuery) Aggregate(fns ...AggregateFunc) *AiModelSelect {
	return amq.Select().Aggregate(fns...)
}

func (amq *AiModelQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range amq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, amq); err != nil {
				return err
			}
		}
	}
	for _, f := range amq.ctx.Fields {
		if !aimodel.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if amq.path != nil {
		prev, err := amq.path(ctx)
		if err != nil {
			return err
		}
		amq.sql = prev
	}
	return nil
}

func (amq *AiModelQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AiModel, error) {
	var (
		nodes = []*AiModel{}
		_spec = amq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AiModel).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AiModel{config: amq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	if len(amq.modifiers) > 0 {
		_spec.Modifiers = amq.modifiers
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, amq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (amq *AiModelQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := amq.querySpec()
	if len(amq.modifiers) > 0 {
		_spec.Modifiers = amq.modifiers
	}
	_spec.Node.Columns = amq.ctx.Fields
	if len(amq.ctx.Fields) > 0 {
		_spec.Unique = amq.ctx.Unique != nil && *amq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, amq.driver, _spec)
}

func (amq *AiModelQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(aimodel.Table, aimodel.Columns, sqlgraph.NewFieldSpec(aimodel.FieldID, field.TypeInt64))
	_spec.From = amq.sql
	if unique := amq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if amq.path != nil {
		_spec.Unique = true
	}
	if fields := amq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aimodel.FieldID)
		for i := range fields {
			if fields[i] != aimodel.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := amq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := amq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := amq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := amq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (amq *AiModelQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(amq.driver.Dialect())
	t1 := builder.Table(aimodel.Table)
	columns := amq.ctx.Fields
	if len(columns) == 0 {
		columns = aimodel.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if amq.sql != nil {
		selector = amq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if amq.ctx.Unique != nil && *amq.ctx.Unique {
		selector.Distinct()
	}
	for _, m := range amq.modifiers {
		m(selector)
	}
	for _, p := range amq.predicates {
		p(selector)
	}
	for _, p := range amq.order {
		p(selector)
	}
	if offset := amq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := amq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ForUpdate locks the selected rows against concurrent updates, and prevent them from being
// updated, deleted or "selected ... for update" by other sessions, until the transaction is
// either committed or rolled-back.
func (amq *AiModelQuery) ForUpdate(opts ...sql.LockOption) *AiModelQuery {
	if amq.driver.Dialect() == dialect.Postgres {
		amq.Unique(false)
	}
	amq.modifiers = append(amq.modifiers, func(s *sql.Selector) {
		s.ForUpdate(opts...)
	})
	return amq
}

// ForShare behaves similarly to ForUpdate, except that it acquires a shared mode lock
// on any rows that are read. Other sessions can read the rows, but cannot modify them
// until your transaction commits.
func (amq *AiModelQuery) ForShare(opts ...sql.LockOption) *AiModelQuery {
	if amq.driver.Dialect() == dialect.Postgres {
		amq.Unique(false)
	}
	amq.modifiers = append(amq.modifiers, func(s *sql.Selector) {
		s.ForShare(opts...)
	})
	return amq
}

// Modify adds a query modifier for attaching custom logic to queries.
func (amq *AiModelQuery) Modify(modifiers ...func(s *sql.Selector)) *AiModelSelect {
	amq.modifiers = append(amq.modifiers, modifiers...)
	return amq.Select()
}

// AiModelGroupBy is the group-by builder for AiModel entities.
type AiModelGroupBy struct {
	selector
	build *AiModelQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (amgb *AiModelGroupBy) Aggregate(fns ...AggregateFunc) *AiModelGroupBy {
	amgb.fns = append(amgb.fns, fns...)
	return amgb
}

// Scan applies the selector query and scans the result into the given value.
func (amgb *AiModelGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, amgb.build.ctx, "GroupBy")
	if err := amgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiModelQuery, *AiModelGroupBy](ctx, amgb.build, amgb, amgb.build.inters, v)
}

func (amgb *AiModelGroupBy) sqlScan(ctx context.Context, root *AiModelQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(amgb.fns))
	for _, fn := range amgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*amgb.flds)+len(amgb.fns))
		for _, f := range *amgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*amgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := amgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AiModelSelect is the builder for selecting fields of AiModel entities.
type AiModelSelect struct {
	*AiModelQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ams *AiModelSelect) Aggregate(fns ...AggregateFunc) *AiModelSelect {
	ams.fns = append(ams.fns, fns...)
	return ams
}

// Scan applies the selector query and scans the result into the given value.
func (ams *AiModelSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ams.ctx, "Select")
	if err := ams.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiModelQuery, *AiModelSelect](ctx, ams.AiModelQuery, ams, ams.inters, v)
}

func (ams *AiModelSelect) sqlScan(ctx context.Context, root *AiModelQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ams.fns))
	for _, fn := range ams.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ams.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ams.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// Modify adds a query modifier for attaching custom logic to queries.
func (ams *AiModelSelect) Modify(modifiers ...func(s *sql.Selector)) *AiModelSelect {
	ams.modifiers = append(ams.modifiers, modifiers...)
	return ams
}

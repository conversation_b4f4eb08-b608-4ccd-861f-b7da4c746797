// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

// AiAgentDelete is the builder for deleting a AiAgent entity.
type AiAgentDelete struct {
	config
	hooks    []Hook
	mutation *AiAgentMutation
}

// Where appends a list predicates to the AiAgentDelete builder.
func (aad *AiAgentDelete) Where(ps ...predicate.AiAgent) *AiAgentDelete {
	aad.mutation.Where(ps...)
	return aad
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (aad *AiAgentDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, aad.sqlExec, aad.mutation, aad.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (aad *AiAgentDelete) ExecX(ctx context.Context) int {
	n, err := aad.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (aad *AiAgentDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(aiagent.Table, sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt64))
	if ps := aad.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, aad.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	aad.mutation.done = true
	return affected, err
}

// AiAgentDeleteOne is the builder for deleting a single AiAgent entity.
type AiAgentDeleteOne struct {
	aad *AiAgentDelete
}

// Where appends a list predicates to the AiAgentDelete builder.
func (aado *AiAgentDeleteOne) Where(ps ...predicate.AiAgent) *AiAgentDeleteOne {
	aado.aad.mutation.Where(ps...)
	return aado
}

// Exec executes the deletion query.
func (aado *AiAgentDeleteOne) Exec(ctx context.Context) error {
	n, err := aado.aad.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{aiagent.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (aado *AiAgentDeleteOne) ExecX(ctx context.Context) {
	if err := aado.Exec(ctx); err != nil {
		panic(err)
	}
}

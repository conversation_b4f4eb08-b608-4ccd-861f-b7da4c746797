// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/classificationfiles"
)

// ClassificationFilesCreate is the builder for creating a ClassificationFiles entity.
type ClassificationFilesCreate struct {
	config
	mutation *ClassificationFilesMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetName sets the "name" field.
func (cfc *ClassificationFilesCreate) SetName(s string) *ClassificationFilesCreate {
	cfc.mutation.SetName(s)
	return cfc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableName(s *string) *ClassificationFilesCreate {
	if s != nil {
		cfc.SetName(*s)
	}
	return cfc
}

// SetFileRelationID sets the "file_relation_id" field.
func (cfc *ClassificationFilesCreate) SetFileRelationID(i int64) *ClassificationFilesCreate {
	cfc.mutation.SetFileRelationID(i)
	return cfc
}

// SetNillableFileRelationID sets the "file_relation_id" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableFileRelationID(i *int64) *ClassificationFilesCreate {
	if i != nil {
		cfc.SetFileRelationID(*i)
	}
	return cfc
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (cfc *ClassificationFilesCreate) SetPreEntityTag(s string) *ClassificationFilesCreate {
	cfc.mutation.SetPreEntityTag(s)
	return cfc
}

// SetNillablePreEntityTag sets the "pre_entity_tag" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillablePreEntityTag(s *string) *ClassificationFilesCreate {
	if s != nil {
		cfc.SetPreEntityTag(*s)
	}
	return cfc
}

// SetEntityTag sets the "entity_tag" field.
func (cfc *ClassificationFilesCreate) SetEntityTag(s string) *ClassificationFilesCreate {
	cfc.mutation.SetEntityTag(s)
	return cfc
}

// SetNillableEntityTag sets the "entity_tag" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableEntityTag(s *string) *ClassificationFilesCreate {
	if s != nil {
		cfc.SetEntityTag(*s)
	}
	return cfc
}

// SetFilename sets the "filename" field.
func (cfc *ClassificationFilesCreate) SetFilename(s string) *ClassificationFilesCreate {
	cfc.mutation.SetFilename(s)
	return cfc
}

// SetNillableFilename sets the "filename" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableFilename(s *string) *ClassificationFilesCreate {
	if s != nil {
		cfc.SetFilename(*s)
	}
	return cfc
}

// SetMimeType sets the "mime_type" field.
func (cfc *ClassificationFilesCreate) SetMimeType(s string) *ClassificationFilesCreate {
	cfc.mutation.SetMimeType(s)
	return cfc
}

// SetNillableMimeType sets the "mime_type" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableMimeType(s *string) *ClassificationFilesCreate {
	if s != nil {
		cfc.SetMimeType(*s)
	}
	return cfc
}

// SetUserID sets the "user_id" field.
func (cfc *ClassificationFilesCreate) SetUserID(i int64) *ClassificationFilesCreate {
	cfc.mutation.SetUserID(i)
	return cfc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableUserID(i *int64) *ClassificationFilesCreate {
	if i != nil {
		cfc.SetUserID(*i)
	}
	return cfc
}

// SetUserName sets the "user_name" field.
func (cfc *ClassificationFilesCreate) SetUserName(s string) *ClassificationFilesCreate {
	cfc.mutation.SetUserName(s)
	return cfc
}

// SetNillableUserName sets the "user_name" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableUserName(s *string) *ClassificationFilesCreate {
	if s != nil {
		cfc.SetUserName(*s)
	}
	return cfc
}

// SetDeptIds sets the "dept_ids" field.
func (cfc *ClassificationFilesCreate) SetDeptIds(pq *pq.Int64Array) *ClassificationFilesCreate {
	cfc.mutation.SetDeptIds(pq)
	return cfc
}

// SetDeptName sets the "dept_name" field.
func (cfc *ClassificationFilesCreate) SetDeptName(s string) *ClassificationFilesCreate {
	cfc.mutation.SetDeptName(s)
	return cfc
}

// SetNillableDeptName sets the "dept_name" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableDeptName(s *string) *ClassificationFilesCreate {
	if s != nil {
		cfc.SetDeptName(*s)
	}
	return cfc
}

// SetPath sets the "path" field.
func (cfc *ClassificationFilesCreate) SetPath(s string) *ClassificationFilesCreate {
	cfc.mutation.SetPath(s)
	return cfc
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillablePath(s *string) *ClassificationFilesCreate {
	if s != nil {
		cfc.SetPath(*s)
	}
	return cfc
}

// SetSecurityLevel sets the "security_level" field.
func (cfc *ClassificationFilesCreate) SetSecurityLevel(i int32) *ClassificationFilesCreate {
	cfc.mutation.SetSecurityLevel(i)
	return cfc
}

// SetNillableSecurityLevel sets the "security_level" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableSecurityLevel(i *int32) *ClassificationFilesCreate {
	if i != nil {
		cfc.SetSecurityLevel(*i)
	}
	return cfc
}

// SetNodeLevel sets the "node_level" field.
func (cfc *ClassificationFilesCreate) SetNodeLevel(i int32) *ClassificationFilesCreate {
	cfc.mutation.SetNodeLevel(i)
	return cfc
}

// SetNillableNodeLevel sets the "node_level" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableNodeLevel(i *int32) *ClassificationFilesCreate {
	if i != nil {
		cfc.SetNodeLevel(*i)
	}
	return cfc
}

// SetCheckStatus sets the "check_status" field.
func (cfc *ClassificationFilesCreate) SetCheckStatus(i int32) *ClassificationFilesCreate {
	cfc.mutation.SetCheckStatus(i)
	return cfc
}

// SetNillableCheckStatus sets the "check_status" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableCheckStatus(i *int32) *ClassificationFilesCreate {
	if i != nil {
		cfc.SetCheckStatus(*i)
	}
	return cfc
}

// SetTreeType sets the "tree_type" field.
func (cfc *ClassificationFilesCreate) SetTreeType(i int32) *ClassificationFilesCreate {
	cfc.mutation.SetTreeType(i)
	return cfc
}

// SetNillableTreeType sets the "tree_type" field if the given value is not nil.
func (cfc *ClassificationFilesCreate) SetNillableTreeType(i *int32) *ClassificationFilesCreate {
	if i != nil {
		cfc.SetTreeType(*i)
	}
	return cfc
}

// SetID sets the "id" field.
func (cfc *ClassificationFilesCreate) SetID(i int64) *ClassificationFilesCreate {
	cfc.mutation.SetID(i)
	return cfc
}

// Mutation returns the ClassificationFilesMutation object of the builder.
func (cfc *ClassificationFilesCreate) Mutation() *ClassificationFilesMutation {
	return cfc.mutation
}

// Save creates the ClassificationFiles in the database.
func (cfc *ClassificationFilesCreate) Save(ctx context.Context) (*ClassificationFiles, error) {
	cfc.defaults()
	return withHooks(ctx, cfc.sqlSave, cfc.mutation, cfc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (cfc *ClassificationFilesCreate) SaveX(ctx context.Context) *ClassificationFiles {
	v, err := cfc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cfc *ClassificationFilesCreate) Exec(ctx context.Context) error {
	_, err := cfc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cfc *ClassificationFilesCreate) ExecX(ctx context.Context) {
	if err := cfc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cfc *ClassificationFilesCreate) defaults() {
	if _, ok := cfc.mutation.Name(); !ok {
		v := classificationfiles.DefaultName
		cfc.mutation.SetName(v)
	}
	if _, ok := cfc.mutation.FileRelationID(); !ok {
		v := classificationfiles.DefaultFileRelationID
		cfc.mutation.SetFileRelationID(v)
	}
	if _, ok := cfc.mutation.PreEntityTag(); !ok {
		v := classificationfiles.DefaultPreEntityTag
		cfc.mutation.SetPreEntityTag(v)
	}
	if _, ok := cfc.mutation.EntityTag(); !ok {
		v := classificationfiles.DefaultEntityTag
		cfc.mutation.SetEntityTag(v)
	}
	if _, ok := cfc.mutation.Filename(); !ok {
		v := classificationfiles.DefaultFilename
		cfc.mutation.SetFilename(v)
	}
	if _, ok := cfc.mutation.MimeType(); !ok {
		v := classificationfiles.DefaultMimeType
		cfc.mutation.SetMimeType(v)
	}
	if _, ok := cfc.mutation.UserID(); !ok {
		v := classificationfiles.DefaultUserID
		cfc.mutation.SetUserID(v)
	}
	if _, ok := cfc.mutation.UserName(); !ok {
		v := classificationfiles.DefaultUserName
		cfc.mutation.SetUserName(v)
	}
	if _, ok := cfc.mutation.DeptIds(); !ok {
		v := classificationfiles.DefaultDeptIds
		cfc.mutation.SetDeptIds(v)
	}
	if _, ok := cfc.mutation.DeptName(); !ok {
		v := classificationfiles.DefaultDeptName
		cfc.mutation.SetDeptName(v)
	}
	if _, ok := cfc.mutation.Path(); !ok {
		v := classificationfiles.DefaultPath
		cfc.mutation.SetPath(v)
	}
	if _, ok := cfc.mutation.SecurityLevel(); !ok {
		v := classificationfiles.DefaultSecurityLevel
		cfc.mutation.SetSecurityLevel(v)
	}
	if _, ok := cfc.mutation.NodeLevel(); !ok {
		v := classificationfiles.DefaultNodeLevel
		cfc.mutation.SetNodeLevel(v)
	}
	if _, ok := cfc.mutation.CheckStatus(); !ok {
		v := classificationfiles.DefaultCheckStatus
		cfc.mutation.SetCheckStatus(v)
	}
	if _, ok := cfc.mutation.TreeType(); !ok {
		v := classificationfiles.DefaultTreeType
		cfc.mutation.SetTreeType(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cfc *ClassificationFilesCreate) check() error {
	if _, ok := cfc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "ClassificationFiles.name"`)}
	}
	if _, ok := cfc.mutation.FileRelationID(); !ok {
		return &ValidationError{Name: "file_relation_id", err: errors.New(`ent: missing required field "ClassificationFiles.file_relation_id"`)}
	}
	if _, ok := cfc.mutation.PreEntityTag(); !ok {
		return &ValidationError{Name: "pre_entity_tag", err: errors.New(`ent: missing required field "ClassificationFiles.pre_entity_tag"`)}
	}
	if _, ok := cfc.mutation.EntityTag(); !ok {
		return &ValidationError{Name: "entity_tag", err: errors.New(`ent: missing required field "ClassificationFiles.entity_tag"`)}
	}
	if _, ok := cfc.mutation.Filename(); !ok {
		return &ValidationError{Name: "filename", err: errors.New(`ent: missing required field "ClassificationFiles.filename"`)}
	}
	if _, ok := cfc.mutation.MimeType(); !ok {
		return &ValidationError{Name: "mime_type", err: errors.New(`ent: missing required field "ClassificationFiles.mime_type"`)}
	}
	if _, ok := cfc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "ClassificationFiles.user_id"`)}
	}
	if _, ok := cfc.mutation.UserName(); !ok {
		return &ValidationError{Name: "user_name", err: errors.New(`ent: missing required field "ClassificationFiles.user_name"`)}
	}
	if _, ok := cfc.mutation.DeptIds(); !ok {
		return &ValidationError{Name: "dept_ids", err: errors.New(`ent: missing required field "ClassificationFiles.dept_ids"`)}
	}
	if _, ok := cfc.mutation.DeptName(); !ok {
		return &ValidationError{Name: "dept_name", err: errors.New(`ent: missing required field "ClassificationFiles.dept_name"`)}
	}
	if _, ok := cfc.mutation.Path(); !ok {
		return &ValidationError{Name: "path", err: errors.New(`ent: missing required field "ClassificationFiles.path"`)}
	}
	if _, ok := cfc.mutation.SecurityLevel(); !ok {
		return &ValidationError{Name: "security_level", err: errors.New(`ent: missing required field "ClassificationFiles.security_level"`)}
	}
	if _, ok := cfc.mutation.NodeLevel(); !ok {
		return &ValidationError{Name: "node_level", err: errors.New(`ent: missing required field "ClassificationFiles.node_level"`)}
	}
	if _, ok := cfc.mutation.CheckStatus(); !ok {
		return &ValidationError{Name: "check_status", err: errors.New(`ent: missing required field "ClassificationFiles.check_status"`)}
	}
	if _, ok := cfc.mutation.TreeType(); !ok {
		return &ValidationError{Name: "tree_type", err: errors.New(`ent: missing required field "ClassificationFiles.tree_type"`)}
	}
	return nil
}

func (cfc *ClassificationFilesCreate) sqlSave(ctx context.Context) (*ClassificationFiles, error) {
	if err := cfc.check(); err != nil {
		return nil, err
	}
	_node, _spec := cfc.createSpec()
	if err := sqlgraph.CreateNode(ctx, cfc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != _node.ID {
		id := _spec.ID.Value.(int64)
		_node.ID = int64(id)
	}
	cfc.mutation.id = &_node.ID
	cfc.mutation.done = true
	return _node, nil
}

func (cfc *ClassificationFilesCreate) createSpec() (*ClassificationFiles, *sqlgraph.CreateSpec) {
	var (
		_node = &ClassificationFiles{config: cfc.config}
		_spec = sqlgraph.NewCreateSpec(classificationfiles.Table, sqlgraph.NewFieldSpec(classificationfiles.FieldID, field.TypeInt64))
	)
	_spec.OnConflict = cfc.conflict
	if id, ok := cfc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := cfc.mutation.Name(); ok {
		_spec.SetField(classificationfiles.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := cfc.mutation.FileRelationID(); ok {
		_spec.SetField(classificationfiles.FieldFileRelationID, field.TypeInt64, value)
		_node.FileRelationID = value
	}
	if value, ok := cfc.mutation.PreEntityTag(); ok {
		_spec.SetField(classificationfiles.FieldPreEntityTag, field.TypeString, value)
		_node.PreEntityTag = value
	}
	if value, ok := cfc.mutation.EntityTag(); ok {
		_spec.SetField(classificationfiles.FieldEntityTag, field.TypeString, value)
		_node.EntityTag = value
	}
	if value, ok := cfc.mutation.Filename(); ok {
		_spec.SetField(classificationfiles.FieldFilename, field.TypeString, value)
		_node.Filename = value
	}
	if value, ok := cfc.mutation.MimeType(); ok {
		_spec.SetField(classificationfiles.FieldMimeType, field.TypeString, value)
		_node.MimeType = value
	}
	if value, ok := cfc.mutation.UserID(); ok {
		_spec.SetField(classificationfiles.FieldUserID, field.TypeInt64, value)
		_node.UserID = value
	}
	if value, ok := cfc.mutation.UserName(); ok {
		_spec.SetField(classificationfiles.FieldUserName, field.TypeString, value)
		_node.UserName = value
	}
	if value, ok := cfc.mutation.DeptIds(); ok {
		_spec.SetField(classificationfiles.FieldDeptIds, field.TypeOther, value)
		_node.DeptIds = value
	}
	if value, ok := cfc.mutation.DeptName(); ok {
		_spec.SetField(classificationfiles.FieldDeptName, field.TypeString, value)
		_node.DeptName = value
	}
	if value, ok := cfc.mutation.Path(); ok {
		_spec.SetField(classificationfiles.FieldPath, field.TypeString, value)
		_node.Path = value
	}
	if value, ok := cfc.mutation.SecurityLevel(); ok {
		_spec.SetField(classificationfiles.FieldSecurityLevel, field.TypeInt32, value)
		_node.SecurityLevel = value
	}
	if value, ok := cfc.mutation.NodeLevel(); ok {
		_spec.SetField(classificationfiles.FieldNodeLevel, field.TypeInt32, value)
		_node.NodeLevel = value
	}
	if value, ok := cfc.mutation.CheckStatus(); ok {
		_spec.SetField(classificationfiles.FieldCheckStatus, field.TypeInt32, value)
		_node.CheckStatus = value
	}
	if value, ok := cfc.mutation.TreeType(); ok {
		_spec.SetField(classificationfiles.FieldTreeType, field.TypeInt32, value)
		_node.TreeType = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ClassificationFiles.Create().
//		SetName(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ClassificationFilesUpsert) {
//			SetName(v+v).
//		}).
//		Exec(ctx)
func (cfc *ClassificationFilesCreate) OnConflict(opts ...sql.ConflictOption) *ClassificationFilesUpsertOne {
	cfc.conflict = opts
	return &ClassificationFilesUpsertOne{
		create: cfc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ClassificationFiles.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (cfc *ClassificationFilesCreate) OnConflictColumns(columns ...string) *ClassificationFilesUpsertOne {
	cfc.conflict = append(cfc.conflict, sql.ConflictColumns(columns...))
	return &ClassificationFilesUpsertOne{
		create: cfc,
	}
}

type (
	// ClassificationFilesUpsertOne is the builder for "upsert"-ing
	//  one ClassificationFiles node.
	ClassificationFilesUpsertOne struct {
		create *ClassificationFilesCreate
	}

	// ClassificationFilesUpsert is the "OnConflict" setter.
	ClassificationFilesUpsert struct {
		*sql.UpdateSet
	}
)

// SetName sets the "name" field.
func (u *ClassificationFilesUpsert) SetName(v string) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateName() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldName)
	return u
}

// SetFileRelationID sets the "file_relation_id" field.
func (u *ClassificationFilesUpsert) SetFileRelationID(v int64) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldFileRelationID, v)
	return u
}

// UpdateFileRelationID sets the "file_relation_id" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateFileRelationID() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldFileRelationID)
	return u
}

// AddFileRelationID adds v to the "file_relation_id" field.
func (u *ClassificationFilesUpsert) AddFileRelationID(v int64) *ClassificationFilesUpsert {
	u.Add(classificationfiles.FieldFileRelationID, v)
	return u
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (u *ClassificationFilesUpsert) SetPreEntityTag(v string) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldPreEntityTag, v)
	return u
}

// UpdatePreEntityTag sets the "pre_entity_tag" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdatePreEntityTag() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldPreEntityTag)
	return u
}

// SetEntityTag sets the "entity_tag" field.
func (u *ClassificationFilesUpsert) SetEntityTag(v string) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldEntityTag, v)
	return u
}

// UpdateEntityTag sets the "entity_tag" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateEntityTag() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldEntityTag)
	return u
}

// SetFilename sets the "filename" field.
func (u *ClassificationFilesUpsert) SetFilename(v string) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldFilename, v)
	return u
}

// UpdateFilename sets the "filename" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateFilename() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldFilename)
	return u
}

// SetMimeType sets the "mime_type" field.
func (u *ClassificationFilesUpsert) SetMimeType(v string) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldMimeType, v)
	return u
}

// UpdateMimeType sets the "mime_type" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateMimeType() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldMimeType)
	return u
}

// SetUserID sets the "user_id" field.
func (u *ClassificationFilesUpsert) SetUserID(v int64) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateUserID() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldUserID)
	return u
}

// AddUserID adds v to the "user_id" field.
func (u *ClassificationFilesUpsert) AddUserID(v int64) *ClassificationFilesUpsert {
	u.Add(classificationfiles.FieldUserID, v)
	return u
}

// SetUserName sets the "user_name" field.
func (u *ClassificationFilesUpsert) SetUserName(v string) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldUserName, v)
	return u
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateUserName() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldUserName)
	return u
}

// SetDeptIds sets the "dept_ids" field.
func (u *ClassificationFilesUpsert) SetDeptIds(v *pq.Int64Array) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldDeptIds, v)
	return u
}

// UpdateDeptIds sets the "dept_ids" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateDeptIds() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldDeptIds)
	return u
}

// SetDeptName sets the "dept_name" field.
func (u *ClassificationFilesUpsert) SetDeptName(v string) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldDeptName, v)
	return u
}

// UpdateDeptName sets the "dept_name" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateDeptName() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldDeptName)
	return u
}

// SetPath sets the "path" field.
func (u *ClassificationFilesUpsert) SetPath(v string) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldPath, v)
	return u
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdatePath() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldPath)
	return u
}

// SetSecurityLevel sets the "security_level" field.
func (u *ClassificationFilesUpsert) SetSecurityLevel(v int32) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldSecurityLevel, v)
	return u
}

// UpdateSecurityLevel sets the "security_level" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateSecurityLevel() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldSecurityLevel)
	return u
}

// AddSecurityLevel adds v to the "security_level" field.
func (u *ClassificationFilesUpsert) AddSecurityLevel(v int32) *ClassificationFilesUpsert {
	u.Add(classificationfiles.FieldSecurityLevel, v)
	return u
}

// SetNodeLevel sets the "node_level" field.
func (u *ClassificationFilesUpsert) SetNodeLevel(v int32) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldNodeLevel, v)
	return u
}

// UpdateNodeLevel sets the "node_level" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateNodeLevel() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldNodeLevel)
	return u
}

// AddNodeLevel adds v to the "node_level" field.
func (u *ClassificationFilesUpsert) AddNodeLevel(v int32) *ClassificationFilesUpsert {
	u.Add(classificationfiles.FieldNodeLevel, v)
	return u
}

// SetCheckStatus sets the "check_status" field.
func (u *ClassificationFilesUpsert) SetCheckStatus(v int32) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldCheckStatus, v)
	return u
}

// UpdateCheckStatus sets the "check_status" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateCheckStatus() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldCheckStatus)
	return u
}

// AddCheckStatus adds v to the "check_status" field.
func (u *ClassificationFilesUpsert) AddCheckStatus(v int32) *ClassificationFilesUpsert {
	u.Add(classificationfiles.FieldCheckStatus, v)
	return u
}

// SetTreeType sets the "tree_type" field.
func (u *ClassificationFilesUpsert) SetTreeType(v int32) *ClassificationFilesUpsert {
	u.Set(classificationfiles.FieldTreeType, v)
	return u
}

// UpdateTreeType sets the "tree_type" field to the value that was provided on create.
func (u *ClassificationFilesUpsert) UpdateTreeType() *ClassificationFilesUpsert {
	u.SetExcluded(classificationfiles.FieldTreeType)
	return u
}

// AddTreeType adds v to the "tree_type" field.
func (u *ClassificationFilesUpsert) AddTreeType(v int32) *ClassificationFilesUpsert {
	u.Add(classificationfiles.FieldTreeType, v)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.ClassificationFiles.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(classificationfiles.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ClassificationFilesUpsertOne) UpdateNewValues() *ClassificationFilesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(classificationfiles.FieldID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ClassificationFiles.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ClassificationFilesUpsertOne) Ignore() *ClassificationFilesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ClassificationFilesUpsertOne) DoNothing() *ClassificationFilesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ClassificationFilesCreate.OnConflict
// documentation for more info.
func (u *ClassificationFilesUpsertOne) Update(set func(*ClassificationFilesUpsert)) *ClassificationFilesUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ClassificationFilesUpsert{UpdateSet: update})
	}))
	return u
}

// SetName sets the "name" field.
func (u *ClassificationFilesUpsertOne) SetName(v string) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateName() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateName()
	})
}

// SetFileRelationID sets the "file_relation_id" field.
func (u *ClassificationFilesUpsertOne) SetFileRelationID(v int64) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetFileRelationID(v)
	})
}

// AddFileRelationID adds v to the "file_relation_id" field.
func (u *ClassificationFilesUpsertOne) AddFileRelationID(v int64) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddFileRelationID(v)
	})
}

// UpdateFileRelationID sets the "file_relation_id" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateFileRelationID() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateFileRelationID()
	})
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (u *ClassificationFilesUpsertOne) SetPreEntityTag(v string) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetPreEntityTag(v)
	})
}

// UpdatePreEntityTag sets the "pre_entity_tag" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdatePreEntityTag() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdatePreEntityTag()
	})
}

// SetEntityTag sets the "entity_tag" field.
func (u *ClassificationFilesUpsertOne) SetEntityTag(v string) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetEntityTag(v)
	})
}

// UpdateEntityTag sets the "entity_tag" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateEntityTag() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateEntityTag()
	})
}

// SetFilename sets the "filename" field.
func (u *ClassificationFilesUpsertOne) SetFilename(v string) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetFilename(v)
	})
}

// UpdateFilename sets the "filename" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateFilename() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateFilename()
	})
}

// SetMimeType sets the "mime_type" field.
func (u *ClassificationFilesUpsertOne) SetMimeType(v string) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetMimeType(v)
	})
}

// UpdateMimeType sets the "mime_type" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateMimeType() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateMimeType()
	})
}

// SetUserID sets the "user_id" field.
func (u *ClassificationFilesUpsertOne) SetUserID(v int64) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *ClassificationFilesUpsertOne) AddUserID(v int64) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateUserID() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateUserID()
	})
}

// SetUserName sets the "user_name" field.
func (u *ClassificationFilesUpsertOne) SetUserName(v string) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetUserName(v)
	})
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateUserName() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateUserName()
	})
}

// SetDeptIds sets the "dept_ids" field.
func (u *ClassificationFilesUpsertOne) SetDeptIds(v *pq.Int64Array) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetDeptIds(v)
	})
}

// UpdateDeptIds sets the "dept_ids" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateDeptIds() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateDeptIds()
	})
}

// SetDeptName sets the "dept_name" field.
func (u *ClassificationFilesUpsertOne) SetDeptName(v string) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetDeptName(v)
	})
}

// UpdateDeptName sets the "dept_name" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateDeptName() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateDeptName()
	})
}

// SetPath sets the "path" field.
func (u *ClassificationFilesUpsertOne) SetPath(v string) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdatePath() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdatePath()
	})
}

// SetSecurityLevel sets the "security_level" field.
func (u *ClassificationFilesUpsertOne) SetSecurityLevel(v int32) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetSecurityLevel(v)
	})
}

// AddSecurityLevel adds v to the "security_level" field.
func (u *ClassificationFilesUpsertOne) AddSecurityLevel(v int32) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddSecurityLevel(v)
	})
}

// UpdateSecurityLevel sets the "security_level" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateSecurityLevel() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateSecurityLevel()
	})
}

// SetNodeLevel sets the "node_level" field.
func (u *ClassificationFilesUpsertOne) SetNodeLevel(v int32) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetNodeLevel(v)
	})
}

// AddNodeLevel adds v to the "node_level" field.
func (u *ClassificationFilesUpsertOne) AddNodeLevel(v int32) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddNodeLevel(v)
	})
}

// UpdateNodeLevel sets the "node_level" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateNodeLevel() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateNodeLevel()
	})
}

// SetCheckStatus sets the "check_status" field.
func (u *ClassificationFilesUpsertOne) SetCheckStatus(v int32) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetCheckStatus(v)
	})
}

// AddCheckStatus adds v to the "check_status" field.
func (u *ClassificationFilesUpsertOne) AddCheckStatus(v int32) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddCheckStatus(v)
	})
}

// UpdateCheckStatus sets the "check_status" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateCheckStatus() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateCheckStatus()
	})
}

// SetTreeType sets the "tree_type" field.
func (u *ClassificationFilesUpsertOne) SetTreeType(v int32) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetTreeType(v)
	})
}

// AddTreeType adds v to the "tree_type" field.
func (u *ClassificationFilesUpsertOne) AddTreeType(v int32) *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddTreeType(v)
	})
}

// UpdateTreeType sets the "tree_type" field to the value that was provided on create.
func (u *ClassificationFilesUpsertOne) UpdateTreeType() *ClassificationFilesUpsertOne {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateTreeType()
	})
}

// Exec executes the query.
func (u *ClassificationFilesUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ClassificationFilesCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ClassificationFilesUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ClassificationFilesUpsertOne) ID(ctx context.Context) (id int64, err error) {
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ClassificationFilesUpsertOne) IDX(ctx context.Context) int64 {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ClassificationFilesCreateBulk is the builder for creating many ClassificationFiles entities in bulk.
type ClassificationFilesCreateBulk struct {
	config
	err      error
	builders []*ClassificationFilesCreate
	conflict []sql.ConflictOption
}

// Save creates the ClassificationFiles entities in the database.
func (cfcb *ClassificationFilesCreateBulk) Save(ctx context.Context) ([]*ClassificationFiles, error) {
	if cfcb.err != nil {
		return nil, cfcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(cfcb.builders))
	nodes := make([]*ClassificationFiles, len(cfcb.builders))
	mutators := make([]Mutator, len(cfcb.builders))
	for i := range cfcb.builders {
		func(i int, root context.Context) {
			builder := cfcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ClassificationFilesMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, cfcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = cfcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, cfcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil && nodes[i].ID == 0 {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int64(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, cfcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (cfcb *ClassificationFilesCreateBulk) SaveX(ctx context.Context) []*ClassificationFiles {
	v, err := cfcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cfcb *ClassificationFilesCreateBulk) Exec(ctx context.Context) error {
	_, err := cfcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cfcb *ClassificationFilesCreateBulk) ExecX(ctx context.Context) {
	if err := cfcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.ClassificationFiles.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ClassificationFilesUpsert) {
//			SetName(v+v).
//		}).
//		Exec(ctx)
func (cfcb *ClassificationFilesCreateBulk) OnConflict(opts ...sql.ConflictOption) *ClassificationFilesUpsertBulk {
	cfcb.conflict = opts
	return &ClassificationFilesUpsertBulk{
		create: cfcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.ClassificationFiles.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (cfcb *ClassificationFilesCreateBulk) OnConflictColumns(columns ...string) *ClassificationFilesUpsertBulk {
	cfcb.conflict = append(cfcb.conflict, sql.ConflictColumns(columns...))
	return &ClassificationFilesUpsertBulk{
		create: cfcb,
	}
}

// ClassificationFilesUpsertBulk is the builder for "upsert"-ing
// a bulk of ClassificationFiles nodes.
type ClassificationFilesUpsertBulk struct {
	create *ClassificationFilesCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.ClassificationFiles.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(classificationfiles.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ClassificationFilesUpsertBulk) UpdateNewValues() *ClassificationFilesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(classificationfiles.FieldID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.ClassificationFiles.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ClassificationFilesUpsertBulk) Ignore() *ClassificationFilesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ClassificationFilesUpsertBulk) DoNothing() *ClassificationFilesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ClassificationFilesCreateBulk.OnConflict
// documentation for more info.
func (u *ClassificationFilesUpsertBulk) Update(set func(*ClassificationFilesUpsert)) *ClassificationFilesUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ClassificationFilesUpsert{UpdateSet: update})
	}))
	return u
}

// SetName sets the "name" field.
func (u *ClassificationFilesUpsertBulk) SetName(v string) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateName() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateName()
	})
}

// SetFileRelationID sets the "file_relation_id" field.
func (u *ClassificationFilesUpsertBulk) SetFileRelationID(v int64) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetFileRelationID(v)
	})
}

// AddFileRelationID adds v to the "file_relation_id" field.
func (u *ClassificationFilesUpsertBulk) AddFileRelationID(v int64) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddFileRelationID(v)
	})
}

// UpdateFileRelationID sets the "file_relation_id" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateFileRelationID() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateFileRelationID()
	})
}

// SetPreEntityTag sets the "pre_entity_tag" field.
func (u *ClassificationFilesUpsertBulk) SetPreEntityTag(v string) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetPreEntityTag(v)
	})
}

// UpdatePreEntityTag sets the "pre_entity_tag" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdatePreEntityTag() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdatePreEntityTag()
	})
}

// SetEntityTag sets the "entity_tag" field.
func (u *ClassificationFilesUpsertBulk) SetEntityTag(v string) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetEntityTag(v)
	})
}

// UpdateEntityTag sets the "entity_tag" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateEntityTag() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateEntityTag()
	})
}

// SetFilename sets the "filename" field.
func (u *ClassificationFilesUpsertBulk) SetFilename(v string) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetFilename(v)
	})
}

// UpdateFilename sets the "filename" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateFilename() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateFilename()
	})
}

// SetMimeType sets the "mime_type" field.
func (u *ClassificationFilesUpsertBulk) SetMimeType(v string) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetMimeType(v)
	})
}

// UpdateMimeType sets the "mime_type" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateMimeType() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateMimeType()
	})
}

// SetUserID sets the "user_id" field.
func (u *ClassificationFilesUpsertBulk) SetUserID(v int64) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetUserID(v)
	})
}

// AddUserID adds v to the "user_id" field.
func (u *ClassificationFilesUpsertBulk) AddUserID(v int64) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateUserID() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateUserID()
	})
}

// SetUserName sets the "user_name" field.
func (u *ClassificationFilesUpsertBulk) SetUserName(v string) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetUserName(v)
	})
}

// UpdateUserName sets the "user_name" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateUserName() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateUserName()
	})
}

// SetDeptIds sets the "dept_ids" field.
func (u *ClassificationFilesUpsertBulk) SetDeptIds(v *pq.Int64Array) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetDeptIds(v)
	})
}

// UpdateDeptIds sets the "dept_ids" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateDeptIds() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateDeptIds()
	})
}

// SetDeptName sets the "dept_name" field.
func (u *ClassificationFilesUpsertBulk) SetDeptName(v string) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetDeptName(v)
	})
}

// UpdateDeptName sets the "dept_name" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateDeptName() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateDeptName()
	})
}

// SetPath sets the "path" field.
func (u *ClassificationFilesUpsertBulk) SetPath(v string) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdatePath() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdatePath()
	})
}

// SetSecurityLevel sets the "security_level" field.
func (u *ClassificationFilesUpsertBulk) SetSecurityLevel(v int32) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetSecurityLevel(v)
	})
}

// AddSecurityLevel adds v to the "security_level" field.
func (u *ClassificationFilesUpsertBulk) AddSecurityLevel(v int32) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddSecurityLevel(v)
	})
}

// UpdateSecurityLevel sets the "security_level" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateSecurityLevel() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateSecurityLevel()
	})
}

// SetNodeLevel sets the "node_level" field.
func (u *ClassificationFilesUpsertBulk) SetNodeLevel(v int32) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetNodeLevel(v)
	})
}

// AddNodeLevel adds v to the "node_level" field.
func (u *ClassificationFilesUpsertBulk) AddNodeLevel(v int32) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddNodeLevel(v)
	})
}

// UpdateNodeLevel sets the "node_level" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateNodeLevel() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateNodeLevel()
	})
}

// SetCheckStatus sets the "check_status" field.
func (u *ClassificationFilesUpsertBulk) SetCheckStatus(v int32) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetCheckStatus(v)
	})
}

// AddCheckStatus adds v to the "check_status" field.
func (u *ClassificationFilesUpsertBulk) AddCheckStatus(v int32) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddCheckStatus(v)
	})
}

// UpdateCheckStatus sets the "check_status" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateCheckStatus() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateCheckStatus()
	})
}

// SetTreeType sets the "tree_type" field.
func (u *ClassificationFilesUpsertBulk) SetTreeType(v int32) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.SetTreeType(v)
	})
}

// AddTreeType adds v to the "tree_type" field.
func (u *ClassificationFilesUpsertBulk) AddTreeType(v int32) *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.AddTreeType(v)
	})
}

// UpdateTreeType sets the "tree_type" field to the value that was provided on create.
func (u *ClassificationFilesUpsertBulk) UpdateTreeType() *ClassificationFilesUpsertBulk {
	return u.Update(func(s *ClassificationFilesUpsert) {
		s.UpdateTreeType()
	})
}

// Exec executes the query.
func (u *ClassificationFilesUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the ClassificationFilesCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ClassificationFilesCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ClassificationFilesUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

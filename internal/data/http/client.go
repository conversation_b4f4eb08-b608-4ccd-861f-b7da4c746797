package http

import (
	"context"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/go-kratos/kratos/v2/transport/http"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"go.uber.org/zap"
	http2 "net/http"
)

type Client interface {
	SayHello(ctx context.Context, req *SayHelloRequest) (*SayHelloResponse, error)
}

type SayHelloRequest struct {
	Name string
}

type SayHelloResponse struct {
	Content string
}

type client struct {
	someClient *http.Client
	logger     *zap.Logger
}

func (c *client) SayHello(ctx context.Context, req *SayHelloRequest) (*SayHelloResponse, error) {
	var resp *SayHelloResponse
	return resp, c.someClient.Invoke(ctx, http2.MethodGet, "/sayHello", req, resp)
}

func NewClient(logger *zapadapter.Logger, discovery registry.Discovery) (Client, error) {
	someClient, err := http.NewClient(context.Background(),
		http.WithEndpoint("discovery:///ai-web.http"),
		http.WithDiscovery(discovery),
	)
	if err != nil {
		logger.Error("new http client", zap.Error(err))
		return nil, err
	}
	return &client{
		logger:     logger.Logger,
		someClient: someClient,
	}, nil
}

package oss

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"time"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"gitlab.minum.cloud/BackendTeam/pkg/crypto"
	"gitlab.minum.cloud/BackendTeam/pkg/osc"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/BackendTeam/store/api/sts"
	"go.uber.org/zap"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

type Client struct {
	logger    *zapadapter.Logger
	rpcClient rpc.Client
	public    *osc.ObjectStoreClient
	crypto    *crypto.Client
	bc        *conf.Bootstrap
}

func NewClient(logger *zapadapter.Logger, bc *conf.Bootstrap, crypto *crypto.Client, rpcClient rpc.Client) *Client {
	public, _ := newPublic(logger, crypto, rpcClient)
	c := &Client{
		logger:    logger,
		rpcClient: rpcClient,
		public:    public,
		crypto:    crypto,
		bc:        bc,
	}
	go func() {
		for {
			time.Sleep(5 * time.Second)
			err := c.refresh()
			if err == nil && c.public != nil {
				break
			}
		}
	}()
	return c
}

func (c *Client) refresh() error {
	var err error
	if c.public == nil {
		c.public, err = newPublic(c.logger, c.crypto, c.rpcClient)
	}
	return err
}

func newPublic(logger *zapadapter.Logger, crypto *crypto.Client, rpcClient rpc.Client) (*osc.ObjectStoreClient, error) {
	publicSts, err := rpcClient.CreateSts(context.Background(), &sts.CreateStsRequest{
		ResourceType: sts.ResourceType_PUBLIC.Enum(),
		PolicyType:   sts.PolicyType_FULL_ACCESS.Enum(),
		Key:          crypto.PublicKey(),
		Internal:     true,
	})
	if err != nil {
		logger.Error("create sts", zap.Error(err))
		return nil, err
	}
	var info rpc.STSInfo
	_ = crypto.DecryptWithJSONUnmarshal(publicSts.SessionKey, publicSts.Msg, &info)
	objectStoreClient, err := osc.NewObjectStoreClientWithSTS(
		logger.Logger,
		osc.Info{
			Path:             info.Path,
			Endpoint:         info.Endpoint,
			InternalEndpoint: info.InternalEndpoint,
			Bucket:           info.Bucket,
			Region:           info.Region,
			AccessKey:        info.AccessKey,
			AccessSecret:     info.AccessSecret,
			AccessToken:      info.AccessToken,
		},
		true,
		1*time.Hour,
		rpcClient.GetPublicSTS,
	)
	if err != nil {
		logger.Error("new object store client", zap.Error(err))
		return nil, err
	}
	return objectStoreClient, nil
}

func (c *Client) GetObjectURL(ctx context.Context, key string) string {
	if c.public == nil {
		c.logger.Error("oss client public is nil")
		return ""
	}

	if key == "" {
		c.logger.Info("public object store, key is empty")
		return ""
	}

	endpoint := c.public.Endpoint
	bucket := c.public.Bucket
	return fmt.Sprintf("%s/%s/%s", endpoint, bucket, key)
}

func (c *Client) GetObject(ctx context.Context, key string) ([]byte, error) {
	if c.public == nil {
		c.logger.Error("oss client public is nil")
		return nil, nil
	}

	output, err := c.public.Client.GetObject(ctx, &s3.GetObjectInput{
		Bucket:    aws.String(c.public.Bucket),
		Key:       aws.String(key),
		VersionId: aws.String(""),
	})
	if err != nil {
		c.logger.Error("oss get object", zap.Error(err))
		return nil, fmt.Errorf("oss get object: %w", err)
	}
	defer func() {
		err := output.Body.Close()
		if err != nil {
			c.logger.Error("failed to close oss body", zap.Error(err))
		}
	}()

	data, err := io.ReadAll(output.Body)
	if err != nil {
		return nil, fmt.Errorf("oss get object: %w", err)
	}

	return data, nil
}

func (c *Client) PutObject(ctx context.Context, key string, file multipart.File) error {
	if c.public == nil {
		c.logger.Error("oss client public is nil")
		return nil
	}

	_, err := c.public.Client.PutObject(ctx, &s3.PutObjectInput{
		Bucket: aws.String(c.public.Bucket),
		Key:    aws.String(key),
		Body:   file,
	})
	if err != nil {
		return fmt.Errorf("oss put object: %w", err)
	}

	return nil
}

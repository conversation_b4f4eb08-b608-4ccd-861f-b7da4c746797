package oss

import (
	"context"
	"mime/multipart"
	"path"
	"strings"

	"github.com/google/uuid"
)

const (
	PrefixAiAgentAvatar = "ai_agent_avatar"
)

func (c *Client) GetURL(ctx context.Context, key string) string {
	return c.GetObjectURL(ctx, key)
}

func (c *Client) UploadAiAgentAvatar(ctx context.Context, file multipart.File, fileHeader *multipart.FileHeader) (string, error) {
	return c.uploadWithPrefix(ctx, file, fileHeader, PrefixAiAgentAvatar)
}

func (c *Client) uploadWithPrefix(ctx context.Context, file multipart.File, fileHeader *multipart.FileHeader, prefix string) (string, error) {
	prefix = strings.TrimPrefix(prefix, "/")
	ext := path.Ext(fileHeader.Filename)

	key := prefix + "/" + uuid.New().String() + ext
	return key, c.PutObject(ctx, key, file)
}

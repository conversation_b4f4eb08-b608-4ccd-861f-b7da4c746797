package oss

import (
	"context"
	"fmt"
	"mime/multipart"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"gitlab.minum.cloud/BackendTeam/pkg/crypto"
	"gitlab.minum.cloud/BackendTeam/pkg/osc"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/BackendTeam/store/api/sts"
	"go.uber.org/zap"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

type PersistentClient struct {
	logger     *zapadapter.Logger
	rpcClient  rpc.Client
	persistent *osc.ObjectStoreClient
	crypto     *crypto.Client
}

func NewPersistentClient(logger *zapadapter.Logger, crypto *crypto.Client, rpcClient rpc.Client) *PersistentClient {
	persistent, _ := newPersistent(logger, crypto, rpcClient)
	c := &PersistentClient{
		logger:     logger,
		rpcClient:  rpcClient,
		persistent: persistent,
		crypto:     crypto,
	}
	go func() {
		for {
			time.Sleep(5 * time.Second)
			err := c.refreshPersistent()
			if err == nil && c.persistent != nil {
				break
			}
		}
	}()
	return c
}

func (c *PersistentClient) refreshPersistent() error {
	var err error
	if c.persistent == nil {
		c.persistent, err = newPersistent(c.logger, c.crypto, c.rpcClient)
	}
	return err
}

func newPersistent(logger *zapadapter.Logger, crypto *crypto.Client, rpcClient rpc.Client) (*osc.ObjectStoreClient, error) {
	persistentSts, err := rpcClient.CreateSts(context.Background(), &sts.CreateStsRequest{
		ResourceType: sts.ResourceType_PERSISTENT.Enum(),
		PolicyType:   sts.PolicyType_FULL_ACCESS.Enum(),
		Key:          crypto.PublicKey(),
		Internal:     true,
	})
	if err != nil {
		logger.Error("create sts", zap.Error(err))
		return nil, err
	}
	var info rpc.STSInfo
	_ = crypto.DecryptWithJSONUnmarshal(persistentSts.SessionKey, persistentSts.Msg, &info)
	objectStoreClient, err := osc.NewObjectStoreClientWithSTS(
		logger.Logger,
		osc.Info{
			Path:             info.Path,
			Endpoint:         info.Endpoint,
			InternalEndpoint: info.InternalEndpoint,
			Bucket:           info.Bucket,
			Region:           info.Region,
			AccessKey:        info.AccessKey,
			AccessSecret:     info.AccessSecret,
			AccessToken:      info.AccessToken,
		},
		true,
		1*time.Hour,
		rpcClient.GetPersistentSTS,
	)
	if err != nil {
		logger.Error("new object store persistent client", zap.Error(err))
		return nil, err
	}
	return objectStoreClient, nil
}

func (c *PersistentClient) PutObject(ctx context.Context, key string, file multipart.File) error {
	return c.PutObjectWithExpr(ctx, key, file, nil)
}

func (c *PersistentClient) PutObjectWithExpr(ctx context.Context, key string, file multipart.File, expires *time.Time) error {
	if c.persistent == nil {
		c.logger.Error("oss client persistent is nil")
		return nil
	}

	_, err := c.persistent.Client.PutObject(ctx, &s3.PutObjectInput{
		Bucket:  aws.String(c.persistent.Bucket),
		Key:     aws.String(key),
		Body:    file,
		Expires: expires,
	})
	if err != nil {
		return fmt.Errorf("oss put object: %w", err)
	}

	return nil
}

func (c *PersistentClient) GetObject(ctx context.Context, key string) (*s3.GetObjectOutput, error) {
	if c.persistent == nil {
		c.logger.Error("oss client persistent is nil")
		return nil, nil
	}

	output, err := c.persistent.Client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(c.persistent.Bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return nil, fmt.Errorf("oss get object: %w", err)
	}

	return output, nil
}

package data

import (
	"context"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodelusage"
	"go.uber.org/zap"
	"time"
)

type aiModelUsageRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (a aiModelUsageRepo) GetAiModelUsageDetailGroupByDay(ctx context.Context, modelDetailID int64, startTime, endTime time.Time) ([]*biz.AiModelUsageDetailByDay, error) {
	sql := `
	SELECT
  date_trunc('day', created_at) AS day,
  COUNT(*) FILTER (WHERE request_status = 1) AS call_success_count,
  COUNT(*) FILTER (WHERE request_status = 2) AS call_fail_count,
  SUM(prompt_tokens) AS prompt_tokens,
  SUM(completion_tokens) AS completion_tokens
FROM ai_model_usage
WHERE created_at >= date_trunc('day', $1::timestamptz)
  AND created_at < date_trunc('day', $2::timestamptz) + INTERVAL '1 day' 
  AND model_detail_id = $3 
GROUP BY date_trunc('day', created_at)
ORDER BY day; 
`
	rows, err := a.data.db.Query(ctx, sql, startTime, endTime, modelDetailID)
	if err != nil {
		a.log.Error("failed to query ai model usage detail group by day", zap.Error(err))
		return nil, err
	}
	defer rows.Close()
	var usages []*biz.AiModelUsageDetailByDay
	for rows.Next() {
		var usage biz.AiModelUsageDetailByDay
		var day time.Time
		if err := rows.Scan(&day, &usage.CallSuccessCount, &usage.CallFailCount, &usage.PromptTokens, &usage.CompletionTokens); err != nil {
			a.log.Error("failed to scan ai model usage detail group by day", zap.Error(err))
			return nil, err
		}
		usage.Day = day
		usages = append(usages, &usage)
	}
	if err := rows.Err(); err != nil {
		a.log.Error("failed to iterate ai model usage detail group by day", zap.Error(err))
		return nil, err
	}
	return usages, nil
}

func (a aiModelUsageRepo) Page(ctx context.Context, agentID int64) ([]*biz.AiModelUsage, int64, error) {
	query := a.data.db.AiModelUsage(ctx).Query().WhereOn(agentID > 0, aimodelusage.AgentIDEQ(agentID))
	total, err := query.Count(ctx)
	if err != nil {
		return nil, 0, err
	}
	query = query.Paginate(ctx)
	usages, err := query.All(ctx)
	if err != nil {
		a.log.Error("failed to query ai model usage", zap.Error(err))
		return nil, 0, err
	}
	return conv.AiModelUsage.FromEnts(usages), int64(total), nil
}

func (a aiModelUsageRepo) Create(ctx context.Context, usage *biz.AiModelUsage) error {
	if usage == nil {
		return nil
	}
	_, err := a.data.db.AiModelUsage(ctx).Create().
		SetModelName(usage.ModelName).
		SetModelGatewayName(usage.ModelGatewayName).
		SetAgentID(usage.AgentID).
		SetAgentName(usage.AgentName).
		SetUserID(usage.UserID).
		SetUserName(usage.UserName).
		SetQuestion(usage.Question).
		SetAnswer(usage.Answer).
		SetPromptTokens(usage.PromptTokens).
		SetCompletionTokens(usage.CompletionTokens).
		SetRequestStatus(usage.RequestStatus).
		SetModelDetailID(usage.ModelDetailID).
		SetErrorCode(usage.ErrorCode).Save(ctx)
	if err != nil {
		a.log.Error("failed to create ai model usage", zap.Error(err))
		return err
	}
	return nil
}

func NewAiModelUsageRepo(data *Data, logger *zapadapter.Logger) biz.AiModelUsageRepo {
	return &aiModelUsageRepo{
		data: data,
		log:  logger,
	}
}

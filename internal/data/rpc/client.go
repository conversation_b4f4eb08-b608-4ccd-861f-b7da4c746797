package rpc

import (
	"context"
	"errors"
	"fmt"
	"io"
	http2 "net/http"
	"net/url"
	"time"

	"github.com/go-kratos/kratos/v2/registry"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"gitlab.minum.cloud/BackendTeam/admin/api/checklicense"
	"gitlab.minum.cloud/BackendTeam/admin/api/dept"
	"gitlab.minum.cloud/BackendTeam/admin/api/message"
	"gitlab.minum.cloud/BackendTeam/admin/api/user"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"gitlab.minum.cloud/BackendTeam/pkg/crypto"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/BackendTeam/store/api/sts"
	"gitlab.minum.cloud/BackendTeam/store/api/vfs"
	"gitlab.minum.cloud/innovationteam/ai-api/aiapi/agent"
	"gitlab.minum.cloud/innovationteam/ai-api/aiapi/llm"
	"gitlab.minum.cloud/innovationteam/ai-api/aiapi/search"
	"go.uber.org/zap"
	grpc2 "google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
)

type Client interface {
	search.SearchClient
	llm.LlmClient
	vfs.FileClient
	vfs.FileInternalClient
	user.UserClient
	user.UserInternalClient
	message.MessageClient
	sts.StsClient
	agent.AgentClient
	dept.DeptClient
	checklicense.CheckLicenseClient
	GetPublicSTS() (string, string, string, error)
	GetPersistentSTS() (string, string, string, error)
	GetImage(cxt context.Context, key string) ([]byte, error)
}

type client struct {
	logger *zapadapter.Logger
	crypto *crypto.Client
	search.SearchClient
	llm.LlmClient
	vfs.FileClient
	vfs.FileInternalClient
	user.UserClient
	user.UserInternalClient
	message.MessageClient
	sts.StsClient
	agent.AgentClient
	dept.DeptClient
	checklicense.CheckLicenseClient
	discovery registry.Discovery
}

func (c *client) GetImage(ctx context.Context, key string) ([]byte, error) {
	service, err := c.discovery.GetService(ctx, "data-extract")
	if err != nil {
		return nil, err
	}
	if len(service) == 0 {
		return nil, fmt.Errorf("no service found")
	}

	instance := service[0]
	if len(instance.Endpoints) == 0 {
		return nil, fmt.Errorf("no endpoint found")
	}

	reqUrl, err := url.Parse(instance.Endpoints[0])
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("http://%s:%s/fileEvent/getImage?key=%s", reqUrl.Hostname(), reqUrl.Port(), key)
	request, err := http2.NewRequest(http2.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	request.Header.Set("Host", reqUrl.Hostname())
	httpResp, err := http2.DefaultClient.Do(request)
	if err != nil {
		return nil, err
	}
	if httpResp.StatusCode != 200 {
		defer httpResp.Body.Close()
		respBody, _ := io.ReadAll(httpResp.Body)
		return nil, fmt.Errorf("http status code is %d %s", httpResp.StatusCode, string(respBody))
	}
	defer httpResp.Body.Close()
	return io.ReadAll(httpResp.Body)
}

func NewClient(logger *zapadapter.Logger, discovery registry.Discovery, cfg *conf.Bootstrap) (Client, error) {
	aiApiConn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithEndpoint("discovery:///ai-api.grpc"),
		grpc.WithDiscovery(discovery),
		grpc.WithTimeout(cfg.Server.Grpc.Timeout.AsDuration()),
		grpc.WithOptions(grpc2.WithIdleTimeout(0)),
		grpc.WithMiddleware(authz.MiddleWareClient(logger.Logger)),
		grpc.WithHealthCheck(false),
		grpc.WithStreamInterceptor(func(ctx context.Context, desc *grpc2.StreamDesc, cc *grpc2.ClientConn, method string, streamer grpc2.Streamer, opts ...grpc2.CallOption) (grpc2.ClientStream, error) {
			session, ok := authz.GetSession(ctx)
			if !ok {
				return nil, errors.New("session not found")
			}
			authSession := fmt.Sprintf(`{"UserID":%d,"TenantID":%d}`, session.UserID, session.TenantID)
			pairs := metadata.Pairs("x-md-global-authz-session", authSession)
			newCtx := metadata.NewOutgoingContext(ctx, pairs)
			return streamer(newCtx, desc, cc, method, opts...)
		}),
	)
	if err != nil {
		logger.Error("dial grpc", zap.Error(err))
		return nil, err
	}

	storeConn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithEndpoint("discovery:///store.grpc"),
		grpc.WithDiscovery(discovery),
		grpc.WithTimeout(cfg.Server.Grpc.Timeout.AsDuration()),
		grpc.WithOptions(grpc2.WithIdleTimeout(0)),
		grpc.WithMiddleware(authz.MiddleWareClient(logger.Logger)),
	)
	if err != nil {
		logger.Error("dial grpc", zap.Error(err))
		return nil, err
	}
	adminConn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithEndpoint("discovery:///admin.grpc"),
		grpc.WithDiscovery(discovery),
		grpc.WithTimeout(cfg.Server.Grpc.Timeout.AsDuration()),
		grpc.WithOptions(grpc2.WithIdleTimeout(0)),
		grpc.WithMiddleware(authz.MiddleWareClient(logger.Logger)),
	)
	if err != nil {
		logger.Error("dial grpc", zap.Error(err))
		return nil, err
	}

	//systemConn, err := grpc.DialInsecure(
	//	context.Background(),
	//	grpc.WithEndpoint("discovery:///system.grpc"),
	//	grpc.WithDiscovery(discovery),
	//	grpc.WithTimeout(cfg.Server.Grpc.Timeout.AsDuration()),
	//	grpc.WithOptions(grpc2.WithIdleTimeout(0)),
	//	grpc.WithMiddleware(authz.MiddleWareClient(logger.Logger)),
	//)
	//if err != nil {
	//	logger.Error("dial grpc", zap.Error(err))
	//	return nil, err
	//}

	searchClient := search.NewSearchClient(aiApiConn)
	llmClient := llm.NewLlmClient(aiApiConn)
	fileClient := vfs.NewFileClient(storeConn)
	fileInternalClient := vfs.NewFileInternalClient(storeConn)
	userClient := user.NewUserClient(adminConn)
	userIClient := user.NewUserInternalClient(adminConn)
	messageClient := message.NewMessageClient(adminConn)
	stsClient := sts.NewStsClient(storeConn)
	agentClient := agent.NewAgentClient(aiApiConn)
	deptClient := dept.NewDeptClient(adminConn)
	checklicenseClient := checklicense.NewCheckLicenseClient(adminConn)

	cryptoClient, err := crypto.NewClient(5*time.Minute, logger.Logger)
	if err != nil {
		logger.Error("new crypto client", zap.Error(err))
		return nil, err
	}

	return &client{
		logger:             logger,
		SearchClient:       searchClient,
		LlmClient:          llmClient,
		FileClient:         fileClient,
		FileInternalClient: fileInternalClient,
		UserClient:         userClient,
		UserInternalClient: userIClient,
		MessageClient:      messageClient,
		StsClient:          stsClient,
		crypto:             cryptoClient,
		AgentClient:        agentClient,
		DeptClient:         deptClient,
		discovery:          discovery,
		CheckLicenseClient: checklicenseClient,
	}, nil
}

func (c *client) GetFileContent(ctx context.Context, in *vfs.GetFileContentRequest, opts ...grpc2.CallOption) (*vfs.GetFileContentReply, error) {
	return c.FileClient.GetFileContent(ctx, in, opts...)
}

package rpc

import (
	"gitlab.minum.cloud/BackendTeam/store/api/sts"
	"golang.org/x/net/context"
)

type STSInfo struct {
	Path             string `json:"path"`
	Endpoint         string `json:"endpoint"`
	InternalEndpoint string `json:"internalEndpoint"`
	Bucket           string `json:"bucket"`
	Region           string `json:"region"`
	AccessKey        string `json:"accessKey"`
	AccessSecret     string `json:"accessSecret"`
	AccessToken      string `json:"accessToken"`
}

func (c *client) GetPublicSTS() (string, string, string, error) {
	publicSts, err := c.StsClient.CreateSts(context.Background(), &sts.CreateStsRequest{
		ResourceType: sts.ResourceType_PUBLIC.Enum(),
		PolicyType:   sts.PolicyType_FULL_ACCESS.Enum(),
		Key:          c.crypto.PublicKey(),
		Internal:     true,
	})
	if err != nil {
		return "", "", "", err
	}
	var info STSInfo
	err = c.crypto.DecryptWithJSONUnmarshal(publicSts.SessionKey, publicSts.Msg, &info)
	if err != nil {
		return "", "", "", err
	}
	return info.AccessKey, info.AccessSecret, info.AccessToken, nil
}

func (c *client) GetPersistentSTS() (string, string, string, error) {
	publicSts, err := c.StsClient.CreateSts(context.Background(), &sts.CreateStsRequest{
		ResourceType: sts.ResourceType_PERSISTENT.Enum(),
		PolicyType:   sts.PolicyType_FULL_ACCESS.Enum(),
		Key:          c.crypto.PublicKey(),
		Internal:     true,
	})
	if err != nil {
		return "", "", "", err
	}
	var info STSInfo
	err = c.crypto.DecryptWithJSONUnmarshal(publicSts.SessionKey, publicSts.Msg, &info)
	if err != nil {
		return "", "", "", err
	}
	return info.AccessKey, info.AccessSecret, info.AccessToken, nil
}

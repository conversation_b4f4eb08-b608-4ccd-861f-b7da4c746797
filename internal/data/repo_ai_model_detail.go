package data

import (
	"context"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
)

type aiModelDetailRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (a aiModelDetailRepo) Get(ctx context.Context, id int64) (*biz.AiModelDetail, error) {
	ent, err := a.data.db.AiModelDetail(ctx).Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return conv.AiModelDetail.FromEnt(ent), err
}

func (a aiModelDetailRepo) List(ctx context.Context) ([]*biz.AiModelDetail, error) {
	all, err := a.data.db.AiModelDetail(ctx).Query().All(ctx)
	return conv.AiModelDetail.FromEnts(all), err
}

func NewAiModelDetailRepo(data *Data, logger *zapadapter.Logger) biz.AiModelDetailRepo {
	return &aiModelDetailRepo{
		data: data,
		log:  logger,
	}
}

package data

import (
	"context"
	"fmt"
	"github.com/lib/pq"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichat"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodeldetail"
	"time"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/innovationteam/ai-web/api/rag"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aichatitem"

	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
)

type chatItemRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (c *chatItemRepo) UpdateSuggestQuestions(ctx context.Context, chatItemID int64, suggestQuestions []string) error {
	_, err := c.data.db.AiChatItem(ctx).Update().WhereOn(chatItemID > 0, aichatitem.IDEQ(chatItemID)).SetSuggestQuestions((*pq.StringArray)(&suggestQuestions)).Save(ctx)
	return err
}

func (c *chatItemRepo) UpdateHitContinueSend(ctx context.Context, item *biz.ChatItem) error {
	_, err := c.data.db.AiChatItem(ctx).Update().SetHitContinueSend(item.HitContinueSend).Where(aichatitem.IDEQ(item.ID)).Save(ctx)
	return err
}

func (c *chatItemRepo) QueryUploadFileTypesDistribution(ctx context.Context, modelType int64, startTime, endTime time.Time) ([]*biz.FileType, error) {
	sql := `SELECT 
  mt AS name, 
  COUNT(*) AS count 
FROM ai_chat ac 
JOIN ai_chat_item ai ON ac.id = ai.chat_id 
JOIN ai_agent aa ON ac.agent_id = aa.id, 
LATERAL unnest(ai.mine_types) AS mt 
WHERE ai.mine_types IS NOT NULL 
  AND mt <> '' 
  AND aa.model_type = $1  
  AND ai.created_at >= $2  
  AND ai.created_at <= $3  
GROUP BY mt 
ORDER BY count DESC;
`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var fileTypes []*biz.FileType
	for rows.Next() {
		var fileType biz.FileType
		if err := rows.Scan(&fileType.Name, &fileType.Count); err != nil {
			return nil, err
		}
		fileTypes = append(fileTypes, &fileType)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return fileTypes, nil
}

func (c *chatItemRepo) QueryUploadFileTypesTopKCountGroupUser(ctx context.Context, modelType int64, startTime, endTime time.Time, topK int64) ([]*biz.FileTypesObject, error) {
	sql := `SELECT  
  ai.object_id AS user_id, 
  COUNT(*) AS count 
FROM ai_chat ac 
JOIN ai_chat_item ai ON ac.id = ai.chat_id 
JOIN ai_agent aa ON ac.agent_id = aa.id,
LATERAL unnest(ai.mine_types) AS mt 
WHERE ai.mine_types IS NOT NULL 
  AND aa.model_type = $1 
  AND ai.created_at >= $2 
  AND ai.created_at <= $3 
GROUP BY ai.object_id 
ORDER BY count DESC 
LIMIT $4;
`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime, topK)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var fileTypeObjects []*biz.FileTypesObject
	for rows.Next() {
		var fileTypeObject biz.FileTypesObject
		if err := rows.Scan(&fileTypeObject.ObjectID, &fileTypeObject.Count); err != nil {
			return nil, err
		}
		fileTypeObjects = append(fileTypeObjects, &fileTypeObject)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return fileTypeObjects, nil
}

func (c *chatItemRepo) QueryUploadFileTypesTopKCountGroupDept(ctx context.Context, modelType int64, startTime, endTime time.Time, topK int64) ([]*biz.FileTypesObject, error) {
	sql := `SELECT 
  "user"."dept_id",  
  COUNT(*) AS count 
FROM ai_chat ac 
JOIN ai_chat_item ai ON ac.id = ai.chat_id  
JOIN ai_agent aa ON ac.agent_id = aa.id	 
JOIN "user" ON "user"."id" = ac.user_id, 
LATERAL unnest(ai.mine_types) AS mt 
WHERE ai.mine_types IS NOT NULL 
  AND aa.model_type = $1  
  AND ai.created_at >= $2  
  AND ai.created_at <= $3 
GROUP BY "user"."dept_id" 
ORDER BY count DESC  
LIMIT $4;
`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime, topK)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var fileTypeObjects []*biz.FileTypesObject
	for rows.Next() {
		var fileTypeObject biz.FileTypesObject
		if err := rows.Scan(&fileTypeObject.ObjectID, &fileTypeObject.Count); err != nil {
			return nil, err
		}
		fileTypeObjects = append(fileTypeObjects, &fileTypeObject)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return fileTypeObjects, nil
}

func (c *chatItemRepo) QueryUploadFileTypesDistributionByUserID(ctx context.Context, modelType int64, userID int64, startTime, endTime time.Time) ([]*biz.FileType, error) {
	sql := `SELECT 
  mt AS name, 
  COUNT(*) AS count 
FROM ai_chat ac 
JOIN ai_chat_item ai ON ac.id = ai.chat_id  
JOIN ai_agent aa ON ac.agent_id = aa.id, 
LATERAL unnest(ai.mine_types) AS mt 
WHERE ai.mine_types IS NOT NULL 
  AND aa.model_type = $1  
  AND ai.created_at >= $2  
  AND ai.created_at <= $3 
  AND ai.object_id = $4 
GROUP BY mt 
ORDER BY count DESC; 
`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var fileTypes []*biz.FileType
	for rows.Next() {
		var fileType biz.FileType
		if err := rows.Scan(&fileType.Name, &fileType.Count); err != nil {
			return nil, err
		}
		fileTypes = append(fileTypes, &fileType)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return fileTypes, nil
}

func (c *chatItemRepo) QueryUploadFileTypesDistributionByDeptID(ctx context.Context, modelType int64, deptID int64, startTime, endTime time.Time) ([]*biz.FileType, error) {
	sql := `SELECT  
  mt AS name, 
  COUNT(*) AS count 
FROM ai_chat ac 
JOIN ai_chat_item ai ON ac.id = ai.chat_id  
JOIN ai_agent aa ON ac.agent_id = aa.id	 
JOIN "user" ON "user"."id" = ac.user_id, 
LATERAL unnest(ai.mine_types) AS mt 
WHERE ai.mine_types IS NOT NULL 
  AND aa.model_type = $1  
  AND ai.created_at >= $2  
  AND ai.created_at <= $3  
  AND "user"."dept_id" = $4 
GROUP BY mt 
ORDER BY count DESC;
`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime, deptID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var fileTypes []*biz.FileType
	for rows.Next() {
		var fileType biz.FileType
		if err := rows.Scan(&fileType.Name, &fileType.Count); err != nil {
			return nil, err
		}
		fileTypes = append(fileTypes, &fileType)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return fileTypes, nil
}

func (c *chatItemRepo) QuerySecondaryClassificationDistributionByDeptID(ctx context.Context, modelType int64, deptID int64, startTime, endTime time.Time) ([]*biz.Classification, error) {
	sql := `SELECT 
 ai.secondary_classification AS name,
 COUNT(*) as count 
 FROM ai_chat ac
 JOIN ai_chat_item ai ON ac.id = ai.chat_id 
 JOIN ai_agent aa ON ac.agent_id = aa.id	
 JOIN "user" ON "user"."id" = ac.user_id 
 WHERE secondary_classification IS NOT NULL
  AND secondary_classification <> '' 
	AND aa.model_type = $1 
	AND ai.created_at >= $2 
  AND ai.created_at <= $3 
	AND "user"."dept_id" = $4
 GROUP BY ai.secondary_classification
ORDER BY count DESC;

`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime, deptID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var classifications []*biz.Classification
	for rows.Next() {
		var classification biz.Classification
		if err := rows.Scan(&classification.Name, &classification.Count); err != nil {
			return nil, err
		}
		classifications = append(classifications, &classification)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return classifications, nil
}

func (c *chatItemRepo) QuerySecondaryClassificationTopKCountGroupDept(ctx context.Context, modelType int64, startTime, endTime time.Time, topK int64) ([]*biz.ClassificationObject, error) {
	sql := `SELECT 
 "user"."dept_id", 
 COUNT(*) as count 
 FROM ai_chat ac
 JOIN ai_chat_item ai ON ac.id = ai.chat_id 
 JOIN ai_agent aa ON ac.agent_id = aa.id	
 JOIN "user" ON "user"."id" = ac.user_id 
 WHERE secondary_classification IS NOT NULL
  AND secondary_classification <> '' 
	AND aa.model_type = $1 
	AND ai.created_at >= $2 
    AND ai.created_at <= $3
 GROUP BY "user"."dept_id"
ORDER BY count DESC LIMIT $4;
`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime, topK)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var classifications []*biz.ClassificationObject
	for rows.Next() {
		var classification biz.ClassificationObject
		if err := rows.Scan(&classification.ObjectID, &classification.Count); err != nil {
			return nil, err
		}
		classifications = append(classifications, &classification)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return classifications, nil
}

func (c *chatItemRepo) QuerySecondaryClassificationDistributionByUserID(ctx context.Context, modelType int64, userID int64, startTime, endTime time.Time) ([]*biz.Classification, error) {
	sql := `SELECT
 ai.secondary_classification AS name,
 COUNT(*) as count 
 FROM ai_chat ac
 JOIN ai_chat_item ai ON ac.id = ai.chat_id 
 JOIN ai_agent aa ON ac.agent_id = aa.id	
 WHERE secondary_classification IS NOT NULL
  AND secondary_classification <> '' 
	AND aa.model_type = $1 
	AND ai.created_at >= $2 
    AND ai.created_at <= $3 
	AND ai.object_id = $4
 GROUP BY ai.secondary_classification
ORDER BY count DESC;
`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var classifications []*biz.Classification
	for rows.Next() {
		var classification biz.Classification
		if err := rows.Scan(&classification.Name, &classification.Count); err != nil {
			return nil, err
		}
		classifications = append(classifications, &classification)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return classifications, nil
}

func (c *chatItemRepo) QuerySecondaryClassificationTopKCountGroupUser(ctx context.Context, modelType int64, startTime, endTime time.Time, topK int64) ([]*biz.ClassificationObject, error) {
	sql := `SELECT 
 ai.object_id AS user_id, 
 COUNT(*) as count 
 FROM ai_chat ac
 JOIN ai_chat_item ai ON ac.id = ai.chat_id 
 JOIN ai_agent aa ON ac.agent_id = aa.id	
 WHERE secondary_classification IS NOT NULL
  AND secondary_classification <> '' 
	AND aa.model_type = $1 
	AND ai.created_at >= $2 
    AND ai.created_at <= $3
 GROUP BY ai.object_id
ORDER BY count DESC LIMIT $4;
`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime, topK)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var classifications []*biz.ClassificationObject
	for rows.Next() {
		var classification biz.ClassificationObject
		if err := rows.Scan(&classification.ObjectID, &classification.Count); err != nil {
			return nil, err
		}
		classifications = append(classifications, &classification)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return classifications, nil
}

func (c *chatItemRepo) QuerySecondaryClassificationDistribution(ctx context.Context, modelType int64, startTime, endTime time.Time) ([]*biz.Classification, error) {
	sql := `SELECT
 ai.secondary_classification AS name,
 COUNT(*) as count 
 FROM ai_chat ac
 JOIN ai_chat_item ai ON ac.id = ai.chat_id 
 JOIN ai_agent aa ON ac.agent_id = aa.id	
 WHERE secondary_classification IS NOT NULL
  AND secondary_classification <> '' 
	AND aa.model_type = $1 
	AND ai.created_at >= $2 
    AND ai.created_at <= $3
 GROUP BY ai.secondary_classification
ORDER BY count DESC;
`
	rows, err := c.data.db.Query(ctx, sql, modelType, startTime, endTime)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var classifications []*biz.Classification
	for rows.Next() {
		var classification biz.Classification
		if err := rows.Scan(&classification.Name, &classification.Count); err != nil {
			return nil, err
		}
		classifications = append(classifications, &classification)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return classifications, nil
}

func (c *chatItemRepo) Update(ctx context.Context, item *biz.ChatItem) (*biz.ChatItem, error) {
	chatItemEnt, err := c.data.db.AiChatItem(ctx).UpdateOneID(item.ID).
		SetChatID(item.ChatID).
		SetObjectType(item.ObjectType).
		SetObjectID(item.ObjectID).
		SetMessage(item.Message).
		SetTenantID(item.TenantID).
		SetRefFiles(item.RefFiles).
		SetRoundID(item.RoundID).
		SetPcName(item.PcName).
		SetReason(item.Reason).
		SetIsInternetSearch(item.IsInternetSearch).
		Save(ctx)
	if err != nil {
		return nil, err
	}

	return &biz.ChatItem{
		ID:         chatItemEnt.ID,
		ChatID:     chatItemEnt.ChatID,
		ObjectType: int64(rag.ChatObjectType(chatItemEnt.ObjectType)),
		ObjectID:   chatItemEnt.ObjectID,
		Message:    chatItemEnt.Message,
		RefFiles:   chatItemEnt.RefFiles,
		PcName:     chatItemEnt.PcName,
	}, nil
}

func (c *chatItemRepo) UpdateAgreeStatus(ctx context.Context, chatItemId, agreeStatus int64) error {
	return c.data.db.AiChatItem(ctx).UpdateOneID(chatItemId).SetAgreeStatus(int8(agreeStatus)).Exec(ctx)
}

func (c *chatItemRepo) PageUserQuestion(ctx context.Context, chatItemID int64, userIDs []int64, startTime, endTime time.Time, searchModel bool, modelID, agentID int64, searchfile []string, noRefFiles bool, class string) ([]*biz.ChatItemInfo, int64, error) {
	/*
		select a1.id,
		       a1.message,
		       a1.object_id,
		       a1.pc_name,
		       a1.ref_files,
		       a1.secondary_classification,
		       a3.name,
		       a3.avatar,
		       a3.model_id,
		       a5.model_name,
		       a5.avatar
		from ai_chat_item a1
		         left join ai_chat a2 on a2.id = a1.chat_id
		         left join ai_agent a3 on a3.id = a2.agent_id
		         left join ai_model a4 on a4.id = a3.model_id
		         left join ai_model_detail a5 on a5.id = a4.model
		where object_type = 1
		order by a1.id desc
		;
	*/
	query := c.data.db.AiChatItem(ctx).Query()
	query = query.WhereOn(len(userIDs) > 0, aichatitem.ObjectIDIn(userIDs...)).
		Where(aichatitem.ObjectType(int64(pb.ChatObjectType_ChatObjectTypeUser))).
		WhereOn(!startTime.IsZero(), aichatitem.CreatedAtGTE(startTime)).
		WhereOn(!endTime.IsZero(), aichatitem.CreatedAtLTE(endTime)).
		WhereOn(chatItemID > 0, aichatitem.ID(chatItemID))

	query.Modify(func(s *sql.Selector) {
		a1 := s
		a2 := sql.Table(aichat.Table).As("a2")
		a3 := sql.Table(aiagent.Table).As("a3")
		a4 := sql.Table(aimodel.Table).As("a4")
		a5 := sql.Table(aimodeldetail.Table).As("a5")

		s.Select(a1.C("id"),
			a1.C("message"),
			a1.C("object_id"),
			a1.C("pc_name"),
			a1.C("ref_files"),
			a1.C("secondary_classification"),
			a1.C("created_at"),
			"a3.name as agent_name",
			"a3.avatar as agent_avatar",
			"a3.model_id as model_id",
			"a4.model_name as model_name",
			"a5.avatar as model_avatar",
		)

		a1.LeftJoin(a2).On(a2.C("id"), a1.C("chat_id")).
			LeftJoin(a3).On(a3.C("id"), a2.C("agent_id")).
			LeftJoin(a4).On(a4.C("id"), a3.C("model_id")).
			LeftJoin(a5).On(a5.C("id"), a4.C("model"))

		if len(class) > 0 {
			a1.Where(sql.P(func(builder *sql.Builder) {
				builder.WriteString("ai_chat_item.secondary_classification like ").Arg("%" + class + "%")
			}))
		}

		if searchModel {
			a1.Where(sql.P(func(builder *sql.Builder) {
				builder.WriteString("model_id > 0")
			}))
		} else {
			a1.Where(sql.P(func(builder *sql.Builder) {
				builder.WriteString("model_id = 0")
			}))
		}

		if modelID > 0 {
			a1.Where(sql.P(func(builder *sql.Builder) {
				builder.WriteString(fmt.Sprintf("agent_id = %d", modelID))
			}))
		}

		if agentID > 0 {
			a1.Where(sql.P(func(builder *sql.Builder) {
				builder.WriteString(fmt.Sprintf("agent_id = %d ", agentID))
			}))
		}

		if len(searchfile) > 0 {
			var conditions []func(*sql.Builder)
			for _, filetype := range searchfile {
				filetype := filetype // capture loop variable
				conditions = append(conditions, func(builder *sql.Builder) {
					builder.WriteString("ai_chat_item.ref_files like ").Arg("%" + filetype + "%")
				})
			}

			if noRefFiles {
				conditions = append(conditions, func(builder *sql.Builder) {
					builder.WriteString("ai_chat_item.ref_files = ''")
				})
			}

			a1.Where(sql.P(func(builder *sql.Builder) {
				builder.WriteString("(")
				for i, condition := range conditions {
					if i > 0 {
						builder.WriteString(" or ")
					}
					condition(builder)
				}
				builder.WriteString(")")
			}))
		} else {
			if noRefFiles {
				a1.Where(sql.P(func(builder *sql.Builder) {
					builder.WriteString("ai_chat_item.ref_files = ''")
				}))
			}
		}
	})

	total, err := query.Count(ctx)
	if err != nil {
		return nil, 0, err
	}

	var resp []*biz.ChatItemInfo
	err = query.Order(aichatitem.ByID(sql.OrderDesc())).Paginate(ctx).Modify().Scan(ctx, &resp)
	if err != nil {
		return nil, 0, err
	}
	return resp, int64(total), nil
}

func (c *chatItemRepo) GetTotalChatCount(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	count, err := c.data.db.AiChatItem(ctx).Query().Where(aichatitem.ObjectTypeEQ(int64(rag.ChatObjectType_ChatObjectTypeUser))).WhereOn(!startTime.IsZero(), aichatitem.CreatedAtGTE(startTime)).WhereOn(!endTime.IsZero(), aichatitem.CreatedAtLTE(endTime)).Count(ctx)
	if err != nil {
		return 0, err
	}
	return int64(count), nil
}

// ListAll implements biz.ChatItemRepo.
func (c *chatItemRepo) ListAll(ctx context.Context, chatID int64) ([]*biz.ChatItem, error) {
	chatItems, err := c.data.db.AiChatItem(ctx).Query().Where(aichatitem.ChatID(chatID)).Order(aichatitem.ByID(sql.OrderAsc())).All(ctx)
	if err != nil {
		return nil, err
	}

	return conv.ChatItem.FromEnts(ctx, chatItems), nil
}

// Save implements biz.ChatItemRepo.
func (c *chatItemRepo) Save(ctx context.Context, chatItem *biz.ChatItem) (*biz.ChatItem, error) {
	chatItemEnt, err := c.data.db.AiChatItem(ctx).Create().
		SetChatID(chatItem.ChatID).
		SetObjectType(int64(chatItem.ObjectType)).
		SetObjectID(chatItem.ObjectID).
		SetMessage(chatItem.Message).
		SetTenantID(chatItem.TenantID).
		SetRefFiles(chatItem.RefFiles).
		SetRoundID(chatItem.RoundID).
		SetPcName(chatItem.PcName).
		SetReason(chatItem.Reason).
		SetMineTypes((*pq.StringArray)(&chatItem.MineTypes)).
		SetHitAction(int64(chatItem.HitAction)).
		SetHitResponse(chatItem.HitResponse).
		SetHitContinueSend(chatItem.HitContinueSend).
		Save(ctx)
	if err != nil {
		return nil, err
	}

	return &biz.ChatItem{
		ID:         chatItemEnt.ID,
		ChatID:     chatItemEnt.ChatID,
		ObjectType: int64(rag.ChatObjectType(chatItemEnt.ObjectType)),
		ObjectID:   chatItemEnt.ObjectID,
		Message:    chatItemEnt.Message,
		RefFiles:   chatItemEnt.RefFiles,
		PcName:     chatItemEnt.PcName,
	}, nil
}

func NewChatItemRepo(data *Data, logger *zapadapter.Logger) biz.ChatItemRepo {
	return &chatItemRepo{
		data: data,
		log:  logger,
	}
}

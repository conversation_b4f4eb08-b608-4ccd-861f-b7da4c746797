package data

import (
	"context"
	"fmt"
	"slices"
	"time"

	"entgo.io/ent/dialect/sql"
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebasefile"
)

type knowledgeBaseFileRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func NewKnowledgeBaseFileRepo(
	data *Data,
	log *zapadapter.Logger,
) biz.KnowledgeBaseFileRepo {
	return &knowledgeBaseFileRepo{
		data: data,
		log:  log,
	}
}

func (repo *knowledgeBaseFileRepo) BatchAddKnowledgeBaseFile(ctx context.Context, tenantID, id int64, dataType pb.KnowledgeBaseDataType, fileRelationIDs []int64) error {
	if len(fileRelationIDs) == 0 {
		return nil
	}

	creators := lox.Map(fileRelationIDs, func(fileRelationID int64) *ent.KnowledgeBaseFileCreate {
		return repo.data.db.KnowledgeBaseFile(ctx).
			Create().
			SetKnowledgeBaseID(id).
			SetDataType(int32(dataType)).
			SetFileRelationID(fileRelationID).
			SetMetadata("").
			SetStatus(int32(pb.KnowledgeBaseFileStatus_KnowledgeBaseFileStatusWaiting)).
			SetFailedReason("").
			SetTenantID(tenantID)
	})

	err := repo.data.db.KnowledgeBaseFile(ctx).
		CreateBulk(creators...).
		OnConflict(
			sql.ConflictColumns(
				knowledgebasefile.FieldKnowledgeBaseID,
				knowledgebasefile.FieldFileRelationID,
				knowledgebasefile.FieldDeletedAt,
			),
		).
		UpdateNewValues().
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("batch add knowledge base file: %w", err)
	}

	return nil
}

func (repo *knowledgeBaseFileRepo) BatchDeleteKnowledgeBaseFile(ctx context.Context, tenantID, id int64, dataType pb.KnowledgeBaseDataType, fileRelationIDs []int64) error {
	if len(fileRelationIDs) == 0 {
		return nil
	}

	_, err := repo.data.db.KnowledgeBaseFile(ctx).
		Delete().
		Where(knowledgebasefile.KnowledgeBaseIDEQ(id)).
		Where(knowledgebasefile.DataTypeEQ(int32(dataType))).
		Where(knowledgebasefile.FileRelationIDIn(fileRelationIDs...)).
		Where(knowledgebasefile.TenantIDEQ(tenantID)).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("delete knowledge base file: %w", err)
	}

	return nil
}

func (repo *knowledgeBaseFileRepo) UpdateKnowledgeBaseFile(ctx context.Context, id, fileRelationID int64, metadata *string, status *pb.KnowledgeBaseFileStatus, failedReason *string) error {
	if metadata == nil && status == nil && failedReason == nil {
		return nil
	}

	updator := repo.data.db.KnowledgeBaseFile(ctx).Update()
	if metadata != nil {
		updator = updator.SetMetadata(*metadata)
	}
	if status != nil {
		updator = updator.SetStatus(int32(*status))
	}
	if failedReason != nil {
		updator = updator.SetFailedReason(*failedReason)
	}

	return updator.
		Where(knowledgebasefile.KnowledgeBaseIDEQ(id)).
		Where(knowledgebasefile.FileRelationIDEQ(fileRelationID)).
		Exec(ctx)
}

func (repo *knowledgeBaseFileRepo) GetFileKnowledgeBaseIDs(ctx context.Context, dataType pb.KnowledgeBaseDataType, fileRelationID int64) ([]int64, error) {
	knowledgeBaseFiles, err := repo.data.db.KnowledgeBaseFile(ctx).
		Query().
		Where(knowledgebasefile.DataTypeEQ(int32(dataType))).
		Where(knowledgebasefile.FileRelationIDEQ(fileRelationID)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("query file knowledge base ids: %w", err)
	}

	var knowledgeBaseIDs []int64
	for _, knowledgeBaseFile := range knowledgeBaseFiles {
		knowledgeBaseIDs = append(knowledgeBaseIDs, knowledgeBaseFile.KnowledgeBaseID)
	}

	return knowledgeBaseIDs, nil
}

func (repo *knowledgeBaseFileRepo) GetFilesKnowledgeBaseIDs(ctx context.Context, dataType pb.KnowledgeBaseDataType, fileRelationIDs []int64) (map[int64][]int64, error) {
	if len(fileRelationIDs) == 0 {
		return nil, nil
	}

	knowledgeBaseFiles, err := repo.data.db.KnowledgeBaseFile(ctx).
		Query().
		Where(knowledgebasefile.DataTypeEQ(int32(dataType))).
		Where(knowledgebasefile.FileRelationIDIn(fileRelationIDs...)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("query file knowledge base ids: %w", err)
	}

	knowledgeBaseIDsByFileRelationID := make(map[int64][]int64, len(fileRelationIDs))
	for _, knowledgeBaseFile := range knowledgeBaseFiles {
		knowledgeBaseIDsByFileRelationID[knowledgeBaseFile.FileRelationID] = append(
			knowledgeBaseIDsByFileRelationID[knowledgeBaseFile.FileRelationID],
			knowledgeBaseFile.KnowledgeBaseID,
		)
	}

	return knowledgeBaseIDsByFileRelationID, nil
}

func (repo *knowledgeBaseFileRepo) FilterKnowledgeBaseFile(ctx context.Context, id int64, fileRelationIDs []int64) ([]int64, error) {
	if len(fileRelationIDs) == 0 {
		return nil, nil
	}

	knowledgeBaseFiles, err := repo.data.db.KnowledgeBaseFile(ctx).
		Query().
		Where(knowledgebasefile.KnowledgeBaseIDEQ(id)).
		Where(knowledgebasefile.FileRelationIDIn(fileRelationIDs...)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("query knowledge base file: %w", err)
	}

	var filteredFileRelationIDs []int64
	for _, knowledgeBaseFile := range knowledgeBaseFiles {
		filteredFileRelationIDs = append(filteredFileRelationIDs, knowledgeBaseFile.FileRelationID)
	}

	var res []int64
	for _, fileRelationID := range fileRelationIDs {
		if !slices.Contains(filteredFileRelationIDs, fileRelationID) {
			res = append(res, fileRelationID)
		}
	}

	return res, nil
}

func (repo *knowledgeBaseFileRepo) ExistKnowledgeBaseFile(ctx context.Context, id, fileRelationID int64) (bool, error) {
	count, err := repo.data.db.KnowledgeBaseFile(ctx).
		Query().
		Where(knowledgebasefile.KnowledgeBaseIDEQ(id)).
		Where(knowledgebasefile.FileRelationIDEQ(fileRelationID)).
		Count(ctx)
	if err != nil {
		return false, fmt.Errorf("count knowledge base file: %w", err)
	}
	return count > 0, nil
}

func (repo *knowledgeBaseFileRepo) GetInKnowledgeBaseFileIDs(ctx context.Context, fileRelationIDs []int64) ([]int64, error) {
	if len(fileRelationIDs) == 0 {
		return nil, nil
	}

	var results []*struct {
		FileRelationID int64 `sql:"file_relation_id"`
		Count          int64 `sql:"count"`
	}
	err := repo.data.db.KnowledgeBaseFile(ctx).
		Query().
		Modify(func(kbf *sql.Selector) {
			kb := sql.Table(knowledgebase.Table).As("kb")
			kbf.
				Join(kb).
				On(kb.C(knowledgebase.FieldID), kbf.C(knowledgebasefile.FieldKnowledgeBaseID)).
				Where(sql.EQ(kb.C(knowledgebase.FieldDeletedAt), time.Time{}))
		}).
		Where(knowledgebasefile.FileRelationIDIn(fileRelationIDs...)).
		GroupBy(knowledgebasefile.FieldFileRelationID).
		Aggregate(ent.Count()).
		Scan(ctx, &results)
	if err != nil {
		return nil, fmt.Errorf("query knowledge base file: %w", err)
	}

	var knowledgeBaseFileIDs []int64
	for _, result := range results {
		if result.Count > 0 {
			knowledgeBaseFileIDs = append(knowledgeBaseFileIDs, result.FileRelationID)
		}
	}

	return knowledgeBaseFileIDs, nil
}

func (repo *knowledgeBaseFileRepo) PageKnowledgeBaseFile(ctx context.Context, id int64, fileName string) ([]*biz.KnowledgeBaseFile, int64, error) {
	q := repo.data.db.KnowledgeBaseFile(ctx).
		Query().
		Modify(func(s *sql.Selector) {
			if fileName == "" {
				return
			}

			f := sql.Table("file_relation").As("f")
			s.
				LeftJoin(f).
				On(s.C(knowledgebasefile.FieldFileRelationID), f.C("id")).
				Where(sql.ContainsFold(f.C("name"), fileName))
		}).
		Where(knowledgebasefile.KnowledgeBaseIDEQ(id))

	total, err := q.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("count knowledge base file: %w", err)
	}

	knowledgeBaseFiles, err := q.Paginate(ctx).Order(knowledgebasefile.ByID(sql.OrderDesc())).All(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("query knowledge base file: %w", err)
	}

	return conv.KnowledgeBaseFile.FromEnts(knowledgeBaseFiles), int64(total), nil
}

func (repo *knowledgeBaseFileRepo) CountKnowledgeBaseFile(ctx context.Context, tenantID int64, startTime, endTime time.Time) (int64, error) {
	count, err := repo.data.db.KnowledgeBaseFile(ctx).
		Query().
		Where(knowledgebasefile.TenantIDEQ(tenantID)).
		WhereOn(!startTime.IsZero(), knowledgebasefile.CreatedAtGTE(startTime)).
		WhereOn(!endTime.IsZero(), knowledgebasefile.CreatedAtLTE(endTime)).
		Count(ctx)
	if err != nil {
		return 0, fmt.Errorf("count knowledge base file: %w", err)
	}
	return int64(count), nil
}

func (repo *knowledgeBaseFileRepo) GetFileWithKnowledgeBaseIDs(ctx context.Context, fileRelationID int64, knowledgeBaseIDs []int64) ([]*biz.KnowledgeBaseFile, error) {
	files, err := repo.data.db.KnowledgeBaseFile(ctx).Query().Where(knowledgebasefile.FileRelationIDEQ(fileRelationID)).Where(knowledgebasefile.KnowledgeBaseIDIn(knowledgeBaseIDs...)).All(ctx)
	return conv.KnowledgeBaseFile.FromEnts(files), err
}

package data

import (
	"context"
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/defaultagentavatar"
)

type defaultAgentAvatarRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (d defaultAgentAvatarRepo) GetDefaultAvatars(ctx context.Context, avatarType int64) ([]*biz.AgentAvatar, error) {
	defaultAgentAvatars, err := d.data.db.DefaultAgentAvatar(ctx).Query().WhereOn(avatarType > 0, defaultagentavatar.AvatarTypeEQ(int8(avatarType))).All(ctx)
	if err != nil {
		return nil, err
	}
	return lox.Map(defaultAgentAvatars, func(avatar *ent.DefaultAgentAvatar) *biz.AgentAvatar {
		return &biz.AgentAvatar{
			Avatar:        avatar.Avatar,
			ClickedAvatar: avatar.ClickedAvatar,
		}
	}), nil

}

func NewDefaultAgentAvatarRepoRepo(data *Data, logger *zapadapter.Logger) biz.DefaultAgentAvatarRepo {
	return &defaultAgentAvatarRepo{
		data: data,
		log:  logger,
	}
}

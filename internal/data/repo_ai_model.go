package data

import (
	"context"
	"entgo.io/ent/dialect/sql"

	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aimodel"
)

type aiModelRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (a aiModelRepo) CountByApiKey(ctx context.Context, apiKey string, modelDetailID int64) (int64, error) {
	/**
	select count(*)
	from ai_model
	where api_key = ?
	*/
	count, err := a.data.db.AiModel(ctx).Query().Where(aimodel.APIKey(apiKey)).Where(aimodel.ModelEQ(modelDetailID)).Count(ctx)
	if err != nil {
		return 0, err
	}
	return int64(count), nil
}

func (a aiModelRepo) GetModelDetails(ctx context.Context, modelID []int64) ([]*biz.AiModelInfo, error) {
	/**
	select ai_model.id, name, avatar
	from ai_model
	         left join public.ai_model_detail amd on ai_model.model = amd.id
	;
	*/
	var res []*biz.AiModelInfo
	if err := a.data.db.AiModel(ctx).Query().Where(aimodel.IDIn(modelID...)).Modify(func(s *sql.Selector) {
		amd := sql.Table("ai_model_detail").As("amd")
		s.Select(
			s.C("id"),
			amd.C("name"),
			amd.C("avatar"),
		).LeftJoin(amd).On(aimodel.FieldModel, amd.C("id"))
	}).Scan(ctx, &res); err != nil {
		return nil, err
	}
	return res, nil
}

func (a aiModelRepo) Get(ctx context.Context, id int64) (*biz.AiModel, error) {
	res, err := a.data.db.AiModel(ctx).Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return conv.AiModel.FromEnt(res), nil
}

func (a aiModelRepo) Save(ctx context.Context, model *biz.AiModel) (*biz.AiModel, error) {
	res, err := a.data.db.AiModel(ctx).Create().SetModelName(model.ModelName).SetModel(model.Model).SetAPIKey(model.ApiKey).Save(ctx)
	if err != nil {
		return nil, err
	}
	return conv.AiModel.FromEnt(res), nil
}

func (a aiModelRepo) Update(ctx context.Context, model *biz.AiModel) (*biz.AiModel, error) {
	update := a.data.db.AiModel(ctx).UpdateOneID(model.ID).SetModelName(model.ModelName).SetModel(model.Model)
	if model.ApiKey != "" {
		update = update.SetAPIKey(model.ApiKey)
	}
	save, err := update.Save(ctx)
	if err != nil {
		return nil, err
	}
	return conv.AiModel.FromEnt(save), nil
}

func (a aiModelRepo) Delete(ctx context.Context, id int64) error {
	return a.data.db.AiModel(ctx).DeleteOneID(id).Exec(ctx)
}

func (a aiModelRepo) List(ctx context.Context, modelName string) ([]*biz.AiModel, error) {
	all, err := a.data.db.AiModel(ctx).Query().WhereOn(modelName != "", aimodel.ModelNameContains(modelName)).Order(ent.Desc(aimodel.FieldCreatedAt)).All(ctx)
	if err != nil {
		return nil, err
	}
	return conv.AiModel.FromEnts(all), nil
}

func NewAiModelRepo(data *Data, logger *zapadapter.Logger) biz.AiModelRepo {
	return &aiModelRepo{
		data: data,
		log:  logger,
	}
}

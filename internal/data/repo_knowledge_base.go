package data

import (
	"context"
	"fmt"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/predicate"
)

type knowledgeBaseRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func NewKnowledgeBaseRepo(
	data *Data,
	log *zapadapter.Logger,
) biz.KnowledgeBaseRepo {
	return &knowledgeBaseRepo{
		data: data,
		log:  log,
	}
}

func (repo *knowledgeBaseRepo) CreateKnowledgeBase(ctx context.Context, tenantID int64, name string, public bool, dataType pb.KnowledgeBaseDataType, userID int64, managerUserIDs, editableUserIDs []int64) (int64, error) {
	if editableUserIDs == nil {
		editableUserIDs = []int64{}
	}
	if managerUserIDs == nil {
		managerUserIDs = []int64{}
	}
	knowledgeBase, err := repo.data.db.KnowledgeBase(ctx).
		Create().
		SetName(name).
		SetPublic(public).
		SetDataType(int32(dataType)).
		SetUserID(userID).
		SetManagerUserIds((*pq.Int64Array)(&managerUserIDs)).
		SetEditableUserIds((*pq.Int64Array)(&editableUserIDs)).
		SetTenantID(tenantID).
		Save(ctx)
	if err != nil {
		return 0, fmt.Errorf("create knowledge base: %w", err)
	}
	return knowledgeBase.ID, nil
}

func (repo *knowledgeBaseRepo) UpdateKnowledgeBase(ctx context.Context, tenantID, id int64, name string, public bool, editableUserIDs []int64, ownerOption *biz.OwnerOption) error {
	if editableUserIDs == nil {
		editableUserIDs = []int64{}
	}

	e := repo.data.db.KnowledgeBase(ctx).
		Update().
		SetName(name).
		SetPublic(public).
		SetEditableUserIds((*pq.Int64Array)(&editableUserIDs))

	if ownerOption != nil {
		if ownerOption.NewUserID > 0 {
			e.SetUserID(ownerOption.NewUserID)
		}

		managerUserIDs := ownerOption.ManagerUserIDs
		if managerUserIDs == nil {
			managerUserIDs = []int64{}
		}
		e.SetManagerUserIds((*pq.Int64Array)(&managerUserIDs))
	}

	err := e.
		Where(knowledgebase.IDEQ(id)).
		Where(knowledgebase.TenantIDEQ(tenantID)).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("update knowledge base: %w", err)
	}
	return nil
}

func (repo *knowledgeBaseRepo) ChangeKnowledgeBaseOwner(ctx context.Context, ids []int64, newUserID int64) error {
	if len(ids) == 0 {
		return nil
	}
	err := repo.data.db.KnowledgeBase(ctx).
		Update().
		SetUserID(newUserID).
		Where(knowledgebase.IDIn(ids...)).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("change knowledge base owner: %w", err)
	}
	return nil
}

func (repo *knowledgeBaseRepo) DeleteKnowledgeBase(ctx context.Context, tenantID, userID, id int64) error {
	_, err := repo.data.db.KnowledgeBase(ctx).
		Delete().
		Where(knowledgebase.IDEQ(id)).
		Where(knowledgebase.UserIDEQ(userID)).
		Where(knowledgebase.TenantIDEQ(tenantID)).
		Exec(ctx)
	if err != nil {
		return fmt.Errorf("delete knowledge base: %w", err)
	}
	return nil
}

func (repo *knowledgeBaseRepo) GetKnowledgeBase(ctx context.Context, tenantID, id int64) (*biz.KnowledgeBase, error) {
	knowledgeBase, err := repo.data.db.KnowledgeBase(ctx).
		Query().
		Where(knowledgebase.IDEQ(id)).
		Where(knowledgebase.TenantIDEQ(tenantID)).
		First(ctx)
	if err != nil {
		return nil, fmt.Errorf("get knowledge base: %w", err)
	}
	return conv.KnowledgeBase.FromEnt(knowledgeBase), nil
}

func (repo *knowledgeBaseRepo) GetKnowledgeBasesByUser(ctx context.Context, userID int64) ([]*biz.KnowledgeBase, error) {
	knowledgeBases, err := repo.data.db.KnowledgeBase(ctx).
		Query().
		Where(knowledgebase.UserIDEQ(userID)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("get knowledge bases: %w", err)
	}

	return conv.KnowledgeBase.FromEnts(knowledgeBases), nil
}

func (repo *knowledgeBaseRepo) GetKnowledgeBases(ctx context.Context, tenantID int64, ids []int64) ([]*biz.KnowledgeBase, error) {
	if len(ids) == 0 {
		return nil, nil
	}

	knowledgeBases, err := repo.data.db.KnowledgeBase(ctx).
		Query().
		Where(knowledgebase.IDIn(ids...)).
		Where(knowledgebase.TenantIDEQ(tenantID)).
		All(ctx)
	if err != nil {
		return nil, fmt.Errorf("get knowledge bases: %w", err)
	}

	return conv.KnowledgeBase.FromEnts(knowledgeBases), nil
}

func (repo *knowledgeBaseRepo) GetAllKnowledgeBases(ctx context.Context) ([]*biz.KnowledgeBase, error) {
	knowledgeBases, err := repo.data.db.KnowledgeBase(ctx).Query().All(ctx)
	if err != nil {
		return nil, fmt.Errorf("get knowledge bases: %w", err)
	}

	return conv.KnowledgeBase.FromEnts(knowledgeBases), nil
}

func (repo *knowledgeBaseRepo) PageKnowledgeBase(ctx context.Context, tenantID, userID int64, name string, all bool, dataType int32) ([]*biz.KnowledgeBase, int64, error) {
	q := repo.data.db.KnowledgeBase(ctx).Query()

	managerUserIDsContains := predicate.KnowledgeBase(func(s *sql.Selector) {
		column := s.Quote(sql.Table(knowledgebase.Table).C(knowledgebase.FieldManagerUserIds))
		s.Where(sql.ExprP(fmt.Sprintf("%s @> ARRAY[CAST($1 AS BIGINT)]", column), userID))
	})
	editableUserIDsContains := predicate.KnowledgeBase(func(s *sql.Selector) {
		column := s.Quote(sql.Table(knowledgebase.Table).C(knowledgebase.FieldEditableUserIds))
		s.Where(sql.ExprP(fmt.Sprintf("%s @> ARRAY[CAST($2 AS BIGINT)]", column), userID))
	})
	if all {
		// 所有能看到的
		q.Where(knowledgebase.Or(
			editableUserIDsContains,
			managerUserIDsContains,
			knowledgebase.UserIDEQ(userID),
			knowledgebase.PublicEQ(true),
		))
	} else {
		// 我可编辑的
		q.Where(knowledgebase.Or(
			editableUserIDsContains,
			managerUserIDsContains,
			knowledgebase.UserIDEQ(userID),
		))
	}

	q.
		WhereOn(name != "", knowledgebase.NameContainsFold(name)).
		Where(knowledgebase.TenantIDEQ(tenantID)).
		WhereOn(dataType != 0, knowledgebase.DataTypeEQ(int32(dataType)))

	total, err := q.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("count knowledge base: %w", err)
	}

	knowledgeBases, err := q.Paginate(ctx).Order(knowledgebase.ByID(sql.OrderDesc())).All(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("query knowledge base: %w", err)
	}

	return conv.KnowledgeBase.FromEnts(knowledgeBases), int64(total), nil
}

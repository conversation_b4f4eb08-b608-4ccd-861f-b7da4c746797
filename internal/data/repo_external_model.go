package data

import (
	"context"
	"encoding/json"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/metrics"
	vm "gitlab.minum.cloud/BackendTeam/pkg/victoriametrics-client"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/externalmodelusage"
)

type externalModelRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func NewExternalModelRepo(
	data *Data,
	log *zapadapter.Logger,
) biz.ExternalModelRepo {
	return &externalModelRepo{
		data: data,
		log:  log,
	}
}

func (repo *externalModelRepo) CreateExternalModelUsage(ctx context.Context, externalModelUsage *biz.ExternalModelUsage) (int64, error) {
	var files string
	var mimeTypes []string
	if len(externalModelUsage.Files) == 0 {
		files = "[]"
		mimeTypes = []string{}
	} else {
		byteFiles, err := json.Marshal(externalModelUsage.Files)
		if err != nil {
			return 0, err
		}
		files = string(byteFiles)

		for _, file := range externalModelUsage.Files {
			mimeTypes = append(mimeTypes, file.MimeType)
		}
	}

	entExternalModelUsage, err := repo.data.db.ExternalModelUsage(ctx).
		Create().
		SetModelName(externalModelUsage.ModelName).
		SetQuestion(externalModelUsage.Question).
		SetQuestionTag("").
		SetFiles(files).
		SetMimeTypes((*pq.StringArray)(&mimeTypes)).
		SetUserID(externalModelUsage.UserID).
		SetUserName(externalModelUsage.UserName).
		SetDeptID(externalModelUsage.DeptID).
		SetDeptName(externalModelUsage.DeptName).
		SetPcName(externalModelUsage.PcName).
		SetHappenedAt(externalModelUsage.HappenedAt).
		Save(ctx)
	if err != nil {
		return 0, err
	}

	metrics.CreateSituationAwareness(vm.AccidentExternalModelCall, externalModelUsage.UserID, externalModelUsage.DeptID)

	return entExternalModelUsage.ID, nil
}

func (repo *externalModelRepo) UpdateQuestionTag(ctx context.Context, id int64, questionTag string) error {
	return repo.data.db.ExternalModelUsage(ctx).
		Update().
		SetQuestionTag(questionTag).
		Where(externalmodelusage.IDEQ(id)).
		Exec(ctx)
}

func (repo *externalModelRepo) PageExternalModelUsage(ctx context.Context, id int64, userName, deptName string, startTime, endTime time.Time) ([]*biz.ExternalModelUsage, int64, error) {
	q := repo.data.db.ExternalModelUsage(ctx).
		Query().
		WhereOn(id != 0, externalmodelusage.IDEQ(id)).
		WhereOn(userName != "", externalmodelusage.UserNameContainsFold(userName)).
		WhereOn(deptName != "", externalmodelusage.DeptNameContainsFold(deptName)).
		WhereOn(!startTime.IsZero(), externalmodelusage.HappenedAtGTE(startTime)).
		WhereOn(!endTime.IsZero(), externalmodelusage.HappenedAtLTE(endTime))
	total, err := q.Count(ctx)
	if err != nil {
		return nil, 0, err
	}

	externalModelUsages, err := q.Paginate(ctx).Order(externalmodelusage.ByID(sql.OrderDesc())).All(ctx)
	if err != nil {
		return nil, 0, err
	}

	return conv.ExternalModelUsage.FromEnts(externalModelUsages), int64(total), nil
}

func (repo *externalModelRepo) QueryQuestionTagDistribution(ctx context.Context, startTime, endTime time.Time) ([]*biz.Classification, error) {
	var results []*struct {
		QuestionTag string `sql:"question_tag"`
		Count       int64  `sql:"count"`
	}

	err := repo.data.db.ExternalModelUsage(ctx).
		Query().
		Where(externalmodelusage.QuestionTagNEQ("")).
		WhereOn(!startTime.IsZero(), externalmodelusage.HappenedAtGTE(startTime)).
		WhereOn(!endTime.IsZero(), externalmodelusage.HappenedAtLTE(endTime)).
		GroupBy(externalmodelusage.FieldQuestionTag).
		Aggregate(ent.Count()).
		Scan(ctx, &results)
	if err != nil {
		return nil, err
	}

	var classifications []*biz.Classification
	for _, result := range results {
		classification := &biz.Classification{
			Name:  result.QuestionTag,
			Count: result.Count,
		}
		classifications = append(classifications, classification)
	}

	return classifications, nil
}

func (repo *externalModelRepo) QueryUploadFileTypesDistribution(ctx context.Context, startTime, endTime time.Time) ([]*biz.FileType, error) {
	q := `
SELECT mt As name, COUNT(*) AS count
FROM external_model_usage
CROSS JOIN LATERAL unnest(mime_types) AS mt
WHERE mime_types IS NOT NULL
AND mt <> ''
AND happened_at >= $1
AND happened_at <= $2
GROUP BY mt
ORDER BY count DESC LIMIT 5;
`
	rows, err := repo.data.db.Query(ctx, q, startTime, endTime)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var fileTypes []*biz.FileType
	for rows.Next() {
		var fileType biz.FileType
		if err := rows.Scan(&fileType.Name, &fileType.Count); err != nil {
			return nil, err
		}
		fileTypes = append(fileTypes, &fileType)
	}

	return fileTypes, nil
}

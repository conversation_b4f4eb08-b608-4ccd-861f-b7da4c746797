package data

import (
	"context"
	"github.com/lib/pq"
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/aiagentsecuritypolicy"
	"go.uber.org/zap"
)

type aiAgentSecurityPolicyRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (a aiAgentSecurityPolicyRepo) Get(ctx context.Context, id int64) (*biz.AiAgentSecurityPolicy, error) {
	policy, err := a.data.db.AiAgentSecurityPolicy(ctx).Get(ctx, id)
	if err != nil {
		return nil, err
	}
	return conv.AiAgentSecurityPolicy.FromEnt(policy), nil
}

func (a aiAgentSecurityPolicyRepo) Create(ctx context.Context, policies []*biz.AiAgentSecurityPolicy) ([]*biz.AiAgentSecurityPolicy, error) {
	if len(policies) == 0 {
		return nil, nil
	}
	creates := lox.Map(policies, func(item *biz.AiAgentSecurityPolicy) *ent.AiAgentSecurityPolicyCreate {
		return a.data.db.AiAgentSecurityPolicy(ctx).Create().
			SetAgentID(item.AgentID).
			SetName(item.Name).
			SetPolicyCategory(int64(item.PolicyCategory)).
			SetRiskLevel(int64(item.RiskLevel)).
			SetPolicies((*pq.StringArray)(&item.Policies)).
			SetEnabled(item.Enabled).
			SetHitAction(int64(item.HitAction)).
			SetHitResponse(item.HitResponse).
			SetUpdatedBy(item.UpdatedBy)
	})

	securityPolicies, err := a.data.db.AiAgentSecurityPolicy(ctx).CreateBulk(creates...).Save(ctx)
	if err != nil {
		a.log.Error("failed to create ai agent security policies", zap.Error(err))
		return nil, err
	}
	return conv.AiAgentSecurityPolicy.FromEnts(securityPolicies), nil
}

func (a aiAgentSecurityPolicyRepo) ListByAgentID(ctx context.Context, agentID int64) ([]*biz.AiAgentSecurityPolicy, error) {
	policies, err := a.data.db.AiAgentSecurityPolicy(ctx).Query().Where(aiagentsecuritypolicy.AgentIDEQ(agentID)).All(ctx)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, nil
		}
		a.log.Error("failed to list ai agent security policies by agent ID", zap.Error(err))
		return nil, err
	}
	return conv.AiAgentSecurityPolicy.FromEnts(policies), nil
}

func (a aiAgentSecurityPolicyRepo) Update(ctx context.Context, policies *biz.AiAgentSecurityPolicy) (*biz.AiAgentSecurityPolicy, error) {
	if policies == nil {
		return nil, nil
	}
	update := a.data.db.AiAgentSecurityPolicy(ctx).UpdateOneID(policies.ID).
		SetAgentID(policies.AgentID).
		SetName(policies.Name).
		SetPolicyCategory(int64(policies.PolicyCategory)).
		SetRiskLevel(int64(policies.RiskLevel)).
		SetPolicies((*pq.StringArray)(&policies.Policies)).
		SetEnabled(policies.Enabled).
		SetHitAction(int64(policies.HitAction)).
		SetHitResponse(policies.HitResponse)
	save, err := update.Save(ctx)
	if err != nil {
		a.log.Error("failed to update ai agent security policy", zap.Error(err))
		return nil, err
	}
	return conv.AiAgentSecurityPolicy.FromEnt(save), nil
}

func (a aiAgentSecurityPolicyRepo) Delete(ctx context.Context, id int64) error {
	if id <= 0 {
		return nil
	}
	err := a.data.db.AiAgentSecurityPolicy(ctx).DeleteOneID(id).Exec(ctx)
	if err != nil {
		a.log.Error("failed to delete ai agent security policy", zap.Error(err))
		return err
	}
	return nil
}

func NewAiAgentSecurityPolicyRepo(data *Data, logger *zapadapter.Logger) biz.AiAgentSecurityPolicyRepo {
	return &aiAgentSecurityPolicyRepo{
		data: data,
		log:  logger,
	}
}

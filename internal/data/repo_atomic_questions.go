package data

import (
	"context"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz/conv"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/atomicquestions"
)

type atomicQuestionsRepo struct {
	data *Data
	log  *zapadapter.Logger
}

func (a atomicQuestionsRepo) GetListByFileRelationIDAndEmptyQuestion(ctx context.Context, fileRelationID int64) ([]*biz.AtomicQuestions, error) {
	atomicQuestions, err := a.data.db.AtomicQuestions(ctx).Query().Where(atomicquestions.FileRelationIDEQ(fileRelationID)).Where(atomicquestions.IsHandleEQ(false)).All(ctx)
	if err != nil {
		return nil, err
	}
	return conv.AtomicQuestion.FromEnts(atomicQuestions), nil
}

func (a atomicQuestionsRepo) GetListByFileRelationID(ctx context.Context, fileRelationID int64) ([]*biz.AtomicQuestions, error) {
	atomicQuestions, err := a.data.db.AtomicQuestions(ctx).Query().Where(atomicquestions.FileRelationIDEQ(fileRelationID)).All(ctx)
	if err != nil {
		return nil, err
	}
	return conv.AtomicQuestion.FromEnts(atomicQuestions), nil
}

func NewAtomicQuestionsRepo(data *Data, logger *zapadapter.Logger) biz.AtomicQuestionsRepo {
	return &atomicQuestionsRepo{
		data: data,
		log:  logger,
	}
}

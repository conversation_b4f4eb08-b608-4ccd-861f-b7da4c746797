package kafka

import (
	"context"
	"errors"
	"fmt"

	"github.com/tx7do/kratos-transport/broker"
	"github.com/tx7do/kratos-transport/broker/kafka"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
)

type Client struct {
	b broker.Broker
}

func NewKafka(cfg *conf.Bootstrap) (*Client, error) {
	b := kafka.NewBroker(
		kafka.WithMaxWait(cfg.Kafka.ReadTimeout.AsDuration()),
		broker.WithAddress(cfg.Kafka.Address...),
		broker.WithCodec(cfg.Kafka.Codec),
		kafka.WithAsync(false),
	)

	if b == nil {
		return nil, errors.New("kafka broker is null")
	}

	_ = b.Init()

	if err := b.Connect(); err != nil {
		return nil, fmt.Errorf("connect kafka broker %w", err)
	}

	return &Client{b: b}, nil
}

func (k *Client) Close() error {
	return k.b.Disconnect()
}

// Publish message to kafka
func (k *Client) Publish(ctx context.Context, topic string, msg any, opts ...broker.PublishOption) error {
	return k.b.Publish(ctx, topic, msg, opts...)
}

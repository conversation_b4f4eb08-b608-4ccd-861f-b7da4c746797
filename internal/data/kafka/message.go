package kafka

import (
	"context"

	vfspb "gitlab.minum.cloud/BackendTeam/store/api/vfs"

	externalmodelpb "gitlab.minum.cloud/innovationteam/ai-web/api/externalmodel"
	knowledgebasepb "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
)

func (k *Client) SendAnalysisFileMessage(ctx context.Context, msg *knowledgebasepb.FileAnalysisImmediatelyMessage) error {
	return k.b.Publish(ctx, "fileAnalysisImmediately", msg)
}

// SendQuickCreateMessage
// Effect: -> store QuickCreate
func (k *Client) SendQuickCreateMessage(ctx context.Context, msg *vfspb.FileQuickCreateEvent) error {
	return k.b.Publish(ctx, "quickCreate", msg)
}

// SendProcessSheetKnowledgeBaseFileMessage
// Effect: -> store ProcessSheetKnowledgeBaseFile
func (k *Client) SendProcessSheetKnowledgeBaseFileMessage(ctx context.Context, msg *vfspb.ProcessSheetKnowledgeBaseFileEvent) error {
	return k.b.Publish(ctx, "processSheetKnowledgeBaseFile", msg)
}

// SendExternalModelQuestionTagMessage
// Effect: -> ai-web UpdateExternalModelUsageQuestionTag
func (k *Client) SendExternalModelQuestionTagMessage(ctx context.Context, msg *externalmodelpb.UpdateExternalModelUsageQuestionTag) error {
	return k.b.Publish(ctx, externalmodelpb.TopicExternalModelQuestionTag, msg)
}

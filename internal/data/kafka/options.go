package kafka

import (
	"hash"

	"github.com/tx7do/kratos-transport/broker"
	"github.com/tx7do/kratos-transport/broker/kafka"
)

// WithHerders 消息头
func WithHerders(headers map[string]any) broker.PublishOption {
	return kafka.WithHeaders(headers)
}

// WithMessageKey 消息键
func WithMessageKey(key []byte) broker.PublishOption {
	return kafka.WithMessageKey(key)
}

// WithMessageOffset 消息偏移
func WithMessageOffset(offset int64) broker.PublishOption {
	return kafka.WithMessageOffset(offset)
}

// WithLeastBytesBalancer LeastBytes负载均衡器
func WithLeastBytesBalancer() broker.PublishOption {
	return kafka.WithLeastBytesBalancer()
}

// WithRoundRobinBalancer RoundRobin负载均衡器，默认均衡器。
func WithRoundRobinBalancer() broker.PublishOption {
	return kafka.WithRoundRobinBalancer()
}

// WithHashBalancer Hash负载均衡器
func WithHashBalancer(hasher hash.Hash32) broker.PublishOption {
	return kafka.WithHashBalancer(hasher)
}

// WithReferenceHashBalancer ReferenceHash负载均衡器
func WithReferenceHashBalancer(hasher hash.Hash32) broker.PublishOption {
	return kafka.WithReferenceHashBalancer(hasher)
}

// WithCrc32Balancer CRC32负载均衡器
func WithCrc32Balancer(consistent bool) broker.PublishOption {
	return kafka.WithCrc32Balancer(consistent)
}

// WithMurmur2Balancer Murmur2负载均衡器
func WithMurmur2Balancer(consistent bool) broker.PublishOption {
	return kafka.WithMurmur2Balancer(consistent)
}

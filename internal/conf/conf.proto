syntax = "proto3";
package kratos.api;

import "google/protobuf/duration.proto";

option go_package = "gitlab.minum.cloud/BackendTeam/ai-web/internal/conf;conf";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  Kafka kafka = 3;
  Job job = 4;
  VS vs = 5;
  Agent agent = 6;
  repeated ExternalModelConfig external_model_configs = 7;
  KnowledgeGraph knowledge_graph = 8;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
  string internalGatewayAddr = 3;
  // 文件上传最大文件字数限制
  int64 maxWordCount = 4;
  string skyWalkingQueryAddress = 5;
  string victoria_metrics_url = 6;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string addr = 1;
    string password = 2;
    int64 db = 3;
    google.protobuf.Duration read_timeout = 4;
    google.protobuf.Duration write_timeout = 5;
  }
  Database database = 1;
  Redis redis = 2;
}

message Kafka {
  repeated string address = 1;
  string codec = 2;
  google.protobuf.Duration read_timeout = 3;
}

message Job {
  string server_host = 1;
  int64 server_port = 2;
  string executor_name = 3;
  int64 executor_port = 4;
  string access_token = 5;
}

message VS {
  string host = 1;
  int64 port = 2;
  string collection_name = 3;
  string sheet_collection_name = 5;
  string atomic_question_collection_name = 4;
}

message Agent {
  bool mix_external_model = 1;
}

message ExternalModelConfig {
  // 解析类型 file, chat
  string parse_type = 1;
  // 模型名称 通义千问 腾讯元宝 豆包 deepseek
  string model_name = 2;
  // host
  string host = 3;
  // schema前缀
  string pre_schema = 4;
  // 解析规则 例 message.content.text
  string parse_rule = 5;
  // session 解析规则, 仅在 parse_type 为 chat 时存在，例 conversation_id
  string session_parse_rule = 6;
}

message KnowledgeGraph {
  bool enable = 1;
}

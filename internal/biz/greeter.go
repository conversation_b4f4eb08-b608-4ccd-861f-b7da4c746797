package biz

import (
	"context"

	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"go.uber.org/zap"
)

// ErrUserNotFound is user not found.

// Greeter is a Greeter model.
type Greeter struct {
	Hello string
}

// GreeterRepo is a Greater repo.
type GreeterRepo interface {
	Save(context.Context, *Greeter) (*Greeter, error)
	Update(context.Context, *Greeter) (*Greeter, error)
	FindByID(context.Context, int64) (*Greeter, error)
	ListByHello(context.Context, string) ([]*Greeter, error)
	ListAll(context.Context) ([]*Greeter, error)
}

// GreeterUsecase is a Greeter usecase.
type GreeterUsecase struct {
	repo GreeterRepo
	log  *zapadapter.Logger
	tm   DbTransaction
}

// NewGreeterUsecase new a Greeter usecase.
func NewGreeterUsecase(repo GreeterRepo, tm DbTransaction, logger *zapadapter.Logger) *GreeterUsecase {
	return &GreeterUsecase{
		repo: repo,
		log:  logger,
		tm:   tm,
	}
}

// <PERSON><PERSON><PERSON><PERSON><PERSON> creates a Greeter, and returns the new Greeter.
func (uc *GreeterUsecase) CreateGreeter(ctx context.Context, g *Greeter) (*Greeter, error) {
	uc.log.Info("create greeter", zap.String("hello", g.Hello))
	var (
		greeter *Greeter
		err     error
	)
	return greeter, uc.tm.InTx(ctx, func(ctx context.Context) error {
		greeter, err = uc.repo.Save(ctx, g)
		return err
	})
}

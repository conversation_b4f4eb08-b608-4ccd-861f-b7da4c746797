package biz

import "context"

type AtomicQuestions struct {
	ID             int64    `json:"id"`
	FileRelationID int64    `json:"fileRelationID"`
	ChunkIndex     int64    `json:"chunk_index"`
	ChunkSize      int64    `json:"chunk_size"`
	Index          int64    `json:"index"`
	Question       []string `json:"question"`
	EntityTag      string   `json:"entityTag"`
	PreEntityTag   string   `json:"preEntityTag"`
}

type AtomicQuestionsRepo interface {
	GetListByFileRelationID(context.Context, int64) ([]*AtomicQuestions, error)
	GetListByFileRelationIDAndEmptyQuestion(context.Context, int64) ([]*AtomicQuestions, error)
}

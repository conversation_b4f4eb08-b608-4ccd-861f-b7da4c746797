package biz

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"mime/multipart"
	"sort"
	"strconv"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	"gitlab.minum.cloud/BackendTeam/admin/api/message"
	"gitlab.minum.cloud/BackendTeam/admin/api/user"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	webAgent "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/schema/mixin"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/oss"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

type UserInfo struct {
	ID        int64  `json:"id"`
	Username  string `json:"username"`
	AvatarUrl string `json:"avatar_url"`
}

type DeptInfo struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

type Agent struct {
	ID                   int64                    `json:"id"`
	Name                 string                   `json:"name"`
	Description          string                   `json:"description"`
	Avatar               string                   `json:"avatar"`
	AvatarUrl            string                   `json:"avatar_url"`
	ClickedAvatar        string                   `json:"clicked_avatar"`
	ClickedAvatarUrl     string                   `json:"clicked_avatar_url"`
	WelcomeMsg           string                   `json:"welcome_msg"`
	FallbackMsg          string                   `json:"fallback_msg"`
	OwnerID              int64                    `json:"owner_id"`
	OwnerName            string                   `json:"owner_name"`
	OwnerAvatar          string                   `json:"owner_avatar"`
	VisibilityType       webAgent.VisibilityType  `json:"visibility_type"`
	VisibleToUser        []int64                  `json:"visible_to_user"`
	VisibleToDept        []int64                  `json:"visible_to_dept"`
	KnowledgeBaseIds     []int64                  `json:"knowledge_base_ids"`
	Schema               string                   `json:"schema"`
	IsPublic             bool                     `json:"is_public"`
	IsEnabled            bool                     `json:"is_enabled"`
	VisibleUsers         []*UserInfo              `json:"visible_users"`
	VisibleDepts         []*DeptInfo              `json:"visible_depts"`
	ManageableUsers      []*UserInfo              `json:"manageable_users"`
	IsRefFiles           bool                     `json:"is_ref_files"`
	ModelType            int64                    `json:"model_type"`
	ModelID              int64                    `json:"model_id"`
	ModelName            string                   `json:"model_name"`
	ModelDetailID        int64                    `json:"model_detail_id"`
	ModelAvatar          string                   `json:"model_avatar"`
	UseCount             int64                    `json:"use_count"`
	KnowledgeBaseType    int64                    `json:"knowledge_base_type"`
	InternetSearch       bool                     `json:"internet_search"`
	CanInternetSearch    bool                     `json:"can_internet_search"`
	AgentType            int64                    `json:"agent_type"`
	Thinking             bool                     `json:"thinking"`
	ThinkingModelID      int64                    `json:"thinking_model_id"`
	ThinkingModelName    string                   `json:"thinking_model_name"`
	ThinkingModelAvatar  string                   `json:"thinking_model_avatar"`
	RoleSetting          string                   `json:"role_setting"`
	ThinkingEnableStatus int64                    `json:"thinking_enable_status"`
	SecurityPolicies     []*AiAgentSecurityPolicy `json:"security_policies"`
	UploadFile           bool                     `json:"upload_file"`
	SemanticCache        bool                     `json:"semantic_cache"`
	UpdatedAt            time.Time                `json:"updated_at"`
	ModelDetailName      string                   `json:"model_detail_name"`
	ManageableToUser     []int64                  `json:"manageable_to_user"`
}

type AgentAvatar struct {
	Avatar           string
	AvatarUrl        string
	ClickedAvatar    string
	ClickedAvatarUrl string
}

type AgentRepo interface {
	Save(context.Context, *Agent) (*Agent, error)
	Update(context.Context, *Agent) (*Agent, error)
	FindByIDs(context.Context, []int64) ([]*Agent, error)
	FindByID(context.Context, int64) (*Agent, error)
	FindByUserID(context.Context, int64) ([]*Agent, error)
	Page(ctx context.Context, userID int64, deptID int64, showOnClient bool, modelType int64, isMine bool, agentType []int64, agentName string, isCommonlyUsed bool) ([]*Agent, int64, error)
	Delete(ctx context.Context, id int64) error
	ExistKnowledgeBaseAgent(ctx context.Context, knowledgeBaseID int64) (bool, error)
	Count(ctx context.Context) (int64, error)
	UpdateUseCount(ctx context.Context, agentID int64, useCount int64) error
	GetAllAgent(ctx context.Context, modelType int64) ([]*Agent, error)
	FindByModelId(ctx context.Context, modelID int64) ([]*Agent, error)
}

type UserAgentOrder struct {
	UserID  int64
	AgentID int64
	Index   int64
}

type UserAgentOrderRepo interface {
	Save(context.Context, []*UserAgentOrder) error
	GetUserAgentOrder(ctx context.Context, userID int64) ([]*UserAgentOrder, error)
}

type DefaultAgentAvatarRepo interface {
	GetDefaultAvatars(ctx context.Context, avatarType int64) ([]*AgentAvatar, error)
}

type AgentUsecase struct {
	repo                      AgentRepo
	chatItemRepo              ChatItemRepo
	chatRepo                  ChatRepo
	aimodelRepo               AiModelRepo
	avatarRepo                DefaultAgentAvatarRepo
	log                       *zapadapter.Logger
	tm                        DbTransaction
	ossClient                 *oss.Client
	rpc                       rpc.Client
	kbRepo                    KnowledgeBaseRepo
	kbfRepo                   KnowledgeBaseFileRepo
	mu                        *sync.Mutex
	redis                     *redis.Client
	userAgentOrderRepo        UserAgentOrderRepo
	aiAgentSecurityPolicyRepo AiAgentSecurityPolicyRepo
	aiAgentSecurityLogRepo    AiAgentSecurityLogRepo
}

func (u *AgentUsecase) CreateAgent(ctx context.Context, saveAgent *Agent, securityPolicies []*AiAgentSecurityPolicy) (*Agent, error) {

	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, errors.New("no auth")
	}

	var agent *Agent
	var err error

	err = u.tm.InTx(ctx, func(ctx context.Context) error {
		agent, err = u.repo.Save(ctx, saveAgent)
		if err != nil {
			return err
		}
		if len(securityPolicies) > 0 {
			for i := range securityPolicies {
				securityPolicies[i].AgentID = agent.ID
				securityPolicies[i].UpdatedBy = session.UserID
			}
			_, err = u.aiAgentSecurityPolicyRepo.Create(ctx, securityPolicies)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		u.log.Error("create agent failed", zap.Error(err))
		return nil, err
	}

	messageUserIDs := make([]int64, 0)

	if agent.VisibilityType == webAgent.VisibilityType_VisibilityTypeAll {
		// 查询所有用户id
		allUserIDs, err := u.rpc.GetAllUserIDs(ctx, &user.GetAllUserIDsRequest{})
		if err != nil {
			return nil, err
		}
		for _, userID := range allUserIDs.GetUserIDs() {
			messageUserIDs = append(messageUserIDs, userID)
		}
	} else {
		// 查询所有可见用户id
		for _, userID := range agent.VisibleToUser {
			messageUserIDs = append(messageUserIDs, userID)
		}
		// 查询所有可见部门id
		for _, deptID := range agent.VisibleToDept {
			users, err := u.rpc.GetUserIDsByDept(ctx, &user.GetUserIDsByDeptRequest{DeptID: deptID})
			if err != nil {
				return nil, err
			}
			for _, userID := range users.GetUserIDs() {
				messageUserIDs = append(messageUserIDs, userID)
			}
		}
	}

	//duplicate userIDs
	messageUserIDs = lo.Uniq(messageUserIDs)

	msg := message.SendMessageRequest{
		Message: &message.CommonMessage{
			TenantID: 1,
			UserIDs:  messageUserIDs,
			Title:    "智能体创建",
			Content:  "智能体创建",
			Remark:   "",
			MetaData: &message.CommonMessage_AgentCreatedData{
				AgentCreatedData: &message.AgentCreatedData{
					AgentID:   agent.ID,
					AgentName: agent.Name,
				},
			},
		},
	}
	if _, err := u.rpc.SendMessage(ctx, &msg); err != nil {
		u.log.Warn("send message failed", zap.Error(err))
	}

	return agent, nil
}

func (u *AgentUsecase) UpdateAgent(ctx context.Context, updateAgent *Agent, upsertSecurityPolices []*AiAgentSecurityPolicy, deletedSecurityPolicyIDs []int64) (*Agent, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, errors.New("no auth")
	}
	agents, err := u.repo.FindByIDs(ctx, []int64{updateAgent.ID})
	if err != nil {
		return nil, err
	}
	if len(agents) == 0 {
		return nil, errors.New("agent not found")
	}
	if agents[0].OwnerID != session.UserID && !lo.Contains(agents[0].ManageableToUser, session.UserID) {
		return nil, errors.New("no auth")
	}
	var agent *Agent
	err = u.tm.InTx(ctx, func(ctx context.Context) error {
		agent, err = u.repo.Update(ctx, updateAgent)
		if err != nil {
			u.log.Error("update agent failed", zap.Error(err))
			return err
		}

		for i := range upsertSecurityPolices {
			upsertSecurityPolices[i].AgentID = agent.ID
			upsertSecurityPolices[i].UpdatedBy = session.UserID
			if upsertSecurityPolices[i].ID == 0 {
				_, err = u.aiAgentSecurityPolicyRepo.Create(ctx, []*AiAgentSecurityPolicy{upsertSecurityPolices[i]})
				if err != nil {
					u.log.Error("create security policy failed", zap.Error(err))
					return err
				}
			} else if upsertSecurityPolices[i].ID > 0 {
				_, err = u.aiAgentSecurityPolicyRepo.Update(ctx, upsertSecurityPolices[i])
				if err != nil {
					u.log.Error("update security policy failed", zap.Error(err))
					return err
				}
			}
		}
		if len(deletedSecurityPolicyIDs) > 0 {
			for _, id := range deletedSecurityPolicyIDs {
				err = u.aiAgentSecurityPolicyRepo.Delete(ctx, id)
				if err != nil {
					u.log.Error("delete security policy failed", zap.Error(err))
					return err
				}
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	return agent, nil
}

func (u *AgentUsecase) UpdateAgentUseCount(ctx context.Context, agentID int64) error {
	u.mu.Lock()
	defer u.mu.Unlock()
	agent, err := u.repo.FindByIDs(ctx, []int64{agentID})
	if err != nil {
		return err
	}
	err = u.repo.UpdateUseCount(ctx, agentID, agent[0].UseCount+1)
	if err != nil {
		return err
	}
	return nil
}

func (u *AgentUsecase) DeleteAgent(ctx context.Context, id int64) error {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return errors.New("no auth")
	}
	agents, err := u.repo.FindByIDs(ctx, []int64{id})
	if err != nil {
		return err
	}
	if len(agents) == 0 {
		return errors.New("agent not found")
	}
	if agents[0].OwnerID != session.UserID {
		return errors.New("no auth")
	}

	count, err := u.repo.Count(ctx)
	if err != nil {
		return err
	}
	if count <= 1 {
		return errors.New("至少保留一个智能助手")
	}
	return u.repo.Delete(ctx, id)
}

func (u *AgentUsecase) GetAgent(ctx context.Context, ids []int64) ([]*Agent, error) {
	agents, err := u.repo.FindByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}
	for i, agent := range agents {
		agents[i].AvatarUrl = u.ossClient.GetObjectURL(ctx, agent.Avatar)
		if agent.ThinkingModelID != 0 {
			aiModel, err := u.aimodelRepo.Get(ctx, agent.ThinkingModelID)
			if err != nil {
				u.log.Error(fmt.Sprintf("get aiModel failed, modelID: %d", agent.ThinkingModelID), zap.Error(err))
				continue
			}
			agents[i].ThinkingModelName = aiModel.ModelName
		}
		securityPolicies, err := u.aiAgentSecurityPolicyRepo.ListByAgentID(ctx, agent.ID)
		if err != nil {
			if ent.IsNotFound(err) {
				agents[i].SecurityPolicies = nil
			} else {
				u.log.Error("get security policies failed", zap.Error(err))
				return nil, err
			}
		}
		if securityPolicies != nil && len(securityPolicies) > 0 {
			for j := range securityPolicies {
				if securityPolicies[j].UpdatedBy > 0 {
					user, err := u.rpc.GetUser(ctx, &user.GetUserRequest{Ids: []int64{securityPolicies[j].UpdatedBy}, HasDeleted: true})
					if err != nil {
						u.log.Error("get user failed", zap.Error(err))
						return nil, err
					}
					if len(user.GetItems()) > 0 {
						securityPolicies[j].UpdatedByName = user.GetItems()[0].NickName
					}
				} else {
					securityPolicies[j].UpdatedByName = "未知"
				}
			}
			agents[i].SecurityPolicies = securityPolicies
		} else {
			agents[i].SecurityPolicies = []*AiAgentSecurityPolicy{}
		}
	}
	return agents, nil
}

func (u *AgentUsecase) GetAllAgent(ctx context.Context, modelType int64) ([]*Agent, error) {
	agents, err := u.repo.GetAllAgent(ctx, modelType)
	if err != nil {
		return nil, err
	}
	for i := range agents {
		if agents[i].ModelType == int64(webAgent.ModelType_ModelTypeGateway) {
			model, err := u.aimodelRepo.Get(ctx, agents[i].ModelID)
			if err != nil {
				if ent.IsNotFound(err) {
					continue
				}
				return nil, err
			}
			agents[i].Name = model.ModelName
		}
	}
	return agents, nil
}

func (u *AgentUsecase) PageAgents(ctx context.Context, showOnClient bool, modelType int64, isMine bool, agentCategoryType int64, agentName string) ([]*Agent, int64, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, 0, errors.New("no auth")
	}
	user, err := u.rpc.GetUser(ctx, &user.GetUserRequest{Ids: []int64{session.UserID}, HasDeleted: true})
	if err != nil {
		return nil, 0, err
	}
	if len(user.GetItems()) == 0 {
		return nil, 0, errors.New("no user")
	}
	agentType := make([]int64, 0)
	if agentCategoryType == int64(webAgent.AgentCategory_AgentCategoryAiTools) {
		agentType = append(agentType, int64(webAgent.AgentType_AgentTypeContractReview))
	} else if agentCategoryType == int64(webAgent.AgentCategory_AgentCategoryRag) {
		agentType = append(agentType, int64(webAgent.AgentType_AgentTypeBaseRag))
	} else if agentCategoryType == int64(webAgent.AgentCategory_AgentCategoryDeepRag) {
		agentType = append(agentType, int64(webAgent.AgentType_AgentTypeDeepSearchRag))
	} else if agentCategoryType == int64(webAgent.AgentCategory_AgentCategoryBaseAndDeepSearchRag) {
		agentType = append(agentType, int64(webAgent.AgentType_AgentTypeBaseRag))
		agentType = append(agentType, int64(webAgent.AgentType_AgentTypeDeepSearchRag))
	}
	page, total, err := u.repo.Page(ctx, session.UserID, user.GetItems()[0].DeptID, showOnClient, modelType, isMine, agentType, agentName, agentCategoryType == int64(webAgent.AgentCategory_AgentCategoryCommonlyUsed))
	if err != nil {
		return nil, 0, err
	}

	for i, agent := range page {
		page[i].AvatarUrl = u.ossClient.GetObjectURL(ctx, agent.Avatar)
		if agent.ClickedAvatar != "" {
			page[i].ClickedAvatarUrl = u.ossClient.GetObjectURL(ctx, agent.ClickedAvatar)
		}
	}

	//排序
	userAgentOrder, err := u.userAgentOrderRepo.GetUserAgentOrder(ctx, session.UserID)
	if err != nil {
		return nil, 0, err
	}

	// 根据userAgentOrder的index排序，如果没有则保持原顺序
	if userAgentOrder != nil && len(userAgentOrder) > 0 {
		// 创建一个映射，存储每个agentID对应的排序索引
		orderMap := make(map[int64]int64)
		for _, order := range userAgentOrder {
			orderMap[order.AgentID] = order.Index
		}

		// 使用自定义排序
		sort.SliceStable(page, func(i, j int) bool {
			// 获取两个agent的排序索引
			indexI, existsI := orderMap[page[i].ID]
			indexJ, existsJ := orderMap[page[j].ID]

			// 如果两个agent都在排序列表中，按照索引排序
			if existsI && existsJ {
				return indexI < indexJ
			}
			// 如果只有i在排序列表中，i应该排在前面
			if existsI {
				return true
			}
			// 如果只有j在排序列表中，j应该排在前面
			if existsJ {
				return false
			}
			// 如果都不在排序列表中，保持原顺序
			return i < j
		})
	}

	return page, total, nil
}

func (u *AgentUsecase) UploadAvatar(ctx context.Context, file multipart.File, fileHeader *multipart.FileHeader) (string, string, error) {
	key, err := u.ossClient.UploadAiAgentAvatar(ctx, file, fileHeader)
	if err != nil {
		return "", "", err
	}
	url := u.ossClient.GetObjectURL(ctx, key)

	return key, url, nil
}

func (u *AgentUsecase) GetDefaultAgentAvatars(ctx context.Context, avatarType int64) ([]*AgentAvatar, error) {
	avatars, err := u.avatarRepo.GetDefaultAvatars(ctx, avatarType)
	if err != nil {
		return nil, err
	}
	for i, avatar := range avatars {
		avatars[i].AvatarUrl = u.ossClient.GetObjectURL(ctx, avatar.Avatar)
		avatars[i].ClickedAvatarUrl = u.ossClient.GetObjectURL(ctx, avatar.ClickedAvatar)
	}
	return avatars, nil
}

func (u *AgentUsecase) GetFilePermissionByAgent(ctx context.Context, agentID, fileRelationID int64) (bool, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return false, errors.New("no auth")
	}
	userID := session.UserID
	users, err := u.rpc.GetUser(ctx, &user.GetUserRequest{Ids: []int64{userID}, HasDeleted: true})
	if err != nil {
		return false, err
	}
	if users == nil || len(users.GetItems()) == 0 {
		return false, errors.New("no user")
	}
	// find permission of agent
	agents, err := u.repo.FindByIDs(ctx, []int64{agentID})
	if err != nil {
		return false, err
	}
	if len(agents) == 0 {
		return false, errors.New("agent not found")
	}
	if agents[0].VisibilityType == webAgent.VisibilityType_VisibilityTypeUser {
		if lo.Contains(agents[0].VisibleToUser, session.UserID) == false && lo.Contains(agents[0].VisibleToDept, users.GetItems()[0].DeptID) == false && session.UserID != agents[0].OwnerID {
			return false, nil
		}
	}

	// find file in knowledge base
	kbIDs := agents[0].KnowledgeBaseIds
	kbFiles, err := u.kbfRepo.GetFileWithKnowledgeBaseIDs(ctx, fileRelationID, kbIDs)
	if err != nil {
		return false, err
	}
	return len(kbFiles) > 0, nil

}

func (u *AgentUsecase) InternalModelChatPage(ctx context.Context, chatItemID int64, userName, deptName string, startTime, endTime time.Time, searchModel bool, modelID, agentID int64, searchfile []string, noRefFiles bool, class string) ([]*webAgent.InternalModelChatPageReplyItem, int64, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, 0, ErrAuthSessionNotFound
	}
	ctx = mixin.SkipSoftDelete(ctx)
	var queryUserIDs []int64
	if userName != "" || deptName != "" {
		userIDsReply, err := u.rpc.GetUserIDByUserNameOrDeptName(ctx, &user.GetUserIDByUserNameOrDeptNameRequest{
			UserName: userName,
			DeptName: deptName,
			TenantID: session.TenantID,
		})
		if err != nil {
			return nil, 0, err
		}
		queryUserIDs = userIDsReply.UserIDs
		if len(queryUserIDs) == 0 {
			return nil, 0, nil
		}
	}

	chatItems, total, err := u.chatItemRepo.PageUserQuestion(ctx, chatItemID, queryUserIDs, startTime, endTime, searchModel, modelID, agentID, searchfile, noRefFiles, class)
	if err != nil {
		return nil, 0, err
	}

	userIDs := lox.Map(chatItems, func(item *ChatItemInfo) int64 {
		return item.ObjectID
	})
	users, err := u.rpc.GetDeptUser(ctx, &user.GetDeptUserRequest{UserIDs: userIDs})
	if err != nil {
		return nil, 0, err
	}

	userMap := lo.SliceToMap(users.Items, func(item *user.GetDeptUserReplyItem) (int64, *user.GetDeptUserReplyItem) {
		return item.UserID, item
	})

	items := lox.Map(chatItems, func(item *ChatItemInfo) *webAgent.InternalModelChatPageReplyItem {
		var (
			username, userAvatar, deptName, agentName, agentAvatar string
			userID, userDeptID                                     int64
		)

		userInfo := userMap[item.ObjectID]
		if userInfo != nil {
			username = userInfo.NickName
			userAvatar = userInfo.AvatarURL
			userID = userInfo.UserID
			deptName = userInfo.DeptName
			userDeptID = userInfo.DeptID
		}
		agentName = item.AgentName
		agentAvatar = item.AgentAvatar

		if len(agentAvatar) > 0 {
			agentAvatar = u.ossClient.GetObjectURL(ctx, agentAvatar)
		}

		return &webAgent.InternalModelChatPageReplyItem{
			Message:     item.Message,
			Username:    username,
			UserID:      userID,
			UserAvatar:  userAvatar,
			DeptName:    deptName,
			DeptID:      userDeptID,
			AgentName:   agentName,
			AgentAvatar: agentAvatar,
			CreatedAt:   timestamppb.New(item.CreateAT),
			PcName:      item.PcName,
			Id:          item.ID,
			Class:       item.SecondaryClassification,
			RefFiesText: item.RefFiles,
		}
	})

	return items, total, nil
}

func (u *AgentUsecase) SaveGlobalAgentWhiteList(ctx context.Context, userIDs []int64) error {
	AgentWhiteListKey := "global_agent_white_list"
	userIDsStrs := make([]string, len(userIDs))
	for i, id := range userIDs {
		userIDsStrs[i] = strconv.FormatInt(id, 10)
	}
	err := u.redis.Del(ctx, AgentWhiteListKey).Err()
	if err != nil {
		return err
	}
	return u.redis.SAdd(ctx, AgentWhiteListKey, userIDsStrs).Err()
}

func (u *AgentUsecase) GetGlobalAgentWhiteList(ctx context.Context) ([]*UserInfo, error) {
	AgentWhiteListKey := "global_agent_white_list"
	results, err := u.redis.SMembers(ctx, AgentWhiteListKey).Result()
	if err != nil {
		return nil, err
	}
	ids := make([]int64, len(results))
	for i, id := range results {
		ids[i], err = strconv.ParseInt(id, 10, 64)
		if err != nil {
			return nil, err
		}
	}
	users, err := u.rpc.GetUser(ctx, &user.GetUserRequest{Ids: ids, HasDeleted: true})
	if err != nil {
		return nil, err
	}
	if len(users.GetItems()) == 0 {
		return nil, nil
	}
	return lox.Map(users.GetItems(), func(item *user.UserInfo) *UserInfo {
		return &UserInfo{
			ID:        item.Id,
			Username:  item.NickName,
			AvatarUrl: item.AvatarURL,
		}
	}), nil
}

func (u *AgentUsecase) SaveAgentOrders(ctx context.Context, orders []*UserAgentOrder) error {
	if len(orders) == 0 {
		return nil
	}
	err := u.userAgentOrderRepo.Save(ctx, orders)
	return err
}

func (u *AgentUsecase) TransferAgent(ctx context.Context, agentIDs []int64, toUserID int64) error {
	agents, err := u.repo.FindByIDs(ctx, agentIDs)
	if err != nil {
		return err
	}
	if len(agents) == 0 {
		return errors.New("agent not found")
	}

	err = u.tm.InTx(ctx, func(ctx context.Context) error {
		for i := range agents {
			agents[i].OwnerID = toUserID
			_, err = u.repo.Update(ctx, agents[i])
		}
		return nil
	})

	return err
}

func (u *AgentUsecase) GetUserAgentsAndKnowledgeBases(ctx context.Context, userID int64) ([]*Agent, []*KnowledgeBase, error) {
	agents, err := u.repo.FindByUserID(ctx, userID)
	if err != nil {
		return nil, nil, err
	}
	for i := range agents {
		agents[i].AvatarUrl = u.ossClient.GetObjectURL(ctx, agents[i].Avatar)
	}

	knowledgeBases, err := u.kbRepo.GetKnowledgeBasesByUser(ctx, userID)
	if err != nil {
		return nil, nil, err
	}

	return agents, knowledgeBases, nil
}

func NewAgentUsecase(repo AgentRepo,
	avatarRepo DefaultAgentAvatarRepo,
	log *zapadapter.Logger,
	tm DbTransaction,
	ossClient *oss.Client,
	rpc rpc.Client,
	kbRepo KnowledgeBaseRepo,
	kbfRepo KnowledgeBaseFileRepo,
	itemRepo ChatItemRepo,
	chatRepo ChatRepo,
	aimodelRepo AiModelRepo,
	redis *redis.Client,
	userAgentOrderRepo UserAgentOrderRepo,
	aiAgentSecurityPolicyRepo AiAgentSecurityPolicyRepo,
	aiAgentSecurityLogRepo AiAgentSecurityLogRepo,
) *AgentUsecase {
	mu := &sync.Mutex{}
	return &AgentUsecase{repo: repo,
		avatarRepo:                avatarRepo,
		log:                       log,
		tm:                        tm,
		ossClient:                 ossClient,
		rpc:                       rpc,
		kbRepo:                    kbRepo,
		kbfRepo:                   kbfRepo,
		mu:                        mu,
		chatItemRepo:              itemRepo,
		chatRepo:                  chatRepo,
		aimodelRepo:               aimodelRepo,
		redis:                     redis,
		userAgentOrderRepo:        userAgentOrderRepo,
		aiAgentSecurityPolicyRepo: aiAgentSecurityPolicyRepo,
		aiAgentSecurityLogRepo:    aiAgentSecurityLogRepo,
	}
}

func (u *AgentUsecase) CreateAgentSecurityLogs(ctx context.Context, logs []*AiAgentSecurityLog) error {
	if len(logs) == 0 {
		return nil
	}
	_, err := u.aiAgentSecurityLogRepo.Create(ctx, logs)
	if err != nil {
		u.log.Error("create agent security logs failed", zap.Error(err))
		return err
	}
	return nil
}

func (u *AgentUsecase) PageAgentSecurityLogs(ctx context.Context, hitActions, riskLevel []int64, userName, deptName string, startTime, endTime time.Time) ([]*AiAgentSecurityLog, int64, error) {
	page, total, err := u.aiAgentSecurityLogRepo.Page(ctx, riskLevel, hitActions, userName, deptName, startTime, endTime)
	if err != nil {
		return nil, 0, err
	}
	for i := range page {
		user, err := u.rpc.GetUser(ctx, &user.GetUserRequest{
			Ids:        []int64{page[i].UserID},
			HasDeleted: true,
		})
		if err != nil {
			u.log.Error("get user info failed", zap.Error(err))
			continue
		}
		if len(user.GetItems()) == 0 {
			continue
		}
		page[i].UserAvatar = user.GetItems()[0].GetAvatarURL()
		agent, err := u.repo.FindByID(ctx, page[i].AgentID)
		if err != nil {
			u.log.Error("get agent info failed", zap.Error(err))
			continue
		}
		if agent != nil {
			page[i].AgentAvatar = u.ossClient.GetObjectURL(ctx, agent.Avatar)
		}
	}
	return page, total, nil
}

func (u *AgentUsecase) GetAgentSecurityLogByID(ctx context.Context, ID int64) (*AiAgentSecurityLog, error) {
	log, err := u.aiAgentSecurityLogRepo.Get(ctx, ID)
	if err != nil {
		u.log.Error("get agent security log by id failed", zap.Error(err))
		return nil, err
	}
	if log == nil {
		return nil, errors.New("agent security log not found")
	}
	user, err := u.rpc.GetUser(ctx, &user.GetUserRequest{
		Ids:        []int64{log.UserID},
		HasDeleted: true,
	})
	if err != nil {
		u.log.Error("get user info failed", zap.Error(err))
		return nil, err
	}
	if len(user.GetItems()) > 0 {
		log.UserAvatar = user.GetItems()[0].GetAvatarURL()
	}

	agent, err := u.repo.FindByID(ctx, log.AgentID)
	if err != nil {
		if ent.IsNotFound(err) {
			u.log.Warn("agent not found", zap.Int64("agentID", log.AgentID))
		} else {
			u.log.Error("get agent info failed", zap.Error(err))
			return nil, err
		}
	}
	if agent != nil {
		log.AgentAvatar = u.ossClient.GetObjectURL(ctx, agent.Avatar)
	}
	policies := make([]*AiAgentSecurityPolicy, 0)
	if len(log.HitPolicies) > 0 {
		for _, hitPolicy := range log.HitPolicies {
			var policy AiAgentSecurityPolicy
			err := json.Unmarshal([]byte(hitPolicy), &policy)
			if err != nil {
				u.log.Error("unmarshal hit policy failed", zap.Error(err))
				continue
			}
			policies = append(policies, &policy)
		}
	}
	log.HitPoliciesStructured = policies
	return log, nil
}

func (u *AgentUsecase) GetAgentSecurityLogCount(ctx context.Context) (highRiskCount, mediumRiskCount, lowRiskCount, blockCount, warningCount int64, err error) {
	high, mid, low, err := u.aiAgentSecurityLogRepo.GetRiskCount(ctx)
	if err != nil {
		u.log.Error("get agent security log risk count failed", zap.Error(err))
		return 0, 0, 0, 0, 0, err
	}
	block, warn, err := u.aiAgentSecurityLogRepo.GetHitActionCount(ctx)
	if err != nil {
		u.log.Error("get agent security log hit action count failed", zap.Error(err))
		return 0, 0, 0, 0, 0, err
	}
	return int64(high), int64(mid), int64(low), int64(block), int64(warn), nil
}

package conv

import (
	"context"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"

	"gitlab.minum.cloud/innovationteam/ai-web/api/rag"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"

	"gitlab.minum.cloud/BackendTeam/pkg/lox"
)

var ChatItem chatItem

type chatItem struct{}

func (c chatItem) FromEnt(_ context.Context, e *ent.AiChatItem) *biz.ChatItem {
	if e == nil {
		return nil
	}
	return &biz.ChatItem{
		ID:               e.ID,
		ChatID:           e.ChatID,
		ObjectID:         e.ObjectID,
		ObjectType:       int64(rag.ChatObjectType(e.ObjectType)),
		Message:          e.Message,
		RefFiles:         e.RefFiles,
		RoundID:          e.RoundID,
		CreateAT:         e.CreatedAt,
		PcName:           e.Pc<PERSON>,
		Reason:           e.Reason,
		AgreeStatus:      int64(e.AgreeStatus),
		HitAction:        pb.HitAction(e.HitAction),
		HitResponse:      e.HitResponse,
		HitContinueSend:  e.HitContinueSend,
		IsInternetSearch: e.IsInternetSearch,
		SuggestQuestions: []string(*e.SuggestQuestions),
	}
}

func (c chatItem) FromEnts(ctx context.Context, es []*ent.AiChatItem) []*biz.ChatItem {
	return lox.Map(es, func(e *ent.AiChatItem) *biz.ChatItem {
		return c.FromEnt(ctx, e)
	})
}

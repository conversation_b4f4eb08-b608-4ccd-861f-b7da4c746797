package conv

import (
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var AiAgentSecurityPolicy = aiAgentSecurityPolicy{}

type aiAgentSecurityPolicy struct{}

func (aiAgentSecurityPolicy) FromEnt(ent *ent.AiAgentSecurityPolicy) *biz.AiAgentSecurityPolicy {
	return &biz.AiAgentSecurityPolicy{
		ID:             ent.ID,
		AgentID:        ent.AgentID,
		Name:           ent.Name,
		PolicyCategory: pb.PolicyCategory(ent.PolicyCategory),
		RiskLevel:      pb.RiskLevel(ent.RiskLevel),
		Policies:       *ent.Policies,
		Enabled:        ent.Enabled,
		HitAction:      pb.HitAction(ent.HitAction),
		HitResponse:    ent.HitResponse,
		CreatedAt:      ent.CreatedAt,
		UpdatedAt:      ent.UpdatedAt,
		UpdatedBy:      ent.UpdatedBy,
	}
}

func (aiAgentSecurityPolicy) FromEnts(ents []*ent.AiAgentSecurityPolicy) []*biz.AiAgentSecurityPolicy {
	return lox.Map(ents, func(item *ent.AiAgentSecurityPolicy) *biz.AiAgentSecurityPolicy {
		return AiAgentSecurityPolicy.FromEnt(item)
	})
}

func (aiAgentSecurityPolicy) FromRequest(req *pb.SecurityPolicy) *biz.AiAgentSecurityPolicy {
	return &biz.AiAgentSecurityPolicy{
		Name:           req.GetName(),
		PolicyCategory: pb.PolicyCategory(req.GetPolicyCategory()),
		RiskLevel:      pb.RiskLevel(req.GetRiskLevel()),
		Policies:       req.GetPolicies(),
		Enabled:        req.GetEnabled(),
		HitAction:      pb.HitAction(req.GetHitAction()),
		HitResponse:    req.GetHitResponse(),
		ID:             req.Id,
		UpdatedBy:      req.UpdatedBy,
	}
}

func (aiAgentSecurityPolicy) FromRequests(reqs []*pb.SecurityPolicy) []*biz.AiAgentSecurityPolicy {
	return lox.Map(reqs, func(item *pb.SecurityPolicy) *biz.AiAgentSecurityPolicy {
		return AiAgentSecurityPolicy.FromRequest(item)
	})
}

func (aiAgentSecurityPolicy) ToResponse(policy *biz.AiAgentSecurityPolicy) *pb.SecurityPolicy {
	return &pb.SecurityPolicy{
		Id:             policy.ID,
		Name:           policy.Name,
		PolicyCategory: int64(policy.PolicyCategory),
		RiskLevel:      int64(policy.RiskLevel),
		Policies:       policy.Policies,
		Enabled:        policy.Enabled,
		HitAction:      int64(policy.HitAction),
		HitResponse:    policy.HitResponse,
		UpdatedBy:      policy.UpdatedBy,
		UpdatedByName:  policy.UpdatedByName,
		CreatedAt:      timestamppb.New(policy.CreatedAt),
		UpdatedAt:      timestamppb.New(policy.UpdatedAt),
	}
}

func (aiAgentSecurityPolicy) ToResponses(policies []*biz.AiAgentSecurityPolicy) []*pb.SecurityPolicy {
	return lox.Map(policies, func(item *biz.AiAgentSecurityPolicy) *pb.SecurityPolicy {
		return AiAgentSecurityPolicy.ToResponse(item)
	})
}

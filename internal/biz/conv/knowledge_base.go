package conv

import (
	"gitlab.minum.cloud/BackendTeam/pkg/lox"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

var KnowledgeBase = knowledgeBase{}

type knowledgeBase struct{}

func (knowledgeBase) FromEnts(es []*ent.KnowledgeBase) []*biz.KnowledgeBase {
	return lox.Map(es, KnowledgeBase.FromEnt)
}

func (knowledgeBase) FromEnt(e *ent.KnowledgeBase) *biz.KnowledgeBase {
	return &biz.KnowledgeBase{
		ID:              e.ID,
		Name:            e.Name,
		Public:          e.Public,
		DataType:        pb.KnowledgeBaseDataType(e.DataType),
		UserID:          e.UserID,
		ManagerUserIDs:  ([]int64)(*e.ManagerUserIds),
		EditableUserIDs: ([]int64)(*e.EditableUserIds),
		TenantID:        e.Tenant<PERSON>,
		CreatedAt:       e.<PERSON>,
		UpdatedAt:       e.UpdatedAt,
	}
}

var KnowledgeBaseFile = knowledgeBaseFile{}

type knowledgeBaseFile struct{}

func (knowledgeBaseFile) FromEnts(es []*ent.KnowledgeBaseFile) []*biz.KnowledgeBaseFile {
	return lox.Map(es, KnowledgeBaseFile.FromEnt)
}

func (knowledgeBaseFile) FromEnt(e *ent.KnowledgeBaseFile) *biz.KnowledgeBaseFile {
	return &biz.KnowledgeBaseFile{
		KnowledgeBaseID: e.KnowledgeBaseID,
		DataType:        pb.KnowledgeBaseDataType(e.DataType),
		FileRelationID:  e.FileRelationID,
		Metadata:        e.Metadata,
		Status:          pb.KnowledgeBaseFileStatus(e.Status),
		FailedReason:    e.FailedReason,
		CreatedAt:       e.CreatedAt,
		UpdatedAt:       e.UpdatedAt,
	}
}

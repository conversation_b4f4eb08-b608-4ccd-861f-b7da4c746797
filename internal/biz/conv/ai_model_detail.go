package conv

import (
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

var AiModelDetail = aiModelDetail{}

type aiModelDetail struct{}

func (aiModelDetail) FromEnt(aiModel *ent.AiModelDetail) *biz.AiModelDetail {
	return &biz.AiModelDetail{
		ID:                   aiModel.ID,
		ModelName:            aiModel.ModelName,
		Name:                 aiModel.Name,
		Url:                  aiModel.URL,
		Avatar:               aiModel.Avatar,
		CanInternetSearch:    aiModel.CanInternetSearch,
		BalanceSearchUrl:     aiModel.BalanceSearchURL,
		ThinkingEnableStatus: aiModel.ThinkingEnableStatus,
		BackgroundUrl:        aiModel.BackgroundURL,
	}
}

func (aiModelDetail) FromEnts(aiModels []*ent.AiModelDetail) []*biz.AiModelDetail {
	return lox.Map(aiModels, func(aiModel *ent.AiModelDetail) *biz.AiModelDetail {
		return AiModelDetail.FromEnt(aiModel)
	})
}

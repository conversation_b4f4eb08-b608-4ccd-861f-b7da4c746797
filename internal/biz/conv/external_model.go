package conv

import (
	"encoding/json"

	"gitlab.minum.cloud/BackendTeam/pkg/lox"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/externalmodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

var ExternalModelUsage = externalModelUsage{}

type externalModelUsage struct{}

func (externalModelUsage) FromEnts(es []*ent.ExternalModelUsage) []*biz.ExternalModelUsage {
	return lox.Map(es, ExternalModelUsage.FromEnt)
}

func (externalModelUsage) FromEnt(e *ent.ExternalModelUsage) *biz.ExternalModelUsage {
	var files []*biz.ExternalModelFile
	err := json.Unmarshal([]byte(e.Files), &files)
	if err != nil {
		files = nil
	}

	return &biz.ExternalModelUsage{
		ID:          e.ID,
		ModelName:   e.Model<PERSON>ame,
		ModelAvatar: "",
		Question:    e.Question,
		QuestionTag: e.QuestionTag,
		Files:       files,
		UserID:      e.UserID,
		UserName:    e.UserName,
		DeptID:      e.DeptID,
		DeptName:    e.DeptName,
		PcName:      e.PcName,
		HappenedAt:  e.HappenedAt,
	}
}

var ExternalModelFile = externalModelFile{}

type externalModelFile struct{}

func (externalModelFile) ToPbExternalModelFiles(files []*biz.ExternalModelFile) []*pb.ExternalModelFile {
	return lox.Map(files, ExternalModelFile.ToPbExternalModelFile)
}

func (externalModelFile) ToPbExternalModelFile(file *biz.ExternalModelFile) *pb.ExternalModelFile {
	return &pb.ExternalModelFile{
		FileRelationID: file.FileRelationID,
		PreEntityTag:   file.PreEntityTag,
		EntityTag:      file.EntityTag,
		Name:           file.Name,
		FullPath:       file.FullPath,
		Size:           file.Size,
		MimeType:       file.MimeType,
		Level:          file.Level,
		ClassPath:      file.ClassPath,
	}
}

func (externalModelFile) FromPbExternalModelFiles(files []*pb.ExternalModelFile) []*biz.ExternalModelFile {
	return lox.Map(files, ExternalModelFile.FromPbExternalModelFile)
}

func (externalModelFile) FromPbExternalModelFile(file *pb.ExternalModelFile) *biz.ExternalModelFile {
	return &biz.ExternalModelFile{
		FileRelationID: file.FileRelationID,
		PreEntityTag:   file.PreEntityTag,
		EntityTag:      file.EntityTag,
		Name:           file.Name,
		FullPath:       file.FullPath,
		Size:           file.Size,
		MimeType:       file.MimeType,
		Level:          file.Level,
		ClassPath:      file.ClassPath,
	}
}

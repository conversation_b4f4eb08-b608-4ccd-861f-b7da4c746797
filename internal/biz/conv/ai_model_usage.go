package conv

import (
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

var AiModelUsage = aiModelUsage{}

type aiModelUsage struct{}

func (aiModelUsage) FromEnt(usage *ent.AiModelUsage) *biz.AiModelUsage {
	return &biz.AiModelUsage{
		ID:               usage.ID,
		ModelName:        usage.ModelName,
		ModelGatewayName: usage.ModelGatewayName,
		AgentID:          usage.AgentID,
		AgentName:        usage.AgentName,
		UserID:           usage.UserID,
		UserName:         usage.UserName,
		Question:         usage.Question,
		Answer:           usage.Answer,
		ModelDetailID:    usage.ModelDetailID,
		PromptTokens:     usage.PromptTokens,
		CompletionTokens: usage.CompletionTokens,
		RequestStatus:    usage.RequestStatus,
		ErrorCode:        usage.ErrorCode,
	}
}

func (aiModelUsage) FromEnts(usages []*ent.AiModelUsage) []*biz.AiModelUsage {
	return lox.Map(usages, func(usage *ent.AiModelUsage) *biz.AiModelUsage {
		return AiModelUsage.FromEnt(usage)
	})
}

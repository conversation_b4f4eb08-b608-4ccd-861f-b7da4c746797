package conv

import (
	"context"

	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

var Agent = agent{}

type agent struct{}

func (agent) FromEnt(ctx context.Context, aiAgent *ent.AiAgent) *biz.Agent {
	return &biz.Agent{
		ID:                aiAgent.ID,
		Name:              aiAgent.Name,
		Description:       aiAgent.Description,
		Avatar:            aiAgent.Avatar,
		ClickedAvatar:     aiAgent.ClickedAvatar,
		WelcomeMsg:        aiAgent.WelcomeMsg,
		FallbackMsg:       aiAgent.FallbackMsg,
		OwnerID:           aiAgent.OwnerID,
		VisibilityType:    pb.VisibilityType(aiAgent.VisibilityType),
		VisibleToUser:     []int64(*aiAgent.VisibleToUser),
		VisibleToDept:     []int64(*aiAgent.VisibleToDept),
		ManageableToUser:  []int64(*aiAgent.ManageableToUser),
		Schema:            aiAgent.Schema,
		IsPublic:          aiAgent.IsPublic,
		IsEnabled:         aiAgent.IsEnabled,
		KnowledgeBaseIds:  []int64(*aiAgent.KnowledgeBaseIds),
		IsRefFiles:        aiAgent.IsRefFiles,
		ModelType:         aiAgent.ModelType,
		ModelID:           aiAgent.ModelID,
		UseCount:          aiAgent.UseCount,
		KnowledgeBaseType: aiAgent.KnowledgeBaseType,
		InternetSearch:    aiAgent.InternetSearch,
		CanInternetSearch: aiAgent.InternetSearch,
		AgentType:         aiAgent.AgentType,
		RoleSetting:       aiAgent.RoleSetting,
		Thinking:          aiAgent.Thinking,
		ThinkingModelID:   aiAgent.ThinkingModelID,
		UploadFile:        aiAgent.UploadFile,
		SemanticCache:     aiAgent.SemanticCache,
		UpdatedAt:         aiAgent.UpdatedAt,
	}
}

func (agent) FromEnts(ctx context.Context, aiAgents []*ent.AiAgent) []*biz.Agent {
	return lox.Map(aiAgents, func(aiAgent *ent.AiAgent) *biz.Agent {
		return Agent.FromEnt(ctx, aiAgent)
	})
}

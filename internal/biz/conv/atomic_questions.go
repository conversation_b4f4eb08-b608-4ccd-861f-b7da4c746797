package conv

import (
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

var AtomicQuestion = atomicQuestion{}

type atomicQuestion struct{}

func (atomicQuestion) FromEnt(aiAtomicQuestion *ent.AtomicQuestions) *biz.AtomicQuestions {
	return &biz.AtomicQuestions{
		ID:             aiAtomicQuestion.ID,
		EntityTag:      aiAtomicQuestion.EntityTag,
		PreEntityTag:   aiAtomicQuestion.PreEntityTag,
		FileRelationID: aiAtomicQuestion.FileRelationID,
		ChunkIndex:     aiAtomicQuestion.ChunkIndex,
		ChunkSize:      aiAtomicQuestion.ChunkSize,
		Index:          aiAtomicQuestion.Index,
		Question:       []string(*aiAtomicQuestion.Question),
	}
}

func (atomicQuestion) FromEnts(aiAtomicQuestions []*ent.AtomicQuestions) []*biz.AtomicQuestions {
	return lox.Map(aiAtomicQuestions, AtomicQuestion.FromEnt)
}

package conv

import (
	"context"

	"gitlab.minum.cloud/innovationteam/ai-web/api/rag"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"

	"gitlab.minum.cloud/BackendTeam/pkg/lox"
)

var Chat = chat{}

type chat struct{}

func (chat) FromEnt(ctx context.Context, aiChat *ent.AiChat) *biz.Chat {
	return &biz.Chat{
		ID:        aiChat.ID,
		Name:      aiChat.Name,
		ChatType:  rag.ChatObjectType(aiChat.ChatType),
		AgentID:   aiChat.AgentID,
		UserID:    aiChat.UserID,
		TenantID:  aiChat.TenantID,
		CreatedAt: aiChat.CreatedAt,
	}
}

func (chat) FromEnts(ctx context.Context, aiChats []*ent.AiChat) []*biz.Chat {
	return lox.Map(aiChats, func(aiChat *ent.AiChat) *biz.Chat {
		return Chat.FromEnt(ctx, aiChat)
	})
}

package conv

import (
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

var AiAgentSecurityLog = aiAgentSecurityLog{}

type aiAgentSecurityLog struct{}

func (aiAgentSecurityLog) FromEnt(ent *ent.AiAgentSecurityLog) *biz.AiAgentSecurityLog {
	return &biz.AiAgentSecurityLog{
		ID:               ent.ID,
		AgentID:          ent.AgentID,
		RiskLevel:        pb.RiskLevel(ent.RiskLevel),
		UserID:           ent.UserID,
		UserName:         ent.UserName,
		PcName:           ent.PcName,
		AgentName:        ent.AgentName,
		AgentDescription: ent.AgentDescription,
		DeptName:         ent.DeptName,
		DeptID:           ent.DeptID,
		ActionCategory:   ent.ActionCategory,
		HitAction:        pb.HitAction(ent.HitAction),
		Question:         ent.Question,
		UploadedFiles:    *ent.UploadedFiles,
		HitPolicies:      *ent.HitPolicies,
		CreatedAt:        ent.CreatedAt,
		UpdatedAt:        ent.UpdatedAt,
	}
}

func (aiAgentSecurityLog) FromEnts(ents []*ent.AiAgentSecurityLog) []*biz.AiAgentSecurityLog {
	return lox.Map(ents, func(item *ent.AiAgentSecurityLog) *biz.AiAgentSecurityLog {
		return AiAgentSecurityLog.FromEnt(item)
	})
}

package conv

import (
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

var UserAgentOrder = userAgentOrder{}

type userAgentOrder struct{}

func (userAgentOrder) FromEnts(es []*ent.UserAgentOrder) []*biz.UserAgentOrder {
	return lox.Map(es, UserAgentOrder.FromEnt)
}

func (userAgentOrder) FromEnt(e *ent.UserAgentOrder) *biz.UserAgentOrder {
	return &biz.UserAgentOrder{
		UserID:  e.UserID,
		AgentID: e.AgentID,
		Index:   e.OrderIndex,
	}
}

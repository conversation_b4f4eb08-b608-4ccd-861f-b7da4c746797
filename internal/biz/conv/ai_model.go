package conv

import (
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent"
)

var AiModel = aiModel{}

type aiModel struct{}

func (aiModel) FromEnt(aiModel *ent.AiModel) *biz.AiModel {
	return &biz.AiModel{
		ID:        aiModel.ID,
		ModelName: aiModel.ModelName,
		Model:     aiModel.Model,
		ApiKey:    aiModel.APIKey,
		CreatedAt: aiModel.CreatedAt,
	}
}

func (aiModel) FromEnts(aiModels []*ent.AiModel) []*biz.AiModel {
	return lox.Map(aiModels, func(aiModel *ent.AiModel) *biz.AiModel {
		return AiModel.FromEnt(aiModel)
	})
}

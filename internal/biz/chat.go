package biz

import (
	"context"
	"errors"
	"fmt"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/aimodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/oss"
	"go.uber.org/zap"
	"time"

	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/api/rag"
)

type Chat struct {
	ID                     int64              `json:"id"`
	Name                   string             `json:"name"`
	ChatType               rag.ChatObjectType `json:"chat_type"`
	UserID                 int64              `json:"user_id"`
	TenantID               int64              `json:"tenant_id"`
	AgentID                int64              `json:"agent_id"`
	CreatedAt              time.Time          `json:"created_at"`
	UpdatedAt              time.Time          `json:"updated_at"`
	ChatItems              []*ChatItem        `json:"chat_items"`
	AgentName              string             `json:"agent_name"`
	AgentAvatar            string             `json:"agent_avatar"`
	AgentKnowledgeBaseType int64              `json:"agent_knowledge_base_type"`
	ModelType              int64              `json:"model_type"`
	CanInternetSearch      bool               `json:"can_internet_search"`
	AgentType              int64              `json:"agent_type"`
	FallbackMsg            string             `json:"fall_back_msg"`
	Thinking               bool               `json:"thinking"`
	ThinkingEnableStatus   int64              `json:"thinking_enable_status"`
}

type ChatRepo interface {
	Save(context.Context, *Chat) (*Chat, error)
	Update(context.Context, *Chat) (*Chat, error)
	FindByID(context.Context, int64) (*Chat, error)
	ListAll(context.Context, int64) ([]*Chat, error)
	Page(ctx context.Context, userID int64, agentID int64) ([]*Chat, int64, error)
	Delete(ctx context.Context, ids []int64) error
	GetByIDs(ctx context.Context, ids []int64) ([]*Chat, error)
}

type ChatUsecase struct {
	repo              ChatRepo
	chatItemRepo      ChatItemRepo
	log               *zapadapter.Logger
	tm                DbTransaction
	agentRepo         AgentRepo
	ossClient         *oss.Client
	aiModelRepo       AiModelRepo
	aiModelDetailRepo AiModelDetailRepo
}

func (uc *ChatUsecase) Save(ctx context.Context, chatName string, chatType, agentID int64) (*Chat, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, errors.New("session not found")
	}
	if len([]rune(chatName)) > 20 {
		chatName = string([]rune(chatName)[:20])
	}
	return uc.repo.Save(ctx, &Chat{
		Name:     chatName,
		ChatType: rag.ChatObjectType(chatType),
		UserID:   session.UserID,
		TenantID: session.TenantID,
		AgentID:  agentID,
	})
}

func (uc *ChatUsecase) Update(ctx context.Context, chat *Chat) (*Chat, error) {
	return uc.repo.Update(ctx, chat)
}

func (uc *ChatUsecase) FindByID(ctx context.Context, id int64) (*Chat, error) {
	chat, err := uc.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	chatItems, err := uc.chatItemRepo.ListAll(ctx, chat.ID)
	if err != nil {
		return nil, err
	}
	chat.ChatItems = chatItems
	return chat, nil
}

func (uc *ChatUsecase) Page(ctx context.Context, agentID int64) ([]*Chat, int64, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, 0, errors.New("session not found")
	}
	res, total, err := uc.repo.Page(ctx, session.UserID, agentID)
	if err != nil {
		return nil, 0, err
	}
	for i := range res {
		agent, err := uc.agentRepo.FindByID(ctx, res[i].AgentID)
		if err != nil {
			uc.log.Error(fmt.Sprintf("query agent %d info error", res[i].AgentID), zap.Error(err))
		} else {
			if agent.ModelType == int64(pb.ModelType_ModelTypeInternal) {
				res[i].AgentName = agent.Name
				res[i].AgentAvatar = uc.ossClient.GetObjectURL(ctx, agent.Avatar)
				res[i].AgentKnowledgeBaseType = agent.KnowledgeBaseType
			} else if agent.ModelType == int64(pb.ModelType_ModelTypeExternal) {
				modelDetails, err := uc.aiModelRepo.GetModelDetails(ctx, []int64{agent.ModelID})
				if err != nil {
					uc.log.Error(fmt.Sprintf("query model details error"), zap.Error(err))
				} else {
					if len(modelDetails) > 0 {
						res[i].AgentName = modelDetails[0].Name
						res[i].AgentAvatar = uc.ossClient.GetObjectURL(ctx, modelDetails[0].Avatar)
						res[i].AgentKnowledgeBaseType = agent.KnowledgeBaseType
					}
				}
				if agent.ThinkingModelID > 0 {
					thinkingModel, err := uc.aiModelRepo.Get(ctx, agent.ThinkingModelID)
					if err != nil {
						uc.log.Error(fmt.Sprintf("query thinking model error"), zap.Error(err))
					}
					if thinkingModel != nil && thinkingModel.Model > 0 {
						thinkingModelDetail, err := uc.aiModelDetailRepo.Get(ctx, thinkingModel.Model)
						if err != nil {
							uc.log.Error(fmt.Sprintf("query thinking model details error"), zap.Error(err))
						} else {
							res[i].Thinking = agent.Thinking
							res[i].ThinkingEnableStatus = thinkingModelDetail.ThinkingEnableStatus
						}
					}
				}
			}
			res[i].ModelType = agent.ModelType
			res[i].CanInternetSearch = agent.CanInternetSearch
			res[i].AgentType = agent.AgentType
			res[i].FallbackMsg = agent.FallbackMsg
		}
	}
	return res, total, nil
}

func (uc *ChatUsecase) ListAll(ctx context.Context) ([]*Chat, error) {
	session, ok := authz.GetSession(ctx)
	if !ok {
		return nil, errors.New("session not found")
	}
	return uc.repo.ListAll(ctx, session.UserID)
}

func (uc *ChatUsecase) Delete(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	return uc.repo.Delete(ctx, ids)
}

func NewChatUsecase(repo ChatRepo, chatItemRepo ChatItemRepo, tm DbTransaction, logger *zapadapter.Logger, agentRepo AgentRepo, ossClient *oss.Client, aiModelRepo AiModelRepo, aiModelDetailRepo AiModelDetailRepo) *ChatUsecase {
	return &ChatUsecase{
		repo:              repo,
		chatItemRepo:      chatItemRepo,
		log:               logger,
		tm:                tm,
		agentRepo:         agentRepo,
		ossClient:         ossClient,
		aiModelRepo:       aiModelRepo,
		aiModelDetailRepo: aiModelDetailRepo,
	}
}

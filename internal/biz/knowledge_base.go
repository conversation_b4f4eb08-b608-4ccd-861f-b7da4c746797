package biz

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"time"

	"github.com/qdrant/go-client/qdrant"
	"github.com/samber/lo"
	userpb "gitlab.minum.cloud/BackendTeam/admin/api/user"
	"gitlab.minum.cloud/BackendTeam/pkg/lox"
	"gitlab.minum.cloud/BackendTeam/pkg/sheet"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	vfspb "gitlab.minum.cloud/BackendTeam/store/api/vfs"
	"google.golang.org/protobuf/types/known/timestamppb"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

// 权限
// 拥有者：修改名称，调整可见范围，转让知识库，管理管理员，管理协作者，删除知识库，上传删除文件
// 管理者：修改名称，调整可见范围，                    管理协作者，          上传删除文件
// 协作者：                                                            上传删除文件

var (
	ErrKnowledgeBaseIllegalOperation = pb.ErrorKnowledgeBaseIllegalOperation("非法操作知识库")
	ErrKnowledgeBaseExistAgent       = pb.ErrorKnowledgeBaseExistAgent("知识库存在关联的智能助手")
	ErrKnowledgeBaseAddTooMuchFile   = pb.ErrorKnowledgeBaseAddTooMuchFile("添加知识库文件过多，请删减")
)

type KnowledgeBase struct {
	ID              int64
	Name            string
	Public          bool
	DataType        pb.KnowledgeBaseDataType
	UserID          int64
	ManagerUserIDs  []int64
	EditableUserIDs []int64
	TenantID        int64
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

type KnowledgeBaseFile struct {
	KnowledgeBaseID int64
	DataType        pb.KnowledgeBaseDataType
	FileRelationID  int64
	Metadata        string
	Status          pb.KnowledgeBaseFileStatus
	FailedReason    string
	CreatedAt       time.Time
	UpdatedAt       time.Time
}

type OwnerOption struct {
	NewUserID      int64
	ManagerUserIDs []int64
}

type KnowledgeBaseRepo interface {
	CreateKnowledgeBase(ctx context.Context, tenantID int64, name string, public bool, dataType pb.KnowledgeBaseDataType, userID int64, managerUserIDs, editableUserIDs []int64) (int64, error)
	UpdateKnowledgeBase(ctx context.Context, tenantID, id int64, name string, public bool, editableUserIDs []int64, ownerOption *OwnerOption) error
	ChangeKnowledgeBaseOwner(ctx context.Context, ids []int64, newUserID int64) error
	DeleteKnowledgeBase(ctx context.Context, tenantID, userID, id int64) error
	GetKnowledgeBase(ctx context.Context, tenantID, id int64) (*KnowledgeBase, error)
	GetKnowledgeBasesByUser(ctx context.Context, userID int64) ([]*KnowledgeBase, error)
	GetKnowledgeBases(ctx context.Context, tenantID int64, ids []int64) ([]*KnowledgeBase, error)
	GetAllKnowledgeBases(ctx context.Context) ([]*KnowledgeBase, error)
	PageKnowledgeBase(ctx context.Context, tenantID, userID int64, name string, all bool, dataType int32) ([]*KnowledgeBase, int64, error)
}

type KnowledgeBaseFileRepo interface {
	BatchAddKnowledgeBaseFile(ctx context.Context, tenantID, id int64, dataType pb.KnowledgeBaseDataType, fileRelationIDs []int64) error
	BatchDeleteKnowledgeBaseFile(ctx context.Context, tenantID, id int64, dataType pb.KnowledgeBaseDataType, fileRelationIDs []int64) error
	UpdateKnowledgeBaseFile(ctx context.Context, id, fileRelationID int64, metadata *string, status *pb.KnowledgeBaseFileStatus, failedReason *string) error
	GetFileKnowledgeBaseIDs(ctx context.Context, dataType pb.KnowledgeBaseDataType, fileRelationID int64) ([]int64, error)
	GetFilesKnowledgeBaseIDs(ctx context.Context, dataType pb.KnowledgeBaseDataType, fileRelationIDs []int64) (map[int64][]int64, error)
	FilterKnowledgeBaseFile(ctx context.Context, id int64, fileRelationIDs []int64) ([]int64, error)
	ExistKnowledgeBaseFile(ctx context.Context, id, fileRelationID int64) (bool, error)
	GetInKnowledgeBaseFileIDs(ctx context.Context, fileRelationIDs []int64) ([]int64, error)
	PageKnowledgeBaseFile(ctx context.Context, id int64, fileName string) ([]*KnowledgeBaseFile, int64, error)
	CountKnowledgeBaseFile(ctx context.Context, tenantID int64, startTime, endTime time.Time) (int64, error)
	GetFileWithKnowledgeBaseIDs(ctx context.Context, fileRelationID int64, knowledgeBaseIDs []int64) ([]*KnowledgeBaseFile, error)
}

type KnowledgeBaseUseCase struct {
	log                     *zapadapter.Logger
	tx                      DbTransaction
	cfg                     *conf.Bootstrap
	qdrantClient            *qdrant.Client
	rpcClient               rpc.Client
	knowledgeBaseRepo       KnowledgeBaseRepo
	knowledgeBaseFileRepo   KnowledgeBaseFileRepo
	agentRepo               AgentRepo
	classificationFilesRepo ClassificationFilesRepo
}

func NewKnowledgeBaseUseCase(
	log *zapadapter.Logger,
	tx DbTransaction,
	cfg *conf.Bootstrap,
	qdrantClient *qdrant.Client,
	rpcClient rpc.Client,
	knowledgeBaseRepo KnowledgeBaseRepo,
	knowledgeBaseFileRepo KnowledgeBaseFileRepo,
	agentRepo AgentRepo,
	classificationFilesRepo ClassificationFilesRepo,
) *KnowledgeBaseUseCase {
	return &KnowledgeBaseUseCase{
		log:                     log,
		tx:                      tx,
		cfg:                     cfg,
		qdrantClient:            qdrantClient,
		rpcClient:               rpcClient,
		knowledgeBaseRepo:       knowledgeBaseRepo,
		knowledgeBaseFileRepo:   knowledgeBaseFileRepo,
		agentRepo:               agentRepo,
		classificationFilesRepo: classificationFilesRepo,
	}
}

func (uc *KnowledgeBaseUseCase) CreateKnowledgeBase(ctx context.Context, tenantID int64, name string, public bool, dataType pb.KnowledgeBaseDataType, userID int64, managerUserIDs, editableUserIDs []int64) (int64, error) {
	return uc.knowledgeBaseRepo.CreateKnowledgeBase(ctx, tenantID, name, public, dataType, userID, managerUserIDs, editableUserIDs)
}

func (uc *KnowledgeBaseUseCase) UpdateKnowledgeBase(ctx context.Context, tenantID, id int64, name string, public bool, editableUserIDs []int64, ownerOption *OwnerOption) error {
	return uc.knowledgeBaseRepo.UpdateKnowledgeBase(ctx, tenantID, id, name, public, editableUserIDs, ownerOption)
}

func (uc *KnowledgeBaseUseCase) ChangeKnowledgeBaseOwner(ctx context.Context, ids []int64, newUserID int64) error {
	return uc.knowledgeBaseRepo.ChangeKnowledgeBaseOwner(ctx, ids, newUserID)
}

func (uc *KnowledgeBaseUseCase) DeleteKnowledgeBase(ctx context.Context, tenantID, userID, id int64) error {
	return uc.tx.InTx(ctx, func(ctx context.Context) error {
		exist, err := uc.agentRepo.ExistKnowledgeBaseAgent(ctx, id)
		if err != nil {
			return fmt.Errorf("exist knowledge base agent: %w", err)
		}
		if exist {
			return ErrKnowledgeBaseExistAgent
		}

		return uc.knowledgeBaseRepo.DeleteKnowledgeBase(ctx, tenantID, userID, id)
	})
}

func (uc *KnowledgeBaseUseCase) GetKnowledgeBase(ctx context.Context, tenantID, id int64) (*KnowledgeBase, error) {
	knowledgeBase, err := uc.knowledgeBaseRepo.GetKnowledgeBase(ctx, tenantID, id)
	if err != nil {
		return nil, fmt.Errorf("get knowledge base: %w", err)
	}

	return knowledgeBase, nil
}

func (uc *KnowledgeBaseUseCase) GetKnowledgeBases(ctx context.Context, tenantID int64, ids []int64) ([]*pb.KnowledgeBaseInfo, error) {
	knowledgeBases, err := uc.knowledgeBaseRepo.GetKnowledgeBases(ctx, tenantID, ids)
	if err != nil {
		return nil, err
	}

	return uc.convertKnowledgeBases(ctx, knowledgeBases)
}

func (uc *KnowledgeBaseUseCase) GetAllKnowledgeBases(ctx context.Context) ([]*pb.KnowledgeBaseInfo, error) {
	knowledgeBases, err := uc.knowledgeBaseRepo.GetAllKnowledgeBases(ctx)
	if err != nil {
		return nil, err
	}

	return uc.convertKnowledgeBases(ctx, knowledgeBases)
}

func (uc *KnowledgeBaseUseCase) PageKnowledgeBase(ctx context.Context, tenantID, userID int64, name string, all bool, dataType int32) ([]*pb.KnowledgeBaseInfo, int64, error) {
	knowledgeBases, total, err := uc.knowledgeBaseRepo.PageKnowledgeBase(ctx, tenantID, userID, name, all, dataType)
	if err != nil {
		return nil, 0, err
	}

	items, err := uc.convertKnowledgeBases(ctx, knowledgeBases)
	if err != nil {
		return nil, 0, err
	}

	return items, total, nil
}

func (uc *KnowledgeBaseUseCase) convertKnowledgeBases(ctx context.Context, knowledgeBases []*KnowledgeBase) ([]*pb.KnowledgeBaseInfo, error) {
	var userIDs []int64
	for _, knowledgeBase := range knowledgeBases {
		userIDs = append(userIDs, knowledgeBase.UserID)
		userIDs = append(userIDs, knowledgeBase.ManagerUserIDs...)
		userIDs = append(userIDs, knowledgeBase.EditableUserIDs...)
	}
	userIDs = lo.Uniq(userIDs)

	reply, err := uc.rpcClient.GetUser(ctx, &userpb.GetUserRequest{Ids: userIDs, HasDeleted: true})
	if err != nil {
		return nil, fmt.Errorf("get user: %w", err)
	}
	if len(reply.GetItems()) != len(userIDs) {
		return nil, ErrUserNotFound
	}
	userInfos := make(map[int64]*userpb.UserInfo, len(reply.GetItems()))
	for _, item := range reply.GetItems() {
		userInfos[item.Id] = item
	}

	var items []*pb.KnowledgeBaseInfo
	for _, knowledgeBase := range knowledgeBases {
		userInfo, ok := userInfos[knowledgeBase.UserID]
		if !ok {
			return nil, ErrUserNotFound
		}

		var editableUserInfos []*pb.KnowledgeBaseInfo_UserInfo
		for _, userID := range knowledgeBase.EditableUserIDs {
			userInfo, ok := userInfos[userID]
			if !ok {
				return nil, ErrUserNotFound
			}
			editableUserInfos = append(editableUserInfos, &pb.KnowledgeBaseInfo_UserInfo{
				UserID:     userInfo.Id,
				UserName:   userInfo.NickName,
				UserAvatar: userInfo.AvatarURL,
			})
		}

		var managers []*pb.KnowledgeBaseInfo_UserInfo
		for _, userID := range knowledgeBase.ManagerUserIDs {
			userInfo, ok := userInfos[userID]
			if !ok {
				return nil, ErrUserNotFound
			}
			managers = append(managers, &pb.KnowledgeBaseInfo_UserInfo{
				UserID:     userInfo.Id,
				UserName:   userInfo.NickName,
				UserAvatar: userInfo.AvatarURL,
			})
		}

		items = append(items, &pb.KnowledgeBaseInfo{
			Id:            knowledgeBase.ID,
			Name:          knowledgeBase.Name,
			Public:        knowledgeBase.Public,
			DataType:      knowledgeBase.DataType,
			UserID:        knowledgeBase.UserID,
			UserName:      userInfo.NickName,
			UserAvatar:    userInfo.AvatarURL,
			Managers:      managers,
			EditableUsers: editableUserInfos,
			CreatedAt:     timestamppb.New(knowledgeBase.CreatedAt),
		})
	}

	return items, nil
}

func (uc *KnowledgeBaseUseCase) AddKnowledgeBaseFile(ctx context.Context, knowledgeBase *KnowledgeBase, fileRelationIDs []int64) error {
	addKnowledgeBaseFileFunc := uc.knowledgeBaseFileRepo.BatchAddKnowledgeBaseFile
	updateFuncWrapper := uc.buildUpdateKnowledgeBaseFileFuncWrapper(knowledgeBase, fileRelationIDs, addKnowledgeBaseFileFunc)
	return uc.tx.InTx(ctx, updateFuncWrapper)
}

func (uc *KnowledgeBaseUseCase) DeleteKnowledgeBaseFile(ctx context.Context, knowledgeBase *KnowledgeBase, fileRelationIDs []int64) error {
	deleteKnowledgeBaseFileFunc := uc.knowledgeBaseFileRepo.BatchDeleteKnowledgeBaseFile
	updateFuncWrapper := uc.buildUpdateKnowledgeBaseFileFuncWrapper(knowledgeBase, fileRelationIDs, deleteKnowledgeBaseFileFunc)
	return uc.tx.InTx(ctx, updateFuncWrapper)
}

type UpdateKnowledgeBaseFileFunc func(ctx context.Context, tenantID, id int64, dataType pb.KnowledgeBaseDataType, fileRelationIDs []int64) error

func (uc *KnowledgeBaseUseCase) buildUpdateKnowledgeBaseFileFuncWrapper(knowledgeBase *KnowledgeBase, fileRelationIDs []int64, update UpdateKnowledgeBaseFileFunc) func(ctx context.Context) error {
	return func(ctx context.Context) error {
		if err := update(ctx, knowledgeBase.TenantID, knowledgeBase.ID, knowledgeBase.DataType, fileRelationIDs); err != nil {
			return fmt.Errorf("update knowledge base file: %w", err)
		}

		knowledgeBaseIDsByFileRelationID, err := uc.knowledgeBaseFileRepo.GetFilesKnowledgeBaseIDs(ctx, knowledgeBase.DataType, fileRelationIDs)
		if err != nil {
			return fmt.Errorf("get file knowledge base ids: %w", err)
		}

		if knowledgeBase.DataType == pb.KnowledgeBaseDataType_KnowledgeBaseDataTypeDoc {
			for _, fileRelationID := range fileRelationIDs {
				knowledgeBaseIDs := knowledgeBaseIDsByFileRelationID[fileRelationID]

				wait := false

				if _, err := uc.qdrantClient.SetPayload(ctx, &qdrant.SetPayloadPoints{
					CollectionName: uc.cfg.Vs.CollectionName,
					Wait:           &wait,
					Payload: qdrant.NewValueMap(map[string]any{
						"knowledgeBaseIDs": lox.Map(knowledgeBaseIDs, func(knowledgeBaseID int64) interface{} { return knowledgeBaseID }),
					}),
					PointsSelector: qdrant.NewPointsSelectorFilter(&qdrant.Filter{
						Must: []*qdrant.Condition{
							qdrant.NewMatchInt("fileRelationID", fileRelationID),
						},
					}),
				}); err != nil {
					return fmt.Errorf("qdrant set payload: %w", err)
				}

				if _, err := uc.qdrantClient.SetPayload(ctx, &qdrant.SetPayloadPoints{
					CollectionName: uc.cfg.Vs.AtomicQuestionCollectionName,
					Wait:           &wait,
					Payload: qdrant.NewValueMap(map[string]any{
						"knowledgeBaseIDs": lox.Map(knowledgeBaseIDs, func(knowledgeBaseID int64) interface{} { return knowledgeBaseID }),
					}),
					PointsSelector: qdrant.NewPointsSelectorFilter(&qdrant.Filter{
						Must: []*qdrant.Condition{
							qdrant.NewMatchInt("fileRelationID", fileRelationID),
						},
					}),
				}); err != nil {
					return fmt.Errorf("qdrant set payload: %w", err)
				}
			}
		}

		if knowledgeBase.DataType == pb.KnowledgeBaseDataType_KnowledgeBaseDataTypeSheet {
			for _, fileRelationID := range fileRelationIDs {
				knowledgeBaseIDs := knowledgeBaseIDsByFileRelationID[fileRelationID]

				knowledgeBaseID := knowledgeBase.ID
				if !slices.Contains(knowledgeBaseIDs, knowledgeBase.ID) {
					knowledgeBaseID = 0
				}

				wait := false

				if _, err := uc.qdrantClient.SetPayload(ctx, &qdrant.SetPayloadPoints{
					CollectionName: uc.cfg.Vs.SheetCollectionName,
					Wait:           &wait,
					Payload: qdrant.NewValueMap(map[string]any{
						"knowledgeBaseID": knowledgeBaseID,
					}),
					PointsSelector: qdrant.NewPointsSelectorFilter(&qdrant.Filter{
						Must: []*qdrant.Condition{
							qdrant.NewMatchInt("fileRelationID", fileRelationID),
							qdrant.NewMatchInt("knowledgeBaseID", knowledgeBase.ID),
						},
					}),
				}); err != nil {
					return fmt.Errorf("qdrant set payload: %w", err)
				}
			}
		}

		return nil
	}
}

func (uc *KnowledgeBaseUseCase) UpdateKnowledgeBaseFile(ctx context.Context, id, fileRelationID int64, metadata *string, status *pb.KnowledgeBaseFileStatus, failedReason *string) error {
	if metadata != nil {
		var m sheet.SheetMetadata
		err := json.Unmarshal([]byte(*metadata), &m)
		if err != nil {
			return fmt.Errorf("unmarshal sheet metadata: %w", err)
		}
		if len(m.Columns) == 0 {
			return errors.New("sheet metadata columns cannot be empty")
		}
	}
	return uc.knowledgeBaseFileRepo.UpdateKnowledgeBaseFile(ctx, id, fileRelationID, metadata, status, failedReason)
}

func (uc *KnowledgeBaseUseCase) PageKnowledgeBaseFile(ctx context.Context, knowledgeBase *KnowledgeBase, fileName string) ([]*pb.PageKnowledgeBaseFileReplyItem, int64, error) {
	knowledgeBaseFiles, total, err := uc.knowledgeBaseFileRepo.PageKnowledgeBaseFile(ctx, knowledgeBase.ID, fileName)
	if err != nil {
		return nil, 0, err
	}
	if len(knowledgeBaseFiles) == 0 {
		return nil, total, nil
	}

	fileRelationIDs := lox.Map(knowledgeBaseFiles, func(knowledgeBaseFile *KnowledgeBaseFile) int64 { return knowledgeBaseFile.FileRelationID })

	fileReply, err := uc.rpcClient.GetFile(ctx, &vfspb.GetInternalFileRequest{FileRelationIDs: fileRelationIDs})
	if err != nil {
		return nil, 0, err
	}

	fileByFileRelationID := make(map[int64]*vfspb.FileInfo, len(fileReply.GetFiles()))
	for _, file := range fileReply.GetFiles() {
		fileByFileRelationID[file.FileRelationID] = file
	}

	tags, err := uc.classificationFilesRepo.GetFileTags(ctx, fileRelationIDs)
	if err != nil {
		return nil, 0, err
	}

	if knowledgeBase.DataType == pb.KnowledgeBaseDataType_KnowledgeBaseDataTypeDoc {
		statusByFileRelationID := make(map[int64]pb.KnowledgeBaseFileStatus)
		failedReasonByFileRelationID := make(map[int64]string)
		statusReply, err := uc.rpcClient.GetFileRedisStatus(ctx, &vfspb.GetFileRedisStatusRequest{FileRelationIDs: fileRelationIDs})
		if err != nil {
			return nil, 0, err
		}
		for _, item := range statusReply.GetItems() {
			status := pb.KnowledgeBaseFileStatus_KnowledgeBaseFileStatusWaiting
			failedReason := ""

			if item.VectorError != "" {
				status = pb.KnowledgeBaseFileStatus_KnowledgeBaseFileStatusFailed
				failedReason = item.VectorError
			} else if item.ExtractError != "" {
				status = pb.KnowledgeBaseFileStatus_KnowledgeBaseFileStatusFailed
				failedReason = item.ExtractError
			} else if item.Vector {
				status = pb.KnowledgeBaseFileStatus_KnowledgeBaseFileStatusSuccess
			} else if item.Extract {
				status = pb.KnowledgeBaseFileStatus_KnowledgeBaseFileStatusProcessing
			}

			statusByFileRelationID[item.FileRelationID] = status
			failedReasonByFileRelationID[item.FileRelationID] = failedReason
		}
		for _, knowledgeBaseFile := range knowledgeBaseFiles {
			knowledgeBaseFile.Status = statusByFileRelationID[knowledgeBaseFile.FileRelationID]
			knowledgeBaseFile.FailedReason = failedReasonByFileRelationID[knowledgeBaseFile.FileRelationID]
		}
	}

	var items []*pb.PageKnowledgeBaseFileReplyItem
	for _, knowledgeBaseFile := range knowledgeBaseFiles {
		file, ok := fileByFileRelationID[knowledgeBaseFile.FileRelationID]
		if !ok {
			continue
		}

		items = append(items, &pb.PageKnowledgeBaseFileReplyItem{
			DataType:       knowledgeBaseFile.DataType,
			Metadata:       knowledgeBaseFile.Metadata,
			FileRelationID: knowledgeBaseFile.FileRelationID,
			FileName:       file.Name,
			FilePath:       file.FullPath,
			FileType:       file.MimeType,
			FileSize:       file.Size,
			PreEntityTag:   file.PreEntityTag,
			EntityTag:      file.EntityTag,
			FileStatus:     knowledgeBaseFile.Status,
			FailedReason:   knowledgeBaseFile.FailedReason,
			FileCategories: tags[knowledgeBaseFile.FileRelationID],
			CreatedAt:      timestamppb.New(knowledgeBaseFile.CreatedAt),
		})
	}

	return items, int64(total), nil
}

func (uc *KnowledgeBaseUseCase) FilterKnowledgeBaseFile(ctx context.Context, id int64, fileRelationIDs []int64) ([]int64, error) {
	return uc.knowledgeBaseFileRepo.FilterKnowledgeBaseFile(ctx, id, fileRelationIDs)
}

func (uc *KnowledgeBaseUseCase) ExistKnowledgeBaseFile(ctx context.Context, id, fileRelationID int64) (bool, error) {
	return uc.knowledgeBaseFileRepo.ExistKnowledgeBaseFile(ctx, id, fileRelationID)
}

func (uc *KnowledgeBaseUseCase) GetInKnowledgeBaseFileIDs(ctx context.Context, fileRelationIDs []int64) ([]int64, error) {
	return uc.knowledgeBaseFileRepo.GetInKnowledgeBaseFileIDs(ctx, fileRelationIDs)
}

func (uc *KnowledgeBaseUseCase) GetFileKnowledgeBaseIDs(ctx context.Context, dataType pb.KnowledgeBaseDataType, fileRelationID int64) ([]int64, error) {
	return uc.knowledgeBaseFileRepo.GetFileKnowledgeBaseIDs(ctx, dataType, fileRelationID)
}

func (uc *KnowledgeBaseUseCase) CountKnowledgeBaseFile(ctx context.Context, tenantID int64, startTime, endTime time.Time) (int64, error) {
	return uc.knowledgeBaseFileRepo.CountKnowledgeBaseFile(ctx, tenantID, startTime, endTime)
}

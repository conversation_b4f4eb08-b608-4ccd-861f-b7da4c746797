package biz

import (
	"context"

	"github.com/redis/go-redis/v9"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

type FileInfo struct {
	FileRelationID int64
	PreEntityTag   string
	EntityTag      string
}

type ClassificationFileTags struct {
	ID             int64
	FileRelationID int64
	PreEntityTag   string
	EntityTag      string
	FileName       string
	FileType       string
	UserID         int64
	UserName       string
	DeptID         int64
	DeptName       string
	Path           string
	Tags           []string
	SecurityLevel  int32
}

type ClassificationFilesRepo interface {
	GetFileTags(ctx context.Context, fileRelationIDs []int64) (map[int64][]string, error)
	GetClassificationFileTags(ctx context.Context, fileInfos []*FileInfo) ([]*ClassificationFileTags, error)
}

type ClassificationUseCase struct {
	log                     *zapadapter.Logger
	redis                   *redis.Client
	classificationFilesRepo ClassificationFilesRepo
	rpcClient               rpc.Client
}

func NewClassificationUseCase(logger *zapadapter.Logger, redis *redis.Client, classificationFilesRepo ClassificationFilesRepo, rpcClient rpc.Client) *ClassificationUseCase {
	return &ClassificationUseCase{
		log:                     logger,
		redis:                   redis,
		classificationFilesRepo: classificationFilesRepo,
		rpcClient:               rpcClient,
	}
}

func (uc *ClassificationUseCase) GetClassificationFileTags(ctx context.Context, fileInfos []*FileInfo) ([]*ClassificationFileTags, error) {
	return uc.classificationFilesRepo.GetClassificationFileTags(ctx, fileInfos)
}

package biz

import (
	"context"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"time"
)

type AiAgentSecurityPolicy struct {
	ID             int64             `json:"id"`
	Name           string            `json:"name"`
	AgentID        int64             `json:"agent_id"`
	PolicyCategory pb.PolicyCategory `json:"policy_category"`
	RiskLevel      pb.RiskLevel      `json:"risk_level"`
	Enabled        bool              `json:"enabled"`
	Policies       []string          `json:"policies"`
	HitAction      pb.HitAction      `json:"hit_action"`
	HitResponse    string            `json:"hit_response"`
	UpdatedBy      int64             `json:"updated_by"`
	UpdatedByName  string            `json:"updated_by_name"`
	CreatedAt      time.Time         `json:"created_at"`
	UpdatedAt      time.Time         `json:"updated_at"`
}

type AiAgentSecurityPolicyRepo interface {
	Get(ctx context.Context, id int64) (*AiAgentSecurityPolicy, error)
	Create(ctx context.Context, policies []*AiAgentSecurityPolicy) ([]*AiAgentSecurityPolicy, error)
	ListByAgentID(ctx context.Context, agentID int64) ([]*AiAgentSecurityPolicy, error)
	Update(ctx context.Context, policies *AiAgentSecurityPolicy) (*AiAgentSecurityPolicy, error)
	Delete(ctx context.Context, id int64) error
}

package biz

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	userpb "gitlab.minum.cloud/BackendTeam/admin/api/user"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-api/aiapi/llm"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/externalmodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/oss"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

type ExternalModelFile struct {
	FileRelationID int64
	PreEntityTag   string
	EntityTag      string
	Name           string
	FullPath       string
	Size           int64
	MimeType       string
	Level          int64
	ClassPath      string
}

type ExternalModelUsage struct {
	ID          int64
	ModelName   string
	ModelAvatar string
	Question    string
	QuestionTag string
	Files       []*ExternalModelFile
	UserID      int64
	UserName    string
	UserAvatar  string
	DeptID      int64
	DeptName    string
	PcName      string
	HappenedAt  time.Time
}

type ExternalModelRepo interface {
	CreateExternalModelUsage(ctx context.Context, externalModelUsage *ExternalModelUsage) (int64, error)
	UpdateQuestionTag(ctx context.Context, id int64, questionTag string) error
	PageExternalModelUsage(ctx context.Context, id int64, userName, deptName string, startTime, endTime time.Time) ([]*ExternalModelUsage, int64, error)
	QueryQuestionTagDistribution(ctx context.Context, startTime, endTime time.Time) ([]*Classification, error)
	QueryUploadFileTypesDistribution(ctx context.Context, startTime, endTime time.Time) ([]*FileType, error)
}

type ExternalModelUseCase struct {
	logger                  *zapadapter.Logger
	tx                      DbTransaction
	cfg                     *conf.Bootstrap
	rpcClient               rpc.Client
	ossClient               *oss.Client
	externalModelRepo       ExternalModelRepo
	classificationFilesRepo ClassificationFilesRepo
}

func NewExternalModelUseCase(logger *zapadapter.Logger, tx DbTransaction, cfg *conf.Bootstrap, rpcClient rpc.Client, ossClient *oss.Client, externalModelRepo ExternalModelRepo, classificationFilesRepo ClassificationFilesRepo) *ExternalModelUseCase {
	return &ExternalModelUseCase{
		logger:                  logger,
		tx:                      tx,
		cfg:                     cfg,
		rpcClient:               rpcClient,
		ossClient:               ossClient,
		externalModelRepo:       externalModelRepo,
		classificationFilesRepo: classificationFilesRepo,
	}
}

func (uc *ExternalModelUseCase) GetExternalModelConfig(ctx context.Context) ([]*pb.ExternalModelConfig, error) {
	var configs []*pb.ExternalModelConfig
	for _, c := range uc.cfg.ExternalModelConfigs {
		configs = append(configs, &pb.ExternalModelConfig{
			ParseType:        c.ParseType,
			ModelName:        c.ModelName,
			Host:             c.Host,
			PreSchema:        c.PreSchema,
			ParseRule:        c.ParseRule,
			SessionParseRule: c.SessionParseRule,
		})
	}

	return configs, nil
}

func (uc *ExternalModelUseCase) CreateExternalModelUsage(ctx context.Context, userID int64, modelName, question, pcName string, files []*ExternalModelFile, happenedAt time.Time) (int64, error) {
	reply, err := uc.rpcClient.GetDeptUser(ctx, &userpb.GetDeptUserRequest{UserIDs: []int64{userID}})
	if err != nil {
		return 0, fmt.Errorf("get user: %w", err)
	}
	if len(reply.GetItems()) != 1 {
		return 0, ErrUserNotFound
	}
	user := reply.GetItems()[0]

	externalModelUsage := &ExternalModelUsage{
		ModelName:   modelName,
		Question:    question,
		QuestionTag: "",
		Files:       files,
		UserID:      user.UserID,
		UserName:    user.NickName,
		DeptID:      user.DeptID,
		DeptName:    user.DeptName,
		PcName:      pcName,
		HappenedAt:  happenedAt,
	}

	return uc.externalModelRepo.CreateExternalModelUsage(ctx, externalModelUsage)
}

func (uc *ExternalModelUseCase) UpdateQuestionTag(ctx context.Context, id int64, question string) error {
	reply, err := uc.rpcClient.DoExternalModelQuestionClassify(ctx, &llm.DoExternalModelQuestionClassifyRequest{
		Question:           question,
		QuestionTags:       []string{"问题解决", "信息获取", "决策辅助", "创新发展", "私人"},
		UnknownQuestionTag: "其他",
	})
	if err != nil {
		return fmt.Errorf("do external model question classify: %w", err)
	}

	err = uc.externalModelRepo.UpdateQuestionTag(ctx, id, reply.QuestionTag)
	if err != nil {
		return fmt.Errorf("update question tag: %w", err)
	}

	return nil
}

func (uc *ExternalModelUseCase) PageExternalModelUsage(ctx context.Context, id int64, userName, deptName string, startTime, endTime time.Time) ([]*ExternalModelUsage, int64, error) {
	externalModelUsages, total, err := uc.externalModelRepo.PageExternalModelUsage(ctx, id, userName, deptName, startTime, endTime)
	if err != nil {
		return nil, 0, fmt.Errorf("page external model usage: %w", err)
	}

	var userIDs []int64
	var fileInfos []*FileInfo
	for _, externalModelUsage := range externalModelUsages {
		userIDs = append(userIDs, externalModelUsage.UserID)
		for _, file := range externalModelUsage.Files {
			fileInfos = append(fileInfos, &FileInfo{
				FileRelationID: file.FileRelationID,
				PreEntityTag:   file.PreEntityTag,
				EntityTag:      file.EntityTag,
			})
		}
	}

	// user
	getUserReply, err := uc.rpcClient.GetUser(ctx, &userpb.GetUserRequest{Ids: lo.Uniq(userIDs), HasDeleted: true})
	if err != nil {
		return nil, 0, fmt.Errorf("get user: %w", err)
	}
	userAvatars := make(map[int64]string)
	for _, user := range getUserReply.GetItems() {
		userAvatars[user.Id] = user.AvatarURL
	}

	// tag
	classificationTags, err := uc.classificationFilesRepo.GetClassificationFileTags(ctx, fileInfos)
	if err != nil {
		return nil, 0, err
	}

	classificationTagByKey := make(map[string]*ClassificationFileTags)
	for _, tag := range classificationTags {
		classificationTagByKey[fmt.Sprintf("%d-%s-%s", tag.FileRelationID, tag.PreEntityTag, tag.EntityTag)] = tag
	}

	for _, externalModelUsage := range externalModelUsages {
		// user avatar
		if userAvatar, ok := userAvatars[externalModelUsage.UserID]; ok {
			externalModelUsage.UserAvatar = userAvatar
		}

		// model avatar
		var key string
		switch externalModelUsage.ModelName {
		case "通义千问":
			key = "model_avatar/qwen.png"
		case "腾讯元宝":
			key = "model_avatar/tencent_yuanbao.png"
		case "豆包":
			key = "model_avatar/doubao.png"
		case "deepseek":
			key = "model_avatar/deepseek.png"
		default:
			continue
		}
		externalModelUsage.ModelAvatar = uc.ossClient.GetObjectURL(ctx, key)

		// tag
		for _, file := range externalModelUsage.Files {
			if tag, ok := classificationTagByKey[fmt.Sprintf("%d-%s-%s", file.FileRelationID, file.PreEntityTag, file.EntityTag)]; ok {
				file.ClassPath = tag.Path
				file.Level = int64(tag.SecurityLevel)
			}
		}
	}

	return externalModelUsages, total, nil
}

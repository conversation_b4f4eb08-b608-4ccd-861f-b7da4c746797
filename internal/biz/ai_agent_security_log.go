package biz

import (
	"context"
	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"time"
)

type AiAgentSecurityLog struct {
	ID                    int64                    `json:"id"`
	RiskLevel             pb.RiskLevel             `json:"riskLevel"`
	UserID                int64                    `json:"userId"`
	UserName              string                   `json:"userName"`
	UserAvatar            string                   `json:"userAvatar"`
	DeptID                int64                    `json:"deptId"`
	DeptName              string                   `json:"deptName"`
	PcName                string                   `json:"pcName"`
	AgentID               int64                    `json:"agentId"`
	AgentName             string                   `json:"agentName"`
	AgentAvatar           string                   `json:"agentAvatar"`
	AgentDescription      string                   `json:"agentDescription"`
	ActionCategory        int64                    `json:"actionCategory"`
	HitAction             pb.HitAction             `json:"hitAction"`
	Question              string                   `json:"question"`
	UploadedFiles         []string                 `json:"uploadedFiles"`
	HitPolicies           []string                 `json:"hitPolicies"`
	HitPoliciesStructured []*AiAgentSecurityPolicy `json:"hitPoliciesStructured"`
	CreatedAt             time.Time                `json:"created_at"`
	UpdatedAt             time.Time                `json:"updated_at"`
}

type AiAgentSecurityLogRepo interface {
	Get(ctx context.Context, id int64) (*AiAgentSecurityLog, error)
	Create(ctx context.Context, policies []*AiAgentSecurityLog) ([]*AiAgentSecurityLog, error)
	ListByAgentID(ctx context.Context, agentID int64) ([]*AiAgentSecurityLog, error)
	Update(ctx context.Context, policies *AiAgentSecurityLog) (*AiAgentSecurityLog, error)
	Delete(ctx context.Context, id int64) error
	Page(ctx context.Context, riskLevel, hitAction []int64, userName, deptName string, startTime, endTime time.Time) ([]*AiAgentSecurityLog, int64, error)
	GetRiskCount(ctx context.Context) (highRiskCount, midRiskCount, lowRiskCount int, err error)
	GetHitActionCount(ctx context.Context) (blockCount, warningCount int, err error)
}

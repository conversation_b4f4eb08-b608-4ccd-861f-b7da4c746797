package biz

import (
	"context"
	"errors"
	"time"

	"gitlab.minum.cloud/BackendTeam/admin/api/dept"
	"gitlab.minum.cloud/BackendTeam/admin/api/user"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
)

type ChatItem struct {
	ID               int64        `json:"id"`
	ChatID           int64        `json:"chat_id"`
	ObjectID         int64        `json:"object_id"`
	ObjectType       int64        `json:"object_type"`
	Message          string       `json:"message"`
	FileRelationIDs  []int64      `json:"file_relation_ids"`
	RefFiles         string       `json:"ref_files"`
	TenantID         int64        `json:"tenant_id"`
	RoundID          int64        `json:"round_id"`
	PcName           string       `json:"pc_name"`
	CreateAT         time.Time    `json:"create_at"`
	Reason           string       `json:"reason"`
	AgreeStatus      int64        `json:"agree_status"`
	MineTypes        []string     `json:"mine_types"`
	HitAction        pb.HitAction `json:"hit_action"`
	HitResponse      string       `json:"hit_response"`
	HitContinueSend  bool         `json:"hit_continue_send"`
	IsInternetSearch bool         `json:"is_internet_search"`
	SuggestQuestions []string     `json:"suggest_questions"`
}

type ChatItemInfo struct {
	ID                      int64     `sql:"id"`
	Message                 string    `sql:"message"`
	ObjectID                int64     `sql:"object_id"`
	PcName                  string    `sql:"pc_name"`
	RefFiles                string    `sql:"ref_files"`
	SecondaryClassification string    `sql:"secondary_classification"`
	AgentName               string    `sql:"agent_name"`
	AgentAvatar             string    `sql:"agent_avatar"`
	ModelID                 int64     `sql:"model_id"`
	ModelName               string    `sql:"model_name"`
	ModelAvatar             string    `sql:"model_avatar"`
	CreateAT                time.Time `sql:"created_at"`
}

type Classification struct {
	Name  string `json:"name"`
	Count int64  `json:"count"`
}

type ClassificationObject struct {
	ObjectID        int64             `json:"object_id"`
	ObjectName      string            `json:"object_name"`
	Count           int64             `json:"count"`
	Classifications []*Classification `json:"classifications"`
}

type FileType struct {
	Name  string `json:"name"`
	Count int64  `json:"count"`
}

type FileTypesObject struct {
	ObjectID   int64       `json:"object_id"`
	ObjectName string      `json:"object_name"`
	Count      int64       `json:"count"`
	FileTypes  []*FileType `json:"classifications"`
}

type ChatItemRepo interface {
	Save(context.Context, *ChatItem) (*ChatItem, error)
	Update(context.Context, *ChatItem) (*ChatItem, error)
	UpdateHitContinueSend(context.Context, *ChatItem) error
	ListAll(context.Context, int64) ([]*ChatItem, error)
	GetTotalChatCount(ctx context.Context, startTime, endTime time.Time) (int64, error)
	PageUserQuestion(ctx context.Context, chatItemID int64, userIDs []int64, startTime, endTime time.Time, searchModel bool, modelID, agentID int64, searchfile []string, noRefFiles bool, class string) ([]*ChatItemInfo, int64, error)
	UpdateAgreeStatus(ctx context.Context, chatItemId, agreeStatus int64) error
	UpdateSuggestQuestions(ctx context.Context, chatItemID int64, suggestQuestions []string) error
	QuerySecondaryClassificationDistribution(ctx context.Context, modelType int64, startTime, endTime time.Time) ([]*Classification, error)
	QuerySecondaryClassificationTopKCountGroupUser(ctx context.Context, modelType int64, startTime, endTime time.Time, topK int64) ([]*ClassificationObject, error)
	QuerySecondaryClassificationTopKCountGroupDept(ctx context.Context, modelType int64, startTime, endTime time.Time, topK int64) ([]*ClassificationObject, error)
	QuerySecondaryClassificationDistributionByUserID(ctx context.Context, modelType int64, userID int64, startTime, endTime time.Time) ([]*Classification, error)
	QuerySecondaryClassificationDistributionByDeptID(ctx context.Context, modelType int64, deptID int64, startTime, endTime time.Time) ([]*Classification, error)

	QueryUploadFileTypesDistribution(ctx context.Context, modelType int64, startTime, endTime time.Time) ([]*FileType, error)
	QueryUploadFileTypesTopKCountGroupUser(ctx context.Context, modelType int64, startTime, endTime time.Time, topK int64) ([]*FileTypesObject, error)
	QueryUploadFileTypesTopKCountGroupDept(ctx context.Context, modelType int64, startTime, endTime time.Time, topK int64) ([]*FileTypesObject, error)
	QueryUploadFileTypesDistributionByUserID(ctx context.Context, modelType int64, userID int64, startTime, endTime time.Time) ([]*FileType, error)
	QueryUploadFileTypesDistributionByDeptID(ctx context.Context, modelType int64, deptID int64, startTime, endTime time.Time) ([]*FileType, error)
}

type ChatItemUsecase struct {
	repo              ChatItemRepo
	externalModelRepo ExternalModelRepo
	log               *zapadapter.Logger
	tm                DbTransaction
	rpc               rpc.Client
}

func (uc *ChatItemUsecase) Save(ctx context.Context, chatItem *ChatItem) (*ChatItem, error) {
	return uc.repo.Save(ctx, chatItem)
}

func (uc *ChatItemUsecase) Update(ctx context.Context, chatItem *ChatItem) (*ChatItem, error) {
	return uc.repo.Update(ctx, chatItem)
}

func (uc *ChatItemUsecase) UpdateHitContinueSend(ctx context.Context, chatItem *ChatItem) error {
	return uc.repo.UpdateHitContinueSend(ctx, chatItem)
}

func (uc *ChatItemUsecase) ListAll(ctx context.Context, chatID int64) ([]*ChatItem, error) {
	return uc.repo.ListAll(ctx, chatID)
}

func (uc *ChatItemUsecase) GetChatCount(ctx context.Context, startTime, endTime time.Time) (int64, error) {
	return uc.repo.GetTotalChatCount(ctx, startTime, endTime)
}

func (uc *ChatItemUsecase) UpdateChatItemAgreeStatus(ctx context.Context, chatItemId, agreeStatus int64) error {
	return uc.repo.UpdateAgreeStatus(ctx, chatItemId, agreeStatus)
}

func (uc *ChatItemUsecase) UpdateChatItemSuggestQuestions(ctx context.Context, chatItemID int64, suggestQuestions []string) error {
	return uc.repo.UpdateSuggestQuestions(ctx, chatItemID, suggestQuestions)
}

func (uc *ChatItemUsecase) QuerySecondaryClassificationDistribution(ctx context.Context, modelType int64, startTime, endTime time.Time) ([]*Classification, error) {
	if modelType == 3 {
		return uc.externalModelRepo.QueryQuestionTagDistribution(ctx, startTime, endTime)
	}
	return uc.repo.QuerySecondaryClassificationDistribution(ctx, modelType, startTime, endTime)
}

func (uc *ChatItemUsecase) QuerySecondaryClassificationTopKCount(ctx context.Context, modelType, topType int64, startTime, endTime time.Time, topK int64) ([]*ClassificationObject, error) {
	var classificationObjects []*ClassificationObject
	var err error
	switch topType {
	case int64(pb.SearchTopType_SearchTopTypeUser):
		classificationObjects, err = uc.repo.QuerySecondaryClassificationTopKCountGroupUser(ctx, modelType, startTime, endTime, topK)
		if err != nil {
			return nil, err
		}
		for i := range classificationObjects {
			classifications, err := uc.repo.QuerySecondaryClassificationDistributionByUserID(ctx, modelType, classificationObjects[i].ObjectID, startTime, endTime)
			if err != nil {
				return nil, err
			}
			classificationObjects[i].Classifications = classifications
			users, err := uc.rpc.GetUser(ctx, &user.GetUserRequest{
				Ids:        []int64{classificationObjects[i].ObjectID},
				HasDeleted: true,
			})
			if err != nil {
				return nil, err
			}
			if len(users.GetItems()) > 0 {
				classificationObjects[i].ObjectName = users.GetItems()[0].GetNickName()
			}
		}
	case int64(pb.SearchTopType_SearchTopTypeDept):
		classificationObjects, err = uc.repo.QuerySecondaryClassificationTopKCountGroupDept(ctx, modelType, startTime, endTime, topK)
		if err != nil {
			return nil, err
		}
		for i := range classificationObjects {
			classifications, err := uc.repo.QuerySecondaryClassificationDistributionByDeptID(ctx, modelType, classificationObjects[i].ObjectID, startTime, endTime)
			if err != nil {
				return nil, err
			}
			classificationObjects[i].Classifications = classifications
			depts, err := uc.rpc.GetDept(ctx, &dept.GetDeptRequest{
				Ids: []int64{classificationObjects[i].ObjectID},
			})
			if err != nil {
				return nil, err
			}
			if len(depts.GetItems()) > 0 {
				classificationObjects[i].ObjectName = depts.GetItems()[0].GetName()
			}
		}
	default:
		return nil, errors.New("invalid top type")
	}
	return classificationObjects, nil
}

func (uc *ChatItemUsecase) QueryUploadFileTypesDistribution(ctx context.Context, modelType int64, startTime, endTime time.Time) ([]*FileType, error) {
	if modelType == 3 {
		return uc.externalModelRepo.QueryUploadFileTypesDistribution(ctx, startTime, endTime)
	}
	return uc.repo.QueryUploadFileTypesDistribution(ctx, modelType, startTime, endTime)
}

func (uc *ChatItemUsecase) QueryUploadFileTypesTopKCount(ctx context.Context, modelType, topType int64, startTime, endTime time.Time, topK int64) ([]*FileTypesObject, error) {
	var fileTypesObjects []*FileTypesObject
	var err error
	switch topType {
	case int64(pb.SearchTopType_SearchTopTypeUser):
		fileTypesObjects, err = uc.repo.QueryUploadFileTypesTopKCountGroupUser(ctx, modelType, startTime, endTime, topK)
		if err != nil {
			return nil, err
		}
		for i := range fileTypesObjects {
			fileTypes, err := uc.repo.QueryUploadFileTypesDistributionByUserID(ctx, modelType, fileTypesObjects[i].ObjectID, startTime, endTime)
			if err != nil {
				return nil, err
			}
			fileTypesObjects[i].FileTypes = fileTypes
			users, err := uc.rpc.GetUser(ctx, &user.GetUserRequest{
				Ids:        []int64{fileTypesObjects[i].ObjectID},
				HasDeleted: true,
			})
			if err != nil {
				return nil, err
			}
			if len(users.GetItems()) > 0 {
				fileTypesObjects[i].ObjectName = users.GetItems()[0].GetNickName()
			}
		}
	case int64(pb.SearchTopType_SearchTopTypeDept):
		fileTypesObjects, err = uc.repo.QueryUploadFileTypesTopKCountGroupDept(ctx, modelType, startTime, endTime, topK)
		if err != nil {
			return nil, err
		}
		for i := range fileTypesObjects {
			fileTypes, err := uc.repo.QueryUploadFileTypesDistributionByDeptID(ctx, modelType, fileTypesObjects[i].ObjectID, startTime, endTime)
			if err != nil {
				return nil, err
			}
			fileTypesObjects[i].FileTypes = fileTypes
			depts, err := uc.rpc.GetDept(ctx, &dept.GetDeptRequest{
				Ids: []int64{fileTypesObjects[i].ObjectID},
			})
			if err != nil {
				return nil, err
			}
			if len(depts.GetItems()) > 0 {
				fileTypesObjects[i].ObjectName = depts.GetItems()[0].GetName()
			}
		}
	default:
		return nil, errors.New("invalid top type")
	}
	return fileTypesObjects, nil
}

func NewChatItemUsecase(repo ChatItemRepo, externalModelRepo ExternalModelRepo, tm DbTransaction, logger *zapadapter.Logger, rpc rpc.Client) *ChatItemUsecase {
	return &ChatItemUsecase{
		repo:              repo,
		externalModelRepo: externalModelRepo,
		log:               logger,
		tm:                tm,
		rpc:               rpc,
	}
}

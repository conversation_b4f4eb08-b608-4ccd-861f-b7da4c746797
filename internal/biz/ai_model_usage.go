package biz

import (
	"context"
	"github.com/samber/lo"
	"gitlab.minum.cloud/BackendTeam/admin/api/user"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/oss"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
	"go.uber.org/zap"
	"time"
)

type AiModelUsage struct {
	ID                int64     `json:"id"`
	ModelName         string    `json:"modelName"`
	ModelGatewayName  string    `json:"modelGatewayName"`
	ModelDetailID     int64     `json:"modelDetailID"`
	ModelDetailAvatar string    `json:"modelDetailAvatar"`
	AgentID           int64     `json:"agentID"`
	AgentName         string    `json:"agentName"`
	AgentAvatar       string    `json:"agentAvatar"`
	UserID            int64     `json:"userID"`
	UserName          string    `json:"userName"`
	UserAvatar        string    `json:"userAvatar"`
	Question          string    `json:"question"`
	Answer            string    `json:"answer"`
	PromptTokens      int64     `json:"promptTokens"`
	CompletionTokens  int64     `json:"completionTokens"`
	TotalTokens       int64     `json:"totalTokens"`
	RequestStatus     int8      `json:"requestStatus"`
	ErrorCode         string    `json:"errorCode,omitempty"`
	CreatedAt         time.Time // 创建时间
	UpdatedAt         time.Time // 更新时间
}

type AiModelUsageDetailByDay struct {
	Day              time.Time `json:"day"`
	CallSuccessCount int64     `json:"callSuccessCount"`
	CallFailCount    int64     `json:"callFailCount"`
	PromptTokens     int64     `json:"promptTokens"`
	CompletionTokens int64     `json:"completionTokens"`
}

type AiModelUsageRepo interface {
	Create(ctx context.Context, usage *AiModelUsage) error
	Page(ctx context.Context, agentID int64) ([]*AiModelUsage, int64, error)
	GetAiModelUsageDetailGroupByDay(ctx context.Context, modelDetailID int64, startTime, endTime time.Time) ([]*AiModelUsageDetailByDay, error)
}

type AiModelUsageUseCase struct {
	log             *zapadapter.Logger
	repo            AiModelUsageRepo
	agentRepo       AgentRepo
	modelDetailRepo AiModelDetailRepo
	rpc             rpc.Client
	ossClient       *oss.Client
}

func NewAiModelUsageUseCase(logger *zapadapter.Logger, repo AiModelUsageRepo, agentRepo AgentRepo, modelDetailRepo AiModelDetailRepo, rpc rpc.Client, ossClient *oss.Client) *AiModelUsageUseCase {
	return &AiModelUsageUseCase{
		log:             logger,
		repo:            repo,
		rpc:             rpc,
		agentRepo:       agentRepo,
		ossClient:       ossClient,
		modelDetailRepo: modelDetailRepo,
	}
}

func (uc *AiModelUsageUseCase) Create(ctx context.Context, usage *AiModelUsage) error {
	if usage == nil {
		return nil
	}
	return uc.repo.Create(ctx, usage)
}

func (uc *AiModelUsageUseCase) PageByAgentID(ctx context.Context, agentID int64) ([]*AiModelUsage, int64, error) {
	res, total, err := uc.repo.Page(ctx, agentID)
	if err != nil {
		uc.log.Error("failed to page ai model usage", zap.Error(err))
		return nil, 0, err
	}
	userIDs := make([]int64, 0, len(res))
	userAvatarMap := make(map[int64]string)

	agentIDs := make([]int64, 0, len(res))
	agentAvatarMap := make(map[int64]string)

	for i := range res {
		if res[i].UserID > 0 && !lo.Contains(userIDs, res[i].UserID) {
			userIDs = append(userIDs, res[i].UserID)
		}
		if res[i].AgentID > 0 && !lo.Contains(agentIDs, res[i].AgentID) {
			agentIDs = append(agentIDs, res[i].AgentID)
		}
	}
	users, err := uc.rpc.GetUser(ctx, &user.GetUserRequest{
		Ids:        userIDs,
		HasDeleted: true,
	})
	if err != nil {
		uc.log.Error("failed to get user info", zap.Error(err))
		return nil, 0, err
	}
	for _, u := range users.GetItems() {
		userAvatarMap[u.GetId()] = u.GetAvatarURL()
	}

	agents, err := uc.agentRepo.FindByIDs(ctx, agentIDs)
	if err != nil {
		uc.log.Error("failed to get agents", zap.Error(err))
		return nil, 0, err
	}
	for _, agent := range agents {
		if agent.Avatar != "" {
			agentAvatarMap[agent.ID] = uc.ossClient.GetObjectURL(ctx, agent.Avatar)
		}
	}
	modelDetailAvatarMap := make(map[int64]string)
	modelDetails, err := uc.modelDetailRepo.List(ctx)
	if err != nil {
		uc.log.Error("failed to list model details", zap.Error(err))
		return nil, 0, err
	}
	for _, detail := range modelDetails {
		if detail.Avatar != "" {
			modelDetailAvatarMap[detail.ID] = uc.ossClient.GetObjectURL(ctx, detail.Avatar)
		}
	}

	for i := range res {
		if res[i].UserID > 0 && userAvatarMap[res[i].UserID] != "" {
			res[i].UserAvatar = userAvatarMap[res[i].UserID]
		}
		if res[i].AgentID > 0 && agentAvatarMap[res[i].AgentID] != "" {
			res[i].AgentAvatar = agentAvatarMap[res[i].AgentID]
		}
		if res[i].ModelDetailID > 0 && modelDetailAvatarMap[res[i].ModelDetailID] != "" {
			res[i].ModelDetailAvatar = modelDetailAvatarMap[res[i].ModelDetailID]
		}
	}

	return res, total, nil
}

func (uc *AiModelUsageUseCase) GetAiModelUsageDetailGroupByDay(ctx context.Context, modelDetailID int64, startTime, endTime time.Time) ([]*AiModelUsageDetailByDay, error) {
	if startTime.IsZero() || endTime.IsZero() {
		return nil, nil
	}
	if startTime.After(endTime) {
		return nil, nil
	}
	return uc.repo.GetAiModelUsageDetailGroupByDay(ctx, modelDetailID, startTime, endTime)
}

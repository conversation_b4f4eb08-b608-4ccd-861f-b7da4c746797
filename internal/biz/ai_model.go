package biz

import (
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/errors"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/oss"
	"go.uber.org/zap"
	http2 "net/http"
	"strconv"
	"time"
)

var (
	ErrDelModelInUse        = errors.BadRequest("ERR_DEL_MODEL_IN_USE", "已有智能体使用该模型，无法删除")
	ErrCreateRepeatedApiKey = errors.BadRequest("ERR_REPEATED_API_KEY", "该ApiKey已被使用，无法使用")
)

type AiModel struct {
	ID                   int64
	ModelName            string
	Model                int64
	ApiKey               string
	ModelDetail          *AiModelDetail
	CreatedAt            time.Time `json:"created_at"`
	Balance              float64
	ThinkingEnableStatus int64
	BackgroundUrl        string
}

type AiModelDetail struct {
	ID                   int64
	Name                 string
	ModelName            string
	Url                  string
	Avatar               string
	AvatarUrl            string
	CanInternetSearch    bool
	BalanceSearchUrl     string
	ThinkingEnableStatus int64
	BackgroundUrl        string
}

type AiModelInfo struct {
	ID     int64  `sql:"id"`
	Name   string `sql:"name"`
	Avatar string `sql:"avatar"`
}

type ModelBalance struct {
	IsAvailable  bool                `json:"is_available"`
	BalanceInfos []*ModelBalanceInfo `json:"balance_infos"`
}

type ModelBalanceInfo struct {
	Currency        string `json:"currency"`
	TotalBalance    string `json:"total_balance"`
	GrantedBalance  string `json:"granted_balance"`
	ToppedUpBalance string `json:"topped_up_balance"`
}

type AiModelRepo interface {
	Save(ctx context.Context, model *AiModel) (*AiModel, error)
	Update(ctx context.Context, model *AiModel) (*AiModel, error)
	Delete(ctx context.Context, id int64) error
	List(ctx context.Context, modelName string) ([]*AiModel, error)
	Get(ctx context.Context, id int64) (*AiModel, error)
	GetModelDetails(ctx context.Context, modelID []int64) ([]*AiModelInfo, error)
	CountByApiKey(ctx context.Context, apiKey string, modelDetailID int64) (int64, error)
}

type AiModelDetailRepo interface {
	List(ctx context.Context) ([]*AiModelDetail, error)
	Get(ctx context.Context, id int64) (*AiModelDetail, error)
}

type AiModelUsecase struct {
	repo              AiModelRepo
	agentRepo         AgentRepo
	aiModelDetailRepo AiModelDetailRepo
	log               *zapadapter.Logger
	ossClient         *oss.Client
}

func (usecase *AiModelUsecase) Save(ctx context.Context, model *AiModel) (*AiModel, error) {
	count, err := usecase.repo.CountByApiKey(ctx, model.ApiKey, model.Model)
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, ErrCreateRepeatedApiKey
	}
	return usecase.repo.Save(ctx, model)
}

func (usecase *AiModelUsecase) Update(ctx context.Context, model *AiModel) (*AiModel, error) {
	return usecase.repo.Update(ctx, model)
}

func (usecase *AiModelUsecase) Delete(ctx context.Context, id int64) error {
	agents, err := usecase.agentRepo.FindByModelId(ctx, id)
	if err != nil {
		return err
	}
	if agents != nil && len(agents) > 0 {
		return ErrDelModelInUse
	}
	return usecase.repo.Delete(ctx, id)
}

func (usecase *AiModelUsecase) List(ctx context.Context, modelName string) ([]*AiModel, error) {
	models, err := usecase.repo.List(ctx, modelName)
	if err != nil {
		return nil, err
	}
	for i, model := range models {
		modelDetail, err := usecase.aiModelDetailRepo.Get(ctx, model.Model)
		if err != nil {
			usecase.log.Error("get model detail error", zap.Error(err))
			continue
		}
		if modelDetail != nil {
			imageUrl := usecase.ossClient.GetObjectURL(ctx, modelDetail.Avatar)
			modelDetail.AvatarUrl = imageUrl
			models[i].ModelDetail = modelDetail
			models[i].ThinkingEnableStatus = modelDetail.ThinkingEnableStatus
			models[i].BackgroundUrl = modelDetail.BackgroundUrl
			models[i].Balance = -1
			if modelDetail.BalanceSearchUrl != "" {
				var modelBalance *ModelBalance
				request, err := http2.NewRequest("GET", modelDetail.BalanceSearchUrl, nil)
				if err != nil {
					usecase.log.Error("create request error", zap.Error(err))
				} else {
					client := http2.Client{}
					request.Header.Set("Authorization", "Bearer "+model.ApiKey)
					response, err := client.Do(request)
					if err != nil {
						usecase.log.Error("do request error", zap.Error(err))
					} else {
						defer response.Body.Close()
						if response.StatusCode == http2.StatusOK {
							err = json.NewDecoder(response.Body).Decode(&modelBalance)
							if err != nil {
								usecase.log.Error("decode response body error", zap.Error(err))
							} else {
								if modelBalance != nil && modelBalance.IsAvailable && modelBalance.BalanceInfos != nil && len(modelBalance.BalanceInfos) > 0 {
									// 字符串转float
									models[i].Balance = 0
									if modelBalance.BalanceInfos[0].TotalBalance != "" {
										balance, err := strconv.ParseFloat(modelBalance.BalanceInfos[0].TotalBalance, 64)
										if err != nil {
											usecase.log.Error("parse balance error", zap.Error(err))
										} else {
											models[i].Balance = balance
										}
									} else {
										usecase.log.Warn("total balance is empty")
									}
								}
							}
						}
					}
				}
			}
		}

	}
	return models, nil
}

func (usecase *AiModelUsecase) Get(ctx context.Context, id int64) (*AiModel, error) {
	model, err := usecase.repo.Get(ctx, id)
	if err != nil {
		return nil, err
	}
	if model.Model > 0 {
		modelDetail, err := usecase.aiModelDetailRepo.Get(ctx, model.Model)
		if err != nil {
			usecase.log.Error("get model detail error", zap.Error(err))
		} else {
			if modelDetail != nil {
				imageUrl := usecase.ossClient.GetObjectURL(ctx, modelDetail.Avatar)
				modelDetail.AvatarUrl = imageUrl
				model.ModelDetail = modelDetail
			}
		}
	}
	return model, nil
}

func (usecase *AiModelUsecase) ListModelDetail(ctx context.Context) ([]*AiModelDetail, error) {
	list, err := usecase.aiModelDetailRepo.List(ctx)
	if err != nil {
		usecase.log.Error("list model detail error", zap.Error(err))
		return nil, err
	}
	for i := range list {
		if list[i].Avatar != "" {
			imageUrl := usecase.ossClient.GetObjectURL(ctx, list[i].Avatar)
			list[i].AvatarUrl = imageUrl
		}
	}
	return list, nil
}

func NewAiModelUseCase(repo AiModelRepo, agentRepo AgentRepo, aiModelDetailRepo AiModelDetailRepo, logger *zapadapter.Logger, ossClient *oss.Client) *AiModelUsecase {
	return &AiModelUsecase{
		repo:              repo,
		aiModelDetailRepo: aiModelDetailRepo,
		log:               logger,
		ossClient:         ossClient,
		agentRepo:         agentRepo,
	}
}

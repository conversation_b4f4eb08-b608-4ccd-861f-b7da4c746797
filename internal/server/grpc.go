package server

import (
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	"gitlab.minum.cloud/BackendTeam/pkg/middleware/logging"
	"gitlab.minum.cloud/BackendTeam/pkg/middleware/metrics"
	"gitlab.minum.cloud/BackendTeam/pkg/middleware/page"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	"gitlab.minum.cloud/innovationteam/ai-web/api/classification"
	"gitlab.minum.cloud/innovationteam/ai-web/api/externalmodel"
	"gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/api/rag"
	webAgent "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/service"
)

// NewGRPCServer new a gRPC server.
func NewGRPCServer(c *conf.Server,
	logger *zapadapter.Logger,
	ragSvc *service.RagService,
	classificationSvc *service.ClassificationService,
	knowledgebaseSvc *service.KnowledgeBaseService,
	agentSvc *service.AgentService,
	externalModelSvc *service.ExternalModelService,
) *grpc.Server {
	opts := []grpc.ServerOption{
		grpc.Middleware(
			recovery.Recovery(),
			logging.Server(logger),
			validate.Validator(),
			metrics.Server(),
			page.Page(),
			authz.MiddleWareServer(logger.Logger),
		),
	}
	if c.Grpc.Network != "" {
		opts = append(opts, grpc.Network(c.Grpc.Network))
	}
	if c.Grpc.Addr != "" {
		opts = append(opts, grpc.Address(c.Grpc.Addr))
	}
	if c.Grpc.Timeout != nil {
		opts = append(opts, grpc.Timeout(c.Grpc.Timeout.AsDuration()))
	}
	srv := grpc.NewServer(opts...)
	rag.RegisterRagServer(srv, ragSvc)
	classification.RegisterClassificationServer(srv, classificationSvc)
	knowledgebase.RegisterKnowledgeBaseServer(srv, knowledgebaseSvc)
	webAgent.RegisterAgentServer(srv, agentSvc)
	externalmodel.RegisterExternalModelServer(srv, externalModelSvc)
	return srv
}

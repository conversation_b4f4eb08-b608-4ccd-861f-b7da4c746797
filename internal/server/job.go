package server

import (
	"os"
	"strconv"

	"gitlab.minum.cloud/BackendTeam/pkg/xxl"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/service"
)

func NewJobServer(
	cfg *conf.Bootstrap,
	classificationSvc *service.ClassificationService,
) *xxl.Server {
	srv := xxl.NewServer(&xxl.Config{
		ServerHost:   cfg.Job.ServerHost,
		ServerPort:   strconv.FormatInt(cfg.Job.ServerPort, 10),
		ExecutorName: cfg.Job.ExecutorName,
		ExecutorIp:   os.Getenv("JOB_EXECUTOR_IP"), // use default if not set
		ExecutorPort: strconv.FormatInt(cfg.Job.ExecutorPort, 10),
		AccessToken:  cfg.Job.AccessToken,
	})

	return srv
}

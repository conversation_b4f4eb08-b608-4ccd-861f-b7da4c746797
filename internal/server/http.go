package server

import (
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/middleware/validate"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"gitlab.minum.cloud/BackendTeam/pkg/authz"
	mhttp "gitlab.minum.cloud/BackendTeam/pkg/middleware/http"
	"gitlab.minum.cloud/BackendTeam/pkg/middleware/logging"
	"gitlab.minum.cloud/BackendTeam/pkg/middleware/metrics"
	"gitlab.minum.cloud/BackendTeam/pkg/middleware/page"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	"gitlab.minum.cloud/innovationteam/ai-web/api/aimodel"
	"gitlab.minum.cloud/innovationteam/ai-web/api/classification"
	"gitlab.minum.cloud/innovationteam/ai-web/api/externalmodel"
	"gitlab.minum.cloud/innovationteam/ai-web/api/health"
	"gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
	"gitlab.minum.cloud/innovationteam/ai-web/api/rag"
	webAgent "gitlab.minum.cloud/innovationteam/ai-web/api/webagent"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/service"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server,
	logger *zapadapter.Logger,
	ragSvc *service.RagService,
	classificationSvc *service.ClassificationService,
	knowledgebaseSvc *service.KnowledgeBaseService,
	agentSvc *service.AgentService,
	healthSvc *service.HealthService,
	aiModelSvc *service.AimodelService,
	externalModelSvc *service.ExternalModelService,
) *http.Server {
	opts := []http.ServerOption{
		http.ResponseEncoder(mhttp.JsonResponseEncode()),
		http.Middleware(
			recovery.Recovery(),
			logging.Server(logger),
			validate.Validator(),
			metrics.Server(),
			authz.MiddleWareServer(logger.Logger),
			page.Page(),
		),
		http.PathPrefix("/ai-web"),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}

	srv := http.NewServer(opts...)
	srv.Handle("/metrics", promhttp.Handler())
	rag.RegisterRagHTTPServer(srv, ragSvc)
	classification.RegisterClassificationHTTPServer(srv, classificationSvc)
	knowledgebase.RegisterKnowledgeBaseHTTPServer(srv, knowledgebaseSvc)
	service.RegisterUserHttpHandler(srv, agentSvc)
	service.RegisterAgentChatHttpHandler(srv, agentSvc)
	service.RegisterRagChatHttpHandler(srv, ragSvc)
	health.RegisterHealthHTTPServer(srv, healthSvc)
	webAgent.RegisterAgentHTTPServer(srv, agentSvc)
	aimodel.RegisterAimodelHTTPServer(srv, aiModelSvc)
	externalmodel.RegisterExternalModelHTTPServer(srv, externalModelSvc)
	return srv
}

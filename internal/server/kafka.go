package server

import (
	"context"

	kafkago "github.com/segmentio/kafka-go"
	"github.com/tx7do/kratos-transport/broker"
	kbroker "github.com/tx7do/kratos-transport/broker/kafka"
	"github.com/tx7do/kratos-transport/transport/kafka"
	pkgkafka "gitlab.minum.cloud/BackendTeam/pkg/kafka"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"

	externalmodelpb "gitlab.minum.cloud/innovationteam/ai-web/api/externalmodel"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/service"
)

func NewKafkaServer(cfg *conf.Bootstrap, _ *zapadapter.Logger, externalModelSvc *service.ExternalModelService) *pkgkafka.Server {
	ctx, cancel := context.WithCancel(context.Background())

	srv := kafka.NewServer(
		kafka.WithAddress(cfg.Kafka.Address),
		kafka.WithGlobalTracerProvider(),
		kafka.WithGlobalPropagator(),
		kafka.WithCodec(cfg.Kafka.Codec),
		kafka.WithBrokerOptions(
			kbroker.WithStartOffset(kafkago.LastOffset),
			kbroker.WithMaxWait(cfg.Kafka.ReadTimeout.AsDuration()),
		),
	)

	registerKafkaSubscribers(ctx, srv, externalModelSvc)

	return pkgkafka.NewServer(srv, ctx, cancel)
}

type MessageHandler[T any] func(ctx context.Context, topic string, headers broker.Headers, message *T) error

type Wrap[T any] func() (broker.Handler, broker.Binder)

func registerKafkaSubscribers(ctx context.Context, srv *kafka.Server, externalModelSvc *service.ExternalModelService) {
	if err := kafka.RegisterSubscriber(srv, ctx,
		externalmodelpb.TopicExternalModelQuestionTag,
		externalmodelpb.TopicExternalModelQuestionTag+"-group",
		false,
		externalModelSvc.UpdateExternalModelUsageQuestionTag,
		kbroker.WithSubscribeAutoCreateTopic(externalmodelpb.TopicExternalModelQuestionTag, 10, -1),
	); err != nil {
		panic(err)
	}
}

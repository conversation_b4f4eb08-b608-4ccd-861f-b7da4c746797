kafka:
  codec: json
  read_timeout: 3s
  address:
    - localhost:9092
server:
  http:
    addr: :0
    timeout: 1s
  grpc:
    addr: :0
    timeout: 1s
data:
  database:
    driver: postgres
    source: host=localhost port=5432 user=postgres password=123456 dbname=test sslmode=disable
  redis:
    addr: localhost:6379
    read_timeout: 0.2s
    write_timeout: 0.2s
vs:
  host: **************
  port: 6334
  collection_name: doc_hybrid
  sheet_collection_name: sheet_hybrid
external_model_configs:
  - parse_type: file
    model_name: 通义千问
    host: api.tongyi.com
    pre_schema: POST /assistant/api/chat/file/add
    parse_rule: workName
  - parse_type: chat
    model_name: 通义千问
    host: api.tongyi.com
    pre_schema: POST /dialog/conversation
    parse_rule: contents.content
    session_parse_rule: sessionId
  - parse_type: file
    model_name: 豆包
    host: www.doubao.com
    pre_schema: POST /alice/message/pre_handle_v2
    parse_rule: uplink_entity.entity_content.file.file_name
  - parse_type: chat
    model_name: 豆包
    host: www.doubao.com
    pre_schema: POST /samantha/chat/completion
    parse_rule: messages.content.text
    session_parse_rule: conversation_id
  - parse_type: file
    model_name: 腾讯元宝
    host: yuanbao.tencent.com
    pre_schema: POST /api/resource/genUploadInfo
    parse_rule: fileName
  - parse_type: chat
    model_name: 腾讯元宝
    host: yuanbao.tencent.com
    pre_schema: POST /api/chat
    parse_rule: prompt
    session_parse_rule: agentId
  - parse_type: chat
    model_name: deepseek
    host: chat.deepseek.com
    pre_schema: POST /api/v0/chat/completion
    parse_rule: prompt
    session_parse_rule: chat_session_id
  - parse_type: file
    model_name: deepseek
    host: gator.volces.com
    pre_schema: POST /list
    parse_rule: events.params.ds_fileName
knowledge_graph:
  enable: false

name: project-infra

services:
  postgresql:
    image: postgres
    container_name: postgresql-infra
    ports:
      - 5432:5432
    environment:
      POSTGRES_PASSWORD: 123456
    volumes:
      - postgresql_vol:/var/lib/postgresql/data

  kafka:
    image: docker.io/bitnami/kafka
    ports:
      - 9092:9092
    environment:
      # KRaft settings
      - KAFKA_ENABLE_KRAFT=yes
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_PROCESS_ROLES=controller,broker
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093
      # Listeners
      - <PERSON>AF<PERSON>_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093
      - <PERSON><PERSON><PERSON>_CFG_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=PLAINTEXT
    volumes:
      - kafka_vol:/bitnami/kafka
    container_name: kafka-infra

  redis:
    image: redis/redis-stack-server
    container_name: redis-infra
    ports:
      - 6379:6379

  rnacos:
    image: qingpan/rnacos:stable
    ports:
      - 8848:8848
      - 9848:9848
      - 10848:10848
    container_name: rnacos-infra
    volumes:
      - rnacos_vol:/io

volumes:
  kafka_vol: { }
  rnacos_vol: { }
  postgresql_vol: { }
FROM harbor.minum.cloud/pub/golang:1.23.6-bookworm AS builder
COPY . /src
WORKDIR /src
ENV GOPRIVATE=gitlab.minum.cloud
ENV GONOPROXY=gitlab.minum.cloud
RUN apt install -y curl
RUN GOPROXY=https://nexus.minum.cloud/repository/go-group GOSUMDB=sum.golang.google.cn make build

FROM harbor.minum.cloud/pub/debian:stable-slim
COPY --from=builder /src/bin /app
RUN sed -i '<EMAIL>@mirrors.ustc.edu.cn@g' /etc/apt/sources.list.d/debian.sources && apt update && apt install -y ca-certificates
WORKDIR /app
COPY template.json /app/
COPY template_mix.json /app/
COPY template_normal.json /app/
COPY template_external.json /app/
COPY template_normal_deepsearch_file.json /app/
EXPOSE 8000
EXPOSE 9000
VOLUME /data/conf
ENV NACOS_HOST=localhost
ENV NAOCS_PORT=8848
ENV SW_AGENT_LOG_TYPE=zap
# skywalking agent name
ENV SW_AGENT_NAME=ai-web
# skywalking agent service name
ENV SW_AGENT_REPORTER_GRPC_BACKEND_SERVICE=127.0.0.1:11800
CMD ["./ai-web"]

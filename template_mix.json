{"nodes": [{"data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"name": "question", "required": true, "type": "string"}], "trigger_parameters": [{"name": "question", "required": true, "type": "string"}]}, "id": "100001", "meta": {"position": {"x": -5.0622048232387264, "y": -268.91942046855735}}, "type": "1"}, {"data": {"nodeMeta": {"description": "从知识库中检索相关内容", "icon": "https://example.com/kb-icon.jpg", "subTitle": "知识库", "title": "知识库检索", "mainColor": "#FF9900"}, "inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "query"}], "settingOnError": {}}, "outputs": [{"name": "output", "type": "string"}], "version": "1"}, "id": "100003", "meta": {"position": {"x": 300, "y": -200}}, "type": "4"}, {"data": {"nodeMeta": {"description": "从密数万象中提取文件内容摘要", "icon": "https://example.com/kb-icon.jpg", "subTitle": "密数万象文件摘要提取", "title": "密数万象文件摘要提取", "mainColor": "#FF9900"}, "inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "array<integer>", "value": {"content": {"blockID": "100001", "name": "file_relation_ids", "source": "block-output"}, "type": "ref"}}, "name": "file_relation_ids"}], "settingOnError": {}}, "outputs": [{"name": "output", "type": "string"}], "version": "1"}, "id": "100006", "meta": {"position": {"x": 300, "y": -200}}, "type": "5"}, {"data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "外部大模型", "type": "literal"}}, "name": "modelName"}, {"input": {"type": "string", "value": {"content": "balance", "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0", "type": "literal"}}, "name": "temperature"}, {"input": {"type": "float", "value": {"content": "0.7", "type": "literal"}}, "name": "topP"}, {"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "integer", "value": {"content": "4096", "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "string", "value": {"content": "{{question}}, 回复越简短越好不能超过200字，不过关键信息要充足，若无法回答则返回空", "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "mainColor": "#5C62FF", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "id": "100014", "meta": {"position": {"x": 643.4008038127196, "y": -198.51641826352517}}, "type": "3"}, {"data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "string", "value": {"content": {"blockID": "100003", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "kb_search_result"}, {"input": {"type": "string", "value": {"content": {"blockID": "100014", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "external_search_result"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "外部大模型", "type": "literal"}}, "name": "modelName"}, {"input": {"type": "string", "value": {"content": "balance", "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0", "type": "literal"}}, "name": "temperature"}, {"input": {"type": "float", "value": {"content": "0.7", "type": "literal"}}, "name": "topP"}, {"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "integer", "value": {"content": "4096", "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "string", "value": {"content": "你是密数万象智能助手, 属于江苏大道云隐科技有限公司的产品。你将接受一个用户提出的问题以及从知识库中查出的相关知识片段，然后根据这些知识片段来回答问题。这是用户提出的问题：<question>{{question}}</question>这是从知识库中查出的知识片段：<knowledge_fragments>{{kb_search_result}}</knowledge_fragments> 这是联网搜索的内容：<external_search_result>{{external_search_result}}</external_search_result> 下面是回答问题的步骤：1.仔细阅读问题和知识片段。2.从知识片段中找出与问题相关的部分。3.根据相关部分构建答案，要确保答案完整且能准确回答问题。请在直接给出你的答案。4. 若知识库中查出的知识片段或者从其它大模型中返回的结果不存在则直接回答问题。5. 用不超过100字回答。6. 回答请简单直接。7. 如果无法从知识库检索内容中获取答案则联网搜索并自己回答", "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "mainColor": "#5C62FF", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "id": "100004", "meta": {"position": {"x": 643.4008038127196, "y": -198.51641826352517}}, "type": "3"}, {"data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "string", "value": {"content": {"blockID": "100003", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "kb_search_result"}, {"input": {"type": "string", "value": {"content": {"blockID": "100014", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "external_search_result"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "1", "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "内部模型调用", "type": "literal"}}, "name": "modleName"}, {"input": {"type": "string", "value": {"content": "balance", "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0", "type": "literal"}}, "name": "temperature"}, {"input": {"type": "float", "value": {"content": "0.7", "type": "literal"}}, "name": "topP"}, {"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "integer", "value": {"content": "8192", "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "string", "value": {"content": "你是密数万象智能助手, 属于江苏大道云隐科技有限公司的产品。你将接受一个用户提出的问题以及从知识库中查出的相关知识片段，然后根据这些知识片段来回答问题。这是用户提出的问题：<question>{{question}}</question>这是从知识库中查出的知识片段：<knowledge_fragments>{{kb_search_result}}</knowledge_fragments> 这是联网搜索的内容：<external_search_result>{{external_search_result}}</external_search_result> 下面是回答问题的步骤：1.仔细阅读问题和知识片段。2.从知识片段中找出与问题相关的部分。3.根据相关部分构建答案，要确保答案完整且能准确回答问题。请在直接给出你的答案。4. 若知识库中查出的知识片段或者从其它大模型中返回的结果不存在则直接回答问题。5. 请不要提到其它模型的参考。6. 回答请简单直接", "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "mainColor": "#5C62FF", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "id": "100005", "meta": {"position": {"x": 643.4008038127196, "y": -198.51641826352517}}, "type": "3"}, {"data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "string", "value": {"content": {"blockID": "100006", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "file_summary"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "外部大模型", "type": "literal"}}, "name": "modelName"}, {"input": {"type": "string", "value": {"content": "balance", "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0", "type": "literal"}}, "name": "temperature"}, {"input": {"type": "float", "value": {"content": "0.7", "type": "literal"}}, "name": "topP"}, {"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "integer", "value": {"content": "4096", "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "string", "value": {"content": "你是密数万象智能助手, 属于江苏大道云隐科技有限公司的产品。请参考以下文件内容信息: <file_summary>{{file_summary}}</file_summary> 用不超过200个字回答以下问题: <question>{{question}}</question>", "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "mainColor": "#5C62FF", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "id": "100007", "meta": {"position": {"x": 643.4008038127196, "y": -198.51641826352517}}, "type": "3"}, {"data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "question", "source": "block-output"}, "type": "ref"}}, "name": "question"}, {"input": {"type": "string", "value": {"content": {"blockID": "100006", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "file_summary"}, {"input": {"type": "string", "value": {"content": {"blockID": "100007", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "outside_model_result"}], "llmParam": [{"input": {"type": "integer", "value": {"content": "1", "type": "literal"}}, "name": "modelType"}, {"input": {"type": "string", "value": {"content": "内部模型调用", "type": "literal"}}, "name": "modleName"}, {"input": {"type": "string", "value": {"content": "balance", "type": "literal"}}, "name": "generationDiversity"}, {"input": {"type": "float", "value": {"content": "0", "type": "literal"}}, "name": "temperature"}, {"input": {"type": "float", "value": {"content": "0.7", "type": "literal"}}, "name": "topP"}, {"input": {"type": "integer", "value": {"content": "2", "type": "literal"}}, "name": "responseFormat"}, {"input": {"type": "integer", "value": {"content": "8192", "type": "literal"}}, "name": "maxTokens"}, {"input": {"type": "string", "value": {"content": "你是密数万象智能助手, 属于江苏大道云隐科技有限公司的产品。请参考以下文件内容信息: <file_summary>{{file_summary}}</file_summary> 和外部大模型返回的结果: <outside_model_result>{{outside_model_result}}</outside_model_result> 用不超过2000个字回答以下问题: <question>{{question}}</question> 1.仔细阅读问题和参考内容。2.从参考中找出与问题相关的部分。3.根据相关部分构建答案，要确保答案完整且能准确回答问题。请在直接给出你的答案。4. 若文件内容信息或者从其它大模型中返回的结果不存在则直接回答问题。5. 请不要提到其它模型的参考。6. 回答请简单直接", "type": "literal"}}, "name": "prompt"}, {"input": {"type": "boolean", "value": {"content": false, "type": "literal"}}, "name": "enableChatHistory"}, {"input": {"type": "integer", "value": {"content": "3", "type": "literal"}}, "name": "chatHistoryRound"}, {"input": {"type": "string", "value": {"content": "", "type": "literal"}}, "name": "systemPrompt"}], "settingOnError": {}}, "nodeMeta": {"description": "调用大语言模型,使用变量和提示词生成回复", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM-v2.jpg", "mainColor": "#5C62FF", "subTitle": "大模型", "title": "大模型"}, "outputs": [{"name": "output", "type": "string"}], "version": "3"}, "id": "100008", "meta": {"position": {"x": 643.4008038127196, "y": -198.51641826352517}}, "type": "3"}, {"data": {"inputs": {"branches": [{"condition": {"conditions": [{"left": {"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "file_relation_ids", "source": "block-output"}, "type": "ref"}}}, "operator": 7, "right": {"input": {"type": "string", "value": {"content": "0", "type": "literal"}}}}], "logic": 2}}]}, "nodeMeta": {"description": "连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行否则分支", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Condition-v2.jpg", "mainColor": "#00B2B2", "subTitle": "选择器", "title": "选择器"}}, "id": "100002", "meta": {"position": {"x": 1003.4008038127196, "y": -500.14795762394647}}, "type": "8"}, {"data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100005", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "output_from_kb"}, {"input": {"type": "string", "value": {"content": {"blockID": "100008", "name": "output", "source": "block-output"}, "type": "ref"}}, "name": "output_from_direct_files"}], "terminatePlan": "returnVariables"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "id": "999999", "meta": {"position": {"x": 1554.125851898408, "y": -184.56641826352518}}, "type": "2"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "100002", "sourcePortID": ""}, {"sourceNodeID": "100002", "targetNodeID": "100003", "sourcePortID": "true"}, {"sourceNodeID": "100002", "targetNodeID": "100006", "sourcePortID": "false"}, {"sourceNodeID": "100003", "targetNodeID": "100014", "sourcePortID": ""}, {"sourceNodeID": "100014", "targetNodeID": "100005", "sourcePortID": ""}, {"sourceNodeID": "100005", "targetNodeID": "999999", "sourcePortID": ""}, {"sourceNodeID": "100006", "targetNodeID": "100007", "sourcePortID": ""}, {"sourceNodeID": "100007", "targetNodeID": "100008", "sourcePortID": ""}, {"sourceNodeID": "100008", "targetNodeID": "999999", "sourcePortID": ""}], "versions": {"loop": "v2"}}
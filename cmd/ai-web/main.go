package main

import (
	"flag"
	"fmt"
	"os"
	"strconv"
	"strings"

	knacos "github.com/go-kratos/kratos/contrib/config/nacos/v2"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/encoding"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/nacos-group/nacos-sdk-go/clients"
	"github.com/nacos-group/nacos-sdk-go/clients/config_client"
	"github.com/nacos-group/nacos-sdk-go/common/constant"
	"github.com/nacos-group/nacos-sdk-go/vo"
	"gitlab.minum.cloud/BackendTeam/pkg/kafka"
	"gitlab.minum.cloud/BackendTeam/pkg/xxl"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	_ "go.uber.org/automaxprocs"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	_ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = "ai-web"
	// Version is the version of the compiled software.
	Version string
	id, _   = os.Hostname()
)

var (
	// flagconf is the config flag.
	flagconf string

	// use nacos config
	nacosHost   string
	nacosPort   uint64
	nacosDataID string
	nacosGroup  string
)

func init() {
	flag.StringVar(&flagconf, "conf", "./configs/config.yaml", "config path, eg: -conf config.yaml")

	//flag.StringVar(&nacosHost, "host", "localhost", "nacos host, eg: -host localhost")
	//flag.Uint64Var(&nacosPort, "port", 8848, "nacos port, eg: -port 8848")
	nacosHost = os.Getenv("NACOS_HOST")
	if len(nacosHost) == 0 {
		nacosHost = "localhost"
		fmt.Println("NACOS_HOST not found, use 'localhost'")
	}
	nacosPortStr := os.Getenv("NACOS_PORT")
	nacosPortInt, err := strconv.Atoi(nacosPortStr)
	if err != nil {
		nacosPort = 8848
		fmt.Println("NACOS_PORT not found, use '8848'")
	} else {
		nacosPort = uint64(nacosPortInt)
	}

	flag.StringVar(&nacosDataID, "dataID", "ai-web.yaml", "nacos data id, eg: -dataID ai-web.yaml")
	flag.StringVar(&nacosGroup, "group", "DEFAULT_GROUP", "nacos group, eg: -group DEFAULT_GROUP")
}

func main() {
	flag.Parse()

	var (
		logger      = newLog()
		nacosConfig = newNacosConfigParam(nacosHost, nacosPort)
		config      = newConfig(nacosConfig, logger)
	)

	defer config.Close()

	if err := config.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := config.Scan(&bc); err != nil {
		panic(err)
	}

	app, cleanup, err := wireApp(bc.Server, &bc, logger, nacosConfig)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}

func newApp(logger *zapadapter.Logger, rg registry.Registrar, gs *grpc.Server, hs *http.Server, ks *kafka.Server, js *xxl.Server) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			gs,
			hs,
			ks,
			js,
		),
		kratos.Registrar(rg),
	)
}

func newLog() *zapadapter.Logger {
	encoderConf := zap.NewProductionEncoderConfig()
	var encoder zapcore.Encoder

	isDev := os.Getenv("MINUM_DEV")
	if isDev == "true" {
		customCallerEncoder := func(caller zapcore.EntryCaller, enc zapcore.PrimitiveArrayEncoder) {
			path := caller.TrimmedPath()
			if strings.HasPrefix(path, "zapadapter") {
				return
			}
			enc.AppendString(path)
		}

		encoderConf.EncodeCaller = customCallerEncoder
		encoderConf.EncodeTime = zapcore.RFC3339TimeEncoder
		encoderConf.EncodeLevel = zapcore.CapitalColorLevelEncoder
		encoder = zapcore.NewConsoleEncoder(encoderConf)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderConf)
	}

	writeSyncer := zapcore.AddSync(os.Stderr)
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.InfoLevel)

	z := zap.New(core,
		zap.AddCaller(),
		zap.Fields(
			zap.String("service.id", id),
			zap.String("service.name", Name),
			zap.String("service.version", Version),
		),
		//zap.AddStacktrace(zapcore.ErrorLevel),
	)

	logger := zapadapter.NewLogger(z)
	log.SetLogger(logger)

	return logger
}

func newNacosConfigParam(host string, port uint64) conf.NacosConfig {
	param := vo.NacosClientParam{
		ClientConfig: &constant.ClientConfig{
			TimeoutMs:           5000,
			NotLoadCacheAtStart: true,
			LogDir:              "/tmp/nacos/log",
			CacheDir:            "/tmp/nacos/cache",
			LogLevel:            "debug",
		},
		ServerConfigs: []constant.ServerConfig{
			*constant.NewServerConfig(host, port),
		},
	}

	return conf.NacosConfig{
		DataID: nacosDataID,
		Group:  nacosGroup,
		Param:  param,
	}
}

func newConfig(nacosConf conf.NacosConfig, logger *zapadapter.Logger) config.Config {
	c := config.New(
		config.WithSource(
			// file.NewSource(flagconf),
			knacos.NewConfigSource(newNacosConfigClient(nacosConf, logger),
				knacos.WithDataID(nacosConf.DataID),
				knacos.WithGroup(nacosConf.Group),
			),
		),
		config.WithDecoder(func(src *config.KeyValue, target map[string]interface{}) error {
			return encoding.GetCodec("yaml").Unmarshal(src.Value, &target)
		}),
	)

	return c
}

func newNacosConfigClient(nacosConf conf.NacosConfig, logger *zapadapter.Logger) config_client.IConfigClient {
	configClient, err := clients.NewConfigClient(nacosConf.Param)
	if err != nil {
		logger.Error("new nacos config client", zap.Error(err))
		return nil
	}

	return configClient
}

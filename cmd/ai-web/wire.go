//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/google/wire"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/server"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/service"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Bootstrap, *zapadapter.Logger, conf.NacosConfig) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, newApp))
}

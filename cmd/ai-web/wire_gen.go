// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"gitlab.minum.cloud/BackendTeam/pkg/zapadapter"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/biz"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/conf"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/data/rpc"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/server"
	"gitlab.minum.cloud/innovationteam/ai-web/internal/service"
)

import (
	_ "gitlab.minum.cloud/innovationteam/ai-web/internal/data/ent/runtime"
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, bootstrap *conf.Bootstrap, logger *zapadapter.Logger, nacosConfig conf.NacosConfig) (*kratos.App, func(), error) {
	iNamingClient := data.NewNacosNamingClient(nacosConfig, logger)
	registrar := data.NewRegistrar(nacosConfig, iNamingClient, logger)
	client := data.NewKafka(bootstrap, logger)
	dataData, cleanup, err := data.NewData(bootstrap, client, logger)
	if err != nil {
		return nil, nil, err
	}
	chatRepo := data.NewChatRepo(dataData, logger)
	chatItemRepo := data.NewChatItemRepo(dataData, logger)
	dbTransaction := data.NewDbTransaction(dataData)
	agentRepo := data.NewAgentRepo(dataData, logger)
	cryptoClient, err := data.NewCryptoClient(logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	discovery := data.NewDiscovery(nacosConfig, iNamingClient, logger)
	rpcClient, err := rpc.NewClient(logger, discovery, bootstrap)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	ossClient := data.NewOSSClient(logger, bootstrap, cryptoClient, rpcClient)
	aiModelRepo := data.NewAiModelRepo(dataData, logger)
	aiModelDetailRepo := data.NewAiModelDetailRepo(dataData, logger)
	chatUsecase := biz.NewChatUsecase(chatRepo, chatItemRepo, dbTransaction, logger, agentRepo, ossClient, aiModelRepo, aiModelDetailRepo)
	externalModelRepo := data.NewExternalModelRepo(dataData, logger)
	chatItemUsecase := biz.NewChatItemUsecase(chatItemRepo, externalModelRepo, dbTransaction, logger, rpcClient)
	qdrantClient, err := data.NewQdrantClient(bootstrap)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	knowledgeBaseRepo := data.NewKnowledgeBaseRepo(dataData, logger)
	knowledgeBaseFileRepo := data.NewKnowledgeBaseFileRepo(dataData, logger)
	classificationFilesRepo := data.NewClassificationFilesRepo(dataData, logger)
	knowledgeBaseUseCase := biz.NewKnowledgeBaseUseCase(logger, dbTransaction, bootstrap, qdrantClient, rpcClient, knowledgeBaseRepo, knowledgeBaseFileRepo, agentRepo, classificationFilesRepo)
	ragService := service.NewRagService(chatUsecase, chatItemUsecase, knowledgeBaseUseCase, rpcClient, discovery, logger)
	redisClient := data.NewRedis(dataData)
	classificationUseCase := biz.NewClassificationUseCase(logger, redisClient, classificationFilesRepo, rpcClient)
	classificationService := service.NewClassificationService(logger, classificationUseCase)
	knowledgeBaseService := service.NewKnowledgeBaseService(logger, bootstrap, rpcClient, client, redisClient, knowledgeBaseUseCase)
	defaultAgentAvatarRepo := data.NewDefaultAgentAvatarRepoRepo(dataData, logger)
	userAgentOrderRepo := data.NewUserAgentOrderRepo(dataData, logger)
	aiAgentSecurityPolicyRepo := data.NewAiAgentSecurityPolicyRepo(dataData, logger)
	aiAgentSecurityLogRepo := data.NewAiAgentSecurityLogRepo(dataData, logger)
	agentUsecase := biz.NewAgentUsecase(agentRepo, defaultAgentAvatarRepo, logger, dbTransaction, ossClient, rpcClient, knowledgeBaseRepo, knowledgeBaseFileRepo, chatItemRepo, chatRepo, aiModelRepo, redisClient, userAgentOrderRepo, aiAgentSecurityPolicyRepo, aiAgentSecurityLogRepo)
	aiModelUsecase := biz.NewAiModelUseCase(aiModelRepo, agentRepo, aiModelDetailRepo, logger, ossClient)
	aiModelUsageRepo := data.NewAiModelUsageRepo(dataData, logger)
	aiModelUsageUseCase := biz.NewAiModelUsageUseCase(logger, aiModelUsageRepo, agentRepo, aiModelDetailRepo, rpcClient, ossClient)
	agentService := service.NewAgentService(agentUsecase, logger, chatUsecase, chatItemUsecase, rpcClient, bootstrap, aiModelUsecase, classificationUseCase, aiModelUsageUseCase)
	externalModelUseCase := biz.NewExternalModelUseCase(logger, dbTransaction, bootstrap, rpcClient, ossClient, externalModelRepo, classificationFilesRepo)
	externalModelService := service.NewExternalModelService(logger, client, externalModelUseCase)
	grpcServer := server.NewGRPCServer(confServer, logger, ragService, classificationService, knowledgeBaseService, agentService, externalModelService)
	skywalking_clientClient := data.NewSkyWalkingClient(bootstrap)
	healthService := service.NewHealthService(bootstrap, skywalking_clientClient)
	aimodelService := service.NewAimodelService(aiModelUsecase, rpcClient, aiModelUsageUseCase)
	httpServer := server.NewHTTPServer(confServer, logger, ragService, classificationService, knowledgeBaseService, agentService, healthService, aimodelService, externalModelService)
	kafkaServer := server.NewKafkaServer(bootstrap, logger, externalModelService)
	xxlServer := server.NewJobServer(bootstrap, classificationService)
	app := newApp(logger, registrar, grpcServer, httpServer, kafkaServer, xxlServer)
	return app, func() {
		cleanup()
	}, nil
}

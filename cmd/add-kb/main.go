package main

import (
	"bytes"
	"encoding/csv"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"

	pb "gitlab.minum.cloud/innovationteam/ai-web/api/knowledgebase"
)

// GOOS=windows GOARCH=amd64 go build -o addkb.exe cmd/add-kb/main.go
// GOOS=linux GOARCH=amd64 go build -o addkb cmd/add-kb/main.go
// go run cmd/add-kb/main.go -domain https://saas3-dev.minum.cloud -token 8db90daa70cb63798f5412c17e2ae9fc -csv Untitled.csv -kbid 7
// SELECT id, name, full_path FROM file_relation WHERE full_path = '' and name LIKE '%doc' AND object_id = 1;
func main() {
	domain := flag.String("domain", "", "domain")
	token := flag.String("token", "", "token")
	csvFile := flag.String("csv", "", "csv")
	kbID := flag.Int64("kbid", 0, "kbid")
	flag.Parse()
	fmt.Println(*domain, *token, *csvFile, *kbID)

	file, err := os.Open(*csvFile)
	if err != nil {
		log.Fatal("无法打开文件:", err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	records, err := reader.ReadAll()
	if err != nil {
		log.Fatal("无法读取文件:", err)
	}

	var fileRelationIDs []int64
	for _, record := range records {
		fileRelationID, err := strconv.ParseInt(record[0], 10, 64)
		if err != nil {
			continue
		}
		fileRelationIDs = append(fileRelationIDs, fileRelationID)
	}
	fmt.Println(fileRelationIDs)

	data := pb.AddKnowledgeBaseFileRequest{
		Id:              *kbID,
		FileRelationIDs: fileRelationIDs,
	}
	body, err := json.Marshal(&data)
	if err != nil {
		log.Fatal("无法解析JSON:", err)
	}
	fmt.Println(string(body))

	req, err := http.NewRequest(http.MethodPost, fmt.Sprintf("%s/ai-web/knowledgeBase/addFile", *domain), bytes.NewBuffer(body))
	if err != nil {
		log.Fatal("无法创建请求:", err)
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Token", *token)

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Fatal("请求失败:", err)
	}
	defer resp.Body.Close()

	fmt.Println(resp.StatusCode)

	var res []byte
	_, err = resp.Body.Read(res)
	if err != nil {
		log.Fatal("读取响应失败:", err)
	}
	fmt.Println(string(res))
}
